# 水表采集器配置工具通信协议 - STM32设备端实现

## 概述

本协议库为STM32设备端实现了水表采集器配置工具的通信协议，支持除固件升级外的所有功能。

## 协议特点

- 基于串口通信（UART）
- 采用固定帧格式：`0x7E + 长度 + 命令码 + 数据 + 0xCE`
- 长度字段包含长度字段本身
- 小端序数据传输
- 除升级功能外，其他功能不使用CRC校验

## 文件结构

```
protocol.h          - 协议头文件，包含所有定义和函数声明
protocol.c          - 协议实现文件
protocol_example.c  - 使用示例文件
README_protocol.md  - 本说明文件
```

## 支持的功能

### 1. 系统基础功能
- **连接握手/心跳** (命令码: 0x02)
- **查询系统版本** (命令码: 0x05)

### 2. 网络配置功能
- **中心1配置** (命令码: 0x11) - 设置/读取
- **中心2配置** (命令码: 0x12) - 设置/读取
- **中心3配置** (命令码: 0x13) - 设置/读取

### 3. 厂家协议配置
- **厂家协议配置** (命令码: 0x15) - 设置/读取

### 4. 其他配置功能
- **数据兼容模式配置** (命令码: 0x16) - 设置/读取

### 5. 终端ID配置
- **设置终端ID** (命令码: 0x17)
- **读取终端ID** (命令码: 0x18)

## 快速开始

### 1. 集成到STM32项目

1. 将 `protocol.h` 和 `protocol.c` 添加到你的STM32项目中
2. 在需要使用协议的文件中包含头文件：
   ```c
   #include "protocol.h"
   ```

### 2. 实现UART发送函数

你需要实现 `uart_send_data` 函数：

```c
void uart_send_data(uint8_t *data, int length)
{
    // 使用STM32 HAL库发送数据
    HAL_UART_Transmit(&huart1, data, length, 1000);
}
```

### 3. 处理接收数据

在UART接收中断或主循环中调用协议处理函数：

```c
// 假设你已经接收到完整的数据包
uint8_t rx_buffer[256];
int rx_length = received_length;

// 处理协议数据包
int result = protocol_process(rx_length, (char*)rx_buffer);
if (result == 0) {
    // 处理成功
} else {
    // 处理失败
}
```

## API 参考

### 主要函数

#### protocol_process()
```c
int protocol_process(int length, char* data);
```
- **功能**: 协议数据处理主函数
- **参数**: 
  - `length`: 报文长度
  - `data`: 报文数据指针
- **返回值**: 0-成功，-1-失败

### 配置访问函数

#### 中心配置
```c
int protocol_get_center_config(int center_num, center_config_t *config);
int protocol_set_center_config(int center_num, const center_config_t *config);
```

#### 厂家协议配置
```c
int protocol_get_protocol_config(protocol_config_t *config);
int protocol_set_protocol_config(const protocol_config_t *config);
```

#### 其他配置
```c
int protocol_get_other_config(other_config_t *config);
int protocol_set_other_config(const other_config_t *config);
```

#### 终端ID
```c
int protocol_get_terminal_id(uint8_t *id);
int protocol_set_terminal_id(const uint8_t *id);
```

#### 系统版本
```c
int protocol_get_system_version(uint8_t *version);
int protocol_set_system_version(const uint8_t *version);
```

## 数据结构

### center_config_t
```c
typedef struct {
    uint8_t ip[4];              // IP地址
    uint16_t port;              // 端口号（小端序）
    uint16_t period;            // 上报周期，单位分钟（小端序）
} center_config_t;
```

### protocol_config_t
```c
typedef struct {
    uint8_t manufacturer;       // 厂家代码
    uint8_t baud_code;         // 波特率代码
    uint8_t data_bits;         // 数据位
    uint8_t stop_bits;         // 停止位
    uint8_t parity;            // 校验位
} protocol_config_t;
```

### other_config_t
```c
typedef struct {
    uint8_t data_compat_mode;   // 数据兼容模式
} other_config_t;
```

## 常量定义

### 厂家代码
- `MANUFACTURER_DALIAN` (1) - 大连道盛
- `MANUFACTURER_TAIAN` (2) - 泰安
- `MANUFACTURER_TANGSHAN` (3) - 唐山
- `MANUFACTURER_HENAN` (4) - 河南

### 波特率代码
- `BAUD_1200` (1) - 1200
- `BAUD_2400` (2) - 2400
- `BAUD_4800` (3) - 4800
- `BAUD_9600` (4) - 9600
- `BAUD_19200` (5) - 19200
- `BAUD_38400` (6) - 38400

### 校验位
- `PARITY_NONE` (0) - 无校验
- `PARITY_ODD` (1) - 奇校验
- `PARITY_EVEN` (2) - 偶校验

## 使用示例

详细的使用示例请参考 `protocol_example.c` 文件，其中包含：

1. 协议初始化
2. 配置读写示例
3. UART接收处理示例
4. 测试数据包处理

## 注意事项

1. **内存管理**: 所有配置数据都存储在静态变量中，实际项目中可能需要持久化存储（如EEPROM、Flash等）

2. **线程安全**: 当前实现不是线程安全的，如果在多线程环境中使用，需要添加适当的同步机制

3. **错误处理**: 建议在实际项目中添加更详细的错误处理和日志记录

4. **数据验证**: 可以根据需要添加更严格的数据验证逻辑

## 协议测试

可以使用提供的测试函数验证协议实现：

```c
test_protocol_packets();  // 测试各种协议数据包
```

## 扩展

如果需要添加新的功能或修改现有功能，请：

1. 在 `protocol.h` 中添加相应的命令码和数据结构定义
2. 在 `protocol.c` 中实现相应的处理函数
3. 在 `protocol_process()` 函数的switch语句中添加新的case
4. 更新相应的配置访问函数
