/*b板载外部外设配置，led，key，*/
#include "bsp_board.h"
void EnterStandbyMode(void);
void bsp_board_enter_standy(void)
{
  __HAL_PWR_CLEAR_FLAG(PWR_FLAG_WU);
//	HAL_PWR_EnableWakeUpPin(PWR_WAKEUP_PIN2);/*开启唤醒引脚*/
//	HAL_ADCEx_DisableVREFINT();
//	HAL_PWR_EnterSTANDBYMode();//进入待机模式
	HAL_PWR_EnableWakeUpPin(PWR_WAKEUP_PIN2);/*开启唤醒引脚*/
	EnterStandbyMode();
	
}

#define WAKEUP_EXTI_IRQn EXTI15_10_IRQn

void bsp_board_init(void)
{
   __HAL_RCC_GPIOC_CLK_ENABLE();
    
    // 2. 配置PC14为输入模式（内部上拉）
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = GPIO_PIN_13;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;          // 关键：高电平有效需上拉
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
		
//		 __HAL_RCC_PWR_CLK_ENABLE();

//  __HAL_RCC_GPIOC_CLK_ENABLE();
//  
//  // 配置PC13为外部中断
//  GPIO_InitTypeDef GPIO_InitStruct = {0};
//  GPIO_InitStruct.Pin = GPIO_PIN_13;
//  GPIO_InitStruct.Mode = GPIO_MODE_IT_RISING;
//  GPIO_InitStruct.Pull = GPIO_NOPULL;
//  HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
//  
//  // 设置中断优先级
//  HAL_NVIC_SetPriority(EXTI4_15_IRQn, 0, 0);
//  HAL_NVIC_EnableIRQ(EXTI4_15_IRQn);
}


void EnterStandbyMode(void)
{
  /* 1. 清除所有唤醒标志 */
  __HAL_PWR_CLEAR_FLAG(PWR_FLAG_WU);
  
  /* 2. 配置PC13作为唤醒引脚 - 使用HAL库函数替代直接寄存器操作 */
  // 使能PWR时钟
  __HAL_RCC_PWR_CLK_ENABLE();
  
  // 使能PC13作为唤醒引脚（WakeUp Pin 3）
  // HAL库函数会自动处理不同版本寄存器的差异
  HAL_PWR_EnableWakeUpPin(PWR_WAKEUP_PIN3);
  
  /* 3. 配置PC13引脚为输入模式，无上下拉 */
  GPIO_InitTypeDef GPIO_InitStruct = {0};
  __HAL_RCC_GPIOC_CLK_ENABLE();
  GPIO_InitStruct.Pin = GPIO_PIN_13;
  GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
  GPIO_InitStruct.Pull = GPIO_NOPULL;  // 无上下拉，避免漏电流
  HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
  __HAL_RCC_GPIOC_CLK_DISABLE();  // 配置完成后关闭GPIOC时钟
  
  /* 4. 禁用不必要的外设和时钟，降低功耗 */
  __HAL_RCC_GPIOA_CLK_DISABLE();
  __HAL_RCC_GPIOB_CLK_DISABLE();
  __HAL_RCC_GPIOD_CLK_DISABLE();
  __HAL_RCC_USART2_CLK_DISABLE();
  __HAL_RCC_SPI1_CLK_DISABLE();
  __HAL_RCC_ADC1_CLK_DISABLE();
  __HAL_RCC_TIM2_CLK_DISABLE();
  
  /* 5. 关闭SWD调试接口，进一步降低功耗 */
  __HAL_RCC_DBGMCU_CLK_ENABLE();
  DBGMCU->CR &= ~(DBGMCU_CR_DBG_STANDBY);  // 禁止待机模式下的调试功能
  __HAL_RCC_DBGMCU_CLK_DISABLE();
  
  /* 6. 进入待机模式 */
  HAL_PWR_EnterSTANDBYMode();
}
    
/* GPIO初始化 */
static void MX_GPIO_Init(void)
{
  GPIO_InitTypeDef GPIO_InitStruct = {0};

  /* GPIO时钟使能 */
  __HAL_RCC_GPIOA_CLK_ENABLE();

  /* 配置PA5为LED输出（用于状态指示） */
  GPIO_InitStruct.Pin = GPIO_PIN_5;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
}

/* 错误处理函数 */
void Error_Handler(void)
{
  /* 循环闪烁LED表示错误 */
  while(1)
  {
    HAL_GPIO_TogglePin(GPIOA, GPIO_PIN_5);
    HAL_Delay(100);
  }
}
/* 全局变量 */
//RTC_HandleTypeDef hrtc;
///* RTC初始化（备份域供电，可选功能） */
//static void MX_RTC_Init(void)
//{
//  RTC_TimeTypeDef sTime = {0};
//  RTC_DateTypeDef sDate = {0};

//  hrtc.Instance = RTC;
//  hrtc.Init.HourFormat = RTC_HOURFORMAT_24;
//  hrtc.Init.AsynchPrediv = 127;
//  hrtc.Init.SynchPrediv = 255;
//  hrtc.Init.OutPut = RTC_OUTPUT_DISABLE;
//  hrtc.Init.OutPutPolarity = RTC_OUTPUT_POLARITY_HIGH;
//  hrtc.Init.OutPutType = RTC_OUTPUT_TYPE_OPENDRAIN;
//  if (HAL_RTC_Init(&hrtc) != HAL_OK)
//  {
//    Error_Handler();
//  }

//  /* 检查RTC是否已初始化 */
//  if (HAL_RTCEx_BKUPRead(&hrtc, RTC_BKP_DR0) != 0x32F2)
//  {
//    sTime.Hours = 0x00;
//    sTime.Minutes = 0x00;
//    sTime.Seconds = 0x00;
//    sTime.DayLightSaving = RTC_DAYLIGHTSAVING_NONE;
//    sTime.StoreOperation = RTC_STOREOPERATION_RESET;
//    if (HAL_RTC_SetTime(&hrtc, &sTime, RTC_FORMAT_BCD) != HAL_OK)
//    {
//      Error_Handler();
//    }
//    sDate.WeekDay = RTC_WEEKDAY_MONDAY;
//    sDate.Month = RTC_MONTH_JANUARY;
//    sDate.Date = 0x01;
//    sDate.Year = 0x00;
//    if (HAL_RTC_SetDate(&hrtc, &sDate, RTC_FORMAT_BCD) != HAL_OK)
//    {
//      Error_Handler();
//    }
//    /* 写入备份寄存器标记RTC已初始化 */
//    HAL_RTCEx_BKUPWrite(&hrtc, RTC_BKP_DR0, 0x32F2);
//  }
//}

