/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : D:\2025work\STM32L072PRO\source-application\user\driver_Sequences_0030.log
 *  Created     : 10:13:55 (26/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : D:\2025work\STM32L072PRO\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[10:13:55.765]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:13:55.765]  
[10:13:55.796]  <debugvars>
[10:13:55.819]    // Pre-defined
[10:13:55.846]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:13:55.869]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:13:55.869]    __dp=0x00000000
[10:13:55.871]    __ap=0x00000000
[10:13:55.873]    __traceout=0x00000000      (Trace Disabled)
[10:13:55.874]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:13:55.874]    __FlashAddr=0x00000000
[10:13:55.876]    __FlashLen=0x00000000
[10:13:55.876]    __FlashArg=0x00000000
[10:13:55.877]    __FlashOp=0x00000000
[10:13:55.878]    __Result=0x00000000
[10:13:55.879]    
[10:13:55.879]    // User-defined
[10:13:55.880]    DbgMCU_CR=0x00000007
[10:13:55.880]    DbgMCU_APB1_Fz=0x00000000
[10:13:55.881]    DbgMCU_APB2_Fz=0x00000000
[10:13:55.882]    DoOptionByteLoading=0x00000000
[10:13:55.883]  </debugvars>
[10:13:55.883]  
[10:13:55.884]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:13:55.885]    <block atomic="false" info="">
[10:13:55.885]      Sequence("CheckID");
[10:13:55.886]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:13:55.887]          <block atomic="false" info="">
[10:13:55.888]            __var pidr1 = 0;
[10:13:55.889]              // -> [pidr1 <= 0x00000000]
[10:13:55.889]            __var pidr2 = 0;
[10:13:55.890]              // -> [pidr2 <= 0x00000000]
[10:13:55.891]            __var jep106id = 0;
[10:13:55.891]              // -> [jep106id <= 0x00000000]
[10:13:55.893]            __var ROMTableBase = 0;
[10:13:55.894]              // -> [ROMTableBase <= 0x00000000]
[10:13:55.895]            __ap = 0;      // AHB-AP
[10:13:55.895]              // -> [__ap <= 0x00000000]
[10:13:55.896]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:13:55.897]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:13:55.898]              // -> [ROMTableBase <= 0xF0000000]
[10:13:55.899]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:13:55.900]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:13:55.901]              // -> [pidr1 <= 0x00000004]
[10:13:55.901]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:13:55.903]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:13:55.903]              // -> [pidr2 <= 0x0000000A]
[10:13:55.904]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:13:55.904]              // -> [jep106id <= 0x00000020]
[10:13:55.905]          </block>
[10:13:55.905]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:13:55.906]            // if-block "jep106id != 0x20"
[10:13:55.906]              // =>  FALSE
[10:13:55.907]            // skip if-block "jep106id != 0x20"
[10:13:55.907]          </control>
[10:13:55.907]        </sequence>
[10:13:55.907]    </block>
[10:13:55.907]  </sequence>
[10:13:55.909]  
[10:13:55.920]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:13:55.920]  
[10:13:55.931]  <debugvars>
[10:13:55.931]    // Pre-defined
[10:13:55.931]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:13:55.931]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:13:55.931]    __dp=0x00000000
[10:13:55.931]    __ap=0x00000000
[10:13:55.931]    __traceout=0x00000000      (Trace Disabled)
[10:13:55.936]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:13:55.936]    __FlashAddr=0x00000000
[10:13:55.936]    __FlashLen=0x00000000
[10:13:55.936]    __FlashArg=0x00000000
[10:13:55.936]    __FlashOp=0x00000000
[10:13:55.936]    __Result=0x00000000
[10:13:55.936]    
[10:13:55.936]    // User-defined
[10:13:55.942]    DbgMCU_CR=0x00000007
[10:13:55.942]    DbgMCU_APB1_Fz=0x00000000
[10:13:55.942]    DbgMCU_APB2_Fz=0x00000000
[10:13:55.942]    DoOptionByteLoading=0x00000000
[10:13:55.942]  </debugvars>
[10:13:55.942]  
[10:13:55.942]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:13:55.947]    <block atomic="false" info="">
[10:13:55.947]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:13:55.949]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:13:55.949]    </block>
[10:13:55.949]    <block atomic="false" info="DbgMCU registers">
[10:13:55.949]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:13:55.952]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[10:13:55.952]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[10:13:55.952]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:13:55.952]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:13:55.952]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:13:55.952]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:13:55.957]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:13:55.957]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:13:55.957]    </block>
[10:13:55.957]  </sequence>
[10:13:55.957]  
[10:14:03.265]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:14:03.265]  
[10:14:03.266]  <debugvars>
[10:14:03.266]    // Pre-defined
[10:14:03.267]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:14:03.267]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:14:03.268]    __dp=0x00000000
[10:14:03.268]    __ap=0x00000000
[10:14:03.269]    __traceout=0x00000000      (Trace Disabled)
[10:14:03.269]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:14:03.269]    __FlashAddr=0x00000000
[10:14:03.270]    __FlashLen=0x00000000
[10:14:03.270]    __FlashArg=0x00000000
[10:14:03.270]    __FlashOp=0x00000000
[10:14:03.271]    __Result=0x00000000
[10:14:03.271]    
[10:14:03.271]    // User-defined
[10:14:03.271]    DbgMCU_CR=0x00000007
[10:14:03.272]    DbgMCU_APB1_Fz=0x00000000
[10:14:03.272]    DbgMCU_APB2_Fz=0x00000000
[10:14:03.272]    DoOptionByteLoading=0x00000000
[10:14:03.272]  </debugvars>
[10:14:03.272]  
[10:14:03.273]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:14:03.273]    <block atomic="false" info="">
[10:14:03.273]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:14:03.273]        // -> [connectionFlash <= 0x00000001]
[10:14:03.273]      __var FLASH_BASE = 0x40022000 ;
[10:14:03.274]        // -> [FLASH_BASE <= 0x40022000]
[10:14:03.274]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:14:03.274]        // -> [FLASH_CR <= 0x40022004]
[10:14:03.274]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:14:03.274]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:14:03.275]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:14:03.275]        // -> [LOCK_BIT <= 0x00000001]
[10:14:03.275]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:14:03.275]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:14:03.275]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:14:03.276]        // -> [FLASH_KEYR <= 0x4002200C]
[10:14:03.276]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:14:03.276]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:14:03.276]      __var FLASH_KEY2 = 0x02030405 ;
[10:14:03.277]        // -> [FLASH_KEY2 <= 0x02030405]
[10:14:03.277]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:14:03.277]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:14:03.277]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:14:03.278]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:14:03.278]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:14:03.278]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:14:03.278]      __var FLASH_CR_Value = 0 ;
[10:14:03.278]        // -> [FLASH_CR_Value <= 0x00000000]
[10:14:03.278]      __var DoDebugPortStop = 1 ;
[10:14:03.279]        // -> [DoDebugPortStop <= 0x00000001]
[10:14:03.279]      __var DP_CTRL_STAT = 0x4 ;
[10:14:03.279]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:14:03.279]      __var DP_SELECT = 0x8 ;
[10:14:03.279]        // -> [DP_SELECT <= 0x00000008]
[10:14:03.280]    </block>
[10:14:03.280]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:14:03.280]      // if-block "connectionFlash && DoOptionByteLoading"
[10:14:03.280]        // =>  FALSE
[10:14:03.280]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:14:03.281]    </control>
[10:14:03.281]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:14:03.281]      // if-block "DoDebugPortStop"
[10:14:03.281]        // =>  TRUE
[10:14:03.281]      <block atomic="false" info="">
[10:14:03.281]        WriteDP(DP_SELECT, 0x00000000);
[10:14:03.282]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:14:03.282]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:14:03.283]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:14:03.283]      </block>
[10:14:03.283]      // end if-block "DoDebugPortStop"
[10:14:03.284]    </control>
[10:14:03.284]  </sequence>
[10:14:03.284]  
[10:30:50.994]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:30:50.994]  
[10:30:50.994]  <debugvars>
[10:30:50.995]    // Pre-defined
[10:30:50.995]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:30:50.995]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:30:50.996]    __dp=0x00000000
[10:30:50.996]    __ap=0x00000000
[10:30:50.996]    __traceout=0x00000000      (Trace Disabled)
[10:30:50.997]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:30:50.997]    __FlashAddr=0x00000000
[10:30:50.997]    __FlashLen=0x00000000
[10:30:50.998]    __FlashArg=0x00000000
[10:30:50.998]    __FlashOp=0x00000000
[10:30:50.998]    __Result=0x00000000
[10:30:50.998]    
[10:30:50.998]    // User-defined
[10:30:50.999]    DbgMCU_CR=0x00000007
[10:30:50.999]    DbgMCU_APB1_Fz=0x00000000
[10:30:50.999]    DbgMCU_APB2_Fz=0x00000000
[10:30:51.000]    DoOptionByteLoading=0x00000000
[10:30:51.001]  </debugvars>
[10:30:51.001]  
[10:30:51.001]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:30:51.002]    <block atomic="false" info="">
[10:30:51.002]      Sequence("CheckID");
[10:30:51.002]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:30:51.002]          <block atomic="false" info="">
[10:30:51.002]            __var pidr1 = 0;
[10:30:51.002]              // -> [pidr1 <= 0x00000000]
[10:30:51.003]            __var pidr2 = 0;
[10:30:51.003]              // -> [pidr2 <= 0x00000000]
[10:30:51.003]            __var jep106id = 0;
[10:30:51.003]              // -> [jep106id <= 0x00000000]
[10:30:51.003]            __var ROMTableBase = 0;
[10:30:51.004]              // -> [ROMTableBase <= 0x00000000]
[10:30:51.004]            __ap = 0;      // AHB-AP
[10:30:51.004]              // -> [__ap <= 0x00000000]
[10:30:51.005]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:30:51.006]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:30:51.006]              // -> [ROMTableBase <= 0xF0000000]
[10:30:51.006]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:30:51.007]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:30:51.008]              // -> [pidr1 <= 0x00000004]
[10:30:51.008]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:30:51.009]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:30:51.009]              // -> [pidr2 <= 0x0000000A]
[10:30:51.010]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:30:51.010]              // -> [jep106id <= 0x00000020]
[10:30:51.010]          </block>
[10:30:51.010]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:30:51.010]            // if-block "jep106id != 0x20"
[10:30:51.010]              // =>  FALSE
[10:30:51.010]            // skip if-block "jep106id != 0x20"
[10:30:51.011]          </control>
[10:30:51.012]        </sequence>
[10:30:51.012]    </block>
[10:30:51.012]  </sequence>
[10:30:51.012]  
[10:30:51.024]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:30:51.024]  
[10:30:51.040]  <debugvars>
[10:30:51.040]    // Pre-defined
[10:30:51.040]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:30:51.041]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:30:51.041]    __dp=0x00000000
[10:30:51.041]    __ap=0x00000000
[10:30:51.042]    __traceout=0x00000000      (Trace Disabled)
[10:30:51.042]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:30:51.042]    __FlashAddr=0x00000000
[10:30:51.043]    __FlashLen=0x00000000
[10:30:51.043]    __FlashArg=0x00000000
[10:30:51.043]    __FlashOp=0x00000000
[10:30:51.044]    __Result=0x00000000
[10:30:51.044]    
[10:30:51.044]    // User-defined
[10:30:51.044]    DbgMCU_CR=0x00000007
[10:30:51.045]    DbgMCU_APB1_Fz=0x00000000
[10:30:51.045]    DbgMCU_APB2_Fz=0x00000000
[10:30:51.046]    DoOptionByteLoading=0x00000000
[10:30:51.046]  </debugvars>
[10:30:51.046]  
[10:30:51.047]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:30:51.047]    <block atomic="false" info="">
[10:30:51.048]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:30:51.048]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:30:51.048]    </block>
[10:30:51.049]    <block atomic="false" info="DbgMCU registers">
[10:30:51.050]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:30:51.050]        // -> [Read32(0x40021034) => 0x00004201]   (__dp=0x00000000, __ap=0x00000000)
[10:30:51.051]        // -> [Write32(0x40021034, 0x00404201)]   (__dp=0x00000000, __ap=0x00000000)
[10:30:51.052]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:30:51.052]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:30:51.053]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:30:51.053]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:30:51.054]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:30:51.055]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:30:51.055]    </block>
[10:30:51.056]  </sequence>
[10:30:51.056]  
[10:30:58.706]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:30:58.706]  
[10:30:58.707]  <debugvars>
[10:30:58.707]    // Pre-defined
[10:30:58.708]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:30:58.708]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:30:58.709]    __dp=0x00000000
[10:30:58.709]    __ap=0x00000000
[10:30:58.710]    __traceout=0x00000000      (Trace Disabled)
[10:30:58.710]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:30:58.711]    __FlashAddr=0x00000000
[10:30:58.711]    __FlashLen=0x00000000
[10:30:58.712]    __FlashArg=0x00000000
[10:30:58.712]    __FlashOp=0x00000000
[10:30:58.713]    __Result=0x00000000
[10:30:58.714]    
[10:30:58.714]    // User-defined
[10:30:58.714]    DbgMCU_CR=0x00000007
[10:30:58.715]    DbgMCU_APB1_Fz=0x00000000
[10:30:58.715]    DbgMCU_APB2_Fz=0x00000000
[10:30:58.716]    DoOptionByteLoading=0x00000000
[10:30:58.716]  </debugvars>
[10:30:58.717]  
[10:30:58.717]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:30:58.717]    <block atomic="false" info="">
[10:30:58.718]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:30:58.718]        // -> [connectionFlash <= 0x00000001]
[10:30:58.719]      __var FLASH_BASE = 0x40022000 ;
[10:30:58.719]        // -> [FLASH_BASE <= 0x40022000]
[10:30:58.719]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:30:58.720]        // -> [FLASH_CR <= 0x40022004]
[10:30:58.721]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:30:58.721]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:30:58.722]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:30:58.722]        // -> [LOCK_BIT <= 0x00000001]
[10:30:58.722]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:30:58.723]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:30:58.723]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:30:58.723]        // -> [FLASH_KEYR <= 0x4002200C]
[10:30:58.724]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:30:58.724]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:30:58.724]      __var FLASH_KEY2 = 0x02030405 ;
[10:30:58.724]        // -> [FLASH_KEY2 <= 0x02030405]
[10:30:58.725]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:30:58.725]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:30:58.725]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:30:58.725]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:30:58.726]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:30:58.726]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:30:58.726]      __var FLASH_CR_Value = 0 ;
[10:30:58.726]        // -> [FLASH_CR_Value <= 0x00000000]
[10:30:58.726]      __var DoDebugPortStop = 1 ;
[10:30:58.726]        // -> [DoDebugPortStop <= 0x00000001]
[10:30:58.727]      __var DP_CTRL_STAT = 0x4 ;
[10:30:58.727]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:30:58.727]      __var DP_SELECT = 0x8 ;
[10:30:58.727]        // -> [DP_SELECT <= 0x00000008]
[10:30:58.727]    </block>
[10:30:58.728]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:30:58.728]      // if-block "connectionFlash && DoOptionByteLoading"
[10:30:58.728]        // =>  FALSE
[10:30:58.728]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:30:58.728]    </control>
[10:30:58.728]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:30:58.729]      // if-block "DoDebugPortStop"
[10:30:58.729]        // =>  TRUE
[10:30:58.729]      <block atomic="false" info="">
[10:30:58.729]        WriteDP(DP_SELECT, 0x00000000);
[10:30:58.730]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:30:58.730]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:30:58.730]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:30:58.731]      </block>
[10:30:58.731]      // end if-block "DoDebugPortStop"
[10:30:58.732]    </control>
[10:30:58.732]  </sequence>
[10:30:58.732]  
[10:58:21.353]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:58:21.353]  
[10:58:21.353]  <debugvars>
[10:58:21.353]    // Pre-defined
[10:58:21.354]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:58:21.354]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:58:21.354]    __dp=0x00000000
[10:58:21.355]    __ap=0x00000000
[10:58:21.355]    __traceout=0x00000000      (Trace Disabled)
[10:58:21.355]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:58:21.356]    __FlashAddr=0x00000000
[10:58:21.356]    __FlashLen=0x00000000
[10:58:21.356]    __FlashArg=0x00000000
[10:58:21.357]    __FlashOp=0x00000000
[10:58:21.357]    __Result=0x00000000
[10:58:21.357]    
[10:58:21.357]    // User-defined
[10:58:21.357]    DbgMCU_CR=0x00000007
[10:58:21.358]    DbgMCU_APB1_Fz=0x00000000
[10:58:21.358]    DbgMCU_APB2_Fz=0x00000000
[10:58:21.358]    DoOptionByteLoading=0x00000000
[10:58:21.358]  </debugvars>
[10:58:21.358]  
[10:58:21.359]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:58:21.359]    <block atomic="false" info="">
[10:58:21.359]      Sequence("CheckID");
[10:58:21.360]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:58:21.360]          <block atomic="false" info="">
[10:58:21.360]            __var pidr1 = 0;
[10:58:21.360]              // -> [pidr1 <= 0x00000000]
[10:58:21.360]            __var pidr2 = 0;
[10:58:21.361]              // -> [pidr2 <= 0x00000000]
[10:58:21.361]            __var jep106id = 0;
[10:58:21.361]              // -> [jep106id <= 0x00000000]
[10:58:21.361]            __var ROMTableBase = 0;
[10:58:21.361]              // -> [ROMTableBase <= 0x00000000]
[10:58:21.362]            __ap = 0;      // AHB-AP
[10:58:21.362]              // -> [__ap <= 0x00000000]
[10:58:21.362]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:58:21.363]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:58:21.363]              // -> [ROMTableBase <= 0xF0000000]
[10:58:21.364]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:58:21.365]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:58:21.366]              // -> [pidr1 <= 0x00000004]
[10:58:21.367]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:58:21.367]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:58:21.368]              // -> [pidr2 <= 0x0000000A]
[10:58:21.368]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:58:21.368]              // -> [jep106id <= 0x00000020]
[10:58:21.369]          </block>
[10:58:21.369]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:58:21.369]            // if-block "jep106id != 0x20"
[10:58:21.369]              // =>  FALSE
[10:58:21.369]            // skip if-block "jep106id != 0x20"
[10:58:21.370]          </control>
[10:58:21.370]        </sequence>
[10:58:21.370]    </block>
[10:58:21.371]  </sequence>
[10:58:21.371]  
[10:58:21.383]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:58:21.383]  
[10:58:21.403]  <debugvars>
[10:58:21.405]    // Pre-defined
[10:58:21.406]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:58:21.406]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:58:21.407]    __dp=0x00000000
[10:58:21.407]    __ap=0x00000000
[10:58:21.408]    __traceout=0x00000000      (Trace Disabled)
[10:58:21.408]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:58:21.408]    __FlashAddr=0x00000000
[10:58:21.409]    __FlashLen=0x00000000
[10:58:21.409]    __FlashArg=0x00000000
[10:58:21.410]    __FlashOp=0x00000000
[10:58:21.410]    __Result=0x00000000
[10:58:21.411]    
[10:58:21.411]    // User-defined
[10:58:21.411]    DbgMCU_CR=0x00000007
[10:58:21.412]    DbgMCU_APB1_Fz=0x00000000
[10:58:21.412]    DbgMCU_APB2_Fz=0x00000000
[10:58:21.413]    DoOptionByteLoading=0x00000000
[10:58:21.413]  </debugvars>
[10:58:21.414]  
[10:58:21.414]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:58:21.414]    <block atomic="false" info="">
[10:58:21.415]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:58:21.416]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:58:21.416]    </block>
[10:58:21.417]    <block atomic="false" info="DbgMCU registers">
[10:58:21.417]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:58:21.418]        // -> [Read32(0x40021034) => 0x00004201]   (__dp=0x00000000, __ap=0x00000000)
[10:58:21.419]        // -> [Write32(0x40021034, 0x00404201)]   (__dp=0x00000000, __ap=0x00000000)
[10:58:21.419]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:58:21.420]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:58:21.420]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:58:21.421]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:58:21.422]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:58:21.423]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:58:21.423]    </block>
[10:58:21.423]  </sequence>
[10:58:21.423]  
[10:58:28.801]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:58:28.801]  
[10:58:28.803]  <debugvars>
[10:58:28.803]    // Pre-defined
[10:58:28.804]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:58:28.804]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:58:28.804]    __dp=0x00000000
[10:58:28.805]    __ap=0x00000000
[10:58:28.805]    __traceout=0x00000000      (Trace Disabled)
[10:58:28.805]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:58:28.806]    __FlashAddr=0x00000000
[10:58:28.806]    __FlashLen=0x00000000
[10:58:28.807]    __FlashArg=0x00000000
[10:58:28.807]    __FlashOp=0x00000000
[10:58:28.807]    __Result=0x00000000
[10:58:28.808]    
[10:58:28.808]    // User-defined
[10:58:28.808]    DbgMCU_CR=0x00000007
[10:58:28.808]    DbgMCU_APB1_Fz=0x00000000
[10:58:28.808]    DbgMCU_APB2_Fz=0x00000000
[10:58:28.809]    DoOptionByteLoading=0x00000000
[10:58:28.809]  </debugvars>
[10:58:28.810]  
[10:58:28.810]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:58:28.810]    <block atomic="false" info="">
[10:58:28.810]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:58:28.810]        // -> [connectionFlash <= 0x00000001]
[10:58:28.810]      __var FLASH_BASE = 0x40022000 ;
[10:58:28.811]        // -> [FLASH_BASE <= 0x40022000]
[10:58:28.811]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:58:28.811]        // -> [FLASH_CR <= 0x40022004]
[10:58:28.811]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:58:28.811]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:58:28.812]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:58:28.812]        // -> [LOCK_BIT <= 0x00000001]
[10:58:28.812]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:58:28.812]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:58:28.812]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:58:28.812]        // -> [FLASH_KEYR <= 0x4002200C]
[10:58:28.813]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:58:28.813]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:58:28.813]      __var FLASH_KEY2 = 0x02030405 ;
[10:58:28.813]        // -> [FLASH_KEY2 <= 0x02030405]
[10:58:28.813]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:58:28.814]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:58:28.814]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:58:28.814]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:58:28.814]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:58:28.814]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:58:28.815]      __var FLASH_CR_Value = 0 ;
[10:58:28.815]        // -> [FLASH_CR_Value <= 0x00000000]
[10:58:28.815]      __var DoDebugPortStop = 1 ;
[10:58:28.815]        // -> [DoDebugPortStop <= 0x00000001]
[10:58:28.815]      __var DP_CTRL_STAT = 0x4 ;
[10:58:28.815]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:58:28.816]      __var DP_SELECT = 0x8 ;
[10:58:28.816]        // -> [DP_SELECT <= 0x00000008]
[10:58:28.816]    </block>
[10:58:28.816]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:58:28.816]      // if-block "connectionFlash && DoOptionByteLoading"
[10:58:28.817]        // =>  FALSE
[10:58:28.817]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:58:28.817]    </control>
[10:58:28.817]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:58:28.817]      // if-block "DoDebugPortStop"
[10:58:28.817]        // =>  TRUE
[10:58:28.818]      <block atomic="false" info="">
[10:58:28.818]        WriteDP(DP_SELECT, 0x00000000);
[10:58:28.818]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:58:28.819]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:58:28.819]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:58:28.819]      </block>
[10:58:28.820]      // end if-block "DoDebugPortStop"
[10:58:28.820]    </control>
[10:58:28.820]  </sequence>
[10:58:28.821]  
[11:03:39.341]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:03:39.341]  
[11:03:39.342]  <debugvars>
[11:03:39.342]    // Pre-defined
[11:03:39.342]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:03:39.343]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:03:39.344]    __dp=0x00000000
[11:03:39.344]    __ap=0x00000000
[11:03:39.344]    __traceout=0x00000000      (Trace Disabled)
[11:03:39.345]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:03:39.345]    __FlashAddr=0x00000000
[11:03:39.345]    __FlashLen=0x00000000
[11:03:39.346]    __FlashArg=0x00000000
[11:03:39.346]    __FlashOp=0x00000000
[11:03:39.346]    __Result=0x00000000
[11:03:39.346]    
[11:03:39.346]    // User-defined
[11:03:39.346]    DbgMCU_CR=0x00000007
[11:03:39.347]    DbgMCU_APB1_Fz=0x00000000
[11:03:39.347]    DbgMCU_APB2_Fz=0x00000000
[11:03:39.347]    DoOptionByteLoading=0x00000000
[11:03:39.347]  </debugvars>
[11:03:39.347]  
[11:03:39.348]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:03:39.348]    <block atomic="false" info="">
[11:03:39.348]      Sequence("CheckID");
[11:03:39.348]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:03:39.348]          <block atomic="false" info="">
[11:03:39.348]            __var pidr1 = 0;
[11:03:39.349]              // -> [pidr1 <= 0x00000000]
[11:03:39.349]            __var pidr2 = 0;
[11:03:39.349]              // -> [pidr2 <= 0x00000000]
[11:03:39.349]            __var jep106id = 0;
[11:03:39.349]              // -> [jep106id <= 0x00000000]
[11:03:39.350]            __var ROMTableBase = 0;
[11:03:39.350]              // -> [ROMTableBase <= 0x00000000]
[11:03:39.350]            __ap = 0;      // AHB-AP
[11:03:39.350]              // -> [__ap <= 0x00000000]
[11:03:39.350]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:03:39.351]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:03:39.351]              // -> [ROMTableBase <= 0xF0000000]
[11:03:39.351]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:03:39.352]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:03:39.352]              // -> [pidr1 <= 0x00000004]
[11:03:39.353]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:03:39.354]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:03:39.354]              // -> [pidr2 <= 0x0000000A]
[11:03:39.354]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:03:39.354]              // -> [jep106id <= 0x00000020]
[11:03:39.355]          </block>
[11:03:39.355]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:03:39.355]            // if-block "jep106id != 0x20"
[11:03:39.355]              // =>  FALSE
[11:03:39.356]            // skip if-block "jep106id != 0x20"
[11:03:39.356]          </control>
[11:03:39.356]        </sequence>
[11:03:39.356]    </block>
[11:03:39.356]  </sequence>
[11:03:39.356]  
[11:03:39.368]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:03:39.368]  
[11:03:39.386]  <debugvars>
[11:03:39.386]    // Pre-defined
[11:03:39.388]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:03:39.388]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:03:39.389]    __dp=0x00000000
[11:03:39.389]    __ap=0x00000000
[11:03:39.390]    __traceout=0x00000000      (Trace Disabled)
[11:03:39.391]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:03:39.391]    __FlashAddr=0x00000000
[11:03:39.392]    __FlashLen=0x00000000
[11:03:39.392]    __FlashArg=0x00000000
[11:03:39.392]    __FlashOp=0x00000000
[11:03:39.392]    __Result=0x00000000
[11:03:39.392]    
[11:03:39.392]    // User-defined
[11:03:39.394]    DbgMCU_CR=0x00000007
[11:03:39.394]    DbgMCU_APB1_Fz=0x00000000
[11:03:39.394]    DbgMCU_APB2_Fz=0x00000000
[11:03:39.395]    DoOptionByteLoading=0x00000000
[11:03:39.395]  </debugvars>
[11:03:39.397]  
[11:03:39.397]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:03:39.398]    <block atomic="false" info="">
[11:03:39.398]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:03:39.399]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:03:39.400]    </block>
[11:03:39.400]    <block atomic="false" info="DbgMCU registers">
[11:03:39.400]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:03:39.401]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:03:39.402]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:03:39.402]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:03:39.403]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:03:39.403]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:03:39.404]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:03:39.404]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:03:39.405]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:03:39.406]    </block>
[11:03:39.406]  </sequence>
[11:03:39.406]  
[11:03:46.846]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:03:46.846]  
[11:03:46.846]  <debugvars>
[11:03:46.846]    // Pre-defined
[11:03:46.846]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:03:46.846]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:03:46.846]    __dp=0x00000000
[11:03:46.846]    __ap=0x00000000
[11:03:46.846]    __traceout=0x00000000      (Trace Disabled)
[11:03:46.846]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:03:46.846]    __FlashAddr=0x00000000
[11:03:46.846]    __FlashLen=0x00000000
[11:03:46.846]    __FlashArg=0x00000000
[11:03:46.846]    __FlashOp=0x00000000
[11:03:46.846]    __Result=0x00000000
[11:03:46.846]    
[11:03:46.846]    // User-defined
[11:03:46.846]    DbgMCU_CR=0x00000007
[11:03:46.846]    DbgMCU_APB1_Fz=0x00000000
[11:03:46.861]    DbgMCU_APB2_Fz=0x00000000
[11:03:46.861]    DoOptionByteLoading=0x00000000
[11:03:46.861]  </debugvars>
[11:03:46.861]  
[11:03:46.861]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:03:46.861]    <block atomic="false" info="">
[11:03:46.861]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:03:46.861]        // -> [connectionFlash <= 0x00000001]
[11:03:46.861]      __var FLASH_BASE = 0x40022000 ;
[11:03:46.861]        // -> [FLASH_BASE <= 0x40022000]
[11:03:46.861]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:03:46.861]        // -> [FLASH_CR <= 0x40022004]
[11:03:46.861]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:03:46.861]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:03:46.861]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:03:46.861]        // -> [LOCK_BIT <= 0x00000001]
[11:03:46.861]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:03:46.861]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:03:46.861]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:03:46.861]        // -> [FLASH_KEYR <= 0x4002200C]
[11:03:46.861]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:03:46.861]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:03:46.861]      __var FLASH_KEY2 = 0x02030405 ;
[11:03:46.861]        // -> [FLASH_KEY2 <= 0x02030405]
[11:03:46.861]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:03:46.861]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:03:46.861]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:03:46.861]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:03:46.861]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:03:46.861]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:03:46.861]      __var FLASH_CR_Value = 0 ;
[11:03:46.861]        // -> [FLASH_CR_Value <= 0x00000000]
[11:03:46.861]      __var DoDebugPortStop = 1 ;
[11:03:46.861]        // -> [DoDebugPortStop <= 0x00000001]
[11:03:46.861]      __var DP_CTRL_STAT = 0x4 ;
[11:03:46.861]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:03:46.861]      __var DP_SELECT = 0x8 ;
[11:03:46.861]        // -> [DP_SELECT <= 0x00000008]
[11:03:46.861]    </block>
[11:03:46.861]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:03:46.861]      // if-block "connectionFlash && DoOptionByteLoading"
[11:03:46.861]        // =>  FALSE
[11:03:46.861]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:03:46.861]    </control>
[11:03:46.861]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:03:46.861]      // if-block "DoDebugPortStop"
[11:03:46.861]        // =>  TRUE
[11:03:46.861]      <block atomic="false" info="">
[11:03:46.872]        WriteDP(DP_SELECT, 0x00000000);
[11:03:46.872]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:03:46.872]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:03:46.874]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:03:46.874]      </block>
[11:03:46.874]      // end if-block "DoDebugPortStop"
[11:03:46.874]    </control>
[11:03:46.874]  </sequence>
[11:03:46.874]  
[11:09:23.414]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:09:23.414]  
[11:09:23.414]  <debugvars>
[11:09:23.414]    // Pre-defined
[11:09:23.414]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:09:23.414]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:09:23.414]    __dp=0x00000000
[11:09:23.418]    __ap=0x00000000
[11:09:23.418]    __traceout=0x00000000      (Trace Disabled)
[11:09:23.418]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:09:23.418]    __FlashAddr=0x00000000
[11:09:23.418]    __FlashLen=0x00000000
[11:09:23.418]    __FlashArg=0x00000000
[11:09:23.418]    __FlashOp=0x00000000
[11:09:23.418]    __Result=0x00000000
[11:09:23.418]    
[11:09:23.418]    // User-defined
[11:09:23.418]    DbgMCU_CR=0x00000007
[11:09:23.418]    DbgMCU_APB1_Fz=0x00000000
[11:09:23.418]    DbgMCU_APB2_Fz=0x00000000
[11:09:23.418]    DoOptionByteLoading=0x00000000
[11:09:23.418]  </debugvars>
[11:09:23.418]  
[11:09:23.418]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:09:23.418]    <block atomic="false" info="">
[11:09:23.418]      Sequence("CheckID");
[11:09:23.418]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:09:23.422]          <block atomic="false" info="">
[11:09:23.422]            __var pidr1 = 0;
[11:09:23.422]              // -> [pidr1 <= 0x00000000]
[11:09:23.422]            __var pidr2 = 0;
[11:09:23.422]              // -> [pidr2 <= 0x00000000]
[11:09:23.422]            __var jep106id = 0;
[11:09:23.422]              // -> [jep106id <= 0x00000000]
[11:09:23.422]            __var ROMTableBase = 0;
[11:09:23.422]              // -> [ROMTableBase <= 0x00000000]
[11:09:23.422]            __ap = 0;      // AHB-AP
[11:09:23.422]              // -> [__ap <= 0x00000000]
[11:09:23.424]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:09:23.425]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:09:23.425]              // -> [ROMTableBase <= 0xF0000000]
[11:09:23.425]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:09:23.425]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:09:23.425]              // -> [pidr1 <= 0x00000004]
[11:09:23.425]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:09:23.425]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:09:23.425]              // -> [pidr2 <= 0x0000000A]
[11:09:23.425]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:09:23.425]              // -> [jep106id <= 0x00000020]
[11:09:23.429]          </block>
[11:09:23.429]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:09:23.429]            // if-block "jep106id != 0x20"
[11:09:23.429]              // =>  FALSE
[11:09:23.429]            // skip if-block "jep106id != 0x20"
[11:09:23.429]          </control>
[11:09:23.429]        </sequence>
[11:09:23.429]    </block>
[11:09:23.429]  </sequence>
[11:09:23.429]  
[11:09:23.442]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:09:23.442]  
[11:09:23.442]  <debugvars>
[11:09:23.442]    // Pre-defined
[11:09:23.442]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:09:23.444]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:09:23.444]    __dp=0x00000000
[11:09:23.444]    __ap=0x00000000
[11:09:23.444]    __traceout=0x00000000      (Trace Disabled)
[11:09:23.444]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:09:23.444]    __FlashAddr=0x00000000
[11:09:23.444]    __FlashLen=0x00000000
[11:09:23.444]    __FlashArg=0x00000000
[11:09:23.444]    __FlashOp=0x00000000
[11:09:23.444]    __Result=0x00000000
[11:09:23.444]    
[11:09:23.444]    // User-defined
[11:09:23.444]    DbgMCU_CR=0x00000007
[11:09:23.444]    DbgMCU_APB1_Fz=0x00000000
[11:09:23.444]    DbgMCU_APB2_Fz=0x00000000
[11:09:23.444]    DoOptionByteLoading=0x00000000
[11:09:23.444]  </debugvars>
[11:09:23.444]  
[11:09:23.448]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:09:23.448]    <block atomic="false" info="">
[11:09:23.448]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:09:23.448]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:09:23.448]    </block>
[11:09:23.448]    <block atomic="false" info="DbgMCU registers">
[11:09:23.448]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:09:23.448]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:09:23.452]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:09:23.452]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:09:23.453]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:09:23.453]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:09:23.454]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:09:23.454]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:09:23.456]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:09:23.456]    </block>
[11:09:23.456]  </sequence>
[11:09:23.456]  
[11:09:30.838]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:09:30.838]  
[11:09:30.839]  <debugvars>
[11:09:30.839]    // Pre-defined
[11:09:30.840]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:09:30.840]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:09:30.841]    __dp=0x00000000
[11:09:30.841]    __ap=0x00000000
[11:09:30.841]    __traceout=0x00000000      (Trace Disabled)
[11:09:30.842]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:09:30.842]    __FlashAddr=0x00000000
[11:09:30.842]    __FlashLen=0x00000000
[11:09:30.842]    __FlashArg=0x00000000
[11:09:30.843]    __FlashOp=0x00000000
[11:09:30.843]    __Result=0x00000000
[11:09:30.843]    
[11:09:30.843]    // User-defined
[11:09:30.844]    DbgMCU_CR=0x00000007
[11:09:30.844]    DbgMCU_APB1_Fz=0x00000000
[11:09:30.844]    DbgMCU_APB2_Fz=0x00000000
[11:09:30.844]    DoOptionByteLoading=0x00000000
[11:09:30.844]  </debugvars>
[11:09:30.845]  
[11:09:30.845]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:09:30.845]    <block atomic="false" info="">
[11:09:30.846]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:09:30.846]        // -> [connectionFlash <= 0x00000001]
[11:09:30.846]      __var FLASH_BASE = 0x40022000 ;
[11:09:30.846]        // -> [FLASH_BASE <= 0x40022000]
[11:09:30.846]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:09:30.847]        // -> [FLASH_CR <= 0x40022004]
[11:09:30.847]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:09:30.847]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:09:30.847]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:09:30.847]        // -> [LOCK_BIT <= 0x00000001]
[11:09:30.848]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:09:30.848]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:09:30.848]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:09:30.848]        // -> [FLASH_KEYR <= 0x4002200C]
[11:09:30.848]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:09:30.848]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:09:30.849]      __var FLASH_KEY2 = 0x02030405 ;
[11:09:30.849]        // -> [FLASH_KEY2 <= 0x02030405]
[11:09:30.849]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:09:30.849]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:09:30.849]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:09:30.850]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:09:30.850]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:09:30.850]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:09:30.850]      __var FLASH_CR_Value = 0 ;
[11:09:30.850]        // -> [FLASH_CR_Value <= 0x00000000]
[11:09:30.851]      __var DoDebugPortStop = 1 ;
[11:09:30.851]        // -> [DoDebugPortStop <= 0x00000001]
[11:09:30.851]      __var DP_CTRL_STAT = 0x4 ;
[11:09:30.851]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:09:30.852]      __var DP_SELECT = 0x8 ;
[11:09:30.852]        // -> [DP_SELECT <= 0x00000008]
[11:09:30.852]    </block>
[11:09:30.852]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:09:30.852]      // if-block "connectionFlash && DoOptionByteLoading"
[11:09:30.853]        // =>  FALSE
[11:09:30.853]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:09:30.853]    </control>
[11:09:30.853]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:09:30.853]      // if-block "DoDebugPortStop"
[11:09:30.854]        // =>  TRUE
[11:09:30.854]      <block atomic="false" info="">
[11:09:30.854]        WriteDP(DP_SELECT, 0x00000000);
[11:09:30.855]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:09:30.855]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:09:30.856]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:09:30.856]      </block>
[11:09:30.856]      // end if-block "DoDebugPortStop"
[11:09:30.856]    </control>
[11:09:30.857]  </sequence>
[11:09:30.857]  
[11:28:03.813]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:28:03.813]  
[11:28:03.813]  <debugvars>
[11:28:03.813]    // Pre-defined
[11:28:03.813]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:28:03.813]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[11:28:03.813]    __dp=0x00000000
[11:28:03.813]    __ap=0x00000000
[11:28:03.813]    __traceout=0x00000000      (Trace Disabled)
[11:28:03.813]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:28:03.813]    __FlashAddr=0x00000000
[11:28:03.813]    __FlashLen=0x00000000
[11:28:03.813]    __FlashArg=0x00000000
[11:28:03.813]    __FlashOp=0x00000000
[11:28:03.818]    __Result=0x00000000
[11:28:03.818]    
[11:28:03.818]    // User-defined
[11:28:03.818]    DbgMCU_CR=0x00000007
[11:28:03.818]    DbgMCU_APB1_Fz=0x00000000
[11:28:03.818]    DbgMCU_APB2_Fz=0x00000000
[11:28:03.818]    DoOptionByteLoading=0x00000000
[11:28:03.818]  </debugvars>
[11:28:03.818]  
[11:28:03.818]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:28:03.818]    <block atomic="false" info="">
[11:28:03.820]      Sequence("CheckID");
[11:28:03.820]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:28:03.820]          <block atomic="false" info="">
[11:28:03.820]            __var pidr1 = 0;
[11:28:03.820]              // -> [pidr1 <= 0x00000000]
[11:28:03.820]            __var pidr2 = 0;
[11:28:03.820]              // -> [pidr2 <= 0x00000000]
[11:28:03.820]            __var jep106id = 0;
[11:28:03.820]              // -> [jep106id <= 0x00000000]
[11:28:03.820]            __var ROMTableBase = 0;
[11:28:03.820]              // -> [ROMTableBase <= 0x00000000]
[11:28:03.820]            __ap = 0;      // AHB-AP
[11:28:03.820]              // -> [__ap <= 0x00000000]
[11:28:03.820]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:28:03.820]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:28:03.820]              // -> [ROMTableBase <= 0xF0000000]
[11:28:03.820]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:28:03.824]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:28:03.824]              // -> [pidr1 <= 0x00000004]
[11:28:03.824]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:28:03.824]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:28:03.824]              // -> [pidr2 <= 0x0000000A]
[11:28:03.824]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:28:03.827]              // -> [jep106id <= 0x00000020]
[11:28:03.827]          </block>
[11:28:03.827]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:28:03.827]            // if-block "jep106id != 0x20"
[11:28:03.827]              // =>  FALSE
[11:28:03.827]            // skip if-block "jep106id != 0x20"
[11:28:03.827]          </control>
[11:28:03.827]        </sequence>
[11:28:03.827]    </block>
[11:28:03.827]  </sequence>
[11:28:03.827]  
[11:28:03.839]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:28:03.839]  
[11:28:03.845]  <debugvars>
[11:28:03.845]    // Pre-defined
[11:28:03.845]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:28:03.845]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[11:28:03.847]    __dp=0x00000000
[11:28:03.847]    __ap=0x00000000
[11:28:03.847]    __traceout=0x00000000      (Trace Disabled)
[11:28:03.847]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:28:03.847]    __FlashAddr=0x00000000
[11:28:03.847]    __FlashLen=0x00000000
[11:28:03.847]    __FlashArg=0x00000000
[11:28:03.847]    __FlashOp=0x00000000
[11:28:03.847]    __Result=0x00000000
[11:28:03.847]    
[11:28:03.847]    // User-defined
[11:28:03.847]    DbgMCU_CR=0x00000007
[11:28:03.849]    DbgMCU_APB1_Fz=0x00000000
[11:28:03.849]    DbgMCU_APB2_Fz=0x00000000
[11:28:03.849]    DoOptionByteLoading=0x00000000
[11:28:03.849]  </debugvars>
[11:28:03.849]  
[11:28:03.849]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:28:03.849]    <block atomic="false" info="">
[11:28:03.849]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:28:03.849]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:28:03.849]    </block>
[11:28:03.849]    <block atomic="false" info="DbgMCU registers">
[11:28:03.849]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:28:03.849]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:28:03.849]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:28:03.849]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:28:03.854]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:28:03.854]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:28:03.854]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:28:03.854]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:28:03.854]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:28:03.857]    </block>
[11:28:03.857]  </sequence>
[11:28:03.857]  
[11:31:54.039]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:31:54.039]  
[11:31:54.040]  <debugvars>
[11:31:54.041]    // Pre-defined
[11:31:54.041]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:31:54.041]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[11:31:54.042]    __dp=0x00000000
[11:31:54.042]    __ap=0x00000000
[11:31:54.042]    __traceout=0x00000000      (Trace Disabled)
[11:31:54.043]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:31:54.043]    __FlashAddr=0x00000000
[11:31:54.043]    __FlashLen=0x00000000
[11:31:54.044]    __FlashArg=0x00000000
[11:31:54.044]    __FlashOp=0x00000000
[11:31:54.044]    __Result=0x00000000
[11:31:54.044]    
[11:31:54.044]    // User-defined
[11:31:54.045]    DbgMCU_CR=0x00000007
[11:31:54.045]    DbgMCU_APB1_Fz=0x00000000
[11:31:54.045]    DbgMCU_APB2_Fz=0x00000000
[11:31:54.046]    DoOptionByteLoading=0x00000000
[11:31:54.046]  </debugvars>
[11:31:54.046]  
[11:31:54.046]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:31:54.047]    <block atomic="false" info="">
[11:31:54.047]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:31:54.047]        // -> [connectionFlash <= 0x00000000]
[11:31:54.047]      __var FLASH_BASE = 0x40022000 ;
[11:31:54.047]        // -> [FLASH_BASE <= 0x40022000]
[11:31:54.048]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:31:54.048]        // -> [FLASH_CR <= 0x40022004]
[11:31:54.048]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:31:54.048]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:31:54.048]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:31:54.048]        // -> [LOCK_BIT <= 0x00000001]
[11:31:54.049]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:31:54.049]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:31:54.049]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:31:54.049]        // -> [FLASH_KEYR <= 0x4002200C]
[11:31:54.049]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:31:54.050]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:31:54.050]      __var FLASH_KEY2 = 0x02030405 ;
[11:31:54.050]        // -> [FLASH_KEY2 <= 0x02030405]
[11:31:54.050]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:31:54.050]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:31:54.050]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:31:54.051]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:31:54.051]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:31:54.051]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:31:54.051]      __var FLASH_CR_Value = 0 ;
[11:31:54.051]        // -> [FLASH_CR_Value <= 0x00000000]
[11:31:54.052]      __var DoDebugPortStop = 1 ;
[11:31:54.052]        // -> [DoDebugPortStop <= 0x00000001]
[11:31:54.052]      __var DP_CTRL_STAT = 0x4 ;
[11:31:54.052]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:31:54.052]      __var DP_SELECT = 0x8 ;
[11:31:54.053]        // -> [DP_SELECT <= 0x00000008]
[11:31:54.053]    </block>
[11:31:54.053]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:31:54.053]      // if-block "connectionFlash && DoOptionByteLoading"
[11:31:54.053]        // =>  FALSE
[11:31:54.053]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:31:54.054]    </control>
[11:31:54.054]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:31:54.054]      // if-block "DoDebugPortStop"
[11:31:54.054]        // =>  TRUE
[11:31:54.054]      <block atomic="false" info="">
[11:31:54.055]        WriteDP(DP_SELECT, 0x00000000);
[11:31:54.056]  
[11:31:54.056]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:31:54.056]  
[11:31:54.056]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:31:54.056]      </block>
[11:31:54.057]      // end if-block "DoDebugPortStop"
[11:31:54.057]    </control>
[11:31:54.057]  </sequence>
[11:31:54.057]  
[11:36:35.648]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:36:35.648]  
[11:36:35.648]  <debugvars>
[11:36:35.648]    // Pre-defined
[11:36:35.648]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:36:35.648]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:36:35.648]    __dp=0x00000000
[11:36:35.648]    __ap=0x00000000
[11:36:35.648]    __traceout=0x00000000      (Trace Disabled)
[11:36:35.648]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:36:35.648]    __FlashAddr=0x00000000
[11:36:35.648]    __FlashLen=0x00000000
[11:36:35.648]    __FlashArg=0x00000000
[11:36:35.648]    __FlashOp=0x00000000
[11:36:35.648]    __Result=0x00000000
[11:36:35.648]    
[11:36:35.648]    // User-defined
[11:36:35.648]    DbgMCU_CR=0x00000007
[11:36:35.648]    DbgMCU_APB1_Fz=0x00000000
[11:36:35.648]    DbgMCU_APB2_Fz=0x00000000
[11:36:35.648]    DoOptionByteLoading=0x00000000
[11:36:35.648]  </debugvars>
[11:36:35.648]  
[11:36:35.648]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:36:35.648]    <block atomic="false" info="">
[11:36:35.648]      Sequence("CheckID");
[11:36:35.648]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:36:35.648]          <block atomic="false" info="">
[11:36:35.648]            __var pidr1 = 0;
[11:36:35.648]              // -> [pidr1 <= 0x00000000]
[11:36:35.648]            __var pidr2 = 0;
[11:36:35.648]              // -> [pidr2 <= 0x00000000]
[11:36:35.648]            __var jep106id = 0;
[11:36:35.648]              // -> [jep106id <= 0x00000000]
[11:36:35.648]            __var ROMTableBase = 0;
[11:36:35.648]              // -> [ROMTableBase <= 0x00000000]
[11:36:35.648]            __ap = 0;      // AHB-AP
[11:36:35.648]              // -> [__ap <= 0x00000000]
[11:36:35.648]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:36:35.648]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:36:35.648]              // -> [ROMTableBase <= 0xF0000000]
[11:36:35.648]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:36:35.663]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:36:35.663]              // -> [pidr1 <= 0x00000004]
[11:36:35.663]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:36:35.663]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:36:35.663]              // -> [pidr2 <= 0x0000000A]
[11:36:35.665]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:36:35.665]              // -> [jep106id <= 0x00000020]
[11:36:35.665]          </block>
[11:36:35.665]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:36:35.665]            // if-block "jep106id != 0x20"
[11:36:35.665]              // =>  FALSE
[11:36:35.665]            // skip if-block "jep106id != 0x20"
[11:36:35.665]          </control>
[11:36:35.665]        </sequence>
[11:36:35.665]    </block>
[11:36:35.665]  </sequence>
[11:36:35.667]  
[11:36:35.678]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:36:35.678]  
[11:36:35.678]  <debugvars>
[11:36:35.678]    // Pre-defined
[11:36:35.678]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:36:35.678]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:36:35.678]    __dp=0x00000000
[11:36:35.678]    __ap=0x00000000
[11:36:35.678]    __traceout=0x00000000      (Trace Disabled)
[11:36:35.678]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:36:35.678]    __FlashAddr=0x00000000
[11:36:35.678]    __FlashLen=0x00000000
[11:36:35.678]    __FlashArg=0x00000000
[11:36:35.678]    __FlashOp=0x00000000
[11:36:35.678]    __Result=0x00000000
[11:36:35.678]    
[11:36:35.678]    // User-defined
[11:36:35.678]    DbgMCU_CR=0x00000007
[11:36:35.678]    DbgMCU_APB1_Fz=0x00000000
[11:36:35.678]    DbgMCU_APB2_Fz=0x00000000
[11:36:35.678]    DoOptionByteLoading=0x00000000
[11:36:35.678]  </debugvars>
[11:36:35.678]  
[11:36:35.678]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:36:35.678]    <block atomic="false" info="">
[11:36:35.678]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:36:35.678]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:36:35.678]    </block>
[11:36:35.678]    <block atomic="false" info="DbgMCU registers">
[11:36:35.678]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:36:35.678]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:36:35.678]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:36:35.678]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:36:35.678]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:36:35.678]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:36:35.678]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:36:35.678]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:36:35.689]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:36:35.689]    </block>
[11:36:35.689]  </sequence>
[11:36:35.689]  
[11:36:43.106]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:36:43.106]  
[11:36:43.106]  <debugvars>
[11:36:43.106]    // Pre-defined
[11:36:43.106]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:36:43.106]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:36:43.106]    __dp=0x00000000
[11:36:43.108]    __ap=0x00000000
[11:36:43.108]    __traceout=0x00000000      (Trace Disabled)
[11:36:43.108]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:36:43.108]    __FlashAddr=0x00000000
[11:36:43.108]    __FlashLen=0x00000000
[11:36:43.108]    __FlashArg=0x00000000
[11:36:43.108]    __FlashOp=0x00000000
[11:36:43.108]    __Result=0x00000000
[11:36:43.108]    
[11:36:43.108]    // User-defined
[11:36:43.108]    DbgMCU_CR=0x00000007
[11:36:43.108]    DbgMCU_APB1_Fz=0x00000000
[11:36:43.108]    DbgMCU_APB2_Fz=0x00000000
[11:36:43.108]    DoOptionByteLoading=0x00000000
[11:36:43.108]  </debugvars>
[11:36:43.108]  
[11:36:43.112]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:36:43.112]    <block atomic="false" info="">
[11:36:43.112]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:36:43.112]        // -> [connectionFlash <= 0x00000001]
[11:36:43.112]      __var FLASH_BASE = 0x40022000 ;
[11:36:43.112]        // -> [FLASH_BASE <= 0x40022000]
[11:36:43.112]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:36:43.112]        // -> [FLASH_CR <= 0x40022004]
[11:36:43.112]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:36:43.112]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:36:43.112]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:36:43.112]        // -> [LOCK_BIT <= 0x00000001]
[11:36:43.112]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:36:43.112]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:36:43.112]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:36:43.112]        // -> [FLASH_KEYR <= 0x4002200C]
[11:36:43.112]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:36:43.112]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:36:43.112]      __var FLASH_KEY2 = 0x02030405 ;
[11:36:43.112]        // -> [FLASH_KEY2 <= 0x02030405]
[11:36:43.112]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:36:43.112]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:36:43.117]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:36:43.117]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:36:43.117]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:36:43.118]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:36:43.118]      __var FLASH_CR_Value = 0 ;
[11:36:43.118]        // -> [FLASH_CR_Value <= 0x00000000]
[11:36:43.119]      __var DoDebugPortStop = 1 ;
[11:36:43.119]        // -> [DoDebugPortStop <= 0x00000001]
[11:36:43.119]      __var DP_CTRL_STAT = 0x4 ;
[11:36:43.119]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:36:43.120]      __var DP_SELECT = 0x8 ;
[11:36:43.120]        // -> [DP_SELECT <= 0x00000008]
[11:36:43.120]    </block>
[11:36:43.120]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:36:43.120]      // if-block "connectionFlash && DoOptionByteLoading"
[11:36:43.120]        // =>  FALSE
[11:36:43.121]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:36:43.121]    </control>
[11:36:43.121]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:36:43.121]      // if-block "DoDebugPortStop"
[11:36:43.121]        // =>  TRUE
[11:36:43.122]      <block atomic="false" info="">
[11:36:43.122]        WriteDP(DP_SELECT, 0x00000000);
[11:36:43.128]  
[11:36:43.128]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:36:43.128]  
[11:36:43.135]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:36:43.137]      </block>
[11:36:43.138]      // end if-block "DoDebugPortStop"
[11:36:43.138]    </control>
[11:36:43.139]  </sequence>
[11:36:43.139]  
[11:36:44.416]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:36:44.416]  
[11:36:44.417]  <debugvars>
[11:36:44.417]    // Pre-defined
[11:36:44.417]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:36:44.417]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:36:44.418]    __dp=0x00000000
[11:36:44.418]    __ap=0x00000000
[11:36:44.418]    __traceout=0x00000000      (Trace Disabled)
[11:36:44.419]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:36:44.419]    __FlashAddr=0x00000000
[11:36:44.419]    __FlashLen=0x00000000
[11:36:44.419]    __FlashArg=0x00000000
[11:36:44.419]    __FlashOp=0x00000000
[11:36:44.420]    __Result=0x00000000
[11:36:44.420]    
[11:36:44.420]    // User-defined
[11:36:44.420]    DbgMCU_CR=0x00000007
[11:36:44.420]    DbgMCU_APB1_Fz=0x00000000
[11:36:44.420]    DbgMCU_APB2_Fz=0x00000000
[11:36:44.420]    DoOptionByteLoading=0x00000000
[11:36:44.421]  </debugvars>
[11:36:44.421]  
[11:36:44.421]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:36:44.421]    <block atomic="false" info="">
[11:36:44.421]      Sequence("CheckID");
[11:36:44.422]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:36:44.422]          <block atomic="false" info="">
[11:36:44.422]            __var pidr1 = 0;
[11:36:44.422]              // -> [pidr1 <= 0x00000000]
[11:36:44.422]            __var pidr2 = 0;
[11:36:44.423]              // -> [pidr2 <= 0x00000000]
[11:36:44.423]            __var jep106id = 0;
[11:36:44.423]              // -> [jep106id <= 0x00000000]
[11:36:44.424]            __var ROMTableBase = 0;
[11:36:44.424]              // -> [ROMTableBase <= 0x00000000]
[11:36:44.424]            __ap = 0;      // AHB-AP
[11:36:44.424]              // -> [__ap <= 0x00000000]
[11:36:44.425]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:36:44.425]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:36:44.426]              // -> [ROMTableBase <= 0xF0000000]
[11:36:44.426]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:36:44.427]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:36:44.428]              // -> [pidr1 <= 0x00000004]
[11:36:44.428]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:36:44.429]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:36:44.429]              // -> [pidr2 <= 0x0000000A]
[11:36:44.430]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:36:44.430]              // -> [jep106id <= 0x00000020]
[11:36:44.430]          </block>
[11:36:44.430]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:36:44.431]            // if-block "jep106id != 0x20"
[11:36:44.431]              // =>  FALSE
[11:36:44.431]            // skip if-block "jep106id != 0x20"
[11:36:44.431]          </control>
[11:36:44.431]        </sequence>
[11:36:44.431]    </block>
[11:36:44.432]  </sequence>
[11:36:44.432]  
[11:36:44.444]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:36:44.444]  
[11:36:44.444]  <debugvars>
[11:36:44.444]    // Pre-defined
[11:36:44.445]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:36:44.445]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:36:44.445]    __dp=0x00000000
[11:36:44.446]    __ap=0x00000000
[11:36:44.446]    __traceout=0x00000000      (Trace Disabled)
[11:36:44.446]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:36:44.447]    __FlashAddr=0x00000000
[11:36:44.447]    __FlashLen=0x00000000
[11:36:44.447]    __FlashArg=0x00000000
[11:36:44.447]    __FlashOp=0x00000000
[11:36:44.447]    __Result=0x00000000
[11:36:44.447]    
[11:36:44.447]    // User-defined
[11:36:44.448]    DbgMCU_CR=0x00000007
[11:36:44.448]    DbgMCU_APB1_Fz=0x00000000
[11:36:44.448]    DbgMCU_APB2_Fz=0x00000000
[11:36:44.448]    DoOptionByteLoading=0x00000000
[11:36:44.448]  </debugvars>
[11:36:44.449]  
[11:36:44.449]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:36:44.449]    <block atomic="false" info="">
[11:36:44.449]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:36:44.450]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:36:44.450]    </block>
[11:36:44.451]    <block atomic="false" info="DbgMCU registers">
[11:36:44.451]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:36:44.452]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:36:44.453]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:36:44.453]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:36:44.454]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:36:44.454]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:36:44.455]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:36:44.455]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:36:44.455]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:36:44.455]    </block>
[11:36:44.455]  </sequence>
[11:36:44.457]  
[11:36:52.969]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:36:52.969]  
[11:36:52.970]  <debugvars>
[11:36:52.971]    // Pre-defined
[11:36:52.971]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:36:52.972]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:36:52.972]    __dp=0x00000000
[11:36:52.972]    __ap=0x00000000
[11:36:52.972]    __traceout=0x00000000      (Trace Disabled)
[11:36:52.972]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:36:52.972]    __FlashAddr=0x00000000
[11:36:52.972]    __FlashLen=0x00000000
[11:36:52.972]    __FlashArg=0x00000000
[11:36:52.975]    __FlashOp=0x00000000
[11:36:52.975]    __Result=0x00000000
[11:36:52.975]    
[11:36:52.975]    // User-defined
[11:36:52.976]    DbgMCU_CR=0x00000007
[11:36:52.977]    DbgMCU_APB1_Fz=0x00000000
[11:36:52.977]    DbgMCU_APB2_Fz=0x00000000
[11:36:52.977]    DoOptionByteLoading=0x00000000
[11:36:52.978]  </debugvars>
[11:36:52.978]  
[11:36:52.978]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:36:52.979]    <block atomic="false" info="">
[11:36:52.979]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:36:52.979]        // -> [connectionFlash <= 0x00000001]
[11:36:52.979]      __var FLASH_BASE = 0x40022000 ;
[11:36:52.980]        // -> [FLASH_BASE <= 0x40022000]
[11:36:52.980]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:36:52.980]        // -> [FLASH_CR <= 0x40022004]
[11:36:52.980]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:36:52.980]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:36:52.981]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:36:52.981]        // -> [LOCK_BIT <= 0x00000001]
[11:36:52.981]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:36:52.981]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:36:52.981]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:36:52.981]        // -> [FLASH_KEYR <= 0x4002200C]
[11:36:52.982]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:36:52.982]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:36:52.982]      __var FLASH_KEY2 = 0x02030405 ;
[11:36:52.982]        // -> [FLASH_KEY2 <= 0x02030405]
[11:36:52.982]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:36:52.983]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:36:52.983]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:36:52.983]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:36:52.983]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:36:52.983]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:36:52.983]      __var FLASH_CR_Value = 0 ;
[11:36:52.984]        // -> [FLASH_CR_Value <= 0x00000000]
[11:36:52.984]      __var DoDebugPortStop = 1 ;
[11:36:52.984]        // -> [DoDebugPortStop <= 0x00000001]
[11:36:52.984]      __var DP_CTRL_STAT = 0x4 ;
[11:36:52.984]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:36:52.984]      __var DP_SELECT = 0x8 ;
[11:36:52.985]        // -> [DP_SELECT <= 0x00000008]
[11:36:52.985]    </block>
[11:36:52.985]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:36:52.985]      // if-block "connectionFlash && DoOptionByteLoading"
[11:36:52.985]        // =>  FALSE
[11:36:52.986]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:36:52.986]    </control>
[11:36:52.986]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:36:52.986]      // if-block "DoDebugPortStop"
[11:36:52.986]        // =>  TRUE
[11:36:52.986]      <block atomic="false" info="">
[11:36:52.987]        WriteDP(DP_SELECT, 0x00000000);
[11:36:52.995]  
[11:36:52.995]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:36:52.995]  
[11:36:53.001]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:36:53.003]      </block>
[11:36:53.003]      // end if-block "DoDebugPortStop"
[11:36:53.004]    </control>
[11:36:53.004]  </sequence>
[11:36:53.005]  
[11:36:59.279]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:36:59.279]  
[11:36:59.280]  <debugvars>
[11:36:59.280]    // Pre-defined
[11:36:59.280]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:36:59.281]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:36:59.281]    __dp=0x00000000
[11:36:59.281]    __ap=0x00000000
[11:36:59.282]    __traceout=0x00000000      (Trace Disabled)
[11:36:59.282]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:36:59.282]    __FlashAddr=0x00000000
[11:36:59.282]    __FlashLen=0x00000000
[11:36:59.282]    __FlashArg=0x00000000
[11:36:59.283]    __FlashOp=0x00000000
[11:36:59.283]    __Result=0x00000000
[11:36:59.283]    
[11:36:59.283]    // User-defined
[11:36:59.283]    DbgMCU_CR=0x00000007
[11:36:59.283]    DbgMCU_APB1_Fz=0x00000000
[11:36:59.284]    DbgMCU_APB2_Fz=0x00000000
[11:36:59.284]    DoOptionByteLoading=0x00000000
[11:36:59.284]  </debugvars>
[11:36:59.284]  
[11:36:59.284]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:36:59.284]    <block atomic="false" info="">
[11:36:59.285]      Sequence("CheckID");
[11:36:59.285]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:36:59.285]          <block atomic="false" info="">
[11:36:59.285]            __var pidr1 = 0;
[11:36:59.285]              // -> [pidr1 <= 0x00000000]
[11:36:59.286]            __var pidr2 = 0;
[11:36:59.286]              // -> [pidr2 <= 0x00000000]
[11:36:59.286]            __var jep106id = 0;
[11:36:59.286]              // -> [jep106id <= 0x00000000]
[11:36:59.286]            __var ROMTableBase = 0;
[11:36:59.286]              // -> [ROMTableBase <= 0x00000000]
[11:36:59.287]            __ap = 0;      // AHB-AP
[11:36:59.287]              // -> [__ap <= 0x00000000]
[11:36:59.287]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:36:59.287]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:36:59.288]              // -> [ROMTableBase <= 0xF0000000]
[11:36:59.288]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:36:59.289]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:36:59.289]              // -> [pidr1 <= 0x00000004]
[11:36:59.289]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:36:59.290]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:36:59.290]              // -> [pidr2 <= 0x0000000A]
[11:36:59.291]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:36:59.292]              // -> [jep106id <= 0x00000020]
[11:36:59.292]          </block>
[11:36:59.292]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:36:59.292]            // if-block "jep106id != 0x20"
[11:36:59.293]              // =>  FALSE
[11:36:59.293]            // skip if-block "jep106id != 0x20"
[11:36:59.293]          </control>
[11:36:59.293]        </sequence>
[11:36:59.293]    </block>
[11:36:59.293]  </sequence>
[11:36:59.294]  
[11:36:59.304]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:36:59.304]  
[11:36:59.305]  <debugvars>
[11:36:59.305]    // Pre-defined
[11:36:59.305]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:36:59.306]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:36:59.306]    __dp=0x00000000
[11:36:59.306]    __ap=0x00000000
[11:36:59.306]    __traceout=0x00000000      (Trace Disabled)
[11:36:59.307]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:36:59.307]    __FlashAddr=0x00000000
[11:36:59.307]    __FlashLen=0x00000000
[11:36:59.308]    __FlashArg=0x00000000
[11:36:59.308]    __FlashOp=0x00000000
[11:36:59.308]    __Result=0x00000000
[11:36:59.309]    
[11:36:59.309]    // User-defined
[11:36:59.309]    DbgMCU_CR=0x00000007
[11:36:59.309]    DbgMCU_APB1_Fz=0x00000000
[11:36:59.309]    DbgMCU_APB2_Fz=0x00000000
[11:36:59.309]    DoOptionByteLoading=0x00000000
[11:36:59.309]  </debugvars>
[11:36:59.310]  
[11:36:59.310]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:36:59.310]    <block atomic="false" info="">
[11:36:59.310]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:36:59.311]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:36:59.311]    </block>
[11:36:59.311]    <block atomic="false" info="DbgMCU registers">
[11:36:59.312]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:36:59.312]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:36:59.313]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:36:59.314]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:36:59.314]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:36:59.315]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:36:59.315]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:36:59.316]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:36:59.317]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:36:59.318]    </block>
[11:36:59.318]  </sequence>
[11:36:59.318]  
[11:37:04.180]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:37:04.180]  
[11:37:04.182]  <debugvars>
[11:37:04.182]    // Pre-defined
[11:37:04.182]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:04.182]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:04.182]    __dp=0x00000000
[11:37:04.182]    __ap=0x00000000
[11:37:04.182]    __traceout=0x00000000      (Trace Disabled)
[11:37:04.182]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:04.182]    __FlashAddr=0x00000000
[11:37:04.184]    __FlashLen=0x00000000
[11:37:04.184]    __FlashArg=0x00000000
[11:37:04.184]    __FlashOp=0x00000000
[11:37:04.184]    __Result=0x00000000
[11:37:04.184]    
[11:37:04.184]    // User-defined
[11:37:04.184]    DbgMCU_CR=0x00000007
[11:37:04.184]    DbgMCU_APB1_Fz=0x00000000
[11:37:04.184]    DbgMCU_APB2_Fz=0x00000000
[11:37:04.184]    DoOptionByteLoading=0x00000000
[11:37:04.184]  </debugvars>
[11:37:04.184]  
[11:37:04.184]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:37:04.184]    <block atomic="false" info="">
[11:37:04.184]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:37:04.184]        // -> [connectionFlash <= 0x00000001]
[11:37:04.184]      __var FLASH_BASE = 0x40022000 ;
[11:37:04.184]        // -> [FLASH_BASE <= 0x40022000]
[11:37:04.184]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:37:04.184]        // -> [FLASH_CR <= 0x40022004]
[11:37:04.184]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:37:04.184]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:37:04.184]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:37:04.184]        // -> [LOCK_BIT <= 0x00000001]
[11:37:04.184]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:37:04.184]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:37:04.184]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:37:04.184]        // -> [FLASH_KEYR <= 0x4002200C]
[11:37:04.193]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:37:04.193]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:37:04.193]      __var FLASH_KEY2 = 0x02030405 ;
[11:37:04.193]        // -> [FLASH_KEY2 <= 0x02030405]
[11:37:04.193]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:37:04.193]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:37:04.195]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:37:04.195]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:37:04.195]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:37:04.195]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:37:04.195]      __var FLASH_CR_Value = 0 ;
[11:37:04.195]        // -> [FLASH_CR_Value <= 0x00000000]
[11:37:04.197]      __var DoDebugPortStop = 1 ;
[11:37:04.197]        // -> [DoDebugPortStop <= 0x00000001]
[11:37:04.197]      __var DP_CTRL_STAT = 0x4 ;
[11:37:04.197]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:37:04.197]      __var DP_SELECT = 0x8 ;
[11:37:04.197]        // -> [DP_SELECT <= 0x00000008]
[11:37:04.197]    </block>
[11:37:04.197]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:37:04.197]      // if-block "connectionFlash && DoOptionByteLoading"
[11:37:04.197]        // =>  FALSE
[11:37:04.200]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:37:04.200]    </control>
[11:37:04.200]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:37:04.200]      // if-block "DoDebugPortStop"
[11:37:04.200]        // =>  TRUE
[11:37:04.200]      <block atomic="false" info="">
[11:37:04.200]        WriteDP(DP_SELECT, 0x00000000);
[11:37:04.214]  
[11:37:04.214]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:37:04.214]  
[11:37:04.245]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:37:04.245]      </block>
[11:37:04.246]      // end if-block "DoDebugPortStop"
[11:37:04.247]    </control>
[11:37:04.247]  </sequence>
[11:37:04.247]  
[11:37:06.013]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:37:06.013]  
[11:37:06.013]  <debugvars>
[11:37:06.013]    // Pre-defined
[11:37:06.013]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:06.013]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:06.013]    __dp=0x00000000
[11:37:06.013]    __ap=0x00000000
[11:37:06.013]    __traceout=0x00000000      (Trace Disabled)
[11:37:06.013]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:06.013]    __FlashAddr=0x00000000
[11:37:06.013]    __FlashLen=0x00000000
[11:37:06.013]    __FlashArg=0x00000000
[11:37:06.013]    __FlashOp=0x00000000
[11:37:06.013]    __Result=0x00000000
[11:37:06.018]    
[11:37:06.018]    // User-defined
[11:37:06.018]    DbgMCU_CR=0x00000007
[11:37:06.018]    DbgMCU_APB1_Fz=0x00000000
[11:37:06.018]    DbgMCU_APB2_Fz=0x00000000
[11:37:06.019]    DoOptionByteLoading=0x00000000
[11:37:06.019]  </debugvars>
[11:37:06.019]  
[11:37:06.019]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:37:06.019]    <block atomic="false" info="">
[11:37:06.020]      Sequence("CheckID");
[11:37:06.020]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:37:06.020]          <block atomic="false" info="">
[11:37:06.020]            __var pidr1 = 0;
[11:37:06.020]              // -> [pidr1 <= 0x00000000]
[11:37:06.020]            __var pidr2 = 0;
[11:37:06.021]              // -> [pidr2 <= 0x00000000]
[11:37:06.021]            __var jep106id = 0;
[11:37:06.021]              // -> [jep106id <= 0x00000000]
[11:37:06.021]            __var ROMTableBase = 0;
[11:37:06.021]              // -> [ROMTableBase <= 0x00000000]
[11:37:06.022]            __ap = 0;      // AHB-AP
[11:37:06.022]              // -> [__ap <= 0x00000000]
[11:37:06.022]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:37:06.022]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:37:06.022]              // -> [ROMTableBase <= 0xF0000000]
[11:37:06.022]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:37:06.024]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:37:06.025]              // -> [pidr1 <= 0x00000004]
[11:37:06.025]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:37:06.026]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:37:06.026]              // -> [pidr2 <= 0x0000000A]
[11:37:06.026]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:37:06.027]              // -> [jep106id <= 0x00000020]
[11:37:06.027]          </block>
[11:37:06.027]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:37:06.028]            // if-block "jep106id != 0x20"
[11:37:06.028]              // =>  FALSE
[11:37:06.028]            // skip if-block "jep106id != 0x20"
[11:37:06.029]          </control>
[11:37:06.029]        </sequence>
[11:37:06.029]    </block>
[11:37:06.029]  </sequence>
[11:37:06.030]  
[11:37:06.042]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:37:06.042]  
[11:37:06.042]  <debugvars>
[11:37:06.042]    // Pre-defined
[11:37:06.042]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:06.043]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:06.043]    __dp=0x00000000
[11:37:06.043]    __ap=0x00000000
[11:37:06.044]    __traceout=0x00000000      (Trace Disabled)
[11:37:06.044]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:06.044]    __FlashAddr=0x00000000
[11:37:06.045]    __FlashLen=0x00000000
[11:37:06.045]    __FlashArg=0x00000000
[11:37:06.045]    __FlashOp=0x00000000
[11:37:06.045]    __Result=0x00000000
[11:37:06.045]    
[11:37:06.045]    // User-defined
[11:37:06.046]    DbgMCU_CR=0x00000007
[11:37:06.046]    DbgMCU_APB1_Fz=0x00000000
[11:37:06.046]    DbgMCU_APB2_Fz=0x00000000
[11:37:06.046]    DoOptionByteLoading=0x00000000
[11:37:06.046]  </debugvars>
[11:37:06.046]  
[11:37:06.047]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:37:06.047]    <block atomic="false" info="">
[11:37:06.047]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:37:06.048]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:06.048]    </block>
[11:37:06.048]    <block atomic="false" info="DbgMCU registers">
[11:37:06.049]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:37:06.050]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:37:06.050]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:06.051]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:37:06.051]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:06.052]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:37:06.052]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:06.053]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:37:06.054]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:06.054]    </block>
[11:37:06.054]  </sequence>
[11:37:06.054]  
[11:37:12.538]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:37:12.538]  
[11:37:12.538]  <debugvars>
[11:37:12.539]    // Pre-defined
[11:37:12.539]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:12.539]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:12.539]    __dp=0x00000000
[11:37:12.539]    __ap=0x00000000
[11:37:12.539]    __traceout=0x00000000      (Trace Disabled)
[11:37:12.539]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:12.542]    __FlashAddr=0x00000000
[11:37:12.542]    __FlashLen=0x00000000
[11:37:12.542]    __FlashArg=0x00000000
[11:37:12.542]    __FlashOp=0x00000000
[11:37:12.542]    __Result=0x00000000
[11:37:12.542]    
[11:37:12.542]    // User-defined
[11:37:12.542]    DbgMCU_CR=0x00000007
[11:37:12.542]    DbgMCU_APB1_Fz=0x00000000
[11:37:12.542]    DbgMCU_APB2_Fz=0x00000000
[11:37:12.544]    DoOptionByteLoading=0x00000000
[11:37:12.544]  </debugvars>
[11:37:12.544]  
[11:37:12.544]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:37:12.544]    <block atomic="false" info="">
[11:37:12.544]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:37:12.544]        // -> [connectionFlash <= 0x00000001]
[11:37:12.544]      __var FLASH_BASE = 0x40022000 ;
[11:37:12.544]        // -> [FLASH_BASE <= 0x40022000]
[11:37:12.544]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:37:12.544]        // -> [FLASH_CR <= 0x40022004]
[11:37:12.544]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:37:12.544]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:37:12.544]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:37:12.544]        // -> [LOCK_BIT <= 0x00000001]
[11:37:12.544]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:37:12.544]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:37:12.544]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:37:12.544]        // -> [FLASH_KEYR <= 0x4002200C]
[11:37:12.544]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:37:12.544]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:37:12.544]      __var FLASH_KEY2 = 0x02030405 ;
[11:37:12.544]        // -> [FLASH_KEY2 <= 0x02030405]
[11:37:12.544]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:37:12.544]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:37:12.544]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:37:12.544]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:37:12.544]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:37:12.544]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:37:12.544]      __var FLASH_CR_Value = 0 ;
[11:37:12.544]        // -> [FLASH_CR_Value <= 0x00000000]
[11:37:12.544]      __var DoDebugPortStop = 1 ;
[11:37:12.544]        // -> [DoDebugPortStop <= 0x00000001]
[11:37:12.544]      __var DP_CTRL_STAT = 0x4 ;
[11:37:12.544]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:37:12.544]      __var DP_SELECT = 0x8 ;
[11:37:12.544]        // -> [DP_SELECT <= 0x00000008]
[11:37:12.544]    </block>
[11:37:12.544]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:37:12.544]      // if-block "connectionFlash && DoOptionByteLoading"
[11:37:12.544]        // =>  FALSE
[11:37:12.544]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:37:12.544]    </control>
[11:37:12.544]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:37:12.544]      // if-block "DoDebugPortStop"
[11:37:12.544]        // =>  TRUE
[11:37:12.544]      <block atomic="false" info="">
[11:37:12.544]        WriteDP(DP_SELECT, 0x00000000);
[11:37:12.562]  
[11:37:12.562]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:37:12.562]  
[11:37:12.567]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:37:12.569]      </block>
[11:37:12.569]      // end if-block "DoDebugPortStop"
[11:37:12.570]    </control>
[11:37:12.570]  </sequence>
[11:37:12.571]  
[11:37:14.372]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:37:14.372]  
[11:37:14.372]  <debugvars>
[11:37:14.372]    // Pre-defined
[11:37:14.372]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:14.373]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:14.373]    __dp=0x00000000
[11:37:14.373]    __ap=0x00000000
[11:37:14.373]    __traceout=0x00000000      (Trace Disabled)
[11:37:14.374]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:14.374]    __FlashAddr=0x00000000
[11:37:14.375]    __FlashLen=0x00000000
[11:37:14.375]    __FlashArg=0x00000000
[11:37:14.375]    __FlashOp=0x00000000
[11:37:14.375]    __Result=0x00000000
[11:37:14.376]    
[11:37:14.376]    // User-defined
[11:37:14.376]    DbgMCU_CR=0x00000007
[11:37:14.376]    DbgMCU_APB1_Fz=0x00000000
[11:37:14.376]    DbgMCU_APB2_Fz=0x00000000
[11:37:14.376]    DoOptionByteLoading=0x00000000
[11:37:14.376]  </debugvars>
[11:37:14.377]  
[11:37:14.377]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:37:14.377]    <block atomic="false" info="">
[11:37:14.377]      Sequence("CheckID");
[11:37:14.377]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:37:14.378]          <block atomic="false" info="">
[11:37:14.378]            __var pidr1 = 0;
[11:37:14.378]              // -> [pidr1 <= 0x00000000]
[11:37:14.378]            __var pidr2 = 0;
[11:37:14.378]              // -> [pidr2 <= 0x00000000]
[11:37:14.378]            __var jep106id = 0;
[11:37:14.379]              // -> [jep106id <= 0x00000000]
[11:37:14.379]            __var ROMTableBase = 0;
[11:37:14.379]              // -> [ROMTableBase <= 0x00000000]
[11:37:14.379]            __ap = 0;      // AHB-AP
[11:37:14.379]              // -> [__ap <= 0x00000000]
[11:37:14.380]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:37:14.380]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:37:14.380]              // -> [ROMTableBase <= 0xF0000000]
[11:37:14.381]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:37:14.382]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:37:14.382]              // -> [pidr1 <= 0x00000004]
[11:37:14.383]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:37:14.384]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:37:14.384]              // -> [pidr2 <= 0x0000000A]
[11:37:14.384]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:37:14.385]              // -> [jep106id <= 0x00000020]
[11:37:14.385]          </block>
[11:37:14.385]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:37:14.385]            // if-block "jep106id != 0x20"
[11:37:14.385]              // =>  FALSE
[11:37:14.386]            // skip if-block "jep106id != 0x20"
[11:37:14.386]          </control>
[11:37:14.387]        </sequence>
[11:37:14.387]    </block>
[11:37:14.387]  </sequence>
[11:37:14.387]  
[11:37:14.399]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:37:14.399]  
[11:37:14.400]  <debugvars>
[11:37:14.400]    // Pre-defined
[11:37:14.401]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:14.401]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:14.401]    __dp=0x00000000
[11:37:14.401]    __ap=0x00000000
[11:37:14.403]    __traceout=0x00000000      (Trace Disabled)
[11:37:14.403]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:14.403]    __FlashAddr=0x00000000
[11:37:14.403]    __FlashLen=0x00000000
[11:37:14.404]    __FlashArg=0x00000000
[11:37:14.404]    __FlashOp=0x00000000
[11:37:14.404]    __Result=0x00000000
[11:37:14.404]    
[11:37:14.404]    // User-defined
[11:37:14.404]    DbgMCU_CR=0x00000007
[11:37:14.405]    DbgMCU_APB1_Fz=0x00000000
[11:37:14.405]    DbgMCU_APB2_Fz=0x00000000
[11:37:14.405]    DoOptionByteLoading=0x00000000
[11:37:14.405]  </debugvars>
[11:37:14.405]  
[11:37:14.406]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:37:14.406]    <block atomic="false" info="">
[11:37:14.406]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:37:14.407]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:14.407]    </block>
[11:37:14.408]    <block atomic="false" info="DbgMCU registers">
[11:37:14.408]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:37:14.409]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:37:14.410]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:14.410]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:37:14.411]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:14.411]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:37:14.412]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:14.412]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:37:14.413]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:14.413]    </block>
[11:37:14.414]  </sequence>
[11:37:14.414]  
[11:37:24.171]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:37:24.171]  
[11:37:24.171]  <debugvars>
[11:37:24.171]    // Pre-defined
[11:37:24.171]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:24.171]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:24.171]    __dp=0x00000000
[11:37:24.171]    __ap=0x00000000
[11:37:24.171]    __traceout=0x00000000      (Trace Disabled)
[11:37:24.171]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:24.171]    __FlashAddr=0x00000000
[11:37:24.171]    __FlashLen=0x00000000
[11:37:24.171]    __FlashArg=0x00000000
[11:37:24.171]    __FlashOp=0x00000000
[11:37:24.171]    __Result=0x00000000
[11:37:24.171]    
[11:37:24.171]    // User-defined
[11:37:24.185]    DbgMCU_CR=0x00000007
[11:37:24.185]    DbgMCU_APB1_Fz=0x00000000
[11:37:24.185]    DbgMCU_APB2_Fz=0x00000000
[11:37:24.185]    DoOptionByteLoading=0x00000000
[11:37:24.185]  </debugvars>
[11:37:24.185]  
[11:37:24.187]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:37:24.187]    <block atomic="false" info="">
[11:37:24.187]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:37:24.187]        // -> [connectionFlash <= 0x00000001]
[11:37:24.188]      __var FLASH_BASE = 0x40022000 ;
[11:37:24.189]        // -> [FLASH_BASE <= 0x40022000]
[11:37:24.189]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:37:24.189]        // -> [FLASH_CR <= 0x40022004]
[11:37:24.189]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:37:24.189]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:37:24.189]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:37:24.189]        // -> [LOCK_BIT <= 0x00000001]
[11:37:24.189]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:37:24.189]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:37:24.189]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:37:24.189]        // -> [FLASH_KEYR <= 0x4002200C]
[11:37:24.189]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:37:24.189]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:37:24.192]      __var FLASH_KEY2 = 0x02030405 ;
[11:37:24.192]        // -> [FLASH_KEY2 <= 0x02030405]
[11:37:24.192]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:37:24.192]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:37:24.193]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:37:24.193]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:37:24.193]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:37:24.193]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:37:24.193]      __var FLASH_CR_Value = 0 ;
[11:37:24.193]        // -> [FLASH_CR_Value <= 0x00000000]
[11:37:24.194]      __var DoDebugPortStop = 1 ;
[11:37:24.194]        // -> [DoDebugPortStop <= 0x00000001]
[11:37:24.194]      __var DP_CTRL_STAT = 0x4 ;
[11:37:24.194]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:37:24.194]      __var DP_SELECT = 0x8 ;
[11:37:24.195]        // -> [DP_SELECT <= 0x00000008]
[11:37:24.195]    </block>
[11:37:24.195]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:37:24.195]      // if-block "connectionFlash && DoOptionByteLoading"
[11:37:24.195]        // =>  FALSE
[11:37:24.195]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:37:24.196]    </control>
[11:37:24.196]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:37:24.196]      // if-block "DoDebugPortStop"
[11:37:24.196]        // =>  TRUE
[11:37:24.196]      <block atomic="false" info="">
[11:37:24.197]        WriteDP(DP_SELECT, 0x00000000);
[11:37:24.214]  
[11:37:24.214]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:37:24.214]  
[11:37:24.225]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:37:24.226]      </block>
[11:37:24.226]      // end if-block "DoDebugPortStop"
[11:37:24.227]    </control>
[11:37:24.227]  </sequence>
[11:37:24.227]  
[11:37:32.585]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:37:32.585]  
[11:37:32.585]  <debugvars>
[11:37:32.586]    // Pre-defined
[11:37:32.586]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:32.586]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:32.587]    __dp=0x00000000
[11:37:32.587]    __ap=0x00000000
[11:37:32.587]    __traceout=0x00000000      (Trace Disabled)
[11:37:32.588]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:32.588]    __FlashAddr=0x00000000
[11:37:32.588]    __FlashLen=0x00000000
[11:37:32.588]    __FlashArg=0x00000000
[11:37:32.589]    __FlashOp=0x00000000
[11:37:32.589]    __Result=0x00000000
[11:37:32.589]    
[11:37:32.589]    // User-defined
[11:37:32.590]    DbgMCU_CR=0x00000007
[11:37:32.590]    DbgMCU_APB1_Fz=0x00000000
[11:37:32.590]    DbgMCU_APB2_Fz=0x00000000
[11:37:32.590]    DoOptionByteLoading=0x00000000
[11:37:32.591]  </debugvars>
[11:37:32.591]  
[11:37:32.591]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:37:32.592]    <block atomic="false" info="">
[11:37:32.592]      Sequence("CheckID");
[11:37:32.592]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:37:32.592]          <block atomic="false" info="">
[11:37:32.593]            __var pidr1 = 0;
[11:37:32.593]              // -> [pidr1 <= 0x00000000]
[11:37:32.593]            __var pidr2 = 0;
[11:37:32.593]              // -> [pidr2 <= 0x00000000]
[11:37:32.593]            __var jep106id = 0;
[11:37:32.594]              // -> [jep106id <= 0x00000000]
[11:37:32.594]            __var ROMTableBase = 0;
[11:37:32.594]              // -> [ROMTableBase <= 0x00000000]
[11:37:32.594]            __ap = 0;      // AHB-AP
[11:37:32.594]              // -> [__ap <= 0x00000000]
[11:37:32.594]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:37:32.595]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:37:32.595]              // -> [ROMTableBase <= 0xF0000000]
[11:37:32.596]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:37:32.597]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:37:32.597]              // -> [pidr1 <= 0x00000004]
[11:37:32.598]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:37:32.599]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:37:32.599]              // -> [pidr2 <= 0x0000000A]
[11:37:32.599]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:37:32.599]              // -> [jep106id <= 0x00000020]
[11:37:32.601]          </block>
[11:37:32.601]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:37:32.601]            // if-block "jep106id != 0x20"
[11:37:32.602]              // =>  FALSE
[11:37:32.602]            // skip if-block "jep106id != 0x20"
[11:37:32.602]          </control>
[11:37:32.603]        </sequence>
[11:37:32.603]    </block>
[11:37:32.603]  </sequence>
[11:37:32.604]  
[11:37:32.616]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:37:32.616]  
[11:37:32.644]  <debugvars>
[11:37:32.645]    // Pre-defined
[11:37:32.645]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:32.646]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:32.647]    __dp=0x00000000
[11:37:32.647]    __ap=0x00000000
[11:37:32.648]    __traceout=0x00000000      (Trace Disabled)
[11:37:32.648]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:32.649]    __FlashAddr=0x00000000
[11:37:32.650]    __FlashLen=0x00000000
[11:37:32.650]    __FlashArg=0x00000000
[11:37:32.650]    __FlashOp=0x00000000
[11:37:32.651]    __Result=0x00000000
[11:37:32.651]    
[11:37:32.651]    // User-defined
[11:37:32.652]    DbgMCU_CR=0x00000007
[11:37:32.652]    DbgMCU_APB1_Fz=0x00000000
[11:37:32.653]    DbgMCU_APB2_Fz=0x00000000
[11:37:32.653]    DoOptionByteLoading=0x00000000
[11:37:32.654]  </debugvars>
[11:37:32.654]  
[11:37:32.655]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:37:32.655]    <block atomic="false" info="">
[11:37:32.656]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:37:32.657]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:32.657]    </block>
[11:37:32.657]    <block atomic="false" info="DbgMCU registers">
[11:37:32.658]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:37:32.659]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:37:32.659]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:32.660]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:37:32.660]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:32.661]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:37:32.661]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:32.662]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:37:32.662]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:32.663]    </block>
[11:37:32.663]  </sequence>
[11:37:32.663]  
[11:37:37.874]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:37:37.874]  
[11:37:37.874]  <debugvars>
[11:37:37.874]    // Pre-defined
[11:37:37.874]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:37.874]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:37.874]    __dp=0x00000000
[11:37:37.874]    __ap=0x00000000
[11:37:37.874]    __traceout=0x00000000      (Trace Disabled)
[11:37:37.874]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:37.874]    __FlashAddr=0x00000000
[11:37:37.874]    __FlashLen=0x00000000
[11:37:37.874]    __FlashArg=0x00000000
[11:37:37.874]    __FlashOp=0x00000000
[11:37:37.883]    __Result=0x00000000
[11:37:37.883]    
[11:37:37.883]    // User-defined
[11:37:37.883]    DbgMCU_CR=0x00000007
[11:37:37.883]    DbgMCU_APB1_Fz=0x00000000
[11:37:37.885]    DbgMCU_APB2_Fz=0x00000000
[11:37:37.885]    DoOptionByteLoading=0x00000000
[11:37:37.885]  </debugvars>
[11:37:37.885]  
[11:37:37.885]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:37:37.885]    <block atomic="false" info="">
[11:37:37.885]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:37:37.887]        // -> [connectionFlash <= 0x00000001]
[11:37:37.887]      __var FLASH_BASE = 0x40022000 ;
[11:37:37.887]        // -> [FLASH_BASE <= 0x40022000]
[11:37:37.887]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:37:37.887]        // -> [FLASH_CR <= 0x40022004]
[11:37:37.887]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:37:37.888]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:37:37.888]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:37:37.888]        // -> [LOCK_BIT <= 0x00000001]
[11:37:37.888]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:37:37.889]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:37:37.889]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:37:37.889]        // -> [FLASH_KEYR <= 0x4002200C]
[11:37:37.889]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:37:37.889]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:37:37.889]      __var FLASH_KEY2 = 0x02030405 ;
[11:37:37.889]        // -> [FLASH_KEY2 <= 0x02030405]
[11:37:37.889]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:37:37.889]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:37:37.889]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:37:37.889]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:37:37.889]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:37:37.889]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:37:37.889]      __var FLASH_CR_Value = 0 ;
[11:37:37.891]        // -> [FLASH_CR_Value <= 0x00000000]
[11:37:37.892]      __var DoDebugPortStop = 1 ;
[11:37:37.892]        // -> [DoDebugPortStop <= 0x00000001]
[11:37:37.892]      __var DP_CTRL_STAT = 0x4 ;
[11:37:37.893]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:37:37.893]      __var DP_SELECT = 0x8 ;
[11:37:37.893]        // -> [DP_SELECT <= 0x00000008]
[11:37:37.893]    </block>
[11:37:37.894]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:37:37.894]      // if-block "connectionFlash && DoOptionByteLoading"
[11:37:37.894]        // =>  FALSE
[11:37:37.895]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:37:37.895]    </control>
[11:37:37.895]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:37:37.895]      // if-block "DoDebugPortStop"
[11:37:37.896]        // =>  TRUE
[11:37:37.896]      <block atomic="false" info="">
[11:37:37.896]        WriteDP(DP_SELECT, 0x00000000);
[11:37:37.907]  
[11:37:37.907]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:37:37.907]  
[11:37:37.921]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:37:37.921]      </block>
[11:37:37.921]      // end if-block "DoDebugPortStop"
[11:37:37.921]    </control>
[11:37:37.921]  </sequence>
[11:37:37.925]  
[11:37:39.416]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:37:39.416]  
[11:37:39.417]  <debugvars>
[11:37:39.417]    // Pre-defined
[11:37:39.417]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:39.418]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:39.418]    __dp=0x00000000
[11:37:39.418]    __ap=0x00000000
[11:37:39.418]    __traceout=0x00000000      (Trace Disabled)
[11:37:39.419]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:39.419]    __FlashAddr=0x00000000
[11:37:39.419]    __FlashLen=0x00000000
[11:37:39.420]    __FlashArg=0x00000000
[11:37:39.420]    __FlashOp=0x00000000
[11:37:39.420]    __Result=0x00000000
[11:37:39.421]    
[11:37:39.421]    // User-defined
[11:37:39.421]    DbgMCU_CR=0x00000007
[11:37:39.421]    DbgMCU_APB1_Fz=0x00000000
[11:37:39.422]    DbgMCU_APB2_Fz=0x00000000
[11:37:39.422]    DoOptionByteLoading=0x00000000
[11:37:39.422]  </debugvars>
[11:37:39.422]  
[11:37:39.422]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:37:39.423]    <block atomic="false" info="">
[11:37:39.423]      Sequence("CheckID");
[11:37:39.423]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:37:39.424]          <block atomic="false" info="">
[11:37:39.424]            __var pidr1 = 0;
[11:37:39.424]              // -> [pidr1 <= 0x00000000]
[11:37:39.424]            __var pidr2 = 0;
[11:37:39.425]              // -> [pidr2 <= 0x00000000]
[11:37:39.425]            __var jep106id = 0;
[11:37:39.425]              // -> [jep106id <= 0x00000000]
[11:37:39.425]            __var ROMTableBase = 0;
[11:37:39.425]              // -> [ROMTableBase <= 0x00000000]
[11:37:39.426]            __ap = 0;      // AHB-AP
[11:37:39.426]              // -> [__ap <= 0x00000000]
[11:37:39.426]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:37:39.427]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:37:39.427]              // -> [ROMTableBase <= 0xF0000000]
[11:37:39.427]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:37:39.429]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:37:39.429]              // -> [pidr1 <= 0x00000004]
[11:37:39.429]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:37:39.430]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:37:39.430]              // -> [pidr2 <= 0x0000000A]
[11:37:39.430]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:37:39.431]              // -> [jep106id <= 0x00000020]
[11:37:39.431]          </block>
[11:37:39.431]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:37:39.431]            // if-block "jep106id != 0x20"
[11:37:39.431]              // =>  FALSE
[11:37:39.432]            // skip if-block "jep106id != 0x20"
[11:37:39.432]          </control>
[11:37:39.432]        </sequence>
[11:37:39.432]    </block>
[11:37:39.432]  </sequence>
[11:37:39.433]  
[11:37:39.444]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:37:39.444]  
[11:37:39.445]  <debugvars>
[11:37:39.445]    // Pre-defined
[11:37:39.445]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:39.445]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:39.446]    __dp=0x00000000
[11:37:39.446]    __ap=0x00000000
[11:37:39.446]    __traceout=0x00000000      (Trace Disabled)
[11:37:39.447]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:39.447]    __FlashAddr=0x00000000
[11:37:39.447]    __FlashLen=0x00000000
[11:37:39.447]    __FlashArg=0x00000000
[11:37:39.448]    __FlashOp=0x00000000
[11:37:39.448]    __Result=0x00000000
[11:37:39.448]    
[11:37:39.448]    // User-defined
[11:37:39.448]    DbgMCU_CR=0x00000007
[11:37:39.448]    DbgMCU_APB1_Fz=0x00000000
[11:37:39.449]    DbgMCU_APB2_Fz=0x00000000
[11:37:39.449]    DoOptionByteLoading=0x00000000
[11:37:39.449]  </debugvars>
[11:37:39.449]  
[11:37:39.449]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:37:39.450]    <block atomic="false" info="">
[11:37:39.450]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:37:39.451]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:39.451]    </block>
[11:37:39.451]    <block atomic="false" info="DbgMCU registers">
[11:37:39.451]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:37:39.452]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:37:39.453]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:39.453]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:37:39.454]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:39.455]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:37:39.456]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:39.456]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:37:39.457]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:39.457]    </block>
[11:37:39.457]  </sequence>
[11:37:39.457]  
[11:37:42.938]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:37:42.938]  
[11:37:42.938]  <debugvars>
[11:37:42.938]    // Pre-defined
[11:37:42.938]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:42.938]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:42.938]    __dp=0x00000000
[11:37:42.938]    __ap=0x00000000
[11:37:42.938]    __traceout=0x00000000      (Trace Disabled)
[11:37:42.938]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:42.938]    __FlashAddr=0x00000000
[11:37:42.938]    __FlashLen=0x00000000
[11:37:42.938]    __FlashArg=0x00000000
[11:37:42.938]    __FlashOp=0x00000000
[11:37:42.938]    __Result=0x00000000
[11:37:42.938]    
[11:37:42.938]    // User-defined
[11:37:42.938]    DbgMCU_CR=0x00000007
[11:37:42.938]    DbgMCU_APB1_Fz=0x00000000
[11:37:42.938]    DbgMCU_APB2_Fz=0x00000000
[11:37:42.938]    DoOptionByteLoading=0x00000000
[11:37:42.938]  </debugvars>
[11:37:42.938]  
[11:37:42.938]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:37:42.938]    <block atomic="false" info="">
[11:37:42.938]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:37:42.938]        // -> [connectionFlash <= 0x00000001]
[11:37:42.938]      __var FLASH_BASE = 0x40022000 ;
[11:37:42.950]        // -> [FLASH_BASE <= 0x40022000]
[11:37:42.950]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:37:42.950]        // -> [FLASH_CR <= 0x40022004]
[11:37:42.950]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:37:42.950]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:37:42.951]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:37:42.951]        // -> [LOCK_BIT <= 0x00000001]
[11:37:42.951]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:37:42.951]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:37:42.951]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:37:42.951]        // -> [FLASH_KEYR <= 0x4002200C]
[11:37:42.953]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:37:42.953]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:37:42.953]      __var FLASH_KEY2 = 0x02030405 ;
[11:37:42.953]        // -> [FLASH_KEY2 <= 0x02030405]
[11:37:42.953]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:37:42.954]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:37:42.955]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:37:42.955]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:37:42.955]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:37:42.955]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:37:42.955]      __var FLASH_CR_Value = 0 ;
[11:37:42.956]        // -> [FLASH_CR_Value <= 0x00000000]
[11:37:42.956]      __var DoDebugPortStop = 1 ;
[11:37:42.956]        // -> [DoDebugPortStop <= 0x00000001]
[11:37:42.956]      __var DP_CTRL_STAT = 0x4 ;
[11:37:42.956]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:37:42.956]      __var DP_SELECT = 0x8 ;
[11:37:42.956]        // -> [DP_SELECT <= 0x00000008]
[11:37:42.956]    </block>
[11:37:42.956]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:37:42.956]      // if-block "connectionFlash && DoOptionByteLoading"
[11:37:42.956]        // =>  FALSE
[11:37:42.956]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:37:42.958]    </control>
[11:37:42.958]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:37:42.958]      // if-block "DoDebugPortStop"
[11:37:42.959]        // =>  TRUE
[11:37:42.959]      <block atomic="false" info="">
[11:37:42.959]        WriteDP(DP_SELECT, 0x00000000);
[11:37:42.968]  
[11:37:42.968]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:37:42.968]  
[11:37:42.970]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:37:42.983]      </block>
[11:37:42.983]      // end if-block "DoDebugPortStop"
[11:37:42.983]    </control>
[11:37:42.984]  </sequence>
[11:37:42.985]  
[11:37:45.025]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:37:45.025]  
[11:37:45.025]  <debugvars>
[11:37:45.026]    // Pre-defined
[11:37:45.026]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:45.027]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:45.027]    __dp=0x00000000
[11:37:45.028]    __ap=0x00000000
[11:37:45.028]    __traceout=0x00000000      (Trace Disabled)
[11:37:45.028]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:45.029]    __FlashAddr=0x00000000
[11:37:45.029]    __FlashLen=0x00000000
[11:37:45.029]    __FlashArg=0x00000000
[11:37:45.029]    __FlashOp=0x00000000
[11:37:45.030]    __Result=0x00000000
[11:37:45.030]    
[11:37:45.030]    // User-defined
[11:37:45.030]    DbgMCU_CR=0x00000007
[11:37:45.031]    DbgMCU_APB1_Fz=0x00000000
[11:37:45.031]    DbgMCU_APB2_Fz=0x00000000
[11:37:45.031]    DoOptionByteLoading=0x00000000
[11:37:45.032]  </debugvars>
[11:37:45.032]  
[11:37:45.032]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:37:45.033]    <block atomic="false" info="">
[11:37:45.033]      Sequence("CheckID");
[11:37:45.033]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:37:45.033]          <block atomic="false" info="">
[11:37:45.033]            __var pidr1 = 0;
[11:37:45.033]              // -> [pidr1 <= 0x00000000]
[11:37:45.034]            __var pidr2 = 0;
[11:37:45.034]              // -> [pidr2 <= 0x00000000]
[11:37:45.034]            __var jep106id = 0;
[11:37:45.034]              // -> [jep106id <= 0x00000000]
[11:37:45.035]            __var ROMTableBase = 0;
[11:37:45.035]              // -> [ROMTableBase <= 0x00000000]
[11:37:45.035]            __ap = 0;      // AHB-AP
[11:37:45.036]              // -> [__ap <= 0x00000000]
[11:37:45.036]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:37:45.037]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:37:45.037]              // -> [ROMTableBase <= 0xF0000000]
[11:37:45.037]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:37:45.038]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:37:45.039]              // -> [pidr1 <= 0x00000004]
[11:37:45.039]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:37:45.040]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:37:45.040]              // -> [pidr2 <= 0x0000000A]
[11:37:45.041]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:37:45.041]              // -> [jep106id <= 0x00000020]
[11:37:45.041]          </block>
[11:37:45.041]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:37:45.042]            // if-block "jep106id != 0x20"
[11:37:45.042]              // =>  FALSE
[11:37:45.042]            // skip if-block "jep106id != 0x20"
[11:37:45.042]          </control>
[11:37:45.043]        </sequence>
[11:37:45.043]    </block>
[11:37:45.043]  </sequence>
[11:37:45.043]  
[11:37:45.055]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:37:45.055]  
[11:37:45.062]  <debugvars>
[11:37:45.062]    // Pre-defined
[11:37:45.063]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:45.063]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:45.063]    __dp=0x00000000
[11:37:45.063]    __ap=0x00000000
[11:37:45.063]    __traceout=0x00000000      (Trace Disabled)
[11:37:45.065]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:45.066]    __FlashAddr=0x00000000
[11:37:45.066]    __FlashLen=0x00000000
[11:37:45.066]    __FlashArg=0x00000000
[11:37:45.067]    __FlashOp=0x00000000
[11:37:45.067]    __Result=0x00000000
[11:37:45.068]    
[11:37:45.068]    // User-defined
[11:37:45.068]    DbgMCU_CR=0x00000007
[11:37:45.069]    DbgMCU_APB1_Fz=0x00000000
[11:37:45.069]    DbgMCU_APB2_Fz=0x00000000
[11:37:45.069]    DoOptionByteLoading=0x00000000
[11:37:45.069]  </debugvars>
[11:37:45.070]  
[11:37:45.070]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:37:45.070]    <block atomic="false" info="">
[11:37:45.071]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:37:45.072]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:45.072]    </block>
[11:37:45.072]    <block atomic="false" info="DbgMCU registers">
[11:37:45.072]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:37:45.073]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:37:45.074]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:45.075]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:37:45.076]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:45.076]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:37:45.077]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:45.077]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:37:45.078]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:45.079]    </block>
[11:37:45.079]  </sequence>
[11:37:45.079]  
[11:37:49.239]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:37:49.239]  
[11:37:49.239]  <debugvars>
[11:37:49.239]    // Pre-defined
[11:37:49.239]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:49.239]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:49.239]    __dp=0x00000000
[11:37:49.239]    __ap=0x00000000
[11:37:49.239]    __traceout=0x00000000      (Trace Disabled)
[11:37:49.239]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:49.239]    __FlashAddr=0x00000000
[11:37:49.239]    __FlashLen=0x00000000
[11:37:49.239]    __FlashArg=0x00000000
[11:37:49.239]    __FlashOp=0x00000000
[11:37:49.239]    __Result=0x00000000
[11:37:49.239]    
[11:37:49.239]    // User-defined
[11:37:49.239]    DbgMCU_CR=0x00000007
[11:37:49.239]    DbgMCU_APB1_Fz=0x00000000
[11:37:49.239]    DbgMCU_APB2_Fz=0x00000000
[11:37:49.239]    DoOptionByteLoading=0x00000000
[11:37:49.239]  </debugvars>
[11:37:49.239]  
[11:37:49.239]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:37:49.239]    <block atomic="false" info="">
[11:37:49.239]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:37:49.239]        // -> [connectionFlash <= 0x00000001]
[11:37:49.239]      __var FLASH_BASE = 0x40022000 ;
[11:37:49.239]        // -> [FLASH_BASE <= 0x40022000]
[11:37:49.239]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:37:49.239]        // -> [FLASH_CR <= 0x40022004]
[11:37:49.239]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:37:49.239]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:37:49.248]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:37:49.248]        // -> [LOCK_BIT <= 0x00000001]
[11:37:49.248]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:37:49.248]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:37:49.248]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:37:49.248]        // -> [FLASH_KEYR <= 0x4002200C]
[11:37:49.248]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:37:49.248]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:37:49.248]      __var FLASH_KEY2 = 0x02030405 ;
[11:37:49.251]        // -> [FLASH_KEY2 <= 0x02030405]
[11:37:49.251]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:37:49.252]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:37:49.252]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:37:49.252]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:37:49.252]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:37:49.253]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:37:49.253]      __var FLASH_CR_Value = 0 ;
[11:37:49.253]        // -> [FLASH_CR_Value <= 0x00000000]
[11:37:49.253]      __var DoDebugPortStop = 1 ;
[11:37:49.254]        // -> [DoDebugPortStop <= 0x00000001]
[11:37:49.254]      __var DP_CTRL_STAT = 0x4 ;
[11:37:49.254]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:37:49.254]      __var DP_SELECT = 0x8 ;
[11:37:49.254]        // -> [DP_SELECT <= 0x00000008]
[11:37:49.255]    </block>
[11:37:49.255]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:37:49.255]      // if-block "connectionFlash && DoOptionByteLoading"
[11:37:49.255]        // =>  FALSE
[11:37:49.255]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:37:49.255]    </control>
[11:37:49.256]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:37:49.256]      // if-block "DoDebugPortStop"
[11:37:49.256]        // =>  TRUE
[11:37:49.256]      <block atomic="false" info="">
[11:37:49.256]        WriteDP(DP_SELECT, 0x00000000);
[11:37:49.264]  
[11:37:49.264]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:37:49.264]  
[11:37:49.280]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:37:49.289]      </block>
[11:37:49.289]      // end if-block "DoDebugPortStop"
[11:37:49.289]    </control>
[11:37:49.289]  </sequence>
[11:37:49.292]  
[11:37:50.922]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:37:50.922]  
[11:37:50.922]  <debugvars>
[11:37:50.922]    // Pre-defined
[11:37:50.922]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:50.922]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:50.922]    __dp=0x00000000
[11:37:50.922]    __ap=0x00000000
[11:37:50.922]    __traceout=0x00000000      (Trace Disabled)
[11:37:50.922]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:50.922]    __FlashAddr=0x00000000
[11:37:50.922]    __FlashLen=0x00000000
[11:37:50.922]    __FlashArg=0x00000000
[11:37:50.922]    __FlashOp=0x00000000
[11:37:50.922]    __Result=0x00000000
[11:37:50.936]    
[11:37:50.936]    // User-defined
[11:37:50.936]    DbgMCU_CR=0x00000007
[11:37:50.936]    DbgMCU_APB1_Fz=0x00000000
[11:37:50.936]    DbgMCU_APB2_Fz=0x00000000
[11:37:50.936]    DoOptionByteLoading=0x00000000
[11:37:50.936]  </debugvars>
[11:37:50.937]  
[11:37:50.937]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:37:50.937]    <block atomic="false" info="">
[11:37:50.937]      Sequence("CheckID");
[11:37:50.937]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:37:50.937]          <block atomic="false" info="">
[11:37:50.937]            __var pidr1 = 0;
[11:37:50.937]              // -> [pidr1 <= 0x00000000]
[11:37:50.937]            __var pidr2 = 0;
[11:37:50.937]              // -> [pidr2 <= 0x00000000]
[11:37:50.937]            __var jep106id = 0;
[11:37:50.937]              // -> [jep106id <= 0x00000000]
[11:37:50.937]            __var ROMTableBase = 0;
[11:37:50.937]              // -> [ROMTableBase <= 0x00000000]
[11:37:50.937]            __ap = 0;      // AHB-AP
[11:37:50.937]              // -> [__ap <= 0x00000000]
[11:37:50.937]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:37:50.937]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:37:50.937]              // -> [ROMTableBase <= 0xF0000000]
[11:37:50.937]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:37:50.937]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:37:50.937]              // -> [pidr1 <= 0x00000004]
[11:37:50.937]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:37:50.937]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:37:50.937]              // -> [pidr2 <= 0x0000000A]
[11:37:50.937]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:37:50.937]              // -> [jep106id <= 0x00000020]
[11:37:50.937]          </block>
[11:37:50.937]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:37:50.937]            // if-block "jep106id != 0x20"
[11:37:50.937]              // =>  FALSE
[11:37:50.937]            // skip if-block "jep106id != 0x20"
[11:37:50.937]          </control>
[11:37:50.937]        </sequence>
[11:37:50.937]    </block>
[11:37:50.937]  </sequence>
[11:37:50.937]  
[11:37:50.953]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:37:50.953]  
[11:37:50.960]  <debugvars>
[11:37:50.960]    // Pre-defined
[11:37:50.960]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:50.962]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:50.962]    __dp=0x00000000
[11:37:50.962]    __ap=0x00000000
[11:37:50.962]    __traceout=0x00000000      (Trace Disabled)
[11:37:50.962]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:50.962]    __FlashAddr=0x00000000
[11:37:50.962]    __FlashLen=0x00000000
[11:37:50.962]    __FlashArg=0x00000000
[11:37:50.962]    __FlashOp=0x00000000
[11:37:50.962]    __Result=0x00000000
[11:37:50.964]    
[11:37:50.964]    // User-defined
[11:37:50.964]    DbgMCU_CR=0x00000007
[11:37:50.964]    DbgMCU_APB1_Fz=0x00000000
[11:37:50.964]    DbgMCU_APB2_Fz=0x00000000
[11:37:50.964]    DoOptionByteLoading=0x00000000
[11:37:50.964]  </debugvars>
[11:37:50.964]  
[11:37:50.964]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:37:50.964]    <block atomic="false" info="">
[11:37:50.964]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:37:50.966]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:50.966]    </block>
[11:37:50.966]    <block atomic="false" info="DbgMCU registers">
[11:37:50.966]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:37:50.968]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:37:50.968]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:50.968]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:37:50.968]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:50.968]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:37:50.968]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:50.968]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:37:50.968]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:50.968]    </block>
[11:37:50.968]  </sequence>
[11:37:50.968]  
[11:37:58.439]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:37:58.439]  
[11:37:58.439]  <debugvars>
[11:37:58.439]    // Pre-defined
[11:37:58.439]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:58.439]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:58.439]    __dp=0x00000000
[11:37:58.439]    __ap=0x00000000
[11:37:58.439]    __traceout=0x00000000      (Trace Disabled)
[11:37:58.439]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:58.439]    __FlashAddr=0x00000000
[11:37:58.439]    __FlashLen=0x00000000
[11:37:58.439]    __FlashArg=0x00000000
[11:37:58.439]    __FlashOp=0x00000000
[11:37:58.439]    __Result=0x00000000
[11:37:58.439]    
[11:37:58.439]    // User-defined
[11:37:58.439]    DbgMCU_CR=0x00000007
[11:37:58.439]    DbgMCU_APB1_Fz=0x00000000
[11:37:58.439]    DbgMCU_APB2_Fz=0x00000000
[11:37:58.439]    DoOptionByteLoading=0x00000000
[11:37:58.439]  </debugvars>
[11:37:58.439]  
[11:37:58.439]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:37:58.439]    <block atomic="false" info="">
[11:37:58.439]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:37:58.439]        // -> [connectionFlash <= 0x00000001]
[11:37:58.439]      __var FLASH_BASE = 0x40022000 ;
[11:37:58.439]        // -> [FLASH_BASE <= 0x40022000]
[11:37:58.439]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:37:58.439]        // -> [FLASH_CR <= 0x40022004]
[11:37:58.447]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:37:58.447]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:37:58.447]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:37:58.447]        // -> [LOCK_BIT <= 0x00000001]
[11:37:58.447]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:37:58.447]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:37:58.447]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:37:58.449]        // -> [FLASH_KEYR <= 0x4002200C]
[11:37:58.449]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:37:58.449]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:37:58.449]      __var FLASH_KEY2 = 0x02030405 ;
[11:37:58.449]        // -> [FLASH_KEY2 <= 0x02030405]
[11:37:58.449]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:37:58.449]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:37:58.449]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:37:58.449]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:37:58.449]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:37:58.449]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:37:58.449]      __var FLASH_CR_Value = 0 ;
[11:37:58.454]        // -> [FLASH_CR_Value <= 0x00000000]
[11:37:58.454]      __var DoDebugPortStop = 1 ;
[11:37:58.454]        // -> [DoDebugPortStop <= 0x00000001]
[11:37:58.455]      __var DP_CTRL_STAT = 0x4 ;
[11:37:58.455]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:37:58.455]      __var DP_SELECT = 0x8 ;
[11:37:58.455]        // -> [DP_SELECT <= 0x00000008]
[11:37:58.456]    </block>
[11:37:58.456]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:37:58.456]      // if-block "connectionFlash && DoOptionByteLoading"
[11:37:58.456]        // =>  FALSE
[11:37:58.456]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:37:58.456]    </control>
[11:37:58.456]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:37:58.456]      // if-block "DoDebugPortStop"
[11:37:58.457]        // =>  TRUE
[11:37:58.457]      <block atomic="false" info="">
[11:37:58.457]        WriteDP(DP_SELECT, 0x00000000);
[11:37:58.469]  
[11:37:58.469]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:37:58.469]  
[11:37:58.492]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:37:58.492]      </block>
[11:37:58.493]      // end if-block "DoDebugPortStop"
[11:37:58.493]    </control>
[11:37:58.493]  </sequence>
[11:37:58.494]  
[11:37:59.539]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:37:59.539]  
[11:37:59.539]  <debugvars>
[11:37:59.540]    // Pre-defined
[11:37:59.540]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:59.540]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:59.540]    __dp=0x00000000
[11:37:59.541]    __ap=0x00000000
[11:37:59.541]    __traceout=0x00000000      (Trace Disabled)
[11:37:59.541]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:59.542]    __FlashAddr=0x00000000
[11:37:59.542]    __FlashLen=0x00000000
[11:37:59.542]    __FlashArg=0x00000000
[11:37:59.543]    __FlashOp=0x00000000
[11:37:59.543]    __Result=0x00000000
[11:37:59.543]    
[11:37:59.543]    // User-defined
[11:37:59.543]    DbgMCU_CR=0x00000007
[11:37:59.544]    DbgMCU_APB1_Fz=0x00000000
[11:37:59.544]    DbgMCU_APB2_Fz=0x00000000
[11:37:59.544]    DoOptionByteLoading=0x00000000
[11:37:59.544]  </debugvars>
[11:37:59.544]  
[11:37:59.544]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:37:59.544]    <block atomic="false" info="">
[11:37:59.545]      Sequence("CheckID");
[11:37:59.546]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:37:59.546]          <block atomic="false" info="">
[11:37:59.546]            __var pidr1 = 0;
[11:37:59.546]              // -> [pidr1 <= 0x00000000]
[11:37:59.547]            __var pidr2 = 0;
[11:37:59.547]              // -> [pidr2 <= 0x00000000]
[11:37:59.548]            __var jep106id = 0;
[11:37:59.548]              // -> [jep106id <= 0x00000000]
[11:37:59.548]            __var ROMTableBase = 0;
[11:37:59.548]              // -> [ROMTableBase <= 0x00000000]
[11:37:59.548]            __ap = 0;      // AHB-AP
[11:37:59.548]              // -> [__ap <= 0x00000000]
[11:37:59.549]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:37:59.549]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:37:59.550]              // -> [ROMTableBase <= 0xF0000000]
[11:37:59.550]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:37:59.551]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:37:59.552]              // -> [pidr1 <= 0x00000004]
[11:37:59.552]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:37:59.553]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:37:59.553]              // -> [pidr2 <= 0x0000000A]
[11:37:59.554]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:37:59.554]              // -> [jep106id <= 0x00000020]
[11:37:59.554]          </block>
[11:37:59.555]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:37:59.555]            // if-block "jep106id != 0x20"
[11:37:59.555]              // =>  FALSE
[11:37:59.556]            // skip if-block "jep106id != 0x20"
[11:37:59.556]          </control>
[11:37:59.556]        </sequence>
[11:37:59.557]    </block>
[11:37:59.557]  </sequence>
[11:37:59.557]  
[11:37:59.569]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:37:59.569]  
[11:37:59.583]  <debugvars>
[11:37:59.583]    // Pre-defined
[11:37:59.584]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:37:59.584]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:37:59.585]    __dp=0x00000000
[11:37:59.585]    __ap=0x00000000
[11:37:59.586]    __traceout=0x00000000      (Trace Disabled)
[11:37:59.587]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:37:59.587]    __FlashAddr=0x00000000
[11:37:59.587]    __FlashLen=0x00000000
[11:37:59.587]    __FlashArg=0x00000000
[11:37:59.587]    __FlashOp=0x00000000
[11:37:59.587]    __Result=0x00000000
[11:37:59.587]    
[11:37:59.587]    // User-defined
[11:37:59.587]    DbgMCU_CR=0x00000007
[11:37:59.587]    DbgMCU_APB1_Fz=0x00000000
[11:37:59.587]    DbgMCU_APB2_Fz=0x00000000
[11:37:59.587]    DoOptionByteLoading=0x00000000
[11:37:59.587]  </debugvars>
[11:37:59.587]  
[11:37:59.587]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:37:59.587]    <block atomic="false" info="">
[11:37:59.593]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:37:59.594]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:59.594]    </block>
[11:37:59.595]    <block atomic="false" info="DbgMCU registers">
[11:37:59.595]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:37:59.596]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:37:59.596]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:59.597]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:37:59.597]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:59.598]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:37:59.598]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:59.599]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:37:59.599]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:37:59.600]    </block>
[11:37:59.600]  </sequence>
[11:37:59.600]  
[11:38:03.169]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:38:03.169]  
[11:38:03.170]  <debugvars>
[11:38:03.170]    // Pre-defined
[11:38:03.171]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:38:03.171]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:38:03.171]    __dp=0x00000000
[11:38:03.172]    __ap=0x00000000
[11:38:03.172]    __traceout=0x00000000      (Trace Disabled)
[11:38:03.172]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:38:03.172]    __FlashAddr=0x00000000
[11:38:03.172]    __FlashLen=0x00000000
[11:38:03.172]    __FlashArg=0x00000000
[11:38:03.172]    __FlashOp=0x00000000
[11:38:03.172]    __Result=0x00000000
[11:38:03.172]    
[11:38:03.172]    // User-defined
[11:38:03.172]    DbgMCU_CR=0x00000007
[11:38:03.172]    DbgMCU_APB1_Fz=0x00000000
[11:38:03.172]    DbgMCU_APB2_Fz=0x00000000
[11:38:03.172]    DoOptionByteLoading=0x00000000
[11:38:03.172]  </debugvars>
[11:38:03.172]  
[11:38:03.172]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:38:03.172]    <block atomic="false" info="">
[11:38:03.172]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:38:03.172]        // -> [connectionFlash <= 0x00000001]
[11:38:03.172]      __var FLASH_BASE = 0x40022000 ;
[11:38:03.172]        // -> [FLASH_BASE <= 0x40022000]
[11:38:03.172]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:38:03.177]        // -> [FLASH_CR <= 0x40022004]
[11:38:03.177]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:38:03.177]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:38:03.177]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:38:03.177]        // -> [LOCK_BIT <= 0x00000001]
[11:38:03.177]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:38:03.177]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:38:03.177]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:38:03.177]        // -> [FLASH_KEYR <= 0x4002200C]
[11:38:03.177]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:38:03.177]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:38:03.177]      __var FLASH_KEY2 = 0x02030405 ;
[11:38:03.177]        // -> [FLASH_KEY2 <= 0x02030405]
[11:38:03.177]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:38:03.177]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:38:03.177]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:38:03.177]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:38:03.177]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:38:03.177]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:38:03.177]      __var FLASH_CR_Value = 0 ;
[11:38:03.177]        // -> [FLASH_CR_Value <= 0x00000000]
[11:38:03.177]      __var DoDebugPortStop = 1 ;
[11:38:03.177]        // -> [DoDebugPortStop <= 0x00000001]
[11:38:03.177]      __var DP_CTRL_STAT = 0x4 ;
[11:38:03.177]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:38:03.177]      __var DP_SELECT = 0x8 ;
[11:38:03.177]        // -> [DP_SELECT <= 0x00000008]
[11:38:03.177]    </block>
[11:38:03.177]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:38:03.177]      // if-block "connectionFlash && DoOptionByteLoading"
[11:38:03.177]        // =>  FALSE
[11:38:03.185]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:38:03.185]    </control>
[11:38:03.185]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:38:03.185]      // if-block "DoDebugPortStop"
[11:38:03.186]        // =>  TRUE
[11:38:03.186]      <block atomic="false" info="">
[11:38:03.186]        WriteDP(DP_SELECT, 0x00000000);
[11:38:03.193]  
[11:38:03.193]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:38:03.193]  
[11:38:03.223]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:38:03.226]      </block>
[11:38:03.227]      // end if-block "DoDebugPortStop"
[11:38:03.227]    </control>
[11:38:03.227]  </sequence>
[11:38:03.228]  
[11:38:05.073]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:38:05.073]  
[11:38:05.073]  <debugvars>
[11:38:05.074]    // Pre-defined
[11:38:05.074]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:38:05.074]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:38:05.074]    __dp=0x00000000
[11:38:05.075]    __ap=0x00000000
[11:38:05.075]    __traceout=0x00000000      (Trace Disabled)
[11:38:05.075]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:38:05.076]    __FlashAddr=0x00000000
[11:38:05.076]    __FlashLen=0x00000000
[11:38:05.077]    __FlashArg=0x00000000
[11:38:05.077]    __FlashOp=0x00000000
[11:38:05.077]    __Result=0x00000000
[11:38:05.078]    
[11:38:05.078]    // User-defined
[11:38:05.078]    DbgMCU_CR=0x00000007
[11:38:05.078]    DbgMCU_APB1_Fz=0x00000000
[11:38:05.078]    DbgMCU_APB2_Fz=0x00000000
[11:38:05.078]    DoOptionByteLoading=0x00000000
[11:38:05.079]  </debugvars>
[11:38:05.079]  
[11:38:05.079]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:38:05.079]    <block atomic="false" info="">
[11:38:05.079]      Sequence("CheckID");
[11:38:05.079]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:38:05.080]          <block atomic="false" info="">
[11:38:05.080]            __var pidr1 = 0;
[11:38:05.080]              // -> [pidr1 <= 0x00000000]
[11:38:05.080]            __var pidr2 = 0;
[11:38:05.080]              // -> [pidr2 <= 0x00000000]
[11:38:05.081]            __var jep106id = 0;
[11:38:05.081]              // -> [jep106id <= 0x00000000]
[11:38:05.081]            __var ROMTableBase = 0;
[11:38:05.081]              // -> [ROMTableBase <= 0x00000000]
[11:38:05.081]            __ap = 0;      // AHB-AP
[11:38:05.082]              // -> [__ap <= 0x00000000]
[11:38:05.082]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:38:05.082]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:38:05.083]              // -> [ROMTableBase <= 0xF0000000]
[11:38:05.083]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:38:05.084]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:38:05.085]              // -> [pidr1 <= 0x00000004]
[11:38:05.085]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:38:05.086]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:38:05.086]              // -> [pidr2 <= 0x0000000A]
[11:38:05.086]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:38:05.087]              // -> [jep106id <= 0x00000020]
[11:38:05.087]          </block>
[11:38:05.087]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:38:05.087]            // if-block "jep106id != 0x20"
[11:38:05.087]              // =>  FALSE
[11:38:05.087]            // skip if-block "jep106id != 0x20"
[11:38:05.087]          </control>
[11:38:05.087]        </sequence>
[11:38:05.087]    </block>
[11:38:05.087]  </sequence>
[11:38:05.087]  
[11:38:05.087]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:38:05.087]  
[11:38:05.133]  <debugvars>
[11:38:05.134]    // Pre-defined
[11:38:05.135]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:38:05.135]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:38:05.136]    __dp=0x00000000
[11:38:05.136]    __ap=0x00000000
[11:38:05.137]    __traceout=0x00000000      (Trace Disabled)
[11:38:05.138]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:38:05.138]    __FlashAddr=0x00000000
[11:38:05.139]    __FlashLen=0x00000000
[11:38:05.139]    __FlashArg=0x00000000
[11:38:05.140]    __FlashOp=0x00000000
[11:38:05.140]    __Result=0x00000000
[11:38:05.141]    
[11:38:05.141]    // User-defined
[11:38:05.141]    DbgMCU_CR=0x00000007
[11:38:05.142]    DbgMCU_APB1_Fz=0x00000000
[11:38:05.142]    DbgMCU_APB2_Fz=0x00000000
[11:38:05.143]    DoOptionByteLoading=0x00000000
[11:38:05.143]  </debugvars>
[11:38:05.144]  
[11:38:05.144]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:38:05.145]    <block atomic="false" info="">
[11:38:05.145]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:38:05.147]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:05.147]    </block>
[11:38:05.148]    <block atomic="false" info="DbgMCU registers">
[11:38:05.148]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:38:05.149]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:38:05.150]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:05.150]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:38:05.151]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:05.151]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:38:05.152]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:05.152]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:38:05.153]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:05.154]    </block>
[11:38:05.154]  </sequence>
[11:38:05.154]  
[11:38:08.200]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:38:08.200]  
[11:38:08.201]  <debugvars>
[11:38:08.201]    // Pre-defined
[11:38:08.202]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:38:08.202]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:38:08.202]    __dp=0x00000000
[11:38:08.203]    __ap=0x00000000
[11:38:08.203]    __traceout=0x00000000      (Trace Disabled)
[11:38:08.203]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:38:08.204]    __FlashAddr=0x00000000
[11:38:08.204]    __FlashLen=0x00000000
[11:38:08.204]    __FlashArg=0x00000000
[11:38:08.205]    __FlashOp=0x00000000
[11:38:08.205]    __Result=0x00000000
[11:38:08.205]    
[11:38:08.205]    // User-defined
[11:38:08.205]    DbgMCU_CR=0x00000007
[11:38:08.206]    DbgMCU_APB1_Fz=0x00000000
[11:38:08.206]    DbgMCU_APB2_Fz=0x00000000
[11:38:08.206]    DoOptionByteLoading=0x00000000
[11:38:08.206]  </debugvars>
[11:38:08.206]  
[11:38:08.206]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:38:08.206]    <block atomic="false" info="">
[11:38:08.206]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:38:08.206]        // -> [connectionFlash <= 0x00000001]
[11:38:08.206]      __var FLASH_BASE = 0x40022000 ;
[11:38:08.208]        // -> [FLASH_BASE <= 0x40022000]
[11:38:08.208]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:38:08.208]        // -> [FLASH_CR <= 0x40022004]
[11:38:08.209]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:38:08.209]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:38:08.209]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:38:08.209]        // -> [LOCK_BIT <= 0x00000001]
[11:38:08.209]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:38:08.210]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:38:08.211]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:38:08.211]        // -> [FLASH_KEYR <= 0x4002200C]
[11:38:08.211]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:38:08.211]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:38:08.211]      __var FLASH_KEY2 = 0x02030405 ;
[11:38:08.211]        // -> [FLASH_KEY2 <= 0x02030405]
[11:38:08.212]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:38:08.212]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:38:08.212]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:38:08.212]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:38:08.213]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:38:08.213]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:38:08.213]      __var FLASH_CR_Value = 0 ;
[11:38:08.213]        // -> [FLASH_CR_Value <= 0x00000000]
[11:38:08.213]      __var DoDebugPortStop = 1 ;
[11:38:08.214]        // -> [DoDebugPortStop <= 0x00000001]
[11:38:08.214]      __var DP_CTRL_STAT = 0x4 ;
[11:38:08.214]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:38:08.214]      __var DP_SELECT = 0x8 ;
[11:38:08.214]        // -> [DP_SELECT <= 0x00000008]
[11:38:08.215]    </block>
[11:38:08.215]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:38:08.215]      // if-block "connectionFlash && DoOptionByteLoading"
[11:38:08.215]        // =>  FALSE
[11:38:08.215]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:38:08.215]    </control>
[11:38:08.216]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:38:08.216]      // if-block "DoDebugPortStop"
[11:38:08.216]        // =>  TRUE
[11:38:08.216]      <block atomic="false" info="">
[11:38:08.217]        WriteDP(DP_SELECT, 0x00000000);
[11:38:08.223]  
[11:38:08.223]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:38:08.223]  
[11:38:08.229]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:38:08.231]      </block>
[11:38:08.231]      // end if-block "DoDebugPortStop"
[11:38:08.232]    </control>
[11:38:08.232]  </sequence>
[11:38:08.233]  
[11:38:12.129]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:38:12.129]  
[11:38:12.129]  <debugvars>
[11:38:12.130]    // Pre-defined
[11:38:12.130]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:38:12.130]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:38:12.131]    __dp=0x00000000
[11:38:12.131]    __ap=0x00000000
[11:38:12.131]    __traceout=0x00000000      (Trace Disabled)
[11:38:12.132]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:38:12.132]    __FlashAddr=0x00000000
[11:38:12.132]    __FlashLen=0x00000000
[11:38:12.132]    __FlashArg=0x00000000
[11:38:12.133]    __FlashOp=0x00000000
[11:38:12.133]    __Result=0x00000000
[11:38:12.133]    
[11:38:12.133]    // User-defined
[11:38:12.134]    DbgMCU_CR=0x00000007
[11:38:12.134]    DbgMCU_APB1_Fz=0x00000000
[11:38:12.134]    DbgMCU_APB2_Fz=0x00000000
[11:38:12.134]    DoOptionByteLoading=0x00000000
[11:38:12.135]  </debugvars>
[11:38:12.135]  
[11:38:12.135]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:38:12.135]    <block atomic="false" info="">
[11:38:12.135]      Sequence("CheckID");
[11:38:12.136]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:38:12.136]          <block atomic="false" info="">
[11:38:12.136]            __var pidr1 = 0;
[11:38:12.136]              // -> [pidr1 <= 0x00000000]
[11:38:12.136]            __var pidr2 = 0;
[11:38:12.137]              // -> [pidr2 <= 0x00000000]
[11:38:12.137]            __var jep106id = 0;
[11:38:12.137]              // -> [jep106id <= 0x00000000]
[11:38:12.137]            __var ROMTableBase = 0;
[11:38:12.137]              // -> [ROMTableBase <= 0x00000000]
[11:38:12.137]            __ap = 0;      // AHB-AP
[11:38:12.138]              // -> [__ap <= 0x00000000]
[11:38:12.138]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:38:12.138]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:38:12.139]              // -> [ROMTableBase <= 0xF0000000]
[11:38:12.139]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:38:12.140]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:38:12.141]              // -> [pidr1 <= 0x00000004]
[11:38:12.141]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:38:12.142]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:38:12.143]              // -> [pidr2 <= 0x0000000A]
[11:38:12.143]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:38:12.143]              // -> [jep106id <= 0x00000020]
[11:38:12.144]          </block>
[11:38:12.144]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:38:12.144]            // if-block "jep106id != 0x20"
[11:38:12.144]              // =>  FALSE
[11:38:12.144]            // skip if-block "jep106id != 0x20"
[11:38:12.144]          </control>
[11:38:12.145]        </sequence>
[11:38:12.145]    </block>
[11:38:12.145]  </sequence>
[11:38:12.145]  
[11:38:12.155]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:38:12.155]  
[11:38:12.155]  <debugvars>
[11:38:12.155]    // Pre-defined
[11:38:12.155]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:38:12.155]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:38:12.155]    __dp=0x00000000
[11:38:12.155]    __ap=0x00000000
[11:38:12.155]    __traceout=0x00000000      (Trace Disabled)
[11:38:12.155]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:38:12.155]    __FlashAddr=0x00000000
[11:38:12.155]    __FlashLen=0x00000000
[11:38:12.155]    __FlashArg=0x00000000
[11:38:12.155]    __FlashOp=0x00000000
[11:38:12.155]    __Result=0x00000000
[11:38:12.155]    
[11:38:12.155]    // User-defined
[11:38:12.155]    DbgMCU_CR=0x00000007
[11:38:12.155]    DbgMCU_APB1_Fz=0x00000000
[11:38:12.155]    DbgMCU_APB2_Fz=0x00000000
[11:38:12.161]    DoOptionByteLoading=0x00000000
[11:38:12.161]  </debugvars>
[11:38:12.162]  
[11:38:12.162]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:38:12.162]    <block atomic="false" info="">
[11:38:12.162]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:38:12.162]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:12.162]    </block>
[11:38:12.162]    <block atomic="false" info="DbgMCU registers">
[11:38:12.162]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:38:12.162]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:38:12.166]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:12.167]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:38:12.167]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:12.167]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:38:12.168]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:12.169]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:38:12.169]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:12.170]    </block>
[11:38:12.170]  </sequence>
[11:38:12.170]  
[11:38:17.750]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:38:17.750]  
[11:38:17.752]  <debugvars>
[11:38:17.752]    // Pre-defined
[11:38:17.752]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:38:17.753]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:38:17.753]    __dp=0x00000000
[11:38:17.753]    __ap=0x00000000
[11:38:17.753]    __traceout=0x00000000      (Trace Disabled)
[11:38:17.754]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:38:17.754]    __FlashAddr=0x00000000
[11:38:17.754]    __FlashLen=0x00000000
[11:38:17.754]    __FlashArg=0x00000000
[11:38:17.754]    __FlashOp=0x00000000
[11:38:17.755]    __Result=0x00000000
[11:38:17.755]    
[11:38:17.755]    // User-defined
[11:38:17.755]    DbgMCU_CR=0x00000007
[11:38:17.755]    DbgMCU_APB1_Fz=0x00000000
[11:38:17.755]    DbgMCU_APB2_Fz=0x00000000
[11:38:17.756]    DoOptionByteLoading=0x00000000
[11:38:17.756]  </debugvars>
[11:38:17.756]  
[11:38:17.756]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:38:17.756]    <block atomic="false" info="">
[11:38:17.756]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:38:17.756]        // -> [connectionFlash <= 0x00000001]
[11:38:17.756]      __var FLASH_BASE = 0x40022000 ;
[11:38:17.756]        // -> [FLASH_BASE <= 0x40022000]
[11:38:17.756]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:38:17.756]        // -> [FLASH_CR <= 0x40022004]
[11:38:17.756]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:38:17.758]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:38:17.759]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:38:17.759]        // -> [LOCK_BIT <= 0x00000001]
[11:38:17.759]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:38:17.759]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:38:17.760]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:38:17.760]        // -> [FLASH_KEYR <= 0x4002200C]
[11:38:17.760]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:38:17.760]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:38:17.761]      __var FLASH_KEY2 = 0x02030405 ;
[11:38:17.761]        // -> [FLASH_KEY2 <= 0x02030405]
[11:38:17.761]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:38:17.761]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:38:17.761]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:38:17.762]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:38:17.762]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:38:17.762]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:38:17.762]      __var FLASH_CR_Value = 0 ;
[11:38:17.762]        // -> [FLASH_CR_Value <= 0x00000000]
[11:38:17.763]      __var DoDebugPortStop = 1 ;
[11:38:17.763]        // -> [DoDebugPortStop <= 0x00000001]
[11:38:17.763]      __var DP_CTRL_STAT = 0x4 ;
[11:38:17.763]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:38:17.763]      __var DP_SELECT = 0x8 ;
[11:38:17.764]        // -> [DP_SELECT <= 0x00000008]
[11:38:17.764]    </block>
[11:38:17.764]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:38:17.764]      // if-block "connectionFlash && DoOptionByteLoading"
[11:38:17.764]        // =>  FALSE
[11:38:17.764]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:38:17.764]    </control>
[11:38:17.765]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:38:17.765]      // if-block "DoDebugPortStop"
[11:38:17.765]        // =>  TRUE
[11:38:17.766]      <block atomic="false" info="">
[11:38:17.766]        WriteDP(DP_SELECT, 0x00000000);
[11:38:17.772]  
[11:38:17.772]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:38:17.772]  
[11:38:17.800]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:38:17.802]      </block>
[11:38:17.803]      // end if-block "DoDebugPortStop"
[11:38:17.803]    </control>
[11:38:17.804]  </sequence>
[11:38:17.804]  
[11:38:30.935]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:38:30.935]  
[11:38:30.935]  <debugvars>
[11:38:30.935]    // Pre-defined
[11:38:30.935]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:38:30.935]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:38:30.937]    __dp=0x00000000
[11:38:30.937]    __ap=0x00000000
[11:38:30.937]    __traceout=0x00000000      (Trace Disabled)
[11:38:30.937]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:38:30.937]    __FlashAddr=0x00000000
[11:38:30.938]    __FlashLen=0x00000000
[11:38:30.938]    __FlashArg=0x00000000
[11:38:30.938]    __FlashOp=0x00000000
[11:38:30.938]    __Result=0x00000000
[11:38:30.938]    
[11:38:30.938]    // User-defined
[11:38:30.938]    DbgMCU_CR=0x00000007
[11:38:30.938]    DbgMCU_APB1_Fz=0x00000000
[11:38:30.938]    DbgMCU_APB2_Fz=0x00000000
[11:38:30.938]    DoOptionByteLoading=0x00000000
[11:38:30.938]  </debugvars>
[11:38:30.940]  
[11:38:30.940]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:38:30.940]    <block atomic="false" info="">
[11:38:30.940]      Sequence("CheckID");
[11:38:30.940]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:38:30.940]          <block atomic="false" info="">
[11:38:30.940]            __var pidr1 = 0;
[11:38:30.940]              // -> [pidr1 <= 0x00000000]
[11:38:30.940]            __var pidr2 = 0;
[11:38:30.942]              // -> [pidr2 <= 0x00000000]
[11:38:30.942]            __var jep106id = 0;
[11:38:30.942]              // -> [jep106id <= 0x00000000]
[11:38:30.942]            __var ROMTableBase = 0;
[11:38:30.942]              // -> [ROMTableBase <= 0x00000000]
[11:38:30.942]            __ap = 0;      // AHB-AP
[11:38:30.942]              // -> [__ap <= 0x00000000]
[11:38:30.942]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:38:30.944]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:38:30.944]              // -> [ROMTableBase <= 0xF0000000]
[11:38:30.944]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:38:30.944]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:38:30.946]              // -> [pidr1 <= 0x00000004]
[11:38:30.946]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:38:30.946]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:38:30.946]              // -> [pidr2 <= 0x0000000A]
[11:38:30.946]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:38:30.946]              // -> [jep106id <= 0x00000020]
[11:38:30.946]          </block>
[11:38:30.948]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:38:30.948]            // if-block "jep106id != 0x20"
[11:38:30.948]              // =>  FALSE
[11:38:30.948]            // skip if-block "jep106id != 0x20"
[11:38:30.948]          </control>
[11:38:30.948]        </sequence>
[11:38:30.948]    </block>
[11:38:30.948]  </sequence>
[11:38:30.948]  
[11:38:30.962]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:38:30.962]  
[11:38:30.981]  <debugvars>
[11:38:30.981]    // Pre-defined
[11:38:30.981]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:38:30.981]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:38:30.983]    __dp=0x00000000
[11:38:30.983]    __ap=0x00000000
[11:38:30.983]    __traceout=0x00000000      (Trace Disabled)
[11:38:30.983]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:38:30.983]    __FlashAddr=0x00000000
[11:38:30.984]    __FlashLen=0x00000000
[11:38:30.984]    __FlashArg=0x00000000
[11:38:30.984]    __FlashOp=0x00000000
[11:38:30.984]    __Result=0x00000000
[11:38:30.984]    
[11:38:30.984]    // User-defined
[11:38:30.984]    DbgMCU_CR=0x00000007
[11:38:30.984]    DbgMCU_APB1_Fz=0x00000000
[11:38:30.984]    DbgMCU_APB2_Fz=0x00000000
[11:38:30.984]    DoOptionByteLoading=0x00000000
[11:38:30.984]  </debugvars>
[11:38:30.984]  
[11:38:30.984]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:38:30.984]    <block atomic="false" info="">
[11:38:30.984]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:38:30.984]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:30.984]    </block>
[11:38:30.984]    <block atomic="false" info="DbgMCU registers">
[11:38:30.984]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:38:30.984]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:38:30.984]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:30.984]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:38:30.984]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:30.984]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:38:30.984]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:30.984]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:38:30.984]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:30.984]    </block>
[11:38:30.984]  </sequence>
[11:38:30.984]  
[11:38:32.929]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:38:32.929]  
[11:38:32.930]  <debugvars>
[11:38:32.930]    // Pre-defined
[11:38:32.930]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:38:32.930]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:38:32.931]    __dp=0x00000000
[11:38:32.931]    __ap=0x00000000
[11:38:32.931]    __traceout=0x00000000      (Trace Disabled)
[11:38:32.931]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:38:32.932]    __FlashAddr=0x00000000
[11:38:32.932]    __FlashLen=0x00000000
[11:38:32.932]    __FlashArg=0x00000000
[11:38:32.932]    __FlashOp=0x00000000
[11:38:32.933]    __Result=0x00000000
[11:38:32.933]    
[11:38:32.933]    // User-defined
[11:38:32.933]    DbgMCU_CR=0x00000007
[11:38:32.933]    DbgMCU_APB1_Fz=0x00000000
[11:38:32.934]    DbgMCU_APB2_Fz=0x00000000
[11:38:32.934]    DoOptionByteLoading=0x00000000
[11:38:32.934]  </debugvars>
[11:38:32.934]  
[11:38:32.934]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:38:32.934]    <block atomic="false" info="">
[11:38:32.934]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:38:32.935]        // -> [connectionFlash <= 0x00000001]
[11:38:32.935]      __var FLASH_BASE = 0x40022000 ;
[11:38:32.935]        // -> [FLASH_BASE <= 0x40022000]
[11:38:32.935]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:38:32.935]        // -> [FLASH_CR <= 0x40022004]
[11:38:32.935]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:38:32.935]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:38:32.935]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:38:32.935]        // -> [LOCK_BIT <= 0x00000001]
[11:38:32.937]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:38:32.937]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:38:32.937]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:38:32.937]        // -> [FLASH_KEYR <= 0x4002200C]
[11:38:32.937]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:38:32.938]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:38:32.938]      __var FLASH_KEY2 = 0x02030405 ;
[11:38:32.938]        // -> [FLASH_KEY2 <= 0x02030405]
[11:38:32.938]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:38:32.938]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:38:32.939]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:38:32.939]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:38:32.939]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:38:32.939]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:38:32.939]      __var FLASH_CR_Value = 0 ;
[11:38:32.939]        // -> [FLASH_CR_Value <= 0x00000000]
[11:38:32.939]      __var DoDebugPortStop = 1 ;
[11:38:32.939]        // -> [DoDebugPortStop <= 0x00000001]
[11:38:32.939]      __var DP_CTRL_STAT = 0x4 ;
[11:38:32.939]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:38:32.939]      __var DP_SELECT = 0x8 ;
[11:38:32.939]        // -> [DP_SELECT <= 0x00000008]
[11:38:32.939]    </block>
[11:38:32.939]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:38:32.939]      // if-block "connectionFlash && DoOptionByteLoading"
[11:38:32.939]        // =>  FALSE
[11:38:32.939]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:38:32.939]    </control>
[11:38:32.943]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:38:32.943]      // if-block "DoDebugPortStop"
[11:38:32.943]        // =>  TRUE
[11:38:32.943]      <block atomic="false" info="">
[11:38:32.943]        WriteDP(DP_SELECT, 0x00000000);
[11:38:32.964]  
[11:38:32.964]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:38:32.964]  
[11:38:32.975]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:38:32.976]      </block>
[11:38:32.976]      // end if-block "DoDebugPortStop"
[11:38:32.976]    </control>
[11:38:32.977]  </sequence>
[11:38:32.977]  
[11:38:37.982]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:38:37.982]  
[11:38:37.982]  <debugvars>
[11:38:37.983]    // Pre-defined
[11:38:37.983]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:38:37.983]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:38:37.983]    __dp=0x00000000
[11:38:37.984]    __ap=0x00000000
[11:38:37.984]    __traceout=0x00000000      (Trace Disabled)
[11:38:37.984]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:38:37.985]    __FlashAddr=0x00000000
[11:38:37.985]    __FlashLen=0x00000000
[11:38:37.985]    __FlashArg=0x00000000
[11:38:37.985]    __FlashOp=0x00000000
[11:38:37.986]    __Result=0x00000000
[11:38:37.986]    
[11:38:37.986]    // User-defined
[11:38:37.986]    DbgMCU_CR=0x00000007
[11:38:37.987]    DbgMCU_APB1_Fz=0x00000000
[11:38:37.988]    DbgMCU_APB2_Fz=0x00000000
[11:38:37.988]    DoOptionByteLoading=0x00000000
[11:38:37.988]  </debugvars>
[11:38:37.989]  
[11:38:37.989]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:38:37.989]    <block atomic="false" info="">
[11:38:37.989]      Sequence("CheckID");
[11:38:37.989]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:38:37.990]          <block atomic="false" info="">
[11:38:37.990]            __var pidr1 = 0;
[11:38:37.990]              // -> [pidr1 <= 0x00000000]
[11:38:37.990]            __var pidr2 = 0;
[11:38:37.991]              // -> [pidr2 <= 0x00000000]
[11:38:37.991]            __var jep106id = 0;
[11:38:37.991]              // -> [jep106id <= 0x00000000]
[11:38:37.991]            __var ROMTableBase = 0;
[11:38:37.991]              // -> [ROMTableBase <= 0x00000000]
[11:38:37.992]            __ap = 0;      // AHB-AP
[11:38:37.992]              // -> [__ap <= 0x00000000]
[11:38:37.992]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:38:37.993]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:38:37.993]              // -> [ROMTableBase <= 0xF0000000]
[11:38:37.993]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:38:37.994]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:38:37.995]              // -> [pidr1 <= 0x00000004]
[11:38:37.995]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:38:37.996]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:38:37.996]              // -> [pidr2 <= 0x0000000A]
[11:38:37.997]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:38:37.997]              // -> [jep106id <= 0x00000020]
[11:38:37.997]          </block>
[11:38:37.997]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:38:37.998]            // if-block "jep106id != 0x20"
[11:38:37.998]              // =>  FALSE
[11:38:37.998]            // skip if-block "jep106id != 0x20"
[11:38:37.998]          </control>
[11:38:37.998]        </sequence>
[11:38:37.998]    </block>
[11:38:37.999]  </sequence>
[11:38:37.999]  
[11:38:38.011]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:38:38.011]  
[11:38:38.032]  <debugvars>
[11:38:38.032]    // Pre-defined
[11:38:38.032]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:38:38.033]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:38:38.034]    __dp=0x00000000
[11:38:38.034]    __ap=0x00000000
[11:38:38.034]    __traceout=0x00000000      (Trace Disabled)
[11:38:38.034]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:38:38.035]    __FlashAddr=0x00000000
[11:38:38.036]    __FlashLen=0x00000000
[11:38:38.036]    __FlashArg=0x00000000
[11:38:38.036]    __FlashOp=0x00000000
[11:38:38.036]    __Result=0x00000000
[11:38:38.037]    
[11:38:38.037]    // User-defined
[11:38:38.037]    DbgMCU_CR=0x00000007
[11:38:38.037]    DbgMCU_APB1_Fz=0x00000000
[11:38:38.038]    DbgMCU_APB2_Fz=0x00000000
[11:38:38.038]    DoOptionByteLoading=0x00000000
[11:38:38.038]  </debugvars>
[11:38:38.038]  
[11:38:38.039]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:38:38.039]    <block atomic="false" info="">
[11:38:38.039]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:38:38.040]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:38.040]    </block>
[11:38:38.040]    <block atomic="false" info="DbgMCU registers">
[11:38:38.041]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:38:38.042]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:38:38.043]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:38.043]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:38:38.043]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:38.044]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:38:38.044]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:38.045]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:38:38.045]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:38.046]    </block>
[11:38:38.046]  </sequence>
[11:38:38.046]  
[11:38:43.107]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:38:43.107]  
[11:38:43.107]  <debugvars>
[11:38:43.107]    // Pre-defined
[11:38:43.107]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:38:43.107]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:38:43.118]    __dp=0x00000000
[11:38:43.118]    __ap=0x00000000
[11:38:43.118]    __traceout=0x00000000      (Trace Disabled)
[11:38:43.118]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:38:43.118]    __FlashAddr=0x00000000
[11:38:43.119]    __FlashLen=0x00000000
[11:38:43.120]    __FlashArg=0x00000000
[11:38:43.120]    __FlashOp=0x00000000
[11:38:43.121]    __Result=0x00000000
[11:38:43.121]    
[11:38:43.121]    // User-defined
[11:38:43.121]    DbgMCU_CR=0x00000007
[11:38:43.121]    DbgMCU_APB1_Fz=0x00000000
[11:38:43.121]    DbgMCU_APB2_Fz=0x00000000
[11:38:43.121]    DoOptionByteLoading=0x00000000
[11:38:43.122]  </debugvars>
[11:38:43.122]  
[11:38:43.122]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:38:43.122]    <block atomic="false" info="">
[11:38:43.122]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:38:43.122]        // -> [connectionFlash <= 0x00000001]
[11:38:43.122]      __var FLASH_BASE = 0x40022000 ;
[11:38:43.122]        // -> [FLASH_BASE <= 0x40022000]
[11:38:43.122]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:38:43.122]        // -> [FLASH_CR <= 0x40022004]
[11:38:43.122]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:38:43.122]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:38:43.122]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:38:43.125]        // -> [LOCK_BIT <= 0x00000001]
[11:38:43.125]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:38:43.125]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:38:43.126]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:38:43.126]        // -> [FLASH_KEYR <= 0x4002200C]
[11:38:43.126]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:38:43.126]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:38:43.126]      __var FLASH_KEY2 = 0x02030405 ;
[11:38:43.127]        // -> [FLASH_KEY2 <= 0x02030405]
[11:38:43.127]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:38:43.127]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:38:43.128]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:38:43.128]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:38:43.128]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:38:43.128]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:38:43.129]      __var FLASH_CR_Value = 0 ;
[11:38:43.129]        // -> [FLASH_CR_Value <= 0x00000000]
[11:38:43.129]      __var DoDebugPortStop = 1 ;
[11:38:43.130]        // -> [DoDebugPortStop <= 0x00000001]
[11:38:43.130]      __var DP_CTRL_STAT = 0x4 ;
[11:38:43.130]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:38:43.130]      __var DP_SELECT = 0x8 ;
[11:38:43.131]        // -> [DP_SELECT <= 0x00000008]
[11:38:43.131]    </block>
[11:38:43.131]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:38:43.131]      // if-block "connectionFlash && DoOptionByteLoading"
[11:38:43.131]        // =>  FALSE
[11:38:43.132]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:38:43.132]    </control>
[11:38:43.132]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:38:43.132]      // if-block "DoDebugPortStop"
[11:38:43.132]        // =>  TRUE
[11:38:43.133]      <block atomic="false" info="">
[11:38:43.133]        WriteDP(DP_SELECT, 0x00000000);
[11:38:43.149]  
[11:38:43.149]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:38:43.149]  
[11:38:43.158]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:38:43.158]      </block>
[11:38:43.159]      // end if-block "DoDebugPortStop"
[11:38:43.159]    </control>
[11:38:43.160]  </sequence>
[11:38:43.160]  
[11:38:45.164]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:38:45.164]  
[11:38:45.164]  <debugvars>
[11:38:45.165]    // Pre-defined
[11:38:45.165]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:38:45.165]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:38:45.166]    __dp=0x00000000
[11:38:45.166]    __ap=0x00000000
[11:38:45.166]    __traceout=0x00000000      (Trace Disabled)
[11:38:45.166]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:38:45.167]    __FlashAddr=0x00000000
[11:38:45.167]    __FlashLen=0x00000000
[11:38:45.167]    __FlashArg=0x00000000
[11:38:45.167]    __FlashOp=0x00000000
[11:38:45.167]    __Result=0x00000000
[11:38:45.168]    
[11:38:45.168]    // User-defined
[11:38:45.168]    DbgMCU_CR=0x00000007
[11:38:45.168]    DbgMCU_APB1_Fz=0x00000000
[11:38:45.168]    DbgMCU_APB2_Fz=0x00000000
[11:38:45.168]    DoOptionByteLoading=0x00000000
[11:38:45.169]  </debugvars>
[11:38:45.169]  
[11:38:45.169]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:38:45.169]    <block atomic="false" info="">
[11:38:45.169]      Sequence("CheckID");
[11:38:45.169]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:38:45.170]          <block atomic="false" info="">
[11:38:45.170]            __var pidr1 = 0;
[11:38:45.170]              // -> [pidr1 <= 0x00000000]
[11:38:45.170]            __var pidr2 = 0;
[11:38:45.170]              // -> [pidr2 <= 0x00000000]
[11:38:45.170]            __var jep106id = 0;
[11:38:45.170]              // -> [jep106id <= 0x00000000]
[11:38:45.170]            __var ROMTableBase = 0;
[11:38:45.170]              // -> [ROMTableBase <= 0x00000000]
[11:38:45.170]            __ap = 0;      // AHB-AP
[11:38:45.170]              // -> [__ap <= 0x00000000]
[11:38:45.170]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:38:45.170]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:38:45.170]              // -> [ROMTableBase <= 0xF0000000]
[11:38:45.170]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:38:45.170]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:38:45.170]              // -> [pidr1 <= 0x00000004]
[11:38:45.170]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:38:45.170]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:38:45.170]              // -> [pidr2 <= 0x0000000A]
[11:38:45.170]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:38:45.170]              // -> [jep106id <= 0x00000020]
[11:38:45.170]          </block>
[11:38:45.170]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:38:45.170]            // if-block "jep106id != 0x20"
[11:38:45.170]              // =>  FALSE
[11:38:45.170]            // skip if-block "jep106id != 0x20"
[11:38:45.170]          </control>
[11:38:45.170]        </sequence>
[11:38:45.170]    </block>
[11:38:45.170]  </sequence>
[11:38:45.170]  
[11:38:45.190]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:38:45.190]  
[11:38:45.200]  <debugvars>
[11:38:45.200]    // Pre-defined
[11:38:45.201]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:38:45.201]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:38:45.202]    __dp=0x00000000
[11:38:45.203]    __ap=0x00000000
[11:38:45.203]    __traceout=0x00000000      (Trace Disabled)
[11:38:45.204]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:38:45.205]    __FlashAddr=0x00000000
[11:38:45.205]    __FlashLen=0x00000000
[11:38:45.206]    __FlashArg=0x00000000
[11:38:45.206]    __FlashOp=0x00000000
[11:38:45.207]    __Result=0x00000000
[11:38:45.207]    
[11:38:45.207]    // User-defined
[11:38:45.208]    DbgMCU_CR=0x00000007
[11:38:45.208]    DbgMCU_APB1_Fz=0x00000000
[11:38:45.208]    DbgMCU_APB2_Fz=0x00000000
[11:38:45.209]    DoOptionByteLoading=0x00000000
[11:38:45.209]  </debugvars>
[11:38:45.210]  
[11:38:45.210]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:38:45.211]    <block atomic="false" info="">
[11:38:45.211]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:38:45.213]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:45.213]    </block>
[11:38:45.214]    <block atomic="false" info="DbgMCU registers">
[11:38:45.214]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:38:45.215]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:38:45.216]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:45.217]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:38:45.218]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:45.218]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:38:45.219]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:45.219]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:38:45.220]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:38:45.221]    </block>
[11:38:45.221]  </sequence>
[11:38:45.221]  
[11:38:52.594]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:38:52.594]  
[11:38:52.594]  <debugvars>
[11:38:52.594]    // Pre-defined
[11:38:52.594]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:38:52.594]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:38:52.598]    __dp=0x00000000
[11:38:52.599]    __ap=0x00000000
[11:38:52.599]    __traceout=0x00000000      (Trace Disabled)
[11:38:52.599]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:38:52.600]    __FlashAddr=0x00000000
[11:38:52.600]    __FlashLen=0x00000000
[11:38:52.600]    __FlashArg=0x00000000
[11:38:52.600]    __FlashOp=0x00000000
[11:38:52.602]    __Result=0x00000000
[11:38:52.602]    
[11:38:52.602]    // User-defined
[11:38:52.603]    DbgMCU_CR=0x00000007
[11:38:52.603]    DbgMCU_APB1_Fz=0x00000000
[11:38:52.604]    DbgMCU_APB2_Fz=0x00000000
[11:38:52.604]    DoOptionByteLoading=0x00000000
[11:38:52.605]  </debugvars>
[11:38:52.605]  
[11:38:52.606]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:38:52.606]    <block atomic="false" info="">
[11:38:52.606]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:38:52.607]        // -> [connectionFlash <= 0x00000001]
[11:38:52.607]      __var FLASH_BASE = 0x40022000 ;
[11:38:52.607]        // -> [FLASH_BASE <= 0x40022000]
[11:38:52.608]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:38:52.608]        // -> [FLASH_CR <= 0x40022004]
[11:38:52.608]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:38:52.609]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:38:52.609]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:38:52.609]        // -> [LOCK_BIT <= 0x00000001]
[11:38:52.609]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:38:52.610]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:38:52.610]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:38:52.610]        // -> [FLASH_KEYR <= 0x4002200C]
[11:38:52.610]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:38:52.611]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:38:52.611]      __var FLASH_KEY2 = 0x02030405 ;
[11:38:52.611]        // -> [FLASH_KEY2 <= 0x02030405]
[11:38:52.611]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:38:52.612]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:38:52.612]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:38:52.612]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:38:52.612]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:38:52.612]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:38:52.613]      __var FLASH_CR_Value = 0 ;
[11:38:52.613]        // -> [FLASH_CR_Value <= 0x00000000]
[11:38:52.613]      __var DoDebugPortStop = 1 ;
[11:38:52.613]        // -> [DoDebugPortStop <= 0x00000001]
[11:38:52.613]      __var DP_CTRL_STAT = 0x4 ;
[11:38:52.614]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:38:52.614]      __var DP_SELECT = 0x8 ;
[11:38:52.614]        // -> [DP_SELECT <= 0x00000008]
[11:38:52.614]    </block>
[11:38:52.614]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:38:52.614]      // if-block "connectionFlash && DoOptionByteLoading"
[11:38:52.615]        // =>  FALSE
[11:38:52.615]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:38:52.615]    </control>
[11:38:52.615]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:38:52.615]      // if-block "DoDebugPortStop"
[11:38:52.616]        // =>  TRUE
[11:38:52.616]      <block atomic="false" info="">
[11:38:52.616]        WriteDP(DP_SELECT, 0x00000000);
[11:38:52.616]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:38:52.617]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:38:52.617]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:38:52.617]      </block>
[11:38:52.617]      // end if-block "DoDebugPortStop"
[11:38:52.617]    </control>
[11:38:52.618]  </sequence>
[11:38:52.618]  
[11:42:27.856]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:42:27.856]  
[11:42:27.857]  <debugvars>
[11:42:27.857]    // Pre-defined
[11:42:27.857]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:42:27.857]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:42:27.857]    __dp=0x00000000
[11:42:27.857]    __ap=0x00000000
[11:42:27.857]    __traceout=0x00000000      (Trace Disabled)
[11:42:27.857]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:42:27.859]    __FlashAddr=0x00000000
[11:42:27.859]    __FlashLen=0x00000000
[11:42:27.860]    __FlashArg=0x00000000
[11:42:27.860]    __FlashOp=0x00000000
[11:42:27.860]    __Result=0x00000000
[11:42:27.860]    
[11:42:27.860]    // User-defined
[11:42:27.860]    DbgMCU_CR=0x00000007
[11:42:27.861]    DbgMCU_APB1_Fz=0x00000000
[11:42:27.861]    DbgMCU_APB2_Fz=0x00000000
[11:42:27.861]    DoOptionByteLoading=0x00000000
[11:42:27.861]  </debugvars>
[11:42:27.861]  
[11:42:27.861]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:42:27.862]    <block atomic="false" info="">
[11:42:27.862]      Sequence("CheckID");
[11:42:27.862]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:42:27.862]          <block atomic="false" info="">
[11:42:27.862]            __var pidr1 = 0;
[11:42:27.863]              // -> [pidr1 <= 0x00000000]
[11:42:27.863]            __var pidr2 = 0;
[11:42:27.863]              // -> [pidr2 <= 0x00000000]
[11:42:27.863]            __var jep106id = 0;
[11:42:27.863]              // -> [jep106id <= 0x00000000]
[11:42:27.863]            __var ROMTableBase = 0;
[11:42:27.864]              // -> [ROMTableBase <= 0x00000000]
[11:42:27.864]            __ap = 0;      // AHB-AP
[11:42:27.864]              // -> [__ap <= 0x00000000]
[11:42:27.864]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:42:27.865]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:42:27.865]              // -> [ROMTableBase <= 0xF0000000]
[11:42:27.866]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:42:27.867]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:42:27.867]              // -> [pidr1 <= 0x00000004]
[11:42:27.868]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:42:27.868]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:42:27.869]              // -> [pidr2 <= 0x0000000A]
[11:42:27.869]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:42:27.870]              // -> [jep106id <= 0x00000020]
[11:42:27.870]          </block>
[11:42:27.870]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:42:27.871]            // if-block "jep106id != 0x20"
[11:42:27.871]              // =>  FALSE
[11:42:27.871]            // skip if-block "jep106id != 0x20"
[11:42:27.871]          </control>
[11:42:27.872]        </sequence>
[11:42:27.872]    </block>
[11:42:27.872]  </sequence>
[11:42:27.873]  
[11:42:27.885]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:42:27.885]  
[11:42:27.915]  <debugvars>
[11:42:27.916]    // Pre-defined
[11:42:27.917]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:42:27.918]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:42:27.919]    __dp=0x00000000
[11:42:27.919]    __ap=0x00000000
[11:42:27.920]    __traceout=0x00000000      (Trace Disabled)
[11:42:27.920]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:42:27.921]    __FlashAddr=0x00000000
[11:42:27.922]    __FlashLen=0x00000000
[11:42:27.923]    __FlashArg=0x00000000
[11:42:27.923]    __FlashOp=0x00000000
[11:42:27.924]    __Result=0x00000000
[11:42:27.925]    
[11:42:27.925]    // User-defined
[11:42:27.926]    DbgMCU_CR=0x00000007
[11:42:27.926]    DbgMCU_APB1_Fz=0x00000000
[11:42:27.927]    DbgMCU_APB2_Fz=0x00000000
[11:42:27.928]    DoOptionByteLoading=0x00000000
[11:42:27.929]  </debugvars>
[11:42:27.929]  
[11:42:27.930]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:42:27.931]    <block atomic="false" info="">
[11:42:27.931]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:42:27.934]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:42:27.934]    </block>
[11:42:27.935]    <block atomic="false" info="DbgMCU registers">
[11:42:27.935]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:42:27.935]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:42:27.935]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:42:27.935]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:42:27.935]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:42:27.935]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:42:27.935]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:42:27.935]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:42:27.935]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:42:27.935]    </block>
[11:42:27.935]  </sequence>
[11:42:27.935]  
[11:42:33.303]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:42:33.303]  
[11:42:33.304]  <debugvars>
[11:42:33.304]    // Pre-defined
[11:42:33.304]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:42:33.305]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:42:33.305]    __dp=0x00000000
[11:42:33.305]    __ap=0x00000000
[11:42:33.305]    __traceout=0x00000000      (Trace Disabled)
[11:42:33.306]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:42:33.306]    __FlashAddr=0x00000000
[11:42:33.306]    __FlashLen=0x00000000
[11:42:33.306]    __FlashArg=0x00000000
[11:42:33.306]    __FlashOp=0x00000000
[11:42:33.306]    __Result=0x00000000
[11:42:33.306]    
[11:42:33.306]    // User-defined
[11:42:33.306]    DbgMCU_CR=0x00000007
[11:42:33.306]    DbgMCU_APB1_Fz=0x00000000
[11:42:33.306]    DbgMCU_APB2_Fz=0x00000000
[11:42:33.306]    DoOptionByteLoading=0x00000000
[11:42:33.306]  </debugvars>
[11:42:33.306]  
[11:42:33.306]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:42:33.306]    <block atomic="false" info="">
[11:42:33.306]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:42:33.306]        // -> [connectionFlash <= 0x00000001]
[11:42:33.306]      __var FLASH_BASE = 0x40022000 ;
[11:42:33.306]        // -> [FLASH_BASE <= 0x40022000]
[11:42:33.306]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:42:33.306]        // -> [FLASH_CR <= 0x40022004]
[11:42:33.306]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:42:33.306]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:42:33.306]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:42:33.306]        // -> [LOCK_BIT <= 0x00000001]
[11:42:33.306]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:42:33.306]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:42:33.306]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:42:33.306]        // -> [FLASH_KEYR <= 0x4002200C]
[11:42:33.306]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:42:33.306]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:42:33.306]      __var FLASH_KEY2 = 0x02030405 ;
[11:42:33.306]        // -> [FLASH_KEY2 <= 0x02030405]
[11:42:33.306]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:42:33.306]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:42:33.306]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:42:33.306]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:42:33.306]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:42:33.314]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:42:33.314]      __var FLASH_CR_Value = 0 ;
[11:42:33.314]        // -> [FLASH_CR_Value <= 0x00000000]
[11:42:33.314]      __var DoDebugPortStop = 1 ;
[11:42:33.315]        // -> [DoDebugPortStop <= 0x00000001]
[11:42:33.315]      __var DP_CTRL_STAT = 0x4 ;
[11:42:33.315]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:42:33.315]      __var DP_SELECT = 0x8 ;
[11:42:33.315]        // -> [DP_SELECT <= 0x00000008]
[11:42:33.315]    </block>
[11:42:33.315]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:42:33.315]      // if-block "connectionFlash && DoOptionByteLoading"
[11:42:33.315]        // =>  FALSE
[11:42:33.315]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:42:33.315]    </control>
[11:42:33.315]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:42:33.319]      // if-block "DoDebugPortStop"
[11:42:33.319]        // =>  TRUE
[11:42:33.319]      <block atomic="false" info="">
[11:42:33.320]        WriteDP(DP_SELECT, 0x00000000);
[11:42:33.320]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:42:33.320]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:42:33.321]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:42:33.321]      </block>
[11:42:33.321]      // end if-block "DoDebugPortStop"
[11:42:33.321]    </control>
[11:42:33.321]  </sequence>
[11:42:33.322]  
[11:42:34.983]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:42:34.983]  
[11:42:34.983]  <debugvars>
[11:42:34.983]    // Pre-defined
[11:42:34.984]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:42:34.984]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:42:34.984]    __dp=0x00000000
[11:42:34.985]    __ap=0x00000000
[11:42:34.985]    __traceout=0x00000000      (Trace Disabled)
[11:42:34.985]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:42:34.985]    __FlashAddr=0x00000000
[11:42:34.986]    __FlashLen=0x00000000
[11:42:34.986]    __FlashArg=0x00000000
[11:42:34.986]    __FlashOp=0x00000000
[11:42:34.987]    __Result=0x00000000
[11:42:34.987]    
[11:42:34.987]    // User-defined
[11:42:34.987]    DbgMCU_CR=0x00000007
[11:42:34.988]    DbgMCU_APB1_Fz=0x00000000
[11:42:34.988]    DbgMCU_APB2_Fz=0x00000000
[11:42:34.988]    DoOptionByteLoading=0x00000000
[11:42:34.988]  </debugvars>
[11:42:34.989]  
[11:42:34.989]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:42:34.989]    <block atomic="false" info="">
[11:42:34.990]      Sequence("CheckID");
[11:42:34.990]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:42:34.990]          <block atomic="false" info="">
[11:42:34.991]            __var pidr1 = 0;
[11:42:34.991]              // -> [pidr1 <= 0x00000000]
[11:42:34.992]            __var pidr2 = 0;
[11:42:34.992]              // -> [pidr2 <= 0x00000000]
[11:42:34.993]            __var jep106id = 0;
[11:42:34.993]              // -> [jep106id <= 0x00000000]
[11:42:34.993]            __var ROMTableBase = 0;
[11:42:34.993]              // -> [ROMTableBase <= 0x00000000]
[11:42:34.993]            __ap = 0;      // AHB-AP
[11:42:34.994]              // -> [__ap <= 0x00000000]
[11:42:34.994]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:42:34.994]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:42:34.995]              // -> [ROMTableBase <= 0xF0000000]
[11:42:34.995]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:42:34.996]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:42:34.997]              // -> [pidr1 <= 0x00000004]
[11:42:34.997]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:42:34.998]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:42:34.998]              // -> [pidr2 <= 0x0000000A]
[11:42:34.999]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:42:34.999]              // -> [jep106id <= 0x00000020]
[11:42:34.999]          </block>
[11:42:34.999]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:42:35.000]            // if-block "jep106id != 0x20"
[11:42:35.000]              // =>  FALSE
[11:42:35.000]            // skip if-block "jep106id != 0x20"
[11:42:35.000]          </control>
[11:42:35.000]        </sequence>
[11:42:35.001]    </block>
[11:42:35.001]  </sequence>
[11:42:35.001]  
[11:42:35.013]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:42:35.013]  
[11:42:35.023]  <debugvars>
[11:42:35.023]    // Pre-defined
[11:42:35.024]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:42:35.025]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:42:35.025]    __dp=0x00000000
[11:42:35.026]    __ap=0x00000000
[11:42:35.026]    __traceout=0x00000000      (Trace Disabled)
[11:42:35.027]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:42:35.027]    __FlashAddr=0x00000000
[11:42:35.028]    __FlashLen=0x00000000
[11:42:35.028]    __FlashArg=0x00000000
[11:42:35.029]    __FlashOp=0x00000000
[11:42:35.029]    __Result=0x00000000
[11:42:35.030]    
[11:42:35.030]    // User-defined
[11:42:35.030]    DbgMCU_CR=0x00000007
[11:42:35.031]    DbgMCU_APB1_Fz=0x00000000
[11:42:35.031]    DbgMCU_APB2_Fz=0x00000000
[11:42:35.031]    DoOptionByteLoading=0x00000000
[11:42:35.031]  </debugvars>
[11:42:35.032]  
[11:42:35.033]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:42:35.033]    <block atomic="false" info="">
[11:42:35.034]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:42:35.035]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:42:35.036]    </block>
[11:42:35.036]    <block atomic="false" info="DbgMCU registers">
[11:42:35.037]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:42:35.038]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:42:35.039]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:42:35.039]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:42:35.040]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:42:35.040]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:42:35.041]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:42:35.042]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:42:35.042]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:42:35.043]    </block>
[11:42:35.043]  </sequence>
[11:42:35.043]  
[11:42:43.738]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:42:43.738]  
[11:42:43.739]  <debugvars>
[11:42:43.739]    // Pre-defined
[11:42:43.739]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:42:43.739]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:42:43.739]    __dp=0x00000000
[11:42:43.739]    __ap=0x00000000
[11:42:43.739]    __traceout=0x00000000      (Trace Disabled)
[11:42:43.739]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:42:43.739]    __FlashAddr=0x00000000
[11:42:43.739]    __FlashLen=0x00000000
[11:42:43.739]    __FlashArg=0x00000000
[11:42:43.742]    __FlashOp=0x00000000
[11:42:43.742]    __Result=0x00000000
[11:42:43.742]    
[11:42:43.742]    // User-defined
[11:42:43.743]    DbgMCU_CR=0x00000007
[11:42:43.743]    DbgMCU_APB1_Fz=0x00000000
[11:42:43.743]    DbgMCU_APB2_Fz=0x00000000
[11:42:43.743]    DoOptionByteLoading=0x00000000
[11:42:43.744]  </debugvars>
[11:42:43.744]  
[11:42:43.745]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:42:43.745]    <block atomic="false" info="">
[11:42:43.745]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:42:43.746]        // -> [connectionFlash <= 0x00000001]
[11:42:43.746]      __var FLASH_BASE = 0x40022000 ;
[11:42:43.746]        // -> [FLASH_BASE <= 0x40022000]
[11:42:43.746]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:42:43.747]        // -> [FLASH_CR <= 0x40022004]
[11:42:43.747]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:42:43.747]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:42:43.747]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:42:43.747]        // -> [LOCK_BIT <= 0x00000001]
[11:42:43.748]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:42:43.748]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:42:43.748]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:42:43.748]        // -> [FLASH_KEYR <= 0x4002200C]
[11:42:43.748]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:42:43.748]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:42:43.749]      __var FLASH_KEY2 = 0x02030405 ;
[11:42:43.749]        // -> [FLASH_KEY2 <= 0x02030405]
[11:42:43.749]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:42:43.749]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:42:43.749]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:42:43.750]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:42:43.750]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:42:43.750]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:42:43.750]      __var FLASH_CR_Value = 0 ;
[11:42:43.751]        // -> [FLASH_CR_Value <= 0x00000000]
[11:42:43.751]      __var DoDebugPortStop = 1 ;
[11:42:43.751]        // -> [DoDebugPortStop <= 0x00000001]
[11:42:43.751]      __var DP_CTRL_STAT = 0x4 ;
[11:42:43.751]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:42:43.751]      __var DP_SELECT = 0x8 ;
[11:42:43.752]        // -> [DP_SELECT <= 0x00000008]
[11:42:43.752]    </block>
[11:42:43.752]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:42:43.752]      // if-block "connectionFlash && DoOptionByteLoading"
[11:42:43.752]        // =>  FALSE
[11:42:43.753]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:42:43.753]    </control>
[11:42:43.753]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:42:43.753]      // if-block "DoDebugPortStop"
[11:42:43.753]        // =>  TRUE
[11:42:43.754]      <block atomic="false" info="">
[11:42:43.754]        WriteDP(DP_SELECT, 0x00000000);
[11:42:43.765]  
[11:42:43.765]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:42:43.765]  
[11:42:43.767]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:42:43.767]      </block>
[11:42:43.768]      // end if-block "DoDebugPortStop"
[11:42:43.768]    </control>
[11:42:43.768]  </sequence>
[11:42:43.769]  
[11:42:45.335]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:42:45.335]  
[11:42:45.335]  <debugvars>
[11:42:45.335]    // Pre-defined
[11:42:45.335]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:42:45.335]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:42:45.335]    __dp=0x00000000
[11:42:45.335]    __ap=0x00000000
[11:42:45.335]    __traceout=0x00000000      (Trace Disabled)
[11:42:45.335]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:42:45.335]    __FlashAddr=0x00000000
[11:42:45.335]    __FlashLen=0x00000000
[11:42:45.335]    __FlashArg=0x00000000
[11:42:45.335]    __FlashOp=0x00000000
[11:42:45.335]    __Result=0x00000000
[11:42:45.335]    
[11:42:45.335]    // User-defined
[11:42:45.335]    DbgMCU_CR=0x00000007
[11:42:45.335]    DbgMCU_APB1_Fz=0x00000000
[11:42:45.335]    DbgMCU_APB2_Fz=0x00000000
[11:42:45.335]    DoOptionByteLoading=0x00000000
[11:42:45.335]  </debugvars>
[11:42:45.335]  
[11:42:45.341]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:42:45.341]    <block atomic="false" info="">
[11:42:45.341]      Sequence("CheckID");
[11:42:45.341]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:42:45.341]          <block atomic="false" info="">
[11:42:45.341]            __var pidr1 = 0;
[11:42:45.341]              // -> [pidr1 <= 0x00000000]
[11:42:45.341]            __var pidr2 = 0;
[11:42:45.341]              // -> [pidr2 <= 0x00000000]
[11:42:45.341]            __var jep106id = 0;
[11:42:45.343]              // -> [jep106id <= 0x00000000]
[11:42:45.343]            __var ROMTableBase = 0;
[11:42:45.343]              // -> [ROMTableBase <= 0x00000000]
[11:42:45.343]            __ap = 0;      // AHB-AP
[11:42:45.343]              // -> [__ap <= 0x00000000]
[11:42:45.343]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:42:45.343]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:42:45.343]              // -> [ROMTableBase <= 0xF0000000]
[11:42:45.343]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:42:45.346]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:42:45.346]              // -> [pidr1 <= 0x00000004]
[11:42:45.346]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:42:45.346]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:42:45.346]              // -> [pidr2 <= 0x0000000A]
[11:42:45.346]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:42:45.346]              // -> [jep106id <= 0x00000020]
[11:42:45.346]          </block>
[11:42:45.346]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:42:45.349]            // if-block "jep106id != 0x20"
[11:42:45.349]              // =>  FALSE
[11:42:45.349]            // skip if-block "jep106id != 0x20"
[11:42:45.349]          </control>
[11:42:45.349]        </sequence>
[11:42:45.349]    </block>
[11:42:45.349]  </sequence>
[11:42:45.349]  
[11:42:45.363]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:42:45.363]  
[11:42:45.390]  <debugvars>
[11:42:45.391]    // Pre-defined
[11:42:45.391]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:42:45.392]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:42:45.392]    __dp=0x00000000
[11:42:45.393]    __ap=0x00000000
[11:42:45.393]    __traceout=0x00000000      (Trace Disabled)
[11:42:45.393]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:42:45.394]    __FlashAddr=0x00000000
[11:42:45.394]    __FlashLen=0x00000000
[11:42:45.394]    __FlashArg=0x00000000
[11:42:45.394]    __FlashOp=0x00000000
[11:42:45.396]    __Result=0x00000000
[11:42:45.396]    
[11:42:45.396]    // User-defined
[11:42:45.396]    DbgMCU_CR=0x00000007
[11:42:45.397]    DbgMCU_APB1_Fz=0x00000000
[11:42:45.397]    DbgMCU_APB2_Fz=0x00000000
[11:42:45.398]    DoOptionByteLoading=0x00000000
[11:42:45.398]  </debugvars>
[11:42:45.398]  
[11:42:45.398]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:42:45.398]    <block atomic="false" info="">
[11:42:45.398]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:42:45.399]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:42:45.400]    </block>
[11:42:45.400]    <block atomic="false" info="DbgMCU registers">
[11:42:45.400]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:42:45.401]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:42:45.402]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:42:45.402]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:42:45.403]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:42:45.403]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:42:45.404]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:42:45.404]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:42:45.405]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:42:45.405]    </block>
[11:42:45.405]  </sequence>
[11:42:45.406]  
[11:42:52.766]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:42:52.766]  
[11:42:52.767]  <debugvars>
[11:42:52.768]    // Pre-defined
[11:42:52.768]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:42:52.769]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:42:52.769]    __dp=0x00000000
[11:42:52.769]    __ap=0x00000000
[11:42:52.770]    __traceout=0x00000000      (Trace Disabled)
[11:42:52.770]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:42:52.771]    __FlashAddr=0x00000000
[11:42:52.771]    __FlashLen=0x00000000
[11:42:52.772]    __FlashArg=0x00000000
[11:42:52.772]    __FlashOp=0x00000000
[11:42:52.773]    __Result=0x00000000
[11:42:52.773]    
[11:42:52.773]    // User-defined
[11:42:52.774]    DbgMCU_CR=0x00000007
[11:42:52.774]    DbgMCU_APB1_Fz=0x00000000
[11:42:52.774]    DbgMCU_APB2_Fz=0x00000000
[11:42:52.775]    DoOptionByteLoading=0x00000000
[11:42:52.775]  </debugvars>
[11:42:52.776]  
[11:42:52.776]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:42:52.777]    <block atomic="false" info="">
[11:42:52.777]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:42:52.777]        // -> [connectionFlash <= 0x00000001]
[11:42:52.778]      __var FLASH_BASE = 0x40022000 ;
[11:42:52.778]        // -> [FLASH_BASE <= 0x40022000]
[11:42:52.778]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:42:52.779]        // -> [FLASH_CR <= 0x40022004]
[11:42:52.779]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:42:52.779]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:42:52.780]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:42:52.780]        // -> [LOCK_BIT <= 0x00000001]
[11:42:52.780]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:42:52.780]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:42:52.780]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:42:52.781]        // -> [FLASH_KEYR <= 0x4002200C]
[11:42:52.781]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:42:52.781]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:42:52.781]      __var FLASH_KEY2 = 0x02030405 ;
[11:42:52.781]        // -> [FLASH_KEY2 <= 0x02030405]
[11:42:52.782]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:42:52.782]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:42:52.782]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:42:52.782]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:42:52.782]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:42:52.782]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:42:52.783]      __var FLASH_CR_Value = 0 ;
[11:42:52.783]        // -> [FLASH_CR_Value <= 0x00000000]
[11:42:52.783]      __var DoDebugPortStop = 1 ;
[11:42:52.783]        // -> [DoDebugPortStop <= 0x00000001]
[11:42:52.783]      __var DP_CTRL_STAT = 0x4 ;
[11:42:52.784]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:42:52.784]      __var DP_SELECT = 0x8 ;
[11:42:52.784]        // -> [DP_SELECT <= 0x00000008]
[11:42:52.785]    </block>
[11:42:52.785]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:42:52.785]      // if-block "connectionFlash && DoOptionByteLoading"
[11:42:52.785]        // =>  FALSE
[11:42:52.785]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:42:52.786]    </control>
[11:42:52.786]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:42:52.786]      // if-block "DoDebugPortStop"
[11:42:52.786]        // =>  TRUE
[11:42:52.786]      <block atomic="false" info="">
[11:42:52.787]        WriteDP(DP_SELECT, 0x00000000);
[11:42:52.787]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:42:52.787]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:42:52.788]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:42:52.788]      </block>
[11:42:52.788]      // end if-block "DoDebugPortStop"
[11:42:52.788]    </control>
[11:42:52.789]  </sequence>
[11:42:52.789]  
[11:46:13.423]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:46:13.423]  
[11:46:13.423]  <debugvars>
[11:46:13.423]    // Pre-defined
[11:46:13.423]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:46:13.424]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:46:13.424]    __dp=0x00000000
[11:46:13.425]    __ap=0x00000000
[11:46:13.425]    __traceout=0x00000000      (Trace Disabled)
[11:46:13.425]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:46:13.425]    __FlashAddr=0x00000000
[11:46:13.426]    __FlashLen=0x00000000
[11:46:13.426]    __FlashArg=0x00000000
[11:46:13.426]    __FlashOp=0x00000000
[11:46:13.426]    __Result=0x00000000
[11:46:13.427]    
[11:46:13.427]    // User-defined
[11:46:13.427]    DbgMCU_CR=0x00000007
[11:46:13.427]    DbgMCU_APB1_Fz=0x00000000
[11:46:13.427]    DbgMCU_APB2_Fz=0x00000000
[11:46:13.427]    DoOptionByteLoading=0x00000000
[11:46:13.428]  </debugvars>
[11:46:13.428]  
[11:46:13.428]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:46:13.428]    <block atomic="false" info="">
[11:46:13.428]      Sequence("CheckID");
[11:46:13.428]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:46:13.429]          <block atomic="false" info="">
[11:46:13.429]            __var pidr1 = 0;
[11:46:13.429]              // -> [pidr1 <= 0x00000000]
[11:46:13.429]            __var pidr2 = 0;
[11:46:13.429]              // -> [pidr2 <= 0x00000000]
[11:46:13.430]            __var jep106id = 0;
[11:46:13.430]              // -> [jep106id <= 0x00000000]
[11:46:13.430]            __var ROMTableBase = 0;
[11:46:13.430]              // -> [ROMTableBase <= 0x00000000]
[11:46:13.430]            __ap = 0;      // AHB-AP
[11:46:13.431]              // -> [__ap <= 0x00000000]
[11:46:13.431]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:46:13.431]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:46:13.431]              // -> [ROMTableBase <= 0xF0000000]
[11:46:13.432]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:46:13.432]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:46:13.433]              // -> [pidr1 <= 0x00000004]
[11:46:13.433]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:46:13.433]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:46:13.434]              // -> [pidr2 <= 0x0000000A]
[11:46:13.434]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:46:13.435]              // -> [jep106id <= 0x00000020]
[11:46:13.436]          </block>
[11:46:13.436]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:46:13.436]            // if-block "jep106id != 0x20"
[11:46:13.437]              // =>  FALSE
[11:46:13.437]            // skip if-block "jep106id != 0x20"
[11:46:13.437]          </control>
[11:46:13.438]        </sequence>
[11:46:13.438]    </block>
[11:46:13.438]  </sequence>
[11:46:13.439]  
[11:46:13.451]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:46:13.451]  
[11:46:13.451]  <debugvars>
[11:46:13.451]    // Pre-defined
[11:46:13.452]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:46:13.452]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:46:13.452]    __dp=0x00000000
[11:46:13.452]    __ap=0x00000000
[11:46:13.453]    __traceout=0x00000000      (Trace Disabled)
[11:46:13.453]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:46:13.453]    __FlashAddr=0x00000000
[11:46:13.453]    __FlashLen=0x00000000
[11:46:13.453]    __FlashArg=0x00000000
[11:46:13.454]    __FlashOp=0x00000000
[11:46:13.454]    __Result=0x00000000
[11:46:13.454]    
[11:46:13.454]    // User-defined
[11:46:13.454]    DbgMCU_CR=0x00000007
[11:46:13.454]    DbgMCU_APB1_Fz=0x00000000
[11:46:13.454]    DbgMCU_APB2_Fz=0x00000000
[11:46:13.455]    DoOptionByteLoading=0x00000000
[11:46:13.455]  </debugvars>
[11:46:13.456]  
[11:46:13.456]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:46:13.456]    <block atomic="false" info="">
[11:46:13.456]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:46:13.457]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:46:13.457]    </block>
[11:46:13.458]    <block atomic="false" info="DbgMCU registers">
[11:46:13.458]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:46:13.459]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:46:13.460]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:46:13.461]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:46:13.462]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:46:13.462]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:46:13.463]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:46:13.464]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:46:13.465]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:46:13.465]    </block>
[11:46:13.465]  </sequence>
[11:46:13.466]  
[11:46:20.834]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:46:20.834]  
[11:46:20.834]  <debugvars>
[11:46:20.834]    // Pre-defined
[11:46:20.837]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:46:20.837]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:46:20.837]    __dp=0x00000000
[11:46:20.837]    __ap=0x00000000
[11:46:20.837]    __traceout=0x00000000      (Trace Disabled)
[11:46:20.838]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:46:20.838]    __FlashAddr=0x00000000
[11:46:20.839]    __FlashLen=0x00000000
[11:46:20.839]    __FlashArg=0x00000000
[11:46:20.839]    __FlashOp=0x00000000
[11:46:20.840]    __Result=0x00000000
[11:46:20.840]    
[11:46:20.840]    // User-defined
[11:46:20.840]    DbgMCU_CR=0x00000007
[11:46:20.840]    DbgMCU_APB1_Fz=0x00000000
[11:46:20.840]    DbgMCU_APB2_Fz=0x00000000
[11:46:20.840]    DoOptionByteLoading=0x00000000
[11:46:20.840]  </debugvars>
[11:46:20.840]  
[11:46:20.840]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:46:20.840]    <block atomic="false" info="">
[11:46:20.842]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:46:20.842]        // -> [connectionFlash <= 0x00000001]
[11:46:20.842]      __var FLASH_BASE = 0x40022000 ;
[11:46:20.842]        // -> [FLASH_BASE <= 0x40022000]
[11:46:20.842]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:46:20.842]        // -> [FLASH_CR <= 0x40022004]
[11:46:20.842]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:46:20.842]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:46:20.842]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:46:20.842]        // -> [LOCK_BIT <= 0x00000001]
[11:46:20.844]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:46:20.844]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:46:20.844]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:46:20.844]        // -> [FLASH_KEYR <= 0x4002200C]
[11:46:20.844]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:46:20.844]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:46:20.844]      __var FLASH_KEY2 = 0x02030405 ;
[11:46:20.844]        // -> [FLASH_KEY2 <= 0x02030405]
[11:46:20.844]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:46:20.844]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:46:20.844]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:46:20.844]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:46:20.844]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:46:20.844]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:46:20.846]      __var FLASH_CR_Value = 0 ;
[11:46:20.846]        // -> [FLASH_CR_Value <= 0x00000000]
[11:46:20.846]      __var DoDebugPortStop = 1 ;
[11:46:20.846]        // -> [DoDebugPortStop <= 0x00000001]
[11:46:20.846]      __var DP_CTRL_STAT = 0x4 ;
[11:46:20.846]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:46:20.846]      __var DP_SELECT = 0x8 ;
[11:46:20.846]        // -> [DP_SELECT <= 0x00000008]
[11:46:20.846]    </block>
[11:46:20.846]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:46:20.846]      // if-block "connectionFlash && DoOptionByteLoading"
[11:46:20.848]        // =>  FALSE
[11:46:20.848]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:46:20.848]    </control>
[11:46:20.848]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:46:20.848]      // if-block "DoDebugPortStop"
[11:46:20.848]        // =>  TRUE
[11:46:20.848]      <block atomic="false" info="">
[11:46:20.848]        WriteDP(DP_SELECT, 0x00000000);
[11:46:20.861]  
[11:46:20.861]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:46:20.861]  
[11:46:20.862]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:46:20.863]      </block>
[11:46:20.863]      // end if-block "DoDebugPortStop"
[11:46:20.863]    </control>
[11:46:20.863]  </sequence>
[11:46:20.863]  
[11:46:40.366]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:46:40.366]  
[11:46:40.367]  <debugvars>
[11:46:40.367]    // Pre-defined
[11:46:40.367]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:46:40.368]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:46:40.368]    __dp=0x00000000
[11:46:40.368]    __ap=0x00000000
[11:46:40.369]    __traceout=0x00000000      (Trace Disabled)
[11:46:40.369]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:46:40.369]    __FlashAddr=0x00000000
[11:46:40.369]    __FlashLen=0x00000000
[11:46:40.370]    __FlashArg=0x00000000
[11:46:40.370]    __FlashOp=0x00000000
[11:46:40.370]    __Result=0x00000000
[11:46:40.371]    
[11:46:40.371]    // User-defined
[11:46:40.371]    DbgMCU_CR=0x00000007
[11:46:40.371]    DbgMCU_APB1_Fz=0x00000000
[11:46:40.371]    DbgMCU_APB2_Fz=0x00000000
[11:46:40.371]    DoOptionByteLoading=0x00000000
[11:46:40.371]  </debugvars>
[11:46:40.372]  
[11:46:40.372]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:46:40.372]    <block atomic="false" info="">
[11:46:40.372]      Sequence("CheckID");
[11:46:40.372]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:46:40.373]          <block atomic="false" info="">
[11:46:40.373]            __var pidr1 = 0;
[11:46:40.373]              // -> [pidr1 <= 0x00000000]
[11:46:40.373]            __var pidr2 = 0;
[11:46:40.374]              // -> [pidr2 <= 0x00000000]
[11:46:40.374]            __var jep106id = 0;
[11:46:40.374]              // -> [jep106id <= 0x00000000]
[11:46:40.374]            __var ROMTableBase = 0;
[11:46:40.376]              // -> [ROMTableBase <= 0x00000000]
[11:46:40.376]            __ap = 0;      // AHB-AP
[11:46:40.376]              // -> [__ap <= 0x00000000]
[11:46:40.376]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:46:40.377]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:46:40.378]              // -> [ROMTableBase <= 0xF0000000]
[11:46:40.378]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:46:40.379]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:46:40.380]              // -> [pidr1 <= 0x00000004]
[11:46:40.380]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:46:40.381]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:46:40.381]              // -> [pidr2 <= 0x0000000A]
[11:46:40.381]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:46:40.382]              // -> [jep106id <= 0x00000020]
[11:46:40.382]          </block>
[11:46:40.382]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:46:40.382]            // if-block "jep106id != 0x20"
[11:46:40.382]              // =>  FALSE
[11:46:40.383]            // skip if-block "jep106id != 0x20"
[11:46:40.383]          </control>
[11:46:40.383]        </sequence>
[11:46:40.383]    </block>
[11:46:40.383]  </sequence>
[11:46:40.384]  
[11:46:40.396]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:46:40.396]  
[11:46:40.397]  <debugvars>
[11:46:40.413]    // Pre-defined
[11:46:40.413]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:46:40.414]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:46:40.414]    __dp=0x00000000
[11:46:40.415]    __ap=0x00000000
[11:46:40.415]    __traceout=0x00000000      (Trace Disabled)
[11:46:40.416]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:46:40.416]    __FlashAddr=0x00000000
[11:46:40.417]    __FlashLen=0x00000000
[11:46:40.418]    __FlashArg=0x00000000
[11:46:40.418]    __FlashOp=0x00000000
[11:46:40.419]    __Result=0x00000000
[11:46:40.419]    
[11:46:40.419]    // User-defined
[11:46:40.420]    DbgMCU_CR=0x00000007
[11:46:40.420]    DbgMCU_APB1_Fz=0x00000000
[11:46:40.421]    DbgMCU_APB2_Fz=0x00000000
[11:46:40.421]    DoOptionByteLoading=0x00000000
[11:46:40.421]  </debugvars>
[11:46:40.421]  
[11:46:40.421]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:46:40.421]    <block atomic="false" info="">
[11:46:40.421]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:46:40.424]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:46:40.424]    </block>
[11:46:40.424]    <block atomic="false" info="DbgMCU registers">
[11:46:40.424]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:46:40.424]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:46:40.427]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:46:40.427]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:46:40.428]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:46:40.428]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:46:40.429]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:46:40.429]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:46:40.430]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:46:40.430]    </block>
[11:46:40.430]  </sequence>
[11:46:40.430]  
[11:46:47.799]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:46:47.799]  
[11:46:47.799]  <debugvars>
[11:46:47.799]    // Pre-defined
[11:46:47.799]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:46:47.799]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:46:47.799]    __dp=0x00000000
[11:46:47.799]    __ap=0x00000000
[11:46:47.799]    __traceout=0x00000000      (Trace Disabled)
[11:46:47.799]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:46:47.799]    __FlashAddr=0x00000000
[11:46:47.799]    __FlashLen=0x00000000
[11:46:47.799]    __FlashArg=0x00000000
[11:46:47.799]    __FlashOp=0x00000000
[11:46:47.799]    __Result=0x00000000
[11:46:47.799]    
[11:46:47.799]    // User-defined
[11:46:47.799]    DbgMCU_CR=0x00000007
[11:46:47.799]    DbgMCU_APB1_Fz=0x00000000
[11:46:47.799]    DbgMCU_APB2_Fz=0x00000000
[11:46:47.799]    DoOptionByteLoading=0x00000000
[11:46:47.799]  </debugvars>
[11:46:47.799]  
[11:46:47.799]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:46:47.799]    <block atomic="false" info="">
[11:46:47.799]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:46:47.799]        // -> [connectionFlash <= 0x00000001]
[11:46:47.799]      __var FLASH_BASE = 0x40022000 ;
[11:46:47.799]        // -> [FLASH_BASE <= 0x40022000]
[11:46:47.799]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:46:47.815]        // -> [FLASH_CR <= 0x40022004]
[11:46:47.815]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:46:47.815]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:46:47.815]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:46:47.815]        // -> [LOCK_BIT <= 0x00000001]
[11:46:47.815]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:46:47.815]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:46:47.815]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:46:47.815]        // -> [FLASH_KEYR <= 0x4002200C]
[11:46:47.815]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:46:47.815]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:46:47.815]      __var FLASH_KEY2 = 0x02030405 ;
[11:46:47.815]        // -> [FLASH_KEY2 <= 0x02030405]
[11:46:47.815]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:46:47.815]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:46:47.815]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:46:47.815]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:46:47.815]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:46:47.815]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:46:47.815]      __var FLASH_CR_Value = 0 ;
[11:46:47.815]        // -> [FLASH_CR_Value <= 0x00000000]
[11:46:47.815]      __var DoDebugPortStop = 1 ;
[11:46:47.815]        // -> [DoDebugPortStop <= 0x00000001]
[11:46:47.815]      __var DP_CTRL_STAT = 0x4 ;
[11:46:47.815]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:46:47.815]      __var DP_SELECT = 0x8 ;
[11:46:47.815]        // -> [DP_SELECT <= 0x00000008]
[11:46:47.815]    </block>
[11:46:47.815]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:46:47.815]      // if-block "connectionFlash && DoOptionByteLoading"
[11:46:47.815]        // =>  FALSE
[11:46:47.815]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:46:47.815]    </control>
[11:46:47.815]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:46:47.815]      // if-block "DoDebugPortStop"
[11:46:47.815]        // =>  TRUE
[11:46:47.815]      <block atomic="false" info="">
[11:46:47.815]        WriteDP(DP_SELECT, 0x00000000);
[11:46:47.815]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:46:47.815]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:46:47.815]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:46:47.815]      </block>
[11:46:47.815]      // end if-block "DoDebugPortStop"
[11:46:47.815]    </control>
[11:46:47.815]  </sequence>
[11:46:47.815]  
[11:50:06.869]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:50:06.869]  
[11:50:06.870]  <debugvars>
[11:50:06.870]    // Pre-defined
[11:50:06.870]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:50:06.871]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:50:06.871]    __dp=0x00000000
[11:50:06.871]    __ap=0x00000000
[11:50:06.872]    __traceout=0x00000000      (Trace Disabled)
[11:50:06.872]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:50:06.872]    __FlashAddr=0x00000000
[11:50:06.873]    __FlashLen=0x00000000
[11:50:06.873]    __FlashArg=0x00000000
[11:50:06.873]    __FlashOp=0x00000000
[11:50:06.874]    __Result=0x00000000
[11:50:06.874]    
[11:50:06.874]    // User-defined
[11:50:06.874]    DbgMCU_CR=0x00000007
[11:50:06.875]    DbgMCU_APB1_Fz=0x00000000
[11:50:06.875]    DbgMCU_APB2_Fz=0x00000000
[11:50:06.875]    DoOptionByteLoading=0x00000000
[11:50:06.875]  </debugvars>
[11:50:06.876]  
[11:50:06.876]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:50:06.876]    <block atomic="false" info="">
[11:50:06.876]      Sequence("CheckID");
[11:50:06.876]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:50:06.878]          <block atomic="false" info="">
[11:50:06.878]            __var pidr1 = 0;
[11:50:06.878]              // -> [pidr1 <= 0x00000000]
[11:50:06.878]            __var pidr2 = 0;
[11:50:06.879]              // -> [pidr2 <= 0x00000000]
[11:50:06.879]            __var jep106id = 0;
[11:50:06.879]              // -> [jep106id <= 0x00000000]
[11:50:06.879]            __var ROMTableBase = 0;
[11:50:06.879]              // -> [ROMTableBase <= 0x00000000]
[11:50:06.879]            __ap = 0;      // AHB-AP
[11:50:06.880]              // -> [__ap <= 0x00000000]
[11:50:06.880]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:50:06.880]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:50:06.881]              // -> [ROMTableBase <= 0xF0000000]
[11:50:06.881]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:50:06.882]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:50:06.883]              // -> [pidr1 <= 0x00000004]
[11:50:06.883]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:50:06.884]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:50:06.884]              // -> [pidr2 <= 0x0000000A]
[11:50:06.884]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:50:06.885]              // -> [jep106id <= 0x00000020]
[11:50:06.885]          </block>
[11:50:06.885]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:50:06.885]            // if-block "jep106id != 0x20"
[11:50:06.885]              // =>  FALSE
[11:50:06.886]            // skip if-block "jep106id != 0x20"
[11:50:06.886]          </control>
[11:50:06.886]        </sequence>
[11:50:06.886]    </block>
[11:50:06.886]  </sequence>
[11:50:06.887]  
[11:50:06.899]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:50:06.899]  
[11:50:06.930]  <debugvars>
[11:50:06.930]    // Pre-defined
[11:50:06.931]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:50:06.931]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:50:06.932]    __dp=0x00000000
[11:50:06.932]    __ap=0x00000000
[11:50:06.933]    __traceout=0x00000000      (Trace Disabled)
[11:50:06.934]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:50:06.934]    __FlashAddr=0x00000000
[11:50:06.935]    __FlashLen=0x00000000
[11:50:06.935]    __FlashArg=0x00000000
[11:50:06.936]    __FlashOp=0x00000000
[11:50:06.936]    __Result=0x00000000
[11:50:06.936]    
[11:50:06.936]    // User-defined
[11:50:06.937]    DbgMCU_CR=0x00000007
[11:50:06.938]    DbgMCU_APB1_Fz=0x00000000
[11:50:06.938]    DbgMCU_APB2_Fz=0x00000000
[11:50:06.939]    DoOptionByteLoading=0x00000000
[11:50:06.939]  </debugvars>
[11:50:06.940]  
[11:50:06.940]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:50:06.940]    <block atomic="false" info="">
[11:50:06.941]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:50:06.942]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:06.943]    </block>
[11:50:06.943]    <block atomic="false" info="DbgMCU registers">
[11:50:06.943]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:50:06.944]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:50:06.945]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:06.946]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:50:06.947]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:06.947]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:50:06.948]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:06.948]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:50:06.949]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:06.949]    </block>
[11:50:06.950]  </sequence>
[11:50:06.950]  
[11:50:10.589]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:50:10.589]  
[11:50:10.589]  <debugvars>
[11:50:10.589]    // Pre-defined
[11:50:10.589]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:50:10.589]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:50:10.589]    __dp=0x00000000
[11:50:10.589]    __ap=0x00000000
[11:50:10.589]    __traceout=0x00000000      (Trace Disabled)
[11:50:10.589]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:50:10.589]    __FlashAddr=0x00000000
[11:50:10.593]    __FlashLen=0x00000000
[11:50:10.593]    __FlashArg=0x00000000
[11:50:10.593]    __FlashOp=0x00000000
[11:50:10.593]    __Result=0x00000000
[11:50:10.595]    
[11:50:10.595]    // User-defined
[11:50:10.595]    DbgMCU_CR=0x00000007
[11:50:10.595]    DbgMCU_APB1_Fz=0x00000000
[11:50:10.595]    DbgMCU_APB2_Fz=0x00000000
[11:50:10.596]    DoOptionByteLoading=0x00000000
[11:50:10.596]  </debugvars>
[11:50:10.596]  
[11:50:10.596]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:50:10.596]    <block atomic="false" info="">
[11:50:10.597]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:50:10.597]        // -> [connectionFlash <= 0x00000001]
[11:50:10.597]      __var FLASH_BASE = 0x40022000 ;
[11:50:10.597]        // -> [FLASH_BASE <= 0x40022000]
[11:50:10.597]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:50:10.598]        // -> [FLASH_CR <= 0x40022004]
[11:50:10.598]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:50:10.598]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:50:10.598]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:50:10.598]        // -> [LOCK_BIT <= 0x00000001]
[11:50:10.598]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:50:10.599]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:50:10.599]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:50:10.599]        // -> [FLASH_KEYR <= 0x4002200C]
[11:50:10.599]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:50:10.599]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:50:10.600]      __var FLASH_KEY2 = 0x02030405 ;
[11:50:10.600]        // -> [FLASH_KEY2 <= 0x02030405]
[11:50:10.600]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:50:10.600]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:50:10.600]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:50:10.600]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:50:10.601]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:50:10.601]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:50:10.601]      __var FLASH_CR_Value = 0 ;
[11:50:10.601]        // -> [FLASH_CR_Value <= 0x00000000]
[11:50:10.601]      __var DoDebugPortStop = 1 ;
[11:50:10.602]        // -> [DoDebugPortStop <= 0x00000001]
[11:50:10.602]      __var DP_CTRL_STAT = 0x4 ;
[11:50:10.602]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:50:10.602]      __var DP_SELECT = 0x8 ;
[11:50:10.602]        // -> [DP_SELECT <= 0x00000008]
[11:50:10.603]    </block>
[11:50:10.603]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:50:10.603]      // if-block "connectionFlash && DoOptionByteLoading"
[11:50:10.603]        // =>  FALSE
[11:50:10.603]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:50:10.603]    </control>
[11:50:10.604]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:50:10.604]      // if-block "DoDebugPortStop"
[11:50:10.604]        // =>  TRUE
[11:50:10.604]      <block atomic="false" info="">
[11:50:10.604]        WriteDP(DP_SELECT, 0x00000000);
[11:50:10.615]  
[11:50:10.615]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:50:10.615]  
[11:50:10.637]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:50:10.638]      </block>
[11:50:10.638]      // end if-block "DoDebugPortStop"
[11:50:10.638]    </control>
[11:50:10.638]  </sequence>
[11:50:10.638]  
[11:50:17.969]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:50:17.969]  
[11:50:17.970]  <debugvars>
[11:50:17.970]    // Pre-defined
[11:50:17.970]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:50:17.971]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:50:17.971]    __dp=0x00000000
[11:50:17.971]    __ap=0x00000000
[11:50:17.971]    __traceout=0x00000000      (Trace Disabled)
[11:50:17.972]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:50:17.972]    __FlashAddr=0x00000000
[11:50:17.972]    __FlashLen=0x00000000
[11:50:17.973]    __FlashArg=0x00000000
[11:50:17.973]    __FlashOp=0x00000000
[11:50:17.973]    __Result=0x00000000
[11:50:17.973]    
[11:50:17.973]    // User-defined
[11:50:17.973]    DbgMCU_CR=0x00000007
[11:50:17.974]    DbgMCU_APB1_Fz=0x00000000
[11:50:17.974]    DbgMCU_APB2_Fz=0x00000000
[11:50:17.974]    DoOptionByteLoading=0x00000000
[11:50:17.974]  </debugvars>
[11:50:17.975]  
[11:50:17.975]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:50:17.975]    <block atomic="false" info="">
[11:50:17.975]      Sequence("CheckID");
[11:50:17.975]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:50:17.976]          <block atomic="false" info="">
[11:50:17.976]            __var pidr1 = 0;
[11:50:17.976]              // -> [pidr1 <= 0x00000000]
[11:50:17.976]            __var pidr2 = 0;
[11:50:17.976]              // -> [pidr2 <= 0x00000000]
[11:50:17.977]            __var jep106id = 0;
[11:50:17.977]              // -> [jep106id <= 0x00000000]
[11:50:17.977]            __var ROMTableBase = 0;
[11:50:17.977]              // -> [ROMTableBase <= 0x00000000]
[11:50:17.977]            __ap = 0;      // AHB-AP
[11:50:17.978]              // -> [__ap <= 0x00000000]
[11:50:17.978]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:50:17.978]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:50:17.979]              // -> [ROMTableBase <= 0xF0000000]
[11:50:17.979]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:50:17.980]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:50:17.981]              // -> [pidr1 <= 0x00000004]
[11:50:17.981]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:50:17.982]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:50:17.982]              // -> [pidr2 <= 0x0000000A]
[11:50:17.982]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:50:17.983]              // -> [jep106id <= 0x00000020]
[11:50:17.983]          </block>
[11:50:17.983]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:50:17.983]            // if-block "jep106id != 0x20"
[11:50:17.984]              // =>  FALSE
[11:50:17.984]            // skip if-block "jep106id != 0x20"
[11:50:17.984]          </control>
[11:50:17.985]        </sequence>
[11:50:17.985]    </block>
[11:50:17.985]  </sequence>
[11:50:17.985]  
[11:50:17.997]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:50:17.997]  
[11:50:17.997]  <debugvars>
[11:50:17.997]    // Pre-defined
[11:50:17.998]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:50:17.998]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:50:17.998]    __dp=0x00000000
[11:50:17.998]    __ap=0x00000000
[11:50:17.999]    __traceout=0x00000000      (Trace Disabled)
[11:50:17.999]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:50:17.999]    __FlashAddr=0x00000000
[11:50:17.999]    __FlashLen=0x00000000
[11:50:17.999]    __FlashArg=0x00000000
[11:50:17.999]    __FlashOp=0x00000000
[11:50:17.999]    __Result=0x00000000
[11:50:17.999]    
[11:50:17.999]    // User-defined
[11:50:17.999]    DbgMCU_CR=0x00000007
[11:50:17.999]    DbgMCU_APB1_Fz=0x00000000
[11:50:18.001]    DbgMCU_APB2_Fz=0x00000000
[11:50:18.001]    DoOptionByteLoading=0x00000000
[11:50:18.001]  </debugvars>
[11:50:18.001]  
[11:50:18.002]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:50:18.002]    <block atomic="false" info="">
[11:50:18.002]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:50:18.003]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:18.003]    </block>
[11:50:18.004]    <block atomic="false" info="DbgMCU registers">
[11:50:18.004]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:50:18.005]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:50:18.006]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:18.006]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:50:18.007]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:18.007]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:50:18.008]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:18.009]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:50:18.010]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:18.010]    </block>
[11:50:18.010]  </sequence>
[11:50:18.010]  
[11:50:20.103]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:50:20.103]  
[11:50:20.103]  <debugvars>
[11:50:20.103]    // Pre-defined
[11:50:20.103]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:50:20.103]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:50:20.103]    __dp=0x00000000
[11:50:20.103]    __ap=0x00000000
[11:50:20.103]    __traceout=0x00000000      (Trace Disabled)
[11:50:20.103]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:50:20.103]    __FlashAddr=0x00000000
[11:50:20.103]    __FlashLen=0x00000000
[11:50:20.103]    __FlashArg=0x00000000
[11:50:20.103]    __FlashOp=0x00000000
[11:50:20.103]    __Result=0x00000000
[11:50:20.103]    
[11:50:20.103]    // User-defined
[11:50:20.103]    DbgMCU_CR=0x00000007
[11:50:20.103]    DbgMCU_APB1_Fz=0x00000000
[11:50:20.103]    DbgMCU_APB2_Fz=0x00000000
[11:50:20.103]    DoOptionByteLoading=0x00000000
[11:50:20.103]  </debugvars>
[11:50:20.103]  
[11:50:20.103]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:50:20.103]    <block atomic="false" info="">
[11:50:20.103]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:50:20.103]        // -> [connectionFlash <= 0x00000001]
[11:50:20.111]      __var FLASH_BASE = 0x40022000 ;
[11:50:20.111]        // -> [FLASH_BASE <= 0x40022000]
[11:50:20.111]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:50:20.111]        // -> [FLASH_CR <= 0x40022004]
[11:50:20.111]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:50:20.111]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:50:20.112]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:50:20.112]        // -> [LOCK_BIT <= 0x00000001]
[11:50:20.112]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:50:20.112]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:50:20.112]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:50:20.112]        // -> [FLASH_KEYR <= 0x4002200C]
[11:50:20.112]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:50:20.112]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:50:20.114]      __var FLASH_KEY2 = 0x02030405 ;
[11:50:20.114]        // -> [FLASH_KEY2 <= 0x02030405]
[11:50:20.114]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:50:20.114]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:50:20.114]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:50:20.114]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:50:20.114]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:50:20.114]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:50:20.114]      __var FLASH_CR_Value = 0 ;
[11:50:20.114]        // -> [FLASH_CR_Value <= 0x00000000]
[11:50:20.114]      __var DoDebugPortStop = 1 ;
[11:50:20.114]        // -> [DoDebugPortStop <= 0x00000001]
[11:50:20.114]      __var DP_CTRL_STAT = 0x4 ;
[11:50:20.114]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:50:20.114]      __var DP_SELECT = 0x8 ;
[11:50:20.114]        // -> [DP_SELECT <= 0x00000008]
[11:50:20.114]    </block>
[11:50:20.114]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:50:20.114]      // if-block "connectionFlash && DoOptionByteLoading"
[11:50:20.114]        // =>  FALSE
[11:50:20.114]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:50:20.114]    </control>
[11:50:20.114]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:50:20.114]      // if-block "DoDebugPortStop"
[11:50:20.114]        // =>  TRUE
[11:50:20.119]      <block atomic="false" info="">
[11:50:20.119]        WriteDP(DP_SELECT, 0x00000000);
[11:50:20.119]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:50:20.120]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:50:20.120]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:50:20.120]      </block>
[11:50:20.122]      // end if-block "DoDebugPortStop"
[11:50:20.122]    </control>
[11:50:20.123]  </sequence>
[11:50:20.123]  
[11:50:21.644]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:50:21.644]  
[11:50:21.645]  <debugvars>
[11:50:21.645]    // Pre-defined
[11:50:21.645]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:50:21.646]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:50:21.646]    __dp=0x00000000
[11:50:21.646]    __ap=0x00000000
[11:50:21.646]    __traceout=0x00000000      (Trace Disabled)
[11:50:21.647]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:50:21.647]    __FlashAddr=0x00000000
[11:50:21.647]    __FlashLen=0x00000000
[11:50:21.648]    __FlashArg=0x00000000
[11:50:21.648]    __FlashOp=0x00000000
[11:50:21.648]    __Result=0x00000000
[11:50:21.648]    
[11:50:21.648]    // User-defined
[11:50:21.649]    DbgMCU_CR=0x00000007
[11:50:21.649]    DbgMCU_APB1_Fz=0x00000000
[11:50:21.649]    DbgMCU_APB2_Fz=0x00000000
[11:50:21.650]    DoOptionByteLoading=0x00000000
[11:50:21.650]  </debugvars>
[11:50:21.650]  
[11:50:21.650]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:50:21.650]    <block atomic="false" info="">
[11:50:21.650]      Sequence("CheckID");
[11:50:21.650]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:50:21.650]          <block atomic="false" info="">
[11:50:21.650]            __var pidr1 = 0;
[11:50:21.650]              // -> [pidr1 <= 0x00000000]
[11:50:21.650]            __var pidr2 = 0;
[11:50:21.650]              // -> [pidr2 <= 0x00000000]
[11:50:21.650]            __var jep106id = 0;
[11:50:21.650]              // -> [jep106id <= 0x00000000]
[11:50:21.650]            __var ROMTableBase = 0;
[11:50:21.650]              // -> [ROMTableBase <= 0x00000000]
[11:50:21.650]            __ap = 0;      // AHB-AP
[11:50:21.650]              // -> [__ap <= 0x00000000]
[11:50:21.650]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:50:21.650]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:50:21.650]              // -> [ROMTableBase <= 0xF0000000]
[11:50:21.650]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:50:21.650]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:50:21.650]              // -> [pidr1 <= 0x00000004]
[11:50:21.650]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:50:21.650]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:50:21.650]              // -> [pidr2 <= 0x0000000A]
[11:50:21.650]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:50:21.650]              // -> [jep106id <= 0x00000020]
[11:50:21.650]          </block>
[11:50:21.650]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:50:21.650]            // if-block "jep106id != 0x20"
[11:50:21.650]              // =>  FALSE
[11:50:21.650]            // skip if-block "jep106id != 0x20"
[11:50:21.650]          </control>
[11:50:21.650]        </sequence>
[11:50:21.650]    </block>
[11:50:21.650]  </sequence>
[11:50:21.650]  
[11:50:21.672]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:50:21.672]  
[11:50:21.672]  <debugvars>
[11:50:21.673]    // Pre-defined
[11:50:21.673]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:50:21.673]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:50:21.674]    __dp=0x00000000
[11:50:21.674]    __ap=0x00000000
[11:50:21.674]    __traceout=0x00000000      (Trace Disabled)
[11:50:21.675]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:50:21.675]    __FlashAddr=0x00000000
[11:50:21.675]    __FlashLen=0x00000000
[11:50:21.676]    __FlashArg=0x00000000
[11:50:21.676]    __FlashOp=0x00000000
[11:50:21.676]    __Result=0x00000000
[11:50:21.676]    
[11:50:21.676]    // User-defined
[11:50:21.677]    DbgMCU_CR=0x00000007
[11:50:21.677]    DbgMCU_APB1_Fz=0x00000000
[11:50:21.677]    DbgMCU_APB2_Fz=0x00000000
[11:50:21.677]    DoOptionByteLoading=0x00000000
[11:50:21.678]  </debugvars>
[11:50:21.678]  
[11:50:21.678]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:50:21.678]    <block atomic="false" info="">
[11:50:21.678]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:50:21.679]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:21.679]    </block>
[11:50:21.680]    <block atomic="false" info="DbgMCU registers">
[11:50:21.680]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:50:21.680]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:50:21.680]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:21.682]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:50:21.682]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:21.683]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:50:21.683]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:21.684]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:50:21.684]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:21.685]    </block>
[11:50:21.685]  </sequence>
[11:50:21.685]  
[11:50:25.026]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:50:25.026]  
[11:50:25.026]  <debugvars>
[11:50:25.026]    // Pre-defined
[11:50:25.026]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:50:25.026]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:50:25.035]    __dp=0x00000000
[11:50:25.036]    __ap=0x00000000
[11:50:25.036]    __traceout=0x00000000      (Trace Disabled)
[11:50:25.036]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:50:25.037]    __FlashAddr=0x00000000
[11:50:25.037]    __FlashLen=0x00000000
[11:50:25.037]    __FlashArg=0x00000000
[11:50:25.037]    __FlashOp=0x00000000
[11:50:25.037]    __Result=0x00000000
[11:50:25.037]    
[11:50:25.037]    // User-defined
[11:50:25.038]    DbgMCU_CR=0x00000007
[11:50:25.038]    DbgMCU_APB1_Fz=0x00000000
[11:50:25.039]    DbgMCU_APB2_Fz=0x00000000
[11:50:25.039]    DoOptionByteLoading=0x00000000
[11:50:25.040]  </debugvars>
[11:50:25.040]  
[11:50:25.040]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:50:25.040]    <block atomic="false" info="">
[11:50:25.040]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:50:25.041]        // -> [connectionFlash <= 0x00000001]
[11:50:25.041]      __var FLASH_BASE = 0x40022000 ;
[11:50:25.041]        // -> [FLASH_BASE <= 0x40022000]
[11:50:25.041]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:50:25.041]        // -> [FLASH_CR <= 0x40022004]
[11:50:25.041]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:50:25.041]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:50:25.041]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:50:25.043]        // -> [LOCK_BIT <= 0x00000001]
[11:50:25.043]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:50:25.044]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:50:25.044]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:50:25.044]        // -> [FLASH_KEYR <= 0x4002200C]
[11:50:25.044]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:50:25.044]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:50:25.045]      __var FLASH_KEY2 = 0x02030405 ;
[11:50:25.045]        // -> [FLASH_KEY2 <= 0x02030405]
[11:50:25.045]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:50:25.045]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:50:25.045]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:50:25.045]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:50:25.046]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:50:25.046]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:50:25.046]      __var FLASH_CR_Value = 0 ;
[11:50:25.046]        // -> [FLASH_CR_Value <= 0x00000000]
[11:50:25.046]      __var DoDebugPortStop = 1 ;
[11:50:25.047]        // -> [DoDebugPortStop <= 0x00000001]
[11:50:25.047]      __var DP_CTRL_STAT = 0x4 ;
[11:50:25.047]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:50:25.047]      __var DP_SELECT = 0x8 ;
[11:50:25.047]        // -> [DP_SELECT <= 0x00000008]
[11:50:25.048]    </block>
[11:50:25.048]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:50:25.048]      // if-block "connectionFlash && DoOptionByteLoading"
[11:50:25.048]        // =>  FALSE
[11:50:25.048]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:50:25.048]    </control>
[11:50:25.049]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:50:25.049]      // if-block "DoDebugPortStop"
[11:50:25.049]        // =>  TRUE
[11:50:25.049]      <block atomic="false" info="">
[11:50:25.049]        WriteDP(DP_SELECT, 0x00000000);
[11:50:25.056]  
[11:50:25.056]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:50:25.056]  
[11:50:25.062]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:50:25.064]      </block>
[11:50:25.065]      // end if-block "DoDebugPortStop"
[11:50:25.065]    </control>
[11:50:25.066]  </sequence>
[11:50:25.066]  
[11:50:26.976]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:50:26.976]  
[11:50:26.976]  <debugvars>
[11:50:26.977]    // Pre-defined
[11:50:26.977]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:50:26.977]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:50:26.977]    __dp=0x00000000
[11:50:26.977]    __ap=0x00000000
[11:50:26.977]    __traceout=0x00000000      (Trace Disabled)
[11:50:26.979]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:50:26.980]    __FlashAddr=0x00000000
[11:50:26.980]    __FlashLen=0x00000000
[11:50:26.980]    __FlashArg=0x00000000
[11:50:26.980]    __FlashOp=0x00000000
[11:50:26.981]    __Result=0x00000000
[11:50:26.981]    
[11:50:26.981]    // User-defined
[11:50:26.981]    DbgMCU_CR=0x00000007
[11:50:26.981]    DbgMCU_APB1_Fz=0x00000000
[11:50:26.981]    DbgMCU_APB2_Fz=0x00000000
[11:50:26.982]    DoOptionByteLoading=0x00000000
[11:50:26.982]  </debugvars>
[11:50:26.982]  
[11:50:26.982]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:50:26.982]    <block atomic="false" info="">
[11:50:26.982]      Sequence("CheckID");
[11:50:26.983]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:50:26.983]          <block atomic="false" info="">
[11:50:26.983]            __var pidr1 = 0;
[11:50:26.983]              // -> [pidr1 <= 0x00000000]
[11:50:26.983]            __var pidr2 = 0;
[11:50:26.984]              // -> [pidr2 <= 0x00000000]
[11:50:26.984]            __var jep106id = 0;
[11:50:26.984]              // -> [jep106id <= 0x00000000]
[11:50:26.984]            __var ROMTableBase = 0;
[11:50:26.984]              // -> [ROMTableBase <= 0x00000000]
[11:50:26.985]            __ap = 0;      // AHB-AP
[11:50:26.985]              // -> [__ap <= 0x00000000]
[11:50:26.985]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:50:26.986]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:50:26.986]              // -> [ROMTableBase <= 0xF0000000]
[11:50:26.986]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:50:26.988]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:50:26.988]              // -> [pidr1 <= 0x00000004]
[11:50:26.988]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:50:26.989]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:50:26.990]              // -> [pidr2 <= 0x0000000A]
[11:50:26.990]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:50:26.990]              // -> [jep106id <= 0x00000020]
[11:50:26.990]          </block>
[11:50:26.991]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:50:26.991]            // if-block "jep106id != 0x20"
[11:50:26.991]              // =>  FALSE
[11:50:26.992]            // skip if-block "jep106id != 0x20"
[11:50:26.992]          </control>
[11:50:26.992]        </sequence>
[11:50:26.992]    </block>
[11:50:26.992]  </sequence>
[11:50:26.993]  
[11:50:27.007]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:50:27.007]  
[11:50:27.018]  <debugvars>
[11:50:27.022]    // Pre-defined
[11:50:27.022]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:50:27.037]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:50:27.038]    __dp=0x00000000
[11:50:27.038]    __ap=0x00000000
[11:50:27.038]    __traceout=0x00000000      (Trace Disabled)
[11:50:27.038]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:50:27.039]    __FlashAddr=0x00000000
[11:50:27.039]    __FlashLen=0x00000000
[11:50:27.040]    __FlashArg=0x00000000
[11:50:27.040]    __FlashOp=0x00000000
[11:50:27.040]    __Result=0x00000000
[11:50:27.040]    
[11:50:27.040]    // User-defined
[11:50:27.041]    DbgMCU_CR=0x00000007
[11:50:27.041]    DbgMCU_APB1_Fz=0x00000000
[11:50:27.041]    DbgMCU_APB2_Fz=0x00000000
[11:50:27.041]    DoOptionByteLoading=0x00000000
[11:50:27.042]  </debugvars>
[11:50:27.042]  
[11:50:27.042]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:50:27.042]    <block atomic="false" info="">
[11:50:27.042]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:50:27.043]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:27.044]    </block>
[11:50:27.044]    <block atomic="false" info="DbgMCU registers">
[11:50:27.044]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:50:27.045]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:50:27.046]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:27.047]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:50:27.047]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:27.048]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:50:27.049]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:27.049]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:50:27.050]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:27.051]    </block>
[11:50:27.051]  </sequence>
[11:50:27.051]  
[11:50:35.124]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:50:35.124]  
[11:50:35.124]  <debugvars>
[11:50:35.124]    // Pre-defined
[11:50:35.124]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:50:35.124]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:50:35.124]    __dp=0x00000000
[11:50:35.124]    __ap=0x00000000
[11:50:35.124]    __traceout=0x00000000      (Trace Disabled)
[11:50:35.124]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:50:35.124]    __FlashAddr=0x00000000
[11:50:35.124]    __FlashLen=0x00000000
[11:50:35.124]    __FlashArg=0x00000000
[11:50:35.124]    __FlashOp=0x00000000
[11:50:35.124]    __Result=0x00000000
[11:50:35.124]    
[11:50:35.124]    // User-defined
[11:50:35.124]    DbgMCU_CR=0x00000007
[11:50:35.124]    DbgMCU_APB1_Fz=0x00000000
[11:50:35.124]    DbgMCU_APB2_Fz=0x00000000
[11:50:35.124]    DoOptionByteLoading=0x00000000
[11:50:35.124]  </debugvars>
[11:50:35.124]  
[11:50:35.124]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:50:35.124]    <block atomic="false" info="">
[11:50:35.124]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:50:35.124]        // -> [connectionFlash <= 0x00000001]
[11:50:35.124]      __var FLASH_BASE = 0x40022000 ;
[11:50:35.124]        // -> [FLASH_BASE <= 0x40022000]
[11:50:35.124]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:50:35.124]        // -> [FLASH_CR <= 0x40022004]
[11:50:35.124]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:50:35.124]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:50:35.124]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:50:35.124]        // -> [LOCK_BIT <= 0x00000001]
[11:50:35.124]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:50:35.124]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:50:35.134]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:50:35.134]        // -> [FLASH_KEYR <= 0x4002200C]
[11:50:35.134]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:50:35.134]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:50:35.134]      __var FLASH_KEY2 = 0x02030405 ;
[11:50:35.134]        // -> [FLASH_KEY2 <= 0x02030405]
[11:50:35.134]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:50:35.134]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:50:35.134]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:50:35.134]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:50:35.137]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:50:35.137]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:50:35.137]      __var FLASH_CR_Value = 0 ;
[11:50:35.137]        // -> [FLASH_CR_Value <= 0x00000000]
[11:50:35.137]      __var DoDebugPortStop = 1 ;
[11:50:35.138]        // -> [DoDebugPortStop <= 0x00000001]
[11:50:35.138]      __var DP_CTRL_STAT = 0x4 ;
[11:50:35.138]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:50:35.138]      __var DP_SELECT = 0x8 ;
[11:50:35.139]        // -> [DP_SELECT <= 0x00000008]
[11:50:35.139]    </block>
[11:50:35.139]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:50:35.139]      // if-block "connectionFlash && DoOptionByteLoading"
[11:50:35.139]        // =>  FALSE
[11:50:35.140]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:50:35.140]    </control>
[11:50:35.140]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:50:35.140]      // if-block "DoDebugPortStop"
[11:50:35.140]        // =>  TRUE
[11:50:35.140]      <block atomic="false" info="">
[11:50:35.140]        WriteDP(DP_SELECT, 0x00000000);
[11:50:35.140]  
[11:50:35.140]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:50:35.140]  
[11:50:35.178]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:50:35.180]      </block>
[11:50:35.181]      // end if-block "DoDebugPortStop"
[11:50:35.181]    </control>
[11:50:35.182]  </sequence>
[11:50:35.182]  
[11:50:37.051]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:50:37.051]  
[11:50:37.051]  <debugvars>
[11:50:37.051]    // Pre-defined
[11:50:37.051]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:50:37.051]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:50:37.051]    __dp=0x00000000
[11:50:37.053]    __ap=0x00000000
[11:50:37.053]    __traceout=0x00000000      (Trace Disabled)
[11:50:37.053]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:50:37.054]    __FlashAddr=0x00000000
[11:50:37.054]    __FlashLen=0x00000000
[11:50:37.054]    __FlashArg=0x00000000
[11:50:37.054]    __FlashOp=0x00000000
[11:50:37.054]    __Result=0x00000000
[11:50:37.055]    
[11:50:37.055]    // User-defined
[11:50:37.055]    DbgMCU_CR=0x00000007
[11:50:37.055]    DbgMCU_APB1_Fz=0x00000000
[11:50:37.055]    DbgMCU_APB2_Fz=0x00000000
[11:50:37.055]    DoOptionByteLoading=0x00000000
[11:50:37.055]  </debugvars>
[11:50:37.056]  
[11:50:37.056]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:50:37.056]    <block atomic="false" info="">
[11:50:37.056]      Sequence("CheckID");
[11:50:37.056]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:50:37.057]          <block atomic="false" info="">
[11:50:37.057]            __var pidr1 = 0;
[11:50:37.057]              // -> [pidr1 <= 0x00000000]
[11:50:37.058]            __var pidr2 = 0;
[11:50:37.058]              // -> [pidr2 <= 0x00000000]
[11:50:37.058]            __var jep106id = 0;
[11:50:37.059]              // -> [jep106id <= 0x00000000]
[11:50:37.059]            __var ROMTableBase = 0;
[11:50:37.059]              // -> [ROMTableBase <= 0x00000000]
[11:50:37.060]            __ap = 0;      // AHB-AP
[11:50:37.060]              // -> [__ap <= 0x00000000]
[11:50:37.060]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:50:37.061]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:50:37.061]              // -> [ROMTableBase <= 0xF0000000]
[11:50:37.061]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:50:37.062]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:50:37.064]              // -> [pidr1 <= 0x00000004]
[11:50:37.064]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:50:37.065]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:50:37.065]              // -> [pidr2 <= 0x0000000A]
[11:50:37.065]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:50:37.066]              // -> [jep106id <= 0x00000020]
[11:50:37.066]          </block>
[11:50:37.066]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:50:37.066]            // if-block "jep106id != 0x20"
[11:50:37.066]              // =>  FALSE
[11:50:37.067]            // skip if-block "jep106id != 0x20"
[11:50:37.067]          </control>
[11:50:37.067]        </sequence>
[11:50:37.067]    </block>
[11:50:37.067]  </sequence>
[11:50:37.067]  
[11:50:37.079]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:50:37.079]  
[11:50:37.080]  <debugvars>
[11:50:37.080]    // Pre-defined
[11:50:37.080]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:50:37.081]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:50:37.081]    __dp=0x00000000
[11:50:37.081]    __ap=0x00000000
[11:50:37.081]    __traceout=0x00000000      (Trace Disabled)
[11:50:37.082]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:50:37.082]    __FlashAddr=0x00000000
[11:50:37.082]    __FlashLen=0x00000000
[11:50:37.082]    __FlashArg=0x00000000
[11:50:37.083]    __FlashOp=0x00000000
[11:50:37.083]    __Result=0x00000000
[11:50:37.083]    
[11:50:37.083]    // User-defined
[11:50:37.084]    DbgMCU_CR=0x00000007
[11:50:37.084]    DbgMCU_APB1_Fz=0x00000000
[11:50:37.084]    DbgMCU_APB2_Fz=0x00000000
[11:50:37.085]    DoOptionByteLoading=0x00000000
[11:50:37.085]  </debugvars>
[11:50:37.085]  
[11:50:37.085]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:50:37.085]    <block atomic="false" info="">
[11:50:37.086]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:50:37.086]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:37.087]    </block>
[11:50:37.087]    <block atomic="false" info="DbgMCU registers">
[11:50:37.087]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:50:37.088]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:50:37.089]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:37.089]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:50:37.090]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:37.090]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:50:37.091]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:37.091]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:50:37.092]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:37.092]    </block>
[11:50:37.093]  </sequence>
[11:50:37.093]  
[11:50:41.127]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:50:41.127]  
[11:50:41.129]  <debugvars>
[11:50:41.129]    // Pre-defined
[11:50:41.129]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:50:41.129]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:50:41.129]    __dp=0x00000000
[11:50:41.129]    __ap=0x00000000
[11:50:41.129]    __traceout=0x00000000      (Trace Disabled)
[11:50:41.131]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:50:41.131]    __FlashAddr=0x00000000
[11:50:41.131]    __FlashLen=0x00000000
[11:50:41.131]    __FlashArg=0x00000000
[11:50:41.131]    __FlashOp=0x00000000
[11:50:41.131]    __Result=0x00000000
[11:50:41.131]    
[11:50:41.131]    // User-defined
[11:50:41.133]    DbgMCU_CR=0x00000007
[11:50:41.133]    DbgMCU_APB1_Fz=0x00000000
[11:50:41.133]    DbgMCU_APB2_Fz=0x00000000
[11:50:41.133]    DoOptionByteLoading=0x00000000
[11:50:41.133]  </debugvars>
[11:50:41.133]  
[11:50:41.133]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:50:41.133]    <block atomic="false" info="">
[11:50:41.133]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:50:41.133]        // -> [connectionFlash <= 0x00000001]
[11:50:41.133]      __var FLASH_BASE = 0x40022000 ;
[11:50:41.133]        // -> [FLASH_BASE <= 0x40022000]
[11:50:41.133]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:50:41.133]        // -> [FLASH_CR <= 0x40022004]
[11:50:41.136]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:50:41.136]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:50:41.137]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:50:41.137]        // -> [LOCK_BIT <= 0x00000001]
[11:50:41.137]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:50:41.137]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:50:41.137]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:50:41.137]        // -> [FLASH_KEYR <= 0x4002200C]
[11:50:41.138]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:50:41.138]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:50:41.139]      __var FLASH_KEY2 = 0x02030405 ;
[11:50:41.139]        // -> [FLASH_KEY2 <= 0x02030405]
[11:50:41.139]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:50:41.139]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:50:41.139]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:50:41.140]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:50:41.140]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:50:41.140]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:50:41.140]      __var FLASH_CR_Value = 0 ;
[11:50:41.140]        // -> [FLASH_CR_Value <= 0x00000000]
[11:50:41.140]      __var DoDebugPortStop = 1 ;
[11:50:41.140]        // -> [DoDebugPortStop <= 0x00000001]
[11:50:41.140]      __var DP_CTRL_STAT = 0x4 ;
[11:50:41.140]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:50:41.140]      __var DP_SELECT = 0x8 ;
[11:50:41.143]        // -> [DP_SELECT <= 0x00000008]
[11:50:41.143]    </block>
[11:50:41.143]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:50:41.144]      // if-block "connectionFlash && DoOptionByteLoading"
[11:50:41.144]        // =>  FALSE
[11:50:41.144]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:50:41.144]    </control>
[11:50:41.145]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:50:41.145]      // if-block "DoDebugPortStop"
[11:50:41.145]        // =>  TRUE
[11:50:41.145]      <block atomic="false" info="">
[11:50:41.145]        WriteDP(DP_SELECT, 0x00000000);
[11:50:41.155]  
[11:50:41.155]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:50:41.155]  
[11:50:41.157]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:50:41.157]      </block>
[11:50:41.157]      // end if-block "DoDebugPortStop"
[11:50:41.157]    </control>
[11:50:41.157]  </sequence>
[11:50:41.160]  
[11:50:42.645]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:50:42.645]  
[11:50:42.646]  <debugvars>
[11:50:42.646]    // Pre-defined
[11:50:42.646]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:50:42.647]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:50:42.647]    __dp=0x00000000
[11:50:42.647]    __ap=0x00000000
[11:50:42.648]    __traceout=0x00000000      (Trace Disabled)
[11:50:42.648]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:50:42.648]    __FlashAddr=0x00000000
[11:50:42.648]    __FlashLen=0x00000000
[11:50:42.648]    __FlashArg=0x00000000
[11:50:42.648]    __FlashOp=0x00000000
[11:50:42.648]    __Result=0x00000000
[11:50:42.648]    
[11:50:42.648]    // User-defined
[11:50:42.648]    DbgMCU_CR=0x00000007
[11:50:42.648]    DbgMCU_APB1_Fz=0x00000000
[11:50:42.648]    DbgMCU_APB2_Fz=0x00000000
[11:50:42.648]    DoOptionByteLoading=0x00000000
[11:50:42.648]  </debugvars>
[11:50:42.648]  
[11:50:42.648]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:50:42.648]    <block atomic="false" info="">
[11:50:42.648]      Sequence("CheckID");
[11:50:42.648]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:50:42.648]          <block atomic="false" info="">
[11:50:42.648]            __var pidr1 = 0;
[11:50:42.648]              // -> [pidr1 <= 0x00000000]
[11:50:42.648]            __var pidr2 = 0;
[11:50:42.648]              // -> [pidr2 <= 0x00000000]
[11:50:42.648]            __var jep106id = 0;
[11:50:42.648]              // -> [jep106id <= 0x00000000]
[11:50:42.648]            __var ROMTableBase = 0;
[11:50:42.653]              // -> [ROMTableBase <= 0x00000000]
[11:50:42.653]            __ap = 0;      // AHB-AP
[11:50:42.654]              // -> [__ap <= 0x00000000]
[11:50:42.654]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:50:42.655]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:50:42.655]              // -> [ROMTableBase <= 0xF0000000]
[11:50:42.655]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:50:42.656]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:50:42.657]              // -> [pidr1 <= 0x00000004]
[11:50:42.657]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:50:42.658]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:50:42.658]              // -> [pidr2 <= 0x0000000A]
[11:50:42.659]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:50:42.659]              // -> [jep106id <= 0x00000020]
[11:50:42.659]          </block>
[11:50:42.660]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:50:42.660]            // if-block "jep106id != 0x20"
[11:50:42.660]              // =>  FALSE
[11:50:42.660]            // skip if-block "jep106id != 0x20"
[11:50:42.660]          </control>
[11:50:42.660]        </sequence>
[11:50:42.661]    </block>
[11:50:42.661]  </sequence>
[11:50:42.661]  
[11:50:42.673]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:50:42.673]  
[11:50:42.673]  <debugvars>
[11:50:42.673]    // Pre-defined
[11:50:42.674]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:50:42.674]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:50:42.674]    __dp=0x00000000
[11:50:42.675]    __ap=0x00000000
[11:50:42.675]    __traceout=0x00000000      (Trace Disabled)
[11:50:42.675]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:50:42.675]    __FlashAddr=0x00000000
[11:50:42.675]    __FlashLen=0x00000000
[11:50:42.676]    __FlashArg=0x00000000
[11:50:42.676]    __FlashOp=0x00000000
[11:50:42.676]    __Result=0x00000000
[11:50:42.676]    
[11:50:42.676]    // User-defined
[11:50:42.676]    DbgMCU_CR=0x00000007
[11:50:42.676]    DbgMCU_APB1_Fz=0x00000000
[11:50:42.676]    DbgMCU_APB2_Fz=0x00000000
[11:50:42.676]    DoOptionByteLoading=0x00000000
[11:50:42.676]  </debugvars>
[11:50:42.676]  
[11:50:42.676]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:50:42.678]    <block atomic="false" info="">
[11:50:42.678]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:50:42.679]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:42.679]    </block>
[11:50:42.679]    <block atomic="false" info="DbgMCU registers">
[11:50:42.679]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:50:42.680]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:50:42.681]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:42.681]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:50:42.682]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:42.682]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:50:42.683]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:42.683]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:50:42.684]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:50:42.685]    </block>
[11:50:42.685]  </sequence>
[11:50:42.686]  
[11:50:50.007]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:50:50.007]  
[11:50:50.007]  <debugvars>
[11:50:50.009]    // Pre-defined
[11:50:50.009]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:50:50.009]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:50:50.009]    __dp=0x00000000
[11:50:50.010]    __ap=0x00000000
[11:50:50.010]    __traceout=0x00000000      (Trace Disabled)
[11:50:50.010]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:50:50.011]    __FlashAddr=0x00000000
[11:50:50.011]    __FlashLen=0x00000000
[11:50:50.011]    __FlashArg=0x00000000
[11:50:50.011]    __FlashOp=0x00000000
[11:50:50.012]    __Result=0x00000000
[11:50:50.012]    
[11:50:50.012]    // User-defined
[11:50:50.012]    DbgMCU_CR=0x00000007
[11:50:50.012]    DbgMCU_APB1_Fz=0x00000000
[11:50:50.013]    DbgMCU_APB2_Fz=0x00000000
[11:50:50.013]    DoOptionByteLoading=0x00000000
[11:50:50.013]  </debugvars>
[11:50:50.013]  
[11:50:50.014]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:50:50.014]    <block atomic="false" info="">
[11:50:50.014]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:50:50.014]        // -> [connectionFlash <= 0x00000001]
[11:50:50.014]      __var FLASH_BASE = 0x40022000 ;
[11:50:50.014]        // -> [FLASH_BASE <= 0x40022000]
[11:50:50.015]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:50:50.015]        // -> [FLASH_CR <= 0x40022004]
[11:50:50.015]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:50:50.015]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:50:50.015]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:50:50.016]        // -> [LOCK_BIT <= 0x00000001]
[11:50:50.016]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:50:50.016]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:50:50.016]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:50:50.016]        // -> [FLASH_KEYR <= 0x4002200C]
[11:50:50.016]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:50:50.016]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:50:50.017]      __var FLASH_KEY2 = 0x02030405 ;
[11:50:50.017]        // -> [FLASH_KEY2 <= 0x02030405]
[11:50:50.018]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:50:50.018]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:50:50.018]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:50:50.019]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:50:50.019]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:50:50.019]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:50:50.019]      __var FLASH_CR_Value = 0 ;
[11:50:50.019]        // -> [FLASH_CR_Value <= 0x00000000]
[11:50:50.019]      __var DoDebugPortStop = 1 ;
[11:50:50.020]        // -> [DoDebugPortStop <= 0x00000001]
[11:50:50.020]      __var DP_CTRL_STAT = 0x4 ;
[11:50:50.020]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:50:50.020]      __var DP_SELECT = 0x8 ;
[11:50:50.020]        // -> [DP_SELECT <= 0x00000008]
[11:50:50.021]    </block>
[11:50:50.021]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:50:50.021]      // if-block "connectionFlash && DoOptionByteLoading"
[11:50:50.021]        // =>  FALSE
[11:50:50.021]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:50:50.022]    </control>
[11:50:50.022]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:50:50.022]      // if-block "DoDebugPortStop"
[11:50:50.022]        // =>  TRUE
[11:50:50.022]      <block atomic="false" info="">
[11:50:50.022]        WriteDP(DP_SELECT, 0x00000000);
[11:50:50.023]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:50:50.023]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:50:50.024]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:50:50.024]      </block>
[11:50:50.024]      // end if-block "DoDebugPortStop"
[11:50:50.025]    </control>
[11:50:50.025]  </sequence>
[11:50:50.025]  
[11:56:39.831]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:56:39.831]  
[11:56:39.831]  <debugvars>
[11:56:39.831]    // Pre-defined
[11:56:39.831]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:56:39.831]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:56:39.831]    __dp=0x00000000
[11:56:39.831]    __ap=0x00000000
[11:56:39.841]    __traceout=0x00000000      (Trace Disabled)
[11:56:39.841]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:56:39.841]    __FlashAddr=0x00000000
[11:56:39.841]    __FlashLen=0x00000000
[11:56:39.841]    __FlashArg=0x00000000
[11:56:39.841]    __FlashOp=0x00000000
[11:56:39.841]    __Result=0x00000000
[11:56:39.841]    
[11:56:39.841]    // User-defined
[11:56:39.841]    DbgMCU_CR=0x00000007
[11:56:39.841]    DbgMCU_APB1_Fz=0x00000000
[11:56:39.841]    DbgMCU_APB2_Fz=0x00000000
[11:56:39.841]    DoOptionByteLoading=0x00000000
[11:56:39.841]  </debugvars>
[11:56:39.841]  
[11:56:39.841]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:56:39.841]    <block atomic="false" info="">
[11:56:39.841]      Sequence("CheckID");
[11:56:39.841]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:56:39.841]          <block atomic="false" info="">
[11:56:39.841]            __var pidr1 = 0;
[11:56:39.841]              // -> [pidr1 <= 0x00000000]
[11:56:39.841]            __var pidr2 = 0;
[11:56:39.841]              // -> [pidr2 <= 0x00000000]
[11:56:39.841]            __var jep106id = 0;
[11:56:39.841]              // -> [jep106id <= 0x00000000]
[11:56:39.841]            __var ROMTableBase = 0;
[11:56:39.841]              // -> [ROMTableBase <= 0x00000000]
[11:56:39.841]            __ap = 0;      // AHB-AP
[11:56:39.841]              // -> [__ap <= 0x00000000]
[11:56:39.841]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:56:39.847]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:56:39.847]              // -> [ROMTableBase <= 0xF0000000]
[11:56:39.847]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:56:39.847]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:56:39.849]              // -> [pidr1 <= 0x00000004]
[11:56:39.849]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:56:39.849]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:56:39.851]              // -> [pidr2 <= 0x0000000A]
[11:56:39.851]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:56:39.851]              // -> [jep106id <= 0x00000020]
[11:56:39.851]          </block>
[11:56:39.851]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:56:39.851]            // if-block "jep106id != 0x20"
[11:56:39.851]              // =>  FALSE
[11:56:39.851]            // skip if-block "jep106id != 0x20"
[11:56:39.851]          </control>
[11:56:39.851]        </sequence>
[11:56:39.853]    </block>
[11:56:39.853]  </sequence>
[11:56:39.853]  
[11:56:39.862]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:56:39.862]  
[11:56:39.862]  <debugvars>
[11:56:39.862]    // Pre-defined
[11:56:39.862]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:56:39.862]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:56:39.862]    __dp=0x00000000
[11:56:39.862]    __ap=0x00000000
[11:56:39.862]    __traceout=0x00000000      (Trace Disabled)
[11:56:39.862]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:56:39.862]    __FlashAddr=0x00000000
[11:56:39.862]    __FlashLen=0x00000000
[11:56:39.862]    __FlashArg=0x00000000
[11:56:39.862]    __FlashOp=0x00000000
[11:56:39.862]    __Result=0x00000000
[11:56:39.862]    
[11:56:39.862]    // User-defined
[11:56:39.862]    DbgMCU_CR=0x00000007
[11:56:39.862]    DbgMCU_APB1_Fz=0x00000000
[11:56:39.862]    DbgMCU_APB2_Fz=0x00000000
[11:56:39.862]    DoOptionByteLoading=0x00000000
[11:56:39.862]  </debugvars>
[11:56:39.862]  
[11:56:39.862]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:56:39.862]    <block atomic="false" info="">
[11:56:39.862]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:56:39.862]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:56:39.862]    </block>
[11:56:39.862]    <block atomic="false" info="DbgMCU registers">
[11:56:39.862]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:56:39.871]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:56:39.871]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:56:39.871]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:56:39.873]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:56:39.873]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:56:39.873]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:56:39.873]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:56:39.873]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:56:39.873]    </block>
[11:56:39.873]  </sequence>
[11:56:39.873]  
[11:56:47.258]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:56:47.258]  
[11:56:47.258]  <debugvars>
[11:56:47.258]    // Pre-defined
[11:56:47.258]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:56:47.258]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:56:47.260]    __dp=0x00000000
[11:56:47.260]    __ap=0x00000000
[11:56:47.260]    __traceout=0x00000000      (Trace Disabled)
[11:56:47.260]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:56:47.260]    __FlashAddr=0x00000000
[11:56:47.260]    __FlashLen=0x00000000
[11:56:47.262]    __FlashArg=0x00000000
[11:56:47.262]    __FlashOp=0x00000000
[11:56:47.263]    __Result=0x00000000
[11:56:47.263]    
[11:56:47.263]    // User-defined
[11:56:47.263]    DbgMCU_CR=0x00000007
[11:56:47.263]    DbgMCU_APB1_Fz=0x00000000
[11:56:47.264]    DbgMCU_APB2_Fz=0x00000000
[11:56:47.264]    DoOptionByteLoading=0x00000000
[11:56:47.264]  </debugvars>
[11:56:47.264]  
[11:56:47.264]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:56:47.264]    <block atomic="false" info="">
[11:56:47.264]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:56:47.266]        // -> [connectionFlash <= 0x00000001]
[11:56:47.267]      __var FLASH_BASE = 0x40022000 ;
[11:56:47.267]        // -> [FLASH_BASE <= 0x40022000]
[11:56:47.267]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:56:47.268]        // -> [FLASH_CR <= 0x40022004]
[11:56:47.268]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:56:47.268]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:56:47.268]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:56:47.269]        // -> [LOCK_BIT <= 0x00000001]
[11:56:47.269]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:56:47.269]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:56:47.269]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:56:47.270]        // -> [FLASH_KEYR <= 0x4002200C]
[11:56:47.270]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:56:47.270]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:56:47.271]      __var FLASH_KEY2 = 0x02030405 ;
[11:56:47.271]        // -> [FLASH_KEY2 <= 0x02030405]
[11:56:47.271]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:56:47.271]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:56:47.272]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:56:47.272]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:56:47.272]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:56:47.272]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:56:47.272]      __var FLASH_CR_Value = 0 ;
[11:56:47.273]        // -> [FLASH_CR_Value <= 0x00000000]
[11:56:47.273]      __var DoDebugPortStop = 1 ;
[11:56:47.273]        // -> [DoDebugPortStop <= 0x00000001]
[11:56:47.273]      __var DP_CTRL_STAT = 0x4 ;
[11:56:47.273]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:56:47.274]      __var DP_SELECT = 0x8 ;
[11:56:47.274]        // -> [DP_SELECT <= 0x00000008]
[11:56:47.274]    </block>
[11:56:47.274]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:56:47.274]      // if-block "connectionFlash && DoOptionByteLoading"
[11:56:47.274]        // =>  FALSE
[11:56:47.275]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:56:47.275]    </control>
[11:56:47.275]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:56:47.275]      // if-block "DoDebugPortStop"
[11:56:47.275]        // =>  TRUE
[11:56:47.276]      <block atomic="false" info="">
[11:56:47.276]        WriteDP(DP_SELECT, 0x00000000);
[11:56:47.276]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:56:47.276]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:56:47.277]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:56:47.277]      </block>
[11:56:47.277]      // end if-block "DoDebugPortStop"
[11:56:47.277]    </control>
[11:56:47.277]  </sequence>
[11:56:47.278]  
