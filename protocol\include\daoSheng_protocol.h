#ifndef __DAO_SHENG_PROTOCOL_H__
#define __DAO_SHENG_PROTOCOL_H__ 

#include "system.h"
#include "uart.h"

typedef struct {
    float flow_rate;         // 瞬时流量(m³/s)
    float velocity;          // 流速(m/s)
    float battery_voltage;   // 电池电压(V)
    double net_accumulated;   // 净累积量
    uint32_t work_status;    // 工作状态码
    char software_version[5];// 软件版本
} MeterData;

void parse_modbus_data(uint8_t *data, uint16_t len, MeterData *result);
uint16_t calculate_crc16(const uint8_t *data, uint16_t length);

uint16_t mbus_crc16(uint8_t *data, uint16_t length);
void daoSheng_protocol_parsing(SERIAL serial);
void get_water_value(void);

#endif
