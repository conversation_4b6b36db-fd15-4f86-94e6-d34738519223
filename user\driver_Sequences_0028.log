/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : D:\2025work\STM32L072PRO\source-application\user\driver_Sequences_0028.log
 *  Created     : 08:53:45 (23/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : D:\2025work\STM32L072PRO\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[08:53:45.764]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:53:45.764]  
[08:53:45.788]  <debugvars>
[08:53:45.814]    // Pre-defined
[08:53:45.838]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:53:45.864]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:53:45.865]    __dp=0x00000000
[08:53:45.865]    __ap=0x00000000
[08:53:45.865]    __traceout=0x00000000      (Trace Disabled)
[08:53:45.865]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:53:45.865]    __FlashAddr=0x00000000
[08:53:45.867]    __FlashLen=0x00000000
[08:53:45.868]    __FlashArg=0x00000000
[08:53:45.868]    __FlashOp=0x00000000
[08:53:45.869]    __Result=0x00000000
[08:53:45.869]    
[08:53:45.869]    // User-defined
[08:53:45.870]    DbgMCU_CR=0x00000007
[08:53:45.870]    DbgMCU_APB1_Fz=0x00000000
[08:53:45.871]    DbgMCU_APB2_Fz=0x00000000
[08:53:45.871]    DoOptionByteLoading=0x00000000
[08:53:45.872]  </debugvars>
[08:53:45.872]  
[08:53:45.873]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:53:45.874]    <block atomic="false" info="">
[08:53:45.874]      Sequence("CheckID");
[08:53:45.875]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:53:45.875]          <block atomic="false" info="">
[08:53:45.875]            __var pidr1 = 0;
[08:53:45.875]              // -> [pidr1 <= 0x00000000]
[08:53:45.875]            __var pidr2 = 0;
[08:53:45.877]              // -> [pidr2 <= 0x00000000]
[08:53:45.878]            __var jep106id = 0;
[08:53:45.878]              // -> [jep106id <= 0x00000000]
[08:53:45.879]            __var ROMTableBase = 0;
[08:53:45.879]              // -> [ROMTableBase <= 0x00000000]
[08:53:45.880]            __ap = 0;      // AHB-AP
[08:53:45.880]              // -> [__ap <= 0x00000000]
[08:53:45.880]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:53:45.881]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:53:45.882]              // -> [ROMTableBase <= 0xF0000000]
[08:53:45.883]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:53:45.884]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:53:45.885]              // -> [pidr1 <= 0x00000004]
[08:53:45.885]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:53:45.886]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:53:45.886]              // -> [pidr2 <= 0x0000000A]
[08:53:45.886]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:53:45.887]              // -> [jep106id <= 0x00000020]
[08:53:45.887]          </block>
[08:53:45.887]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:53:45.888]            // if-block "jep106id != 0x20"
[08:53:45.888]              // =>  FALSE
[08:53:45.888]            // skip if-block "jep106id != 0x20"
[08:53:45.888]          </control>
[08:53:45.889]        </sequence>
[08:53:45.889]    </block>
[08:53:45.889]  </sequence>
[08:53:45.890]  
[08:53:45.903]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:53:45.903]  
[08:53:45.922]  <debugvars>
[08:53:45.923]    // Pre-defined
[08:53:45.923]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:53:45.924]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:53:45.924]    __dp=0x00000000
[08:53:45.925]    __ap=0x00000000
[08:53:45.925]    __traceout=0x00000000      (Trace Disabled)
[08:53:45.925]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:53:45.925]    __FlashAddr=0x00000000
[08:53:45.927]    __FlashLen=0x00000000
[08:53:45.927]    __FlashArg=0x00000000
[08:53:45.928]    __FlashOp=0x00000000
[08:53:45.929]    __Result=0x00000000
[08:53:45.929]    
[08:53:45.929]    // User-defined
[08:53:45.930]    DbgMCU_CR=0x00000007
[08:53:45.930]    DbgMCU_APB1_Fz=0x00000000
[08:53:45.931]    DbgMCU_APB2_Fz=0x00000000
[08:53:45.931]    DoOptionByteLoading=0x00000000
[08:53:45.932]  </debugvars>
[08:53:45.932]  
[08:53:45.933]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:53:45.933]    <block atomic="false" info="">
[08:53:45.934]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:53:45.936]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:53:45.936]    </block>
[08:53:45.937]    <block atomic="false" info="DbgMCU registers">
[08:53:45.938]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:53:45.939]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[08:53:45.941]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[08:53:45.941]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:53:45.943]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:53:45.944]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:53:45.945]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:53:45.946]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:53:45.948]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:53:45.948]    </block>
[08:53:45.948]  </sequence>
[08:53:45.949]  
[08:53:53.945]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[08:53:53.945]  
[08:53:53.945]  <debugvars>
[08:53:53.946]    // Pre-defined
[08:53:53.946]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:53:53.946]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:53:53.947]    __dp=0x00000000
[08:53:53.947]    __ap=0x00000000
[08:53:53.947]    __traceout=0x00000000      (Trace Disabled)
[08:53:53.948]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:53:53.948]    __FlashAddr=0x00000000
[08:53:53.948]    __FlashLen=0x00000000
[08:53:53.949]    __FlashArg=0x00000000
[08:53:53.949]    __FlashOp=0x00000000
[08:53:53.949]    __Result=0x00000000
[08:53:53.950]    
[08:53:53.950]    // User-defined
[08:53:53.950]    DbgMCU_CR=0x00000007
[08:53:53.950]    DbgMCU_APB1_Fz=0x00000000
[08:53:53.950]    DbgMCU_APB2_Fz=0x00000000
[08:53:53.951]    DoOptionByteLoading=0x00000000
[08:53:53.951]  </debugvars>
[08:53:53.951]  
[08:53:53.951]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[08:53:53.952]    <block atomic="false" info="">
[08:53:53.952]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[08:53:53.952]        // -> [connectionFlash <= 0x00000001]
[08:53:53.953]      __var FLASH_BASE = 0x40022000 ;
[08:53:53.953]        // -> [FLASH_BASE <= 0x40022000]
[08:53:53.953]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[08:53:53.954]        // -> [FLASH_CR <= 0x40022004]
[08:53:53.954]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[08:53:53.954]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[08:53:53.954]      __var LOCK_BIT = ( 1 << 0 ) ;
[08:53:53.954]        // -> [LOCK_BIT <= 0x00000001]
[08:53:53.955]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[08:53:53.955]        // -> [OPTLOCK_BIT <= 0x00000004]
[08:53:53.955]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[08:53:53.955]        // -> [FLASH_KEYR <= 0x4002200C]
[08:53:53.955]      __var FLASH_KEY1 = 0x89ABCDEF ;
[08:53:53.955]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[08:53:53.956]      __var FLASH_KEY2 = 0x02030405 ;
[08:53:53.956]        // -> [FLASH_KEY2 <= 0x02030405]
[08:53:53.957]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[08:53:53.957]        // -> [FLASH_OPTKEYR <= 0x40022014]
[08:53:53.957]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[08:53:53.957]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[08:53:53.958]      __var FLASH_OPTKEY2 = 0x24252627 ;
[08:53:53.958]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[08:53:53.958]      __var FLASH_CR_Value = 0 ;
[08:53:53.958]        // -> [FLASH_CR_Value <= 0x00000000]
[08:53:53.958]      __var DoDebugPortStop = 1 ;
[08:53:53.958]        // -> [DoDebugPortStop <= 0x00000001]
[08:53:53.959]      __var DP_CTRL_STAT = 0x4 ;
[08:53:53.959]        // -> [DP_CTRL_STAT <= 0x00000004]
[08:53:53.959]      __var DP_SELECT = 0x8 ;
[08:53:53.960]        // -> [DP_SELECT <= 0x00000008]
[08:53:53.960]    </block>
[08:53:53.960]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[08:53:53.960]      // if-block "connectionFlash && DoOptionByteLoading"
[08:53:53.960]        // =>  FALSE
[08:53:53.960]      // skip if-block "connectionFlash && DoOptionByteLoading"
[08:53:53.960]    </control>
[08:53:53.961]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[08:53:53.961]      // if-block "DoDebugPortStop"
[08:53:53.961]        // =>  TRUE
[08:53:53.961]      <block atomic="false" info="">
[08:53:53.961]        WriteDP(DP_SELECT, 0x00000000);
[08:53:53.962]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[08:53:53.962]        WriteDP(DP_CTRL_STAT, 0x00000000);
[08:53:53.963]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[08:53:53.963]      </block>
[08:53:53.963]      // end if-block "DoDebugPortStop"
[08:53:53.963]    </control>
[08:53:53.963]  </sequence>
[08:53:53.964]  
[08:56:12.871]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:56:12.871]  
[08:56:12.871]  <debugvars>
[08:56:12.871]    // Pre-defined
[08:56:12.872]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:56:12.872]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[08:56:12.872]    __dp=0x00000000
[08:56:12.872]    __ap=0x00000000
[08:56:12.872]    __traceout=0x00000000      (Trace Disabled)
[08:56:12.873]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:56:12.873]    __FlashAddr=0x00000000
[08:56:12.873]    __FlashLen=0x00000000
[08:56:12.873]    __FlashArg=0x00000000
[08:56:12.873]    __FlashOp=0x00000000
[08:56:12.874]    __Result=0x00000000
[08:56:12.874]    
[08:56:12.874]    // User-defined
[08:56:12.874]    DbgMCU_CR=0x00000007
[08:56:12.874]    DbgMCU_APB1_Fz=0x00000000
[08:56:12.874]    DbgMCU_APB2_Fz=0x00000000
[08:56:12.875]    DoOptionByteLoading=0x00000000
[08:56:12.875]  </debugvars>
[08:56:12.875]  
[08:56:12.875]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:56:12.875]    <block atomic="false" info="">
[08:56:12.876]      Sequence("CheckID");
[08:56:12.876]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:56:12.876]          <block atomic="false" info="">
[08:56:12.876]            __var pidr1 = 0;
[08:56:12.877]              // -> [pidr1 <= 0x00000000]
[08:56:12.877]            __var pidr2 = 0;
[08:56:12.877]              // -> [pidr2 <= 0x00000000]
[08:56:12.877]            __var jep106id = 0;
[08:56:12.877]              // -> [jep106id <= 0x00000000]
[08:56:12.878]            __var ROMTableBase = 0;
[08:56:12.878]              // -> [ROMTableBase <= 0x00000000]
[08:56:12.878]            __ap = 0;      // AHB-AP
[08:56:12.878]              // -> [__ap <= 0x00000000]
[08:56:12.878]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:56:12.879]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:56:12.880]              // -> [ROMTableBase <= 0xF0000000]
[08:56:12.881]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:56:12.882]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:56:12.882]              // -> [pidr1 <= 0x00000004]
[08:56:12.882]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:56:12.883]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:56:12.883]              // -> [pidr2 <= 0x0000000A]
[08:56:12.883]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:56:12.884]              // -> [jep106id <= 0x00000020]
[08:56:12.884]          </block>
[08:56:12.884]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:56:12.884]            // if-block "jep106id != 0x20"
[08:56:12.884]              // =>  FALSE
[08:56:12.885]            // skip if-block "jep106id != 0x20"
[08:56:12.885]          </control>
[08:56:12.885]        </sequence>
[08:56:12.885]    </block>
[08:56:12.885]  </sequence>
[08:56:12.885]  
[08:56:12.898]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:56:12.898]  
[08:56:12.911]  <debugvars>
[08:56:12.912]    // Pre-defined
[08:56:12.912]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:56:12.912]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[08:56:12.912]    __dp=0x00000000
[08:56:12.913]    __ap=0x00000000
[08:56:12.913]    __traceout=0x00000000      (Trace Disabled)
[08:56:12.913]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:56:12.913]    __FlashAddr=0x00000000
[08:56:12.913]    __FlashLen=0x00000000
[08:56:12.914]    __FlashArg=0x00000000
[08:56:12.914]    __FlashOp=0x00000000
[08:56:12.914]    __Result=0x00000000
[08:56:12.914]    
[08:56:12.914]    // User-defined
[08:56:12.915]    DbgMCU_CR=0x00000007
[08:56:12.915]    DbgMCU_APB1_Fz=0x00000000
[08:56:12.915]    DbgMCU_APB2_Fz=0x00000000
[08:56:12.915]    DoOptionByteLoading=0x00000000
[08:56:12.915]  </debugvars>
[08:56:12.916]  
[08:56:12.916]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:56:12.916]    <block atomic="false" info="">
[08:56:12.916]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:56:12.918]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:56:12.918]    </block>
[08:56:12.918]    <block atomic="false" info="DbgMCU registers">
[08:56:12.918]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:56:12.919]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[08:56:12.920]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[08:56:12.920]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:56:12.921]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:56:12.921]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:56:12.922]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:56:12.922]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:56:12.923]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:56:12.923]    </block>
[08:56:12.924]  </sequence>
[08:56:12.924]  
[08:58:39.477]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[08:58:39.477]  
[08:58:39.489]  <debugvars>
[08:58:39.490]    // Pre-defined
[08:58:39.490]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:58:39.490]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[08:58:39.491]    __dp=0x00000000
[08:58:39.492]    __ap=0x00000000
[08:58:39.492]    __traceout=0x00000000      (Trace Disabled)
[08:58:39.492]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:58:39.492]    __FlashAddr=0x00000000
[08:58:39.494]    __FlashLen=0x00000000
[08:58:39.494]    __FlashArg=0x00000000
[08:58:39.494]    __FlashOp=0x00000000
[08:58:39.495]    __Result=0x00000000
[08:58:39.495]    
[08:58:39.495]    // User-defined
[08:58:39.496]    DbgMCU_CR=0x00000007
[08:58:39.496]    DbgMCU_APB1_Fz=0x00000000
[08:58:39.496]    DbgMCU_APB2_Fz=0x00000000
[08:58:39.497]    DoOptionByteLoading=0x00000000
[08:58:39.498]  </debugvars>
[08:58:39.498]  
[08:58:39.498]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[08:58:39.499]    <block atomic="false" info="">
[08:58:39.499]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[08:58:39.499]        // -> [connectionFlash <= 0x00000000]
[08:58:39.499]      __var FLASH_BASE = 0x40022000 ;
[08:58:39.500]        // -> [FLASH_BASE <= 0x40022000]
[08:58:39.500]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[08:58:39.500]        // -> [FLASH_CR <= 0x40022004]
[08:58:39.501]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[08:58:39.501]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[08:58:39.501]      __var LOCK_BIT = ( 1 << 0 ) ;
[08:58:39.501]        // -> [LOCK_BIT <= 0x00000001]
[08:58:39.501]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[08:58:39.501]        // -> [OPTLOCK_BIT <= 0x00000004]
[08:58:39.502]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[08:58:39.502]        // -> [FLASH_KEYR <= 0x4002200C]
[08:58:39.502]      __var FLASH_KEY1 = 0x89ABCDEF ;
[08:58:39.502]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[08:58:39.502]      __var FLASH_KEY2 = 0x02030405 ;
[08:58:39.503]        // -> [FLASH_KEY2 <= 0x02030405]
[08:58:39.503]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[08:58:39.503]        // -> [FLASH_OPTKEYR <= 0x40022014]
[08:58:39.503]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[08:58:39.503]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[08:58:39.503]      __var FLASH_OPTKEY2 = 0x24252627 ;
[08:58:39.504]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[08:58:39.504]      __var FLASH_CR_Value = 0 ;
[08:58:39.504]        // -> [FLASH_CR_Value <= 0x00000000]
[08:58:39.504]      __var DoDebugPortStop = 1 ;
[08:58:39.504]        // -> [DoDebugPortStop <= 0x00000001]
[08:58:39.505]      __var DP_CTRL_STAT = 0x4 ;
[08:58:39.505]        // -> [DP_CTRL_STAT <= 0x00000004]
[08:58:39.505]      __var DP_SELECT = 0x8 ;
[08:58:39.505]        // -> [DP_SELECT <= 0x00000008]
[08:58:39.505]    </block>
[08:58:39.505]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[08:58:39.506]      // if-block "connectionFlash && DoOptionByteLoading"
[08:58:39.506]        // =>  FALSE
[08:58:39.506]      // skip if-block "connectionFlash && DoOptionByteLoading"
[08:58:39.506]    </control>
[08:58:39.506]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[08:58:39.506]      // if-block "DoDebugPortStop"
[08:58:39.506]        // =>  TRUE
[08:58:39.506]      <block atomic="false" info="">
[08:58:39.507]        WriteDP(DP_SELECT, 0x00000000);
[08:58:39.508]  
[08:58:39.508]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[08:58:39.508]  
[08:58:39.508]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[08:58:39.508]      </block>
[08:58:39.509]      // end if-block "DoDebugPortStop"
[08:58:39.509]    </control>
[08:58:39.509]  </sequence>
[08:58:39.510]  
[09:22:37.369]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:22:37.369]  
[09:22:37.370]  <debugvars>
[09:22:37.371]    // Pre-defined
[09:22:37.371]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:22:37.371]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:22:37.372]    __dp=0x00000000
[09:22:37.372]    __ap=0x00000000
[09:22:37.372]    __traceout=0x00000000      (Trace Disabled)
[09:22:37.373]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:22:37.373]    __FlashAddr=0x00000000
[09:22:37.373]    __FlashLen=0x00000000
[09:22:37.374]    __FlashArg=0x00000000
[09:22:37.374]    __FlashOp=0x00000000
[09:22:37.374]    __Result=0x00000000
[09:22:37.374]    
[09:22:37.374]    // User-defined
[09:22:37.375]    DbgMCU_CR=0x00000007
[09:22:37.375]    DbgMCU_APB1_Fz=0x00000000
[09:22:37.375]    DbgMCU_APB2_Fz=0x00000000
[09:22:37.375]    DoOptionByteLoading=0x00000000
[09:22:37.375]  </debugvars>
[09:22:37.376]  
[09:22:37.376]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:22:37.376]    <block atomic="false" info="">
[09:22:37.376]      Sequence("CheckID");
[09:22:37.376]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:22:37.377]          <block atomic="false" info="">
[09:22:37.377]            __var pidr1 = 0;
[09:22:37.377]              // -> [pidr1 <= 0x00000000]
[09:22:37.377]            __var pidr2 = 0;
[09:22:37.377]              // -> [pidr2 <= 0x00000000]
[09:22:37.377]            __var jep106id = 0;
[09:22:37.378]              // -> [jep106id <= 0x00000000]
[09:22:37.378]            __var ROMTableBase = 0;
[09:22:37.378]              // -> [ROMTableBase <= 0x00000000]
[09:22:37.378]            __ap = 0;      // AHB-AP
[09:22:37.378]              // -> [__ap <= 0x00000000]
[09:22:37.379]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:22:37.379]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:22:37.380]              // -> [ROMTableBase <= 0xF0000000]
[09:22:37.380]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:22:37.382]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:22:37.382]              // -> [pidr1 <= 0x00000004]
[09:22:37.382]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:22:37.383]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:22:37.383]              // -> [pidr2 <= 0x0000000A]
[09:22:37.383]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:22:37.384]              // -> [jep106id <= 0x00000020]
[09:22:37.384]          </block>
[09:22:37.384]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:22:37.385]            // if-block "jep106id != 0x20"
[09:22:37.385]              // =>  FALSE
[09:22:37.386]            // skip if-block "jep106id != 0x20"
[09:22:37.386]          </control>
[09:22:37.386]        </sequence>
[09:22:37.387]    </block>
[09:22:37.387]  </sequence>
[09:22:37.387]  
[09:22:37.399]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:22:37.399]  
[09:22:37.411]  <debugvars>
[09:22:37.412]    // Pre-defined
[09:22:37.412]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:22:37.413]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:22:37.413]    __dp=0x00000000
[09:22:37.414]    __ap=0x00000000
[09:22:37.414]    __traceout=0x00000000      (Trace Disabled)
[09:22:37.415]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:22:37.415]    __FlashAddr=0x00000000
[09:22:37.415]    __FlashLen=0x00000000
[09:22:37.416]    __FlashArg=0x00000000
[09:22:37.416]    __FlashOp=0x00000000
[09:22:37.417]    __Result=0x00000000
[09:22:37.417]    
[09:22:37.417]    // User-defined
[09:22:37.417]    DbgMCU_CR=0x00000007
[09:22:37.417]    DbgMCU_APB1_Fz=0x00000000
[09:22:37.418]    DbgMCU_APB2_Fz=0x00000000
[09:22:37.419]    DoOptionByteLoading=0x00000000
[09:22:37.419]  </debugvars>
[09:22:37.419]  
[09:22:37.420]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:22:37.420]    <block atomic="false" info="">
[09:22:37.421]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:22:37.422]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:22:37.422]    </block>
[09:22:37.422]    <block atomic="false" info="DbgMCU registers">
[09:22:37.423]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:22:37.423]        // -> [Read32(0x40021034) => 0x00000200]   (__dp=0x00000000, __ap=0x00000000)
[09:22:37.424]        // -> [Write32(0x40021034, 0x00400200)]   (__dp=0x00000000, __ap=0x00000000)
[09:22:37.424]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:22:37.425]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:22:37.426]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:22:37.426]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:22:37.427]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:22:37.428]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:22:37.428]    </block>
[09:22:37.428]  </sequence>
[09:22:37.428]  
[09:22:45.461]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:22:45.461]  
[09:22:45.462]  <debugvars>
[09:22:45.463]    // Pre-defined
[09:22:45.464]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:22:45.464]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:22:45.465]    __dp=0x00000000
[09:22:45.465]    __ap=0x00000000
[09:22:45.466]    __traceout=0x00000000      (Trace Disabled)
[09:22:45.466]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:22:45.466]    __FlashAddr=0x00000000
[09:22:45.467]    __FlashLen=0x00000000
[09:22:45.467]    __FlashArg=0x00000000
[09:22:45.468]    __FlashOp=0x00000000
[09:22:45.469]    __Result=0x00000000
[09:22:45.469]    
[09:22:45.469]    // User-defined
[09:22:45.470]    DbgMCU_CR=0x00000007
[09:22:45.470]    DbgMCU_APB1_Fz=0x00000000
[09:22:45.470]    DbgMCU_APB2_Fz=0x00000000
[09:22:45.471]    DoOptionByteLoading=0x00000000
[09:22:45.471]  </debugvars>
[09:22:45.472]  
[09:22:45.473]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:22:45.473]    <block atomic="false" info="">
[09:22:45.474]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:22:45.474]        // -> [connectionFlash <= 0x00000001]
[09:22:45.475]      __var FLASH_BASE = 0x40022000 ;
[09:22:45.475]        // -> [FLASH_BASE <= 0x40022000]
[09:22:45.475]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:22:45.476]        // -> [FLASH_CR <= 0x40022004]
[09:22:45.476]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:22:45.477]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:22:45.477]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:22:45.478]        // -> [LOCK_BIT <= 0x00000001]
[09:22:45.478]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:22:45.478]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:22:45.479]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:22:45.479]        // -> [FLASH_KEYR <= 0x4002200C]
[09:22:45.479]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:22:45.479]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:22:45.479]      __var FLASH_KEY2 = 0x02030405 ;
[09:22:45.480]        // -> [FLASH_KEY2 <= 0x02030405]
[09:22:45.480]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:22:45.480]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:22:45.480]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:22:45.480]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:22:45.480]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:22:45.481]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:22:45.481]      __var FLASH_CR_Value = 0 ;
[09:22:45.481]        // -> [FLASH_CR_Value <= 0x00000000]
[09:22:45.481]      __var DoDebugPortStop = 1 ;
[09:22:45.481]        // -> [DoDebugPortStop <= 0x00000001]
[09:22:45.482]      __var DP_CTRL_STAT = 0x4 ;
[09:22:45.482]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:22:45.482]      __var DP_SELECT = 0x8 ;
[09:22:45.482]        // -> [DP_SELECT <= 0x00000008]
[09:22:45.482]    </block>
[09:22:45.483]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:22:45.483]      // if-block "connectionFlash && DoOptionByteLoading"
[09:22:45.483]        // =>  FALSE
[09:22:45.483]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:22:45.483]    </control>
[09:22:45.483]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:22:45.483]      // if-block "DoDebugPortStop"
[09:22:45.483]        // =>  TRUE
[09:22:45.484]      <block atomic="false" info="">
[09:22:45.484]        WriteDP(DP_SELECT, 0x00000000);
[09:22:45.485]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:22:45.486]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:22:45.486]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:22:45.487]      </block>
[09:22:45.487]      // end if-block "DoDebugPortStop"
[09:22:45.487]    </control>
[09:22:45.487]  </sequence>
[09:22:45.488]  
[09:54:12.222]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:54:12.222]  
[09:54:12.222]  <debugvars>
[09:54:12.223]    // Pre-defined
[09:54:12.223]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:54:12.223]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:54:12.224]    __dp=0x00000000
[09:54:12.224]    __ap=0x00000000
[09:54:12.224]    __traceout=0x00000000      (Trace Disabled)
[09:54:12.224]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:54:12.225]    __FlashAddr=0x00000000
[09:54:12.225]    __FlashLen=0x00000000
[09:54:12.225]    __FlashArg=0x00000000
[09:54:12.226]    __FlashOp=0x00000000
[09:54:12.226]    __Result=0x00000000
[09:54:12.226]    
[09:54:12.226]    // User-defined
[09:54:12.226]    DbgMCU_CR=0x00000007
[09:54:12.227]    DbgMCU_APB1_Fz=0x00000000
[09:54:12.227]    DbgMCU_APB2_Fz=0x00000000
[09:54:12.227]    DoOptionByteLoading=0x00000000
[09:54:12.228]  </debugvars>
[09:54:12.228]  
[09:54:12.228]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:54:12.229]    <block atomic="false" info="">
[09:54:12.229]      Sequence("CheckID");
[09:54:12.229]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:54:12.229]          <block atomic="false" info="">
[09:54:12.229]            __var pidr1 = 0;
[09:54:12.230]              // -> [pidr1 <= 0x00000000]
[09:54:12.230]            __var pidr2 = 0;
[09:54:12.230]              // -> [pidr2 <= 0x00000000]
[09:54:12.230]            __var jep106id = 0;
[09:54:12.230]              // -> [jep106id <= 0x00000000]
[09:54:12.230]            __var ROMTableBase = 0;
[09:54:12.230]              // -> [ROMTableBase <= 0x00000000]
[09:54:12.231]            __ap = 0;      // AHB-AP
[09:54:12.231]              // -> [__ap <= 0x00000000]
[09:54:12.232]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:54:12.232]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:54:12.233]              // -> [ROMTableBase <= 0xF0000000]
[09:54:12.234]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:54:12.235]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:54:12.235]              // -> [pidr1 <= 0x00000004]
[09:54:12.236]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:54:12.237]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:54:12.237]              // -> [pidr2 <= 0x0000000A]
[09:54:12.237]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:54:12.238]              // -> [jep106id <= 0x00000020]
[09:54:12.238]          </block>
[09:54:12.238]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:54:12.239]            // if-block "jep106id != 0x20"
[09:54:12.239]              // =>  FALSE
[09:54:12.239]            // skip if-block "jep106id != 0x20"
[09:54:12.239]          </control>
[09:54:12.239]        </sequence>
[09:54:12.240]    </block>
[09:54:12.240]  </sequence>
[09:54:12.240]  
[09:54:12.252]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:54:12.252]  
[09:54:12.253]  <debugvars>
[09:54:12.253]    // Pre-defined
[09:54:12.253]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:54:12.253]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:54:12.254]    __dp=0x00000000
[09:54:12.254]    __ap=0x00000000
[09:54:12.254]    __traceout=0x00000000      (Trace Disabled)
[09:54:12.254]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:54:12.255]    __FlashAddr=0x00000000
[09:54:12.255]    __FlashLen=0x00000000
[09:54:12.255]    __FlashArg=0x00000000
[09:54:12.256]    __FlashOp=0x00000000
[09:54:12.256]    __Result=0x00000000
[09:54:12.256]    
[09:54:12.256]    // User-defined
[09:54:12.256]    DbgMCU_CR=0x00000007
[09:54:12.256]    DbgMCU_APB1_Fz=0x00000000
[09:54:12.256]    DbgMCU_APB2_Fz=0x00000000
[09:54:12.256]    DoOptionByteLoading=0x00000000
[09:54:12.257]  </debugvars>
[09:54:12.257]  
[09:54:12.257]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:54:12.257]    <block atomic="false" info="">
[09:54:12.258]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:54:12.258]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:54:12.259]    </block>
[09:54:12.259]    <block atomic="false" info="DbgMCU registers">
[09:54:12.259]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:54:12.260]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[09:54:12.261]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[09:54:12.261]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:54:12.262]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:54:12.263]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:54:12.264]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:54:12.264]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:54:12.264]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:54:12.265]    </block>
[09:54:12.265]  </sequence>
[09:54:12.265]  
[09:54:27.601]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:54:27.601]  
[09:54:27.601]  <debugvars>
[09:54:27.603]    // Pre-defined
[09:54:27.603]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:54:27.603]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:54:27.603]    __dp=0x00000000
[09:54:27.603]    __ap=0x00000000
[09:54:27.603]    __traceout=0x00000000      (Trace Disabled)
[09:54:27.605]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:54:27.605]    __FlashAddr=0x00000000
[09:54:27.605]    __FlashLen=0x00000000
[09:54:27.605]    __FlashArg=0x00000000
[09:54:27.605]    __FlashOp=0x00000000
[09:54:27.605]    __Result=0x00000000
[09:54:27.605]    
[09:54:27.605]    // User-defined
[09:54:27.605]    DbgMCU_CR=0x00000007
[09:54:27.607]    DbgMCU_APB1_Fz=0x00000000
[09:54:27.607]    DbgMCU_APB2_Fz=0x00000000
[09:54:27.607]    DoOptionByteLoading=0x00000000
[09:54:27.607]  </debugvars>
[09:54:27.607]  
[09:54:27.607]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:54:27.607]    <block atomic="false" info="">
[09:54:27.607]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:54:27.607]        // -> [connectionFlash <= 0x00000001]
[09:54:27.609]      __var FLASH_BASE = 0x40022000 ;
[09:54:27.609]        // -> [FLASH_BASE <= 0x40022000]
[09:54:27.609]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:54:27.609]        // -> [FLASH_CR <= 0x40022004]
[09:54:27.609]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:54:27.610]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:54:27.611]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:54:27.611]        // -> [LOCK_BIT <= 0x00000001]
[09:54:27.612]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:54:27.612]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:54:27.612]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:54:27.613]        // -> [FLASH_KEYR <= 0x4002200C]
[09:54:27.613]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:54:27.613]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:54:27.614]      __var FLASH_KEY2 = 0x02030405 ;
[09:54:27.614]        // -> [FLASH_KEY2 <= 0x02030405]
[09:54:27.614]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:54:27.614]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:54:27.615]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:54:27.615]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:54:27.615]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:54:27.615]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:54:27.615]      __var FLASH_CR_Value = 0 ;
[09:54:27.616]        // -> [FLASH_CR_Value <= 0x00000000]
[09:54:27.616]      __var DoDebugPortStop = 1 ;
[09:54:27.616]        // -> [DoDebugPortStop <= 0x00000001]
[09:54:27.617]      __var DP_CTRL_STAT = 0x4 ;
[09:54:27.617]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:54:27.617]      __var DP_SELECT = 0x8 ;
[09:54:27.618]        // -> [DP_SELECT <= 0x00000008]
[09:54:27.618]    </block>
[09:54:27.618]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:54:27.619]      // if-block "connectionFlash && DoOptionByteLoading"
[09:54:27.619]        // =>  FALSE
[09:54:27.619]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:54:27.620]    </control>
[09:54:27.620]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:54:27.620]      // if-block "DoDebugPortStop"
[09:54:27.621]        // =>  TRUE
[09:54:27.621]      <block atomic="false" info="">
[09:54:27.621]        WriteDP(DP_SELECT, 0x00000000);
[09:54:27.622]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:54:27.622]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:54:27.623]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:54:27.623]      </block>
[09:54:27.623]      // end if-block "DoDebugPortStop"
[09:54:27.624]    </control>
[09:54:27.624]  </sequence>
[09:54:27.624]  
[09:54:30.405]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:54:30.405]  
[09:54:30.405]  <debugvars>
[09:54:30.405]    // Pre-defined
[09:54:30.406]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:54:30.407]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:54:30.407]    __dp=0x00000000
[09:54:30.407]    __ap=0x00000000
[09:54:30.408]    __traceout=0x00000000      (Trace Disabled)
[09:54:30.408]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:54:30.408]    __FlashAddr=0x00000000
[09:54:30.409]    __FlashLen=0x00000000
[09:54:30.409]    __FlashArg=0x00000000
[09:54:30.409]    __FlashOp=0x00000000
[09:54:30.409]    __Result=0x00000000
[09:54:30.410]    
[09:54:30.410]    // User-defined
[09:54:30.410]    DbgMCU_CR=0x00000007
[09:54:30.410]    DbgMCU_APB1_Fz=0x00000000
[09:54:30.410]    DbgMCU_APB2_Fz=0x00000000
[09:54:30.410]    DoOptionByteLoading=0x00000000
[09:54:30.411]  </debugvars>
[09:54:30.411]  
[09:54:30.411]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:54:30.412]    <block atomic="false" info="">
[09:54:30.412]      Sequence("CheckID");
[09:54:30.412]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:54:30.413]          <block atomic="false" info="">
[09:54:30.413]            __var pidr1 = 0;
[09:54:30.413]              // -> [pidr1 <= 0x00000000]
[09:54:30.414]            __var pidr2 = 0;
[09:54:30.414]              // -> [pidr2 <= 0x00000000]
[09:54:30.414]            __var jep106id = 0;
[09:54:30.414]              // -> [jep106id <= 0x00000000]
[09:54:30.414]            __var ROMTableBase = 0;
[09:54:30.414]              // -> [ROMTableBase <= 0x00000000]
[09:54:30.415]            __ap = 0;      // AHB-AP
[09:54:30.416]              // -> [__ap <= 0x00000000]
[09:54:30.416]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:54:30.417]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:54:30.417]              // -> [ROMTableBase <= 0xF0000000]
[09:54:30.417]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:54:30.418]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:54:30.419]              // -> [pidr1 <= 0x00000004]
[09:54:30.419]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:54:30.420]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:54:30.420]              // -> [pidr2 <= 0x0000000A]
[09:54:30.421]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:54:30.421]              // -> [jep106id <= 0x00000020]
[09:54:30.421]          </block>
[09:54:30.422]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:54:30.422]            // if-block "jep106id != 0x20"
[09:54:30.422]              // =>  FALSE
[09:54:30.422]            // skip if-block "jep106id != 0x20"
[09:54:30.423]          </control>
[09:54:30.423]        </sequence>
[09:54:30.423]    </block>
[09:54:30.423]  </sequence>
[09:54:30.424]  
[09:54:30.436]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:54:30.436]  
[09:54:30.448]  <debugvars>
[09:54:30.449]    // Pre-defined
[09:54:30.449]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:54:30.450]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:54:30.450]    __dp=0x00000000
[09:54:30.451]    __ap=0x00000000
[09:54:30.451]    __traceout=0x00000000      (Trace Disabled)
[09:54:30.452]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:54:30.452]    __FlashAddr=0x00000000
[09:54:30.453]    __FlashLen=0x00000000
[09:54:30.453]    __FlashArg=0x00000000
[09:54:30.454]    __FlashOp=0x00000000
[09:54:30.454]    __Result=0x00000000
[09:54:30.455]    
[09:54:30.455]    // User-defined
[09:54:30.455]    DbgMCU_CR=0x00000007
[09:54:30.456]    DbgMCU_APB1_Fz=0x00000000
[09:54:30.456]    DbgMCU_APB2_Fz=0x00000000
[09:54:30.457]    DoOptionByteLoading=0x00000000
[09:54:30.457]  </debugvars>
[09:54:30.458]  
[09:54:30.458]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:54:30.459]    <block atomic="false" info="">
[09:54:30.459]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:54:30.461]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:54:30.461]    </block>
[09:54:30.461]    <block atomic="false" info="DbgMCU registers">
[09:54:30.462]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:54:30.463]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[09:54:30.464]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[09:54:30.464]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:54:30.465]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:54:30.465]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:54:30.466]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:54:30.467]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:54:30.468]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:54:30.468]    </block>
[09:54:30.468]  </sequence>
[09:54:30.469]  
[09:54:38.527]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:54:38.527]  
[09:54:38.527]  <debugvars>
[09:54:38.527]    // Pre-defined
[09:54:38.528]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:54:38.528]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:54:38.528]    __dp=0x00000000
[09:54:38.530]    __ap=0x00000000
[09:54:38.530]    __traceout=0x00000000      (Trace Disabled)
[09:54:38.530]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:54:38.531]    __FlashAddr=0x00000000
[09:54:38.531]    __FlashLen=0x00000000
[09:54:38.531]    __FlashArg=0x00000000
[09:54:38.531]    __FlashOp=0x00000000
[09:54:38.532]    __Result=0x00000000
[09:54:38.532]    
[09:54:38.532]    // User-defined
[09:54:38.533]    DbgMCU_CR=0x00000007
[09:54:38.533]    DbgMCU_APB1_Fz=0x00000000
[09:54:38.534]    DbgMCU_APB2_Fz=0x00000000
[09:54:38.534]    DoOptionByteLoading=0x00000000
[09:54:38.535]  </debugvars>
[09:54:38.535]  
[09:54:38.535]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:54:38.536]    <block atomic="false" info="">
[09:54:38.536]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:54:38.537]        // -> [connectionFlash <= 0x00000001]
[09:54:38.537]      __var FLASH_BASE = 0x40022000 ;
[09:54:38.538]        // -> [FLASH_BASE <= 0x40022000]
[09:54:38.538]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:54:38.539]        // -> [FLASH_CR <= 0x40022004]
[09:54:38.539]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:54:38.540]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:54:38.540]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:54:38.541]        // -> [LOCK_BIT <= 0x00000001]
[09:54:38.541]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:54:38.542]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:54:38.542]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:54:38.543]        // -> [FLASH_KEYR <= 0x4002200C]
[09:54:38.543]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:54:38.543]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:54:38.544]      __var FLASH_KEY2 = 0x02030405 ;
[09:54:38.544]        // -> [FLASH_KEY2 <= 0x02030405]
[09:54:38.544]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:54:38.544]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:54:38.545]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:54:38.545]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:54:38.545]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:54:38.545]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:54:38.545]      __var FLASH_CR_Value = 0 ;
[09:54:38.545]        // -> [FLASH_CR_Value <= 0x00000000]
[09:54:38.546]      __var DoDebugPortStop = 1 ;
[09:54:38.546]        // -> [DoDebugPortStop <= 0x00000001]
[09:54:38.546]      __var DP_CTRL_STAT = 0x4 ;
[09:54:38.546]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:54:38.546]      __var DP_SELECT = 0x8 ;
[09:54:38.547]        // -> [DP_SELECT <= 0x00000008]
[09:54:38.547]    </block>
[09:54:38.547]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:54:38.547]      // if-block "connectionFlash && DoOptionByteLoading"
[09:54:38.547]        // =>  FALSE
[09:54:38.548]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:54:38.548]    </control>
[09:54:38.548]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:54:38.548]      // if-block "DoDebugPortStop"
[09:54:38.548]        // =>  TRUE
[09:54:38.548]      <block atomic="false" info="">
[09:54:38.548]        WriteDP(DP_SELECT, 0x00000000);
[09:54:38.549]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:54:38.549]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:54:38.550]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:54:38.550]      </block>
[09:54:38.551]      // end if-block "DoDebugPortStop"
[09:54:38.551]    </control>
[09:54:38.551]  </sequence>
[09:54:38.552]  
[09:54:49.734]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:54:49.734]  
[09:54:49.734]  <debugvars>
[09:54:49.734]    // Pre-defined
[09:54:49.734]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:54:49.734]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[09:54:49.735]    __dp=0x00000000
[09:54:49.735]    __ap=0x00000000
[09:54:49.735]    __traceout=0x00000000      (Trace Disabled)
[09:54:49.735]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:54:49.735]    __FlashAddr=0x00000000
[09:54:49.736]    __FlashLen=0x00000000
[09:54:49.736]    __FlashArg=0x00000000
[09:54:49.736]    __FlashOp=0x00000000
[09:54:49.736]    __Result=0x00000000
[09:54:49.736]    
[09:54:49.736]    // User-defined
[09:54:49.736]    DbgMCU_CR=0x00000007
[09:54:49.737]    DbgMCU_APB1_Fz=0x00000000
[09:54:49.737]    DbgMCU_APB2_Fz=0x00000000
[09:54:49.737]    DoOptionByteLoading=0x00000000
[09:54:49.737]  </debugvars>
[09:54:49.737]  
[09:54:49.737]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:54:49.739]    <block atomic="false" info="">
[09:54:49.739]      Sequence("CheckID");
[09:54:49.739]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:54:49.739]          <block atomic="false" info="">
[09:54:49.739]            __var pidr1 = 0;
[09:54:49.740]              // -> [pidr1 <= 0x00000000]
[09:54:49.740]            __var pidr2 = 0;
[09:54:49.740]              // -> [pidr2 <= 0x00000000]
[09:54:49.740]            __var jep106id = 0;
[09:54:49.740]              // -> [jep106id <= 0x00000000]
[09:54:49.741]            __var ROMTableBase = 0;
[09:54:49.741]              // -> [ROMTableBase <= 0x00000000]
[09:54:49.741]            __ap = 0;      // AHB-AP
[09:54:49.741]              // -> [__ap <= 0x00000000]
[09:54:49.741]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:54:49.742]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:54:49.742]              // -> [ROMTableBase <= 0xF0000000]
[09:54:49.743]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:54:49.743]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:54:49.744]              // -> [pidr1 <= 0x00000004]
[09:54:49.744]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:54:49.745]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:54:49.745]              // -> [pidr2 <= 0x0000000A]
[09:54:49.745]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:54:49.745]              // -> [jep106id <= 0x00000020]
[09:54:49.745]          </block>
[09:54:49.747]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:54:49.747]            // if-block "jep106id != 0x20"
[09:54:49.747]              // =>  FALSE
[09:54:49.747]            // skip if-block "jep106id != 0x20"
[09:54:49.748]          </control>
[09:54:49.748]        </sequence>
[09:54:49.748]    </block>
[09:54:49.748]  </sequence>
[09:54:49.748]  
[09:54:49.762]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:54:49.762]  
[09:54:49.781]  <debugvars>
[09:54:49.781]    // Pre-defined
[09:54:49.782]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:54:49.782]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[09:54:49.782]    __dp=0x00000000
[09:54:49.783]    __ap=0x00000000
[09:54:49.783]    __traceout=0x00000000      (Trace Disabled)
[09:54:49.783]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:54:49.783]    __FlashAddr=0x00000000
[09:54:49.783]    __FlashLen=0x00000000
[09:54:49.785]    __FlashArg=0x00000000
[09:54:49.785]    __FlashOp=0x00000000
[09:54:49.785]    __Result=0x00000000
[09:54:49.785]    
[09:54:49.785]    // User-defined
[09:54:49.785]    DbgMCU_CR=0x00000007
[09:54:49.786]    DbgMCU_APB1_Fz=0x00000000
[09:54:49.786]    DbgMCU_APB2_Fz=0x00000000
[09:54:49.786]    DoOptionByteLoading=0x00000000
[09:54:49.787]  </debugvars>
[09:54:49.787]  
[09:54:49.787]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:54:49.787]    <block atomic="false" info="">
[09:54:49.788]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:54:49.789]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:54:49.789]    </block>
[09:54:49.789]    <block atomic="false" info="DbgMCU registers">
[09:54:49.789]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:54:49.790]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[09:54:49.791]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[09:54:49.791]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:54:49.792]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:54:49.792]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:54:49.794]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:54:49.794]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:54:49.794]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:54:49.794]    </block>
[09:54:49.795]  </sequence>
[09:54:49.795]  
[09:58:03.216]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:58:03.216]  
[09:58:03.216]  <debugvars>
[09:58:03.217]    // Pre-defined
[09:58:03.217]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:58:03.217]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[09:58:03.218]    __dp=0x00000000
[09:58:03.219]    __ap=0x00000000
[09:58:03.219]    __traceout=0x00000000      (Trace Disabled)
[09:58:03.220]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:58:03.220]    __FlashAddr=0x00000000
[09:58:03.221]    __FlashLen=0x00000000
[09:58:03.221]    __FlashArg=0x00000000
[09:58:03.221]    __FlashOp=0x00000000
[09:58:03.222]    __Result=0x00000000
[09:58:03.222]    
[09:58:03.222]    // User-defined
[09:58:03.222]    DbgMCU_CR=0x00000007
[09:58:03.222]    DbgMCU_APB1_Fz=0x00000000
[09:58:03.222]    DbgMCU_APB2_Fz=0x00000000
[09:58:03.223]    DoOptionByteLoading=0x00000000
[09:58:03.223]  </debugvars>
[09:58:03.223]  
[09:58:03.223]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:58:03.223]    <block atomic="false" info="">
[09:58:03.224]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:58:03.224]        // -> [connectionFlash <= 0x00000000]
[09:58:03.224]      __var FLASH_BASE = 0x40022000 ;
[09:58:03.224]        // -> [FLASH_BASE <= 0x40022000]
[09:58:03.224]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:58:03.224]        // -> [FLASH_CR <= 0x40022004]
[09:58:03.225]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:58:03.225]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:58:03.225]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:58:03.225]        // -> [LOCK_BIT <= 0x00000001]
[09:58:03.226]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:58:03.226]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:58:03.226]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:58:03.226]        // -> [FLASH_KEYR <= 0x4002200C]
[09:58:03.226]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:58:03.226]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:58:03.227]      __var FLASH_KEY2 = 0x02030405 ;
[09:58:03.227]        // -> [FLASH_KEY2 <= 0x02030405]
[09:58:03.227]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:58:03.227]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:58:03.227]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:58:03.228]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:58:03.228]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:58:03.228]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:58:03.228]      __var FLASH_CR_Value = 0 ;
[09:58:03.228]        // -> [FLASH_CR_Value <= 0x00000000]
[09:58:03.229]      __var DoDebugPortStop = 1 ;
[09:58:03.229]        // -> [DoDebugPortStop <= 0x00000001]
[09:58:03.229]      __var DP_CTRL_STAT = 0x4 ;
[09:58:03.229]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:58:03.229]      __var DP_SELECT = 0x8 ;
[09:58:03.229]        // -> [DP_SELECT <= 0x00000008]
[09:58:03.230]    </block>
[09:58:03.230]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:58:03.230]      // if-block "connectionFlash && DoOptionByteLoading"
[09:58:03.230]        // =>  FALSE
[09:58:03.230]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:58:03.231]    </control>
[09:58:03.231]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:58:03.231]      // if-block "DoDebugPortStop"
[09:58:03.231]        // =>  TRUE
[09:58:03.232]      <block atomic="false" info="">
[09:58:03.232]        WriteDP(DP_SELECT, 0x00000000);
[09:58:03.232]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:58:03.233]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:58:03.233]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:58:03.233]      </block>
[09:58:03.234]      // end if-block "DoDebugPortStop"
[09:58:03.234]    </control>
[09:58:03.234]  </sequence>
[09:58:03.235]  
[09:58:07.949]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:58:07.949]  
[09:58:07.949]  <debugvars>
[09:58:07.949]    // Pre-defined
[09:58:07.950]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:58:07.950]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:58:07.950]    __dp=0x00000000
[09:58:07.951]    __ap=0x00000000
[09:58:07.951]    __traceout=0x00000000      (Trace Disabled)
[09:58:07.951]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:58:07.951]    __FlashAddr=0x00000000
[09:58:07.951]    __FlashLen=0x00000000
[09:58:07.952]    __FlashArg=0x00000000
[09:58:07.952]    __FlashOp=0x00000000
[09:58:07.952]    __Result=0x00000000
[09:58:07.952]    
[09:58:07.952]    // User-defined
[09:58:07.952]    DbgMCU_CR=0x00000007
[09:58:07.953]    DbgMCU_APB1_Fz=0x00000000
[09:58:07.953]    DbgMCU_APB2_Fz=0x00000000
[09:58:07.953]    DoOptionByteLoading=0x00000000
[09:58:07.953]  </debugvars>
[09:58:07.953]  
[09:58:07.954]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:58:07.954]    <block atomic="false" info="">
[09:58:07.954]      Sequence("CheckID");
[09:58:07.954]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:58:07.954]          <block atomic="false" info="">
[09:58:07.954]            __var pidr1 = 0;
[09:58:07.955]              // -> [pidr1 <= 0x00000000]
[09:58:07.955]            __var pidr2 = 0;
[09:58:07.955]              // -> [pidr2 <= 0x00000000]
[09:58:07.955]            __var jep106id = 0;
[09:58:07.955]              // -> [jep106id <= 0x00000000]
[09:58:07.955]            __var ROMTableBase = 0;
[09:58:07.956]              // -> [ROMTableBase <= 0x00000000]
[09:58:07.956]            __ap = 0;      // AHB-AP
[09:58:07.956]              // -> [__ap <= 0x00000000]
[09:58:07.956]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:58:07.957]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:58:07.957]              // -> [ROMTableBase <= 0xF0000000]
[09:58:07.957]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:58:07.958]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:58:07.959]              // -> [pidr1 <= 0x00000004]
[09:58:07.959]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:58:07.960]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:58:07.960]              // -> [pidr2 <= 0x0000000A]
[09:58:07.960]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:58:07.962]              // -> [jep106id <= 0x00000020]
[09:58:07.962]          </block>
[09:58:07.962]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:58:07.962]            // if-block "jep106id != 0x20"
[09:58:07.963]              // =>  FALSE
[09:58:07.964]            // skip if-block "jep106id != 0x20"
[09:58:07.964]          </control>
[09:58:07.965]        </sequence>
[09:58:07.965]    </block>
[09:58:07.966]  </sequence>
[09:58:07.966]  
[09:58:07.979]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:58:07.979]  
[09:58:07.979]  <debugvars>
[09:58:07.980]    // Pre-defined
[09:58:07.980]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:58:07.980]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:58:07.980]    __dp=0x00000000
[09:58:07.981]    __ap=0x00000000
[09:58:07.981]    __traceout=0x00000000      (Trace Disabled)
[09:58:07.982]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:58:07.982]    __FlashAddr=0x00000000
[09:58:07.982]    __FlashLen=0x00000000
[09:58:07.982]    __FlashArg=0x00000000
[09:58:07.983]    __FlashOp=0x00000000
[09:58:07.983]    __Result=0x00000000
[09:58:07.983]    
[09:58:07.983]    // User-defined
[09:58:07.984]    DbgMCU_CR=0x00000007
[09:58:07.984]    DbgMCU_APB1_Fz=0x00000000
[09:58:07.984]    DbgMCU_APB2_Fz=0x00000000
[09:58:07.984]    DoOptionByteLoading=0x00000000
[09:58:07.985]  </debugvars>
[09:58:07.985]  
[09:58:07.985]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:58:07.986]    <block atomic="false" info="">
[09:58:07.986]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:58:07.987]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:58:07.987]    </block>
[09:58:07.987]    <block atomic="false" info="DbgMCU registers">
[09:58:07.987]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:58:07.988]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[09:58:07.989]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[09:58:07.989]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:58:07.990]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:58:07.991]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:58:07.991]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:58:07.992]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:58:07.993]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:58:07.993]    </block>
[09:58:07.993]  </sequence>
[09:58:07.993]  
[09:58:21.701]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:58:21.701]  
[09:58:21.702]  <debugvars>
[09:58:21.702]    // Pre-defined
[09:58:21.702]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:58:21.703]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:58:21.703]    __dp=0x00000000
[09:58:21.703]    __ap=0x00000000
[09:58:21.703]    __traceout=0x00000000      (Trace Disabled)
[09:58:21.703]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:58:21.705]    __FlashAddr=0x00000000
[09:58:21.705]    __FlashLen=0x00000000
[09:58:21.705]    __FlashArg=0x00000000
[09:58:21.705]    __FlashOp=0x00000000
[09:58:21.706]    __Result=0x00000000
[09:58:21.706]    
[09:58:21.706]    // User-defined
[09:58:21.706]    DbgMCU_CR=0x00000007
[09:58:21.706]    DbgMCU_APB1_Fz=0x00000000
[09:58:21.707]    DbgMCU_APB2_Fz=0x00000000
[09:58:21.707]    DoOptionByteLoading=0x00000000
[09:58:21.707]  </debugvars>
[09:58:21.707]  
[09:58:21.707]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:58:21.708]    <block atomic="false" info="">
[09:58:21.708]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:58:21.708]        // -> [connectionFlash <= 0x00000001]
[09:58:21.708]      __var FLASH_BASE = 0x40022000 ;
[09:58:21.708]        // -> [FLASH_BASE <= 0x40022000]
[09:58:21.708]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:58:21.709]        // -> [FLASH_CR <= 0x40022004]
[09:58:21.709]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:58:21.709]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:58:21.709]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:58:21.710]        // -> [LOCK_BIT <= 0x00000001]
[09:58:21.710]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:58:21.710]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:58:21.711]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:58:21.711]        // -> [FLASH_KEYR <= 0x4002200C]
[09:58:21.711]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:58:21.711]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:58:21.711]      __var FLASH_KEY2 = 0x02030405 ;
[09:58:21.711]        // -> [FLASH_KEY2 <= 0x02030405]
[09:58:21.712]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:58:21.712]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:58:21.712]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:58:21.712]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:58:21.712]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:58:21.713]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:58:21.713]      __var FLASH_CR_Value = 0 ;
[09:58:21.713]        // -> [FLASH_CR_Value <= 0x00000000]
[09:58:21.713]      __var DoDebugPortStop = 1 ;
[09:58:21.713]        // -> [DoDebugPortStop <= 0x00000001]
[09:58:21.713]      __var DP_CTRL_STAT = 0x4 ;
[09:58:21.714]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:58:21.714]      __var DP_SELECT = 0x8 ;
[09:58:21.715]        // -> [DP_SELECT <= 0x00000008]
[09:58:21.715]    </block>
[09:58:21.715]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:58:21.715]      // if-block "connectionFlash && DoOptionByteLoading"
[09:58:21.715]        // =>  FALSE
[09:58:21.716]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:58:21.716]    </control>
[09:58:21.716]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:58:21.716]      // if-block "DoDebugPortStop"
[09:58:21.717]        // =>  TRUE
[09:58:21.717]      <block atomic="false" info="">
[09:58:21.717]        WriteDP(DP_SELECT, 0x00000000);
[09:58:21.718]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:58:21.719]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:58:21.720]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:58:21.720]      </block>
[09:58:21.720]      // end if-block "DoDebugPortStop"
[09:58:21.721]    </control>
[09:58:21.721]  </sequence>
[09:58:21.721]  
[09:58:25.476]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:58:25.476]  
[09:58:25.477]  <debugvars>
[09:58:25.477]    // Pre-defined
[09:58:25.477]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:58:25.478]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:58:25.478]    __dp=0x00000000
[09:58:25.478]    __ap=0x00000000
[09:58:25.479]    __traceout=0x00000000      (Trace Disabled)
[09:58:25.479]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:58:25.479]    __FlashAddr=0x00000000
[09:58:25.480]    __FlashLen=0x00000000
[09:58:25.480]    __FlashArg=0x00000000
[09:58:25.480]    __FlashOp=0x00000000
[09:58:25.480]    __Result=0x00000000
[09:58:25.481]    
[09:58:25.481]    // User-defined
[09:58:25.481]    DbgMCU_CR=0x00000007
[09:58:25.481]    DbgMCU_APB1_Fz=0x00000000
[09:58:25.481]    DbgMCU_APB2_Fz=0x00000000
[09:58:25.481]    DoOptionByteLoading=0x00000000
[09:58:25.481]  </debugvars>
[09:58:25.481]  
[09:58:25.481]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:58:25.481]    <block atomic="false" info="">
[09:58:25.482]      Sequence("CheckID");
[09:58:25.482]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:58:25.483]          <block atomic="false" info="">
[09:58:25.483]            __var pidr1 = 0;
[09:58:25.483]              // -> [pidr1 <= 0x00000000]
[09:58:25.483]            __var pidr2 = 0;
[09:58:25.485]              // -> [pidr2 <= 0x00000000]
[09:58:25.485]            __var jep106id = 0;
[09:58:25.485]              // -> [jep106id <= 0x00000000]
[09:58:25.485]            __var ROMTableBase = 0;
[09:58:25.486]              // -> [ROMTableBase <= 0x00000000]
[09:58:25.486]            __ap = 0;      // AHB-AP
[09:58:25.486]              // -> [__ap <= 0x00000000]
[09:58:25.487]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:58:25.487]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:58:25.487]              // -> [ROMTableBase <= 0xF0000000]
[09:58:25.488]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:58:25.489]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:58:25.489]              // -> [pidr1 <= 0x00000004]
[09:58:25.489]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:58:25.490]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:58:25.490]              // -> [pidr2 <= 0x0000000A]
[09:58:25.491]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:58:25.491]              // -> [jep106id <= 0x00000020]
[09:58:25.491]          </block>
[09:58:25.491]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:58:25.493]            // if-block "jep106id != 0x20"
[09:58:25.493]              // =>  FALSE
[09:58:25.493]            // skip if-block "jep106id != 0x20"
[09:58:25.493]          </control>
[09:58:25.494]        </sequence>
[09:58:25.494]    </block>
[09:58:25.494]  </sequence>
[09:58:25.494]  
[09:58:25.506]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:58:25.506]  
[09:58:25.521]  <debugvars>
[09:58:25.523]    // Pre-defined
[09:58:25.523]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:58:25.523]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:58:25.524]    __dp=0x00000000
[09:58:25.524]    __ap=0x00000000
[09:58:25.524]    __traceout=0x00000000      (Trace Disabled)
[09:58:25.524]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:58:25.525]    __FlashAddr=0x00000000
[09:58:25.525]    __FlashLen=0x00000000
[09:58:25.525]    __FlashArg=0x00000000
[09:58:25.525]    __FlashOp=0x00000000
[09:58:25.526]    __Result=0x00000000
[09:58:25.526]    
[09:58:25.526]    // User-defined
[09:58:25.526]    DbgMCU_CR=0x00000007
[09:58:25.526]    DbgMCU_APB1_Fz=0x00000000
[09:58:25.527]    DbgMCU_APB2_Fz=0x00000000
[09:58:25.527]    DoOptionByteLoading=0x00000000
[09:58:25.527]  </debugvars>
[09:58:25.527]  
[09:58:25.527]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:58:25.528]    <block atomic="false" info="">
[09:58:25.528]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:58:25.529]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:58:25.529]    </block>
[09:58:25.529]    <block atomic="false" info="DbgMCU registers">
[09:58:25.530]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:58:25.531]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[09:58:25.531]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[09:58:25.532]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:58:25.533]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:58:25.533]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:58:25.533]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:58:25.535]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:58:25.535]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:58:25.536]    </block>
[09:58:25.536]  </sequence>
[09:58:25.536]  
[09:58:33.605]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:58:33.605]  
[09:58:33.607]  <debugvars>
[09:58:33.607]    // Pre-defined
[09:58:33.608]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:58:33.608]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:58:33.609]    __dp=0x00000000
[09:58:33.610]    __ap=0x00000000
[09:58:33.610]    __traceout=0x00000000      (Trace Disabled)
[09:58:33.611]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:58:33.611]    __FlashAddr=0x00000000
[09:58:33.612]    __FlashLen=0x00000000
[09:58:33.612]    __FlashArg=0x00000000
[09:58:33.612]    __FlashOp=0x00000000
[09:58:33.613]    __Result=0x00000000
[09:58:33.613]    
[09:58:33.613]    // User-defined
[09:58:33.613]    DbgMCU_CR=0x00000007
[09:58:33.613]    DbgMCU_APB1_Fz=0x00000000
[09:58:33.615]    DbgMCU_APB2_Fz=0x00000000
[09:58:33.615]    DoOptionByteLoading=0x00000000
[09:58:33.615]  </debugvars>
[09:58:33.615]  
[09:58:33.616]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:58:33.616]    <block atomic="false" info="">
[09:58:33.617]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:58:33.617]        // -> [connectionFlash <= 0x00000001]
[09:58:33.618]      __var FLASH_BASE = 0x40022000 ;
[09:58:33.618]        // -> [FLASH_BASE <= 0x40022000]
[09:58:33.618]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:58:33.618]        // -> [FLASH_CR <= 0x40022004]
[09:58:33.619]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:58:33.619]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:58:33.619]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:58:33.620]        // -> [LOCK_BIT <= 0x00000001]
[09:58:33.620]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:58:33.620]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:58:33.621]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:58:33.621]        // -> [FLASH_KEYR <= 0x4002200C]
[09:58:33.621]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:58:33.621]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:58:33.621]      __var FLASH_KEY2 = 0x02030405 ;
[09:58:33.622]        // -> [FLASH_KEY2 <= 0x02030405]
[09:58:33.622]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:58:33.622]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:58:33.622]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:58:33.623]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:58:33.623]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:58:33.623]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:58:33.623]      __var FLASH_CR_Value = 0 ;
[09:58:33.624]        // -> [FLASH_CR_Value <= 0x00000000]
[09:58:33.624]      __var DoDebugPortStop = 1 ;
[09:58:33.624]        // -> [DoDebugPortStop <= 0x00000001]
[09:58:33.624]      __var DP_CTRL_STAT = 0x4 ;
[09:58:33.625]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:58:33.625]      __var DP_SELECT = 0x8 ;
[09:58:33.625]        // -> [DP_SELECT <= 0x00000008]
[09:58:33.625]    </block>
[09:58:33.626]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:58:33.626]      // if-block "connectionFlash && DoOptionByteLoading"
[09:58:33.626]        // =>  FALSE
[09:58:33.626]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:58:33.626]    </control>
[09:58:33.626]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:58:33.627]      // if-block "DoDebugPortStop"
[09:58:33.627]        // =>  TRUE
[09:58:33.627]      <block atomic="false" info="">
[09:58:33.627]        WriteDP(DP_SELECT, 0x00000000);
[09:58:33.628]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:58:33.628]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:58:33.628]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:58:33.629]      </block>
[09:58:33.629]      // end if-block "DoDebugPortStop"
[09:58:33.629]    </control>
[09:58:33.629]  </sequence>
[09:58:33.629]  
[10:13:33.551]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:13:33.551]  
[10:13:33.551]  <debugvars>
[10:13:33.551]    // Pre-defined
[10:13:33.552]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:13:33.552]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:13:33.553]    __dp=0x00000000
[10:13:33.554]    __ap=0x00000000
[10:13:33.554]    __traceout=0x00000000      (Trace Disabled)
[10:13:33.554]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:13:33.555]    __FlashAddr=0x00000000
[10:13:33.555]    __FlashLen=0x00000000
[10:13:33.555]    __FlashArg=0x00000000
[10:13:33.556]    __FlashOp=0x00000000
[10:13:33.556]    __Result=0x00000000
[10:13:33.556]    
[10:13:33.556]    // User-defined
[10:13:33.556]    DbgMCU_CR=0x00000007
[10:13:33.557]    DbgMCU_APB1_Fz=0x00000000
[10:13:33.557]    DbgMCU_APB2_Fz=0x00000000
[10:13:33.557]    DoOptionByteLoading=0x00000000
[10:13:33.557]  </debugvars>
[10:13:33.557]  
[10:13:33.557]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:13:33.557]    <block atomic="false" info="">
[10:13:33.557]      Sequence("CheckID");
[10:13:33.557]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:13:33.557]          <block atomic="false" info="">
[10:13:33.559]            __var pidr1 = 0;
[10:13:33.559]              // -> [pidr1 <= 0x00000000]
[10:13:33.559]            __var pidr2 = 0;
[10:13:33.559]              // -> [pidr2 <= 0x00000000]
[10:13:33.560]            __var jep106id = 0;
[10:13:33.560]              // -> [jep106id <= 0x00000000]
[10:13:33.560]            __var ROMTableBase = 0;
[10:13:33.561]              // -> [ROMTableBase <= 0x00000000]
[10:13:33.561]            __ap = 0;      // AHB-AP
[10:13:33.561]              // -> [__ap <= 0x00000000]
[10:13:33.562]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:13:33.562]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:13:33.563]              // -> [ROMTableBase <= 0xF0000000]
[10:13:33.563]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:13:33.564]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:13:33.564]              // -> [pidr1 <= 0x00000004]
[10:13:33.565]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:13:33.565]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:13:33.565]              // -> [pidr2 <= 0x0000000A]
[10:13:33.566]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:13:33.566]              // -> [jep106id <= 0x00000020]
[10:13:33.566]          </block>
[10:13:33.567]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:13:33.567]            // if-block "jep106id != 0x20"
[10:13:33.567]              // =>  FALSE
[10:13:33.568]            // skip if-block "jep106id != 0x20"
[10:13:33.568]          </control>
[10:13:33.569]        </sequence>
[10:13:33.569]    </block>
[10:13:33.569]  </sequence>
[10:13:33.570]  
[10:13:33.582]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:13:33.582]  
[10:13:33.600]  <debugvars>
[10:13:33.601]    // Pre-defined
[10:13:33.601]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:13:33.602]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:13:33.602]    __dp=0x00000000
[10:13:33.603]    __ap=0x00000000
[10:13:33.603]    __traceout=0x00000000      (Trace Disabled)
[10:13:33.605]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:13:33.606]    __FlashAddr=0x00000000
[10:13:33.606]    __FlashLen=0x00000000
[10:13:33.607]    __FlashArg=0x00000000
[10:13:33.608]    __FlashOp=0x00000000
[10:13:33.609]    __Result=0x00000000
[10:13:33.609]    
[10:13:33.609]    // User-defined
[10:13:33.610]    DbgMCU_CR=0x00000007
[10:13:33.611]    DbgMCU_APB1_Fz=0x00000000
[10:13:33.611]    DbgMCU_APB2_Fz=0x00000000
[10:13:33.612]    DoOptionByteLoading=0x00000000
[10:13:33.613]  </debugvars>
[10:13:33.614]  
[10:13:33.614]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:13:33.615]    <block atomic="false" info="">
[10:13:33.616]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:13:33.618]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:13:33.619]    </block>
[10:13:33.619]    <block atomic="false" info="DbgMCU registers">
[10:13:33.620]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:13:33.622]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[10:13:33.624]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[10:13:33.625]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:13:33.627]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:13:33.627]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:13:33.628]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:13:33.629]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:13:33.630]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:13:33.631]    </block>
[10:13:33.631]  </sequence>
[10:13:33.632]  
[10:13:41.765]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:13:41.765]  
[10:13:41.766]  <debugvars>
[10:13:41.767]    // Pre-defined
[10:13:41.768]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:13:41.768]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:13:41.769]    __dp=0x00000000
[10:13:41.769]    __ap=0x00000000
[10:13:41.770]    __traceout=0x00000000      (Trace Disabled)
[10:13:41.771]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:13:41.771]    __FlashAddr=0x00000000
[10:13:41.772]    __FlashLen=0x00000000
[10:13:41.772]    __FlashArg=0x00000000
[10:13:41.772]    __FlashOp=0x00000000
[10:13:41.772]    __Result=0x00000000
[10:13:41.773]    
[10:13:41.773]    // User-defined
[10:13:41.774]    DbgMCU_CR=0x00000007
[10:13:41.775]    DbgMCU_APB1_Fz=0x00000000
[10:13:41.776]    DbgMCU_APB2_Fz=0x00000000
[10:13:41.776]    DoOptionByteLoading=0x00000000
[10:13:41.777]  </debugvars>
[10:13:41.777]  
[10:13:41.778]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:13:41.778]    <block atomic="false" info="">
[10:13:41.779]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:13:41.779]        // -> [connectionFlash <= 0x00000001]
[10:13:41.780]      __var FLASH_BASE = 0x40022000 ;
[10:13:41.780]        // -> [FLASH_BASE <= 0x40022000]
[10:13:41.780]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:13:41.781]        // -> [FLASH_CR <= 0x40022004]
[10:13:41.781]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:13:41.782]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:13:41.782]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:13:41.783]        // -> [LOCK_BIT <= 0x00000001]
[10:13:41.783]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:13:41.783]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:13:41.784]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:13:41.784]        // -> [FLASH_KEYR <= 0x4002200C]
[10:13:41.785]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:13:41.785]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:13:41.786]      __var FLASH_KEY2 = 0x02030405 ;
[10:13:41.786]        // -> [FLASH_KEY2 <= 0x02030405]
[10:13:41.786]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:13:41.786]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:13:41.787]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:13:41.787]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:13:41.787]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:13:41.788]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:13:41.788]      __var FLASH_CR_Value = 0 ;
[10:13:41.788]        // -> [FLASH_CR_Value <= 0x00000000]
[10:13:41.789]      __var DoDebugPortStop = 1 ;
[10:13:41.789]        // -> [DoDebugPortStop <= 0x00000001]
[10:13:41.789]      __var DP_CTRL_STAT = 0x4 ;
[10:13:41.789]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:13:41.790]      __var DP_SELECT = 0x8 ;
[10:13:41.790]        // -> [DP_SELECT <= 0x00000008]
[10:13:41.790]    </block>
[10:13:41.790]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:13:41.790]      // if-block "connectionFlash && DoOptionByteLoading"
[10:13:41.791]        // =>  FALSE
[10:13:41.791]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:13:41.791]    </control>
[10:13:41.791]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:13:41.791]      // if-block "DoDebugPortStop"
[10:13:41.792]        // =>  TRUE
[10:13:41.792]      <block atomic="false" info="">
[10:13:41.792]        WriteDP(DP_SELECT, 0x00000000);
[10:13:41.792]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:13:41.793]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:13:41.793]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:13:41.793]      </block>
[10:13:41.794]      // end if-block "DoDebugPortStop"
[10:13:41.794]    </control>
[10:13:41.794]  </sequence>
[10:13:41.794]  
[10:15:28.373]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:15:28.373]  
[10:15:28.374]  <debugvars>
[10:15:28.374]    // Pre-defined
[10:15:28.374]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:15:28.374]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:15:28.376]    __dp=0x00000000
[10:15:28.376]    __ap=0x00000000
[10:15:28.376]    __traceout=0x00000000      (Trace Disabled)
[10:15:28.376]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:15:28.377]    __FlashAddr=0x00000000
[10:15:28.377]    __FlashLen=0x00000000
[10:15:28.377]    __FlashArg=0x00000000
[10:15:28.377]    __FlashOp=0x00000000
[10:15:28.377]    __Result=0x00000000
[10:15:28.377]    
[10:15:28.377]    // User-defined
[10:15:28.378]    DbgMCU_CR=0x00000007
[10:15:28.378]    DbgMCU_APB1_Fz=0x00000000
[10:15:28.378]    DbgMCU_APB2_Fz=0x00000000
[10:15:28.379]    DoOptionByteLoading=0x00000000
[10:15:28.379]  </debugvars>
[10:15:28.379]  
[10:15:28.380]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:15:28.380]    <block atomic="false" info="">
[10:15:28.380]      Sequence("CheckID");
[10:15:28.381]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:15:28.381]          <block atomic="false" info="">
[10:15:28.381]            __var pidr1 = 0;
[10:15:28.381]              // -> [pidr1 <= 0x00000000]
[10:15:28.381]            __var pidr2 = 0;
[10:15:28.382]              // -> [pidr2 <= 0x00000000]
[10:15:28.382]            __var jep106id = 0;
[10:15:28.382]              // -> [jep106id <= 0x00000000]
[10:15:28.382]            __var ROMTableBase = 0;
[10:15:28.382]              // -> [ROMTableBase <= 0x00000000]
[10:15:28.383]            __ap = 0;      // AHB-AP
[10:15:28.383]              // -> [__ap <= 0x00000000]
[10:15:28.383]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:15:28.383]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:15:28.384]              // -> [ROMTableBase <= 0xF0000000]
[10:15:28.384]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:15:28.385]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:15:28.386]              // -> [pidr1 <= 0x00000004]
[10:15:28.386]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:15:28.387]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:15:28.387]              // -> [pidr2 <= 0x0000000A]
[10:15:28.388]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:15:28.388]              // -> [jep106id <= 0x00000020]
[10:15:28.388]          </block>
[10:15:28.388]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:15:28.389]            // if-block "jep106id != 0x20"
[10:15:28.389]              // =>  FALSE
[10:15:28.389]            // skip if-block "jep106id != 0x20"
[10:15:28.389]          </control>
[10:15:28.389]        </sequence>
[10:15:28.390]    </block>
[10:15:28.390]  </sequence>
[10:15:28.390]  
[10:15:28.402]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:15:28.402]  
[10:15:28.402]  <debugvars>
[10:15:28.403]    // Pre-defined
[10:15:28.403]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:15:28.403]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:15:28.404]    __dp=0x00000000
[10:15:28.404]    __ap=0x00000000
[10:15:28.404]    __traceout=0x00000000      (Trace Disabled)
[10:15:28.405]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:15:28.405]    __FlashAddr=0x00000000
[10:15:28.405]    __FlashLen=0x00000000
[10:15:28.405]    __FlashArg=0x00000000
[10:15:28.405]    __FlashOp=0x00000000
[10:15:28.406]    __Result=0x00000000
[10:15:28.406]    
[10:15:28.406]    // User-defined
[10:15:28.406]    DbgMCU_CR=0x00000007
[10:15:28.406]    DbgMCU_APB1_Fz=0x00000000
[10:15:28.406]    DbgMCU_APB2_Fz=0x00000000
[10:15:28.406]    DoOptionByteLoading=0x00000000
[10:15:28.407]  </debugvars>
[10:15:28.407]  
[10:15:28.407]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:15:28.407]    <block atomic="false" info="">
[10:15:28.408]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:15:28.409]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:15:28.409]    </block>
[10:15:28.409]    <block atomic="false" info="DbgMCU registers">
[10:15:28.410]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:15:28.411]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[10:15:28.411]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[10:15:28.412]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:15:28.413]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:15:28.413]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:15:28.414]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:15:28.414]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:15:28.415]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:15:28.415]    </block>
[10:15:28.416]  </sequence>
[10:15:28.416]  
[10:15:36.432]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:15:36.432]  
[10:15:36.433]  <debugvars>
[10:15:36.434]    // Pre-defined
[10:15:36.435]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:15:36.435]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:15:36.435]    __dp=0x00000000
[10:15:36.436]    __ap=0x00000000
[10:15:36.437]    __traceout=0x00000000      (Trace Disabled)
[10:15:36.437]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:15:36.438]    __FlashAddr=0x00000000
[10:15:36.438]    __FlashLen=0x00000000
[10:15:36.439]    __FlashArg=0x00000000
[10:15:36.439]    __FlashOp=0x00000000
[10:15:36.440]    __Result=0x00000000
[10:15:36.441]    
[10:15:36.441]    // User-defined
[10:15:36.441]    DbgMCU_CR=0x00000007
[10:15:36.442]    DbgMCU_APB1_Fz=0x00000000
[10:15:36.442]    DbgMCU_APB2_Fz=0x00000000
[10:15:36.443]    DoOptionByteLoading=0x00000000
[10:15:36.443]  </debugvars>
[10:15:36.444]  
[10:15:36.444]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:15:36.445]    <block atomic="false" info="">
[10:15:36.445]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:15:36.446]        // -> [connectionFlash <= 0x00000001]
[10:15:36.446]      __var FLASH_BASE = 0x40022000 ;
[10:15:36.447]        // -> [FLASH_BASE <= 0x40022000]
[10:15:36.447]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:15:36.448]        // -> [FLASH_CR <= 0x40022004]
[10:15:36.448]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:15:36.449]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:15:36.449]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:15:36.450]        // -> [LOCK_BIT <= 0x00000001]
[10:15:36.450]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:15:36.450]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:15:36.451]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:15:36.452]        // -> [FLASH_KEYR <= 0x4002200C]
[10:15:36.452]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:15:36.452]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:15:36.453]      __var FLASH_KEY2 = 0x02030405 ;
[10:15:36.453]        // -> [FLASH_KEY2 <= 0x02030405]
[10:15:36.454]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:15:36.454]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:15:36.455]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:15:36.455]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:15:36.456]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:15:36.456]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:15:36.457]      __var FLASH_CR_Value = 0 ;
[10:15:36.457]        // -> [FLASH_CR_Value <= 0x00000000]
[10:15:36.458]      __var DoDebugPortStop = 1 ;
[10:15:36.458]        // -> [DoDebugPortStop <= 0x00000001]
[10:15:36.459]      __var DP_CTRL_STAT = 0x4 ;
[10:15:36.460]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:15:36.460]      __var DP_SELECT = 0x8 ;
[10:15:36.461]        // -> [DP_SELECT <= 0x00000008]
[10:15:36.461]    </block>
[10:15:36.462]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:15:36.462]      // if-block "connectionFlash && DoOptionByteLoading"
[10:15:36.463]        // =>  FALSE
[10:15:36.463]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:15:36.464]    </control>
[10:15:36.464]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:15:36.465]      // if-block "DoDebugPortStop"
[10:15:36.465]        // =>  TRUE
[10:15:36.466]      <block atomic="false" info="">
[10:15:36.466]        WriteDP(DP_SELECT, 0x00000000);
[10:15:36.466]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:15:36.466]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:15:36.468]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:15:36.468]      </block>
[10:15:36.468]      // end if-block "DoDebugPortStop"
[10:15:36.469]    </control>
[10:15:36.469]  </sequence>
[10:15:36.470]  
[10:20:44.230]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:20:44.230]  
[10:20:44.230]  <debugvars>
[10:20:44.231]    // Pre-defined
[10:20:44.231]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:20:44.231]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:20:44.232]    __dp=0x00000000
[10:20:44.232]    __ap=0x00000000
[10:20:44.232]    __traceout=0x00000000      (Trace Disabled)
[10:20:44.232]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:20:44.232]    __FlashAddr=0x00000000
[10:20:44.234]    __FlashLen=0x00000000
[10:20:44.234]    __FlashArg=0x00000000
[10:20:44.234]    __FlashOp=0x00000000
[10:20:44.235]    __Result=0x00000000
[10:20:44.235]    
[10:20:44.235]    // User-defined
[10:20:44.235]    DbgMCU_CR=0x00000007
[10:20:44.235]    DbgMCU_APB1_Fz=0x00000000
[10:20:44.236]    DbgMCU_APB2_Fz=0x00000000
[10:20:44.236]    DoOptionByteLoading=0x00000000
[10:20:44.236]  </debugvars>
[10:20:44.237]  
[10:20:44.237]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:20:44.237]    <block atomic="false" info="">
[10:20:44.238]      Sequence("CheckID");
[10:20:44.238]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:20:44.238]          <block atomic="false" info="">
[10:20:44.239]            __var pidr1 = 0;
[10:20:44.239]              // -> [pidr1 <= 0x00000000]
[10:20:44.239]            __var pidr2 = 0;
[10:20:44.239]              // -> [pidr2 <= 0x00000000]
[10:20:44.239]            __var jep106id = 0;
[10:20:44.240]              // -> [jep106id <= 0x00000000]
[10:20:44.240]            __var ROMTableBase = 0;
[10:20:44.240]              // -> [ROMTableBase <= 0x00000000]
[10:20:44.240]            __ap = 0;      // AHB-AP
[10:20:44.240]              // -> [__ap <= 0x00000000]
[10:20:44.240]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:20:44.241]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:20:44.242]              // -> [ROMTableBase <= 0xF0000000]
[10:20:44.243]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:20:44.244]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:20:44.244]              // -> [pidr1 <= 0x00000004]
[10:20:44.245]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:20:44.245]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:20:44.246]              // -> [pidr2 <= 0x0000000A]
[10:20:44.246]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:20:44.246]              // -> [jep106id <= 0x00000020]
[10:20:44.247]          </block>
[10:20:44.247]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:20:44.247]            // if-block "jep106id != 0x20"
[10:20:44.248]              // =>  FALSE
[10:20:44.248]            // skip if-block "jep106id != 0x20"
[10:20:44.248]          </control>
[10:20:44.248]        </sequence>
[10:20:44.248]    </block>
[10:20:44.249]  </sequence>
[10:20:44.249]  
[10:20:44.261]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:20:44.261]  
[10:20:44.280]  <debugvars>
[10:20:44.280]    // Pre-defined
[10:20:44.280]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:20:44.281]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:20:44.281]    __dp=0x00000000
[10:20:44.281]    __ap=0x00000000
[10:20:44.281]    __traceout=0x00000000      (Trace Disabled)
[10:20:44.283]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:20:44.283]    __FlashAddr=0x00000000
[10:20:44.283]    __FlashLen=0x00000000
[10:20:44.283]    __FlashArg=0x00000000
[10:20:44.283]    __FlashOp=0x00000000
[10:20:44.284]    __Result=0x00000000
[10:20:44.284]    
[10:20:44.284]    // User-defined
[10:20:44.284]    DbgMCU_CR=0x00000007
[10:20:44.284]    DbgMCU_APB1_Fz=0x00000000
[10:20:44.285]    DbgMCU_APB2_Fz=0x00000000
[10:20:44.285]    DoOptionByteLoading=0x00000000
[10:20:44.285]  </debugvars>
[10:20:44.285]  
[10:20:44.285]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:20:44.285]    <block atomic="false" info="">
[10:20:44.285]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:20:44.286]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:20:44.287]    </block>
[10:20:44.287]    <block atomic="false" info="DbgMCU registers">
[10:20:44.287]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:20:44.288]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[10:20:44.289]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[10:20:44.289]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:20:44.290]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:20:44.290]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:20:44.292]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:20:44.292]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:20:44.293]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:20:44.293]    </block>
[10:20:44.293]  </sequence>
[10:20:44.293]  
[10:20:46.336]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:20:46.336]  
[10:20:46.336]  <debugvars>
[10:20:46.337]    // Pre-defined
[10:20:46.337]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:20:46.337]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:20:46.337]    __dp=0x00000000
[10:20:46.338]    __ap=0x00000000
[10:20:46.338]    __traceout=0x00000000      (Trace Disabled)
[10:20:46.338]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:20:46.338]    __FlashAddr=0x00000000
[10:20:46.338]    __FlashLen=0x00000000
[10:20:46.339]    __FlashArg=0x00000000
[10:20:46.339]    __FlashOp=0x00000000
[10:20:46.339]    __Result=0x00000000
[10:20:46.339]    
[10:20:46.339]    // User-defined
[10:20:46.339]    DbgMCU_CR=0x00000007
[10:20:46.340]    DbgMCU_APB1_Fz=0x00000000
[10:20:46.340]    DbgMCU_APB2_Fz=0x00000000
[10:20:46.340]    DoOptionByteLoading=0x00000000
[10:20:46.340]  </debugvars>
[10:20:46.340]  
[10:20:46.340]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:20:46.341]    <block atomic="false" info="">
[10:20:46.341]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:20:46.341]        // -> [connectionFlash <= 0x00000001]
[10:20:46.341]      __var FLASH_BASE = 0x40022000 ;
[10:20:46.341]        // -> [FLASH_BASE <= 0x40022000]
[10:20:46.342]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:20:46.342]        // -> [FLASH_CR <= 0x40022004]
[10:20:46.342]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:20:46.342]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:20:46.342]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:20:46.342]        // -> [LOCK_BIT <= 0x00000001]
[10:20:46.343]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:20:46.343]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:20:46.343]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:20:46.343]        // -> [FLASH_KEYR <= 0x4002200C]
[10:20:46.344]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:20:46.344]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:20:46.344]      __var FLASH_KEY2 = 0x02030405 ;
[10:20:46.344]        // -> [FLASH_KEY2 <= 0x02030405]
[10:20:46.345]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:20:46.345]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:20:46.345]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:20:46.345]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:20:46.345]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:20:46.345]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:20:46.346]      __var FLASH_CR_Value = 0 ;
[10:20:46.346]        // -> [FLASH_CR_Value <= 0x00000000]
[10:20:46.346]      __var DoDebugPortStop = 1 ;
[10:20:46.346]        // -> [DoDebugPortStop <= 0x00000001]
[10:20:46.346]      __var DP_CTRL_STAT = 0x4 ;
[10:20:46.346]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:20:46.347]      __var DP_SELECT = 0x8 ;
[10:20:46.347]        // -> [DP_SELECT <= 0x00000008]
[10:20:46.347]    </block>
[10:20:46.347]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:20:46.347]      // if-block "connectionFlash && DoOptionByteLoading"
[10:20:46.348]        // =>  FALSE
[10:20:46.348]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:20:46.348]    </control>
[10:20:46.348]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:20:46.348]      // if-block "DoDebugPortStop"
[10:20:46.348]        // =>  TRUE
[10:20:46.349]      <block atomic="false" info="">
[10:20:46.349]        WriteDP(DP_SELECT, 0x00000000);
[10:20:46.349]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:20:46.349]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:20:46.350]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:20:46.350]      </block>
[10:20:46.350]      // end if-block "DoDebugPortStop"
[10:20:46.350]    </control>
[10:20:46.350]  </sequence>
[10:20:46.351]  
[10:20:52.544]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:20:52.544]  
[10:20:52.545]  <debugvars>
[10:20:52.545]    // Pre-defined
[10:20:52.546]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:20:52.546]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:20:52.546]    __dp=0x00000000
[10:20:52.546]    __ap=0x00000000
[10:20:52.547]    __traceout=0x00000000      (Trace Disabled)
[10:20:52.547]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:20:52.547]    __FlashAddr=0x00000000
[10:20:52.548]    __FlashLen=0x00000000
[10:20:52.548]    __FlashArg=0x00000000
[10:20:52.548]    __FlashOp=0x00000000
[10:20:52.548]    __Result=0x00000000
[10:20:52.549]    
[10:20:52.549]    // User-defined
[10:20:52.549]    DbgMCU_CR=0x00000007
[10:20:52.549]    DbgMCU_APB1_Fz=0x00000000
[10:20:52.549]    DbgMCU_APB2_Fz=0x00000000
[10:20:52.549]    DoOptionByteLoading=0x00000000
[10:20:52.550]  </debugvars>
[10:20:52.550]  
[10:20:52.550]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:20:52.550]    <block atomic="false" info="">
[10:20:52.550]      Sequence("CheckID");
[10:20:52.551]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:20:52.551]          <block atomic="false" info="">
[10:20:52.551]            __var pidr1 = 0;
[10:20:52.551]              // -> [pidr1 <= 0x00000000]
[10:20:52.551]            __var pidr2 = 0;
[10:20:52.552]              // -> [pidr2 <= 0x00000000]
[10:20:52.552]            __var jep106id = 0;
[10:20:52.553]              // -> [jep106id <= 0x00000000]
[10:20:52.553]            __var ROMTableBase = 0;
[10:20:52.553]              // -> [ROMTableBase <= 0x00000000]
[10:20:52.555]            __ap = 0;      // AHB-AP
[10:20:52.555]              // -> [__ap <= 0x00000000]
[10:20:52.555]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:20:52.556]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:20:52.556]              // -> [ROMTableBase <= 0xF0000000]
[10:20:52.556]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:20:52.558]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:20:52.558]              // -> [pidr1 <= 0x00000004]
[10:20:52.558]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:20:52.559]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:20:52.560]              // -> [pidr2 <= 0x0000000A]
[10:20:52.560]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:20:52.560]              // -> [jep106id <= 0x00000020]
[10:20:52.560]          </block>
[10:20:52.561]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:20:52.561]            // if-block "jep106id != 0x20"
[10:20:52.561]              // =>  FALSE
[10:20:52.561]            // skip if-block "jep106id != 0x20"
[10:20:52.561]          </control>
[10:20:52.562]        </sequence>
[10:20:52.562]    </block>
[10:20:52.562]  </sequence>
[10:20:52.562]  
[10:20:52.574]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:20:52.574]  
[10:20:52.574]  <debugvars>
[10:20:52.574]    // Pre-defined
[10:20:52.575]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:20:52.575]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:20:52.575]    __dp=0x00000000
[10:20:52.576]    __ap=0x00000000
[10:20:52.576]    __traceout=0x00000000      (Trace Disabled)
[10:20:52.576]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:20:52.576]    __FlashAddr=0x00000000
[10:20:52.577]    __FlashLen=0x00000000
[10:20:52.577]    __FlashArg=0x00000000
[10:20:52.577]    __FlashOp=0x00000000
[10:20:52.577]    __Result=0x00000000
[10:20:52.577]    
[10:20:52.577]    // User-defined
[10:20:52.578]    DbgMCU_CR=0x00000007
[10:20:52.578]    DbgMCU_APB1_Fz=0x00000000
[10:20:52.578]    DbgMCU_APB2_Fz=0x00000000
[10:20:52.578]    DoOptionByteLoading=0x00000000
[10:20:52.578]  </debugvars>
[10:20:52.578]  
[10:20:52.579]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:20:52.579]    <block atomic="false" info="">
[10:20:52.579]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:20:52.580]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:20:52.580]    </block>
[10:20:52.580]    <block atomic="false" info="DbgMCU registers">
[10:20:52.581]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:20:52.581]        // -> [Read32(0x40021034) => 0x00000200]   (__dp=0x00000000, __ap=0x00000000)
[10:20:52.582]        // -> [Write32(0x40021034, 0x00400200)]   (__dp=0x00000000, __ap=0x00000000)
[10:20:52.582]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:20:52.583]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:20:52.583]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:20:52.585]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:20:52.585]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:20:52.586]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:20:52.587]    </block>
[10:20:52.587]  </sequence>
[10:20:52.587]  
[10:21:00.689]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:21:00.689]  
[10:21:00.689]  <debugvars>
[10:21:00.689]    // Pre-defined
[10:21:00.690]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:21:00.690]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:21:00.690]    __dp=0x00000000
[10:21:00.692]    __ap=0x00000000
[10:21:00.692]    __traceout=0x00000000      (Trace Disabled)
[10:21:00.692]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:21:00.693]    __FlashAddr=0x00000000
[10:21:00.693]    __FlashLen=0x00000000
[10:21:00.693]    __FlashArg=0x00000000
[10:21:00.693]    __FlashOp=0x00000000
[10:21:00.694]    __Result=0x00000000
[10:21:00.694]    
[10:21:00.694]    // User-defined
[10:21:00.694]    DbgMCU_CR=0x00000007
[10:21:00.694]    DbgMCU_APB1_Fz=0x00000000
[10:21:00.695]    DbgMCU_APB2_Fz=0x00000000
[10:21:00.695]    DoOptionByteLoading=0x00000000
[10:21:00.695]  </debugvars>
[10:21:00.695]  
[10:21:00.695]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:21:00.696]    <block atomic="false" info="">
[10:21:00.696]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:21:00.696]        // -> [connectionFlash <= 0x00000001]
[10:21:00.696]      __var FLASH_BASE = 0x40022000 ;
[10:21:00.696]        // -> [FLASH_BASE <= 0x40022000]
[10:21:00.697]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:21:00.697]        // -> [FLASH_CR <= 0x40022004]
[10:21:00.697]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:21:00.697]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:21:00.697]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:21:00.697]        // -> [LOCK_BIT <= 0x00000001]
[10:21:00.698]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:21:00.698]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:21:00.698]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:21:00.698]        // -> [FLASH_KEYR <= 0x4002200C]
[10:21:00.698]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:21:00.699]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:21:00.699]      __var FLASH_KEY2 = 0x02030405 ;
[10:21:00.699]        // -> [FLASH_KEY2 <= 0x02030405]
[10:21:00.699]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:21:00.699]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:21:00.700]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:21:00.700]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:21:00.700]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:21:00.700]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:21:00.701]      __var FLASH_CR_Value = 0 ;
[10:21:00.701]        // -> [FLASH_CR_Value <= 0x00000000]
[10:21:00.701]      __var DoDebugPortStop = 1 ;
[10:21:00.701]        // -> [DoDebugPortStop <= 0x00000001]
[10:21:00.702]      __var DP_CTRL_STAT = 0x4 ;
[10:21:00.702]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:21:00.702]      __var DP_SELECT = 0x8 ;
[10:21:00.702]        // -> [DP_SELECT <= 0x00000008]
[10:21:00.703]    </block>
[10:21:00.703]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:21:00.703]      // if-block "connectionFlash && DoOptionByteLoading"
[10:21:00.703]        // =>  FALSE
[10:21:00.704]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:21:00.704]    </control>
[10:21:00.704]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:21:00.704]      // if-block "DoDebugPortStop"
[10:21:00.704]        // =>  TRUE
[10:21:00.704]      <block atomic="false" info="">
[10:21:00.705]        WriteDP(DP_SELECT, 0x00000000);
[10:21:00.705]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:21:00.705]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:21:00.706]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:21:00.706]      </block>
[10:21:00.706]      // end if-block "DoDebugPortStop"
[10:21:00.706]    </control>
[10:21:00.706]  </sequence>
[10:21:00.706]  
[10:22:18.178]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:22:18.178]  
[10:22:18.178]  <debugvars>
[10:22:18.179]    // Pre-defined
[10:22:18.179]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:22:18.179]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:22:18.180]    __dp=0x00000000
[10:22:18.180]    __ap=0x00000000
[10:22:18.180]    __traceout=0x00000000      (Trace Disabled)
[10:22:18.180]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:22:18.181]    __FlashAddr=0x00000000
[10:22:18.181]    __FlashLen=0x00000000
[10:22:18.181]    __FlashArg=0x00000000
[10:22:18.182]    __FlashOp=0x00000000
[10:22:18.182]    __Result=0x00000000
[10:22:18.182]    
[10:22:18.182]    // User-defined
[10:22:18.182]    DbgMCU_CR=0x00000007
[10:22:18.182]    DbgMCU_APB1_Fz=0x00000000
[10:22:18.182]    DbgMCU_APB2_Fz=0x00000000
[10:22:18.183]    DoOptionByteLoading=0x00000000
[10:22:18.183]  </debugvars>
[10:22:18.183]  
[10:22:18.183]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:22:18.183]    <block atomic="false" info="">
[10:22:18.184]      Sequence("CheckID");
[10:22:18.184]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:22:18.184]          <block atomic="false" info="">
[10:22:18.184]            __var pidr1 = 0;
[10:22:18.184]              // -> [pidr1 <= 0x00000000]
[10:22:18.184]            __var pidr2 = 0;
[10:22:18.185]              // -> [pidr2 <= 0x00000000]
[10:22:18.185]            __var jep106id = 0;
[10:22:18.185]              // -> [jep106id <= 0x00000000]
[10:22:18.185]            __var ROMTableBase = 0;
[10:22:18.185]              // -> [ROMTableBase <= 0x00000000]
[10:22:18.185]            __ap = 0;      // AHB-AP
[10:22:18.187]              // -> [__ap <= 0x00000000]
[10:22:18.187]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:22:18.187]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:22:18.188]              // -> [ROMTableBase <= 0xF0000000]
[10:22:18.188]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:22:18.189]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:22:18.190]              // -> [pidr1 <= 0x00000004]
[10:22:18.190]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:22:18.191]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:22:18.192]              // -> [pidr2 <= 0x0000000A]
[10:22:18.192]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:22:18.192]              // -> [jep106id <= 0x00000020]
[10:22:18.192]          </block>
[10:22:18.193]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:22:18.193]            // if-block "jep106id != 0x20"
[10:22:18.193]              // =>  FALSE
[10:22:18.193]            // skip if-block "jep106id != 0x20"
[10:22:18.193]          </control>
[10:22:18.194]        </sequence>
[10:22:18.194]    </block>
[10:22:18.194]  </sequence>
[10:22:18.194]  
[10:22:18.206]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:22:18.206]  
[10:22:18.222]  <debugvars>
[10:22:18.223]    // Pre-defined
[10:22:18.223]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:22:18.224]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:22:18.224]    __dp=0x00000000
[10:22:18.225]    __ap=0x00000000
[10:22:18.225]    __traceout=0x00000000      (Trace Disabled)
[10:22:18.226]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:22:18.227]    __FlashAddr=0x00000000
[10:22:18.227]    __FlashLen=0x00000000
[10:22:18.228]    __FlashArg=0x00000000
[10:22:18.228]    __FlashOp=0x00000000
[10:22:18.229]    __Result=0x00000000
[10:22:18.229]    
[10:22:18.229]    // User-defined
[10:22:18.230]    DbgMCU_CR=0x00000007
[10:22:18.230]    DbgMCU_APB1_Fz=0x00000000
[10:22:18.231]    DbgMCU_APB2_Fz=0x00000000
[10:22:18.231]    DoOptionByteLoading=0x00000000
[10:22:18.232]  </debugvars>
[10:22:18.232]  
[10:22:18.232]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:22:18.232]    <block atomic="false" info="">
[10:22:18.233]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:22:18.235]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:18.235]    </block>
[10:22:18.235]    <block atomic="false" info="DbgMCU registers">
[10:22:18.235]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:22:18.236]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[10:22:18.237]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:18.237]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:22:18.238]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:18.239]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:22:18.239]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:18.240]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:22:18.240]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:18.241]    </block>
[10:22:18.241]  </sequence>
[10:22:18.241]  
[10:22:20.074]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:22:20.074]  
[10:22:20.075]  <debugvars>
[10:22:20.076]    // Pre-defined
[10:22:20.076]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:22:20.076]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:22:20.076]    __dp=0x00000000
[10:22:20.077]    __ap=0x00000000
[10:22:20.077]    __traceout=0x00000000      (Trace Disabled)
[10:22:20.077]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:22:20.077]    __FlashAddr=0x00000000
[10:22:20.077]    __FlashLen=0x00000000
[10:22:20.078]    __FlashArg=0x00000000
[10:22:20.078]    __FlashOp=0x00000000
[10:22:20.078]    __Result=0x00000000
[10:22:20.078]    
[10:22:20.078]    // User-defined
[10:22:20.078]    DbgMCU_CR=0x00000007
[10:22:20.078]    DbgMCU_APB1_Fz=0x00000000
[10:22:20.079]    DbgMCU_APB2_Fz=0x00000000
[10:22:20.079]    DoOptionByteLoading=0x00000000
[10:22:20.079]  </debugvars>
[10:22:20.079]  
[10:22:20.080]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:22:20.080]    <block atomic="false" info="">
[10:22:20.080]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:22:20.080]        // -> [connectionFlash <= 0x00000001]
[10:22:20.081]      __var FLASH_BASE = 0x40022000 ;
[10:22:20.081]        // -> [FLASH_BASE <= 0x40022000]
[10:22:20.081]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:22:20.081]        // -> [FLASH_CR <= 0x40022004]
[10:22:20.081]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:22:20.082]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:22:20.083]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:22:20.083]        // -> [LOCK_BIT <= 0x00000001]
[10:22:20.083]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:22:20.084]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:22:20.084]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:22:20.084]        // -> [FLASH_KEYR <= 0x4002200C]
[10:22:20.084]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:22:20.085]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:22:20.085]      __var FLASH_KEY2 = 0x02030405 ;
[10:22:20.085]        // -> [FLASH_KEY2 <= 0x02030405]
[10:22:20.085]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:22:20.085]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:22:20.085]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:22:20.086]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:22:20.086]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:22:20.086]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:22:20.088]      __var FLASH_CR_Value = 0 ;
[10:22:20.088]        // -> [FLASH_CR_Value <= 0x00000000]
[10:22:20.088]      __var DoDebugPortStop = 1 ;
[10:22:20.088]        // -> [DoDebugPortStop <= 0x00000001]
[10:22:20.089]      __var DP_CTRL_STAT = 0x4 ;
[10:22:20.089]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:22:20.089]      __var DP_SELECT = 0x8 ;
[10:22:20.089]        // -> [DP_SELECT <= 0x00000008]
[10:22:20.090]    </block>
[10:22:20.090]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:22:20.090]      // if-block "connectionFlash && DoOptionByteLoading"
[10:22:20.090]        // =>  FALSE
[10:22:20.090]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:22:20.090]    </control>
[10:22:20.091]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:22:20.091]      // if-block "DoDebugPortStop"
[10:22:20.091]        // =>  TRUE
[10:22:20.091]      <block atomic="false" info="">
[10:22:20.091]        WriteDP(DP_SELECT, 0x00000000);
[10:22:20.092]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:22:20.092]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:22:20.092]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:22:20.092]      </block>
[10:22:20.092]      // end if-block "DoDebugPortStop"
[10:22:20.092]    </control>
[10:22:20.092]  </sequence>
[10:22:20.093]  
[10:22:24.710]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:22:24.710]  
[10:22:24.710]  <debugvars>
[10:22:24.710]    // Pre-defined
[10:22:24.711]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:22:24.711]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:22:24.711]    __dp=0x00000000
[10:22:24.711]    __ap=0x00000000
[10:22:24.712]    __traceout=0x00000000      (Trace Disabled)
[10:22:24.712]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:22:24.712]    __FlashAddr=0x00000000
[10:22:24.713]    __FlashLen=0x00000000
[10:22:24.713]    __FlashArg=0x00000000
[10:22:24.713]    __FlashOp=0x00000000
[10:22:24.714]    __Result=0x00000000
[10:22:24.714]    
[10:22:24.714]    // User-defined
[10:22:24.714]    DbgMCU_CR=0x00000007
[10:22:24.715]    DbgMCU_APB1_Fz=0x00000000
[10:22:24.715]    DbgMCU_APB2_Fz=0x00000000
[10:22:24.715]    DoOptionByteLoading=0x00000000
[10:22:24.715]  </debugvars>
[10:22:24.715]  
[10:22:24.715]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:22:24.715]    <block atomic="false" info="">
[10:22:24.715]      Sequence("CheckID");
[10:22:24.716]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:22:24.717]          <block atomic="false" info="">
[10:22:24.717]            __var pidr1 = 0;
[10:22:24.717]              // -> [pidr1 <= 0x00000000]
[10:22:24.717]            __var pidr2 = 0;
[10:22:24.717]              // -> [pidr2 <= 0x00000000]
[10:22:24.718]            __var jep106id = 0;
[10:22:24.718]              // -> [jep106id <= 0x00000000]
[10:22:24.718]            __var ROMTableBase = 0;
[10:22:24.719]              // -> [ROMTableBase <= 0x00000000]
[10:22:24.719]            __ap = 0;      // AHB-AP
[10:22:24.719]              // -> [__ap <= 0x00000000]
[10:22:24.720]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:22:24.720]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:22:24.721]              // -> [ROMTableBase <= 0xF0000000]
[10:22:24.721]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:22:24.722]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:22:24.723]              // -> [pidr1 <= 0x00000004]
[10:22:24.723]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:22:24.724]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:22:24.724]              // -> [pidr2 <= 0x0000000A]
[10:22:24.724]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:22:24.724]              // -> [jep106id <= 0x00000020]
[10:22:24.725]          </block>
[10:22:24.725]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:22:24.725]            // if-block "jep106id != 0x20"
[10:22:24.725]              // =>  FALSE
[10:22:24.725]            // skip if-block "jep106id != 0x20"
[10:22:24.725]          </control>
[10:22:24.726]        </sequence>
[10:22:24.726]    </block>
[10:22:24.726]  </sequence>
[10:22:24.726]  
[10:22:24.738]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:22:24.738]  
[10:22:24.738]  <debugvars>
[10:22:24.739]    // Pre-defined
[10:22:24.739]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:22:24.739]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:22:24.739]    __dp=0x00000000
[10:22:24.740]    __ap=0x00000000
[10:22:24.740]    __traceout=0x00000000      (Trace Disabled)
[10:22:24.740]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:22:24.741]    __FlashAddr=0x00000000
[10:22:24.741]    __FlashLen=0x00000000
[10:22:24.741]    __FlashArg=0x00000000
[10:22:24.741]    __FlashOp=0x00000000
[10:22:24.742]    __Result=0x00000000
[10:22:24.742]    
[10:22:24.742]    // User-defined
[10:22:24.742]    DbgMCU_CR=0x00000007
[10:22:24.742]    DbgMCU_APB1_Fz=0x00000000
[10:22:24.742]    DbgMCU_APB2_Fz=0x00000000
[10:22:24.742]    DoOptionByteLoading=0x00000000
[10:22:24.743]  </debugvars>
[10:22:24.743]  
[10:22:24.743]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:22:24.743]    <block atomic="false" info="">
[10:22:24.743]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:22:24.744]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:24.744]    </block>
[10:22:24.745]    <block atomic="false" info="DbgMCU registers">
[10:22:24.745]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:22:24.746]        // -> [Read32(0x40021034) => 0x00000200]   (__dp=0x00000000, __ap=0x00000000)
[10:22:24.747]        // -> [Write32(0x40021034, 0x00400200)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:24.747]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:22:24.748]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:24.748]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:22:24.749]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:24.749]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:22:24.749]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:24.750]    </block>
[10:22:24.750]  </sequence>
[10:22:24.750]  
[10:22:32.752]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:22:32.752]  
[10:22:32.752]  <debugvars>
[10:22:32.753]    // Pre-defined
[10:22:32.753]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:22:32.755]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:22:32.755]    __dp=0x00000000
[10:22:32.756]    __ap=0x00000000
[10:22:32.757]    __traceout=0x00000000      (Trace Disabled)
[10:22:32.757]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:22:32.758]    __FlashAddr=0x00000000
[10:22:32.758]    __FlashLen=0x00000000
[10:22:32.759]    __FlashArg=0x00000000
[10:22:32.760]    __FlashOp=0x00000000
[10:22:32.760]    __Result=0x00000000
[10:22:32.761]    
[10:22:32.761]    // User-defined
[10:22:32.761]    DbgMCU_CR=0x00000007
[10:22:32.762]    DbgMCU_APB1_Fz=0x00000000
[10:22:32.762]    DbgMCU_APB2_Fz=0x00000000
[10:22:32.763]    DoOptionByteLoading=0x00000000
[10:22:32.763]  </debugvars>
[10:22:32.764]  
[10:22:32.765]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:22:32.765]    <block atomic="false" info="">
[10:22:32.766]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:22:32.766]        // -> [connectionFlash <= 0x00000001]
[10:22:32.767]      __var FLASH_BASE = 0x40022000 ;
[10:22:32.767]        // -> [FLASH_BASE <= 0x40022000]
[10:22:32.767]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:22:32.768]        // -> [FLASH_CR <= 0x40022004]
[10:22:32.769]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:22:32.769]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:22:32.770]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:22:32.770]        // -> [LOCK_BIT <= 0x00000001]
[10:22:32.771]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:22:32.771]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:22:32.772]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:22:32.772]        // -> [FLASH_KEYR <= 0x4002200C]
[10:22:32.773]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:22:32.773]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:22:32.774]      __var FLASH_KEY2 = 0x02030405 ;
[10:22:32.774]        // -> [FLASH_KEY2 <= 0x02030405]
[10:22:32.775]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:22:32.775]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:22:32.776]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:22:32.776]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:22:32.777]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:22:32.777]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:22:32.778]      __var FLASH_CR_Value = 0 ;
[10:22:32.778]        // -> [FLASH_CR_Value <= 0x00000000]
[10:22:32.779]      __var DoDebugPortStop = 1 ;
[10:22:32.779]        // -> [DoDebugPortStop <= 0x00000001]
[10:22:32.779]      __var DP_CTRL_STAT = 0x4 ;
[10:22:32.780]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:22:32.781]      __var DP_SELECT = 0x8 ;
[10:22:32.781]        // -> [DP_SELECT <= 0x00000008]
[10:22:32.782]    </block>
[10:22:32.782]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:22:32.782]      // if-block "connectionFlash && DoOptionByteLoading"
[10:22:32.782]        // =>  FALSE
[10:22:32.783]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:22:32.783]    </control>
[10:22:32.783]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:22:32.783]      // if-block "DoDebugPortStop"
[10:22:32.784]        // =>  TRUE
[10:22:32.784]      <block atomic="false" info="">
[10:22:32.785]        WriteDP(DP_SELECT, 0x00000000);
[10:22:32.786]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:22:32.786]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:22:32.787]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:22:32.787]      </block>
[10:22:32.787]      // end if-block "DoDebugPortStop"
[10:22:32.787]    </control>
[10:22:32.788]  </sequence>
[10:22:32.788]  
[10:22:54.715]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:22:54.715]  
[10:22:54.715]  <debugvars>
[10:22:54.716]    // Pre-defined
[10:22:54.716]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:22:54.716]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:22:54.717]    __dp=0x00000000
[10:22:54.717]    __ap=0x00000000
[10:22:54.717]    __traceout=0x00000000      (Trace Disabled)
[10:22:54.717]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:22:54.717]    __FlashAddr=0x00000000
[10:22:54.718]    __FlashLen=0x00000000
[10:22:54.718]    __FlashArg=0x00000000
[10:22:54.718]    __FlashOp=0x00000000
[10:22:54.719]    __Result=0x00000000
[10:22:54.719]    
[10:22:54.719]    // User-defined
[10:22:54.719]    DbgMCU_CR=0x00000007
[10:22:54.720]    DbgMCU_APB1_Fz=0x00000000
[10:22:54.720]    DbgMCU_APB2_Fz=0x00000000
[10:22:54.720]    DoOptionByteLoading=0x00000000
[10:22:54.721]  </debugvars>
[10:22:54.721]  
[10:22:54.721]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:22:54.721]    <block atomic="false" info="">
[10:22:54.721]      Sequence("CheckID");
[10:22:54.722]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:22:54.722]          <block atomic="false" info="">
[10:22:54.722]            __var pidr1 = 0;
[10:22:54.722]              // -> [pidr1 <= 0x00000000]
[10:22:54.722]            __var pidr2 = 0;
[10:22:54.722]              // -> [pidr2 <= 0x00000000]
[10:22:54.723]            __var jep106id = 0;
[10:22:54.723]              // -> [jep106id <= 0x00000000]
[10:22:54.723]            __var ROMTableBase = 0;
[10:22:54.723]              // -> [ROMTableBase <= 0x00000000]
[10:22:54.723]            __ap = 0;      // AHB-AP
[10:22:54.723]              // -> [__ap <= 0x00000000]
[10:22:54.724]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:22:54.725]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:22:54.725]              // -> [ROMTableBase <= 0xF0000000]
[10:22:54.726]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:22:54.727]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:22:54.728]              // -> [pidr1 <= 0x00000004]
[10:22:54.728]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:22:54.729]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:22:54.729]              // -> [pidr2 <= 0x0000000A]
[10:22:54.730]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:22:54.730]              // -> [jep106id <= 0x00000020]
[10:22:54.730]          </block>
[10:22:54.731]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:22:54.731]            // if-block "jep106id != 0x20"
[10:22:54.731]              // =>  FALSE
[10:22:54.731]            // skip if-block "jep106id != 0x20"
[10:22:54.732]          </control>
[10:22:54.732]        </sequence>
[10:22:54.732]    </block>
[10:22:54.732]  </sequence>
[10:22:54.732]  
[10:22:54.744]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:22:54.744]  
[10:22:54.763]  <debugvars>
[10:22:54.764]    // Pre-defined
[10:22:54.765]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:22:54.765]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:22:54.766]    __dp=0x00000000
[10:22:54.767]    __ap=0x00000000
[10:22:54.768]    __traceout=0x00000000      (Trace Disabled)
[10:22:54.768]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:22:54.769]    __FlashAddr=0x00000000
[10:22:54.769]    __FlashLen=0x00000000
[10:22:54.770]    __FlashArg=0x00000000
[10:22:54.771]    __FlashOp=0x00000000
[10:22:54.772]    __Result=0x00000000
[10:22:54.772]    
[10:22:54.772]    // User-defined
[10:22:54.773]    DbgMCU_CR=0x00000007
[10:22:54.774]    DbgMCU_APB1_Fz=0x00000000
[10:22:54.774]    DbgMCU_APB2_Fz=0x00000000
[10:22:54.775]    DoOptionByteLoading=0x00000000
[10:22:54.776]  </debugvars>
[10:22:54.777]  
[10:22:54.777]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:22:54.778]    <block atomic="false" info="">
[10:22:54.779]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:22:54.781]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:54.782]    </block>
[10:22:54.783]    <block atomic="false" info="DbgMCU registers">
[10:22:54.784]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:22:54.785]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[10:22:54.788]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:54.788]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:22:54.790]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:54.791]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:22:54.792]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:54.793]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:22:54.794]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:54.795]    </block>
[10:22:54.795]  </sequence>
[10:22:54.796]  
[10:23:54.731]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:23:54.731]  
[10:23:54.732]  <debugvars>
[10:23:54.732]    // Pre-defined
[10:23:54.734]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:23:54.734]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:23:54.735]    __dp=0x00000000
[10:23:54.736]    __ap=0x00000000
[10:23:54.736]    __traceout=0x00000000      (Trace Disabled)
[10:23:54.737]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:23:54.737]    __FlashAddr=0x00000000
[10:23:54.739]    __FlashLen=0x00000000
[10:23:54.740]    __FlashArg=0x00000000
[10:23:54.740]    __FlashOp=0x00000000
[10:23:54.741]    __Result=0x00000000
[10:23:54.742]    
[10:23:54.742]    // User-defined
[10:23:54.743]    DbgMCU_CR=0x00000007
[10:23:54.743]    DbgMCU_APB1_Fz=0x00000000
[10:23:54.744]    DbgMCU_APB2_Fz=0x00000000
[10:23:54.745]    DoOptionByteLoading=0x00000000
[10:23:54.746]  </debugvars>
[10:23:54.746]  
[10:23:54.747]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:23:54.748]    <block atomic="false" info="">
[10:23:54.748]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:23:54.749]        // -> [connectionFlash <= 0x00000000]
[10:23:54.749]      __var FLASH_BASE = 0x40022000 ;
[10:23:54.749]        // -> [FLASH_BASE <= 0x40022000]
[10:23:54.750]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:23:54.750]        // -> [FLASH_CR <= 0x40022004]
[10:23:54.751]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:23:54.752]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:23:54.752]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:23:54.753]        // -> [LOCK_BIT <= 0x00000001]
[10:23:54.753]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:23:54.753]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:23:54.754]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:23:54.754]        // -> [FLASH_KEYR <= 0x4002200C]
[10:23:54.755]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:23:54.755]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:23:54.755]      __var FLASH_KEY2 = 0x02030405 ;
[10:23:54.756]        // -> [FLASH_KEY2 <= 0x02030405]
[10:23:54.756]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:23:54.756]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:23:54.757]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:23:54.757]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:23:54.757]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:23:54.757]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:23:54.758]      __var FLASH_CR_Value = 0 ;
[10:23:54.758]        // -> [FLASH_CR_Value <= 0x00000000]
[10:23:54.758]      __var DoDebugPortStop = 1 ;
[10:23:54.758]        // -> [DoDebugPortStop <= 0x00000001]
[10:23:54.758]      __var DP_CTRL_STAT = 0x4 ;
[10:23:54.759]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:23:54.759]      __var DP_SELECT = 0x8 ;
[10:23:54.759]        // -> [DP_SELECT <= 0x00000008]
[10:23:54.759]    </block>
[10:23:54.759]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:23:54.760]      // if-block "connectionFlash && DoOptionByteLoading"
[10:23:54.760]        // =>  FALSE
[10:23:54.760]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:23:54.760]    </control>
[10:23:54.760]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:23:54.761]      // if-block "DoDebugPortStop"
[10:23:54.761]        // =>  TRUE
[10:23:54.761]      <block atomic="false" info="">
[10:23:54.762]        WriteDP(DP_SELECT, 0x00000000);
[10:23:54.762]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:23:54.762]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:23:54.763]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:23:54.763]      </block>
[10:23:54.763]      // end if-block "DoDebugPortStop"
[10:23:54.764]    </control>
[10:23:54.764]  </sequence>
[10:23:54.764]  
[10:24:30.456]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:24:30.456]  
[10:24:30.456]  <debugvars>
[10:24:30.456]    // Pre-defined
[10:24:30.457]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:24:30.457]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:24:30.457]    __dp=0x00000000
[10:24:30.458]    __ap=0x00000000
[10:24:30.458]    __traceout=0x00000000      (Trace Disabled)
[10:24:30.458]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:24:30.458]    __FlashAddr=0x00000000
[10:24:30.459]    __FlashLen=0x00000000
[10:24:30.459]    __FlashArg=0x00000000
[10:24:30.459]    __FlashOp=0x00000000
[10:24:30.459]    __Result=0x00000000
[10:24:30.459]    
[10:24:30.459]    // User-defined
[10:24:30.460]    DbgMCU_CR=0x00000007
[10:24:30.460]    DbgMCU_APB1_Fz=0x00000000
[10:24:30.460]    DbgMCU_APB2_Fz=0x00000000
[10:24:30.460]    DoOptionByteLoading=0x00000000
[10:24:30.460]  </debugvars>
[10:24:30.461]  
[10:24:30.461]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:24:30.461]    <block atomic="false" info="">
[10:24:30.461]      Sequence("CheckID");
[10:24:30.461]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:24:30.461]          <block atomic="false" info="">
[10:24:30.462]            __var pidr1 = 0;
[10:24:30.462]              // -> [pidr1 <= 0x00000000]
[10:24:30.462]            __var pidr2 = 0;
[10:24:30.462]              // -> [pidr2 <= 0x00000000]
[10:24:30.462]            __var jep106id = 0;
[10:24:30.463]              // -> [jep106id <= 0x00000000]
[10:24:30.463]            __var ROMTableBase = 0;
[10:24:30.463]              // -> [ROMTableBase <= 0x00000000]
[10:24:30.463]            __ap = 0;      // AHB-AP
[10:24:30.463]              // -> [__ap <= 0x00000000]
[10:24:30.463]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:24:30.464]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:24:30.465]              // -> [ROMTableBase <= 0xF0000000]
[10:24:30.465]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:24:30.466]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:24:30.467]              // -> [pidr1 <= 0x00000004]
[10:24:30.467]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:24:30.468]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:24:30.468]              // -> [pidr2 <= 0x0000000A]
[10:24:30.468]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:24:30.468]              // -> [jep106id <= 0x00000020]
[10:24:30.469]          </block>
[10:24:30.469]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:24:30.469]            // if-block "jep106id != 0x20"
[10:24:30.470]              // =>  FALSE
[10:24:30.470]            // skip if-block "jep106id != 0x20"
[10:24:30.470]          </control>
[10:24:30.471]        </sequence>
[10:24:30.471]    </block>
[10:24:30.471]  </sequence>
[10:24:30.471]  
[10:24:30.482]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:24:30.482]  
[10:24:30.493]  <debugvars>
[10:24:30.494]    // Pre-defined
[10:24:30.495]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:24:30.495]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:24:30.496]    __dp=0x00000000
[10:24:30.496]    __ap=0x00000000
[10:24:30.497]    __traceout=0x00000000      (Trace Disabled)
[10:24:30.498]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:24:30.498]    __FlashAddr=0x00000000
[10:24:30.499]    __FlashLen=0x00000000
[10:24:30.499]    __FlashArg=0x00000000
[10:24:30.499]    __FlashOp=0x00000000
[10:24:30.500]    __Result=0x00000000
[10:24:30.500]    
[10:24:30.500]    // User-defined
[10:24:30.500]    DbgMCU_CR=0x00000007
[10:24:30.501]    DbgMCU_APB1_Fz=0x00000000
[10:24:30.502]    DbgMCU_APB2_Fz=0x00000000
[10:24:30.502]    DoOptionByteLoading=0x00000000
[10:24:30.502]  </debugvars>
[10:24:30.503]  
[10:24:30.503]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:24:30.503]    <block atomic="false" info="">
[10:24:30.504]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:24:30.505]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:24:30.505]    </block>
[10:24:30.505]    <block atomic="false" info="DbgMCU registers">
[10:24:30.506]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:24:30.507]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[10:24:30.507]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[10:24:30.508]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:24:30.508]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:24:30.509]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:24:30.510]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:24:30.510]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:24:30.511]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:24:30.511]    </block>
[10:24:30.511]  </sequence>
[10:24:30.511]  
[10:24:32.032]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:24:32.032]  
[10:24:32.032]  <debugvars>
[10:24:32.033]    // Pre-defined
[10:24:32.033]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:24:32.033]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:24:32.033]    __dp=0x00000000
[10:24:32.033]    __ap=0x00000000
[10:24:32.034]    __traceout=0x00000000      (Trace Disabled)
[10:24:32.034]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:24:32.035]    __FlashAddr=0x00000000
[10:24:32.035]    __FlashLen=0x00000000
[10:24:32.035]    __FlashArg=0x00000000
[10:24:32.035]    __FlashOp=0x00000000
[10:24:32.035]    __Result=0x00000000
[10:24:32.035]    
[10:24:32.035]    // User-defined
[10:24:32.035]    DbgMCU_CR=0x00000007
[10:24:32.036]    DbgMCU_APB1_Fz=0x00000000
[10:24:32.037]    DbgMCU_APB2_Fz=0x00000000
[10:24:32.037]    DoOptionByteLoading=0x00000000
[10:24:32.037]  </debugvars>
[10:24:32.037]  
[10:24:32.037]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:24:32.038]    <block atomic="false" info="">
[10:24:32.038]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:24:32.038]        // -> [connectionFlash <= 0x00000001]
[10:24:32.038]      __var FLASH_BASE = 0x40022000 ;
[10:24:32.039]        // -> [FLASH_BASE <= 0x40022000]
[10:24:32.039]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:24:32.039]        // -> [FLASH_CR <= 0x40022004]
[10:24:32.039]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:24:32.039]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:24:32.040]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:24:32.040]        // -> [LOCK_BIT <= 0x00000001]
[10:24:32.040]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:24:32.040]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:24:32.040]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:24:32.040]        // -> [FLASH_KEYR <= 0x4002200C]
[10:24:32.041]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:24:32.041]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:24:32.041]      __var FLASH_KEY2 = 0x02030405 ;
[10:24:32.042]        // -> [FLASH_KEY2 <= 0x02030405]
[10:24:32.042]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:24:32.042]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:24:32.043]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:24:32.043]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:24:32.043]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:24:32.043]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:24:32.044]      __var FLASH_CR_Value = 0 ;
[10:24:32.044]        // -> [FLASH_CR_Value <= 0x00000000]
[10:24:32.044]      __var DoDebugPortStop = 1 ;
[10:24:32.044]        // -> [DoDebugPortStop <= 0x00000001]
[10:24:32.044]      __var DP_CTRL_STAT = 0x4 ;
[10:24:32.045]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:24:32.045]      __var DP_SELECT = 0x8 ;
[10:24:32.045]        // -> [DP_SELECT <= 0x00000008]
[10:24:32.045]    </block>
[10:24:32.045]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:24:32.045]      // if-block "connectionFlash && DoOptionByteLoading"
[10:24:32.046]        // =>  FALSE
[10:24:32.046]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:24:32.046]    </control>
[10:24:32.046]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:24:32.046]      // if-block "DoDebugPortStop"
[10:24:32.047]        // =>  TRUE
[10:24:32.047]      <block atomic="false" info="">
[10:24:32.047]        WriteDP(DP_SELECT, 0x00000000);
[10:24:32.047]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:24:32.048]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:24:32.048]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:24:32.049]      </block>
[10:24:32.049]      // end if-block "DoDebugPortStop"
[10:24:32.049]    </control>
[10:24:32.049]  </sequence>
[10:24:32.049]  
[10:24:36.818]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:24:36.818]  
[10:24:36.819]  <debugvars>
[10:24:36.819]    // Pre-defined
[10:24:36.819]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:24:36.819]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:24:36.820]    __dp=0x00000000
[10:24:36.820]    __ap=0x00000000
[10:24:36.820]    __traceout=0x00000000      (Trace Disabled)
[10:24:36.820]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:24:36.821]    __FlashAddr=0x00000000
[10:24:36.821]    __FlashLen=0x00000000
[10:24:36.821]    __FlashArg=0x00000000
[10:24:36.821]    __FlashOp=0x00000000
[10:24:36.822]    __Result=0x00000000
[10:24:36.822]    
[10:24:36.822]    // User-defined
[10:24:36.822]    DbgMCU_CR=0x00000007
[10:24:36.822]    DbgMCU_APB1_Fz=0x00000000
[10:24:36.822]    DbgMCU_APB2_Fz=0x00000000
[10:24:36.823]    DoOptionByteLoading=0x00000000
[10:24:36.823]  </debugvars>
[10:24:36.823]  
[10:24:36.823]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:24:36.823]    <block atomic="false" info="">
[10:24:36.823]      Sequence("CheckID");
[10:24:36.824]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:24:36.824]          <block atomic="false" info="">
[10:24:36.824]            __var pidr1 = 0;
[10:24:36.824]              // -> [pidr1 <= 0x00000000]
[10:24:36.825]            __var pidr2 = 0;
[10:24:36.825]              // -> [pidr2 <= 0x00000000]
[10:24:36.826]            __var jep106id = 0;
[10:24:36.826]              // -> [jep106id <= 0x00000000]
[10:24:36.826]            __var ROMTableBase = 0;
[10:24:36.826]              // -> [ROMTableBase <= 0x00000000]
[10:24:36.826]            __ap = 0;      // AHB-AP
[10:24:36.826]              // -> [__ap <= 0x00000000]
[10:24:36.827]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:24:36.827]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:24:36.827]              // -> [ROMTableBase <= 0xF0000000]
[10:24:36.828]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:24:36.828]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:24:36.829]              // -> [pidr1 <= 0x00000004]
[10:24:36.829]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:24:36.830]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:24:36.830]              // -> [pidr2 <= 0x0000000A]
[10:24:36.830]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:24:36.830]              // -> [jep106id <= 0x00000020]
[10:24:36.831]          </block>
[10:24:36.831]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:24:36.831]            // if-block "jep106id != 0x20"
[10:24:36.831]              // =>  FALSE
[10:24:36.831]            // skip if-block "jep106id != 0x20"
[10:24:36.831]          </control>
[10:24:36.832]        </sequence>
[10:24:36.832]    </block>
[10:24:36.832]  </sequence>
[10:24:36.832]  
[10:24:36.843]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:24:36.843]  
[10:24:36.844]  <debugvars>
[10:24:36.844]    // Pre-defined
[10:24:36.844]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:24:36.844]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:24:36.845]    __dp=0x00000000
[10:24:36.845]    __ap=0x00000000
[10:24:36.845]    __traceout=0x00000000      (Trace Disabled)
[10:24:36.846]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:24:36.846]    __FlashAddr=0x00000000
[10:24:36.846]    __FlashLen=0x00000000
[10:24:36.846]    __FlashArg=0x00000000
[10:24:36.846]    __FlashOp=0x00000000
[10:24:36.846]    __Result=0x00000000
[10:24:36.847]    
[10:24:36.847]    // User-defined
[10:24:36.847]    DbgMCU_CR=0x00000007
[10:24:36.847]    DbgMCU_APB1_Fz=0x00000000
[10:24:36.847]    DbgMCU_APB2_Fz=0x00000000
[10:24:36.847]    DoOptionByteLoading=0x00000000
[10:24:36.848]  </debugvars>
[10:24:36.848]  
[10:24:36.848]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:24:36.848]    <block atomic="false" info="">
[10:24:36.848]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:24:36.849]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:24:36.849]    </block>
[10:24:36.849]    <block atomic="false" info="DbgMCU registers">
[10:24:36.849]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:24:36.851]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[10:24:36.852]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[10:24:36.852]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:24:36.853]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:24:36.854]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:24:36.855]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:24:36.855]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:24:36.856]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:24:36.856]    </block>
[10:24:36.856]  </sequence>
[10:24:36.856]  
[10:24:43.907]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:24:43.907]  
[10:24:43.909]  <debugvars>
[10:24:43.909]    // Pre-defined
[10:24:43.910]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:24:43.911]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:24:43.911]    __dp=0x00000000
[10:24:43.912]    __ap=0x00000000
[10:24:43.912]    __traceout=0x00000000      (Trace Disabled)
[10:24:43.913]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:24:43.913]    __FlashAddr=0x00000000
[10:24:43.913]    __FlashLen=0x00000000
[10:24:43.914]    __FlashArg=0x00000000
[10:24:43.914]    __FlashOp=0x00000000
[10:24:43.915]    __Result=0x00000000
[10:24:43.915]    
[10:24:43.915]    // User-defined
[10:24:43.916]    DbgMCU_CR=0x00000007
[10:24:43.916]    DbgMCU_APB1_Fz=0x00000000
[10:24:43.916]    DbgMCU_APB2_Fz=0x00000000
[10:24:43.917]    DoOptionByteLoading=0x00000000
[10:24:43.917]  </debugvars>
[10:24:43.918]  
[10:24:43.918]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:24:43.918]    <block atomic="false" info="">
[10:24:43.918]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:24:43.919]        // -> [connectionFlash <= 0x00000001]
[10:24:43.920]      __var FLASH_BASE = 0x40022000 ;
[10:24:43.920]        // -> [FLASH_BASE <= 0x40022000]
[10:24:43.920]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:24:43.921]        // -> [FLASH_CR <= 0x40022004]
[10:24:43.921]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:24:43.921]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:24:43.921]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:24:43.922]        // -> [LOCK_BIT <= 0x00000001]
[10:24:43.922]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:24:43.922]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:24:43.922]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:24:43.922]        // -> [FLASH_KEYR <= 0x4002200C]
[10:24:43.922]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:24:43.923]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:24:43.923]      __var FLASH_KEY2 = 0x02030405 ;
[10:24:43.923]        // -> [FLASH_KEY2 <= 0x02030405]
[10:24:43.923]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:24:43.923]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:24:43.924]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:24:43.924]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:24:43.924]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:24:43.924]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:24:43.925]      __var FLASH_CR_Value = 0 ;
[10:24:43.925]        // -> [FLASH_CR_Value <= 0x00000000]
[10:24:43.925]      __var DoDebugPortStop = 1 ;
[10:24:43.926]        // -> [DoDebugPortStop <= 0x00000001]
[10:24:43.926]      __var DP_CTRL_STAT = 0x4 ;
[10:24:43.926]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:24:43.927]      __var DP_SELECT = 0x8 ;
[10:24:43.927]        // -> [DP_SELECT <= 0x00000008]
[10:24:43.927]    </block>
[10:24:43.927]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:24:43.927]      // if-block "connectionFlash && DoOptionByteLoading"
[10:24:43.928]        // =>  FALSE
[10:24:43.928]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:24:43.928]    </control>
[10:24:43.928]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:24:43.928]      // if-block "DoDebugPortStop"
[10:24:43.929]        // =>  TRUE
[10:24:43.929]      <block atomic="false" info="">
[10:24:43.929]        WriteDP(DP_SELECT, 0x00000000);
[10:24:43.929]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:24:43.930]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:24:43.930]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:24:43.931]      </block>
[10:24:43.931]      // end if-block "DoDebugPortStop"
[10:24:43.931]    </control>
[10:24:43.931]  </sequence>
[10:24:43.931]  
[12:07:19.115]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[12:07:19.115]  
[12:07:19.129]  <debugvars>
[12:07:19.129]    // Pre-defined
[12:07:19.129]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:07:19.129]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:07:19.129]    __dp=0x00000000
[12:07:19.129]    __ap=0x00000000
[12:07:19.129]    __traceout=0x00000000      (Trace Disabled)
[12:07:19.129]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:07:19.132]    __FlashAddr=0x00000000
[12:07:19.132]    __FlashLen=0x00000000
[12:07:19.132]    __FlashArg=0x00000000
[12:07:19.133]    __FlashOp=0x00000000
[12:07:19.133]    __Result=0x00000000
[12:07:19.133]    
[12:07:19.133]    // User-defined
[12:07:19.133]    DbgMCU_CR=0x00000007
[12:07:19.133]    DbgMCU_APB1_Fz=0x00000000
[12:07:19.133]    DbgMCU_APB2_Fz=0x00000000
[12:07:19.133]    DoOptionByteLoading=0x00000000
[12:07:19.134]  </debugvars>
[12:07:19.134]  
[12:07:19.134]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[12:07:19.135]    <block atomic="false" info="">
[12:07:19.135]      Sequence("CheckID");
[12:07:19.135]        <sequence name="CheckID" Pname="" disable="false" info="">
[12:07:19.135]          <block atomic="false" info="">
[12:07:19.135]            __var pidr1 = 0;
[12:07:19.136]              // -> [pidr1 <= 0x00000000]
[12:07:19.136]            __var pidr2 = 0;
[12:07:19.136]              // -> [pidr2 <= 0x00000000]
[12:07:19.136]            __var jep106id = 0;
[12:07:19.136]              // -> [jep106id <= 0x00000000]
[12:07:19.137]            __var ROMTableBase = 0;
[12:07:19.137]              // -> [ROMTableBase <= 0x00000000]
[12:07:19.137]            __ap = 0;      // AHB-AP
[12:07:19.137]              // -> [__ap <= 0x00000000]
[12:07:19.138]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[12:07:19.139]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[12:07:19.139]              // -> [ROMTableBase <= 0xF0000000]
[12:07:19.139]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[12:07:19.140]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[12:07:19.140]              // -> [pidr1 <= 0x00000004]
[12:07:19.140]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[12:07:19.142]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[12:07:19.142]              // -> [pidr2 <= 0x0000000A]
[12:07:19.142]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[12:07:19.142]              // -> [jep106id <= 0x00000020]
[12:07:19.142]          </block>
[12:07:19.143]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[12:07:19.143]            // if-block "jep106id != 0x20"
[12:07:19.143]              // =>  FALSE
[12:07:19.143]            // skip if-block "jep106id != 0x20"
[12:07:19.143]          </control>
[12:07:19.144]        </sequence>
[12:07:19.144]    </block>
[12:07:19.144]  </sequence>
[12:07:19.144]  
[12:07:19.156]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[12:07:19.156]  
[12:07:19.156]  <debugvars>
[12:07:19.156]    // Pre-defined
[12:07:19.157]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:07:19.157]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:07:19.157]    __dp=0x00000000
[12:07:19.158]    __ap=0x00000000
[12:07:19.158]    __traceout=0x00000000      (Trace Disabled)
[12:07:19.159]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:07:19.159]    __FlashAddr=0x00000000
[12:07:19.159]    __FlashLen=0x00000000
[12:07:19.159]    __FlashArg=0x00000000
[12:07:19.160]    __FlashOp=0x00000000
[12:07:19.160]    __Result=0x00000000
[12:07:19.160]    
[12:07:19.160]    // User-defined
[12:07:19.160]    DbgMCU_CR=0x00000007
[12:07:19.161]    DbgMCU_APB1_Fz=0x00000000
[12:07:19.161]    DbgMCU_APB2_Fz=0x00000000
[12:07:19.161]    DoOptionByteLoading=0x00000000
[12:07:19.161]  </debugvars>
[12:07:19.161]  
[12:07:19.162]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[12:07:19.162]    <block atomic="false" info="">
[12:07:19.162]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[12:07:19.163]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[12:07:19.163]    </block>
[12:07:19.164]    <block atomic="false" info="DbgMCU registers">
[12:07:19.164]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[12:07:19.165]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[12:07:19.165]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[12:07:19.166]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[12:07:19.166]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[12:07:19.167]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[12:07:19.167]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[12:07:19.168]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[12:07:19.168]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[12:07:19.168]    </block>
[12:07:19.168]  </sequence>
[12:07:19.168]  
[12:07:22.366]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[12:07:22.366]  
[12:07:22.366]  <debugvars>
[12:07:22.366]    // Pre-defined
[12:07:22.366]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:07:22.366]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:07:22.366]    __dp=0x00000000
[12:07:22.366]    __ap=0x00000000
[12:07:22.366]    __traceout=0x00000000      (Trace Disabled)
[12:07:22.366]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:07:22.366]    __FlashAddr=0x00000000
[12:07:22.366]    __FlashLen=0x00000000
[12:07:22.366]    __FlashArg=0x00000000
[12:07:22.366]    __FlashOp=0x00000000
[12:07:22.366]    __Result=0x00000000
[12:07:22.366]    
[12:07:22.366]    // User-defined
[12:07:22.366]    DbgMCU_CR=0x00000007
[12:07:22.371]    DbgMCU_APB1_Fz=0x00000000
[12:07:22.371]    DbgMCU_APB2_Fz=0x00000000
[12:07:22.371]    DoOptionByteLoading=0x00000000
[12:07:22.371]  </debugvars>
[12:07:22.371]  
[12:07:22.371]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[12:07:22.371]    <block atomic="false" info="">
[12:07:22.371]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[12:07:22.371]        // -> [connectionFlash <= 0x00000001]
[12:07:22.371]      __var FLASH_BASE = 0x40022000 ;
[12:07:22.373]        // -> [FLASH_BASE <= 0x40022000]
[12:07:22.373]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[12:07:22.373]        // -> [FLASH_CR <= 0x40022004]
[12:07:22.373]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[12:07:22.373]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[12:07:22.373]      __var LOCK_BIT = ( 1 << 0 ) ;
[12:07:22.373]        // -> [LOCK_BIT <= 0x00000001]
[12:07:22.373]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[12:07:22.375]        // -> [OPTLOCK_BIT <= 0x00000004]
[12:07:22.375]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[12:07:22.375]        // -> [FLASH_KEYR <= 0x4002200C]
[12:07:22.375]      __var FLASH_KEY1 = 0x89ABCDEF ;
[12:07:22.375]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[12:07:22.376]      __var FLASH_KEY2 = 0x02030405 ;
[12:07:22.376]        // -> [FLASH_KEY2 <= 0x02030405]
[12:07:22.376]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[12:07:22.376]        // -> [FLASH_OPTKEYR <= 0x40022014]
[12:07:22.376]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[12:07:22.376]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[12:07:22.376]      __var FLASH_OPTKEY2 = 0x24252627 ;
[12:07:22.376]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[12:07:22.376]      __var FLASH_CR_Value = 0 ;
[12:07:22.376]        // -> [FLASH_CR_Value <= 0x00000000]
[12:07:22.378]      __var DoDebugPortStop = 1 ;
[12:07:22.378]        // -> [DoDebugPortStop <= 0x00000001]
[12:07:22.378]      __var DP_CTRL_STAT = 0x4 ;
[12:07:22.378]        // -> [DP_CTRL_STAT <= 0x00000004]
[12:07:22.378]      __var DP_SELECT = 0x8 ;
[12:07:22.378]        // -> [DP_SELECT <= 0x00000008]
[12:07:22.378]    </block>
[12:07:22.378]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[12:07:22.378]      // if-block "connectionFlash && DoOptionByteLoading"
[12:07:22.378]        // =>  FALSE
[12:07:22.378]      // skip if-block "connectionFlash && DoOptionByteLoading"
[12:07:22.380]    </control>
[12:07:22.380]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[12:07:22.380]      // if-block "DoDebugPortStop"
[12:07:22.380]        // =>  TRUE
[12:07:22.381]      <block atomic="false" info="">
[12:07:22.381]        WriteDP(DP_SELECT, 0x00000000);
[12:07:22.381]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[12:07:22.381]        WriteDP(DP_CTRL_STAT, 0x00000000);
[12:07:22.381]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[12:07:22.381]      </block>
[12:07:22.381]      // end if-block "DoDebugPortStop"
[12:07:22.383]    </control>
[12:07:22.383]  </sequence>
[12:07:22.383]  
[12:07:30.312]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[12:07:30.312]  
[12:07:30.312]  <debugvars>
[12:07:30.312]    // Pre-defined
[12:07:30.312]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:07:30.312]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:07:30.312]    __dp=0x00000000
[12:07:30.312]    __ap=0x00000000
[12:07:30.312]    __traceout=0x00000000      (Trace Disabled)
[12:07:30.312]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:07:30.312]    __FlashAddr=0x00000000
[12:07:30.315]    __FlashLen=0x00000000
[12:07:30.315]    __FlashArg=0x00000000
[12:07:30.316]    __FlashOp=0x00000000
[12:07:30.316]    __Result=0x00000000
[12:07:30.316]    
[12:07:30.316]    // User-defined
[12:07:30.316]    DbgMCU_CR=0x00000007
[12:07:30.317]    DbgMCU_APB1_Fz=0x00000000
[12:07:30.317]    DbgMCU_APB2_Fz=0x00000000
[12:07:30.317]    DoOptionByteLoading=0x00000000
[12:07:30.318]  </debugvars>
[12:07:30.318]  
[12:07:30.318]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[12:07:30.318]    <block atomic="false" info="">
[12:07:30.318]      Sequence("CheckID");
[12:07:30.318]        <sequence name="CheckID" Pname="" disable="false" info="">
[12:07:30.319]          <block atomic="false" info="">
[12:07:30.319]            __var pidr1 = 0;
[12:07:30.319]              // -> [pidr1 <= 0x00000000]
[12:07:30.319]            __var pidr2 = 0;
[12:07:30.320]              // -> [pidr2 <= 0x00000000]
[12:07:30.321]            __var jep106id = 0;
[12:07:30.321]              // -> [jep106id <= 0x00000000]
[12:07:30.321]            __var ROMTableBase = 0;
[12:07:30.322]              // -> [ROMTableBase <= 0x00000000]
[12:07:30.322]            __ap = 0;      // AHB-AP
[12:07:30.322]              // -> [__ap <= 0x00000000]
[12:07:30.323]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[12:07:30.323]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[12:07:30.324]              // -> [ROMTableBase <= 0xF0000000]
[12:07:30.324]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[12:07:30.325]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[12:07:30.325]              // -> [pidr1 <= 0x00000004]
[12:07:30.326]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[12:07:30.327]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[12:07:30.327]              // -> [pidr2 <= 0x0000000A]
[12:07:30.327]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[12:07:30.327]              // -> [jep106id <= 0x00000020]
[12:07:30.327]          </block>
[12:07:30.328]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[12:07:30.328]            // if-block "jep106id != 0x20"
[12:07:30.328]              // =>  FALSE
[12:07:30.328]            // skip if-block "jep106id != 0x20"
[12:07:30.328]          </control>
[12:07:30.329]        </sequence>
[12:07:30.329]    </block>
[12:07:30.329]  </sequence>
[12:07:30.329]  
[12:07:30.342]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[12:07:30.342]  
[12:07:30.358]  <debugvars>
[12:07:30.358]    // Pre-defined
[12:07:30.359]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:07:30.359]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:07:30.360]    __dp=0x00000000
[12:07:30.361]    __ap=0x00000000
[12:07:30.361]    __traceout=0x00000000      (Trace Disabled)
[12:07:30.362]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:07:30.362]    __FlashAddr=0x00000000
[12:07:30.363]    __FlashLen=0x00000000
[12:07:30.363]    __FlashArg=0x00000000
[12:07:30.364]    __FlashOp=0x00000000
[12:07:30.365]    __Result=0x00000000
[12:07:30.365]    
[12:07:30.365]    // User-defined
[12:07:30.366]    DbgMCU_CR=0x00000007
[12:07:30.366]    DbgMCU_APB1_Fz=0x00000000
[12:07:30.366]    DbgMCU_APB2_Fz=0x00000000
[12:07:30.366]    DoOptionByteLoading=0x00000000
[12:07:30.367]  </debugvars>
[12:07:30.367]  
[12:07:30.367]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[12:07:30.367]    <block atomic="false" info="">
[12:07:30.367]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[12:07:30.369]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[12:07:30.369]    </block>
[12:07:30.369]    <block atomic="false" info="DbgMCU registers">
[12:07:30.369]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[12:07:30.369]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[12:07:30.369]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[12:07:30.369]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[12:07:30.369]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[12:07:30.374]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[12:07:30.374]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[12:07:30.374]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[12:07:30.376]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[12:07:30.376]    </block>
[12:07:30.376]  </sequence>
[12:07:30.377]  
[12:07:37.604]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[12:07:37.604]  
[12:07:37.604]  <debugvars>
[12:07:37.606]    // Pre-defined
[12:07:37.606]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:07:37.606]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:07:37.606]    __dp=0x00000000
[12:07:37.606]    __ap=0x00000000
[12:07:37.606]    __traceout=0x00000000      (Trace Disabled)
[12:07:37.606]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:07:37.606]    __FlashAddr=0x00000000
[12:07:37.606]    __FlashLen=0x00000000
[12:07:37.606]    __FlashArg=0x00000000
[12:07:37.608]    __FlashOp=0x00000000
[12:07:37.608]    __Result=0x00000000
[12:07:37.608]    
[12:07:37.608]    // User-defined
[12:07:37.608]    DbgMCU_CR=0x00000007
[12:07:37.608]    DbgMCU_APB1_Fz=0x00000000
[12:07:37.609]    DbgMCU_APB2_Fz=0x00000000
[12:07:37.609]    DoOptionByteLoading=0x00000000
[12:07:37.609]  </debugvars>
[12:07:37.609]  
[12:07:37.609]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[12:07:37.609]    <block atomic="false" info="">
[12:07:37.609]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[12:07:37.609]        // -> [connectionFlash <= 0x00000001]
[12:07:37.609]      __var FLASH_BASE = 0x40022000 ;
[12:07:37.609]        // -> [FLASH_BASE <= 0x40022000]
[12:07:37.611]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[12:07:37.611]        // -> [FLASH_CR <= 0x40022004]
[12:07:37.611]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[12:07:37.611]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[12:07:37.611]      __var LOCK_BIT = ( 1 << 0 ) ;
[12:07:37.612]        // -> [LOCK_BIT <= 0x00000001]
[12:07:37.612]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[12:07:37.612]        // -> [OPTLOCK_BIT <= 0x00000004]
[12:07:37.613]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[12:07:37.613]        // -> [FLASH_KEYR <= 0x4002200C]
[12:07:37.613]      __var FLASH_KEY1 = 0x89ABCDEF ;
[12:07:37.613]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[12:07:37.613]      __var FLASH_KEY2 = 0x02030405 ;
[12:07:37.613]        // -> [FLASH_KEY2 <= 0x02030405]
[12:07:37.613]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[12:07:37.613]        // -> [FLASH_OPTKEYR <= 0x40022014]
[12:07:37.614]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[12:07:37.614]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[12:07:37.614]      __var FLASH_OPTKEY2 = 0x24252627 ;
[12:07:37.614]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[12:07:37.614]      __var FLASH_CR_Value = 0 ;
[12:07:37.614]        // -> [FLASH_CR_Value <= 0x00000000]
[12:07:37.614]      __var DoDebugPortStop = 1 ;
[12:07:37.614]        // -> [DoDebugPortStop <= 0x00000001]
[12:07:37.614]      __var DP_CTRL_STAT = 0x4 ;
[12:07:37.614]        // -> [DP_CTRL_STAT <= 0x00000004]
[12:07:37.616]      __var DP_SELECT = 0x8 ;
[12:07:37.616]        // -> [DP_SELECT <= 0x00000008]
[12:07:37.616]    </block>
[12:07:37.616]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[12:07:37.616]      // if-block "connectionFlash && DoOptionByteLoading"
[12:07:37.616]        // =>  FALSE
[12:07:37.616]      // skip if-block "connectionFlash && DoOptionByteLoading"
[12:07:37.616]    </control>
[12:07:37.616]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[12:07:37.616]      // if-block "DoDebugPortStop"
[12:07:37.616]        // =>  TRUE
[12:07:37.616]      <block atomic="false" info="">
[12:07:37.616]        WriteDP(DP_SELECT, 0x00000000);
[12:07:37.616]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[12:07:37.619]        WriteDP(DP_CTRL_STAT, 0x00000000);
[12:07:37.619]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[12:07:37.619]      </block>
[12:07:37.619]      // end if-block "DoDebugPortStop"
[12:07:37.619]    </control>
[12:07:37.619]  </sequence>
[12:07:37.619]  
[12:07:56.540]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[12:07:56.540]  
[12:07:56.540]  <debugvars>
[12:07:56.540]    // Pre-defined
[12:07:56.540]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:07:56.545]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:07:56.545]    __dp=0x00000000
[12:07:56.545]    __ap=0x00000000
[12:07:56.545]    __traceout=0x00000000      (Trace Disabled)
[12:07:56.545]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:07:56.546]    __FlashAddr=0x00000000
[12:07:56.546]    __FlashLen=0x00000000
[12:07:56.546]    __FlashArg=0x00000000
[12:07:56.547]    __FlashOp=0x00000000
[12:07:56.547]    __Result=0x00000000
[12:07:56.548]    
[12:07:56.548]    // User-defined
[12:07:56.548]    DbgMCU_CR=0x00000007
[12:07:56.548]    DbgMCU_APB1_Fz=0x00000000
[12:07:56.548]    DbgMCU_APB2_Fz=0x00000000
[12:07:56.548]    DoOptionByteLoading=0x00000000
[12:07:56.548]  </debugvars>
[12:07:56.549]  
[12:07:56.549]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[12:07:56.549]    <block atomic="false" info="">
[12:07:56.549]      Sequence("CheckID");
[12:07:56.549]        <sequence name="CheckID" Pname="" disable="false" info="">
[12:07:56.549]          <block atomic="false" info="">
[12:07:56.550]            __var pidr1 = 0;
[12:07:56.550]              // -> [pidr1 <= 0x00000000]
[12:07:56.550]            __var pidr2 = 0;
[12:07:56.550]              // -> [pidr2 <= 0x00000000]
[12:07:56.550]            __var jep106id = 0;
[12:07:56.550]              // -> [jep106id <= 0x00000000]
[12:07:56.551]            __var ROMTableBase = 0;
[12:07:56.551]              // -> [ROMTableBase <= 0x00000000]
[12:07:56.551]            __ap = 0;      // AHB-AP
[12:07:56.551]              // -> [__ap <= 0x00000000]
[12:07:56.551]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[12:07:56.552]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[12:07:56.552]              // -> [ROMTableBase <= 0xF0000000]
[12:07:56.553]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[12:07:56.555]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[12:07:56.555]              // -> [pidr1 <= 0x00000004]
[12:07:56.555]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[12:07:56.556]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[12:07:56.556]              // -> [pidr2 <= 0x0000000A]
[12:07:56.556]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[12:07:56.556]              // -> [jep106id <= 0x00000020]
[12:07:56.557]          </block>
[12:07:56.557]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[12:07:56.557]            // if-block "jep106id != 0x20"
[12:07:56.558]              // =>  FALSE
[12:07:56.558]            // skip if-block "jep106id != 0x20"
[12:07:56.558]          </control>
[12:07:56.558]        </sequence>
[12:07:56.558]    </block>
[12:07:56.559]  </sequence>
[12:07:56.559]  
[12:07:56.571]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[12:07:56.571]  
[12:07:56.590]  <debugvars>
[12:07:56.590]    // Pre-defined
[12:07:56.590]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:07:56.590]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:07:56.592]    __dp=0x00000000
[12:07:56.592]    __ap=0x00000000
[12:07:56.592]    __traceout=0x00000000      (Trace Disabled)
[12:07:56.592]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:07:56.593]    __FlashAddr=0x00000000
[12:07:56.593]    __FlashLen=0x00000000
[12:07:56.593]    __FlashArg=0x00000000
[12:07:56.593]    __FlashOp=0x00000000
[12:07:56.593]    __Result=0x00000000
[12:07:56.594]    
[12:07:56.594]    // User-defined
[12:07:56.594]    DbgMCU_CR=0x00000007
[12:07:56.594]    DbgMCU_APB1_Fz=0x00000000
[12:07:56.594]    DbgMCU_APB2_Fz=0x00000000
[12:07:56.594]    DoOptionByteLoading=0x00000000
[12:07:56.595]  </debugvars>
[12:07:56.595]  
[12:07:56.595]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[12:07:56.595]    <block atomic="false" info="">
[12:07:56.595]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[12:07:56.596]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[12:07:56.596]    </block>
[12:07:56.597]    <block atomic="false" info="DbgMCU registers">
[12:07:56.597]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[12:07:56.598]        // -> [Read32(0x40021034) => 0x00000201]   (__dp=0x00000000, __ap=0x00000000)
[12:07:56.599]        // -> [Write32(0x40021034, 0x00400201)]   (__dp=0x00000000, __ap=0x00000000)
[12:07:56.599]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[12:07:56.600]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[12:07:56.601]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[12:07:56.601]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[12:07:56.602]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[12:07:56.602]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[12:07:56.603]    </block>
[12:07:56.603]  </sequence>
[12:07:56.603]  
[12:08:03.670]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[12:08:03.670]  
[12:08:03.671]  <debugvars>
[12:08:03.671]    // Pre-defined
[12:08:03.671]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:08:03.671]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:08:03.672]    __dp=0x00000000
[12:08:03.673]    __ap=0x00000000
[12:08:03.673]    __traceout=0x00000000      (Trace Disabled)
[12:08:03.674]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:08:03.674]    __FlashAddr=0x00000000
[12:08:03.675]    __FlashLen=0x00000000
[12:08:03.675]    __FlashArg=0x00000000
[12:08:03.675]    __FlashOp=0x00000000
[12:08:03.676]    __Result=0x00000000
[12:08:03.676]    
[12:08:03.676]    // User-defined
[12:08:03.676]    DbgMCU_CR=0x00000007
[12:08:03.677]    DbgMCU_APB1_Fz=0x00000000
[12:08:03.677]    DbgMCU_APB2_Fz=0x00000000
[12:08:03.677]    DoOptionByteLoading=0x00000000
[12:08:03.678]  </debugvars>
[12:08:03.678]  
[12:08:03.678]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[12:08:03.678]    <block atomic="false" info="">
[12:08:03.678]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[12:08:03.679]        // -> [connectionFlash <= 0x00000001]
[12:08:03.679]      __var FLASH_BASE = 0x40022000 ;
[12:08:03.679]        // -> [FLASH_BASE <= 0x40022000]
[12:08:03.679]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[12:08:03.679]        // -> [FLASH_CR <= 0x40022004]
[12:08:03.680]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[12:08:03.680]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[12:08:03.680]      __var LOCK_BIT = ( 1 << 0 ) ;
[12:08:03.680]        // -> [LOCK_BIT <= 0x00000001]
[12:08:03.680]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[12:08:03.680]        // -> [OPTLOCK_BIT <= 0x00000004]
[12:08:03.681]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[12:08:03.681]        // -> [FLASH_KEYR <= 0x4002200C]
[12:08:03.681]      __var FLASH_KEY1 = 0x89ABCDEF ;
[12:08:03.681]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[12:08:03.681]      __var FLASH_KEY2 = 0x02030405 ;
[12:08:03.682]        // -> [FLASH_KEY2 <= 0x02030405]
[12:08:03.682]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[12:08:03.682]        // -> [FLASH_OPTKEYR <= 0x40022014]
[12:08:03.682]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[12:08:03.682]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[12:08:03.682]      __var FLASH_OPTKEY2 = 0x24252627 ;
[12:08:03.683]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[12:08:03.683]      __var FLASH_CR_Value = 0 ;
[12:08:03.683]        // -> [FLASH_CR_Value <= 0x00000000]
[12:08:03.683]      __var DoDebugPortStop = 1 ;
[12:08:03.684]        // -> [DoDebugPortStop <= 0x00000001]
[12:08:03.684]      __var DP_CTRL_STAT = 0x4 ;
[12:08:03.684]        // -> [DP_CTRL_STAT <= 0x00000004]
[12:08:03.684]      __var DP_SELECT = 0x8 ;
[12:08:03.685]        // -> [DP_SELECT <= 0x00000008]
[12:08:03.685]    </block>
[12:08:03.685]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[12:08:03.685]      // if-block "connectionFlash && DoOptionByteLoading"
[12:08:03.685]        // =>  FALSE
[12:08:03.686]      // skip if-block "connectionFlash && DoOptionByteLoading"
[12:08:03.686]    </control>
[12:08:03.686]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[12:08:03.686]      // if-block "DoDebugPortStop"
[12:08:03.686]        // =>  TRUE
[12:08:03.687]      <block atomic="false" info="">
[12:08:03.687]        WriteDP(DP_SELECT, 0x00000000);
[12:08:03.687]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[12:08:03.687]        WriteDP(DP_CTRL_STAT, 0x00000000);
[12:08:03.688]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[12:08:03.688]      </block>
[12:08:03.689]      // end if-block "DoDebugPortStop"
[12:08:03.689]    </control>
[12:08:03.689]  </sequence>
[12:08:03.689]  
[12:26:01.347]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[12:26:01.347]  
[12:26:01.347]  <debugvars>
[12:26:01.348]    // Pre-defined
[12:26:01.348]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:26:01.348]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:26:01.348]    __dp=0x00000000
[12:26:01.349]    __ap=0x00000000
[12:26:01.349]    __traceout=0x00000000      (Trace Disabled)
[12:26:01.349]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:26:01.350]    __FlashAddr=0x00000000
[12:26:01.350]    __FlashLen=0x00000000
[12:26:01.350]    __FlashArg=0x00000000
[12:26:01.351]    __FlashOp=0x00000000
[12:26:01.351]    __Result=0x00000000
[12:26:01.351]    
[12:26:01.351]    // User-defined
[12:26:01.351]    DbgMCU_CR=0x00000007
[12:26:01.351]    DbgMCU_APB1_Fz=0x00000000
[12:26:01.352]    DbgMCU_APB2_Fz=0x00000000
[12:26:01.352]    DoOptionByteLoading=0x00000000
[12:26:01.352]  </debugvars>
[12:26:01.352]  
[12:26:01.352]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[12:26:01.352]    <block atomic="false" info="">
[12:26:01.353]      Sequence("CheckID");
[12:26:01.353]        <sequence name="CheckID" Pname="" disable="false" info="">
[12:26:01.353]          <block atomic="false" info="">
[12:26:01.353]            __var pidr1 = 0;
[12:26:01.353]              // -> [pidr1 <= 0x00000000]
[12:26:01.354]            __var pidr2 = 0;
[12:26:01.354]              // -> [pidr2 <= 0x00000000]
[12:26:01.354]            __var jep106id = 0;
[12:26:01.354]              // -> [jep106id <= 0x00000000]
[12:26:01.354]            __var ROMTableBase = 0;
[12:26:01.354]              // -> [ROMTableBase <= 0x00000000]
[12:26:01.355]            __ap = 0;      // AHB-AP
[12:26:01.355]              // -> [__ap <= 0x00000000]
[12:26:01.355]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[12:26:01.355]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[12:26:01.355]              // -> [ROMTableBase <= 0xF0000000]
[12:26:01.355]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[12:26:01.357]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[12:26:01.358]              // -> [pidr1 <= 0x00000004]
[12:26:01.358]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[12:26:01.359]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[12:26:01.359]              // -> [pidr2 <= 0x0000000A]
[12:26:01.359]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[12:26:01.360]              // -> [jep106id <= 0x00000020]
[12:26:01.360]          </block>
[12:26:01.360]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[12:26:01.361]            // if-block "jep106id != 0x20"
[12:26:01.361]              // =>  FALSE
[12:26:01.361]            // skip if-block "jep106id != 0x20"
[12:26:01.362]          </control>
[12:26:01.362]        </sequence>
[12:26:01.362]    </block>
[12:26:01.362]  </sequence>
[12:26:01.362]  
[12:26:01.375]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[12:26:01.375]  
[12:26:01.375]  <debugvars>
[12:26:01.375]    // Pre-defined
[12:26:01.376]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:26:01.376]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:26:01.376]    __dp=0x00000000
[12:26:01.376]    __ap=0x00000000
[12:26:01.377]    __traceout=0x00000000      (Trace Disabled)
[12:26:01.377]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:26:01.377]    __FlashAddr=0x00000000
[12:26:01.377]    __FlashLen=0x00000000
[12:26:01.377]    __FlashArg=0x00000000
[12:26:01.378]    __FlashOp=0x00000000
[12:26:01.378]    __Result=0x00000000
[12:26:01.378]    
[12:26:01.378]    // User-defined
[12:26:01.378]    DbgMCU_CR=0x00000007
[12:26:01.378]    DbgMCU_APB1_Fz=0x00000000
[12:26:01.378]    DbgMCU_APB2_Fz=0x00000000
[12:26:01.379]    DoOptionByteLoading=0x00000000
[12:26:01.379]  </debugvars>
[12:26:01.380]  
[12:26:01.380]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[12:26:01.380]    <block atomic="false" info="">
[12:26:01.381]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[12:26:01.382]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[12:26:01.382]    </block>
[12:26:01.383]    <block atomic="false" info="DbgMCU registers">
[12:26:01.383]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[12:26:01.384]        // -> [Read32(0x40021034) => 0x00000201]   (__dp=0x00000000, __ap=0x00000000)
[12:26:01.385]        // -> [Write32(0x40021034, 0x00400201)]   (__dp=0x00000000, __ap=0x00000000)
[12:26:01.385]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[12:26:01.386]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[12:26:01.386]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[12:26:01.387]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[12:26:01.387]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[12:26:01.388]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[12:26:01.388]    </block>
[12:26:01.388]  </sequence>
[12:26:01.388]  
[12:26:08.556]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[12:26:08.556]  
[12:26:08.557]  <debugvars>
[12:26:08.558]    // Pre-defined
[12:26:08.559]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:26:08.559]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:26:08.559]    __dp=0x00000000
[12:26:08.561]    __ap=0x00000000
[12:26:08.561]    __traceout=0x00000000      (Trace Disabled)
[12:26:08.561]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:26:08.561]    __FlashAddr=0x00000000
[12:26:08.565]    __FlashLen=0x00000000
[12:26:08.565]    __FlashArg=0x00000000
[12:26:08.565]    __FlashOp=0x00000000
[12:26:08.565]    __Result=0x00000000
[12:26:08.565]    
[12:26:08.565]    // User-defined
[12:26:08.565]    DbgMCU_CR=0x00000007
[12:26:08.565]    DbgMCU_APB1_Fz=0x00000000
[12:26:08.565]    DbgMCU_APB2_Fz=0x00000000
[12:26:08.565]    DoOptionByteLoading=0x00000000
[12:26:08.565]  </debugvars>
[12:26:08.570]  
[12:26:08.571]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[12:26:08.571]    <block atomic="false" info="">
[12:26:08.571]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[12:26:08.571]        // -> [connectionFlash <= 0x00000001]
[12:26:08.571]      __var FLASH_BASE = 0x40022000 ;
[12:26:08.573]        // -> [FLASH_BASE <= 0x40022000]
[12:26:08.573]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[12:26:08.573]        // -> [FLASH_CR <= 0x40022004]
[12:26:08.574]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[12:26:08.574]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[12:26:08.574]      __var LOCK_BIT = ( 1 << 0 ) ;
[12:26:08.574]        // -> [LOCK_BIT <= 0x00000001]
[12:26:08.575]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[12:26:08.575]        // -> [OPTLOCK_BIT <= 0x00000004]
[12:26:08.575]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[12:26:08.575]        // -> [FLASH_KEYR <= 0x4002200C]
[12:26:08.576]      __var FLASH_KEY1 = 0x89ABCDEF ;
[12:26:08.576]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[12:26:08.576]      __var FLASH_KEY2 = 0x02030405 ;
[12:26:08.577]        // -> [FLASH_KEY2 <= 0x02030405]
[12:26:08.577]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[12:26:08.577]        // -> [FLASH_OPTKEYR <= 0x40022014]
[12:26:08.578]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[12:26:08.578]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[12:26:08.578]      __var FLASH_OPTKEY2 = 0x24252627 ;
[12:26:08.579]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[12:26:08.579]      __var FLASH_CR_Value = 0 ;
[12:26:08.579]        // -> [FLASH_CR_Value <= 0x00000000]
[12:26:08.579]      __var DoDebugPortStop = 1 ;
[12:26:08.579]        // -> [DoDebugPortStop <= 0x00000001]
[12:26:08.580]      __var DP_CTRL_STAT = 0x4 ;
[12:26:08.580]        // -> [DP_CTRL_STAT <= 0x00000004]
[12:26:08.580]      __var DP_SELECT = 0x8 ;
[12:26:08.580]        // -> [DP_SELECT <= 0x00000008]
[12:26:08.580]    </block>
[12:26:08.581]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[12:26:08.581]      // if-block "connectionFlash && DoOptionByteLoading"
[12:26:08.581]        // =>  FALSE
[12:26:08.581]      // skip if-block "connectionFlash && DoOptionByteLoading"
[12:26:08.581]    </control>
[12:26:08.581]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[12:26:08.582]      // if-block "DoDebugPortStop"
[12:26:08.582]        // =>  TRUE
[12:26:08.582]      <block atomic="false" info="">
[12:26:08.582]        WriteDP(DP_SELECT, 0x00000000);
[12:26:08.583]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[12:26:08.583]        WriteDP(DP_CTRL_STAT, 0x00000000);
[12:26:08.583]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[12:26:08.584]      </block>
[12:26:08.584]      // end if-block "DoDebugPortStop"
[12:26:08.584]    </control>
[12:26:08.585]  </sequence>
[12:26:08.585]  
[14:49:21.655]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:49:21.655]  
[14:49:21.674]  <debugvars>
[14:49:21.674]    // Pre-defined
[14:49:21.674]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:49:21.674]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:49:21.674]    __dp=0x00000000
[14:49:21.675]    __ap=0x00000000
[14:49:21.675]    __traceout=0x00000000      (Trace Disabled)
[14:49:21.675]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:49:21.676]    __FlashAddr=0x00000000
[14:49:21.676]    __FlashLen=0x00000000
[14:49:21.676]    __FlashArg=0x00000000
[14:49:21.677]    __FlashOp=0x00000000
[14:49:21.677]    __Result=0x00000000
[14:49:21.677]    
[14:49:21.677]    // User-defined
[14:49:21.677]    DbgMCU_CR=0x00000007
[14:49:21.678]    DbgMCU_APB1_Fz=0x00000000
[14:49:21.679]    DbgMCU_APB2_Fz=0x00000000
[14:49:21.679]    DoOptionByteLoading=0x00000000
[14:49:21.679]  </debugvars>
[14:49:21.680]  
[14:49:21.680]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:49:21.680]    <block atomic="false" info="">
[14:49:21.680]      Sequence("CheckID");
[14:49:21.680]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:49:21.681]          <block atomic="false" info="">
[14:49:21.681]            __var pidr1 = 0;
[14:49:21.681]              // -> [pidr1 <= 0x00000000]
[14:49:21.682]            __var pidr2 = 0;
[14:49:21.682]              // -> [pidr2 <= 0x00000000]
[14:49:21.682]            __var jep106id = 0;
[14:49:21.684]              // -> [jep106id <= 0x00000000]
[14:49:21.684]            __var ROMTableBase = 0;
[14:49:21.684]              // -> [ROMTableBase <= 0x00000000]
[14:49:21.684]            __ap = 0;      // AHB-AP
[14:49:21.684]              // -> [__ap <= 0x00000000]
[14:49:21.685]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:49:21.685]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:49:21.685]              // -> [ROMTableBase <= 0xF0000000]
[14:49:21.686]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:49:21.687]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:49:21.687]              // -> [pidr1 <= 0x00000004]
[14:49:21.687]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:49:21.688]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:49:21.688]              // -> [pidr2 <= 0x0000000A]
[14:49:21.688]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:49:21.688]              // -> [jep106id <= 0x00000020]
[14:49:21.688]          </block>
[14:49:21.690]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:49:21.690]            // if-block "jep106id != 0x20"
[14:49:21.690]              // =>  FALSE
[14:49:21.691]            // skip if-block "jep106id != 0x20"
[14:49:21.691]          </control>
[14:49:21.691]        </sequence>
[14:49:21.692]    </block>
[14:49:21.692]  </sequence>
[14:49:21.692]  
[14:49:21.706]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:49:21.706]  
[14:49:21.723]  <debugvars>
[14:49:21.723]    // Pre-defined
[14:49:21.723]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:49:21.723]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:49:21.723]    __dp=0x00000000
[14:49:21.724]    __ap=0x00000000
[14:49:21.724]    __traceout=0x00000000      (Trace Disabled)
[14:49:21.725]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:49:21.725]    __FlashAddr=0x00000000
[14:49:21.725]    __FlashLen=0x00000000
[14:49:21.725]    __FlashArg=0x00000000
[14:49:21.725]    __FlashOp=0x00000000
[14:49:21.725]    __Result=0x00000000
[14:49:21.726]    
[14:49:21.726]    // User-defined
[14:49:21.726]    DbgMCU_CR=0x00000007
[14:49:21.727]    DbgMCU_APB1_Fz=0x00000000
[14:49:21.727]    DbgMCU_APB2_Fz=0x00000000
[14:49:21.727]    DoOptionByteLoading=0x00000000
[14:49:21.727]  </debugvars>
[14:49:21.727]  
[14:49:21.728]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:49:21.728]    <block atomic="false" info="">
[14:49:21.728]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:49:21.729]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:49:21.729]    </block>
[14:49:21.730]    <block atomic="false" info="DbgMCU registers">
[14:49:21.730]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:49:21.743]        // -> [Read32(0x40021034) => 0x00000201]   (__dp=0x00000000, __ap=0x00000000)
[14:49:21.744]        // -> [Write32(0x40021034, 0x00400201)]   (__dp=0x00000000, __ap=0x00000000)
[14:49:21.744]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:49:21.745]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:49:21.745]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:49:21.747]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:49:21.747]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:49:21.748]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:49:21.749]    </block>
[14:49:21.749]  </sequence>
[14:49:21.749]  
[14:49:29.376]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:49:29.376]  
[14:49:29.377]  <debugvars>
[14:49:29.377]    // Pre-defined
[14:49:29.378]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:49:29.378]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:49:29.378]    __dp=0x00000000
[14:49:29.378]    __ap=0x00000000
[14:49:29.379]    __traceout=0x00000000      (Trace Disabled)
[14:49:29.379]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:49:29.379]    __FlashAddr=0x00000000
[14:49:29.379]    __FlashLen=0x00000000
[14:49:29.380]    __FlashArg=0x00000000
[14:49:29.380]    __FlashOp=0x00000000
[14:49:29.381]    __Result=0x00000000
[14:49:29.381]    
[14:49:29.381]    // User-defined
[14:49:29.381]    DbgMCU_CR=0x00000007
[14:49:29.382]    DbgMCU_APB1_Fz=0x00000000
[14:49:29.382]    DbgMCU_APB2_Fz=0x00000000
[14:49:29.382]    DoOptionByteLoading=0x00000000
[14:49:29.382]  </debugvars>
[14:49:29.382]  
[14:49:29.383]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:49:29.383]    <block atomic="false" info="">
[14:49:29.383]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:49:29.383]        // -> [connectionFlash <= 0x00000001]
[14:49:29.384]      __var FLASH_BASE = 0x40022000 ;
[14:49:29.384]        // -> [FLASH_BASE <= 0x40022000]
[14:49:29.384]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:49:29.385]        // -> [FLASH_CR <= 0x40022004]
[14:49:29.385]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:49:29.385]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:49:29.385]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:49:29.385]        // -> [LOCK_BIT <= 0x00000001]
[14:49:29.385]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:49:29.385]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:49:29.387]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:49:29.387]        // -> [FLASH_KEYR <= 0x4002200C]
[14:49:29.387]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:49:29.387]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:49:29.387]      __var FLASH_KEY2 = 0x02030405 ;
[14:49:29.387]        // -> [FLASH_KEY2 <= 0x02030405]
[14:49:29.388]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:49:29.389]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:49:29.389]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:49:29.389]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:49:29.389]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:49:29.389]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:49:29.390]      __var FLASH_CR_Value = 0 ;
[14:49:29.390]        // -> [FLASH_CR_Value <= 0x00000000]
[14:49:29.390]      __var DoDebugPortStop = 1 ;
[14:49:29.390]        // -> [DoDebugPortStop <= 0x00000001]
[14:49:29.402]      __var DP_CTRL_STAT = 0x4 ;
[14:49:29.403]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:49:29.403]      __var DP_SELECT = 0x8 ;
[14:49:29.403]        // -> [DP_SELECT <= 0x00000008]
[14:49:29.404]    </block>
[14:49:29.404]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:49:29.405]      // if-block "connectionFlash && DoOptionByteLoading"
[14:49:29.405]        // =>  FALSE
[14:49:29.406]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:49:29.406]    </control>
[14:49:29.406]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:49:29.407]      // if-block "DoDebugPortStop"
[14:49:29.407]        // =>  TRUE
[14:49:29.407]      <block atomic="false" info="">
[14:49:29.408]        WriteDP(DP_SELECT, 0x00000000);
[14:49:29.419]  
[14:49:29.419]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[14:49:29.419]  
[14:49:29.421]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:49:29.422]      </block>
[14:49:29.422]      // end if-block "DoDebugPortStop"
[14:49:29.423]    </control>
[14:49:29.423]  </sequence>
[14:49:29.424]  
[14:50:09.798]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:50:09.798]  
[14:50:09.798]  <debugvars>
[14:50:09.798]    // Pre-defined
[14:50:09.799]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:50:09.799]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:50:09.799]    __dp=0x00000000
[14:50:09.800]    __ap=0x00000000
[14:50:09.800]    __traceout=0x00000000      (Trace Disabled)
[14:50:09.800]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:50:09.800]    __FlashAddr=0x00000000
[14:50:09.801]    __FlashLen=0x00000000
[14:50:09.801]    __FlashArg=0x00000000
[14:50:09.801]    __FlashOp=0x00000000
[14:50:09.801]    __Result=0x00000000
[14:50:09.801]    
[14:50:09.801]    // User-defined
[14:50:09.802]    DbgMCU_CR=0x00000007
[14:50:09.802]    DbgMCU_APB1_Fz=0x00000000
[14:50:09.802]    DbgMCU_APB2_Fz=0x00000000
[14:50:09.802]    DoOptionByteLoading=0x00000000
[14:50:09.802]  </debugvars>
[14:50:09.802]  
[14:50:09.802]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:50:09.802]    <block atomic="false" info="">
[14:50:09.803]      Sequence("CheckID");
[14:50:09.803]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:50:09.804]          <block atomic="false" info="">
[14:50:09.804]            __var pidr1 = 0;
[14:50:09.804]              // -> [pidr1 <= 0x00000000]
[14:50:09.804]            __var pidr2 = 0;
[14:50:09.805]              // -> [pidr2 <= 0x00000000]
[14:50:09.805]            __var jep106id = 0;
[14:50:09.806]              // -> [jep106id <= 0x00000000]
[14:50:09.806]            __var ROMTableBase = 0;
[14:50:09.807]              // -> [ROMTableBase <= 0x00000000]
[14:50:09.807]            __ap = 0;      // AHB-AP
[14:50:09.808]              // -> [__ap <= 0x00000000]
[14:50:09.808]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:50:09.808]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:50:09.808]              // -> [ROMTableBase <= 0xF0000000]
[14:50:09.808]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:50:09.811]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:50:09.811]              // -> [pidr1 <= 0x00000004]
[14:50:09.811]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:50:09.812]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:50:09.812]              // -> [pidr2 <= 0x0000000A]
[14:50:09.813]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:50:09.813]              // -> [jep106id <= 0x00000020]
[14:50:09.813]          </block>
[14:50:09.813]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:50:09.814]            // if-block "jep106id != 0x20"
[14:50:09.814]              // =>  FALSE
[14:50:09.814]            // skip if-block "jep106id != 0x20"
[14:50:09.814]          </control>
[14:50:09.815]        </sequence>
[14:50:09.815]    </block>
[14:50:09.815]  </sequence>
[14:50:09.815]  
[14:50:09.828]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:50:09.828]  
[14:50:09.843]  <debugvars>
[14:50:09.844]    // Pre-defined
[14:50:09.844]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:50:09.845]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:50:09.845]    __dp=0x00000000
[14:50:09.846]    __ap=0x00000000
[14:50:09.846]    __traceout=0x00000000      (Trace Disabled)
[14:50:09.847]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:50:09.847]    __FlashAddr=0x00000000
[14:50:09.848]    __FlashLen=0x00000000
[14:50:09.848]    __FlashArg=0x00000000
[14:50:09.849]    __FlashOp=0x00000000
[14:50:09.849]    __Result=0x00000000
[14:50:09.849]    
[14:50:09.849]    // User-defined
[14:50:09.850]    DbgMCU_CR=0x00000007
[14:50:09.851]    DbgMCU_APB1_Fz=0x00000000
[14:50:09.851]    DbgMCU_APB2_Fz=0x00000000
[14:50:09.851]    DoOptionByteLoading=0x00000000
[14:50:09.852]  </debugvars>
[14:50:09.852]  
[14:50:09.853]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:50:09.853]    <block atomic="false" info="">
[14:50:09.854]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:50:09.855]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:50:09.856]    </block>
[14:50:09.856]    <block atomic="false" info="DbgMCU registers">
[14:50:09.857]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:50:09.858]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[14:50:09.859]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[14:50:09.859]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:50:09.860]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:50:09.860]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:50:09.861]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:50:09.861]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:50:09.862]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:50:09.863]    </block>
[14:50:09.863]  </sequence>
[14:50:09.863]  
[14:50:13.767]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:50:13.767]  
[14:50:13.768]  <debugvars>
[14:50:13.768]    // Pre-defined
[14:50:13.768]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:50:13.768]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:50:13.769]    __dp=0x00000000
[14:50:13.769]    __ap=0x00000000
[14:50:13.769]    __traceout=0x00000000      (Trace Disabled)
[14:50:13.769]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:50:13.769]    __FlashAddr=0x00000000
[14:50:13.769]    __FlashLen=0x00000000
[14:50:13.770]    __FlashArg=0x00000000
[14:50:13.770]    __FlashOp=0x00000000
[14:50:13.770]    __Result=0x00000000
[14:50:13.770]    
[14:50:13.770]    // User-defined
[14:50:13.770]    DbgMCU_CR=0x00000007
[14:50:13.771]    DbgMCU_APB1_Fz=0x00000000
[14:50:13.771]    DbgMCU_APB2_Fz=0x00000000
[14:50:13.771]    DoOptionByteLoading=0x00000000
[14:50:13.771]  </debugvars>
[14:50:13.772]  
[14:50:13.772]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:50:13.772]    <block atomic="false" info="">
[14:50:13.772]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:50:13.772]        // -> [connectionFlash <= 0x00000001]
[14:50:13.772]      __var FLASH_BASE = 0x40022000 ;
[14:50:13.773]        // -> [FLASH_BASE <= 0x40022000]
[14:50:13.773]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:50:13.773]        // -> [FLASH_CR <= 0x40022004]
[14:50:13.773]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:50:13.773]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:50:13.774]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:50:13.774]        // -> [LOCK_BIT <= 0x00000001]
[14:50:13.774]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:50:13.774]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:50:13.775]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:50:13.775]        // -> [FLASH_KEYR <= 0x4002200C]
[14:50:13.775]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:50:13.776]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:50:13.776]      __var FLASH_KEY2 = 0x02030405 ;
[14:50:13.776]        // -> [FLASH_KEY2 <= 0x02030405]
[14:50:13.777]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:50:13.777]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:50:13.777]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:50:13.777]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:50:13.777]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:50:13.778]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:50:13.778]      __var FLASH_CR_Value = 0 ;
[14:50:13.778]        // -> [FLASH_CR_Value <= 0x00000000]
[14:50:13.778]      __var DoDebugPortStop = 1 ;
[14:50:13.779]        // -> [DoDebugPortStop <= 0x00000001]
[14:50:13.779]      __var DP_CTRL_STAT = 0x4 ;
[14:50:13.779]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:50:13.779]      __var DP_SELECT = 0x8 ;
[14:50:13.779]        // -> [DP_SELECT <= 0x00000008]
[14:50:13.781]    </block>
[14:50:13.781]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:50:13.781]      // if-block "connectionFlash && DoOptionByteLoading"
[14:50:13.781]        // =>  FALSE
[14:50:13.782]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:50:13.782]    </control>
[14:50:13.782]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:50:13.782]      // if-block "DoDebugPortStop"
[14:50:13.782]        // =>  TRUE
[14:50:13.783]      <block atomic="false" info="">
[14:50:13.783]        WriteDP(DP_SELECT, 0x00000000);
[14:50:13.784]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:50:13.784]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:50:13.784]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:50:13.785]      </block>
[14:50:13.785]      // end if-block "DoDebugPortStop"
[14:50:13.785]    </control>
[14:50:13.785]  </sequence>
[14:50:13.785]  
[14:50:31.017]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:50:31.017]  
[14:50:31.017]  <debugvars>
[14:50:31.017]    // Pre-defined
[14:50:31.018]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:50:31.018]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:50:31.018]    __dp=0x00000000
[14:50:31.018]    __ap=0x00000000
[14:50:31.019]    __traceout=0x00000000      (Trace Disabled)
[14:50:31.019]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:50:31.020]    __FlashAddr=0x00000000
[14:50:31.020]    __FlashLen=0x00000000
[14:50:31.020]    __FlashArg=0x00000000
[14:50:31.020]    __FlashOp=0x00000000
[14:50:31.021]    __Result=0x00000000
[14:50:31.021]    
[14:50:31.021]    // User-defined
[14:50:31.021]    DbgMCU_CR=0x00000007
[14:50:31.021]    DbgMCU_APB1_Fz=0x00000000
[14:50:31.022]    DbgMCU_APB2_Fz=0x00000000
[14:50:31.022]    DoOptionByteLoading=0x00000000
[14:50:31.022]  </debugvars>
[14:50:31.022]  
[14:50:31.022]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:50:31.023]    <block atomic="false" info="">
[14:50:31.023]      Sequence("CheckID");
[14:50:31.023]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:50:31.023]          <block atomic="false" info="">
[14:50:31.023]            __var pidr1 = 0;
[14:50:31.024]              // -> [pidr1 <= 0x00000000]
[14:50:31.024]            __var pidr2 = 0;
[14:50:31.024]              // -> [pidr2 <= 0x00000000]
[14:50:31.024]            __var jep106id = 0;
[14:50:31.024]              // -> [jep106id <= 0x00000000]
[14:50:31.025]            __var ROMTableBase = 0;
[14:50:31.025]              // -> [ROMTableBase <= 0x00000000]
[14:50:31.025]            __ap = 0;      // AHB-AP
[14:50:31.025]              // -> [__ap <= 0x00000000]
[14:50:31.025]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:50:31.026]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:50:31.026]              // -> [ROMTableBase <= 0xF0000000]
[14:50:31.027]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:50:31.028]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:50:31.028]              // -> [pidr1 <= 0x00000004]
[14:50:31.028]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:50:31.029]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:50:31.030]              // -> [pidr2 <= 0x0000000A]
[14:50:31.030]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:50:31.030]              // -> [jep106id <= 0x00000020]
[14:50:31.031]          </block>
[14:50:31.031]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:50:31.031]            // if-block "jep106id != 0x20"
[14:50:31.031]              // =>  FALSE
[14:50:31.032]            // skip if-block "jep106id != 0x20"
[14:50:31.032]          </control>
[14:50:31.032]        </sequence>
[14:50:31.032]    </block>
[14:50:31.033]  </sequence>
[14:50:31.033]  
[14:50:31.045]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:50:31.045]  
[14:50:31.068]  <debugvars>
[14:50:31.068]    // Pre-defined
[14:50:31.069]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:50:31.069]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:50:31.070]    __dp=0x00000000
[14:50:31.070]    __ap=0x00000000
[14:50:31.071]    __traceout=0x00000000      (Trace Disabled)
[14:50:31.071]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:50:31.072]    __FlashAddr=0x00000000
[14:50:31.073]    __FlashLen=0x00000000
[14:50:31.073]    __FlashArg=0x00000000
[14:50:31.073]    __FlashOp=0x00000000
[14:50:31.074]    __Result=0x00000000
[14:50:31.074]    
[14:50:31.074]    // User-defined
[14:50:31.074]    DbgMCU_CR=0x00000007
[14:50:31.075]    DbgMCU_APB1_Fz=0x00000000
[14:50:31.075]    DbgMCU_APB2_Fz=0x00000000
[14:50:31.075]    DoOptionByteLoading=0x00000000
[14:50:31.076]  </debugvars>
[14:50:31.076]  
[14:50:31.076]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:50:31.077]    <block atomic="false" info="">
[14:50:31.077]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:50:31.078]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:50:31.078]    </block>
[14:50:31.078]    <block atomic="false" info="DbgMCU registers">
[14:50:31.078]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:50:31.079]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[14:50:31.081]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[14:50:31.081]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:50:31.082]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:50:31.082]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:50:31.083]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:50:31.083]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:50:31.084]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:50:31.084]    </block>
[14:50:31.084]  </sequence>
[14:50:31.085]  
[14:50:38.154]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:50:38.154]  
[14:50:38.155]  <debugvars>
[14:50:38.156]    // Pre-defined
[14:50:38.157]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:50:38.157]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:50:38.157]    __dp=0x00000000
[14:50:38.158]    __ap=0x00000000
[14:50:38.158]    __traceout=0x00000000      (Trace Disabled)
[14:50:38.159]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:50:38.159]    __FlashAddr=0x00000000
[14:50:38.159]    __FlashLen=0x00000000
[14:50:38.160]    __FlashArg=0x00000000
[14:50:38.160]    __FlashOp=0x00000000
[14:50:38.160]    __Result=0x00000000
[14:50:38.161]    
[14:50:38.161]    // User-defined
[14:50:38.161]    DbgMCU_CR=0x00000007
[14:50:38.161]    DbgMCU_APB1_Fz=0x00000000
[14:50:38.162]    DbgMCU_APB2_Fz=0x00000000
[14:50:38.162]    DoOptionByteLoading=0x00000000
[14:50:38.162]  </debugvars>
[14:50:38.162]  
[14:50:38.163]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:50:38.163]    <block atomic="false" info="">
[14:50:38.163]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:50:38.163]        // -> [connectionFlash <= 0x00000001]
[14:50:38.164]      __var FLASH_BASE = 0x40022000 ;
[14:50:38.164]        // -> [FLASH_BASE <= 0x40022000]
[14:50:38.164]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:50:38.164]        // -> [FLASH_CR <= 0x40022004]
[14:50:38.165]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:50:38.165]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:50:38.165]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:50:38.166]        // -> [LOCK_BIT <= 0x00000001]
[14:50:38.166]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:50:38.166]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:50:38.167]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:50:38.167]        // -> [FLASH_KEYR <= 0x4002200C]
[14:50:38.167]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:50:38.167]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:50:38.167]      __var FLASH_KEY2 = 0x02030405 ;
[14:50:38.168]        // -> [FLASH_KEY2 <= 0x02030405]
[14:50:38.168]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:50:38.169]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:50:38.169]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:50:38.170]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:50:38.170]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:50:38.170]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:50:38.170]      __var FLASH_CR_Value = 0 ;
[14:50:38.170]        // -> [FLASH_CR_Value <= 0x00000000]
[14:50:38.171]      __var DoDebugPortStop = 1 ;
[14:50:38.171]        // -> [DoDebugPortStop <= 0x00000001]
[14:50:38.171]      __var DP_CTRL_STAT = 0x4 ;
[14:50:38.171]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:50:38.172]      __var DP_SELECT = 0x8 ;
[14:50:38.172]        // -> [DP_SELECT <= 0x00000008]
[14:50:38.172]    </block>
[14:50:38.172]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:50:38.172]      // if-block "connectionFlash && DoOptionByteLoading"
[14:50:38.173]        // =>  FALSE
[14:50:38.173]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:50:38.173]    </control>
[14:50:38.173]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:50:38.173]      // if-block "DoDebugPortStop"
[14:50:38.174]        // =>  TRUE
[14:50:38.174]      <block atomic="false" info="">
[14:50:38.174]        WriteDP(DP_SELECT, 0x00000000);
[14:50:38.174]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:50:38.175]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:50:38.175]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:50:38.175]      </block>
[14:50:38.176]      // end if-block "DoDebugPortStop"
[14:50:38.176]    </control>
[14:50:38.176]  </sequence>
[14:50:38.176]  
[14:51:35.891]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:51:35.891]  
[14:51:35.892]  <debugvars>
[14:51:35.892]    // Pre-defined
[14:51:35.892]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:51:35.893]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[14:51:35.893]    __dp=0x00000000
[14:51:35.893]    __ap=0x00000000
[14:51:35.893]    __traceout=0x00000000      (Trace Disabled)
[14:51:35.894]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:51:35.894]    __FlashAddr=0x00000000
[14:51:35.894]    __FlashLen=0x00000000
[14:51:35.894]    __FlashArg=0x00000000
[14:51:35.895]    __FlashOp=0x00000000
[14:51:35.895]    __Result=0x00000000
[14:51:35.895]    
[14:51:35.895]    // User-defined
[14:51:35.896]    DbgMCU_CR=0x00000007
[14:51:35.896]    DbgMCU_APB1_Fz=0x00000000
[14:51:35.896]    DbgMCU_APB2_Fz=0x00000000
[14:51:35.896]    DoOptionByteLoading=0x00000000
[14:51:35.897]  </debugvars>
[14:51:35.897]  
[14:51:35.897]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:51:35.897]    <block atomic="false" info="">
[14:51:35.897]      Sequence("CheckID");
[14:51:35.898]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:51:35.898]          <block atomic="false" info="">
[14:51:35.898]            __var pidr1 = 0;
[14:51:35.899]              // -> [pidr1 <= 0x00000000]
[14:51:35.899]            __var pidr2 = 0;
[14:51:35.899]              // -> [pidr2 <= 0x00000000]
[14:51:35.899]            __var jep106id = 0;
[14:51:35.900]              // -> [jep106id <= 0x00000000]
[14:51:35.900]            __var ROMTableBase = 0;
[14:51:35.900]              // -> [ROMTableBase <= 0x00000000]
[14:51:35.900]            __ap = 0;      // AHB-AP
[14:51:35.900]              // -> [__ap <= 0x00000000]
[14:51:35.900]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:51:35.901]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:51:35.901]              // -> [ROMTableBase <= 0xF0000000]
[14:51:35.902]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:51:35.904]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:51:35.904]              // -> [pidr1 <= 0x00000004]
[14:51:35.905]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:51:35.906]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:51:35.906]              // -> [pidr2 <= 0x0000000A]
[14:51:35.906]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:51:35.907]              // -> [jep106id <= 0x00000020]
[14:51:35.907]          </block>
[14:51:35.907]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:51:35.907]            // if-block "jep106id != 0x20"
[14:51:35.907]              // =>  FALSE
[14:51:35.907]            // skip if-block "jep106id != 0x20"
[14:51:35.908]          </control>
[14:51:35.908]        </sequence>
[14:51:35.908]    </block>
[14:51:35.908]  </sequence>
[14:51:35.909]  
[14:51:35.921]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:51:35.921]  
[14:51:35.933]  <debugvars>
[14:51:35.933]    // Pre-defined
[14:51:35.933]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:51:35.933]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[14:51:35.934]    __dp=0x00000000
[14:51:35.935]    __ap=0x00000000
[14:51:35.935]    __traceout=0x00000000      (Trace Disabled)
[14:51:35.936]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:51:35.936]    __FlashAddr=0x00000000
[14:51:35.937]    __FlashLen=0x00000000
[14:51:35.937]    __FlashArg=0x00000000
[14:51:35.938]    __FlashOp=0x00000000
[14:51:35.938]    __Result=0x00000000
[14:51:35.938]    
[14:51:35.938]    // User-defined
[14:51:35.939]    DbgMCU_CR=0x00000007
[14:51:35.939]    DbgMCU_APB1_Fz=0x00000000
[14:51:35.939]    DbgMCU_APB2_Fz=0x00000000
[14:51:35.940]    DoOptionByteLoading=0x00000000
[14:51:35.940]  </debugvars>
[14:51:35.940]  
[14:51:35.940]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:51:35.941]    <block atomic="false" info="">
[14:51:35.941]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:51:35.942]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:51:35.942]    </block>
[14:51:35.942]    <block atomic="false" info="DbgMCU registers">
[14:51:35.943]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:51:35.944]        // -> [Read32(0x40021034) => 0x00000201]   (__dp=0x00000000, __ap=0x00000000)
[14:51:35.944]        // -> [Write32(0x40021034, 0x00400201)]   (__dp=0x00000000, __ap=0x00000000)
[14:51:35.945]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:51:35.955]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:51:35.957]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:51:35.958]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:51:35.958]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:51:35.959]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:51:35.959]    </block>
[14:51:35.960]  </sequence>
[14:51:35.960]  
[14:53:48.491]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:53:48.491]  
[14:53:48.492]  <debugvars>
[14:53:48.492]    // Pre-defined
[14:53:48.493]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:53:48.493]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[14:53:48.494]    __dp=0x00000000
[14:53:48.494]    __ap=0x00000000
[14:53:48.495]    __traceout=0x00000000      (Trace Disabled)
[14:53:48.495]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:53:48.495]    __FlashAddr=0x00000000
[14:53:48.496]    __FlashLen=0x00000000
[14:53:48.496]    __FlashArg=0x00000000
[14:53:48.496]    __FlashOp=0x00000000
[14:53:48.496]    __Result=0x00000000
[14:53:48.496]    
[14:53:48.496]    // User-defined
[14:53:48.497]    DbgMCU_CR=0x00000007
[14:53:48.497]    DbgMCU_APB1_Fz=0x00000000
[14:53:48.497]    DbgMCU_APB2_Fz=0x00000000
[14:53:48.497]    DoOptionByteLoading=0x00000000
[14:53:48.497]  </debugvars>
[14:53:48.498]  
[14:53:48.499]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:53:48.499]    <block atomic="false" info="">
[14:53:48.499]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:53:48.500]        // -> [connectionFlash <= 0x00000000]
[14:53:48.500]      __var FLASH_BASE = 0x40022000 ;
[14:53:48.500]        // -> [FLASH_BASE <= 0x40022000]
[14:53:48.500]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:53:48.500]        // -> [FLASH_CR <= 0x40022004]
[14:53:48.501]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:53:48.501]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:53:48.501]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:53:48.501]        // -> [LOCK_BIT <= 0x00000001]
[14:53:48.501]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:53:48.502]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:53:48.502]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:53:48.502]        // -> [FLASH_KEYR <= 0x4002200C]
[14:53:48.502]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:53:48.502]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:53:48.502]      __var FLASH_KEY2 = 0x02030405 ;
[14:53:48.503]        // -> [FLASH_KEY2 <= 0x02030405]
[14:53:48.503]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:53:48.503]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:53:48.503]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:53:48.503]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:53:48.504]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:53:48.504]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:53:48.504]      __var FLASH_CR_Value = 0 ;
[14:53:48.504]        // -> [FLASH_CR_Value <= 0x00000000]
[14:53:48.504]      __var DoDebugPortStop = 1 ;
[14:53:48.504]        // -> [DoDebugPortStop <= 0x00000001]
[14:53:48.505]      __var DP_CTRL_STAT = 0x4 ;
[14:53:48.505]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:53:48.505]      __var DP_SELECT = 0x8 ;
[14:53:48.505]        // -> [DP_SELECT <= 0x00000008]
[14:53:48.505]    </block>
[14:53:48.506]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:53:48.506]      // if-block "connectionFlash && DoOptionByteLoading"
[14:53:48.506]        // =>  FALSE
[14:53:48.506]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:53:48.506]    </control>
[14:53:48.507]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:53:48.507]      // if-block "DoDebugPortStop"
[14:53:48.507]        // =>  TRUE
[14:53:48.507]      <block atomic="false" info="">
[14:53:48.507]        WriteDP(DP_SELECT, 0x00000000);
[14:53:48.508]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:53:48.508]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:53:48.509]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:53:48.509]      </block>
[14:53:48.509]      // end if-block "DoDebugPortStop"
[14:53:48.510]    </control>
[14:53:48.510]  </sequence>
[14:53:48.510]  
[14:55:01.812]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:55:01.812]  
[14:55:01.812]  <debugvars>
[14:55:01.812]    // Pre-defined
[14:55:01.813]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:55:01.813]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:55:01.813]    __dp=0x00000000
[14:55:01.813]    __ap=0x00000000
[14:55:01.814]    __traceout=0x00000000      (Trace Disabled)
[14:55:01.814]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:55:01.815]    __FlashAddr=0x00000000
[14:55:01.815]    __FlashLen=0x00000000
[14:55:01.815]    __FlashArg=0x00000000
[14:55:01.815]    __FlashOp=0x00000000
[14:55:01.816]    __Result=0x00000000
[14:55:01.816]    
[14:55:01.816]    // User-defined
[14:55:01.816]    DbgMCU_CR=0x00000007
[14:55:01.816]    DbgMCU_APB1_Fz=0x00000000
[14:55:01.817]    DbgMCU_APB2_Fz=0x00000000
[14:55:01.817]    DoOptionByteLoading=0x00000000
[14:55:01.817]  </debugvars>
[14:55:01.817]  
[14:55:01.817]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:55:01.818]    <block atomic="false" info="">
[14:55:01.818]      Sequence("CheckID");
[14:55:01.818]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:55:01.818]          <block atomic="false" info="">
[14:55:01.819]            __var pidr1 = 0;
[14:55:01.819]              // -> [pidr1 <= 0x00000000]
[14:55:01.819]            __var pidr2 = 0;
[14:55:01.819]              // -> [pidr2 <= 0x00000000]
[14:55:01.819]            __var jep106id = 0;
[14:55:01.819]              // -> [jep106id <= 0x00000000]
[14:55:01.820]            __var ROMTableBase = 0;
[14:55:01.820]              // -> [ROMTableBase <= 0x00000000]
[14:55:01.820]            __ap = 0;      // AHB-AP
[14:55:01.820]              // -> [__ap <= 0x00000000]
[14:55:01.821]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:55:01.821]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:55:01.821]              // -> [ROMTableBase <= 0xF0000000]
[14:55:01.822]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:55:01.823]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:55:01.823]              // -> [pidr1 <= 0x00000004]
[14:55:01.823]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:55:01.824]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:55:01.824]              // -> [pidr2 <= 0x0000000A]
[14:55:01.824]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:55:01.824]              // -> [jep106id <= 0x00000020]
[14:55:01.824]          </block>
[14:55:01.825]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:55:01.825]            // if-block "jep106id != 0x20"
[14:55:01.825]              // =>  FALSE
[14:55:01.825]            // skip if-block "jep106id != 0x20"
[14:55:01.825]          </control>
[14:55:01.825]        </sequence>
[14:55:01.825]    </block>
[14:55:01.825]  </sequence>
[14:55:01.827]  
[14:55:01.839]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:55:01.839]  
[14:55:01.866]  <debugvars>
[14:55:01.866]    // Pre-defined
[14:55:01.867]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:55:01.867]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:55:01.867]    __dp=0x00000000
[14:55:01.868]    __ap=0x00000000
[14:55:01.868]    __traceout=0x00000000      (Trace Disabled)
[14:55:01.868]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:55:01.868]    __FlashAddr=0x00000000
[14:55:01.868]    __FlashLen=0x00000000
[14:55:01.868]    __FlashArg=0x00000000
[14:55:01.869]    __FlashOp=0x00000000
[14:55:01.869]    __Result=0x00000000
[14:55:01.869]    
[14:55:01.869]    // User-defined
[14:55:01.870]    DbgMCU_CR=0x00000007
[14:55:01.870]    DbgMCU_APB1_Fz=0x00000000
[14:55:01.870]    DbgMCU_APB2_Fz=0x00000000
[14:55:01.870]    DoOptionByteLoading=0x00000000
[14:55:01.871]  </debugvars>
[14:55:01.871]  
[14:55:01.871]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:55:01.871]    <block atomic="false" info="">
[14:55:01.871]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:55:01.872]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:55:01.872]    </block>
[14:55:01.872]    <block atomic="false" info="DbgMCU registers">
[14:55:01.873]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:55:01.874]        // -> [Read32(0x40021034) => 0x00000201]   (__dp=0x00000000, __ap=0x00000000)
[14:55:01.874]        // -> [Write32(0x40021034, 0x00400201)]   (__dp=0x00000000, __ap=0x00000000)
[14:55:01.875]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:55:01.875]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:55:01.876]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:55:01.877]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:55:01.877]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:55:01.878]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:55:01.878]    </block>
[14:55:01.878]  </sequence>
[14:55:01.878]  
[14:55:08.986]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:55:08.986]  
[14:55:08.987]  <debugvars>
[14:55:08.987]    // Pre-defined
[14:55:08.987]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:55:08.988]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:55:08.988]    __dp=0x00000000
[14:55:08.989]    __ap=0x00000000
[14:55:08.989]    __traceout=0x00000000      (Trace Disabled)
[14:55:08.990]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:55:08.990]    __FlashAddr=0x00000000
[14:55:08.990]    __FlashLen=0x00000000
[14:55:08.991]    __FlashArg=0x00000000
[14:55:08.991]    __FlashOp=0x00000000
[14:55:08.992]    __Result=0x00000000
[14:55:08.992]    
[14:55:08.992]    // User-defined
[14:55:08.992]    DbgMCU_CR=0x00000007
[14:55:08.992]    DbgMCU_APB1_Fz=0x00000000
[14:55:08.993]    DbgMCU_APB2_Fz=0x00000000
[14:55:08.993]    DoOptionByteLoading=0x00000000
[14:55:08.994]  </debugvars>
[14:55:08.994]  
[14:55:08.994]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:55:08.995]    <block atomic="false" info="">
[14:55:08.995]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:55:08.995]        // -> [connectionFlash <= 0x00000001]
[14:55:08.995]      __var FLASH_BASE = 0x40022000 ;
[14:55:08.996]        // -> [FLASH_BASE <= 0x40022000]
[14:55:08.996]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:55:08.996]        // -> [FLASH_CR <= 0x40022004]
[14:55:08.996]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:55:08.996]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:55:08.997]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:55:08.997]        // -> [LOCK_BIT <= 0x00000001]
[14:55:08.997]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:55:08.997]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:55:08.997]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:55:08.997]        // -> [FLASH_KEYR <= 0x4002200C]
[14:55:08.998]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:55:08.999]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:55:08.999]      __var FLASH_KEY2 = 0x02030405 ;
[14:55:08.999]        // -> [FLASH_KEY2 <= 0x02030405]
[14:55:09.000]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:55:09.000]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:55:09.000]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:55:09.001]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:55:09.001]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:55:09.001]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:55:09.001]      __var FLASH_CR_Value = 0 ;
[14:55:09.001]        // -> [FLASH_CR_Value <= 0x00000000]
[14:55:09.001]      __var DoDebugPortStop = 1 ;
[14:55:09.002]        // -> [DoDebugPortStop <= 0x00000001]
[14:55:09.002]      __var DP_CTRL_STAT = 0x4 ;
[14:55:09.002]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:55:09.002]      __var DP_SELECT = 0x8 ;
[14:55:09.002]        // -> [DP_SELECT <= 0x00000008]
[14:55:09.002]    </block>
[14:55:09.003]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:55:09.003]      // if-block "connectionFlash && DoOptionByteLoading"
[14:55:09.003]        // =>  FALSE
[14:55:09.003]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:55:09.003]    </control>
[14:55:09.004]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:55:09.004]      // if-block "DoDebugPortStop"
[14:55:09.004]        // =>  TRUE
[14:55:09.004]      <block atomic="false" info="">
[14:55:09.004]        WriteDP(DP_SELECT, 0x00000000);
[14:55:09.005]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:55:09.005]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:55:09.006]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:55:09.006]      </block>
[14:55:09.006]      // end if-block "DoDebugPortStop"
[14:55:09.006]    </control>
[14:55:09.006]  </sequence>
[14:55:09.006]  
[15:00:36.036]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:00:36.036]  
[15:00:36.037]  <debugvars>
[15:00:36.037]    // Pre-defined
[15:00:36.037]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:00:36.038]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:00:36.038]    __dp=0x00000000
[15:00:36.038]    __ap=0x00000000
[15:00:36.039]    __traceout=0x00000000      (Trace Disabled)
[15:00:36.039]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:00:36.039]    __FlashAddr=0x00000000
[15:00:36.039]    __FlashLen=0x00000000
[15:00:36.039]    __FlashArg=0x00000000
[15:00:36.040]    __FlashOp=0x00000000
[15:00:36.040]    __Result=0x00000000
[15:00:36.040]    
[15:00:36.040]    // User-defined
[15:00:36.040]    DbgMCU_CR=0x00000007
[15:00:36.041]    DbgMCU_APB1_Fz=0x00000000
[15:00:36.041]    DbgMCU_APB2_Fz=0x00000000
[15:00:36.041]    DoOptionByteLoading=0x00000000
[15:00:36.041]  </debugvars>
[15:00:36.042]  
[15:00:36.042]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:00:36.042]    <block atomic="false" info="">
[15:00:36.042]      Sequence("CheckID");
[15:00:36.042]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:00:36.043]          <block atomic="false" info="">
[15:00:36.043]            __var pidr1 = 0;
[15:00:36.043]              // -> [pidr1 <= 0x00000000]
[15:00:36.043]            __var pidr2 = 0;
[15:00:36.043]              // -> [pidr2 <= 0x00000000]
[15:00:36.044]            __var jep106id = 0;
[15:00:36.044]              // -> [jep106id <= 0x00000000]
[15:00:36.044]            __var ROMTableBase = 0;
[15:00:36.044]              // -> [ROMTableBase <= 0x00000000]
[15:00:36.044]            __ap = 0;      // AHB-AP
[15:00:36.045]              // -> [__ap <= 0x00000000]
[15:00:36.045]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:00:36.045]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:00:36.046]              // -> [ROMTableBase <= 0xF0000000]
[15:00:36.046]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:00:36.047]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:00:36.047]              // -> [pidr1 <= 0x00000004]
[15:00:36.047]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:00:36.048]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:00:36.048]              // -> [pidr2 <= 0x0000000A]
[15:00:36.049]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:00:36.049]              // -> [jep106id <= 0x00000020]
[15:00:36.049]          </block>
[15:00:36.049]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:00:36.049]            // if-block "jep106id != 0x20"
[15:00:36.049]              // =>  FALSE
[15:00:36.051]            // skip if-block "jep106id != 0x20"
[15:00:36.051]          </control>
[15:00:36.051]        </sequence>
[15:00:36.051]    </block>
[15:00:36.051]  </sequence>
[15:00:36.051]  
[15:00:36.065]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:00:36.065]  
[15:00:36.065]  <debugvars>
[15:00:36.065]    // Pre-defined
[15:00:36.065]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:00:36.066]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:00:36.066]    __dp=0x00000000
[15:00:36.066]    __ap=0x00000000
[15:00:36.066]    __traceout=0x00000000      (Trace Disabled)
[15:00:36.067]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:00:36.067]    __FlashAddr=0x00000000
[15:00:36.067]    __FlashLen=0x00000000
[15:00:36.067]    __FlashArg=0x00000000
[15:00:36.067]    __FlashOp=0x00000000
[15:00:36.067]    __Result=0x00000000
[15:00:36.069]    
[15:00:36.069]    // User-defined
[15:00:36.069]    DbgMCU_CR=0x00000007
[15:00:36.069]    DbgMCU_APB1_Fz=0x00000000
[15:00:36.069]    DbgMCU_APB2_Fz=0x00000000
[15:00:36.070]    DoOptionByteLoading=0x00000000
[15:00:36.070]  </debugvars>
[15:00:36.070]  
[15:00:36.071]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:00:36.071]    <block atomic="false" info="">
[15:00:36.071]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:00:36.072]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:00:36.072]    </block>
[15:00:36.072]    <block atomic="false" info="DbgMCU registers">
[15:00:36.072]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:00:36.074]        // -> [Read32(0x40021034) => 0x00000201]   (__dp=0x00000000, __ap=0x00000000)
[15:00:36.075]        // -> [Write32(0x40021034, 0x00400201)]   (__dp=0x00000000, __ap=0x00000000)
[15:00:36.075]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:00:36.077]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:00:36.077]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:00:36.079]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:00:36.079]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:00:36.080]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:00:36.080]    </block>
[15:00:36.080]  </sequence>
[15:00:36.080]  
[15:00:43.151]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:00:43.151]  
[15:00:43.152]  <debugvars>
[15:00:43.152]    // Pre-defined
[15:00:43.152]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:00:43.153]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:00:43.153]    __dp=0x00000000
[15:00:43.153]    __ap=0x00000000
[15:00:43.154]    __traceout=0x00000000      (Trace Disabled)
[15:00:43.155]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:00:43.155]    __FlashAddr=0x00000000
[15:00:43.155]    __FlashLen=0x00000000
[15:00:43.156]    __FlashArg=0x00000000
[15:00:43.156]    __FlashOp=0x00000000
[15:00:43.156]    __Result=0x00000000
[15:00:43.156]    
[15:00:43.156]    // User-defined
[15:00:43.157]    DbgMCU_CR=0x00000007
[15:00:43.157]    DbgMCU_APB1_Fz=0x00000000
[15:00:43.157]    DbgMCU_APB2_Fz=0x00000000
[15:00:43.157]    DoOptionByteLoading=0x00000000
[15:00:43.157]  </debugvars>
[15:00:43.158]  
[15:00:43.158]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:00:43.158]    <block atomic="false" info="">
[15:00:43.159]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:00:43.159]        // -> [connectionFlash <= 0x00000001]
[15:00:43.159]      __var FLASH_BASE = 0x40022000 ;
[15:00:43.159]        // -> [FLASH_BASE <= 0x40022000]
[15:00:43.173]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:00:43.173]        // -> [FLASH_CR <= 0x40022004]
[15:00:43.174]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:00:43.175]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:00:43.175]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:00:43.176]        // -> [LOCK_BIT <= 0x00000001]
[15:00:43.177]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:00:43.177]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:00:43.178]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:00:43.178]        // -> [FLASH_KEYR <= 0x4002200C]
[15:00:43.178]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:00:43.179]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:00:43.179]      __var FLASH_KEY2 = 0x02030405 ;
[15:00:43.179]        // -> [FLASH_KEY2 <= 0x02030405]
[15:00:43.180]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:00:43.180]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:00:43.180]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:00:43.180]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:00:43.181]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:00:43.181]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:00:43.182]      __var FLASH_CR_Value = 0 ;
[15:00:43.182]        // -> [FLASH_CR_Value <= 0x00000000]
[15:00:43.182]      __var DoDebugPortStop = 1 ;
[15:00:43.183]        // -> [DoDebugPortStop <= 0x00000001]
[15:00:43.183]      __var DP_CTRL_STAT = 0x4 ;
[15:00:43.183]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:00:43.184]      __var DP_SELECT = 0x8 ;
[15:00:43.184]        // -> [DP_SELECT <= 0x00000008]
[15:00:43.184]    </block>
[15:00:43.184]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:00:43.184]      // if-block "connectionFlash && DoOptionByteLoading"
[15:00:43.184]        // =>  FALSE
[15:00:43.185]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:00:43.185]    </control>
[15:00:43.185]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:00:43.185]      // if-block "DoDebugPortStop"
[15:00:43.186]        // =>  TRUE
[15:00:43.186]      <block atomic="false" info="">
[15:00:43.186]        WriteDP(DP_SELECT, 0x00000000);
[15:00:43.186]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:00:43.187]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:00:43.187]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:00:43.188]      </block>
[15:00:43.188]      // end if-block "DoDebugPortStop"
[15:00:43.188]    </control>
[15:00:43.188]  </sequence>
[15:00:43.189]  
[15:05:57.282]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:05:57.282]  
[15:05:57.283]  <debugvars>
[15:05:57.283]    // Pre-defined
[15:05:57.283]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:05:57.284]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:05:57.284]    __dp=0x00000000
[15:05:57.284]    __ap=0x00000000
[15:05:57.284]    __traceout=0x00000000      (Trace Disabled)
[15:05:57.284]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:05:57.284]    __FlashAddr=0x00000000
[15:05:57.285]    __FlashLen=0x00000000
[15:05:57.285]    __FlashArg=0x00000000
[15:05:57.285]    __FlashOp=0x00000000
[15:05:57.286]    __Result=0x00000000
[15:05:57.286]    
[15:05:57.286]    // User-defined
[15:05:57.286]    DbgMCU_CR=0x00000007
[15:05:57.286]    DbgMCU_APB1_Fz=0x00000000
[15:05:57.287]    DbgMCU_APB2_Fz=0x00000000
[15:05:57.287]    DoOptionByteLoading=0x00000000
[15:05:57.287]  </debugvars>
[15:05:57.287]  
[15:05:57.287]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:05:57.288]    <block atomic="false" info="">
[15:05:57.288]      Sequence("CheckID");
[15:05:57.288]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:05:57.288]          <block atomic="false" info="">
[15:05:57.288]            __var pidr1 = 0;
[15:05:57.288]              // -> [pidr1 <= 0x00000000]
[15:05:57.288]            __var pidr2 = 0;
[15:05:57.289]              // -> [pidr2 <= 0x00000000]
[15:05:57.289]            __var jep106id = 0;
[15:05:57.289]              // -> [jep106id <= 0x00000000]
[15:05:57.290]            __var ROMTableBase = 0;
[15:05:57.290]              // -> [ROMTableBase <= 0x00000000]
[15:05:57.290]            __ap = 0;      // AHB-AP
[15:05:57.290]              // -> [__ap <= 0x00000000]
[15:05:57.290]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:05:57.291]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:05:57.291]              // -> [ROMTableBase <= 0xF0000000]
[15:05:57.291]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:05:57.292]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:05:57.292]              // -> [pidr1 <= 0x00000004]
[15:05:57.293]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:05:57.294]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:05:57.294]              // -> [pidr2 <= 0x0000000A]
[15:05:57.294]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:05:57.295]              // -> [jep106id <= 0x00000020]
[15:05:57.295]          </block>
[15:05:57.295]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:05:57.295]            // if-block "jep106id != 0x20"
[15:05:57.295]              // =>  FALSE
[15:05:57.295]            // skip if-block "jep106id != 0x20"
[15:05:57.296]          </control>
[15:05:57.296]        </sequence>
[15:05:57.296]    </block>
[15:05:57.296]  </sequence>
[15:05:57.296]  
[15:05:57.310]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:05:57.310]  
[15:05:57.310]  <debugvars>
[15:05:57.310]    // Pre-defined
[15:05:57.310]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:05:57.311]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:05:57.311]    __dp=0x00000000
[15:05:57.311]    __ap=0x00000000
[15:05:57.311]    __traceout=0x00000000      (Trace Disabled)
[15:05:57.311]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:05:57.312]    __FlashAddr=0x00000000
[15:05:57.312]    __FlashLen=0x00000000
[15:05:57.312]    __FlashArg=0x00000000
[15:05:57.312]    __FlashOp=0x00000000
[15:05:57.312]    __Result=0x00000000
[15:05:57.313]    
[15:05:57.313]    // User-defined
[15:05:57.313]    DbgMCU_CR=0x00000007
[15:05:57.313]    DbgMCU_APB1_Fz=0x00000000
[15:05:57.313]    DbgMCU_APB2_Fz=0x00000000
[15:05:57.313]    DoOptionByteLoading=0x00000000
[15:05:57.314]  </debugvars>
[15:05:57.314]  
[15:05:57.314]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:05:57.314]    <block atomic="false" info="">
[15:05:57.315]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:05:57.315]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:05:57.316]    </block>
[15:05:57.316]    <block atomic="false" info="DbgMCU registers">
[15:05:57.316]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:05:57.317]        // -> [Read32(0x40021034) => 0x00000201]   (__dp=0x00000000, __ap=0x00000000)
[15:05:57.318]        // -> [Write32(0x40021034, 0x00400201)]   (__dp=0x00000000, __ap=0x00000000)
[15:05:57.318]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:05:57.319]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:05:57.319]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:05:57.320]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:05:57.320]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:05:57.322]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:05:57.322]    </block>
[15:05:57.322]  </sequence>
[15:05:57.322]  
[15:06:04.521]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:06:04.521]  
[15:06:04.522]  <debugvars>
[15:06:04.523]    // Pre-defined
[15:06:04.524]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:06:04.524]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:06:04.525]    __dp=0x00000000
[15:06:04.526]    __ap=0x00000000
[15:06:04.527]    __traceout=0x00000000      (Trace Disabled)
[15:06:04.527]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:06:04.528]    __FlashAddr=0x00000000
[15:06:04.528]    __FlashLen=0x00000000
[15:06:04.529]    __FlashArg=0x00000000
[15:06:04.529]    __FlashOp=0x00000000
[15:06:04.530]    __Result=0x00000000
[15:06:04.531]    
[15:06:04.531]    // User-defined
[15:06:04.531]    DbgMCU_CR=0x00000007
[15:06:04.532]    DbgMCU_APB1_Fz=0x00000000
[15:06:04.532]    DbgMCU_APB2_Fz=0x00000000
[15:06:04.533]    DoOptionByteLoading=0x00000000
[15:06:04.533]  </debugvars>
[15:06:04.534]  
[15:06:04.534]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:06:04.535]    <block atomic="false" info="">
[15:06:04.535]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:06:04.536]        // -> [connectionFlash <= 0x00000001]
[15:06:04.536]      __var FLASH_BASE = 0x40022000 ;
[15:06:04.537]        // -> [FLASH_BASE <= 0x40022000]
[15:06:04.537]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:06:04.537]        // -> [FLASH_CR <= 0x40022004]
[15:06:04.538]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:06:04.538]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:06:04.539]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:06:04.539]        // -> [LOCK_BIT <= 0x00000001]
[15:06:04.540]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:06:04.540]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:06:04.541]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:06:04.541]        // -> [FLASH_KEYR <= 0x4002200C]
[15:06:04.542]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:06:04.542]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:06:04.543]      __var FLASH_KEY2 = 0x02030405 ;
[15:06:04.543]        // -> [FLASH_KEY2 <= 0x02030405]
[15:06:04.544]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:06:04.544]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:06:04.544]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:06:04.545]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:06:04.545]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:06:04.546]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:06:04.546]      __var FLASH_CR_Value = 0 ;
[15:06:04.547]        // -> [FLASH_CR_Value <= 0x00000000]
[15:06:04.547]      __var DoDebugPortStop = 1 ;
[15:06:04.548]        // -> [DoDebugPortStop <= 0x00000001]
[15:06:04.548]      __var DP_CTRL_STAT = 0x4 ;
[15:06:04.548]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:06:04.548]      __var DP_SELECT = 0x8 ;
[15:06:04.549]        // -> [DP_SELECT <= 0x00000008]
[15:06:04.549]    </block>
[15:06:04.549]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:06:04.550]      // if-block "connectionFlash && DoOptionByteLoading"
[15:06:04.550]        // =>  FALSE
[15:06:04.550]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:06:04.550]    </control>
[15:06:04.551]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:06:04.551]      // if-block "DoDebugPortStop"
[15:06:04.551]        // =>  TRUE
[15:06:04.551]      <block atomic="false" info="">
[15:06:04.551]        WriteDP(DP_SELECT, 0x00000000);
[15:06:04.552]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:06:04.553]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:06:04.553]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:06:04.553]      </block>
[15:06:04.554]      // end if-block "DoDebugPortStop"
[15:06:04.554]    </control>
[15:06:04.554]  </sequence>
[15:06:04.555]  
[16:46:26.120]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:46:26.120]  
[16:46:26.135]  <debugvars>
[16:46:26.135]    // Pre-defined
[16:46:26.136]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:46:26.137]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:46:26.137]    __dp=0x00000000
[16:46:26.137]    __ap=0x00000000
[16:46:26.138]    __traceout=0x00000000      (Trace Disabled)
[16:46:26.138]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:46:26.139]    __FlashAddr=0x00000000
[16:46:26.139]    __FlashLen=0x00000000
[16:46:26.140]    __FlashArg=0x00000000
[16:46:26.140]    __FlashOp=0x00000000
[16:46:26.140]    __Result=0x00000000
[16:46:26.141]    
[16:46:26.141]    // User-defined
[16:46:26.141]    DbgMCU_CR=0x00000007
[16:46:26.142]    DbgMCU_APB1_Fz=0x00000000
[16:46:26.142]    DbgMCU_APB2_Fz=0x00000000
[16:46:26.142]    DoOptionByteLoading=0x00000000
[16:46:26.143]  </debugvars>
[16:46:26.143]  
[16:46:26.143]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:46:26.143]    <block atomic="false" info="">
[16:46:26.143]      Sequence("CheckID");
[16:46:26.144]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:46:26.144]          <block atomic="false" info="">
[16:46:26.144]            __var pidr1 = 0;
[16:46:26.144]              // -> [pidr1 <= 0x00000000]
[16:46:26.144]            __var pidr2 = 0;
[16:46:26.145]              // -> [pidr2 <= 0x00000000]
[16:46:26.146]            __var jep106id = 0;
[16:46:26.146]              // -> [jep106id <= 0x00000000]
[16:46:26.146]            __var ROMTableBase = 0;
[16:46:26.146]              // -> [ROMTableBase <= 0x00000000]
[16:46:26.146]            __ap = 0;      // AHB-AP
[16:46:26.146]              // -> [__ap <= 0x00000000]
[16:46:26.146]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:46:26.148]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:46:26.148]              // -> [ROMTableBase <= 0xF0000000]
[16:46:26.148]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:46:26.149]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:46:26.150]              // -> [pidr1 <= 0x00000004]
[16:46:26.150]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:46:26.151]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:46:26.151]              // -> [pidr2 <= 0x0000000A]
[16:46:26.152]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:46:26.152]              // -> [jep106id <= 0x00000020]
[16:46:26.152]          </block>
[16:46:26.152]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:46:26.152]            // if-block "jep106id != 0x20"
[16:46:26.153]              // =>  FALSE
[16:46:26.153]            // skip if-block "jep106id != 0x20"
[16:46:26.153]          </control>
[16:46:26.153]        </sequence>
[16:46:26.153]    </block>
[16:46:26.153]  </sequence>
[16:46:26.153]  
[16:46:26.166]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:46:26.166]  
[16:46:26.178]  <debugvars>
[16:46:26.178]    // Pre-defined
[16:46:26.179]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:46:26.180]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:46:26.180]    __dp=0x00000000
[16:46:26.180]    __ap=0x00000000
[16:46:26.182]    __traceout=0x00000000      (Trace Disabled)
[16:46:26.183]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:46:26.183]    __FlashAddr=0x00000000
[16:46:26.184]    __FlashLen=0x00000000
[16:46:26.185]    __FlashArg=0x00000000
[16:46:26.185]    __FlashOp=0x00000000
[16:46:26.186]    __Result=0x00000000
[16:46:26.187]    
[16:46:26.187]    // User-defined
[16:46:26.188]    DbgMCU_CR=0x00000007
[16:46:26.188]    DbgMCU_APB1_Fz=0x00000000
[16:46:26.189]    DbgMCU_APB2_Fz=0x00000000
[16:46:26.190]    DoOptionByteLoading=0x00000000
[16:46:26.190]  </debugvars>
[16:46:26.191]  
[16:46:26.191]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:46:26.192]    <block atomic="false" info="">
[16:46:26.193]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:46:26.195]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:46:26.196]    </block>
[16:46:26.196]    <block atomic="false" info="DbgMCU registers">
[16:46:26.197]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:46:26.200]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:46:26.201]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:46:26.202]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:46:26.203]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:46:26.204]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:46:26.205]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:46:26.205]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:46:26.207]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:46:26.207]    </block>
[16:46:26.207]  </sequence>
[16:46:26.207]  
[16:46:33.576]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:46:33.576]  
[16:46:33.576]  <debugvars>
[16:46:33.577]    // Pre-defined
[16:46:33.577]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:46:33.577]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:46:33.578]    __dp=0x00000000
[16:46:33.579]    __ap=0x00000000
[16:46:33.580]    __traceout=0x00000000      (Trace Disabled)
[16:46:33.580]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:46:33.580]    __FlashAddr=0x00000000
[16:46:33.580]    __FlashLen=0x00000000
[16:46:33.581]    __FlashArg=0x00000000
[16:46:33.581]    __FlashOp=0x00000000
[16:46:33.582]    __Result=0x00000000
[16:46:33.582]    
[16:46:33.582]    // User-defined
[16:46:33.583]    DbgMCU_CR=0x00000007
[16:46:33.584]    DbgMCU_APB1_Fz=0x00000000
[16:46:33.584]    DbgMCU_APB2_Fz=0x00000000
[16:46:33.585]    DoOptionByteLoading=0x00000000
[16:46:33.585]  </debugvars>
[16:46:33.586]  
[16:46:33.586]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:46:33.587]    <block atomic="false" info="">
[16:46:33.587]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:46:33.587]        // -> [connectionFlash <= 0x00000001]
[16:46:33.588]      __var FLASH_BASE = 0x40022000 ;
[16:46:33.588]        // -> [FLASH_BASE <= 0x40022000]
[16:46:33.588]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:46:33.589]        // -> [FLASH_CR <= 0x40022004]
[16:46:33.589]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:46:33.589]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:46:33.590]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:46:33.590]        // -> [LOCK_BIT <= 0x00000001]
[16:46:33.590]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:46:33.591]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:46:33.591]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:46:33.591]        // -> [FLASH_KEYR <= 0x4002200C]
[16:46:33.591]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:46:33.592]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:46:33.592]      __var FLASH_KEY2 = 0x02030405 ;
[16:46:33.592]        // -> [FLASH_KEY2 <= 0x02030405]
[16:46:33.592]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:46:33.593]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:46:33.593]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:46:33.593]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:46:33.593]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:46:33.593]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:46:33.594]      __var FLASH_CR_Value = 0 ;
[16:46:33.594]        // -> [FLASH_CR_Value <= 0x00000000]
[16:46:33.594]      __var DoDebugPortStop = 1 ;
[16:46:33.594]        // -> [DoDebugPortStop <= 0x00000001]
[16:46:33.594]      __var DP_CTRL_STAT = 0x4 ;
[16:46:33.594]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:46:33.595]      __var DP_SELECT = 0x8 ;
[16:46:33.595]        // -> [DP_SELECT <= 0x00000008]
[16:46:33.595]    </block>
[16:46:33.595]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:46:33.595]      // if-block "connectionFlash && DoOptionByteLoading"
[16:46:33.596]        // =>  FALSE
[16:46:33.596]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:46:33.596]    </control>
[16:46:33.596]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:46:33.596]      // if-block "DoDebugPortStop"
[16:46:33.596]        // =>  TRUE
[16:46:33.596]      <block atomic="false" info="">
[16:46:33.596]        WriteDP(DP_SELECT, 0x00000000);
[16:46:33.598]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:46:33.598]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:46:33.598]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:46:33.598]      </block>
[16:46:33.598]      // end if-block "DoDebugPortStop"
[16:46:33.600]    </control>
[16:46:33.600]  </sequence>
[16:46:33.600]  
