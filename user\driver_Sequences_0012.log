/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : D:\2025work\STM32L072PRO\source-application\user\driver_Sequences_0012.log
 *  Created     : 20:07:01 (14/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : D:\2025work\STM32L072PRO\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[20:07:01.467]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[20:07:01.467]  
[20:07:01.488]  <debugvars>
[20:07:01.508]    // Pre-defined
[20:07:01.530]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:07:01.550]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:07:01.550]    __dp=0x00000000
[20:07:01.550]    __ap=0x00000000
[20:07:01.550]    __traceout=0x00000000      (Trace Disabled)
[20:07:01.550]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:07:01.550]    __FlashAddr=0x00000000
[20:07:01.552]    __FlashLen=0x00000000
[20:07:01.552]    __FlashArg=0x00000000
[20:07:01.552]    __FlashOp=0x00000000
[20:07:01.552]    __Result=0x00000000
[20:07:01.552]    
[20:07:01.552]    // User-defined
[20:07:01.553]    DbgMCU_CR=0x00000007
[20:07:01.553]    DbgMCU_APB1_Fz=0x00000000
[20:07:01.553]    DbgMCU_APB2_Fz=0x00000000
[20:07:01.553]    DoOptionByteLoading=0x00000000
[20:07:01.553]  </debugvars>
[20:07:01.554]  
[20:07:01.554]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[20:07:01.554]    <block atomic="false" info="">
[20:07:01.554]      Sequence("CheckID");
[20:07:01.554]        <sequence name="CheckID" Pname="" disable="false" info="">
[20:07:01.555]          <block atomic="false" info="">
[20:07:01.555]            __var pidr1 = 0;
[20:07:01.555]              // -> [pidr1 <= 0x00000000]
[20:07:01.555]            __var pidr2 = 0;
[20:07:01.555]              // -> [pidr2 <= 0x00000000]
[20:07:01.556]            __var jep106id = 0;
[20:07:01.556]              // -> [jep106id <= 0x00000000]
[20:07:01.556]            __var ROMTableBase = 0;
[20:07:01.556]              // -> [ROMTableBase <= 0x00000000]
[20:07:01.556]            __ap = 0;      // AHB-AP
[20:07:01.557]              // -> [__ap <= 0x00000000]
[20:07:01.557]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[20:07:01.558]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[20:07:01.558]              // -> [ROMTableBase <= 0xF0000000]
[20:07:01.558]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[20:07:01.560]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[20:07:01.560]              // -> [pidr1 <= 0x00000004]
[20:07:01.561]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[20:07:01.562]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[20:07:01.563]              // -> [pidr2 <= 0x0000000A]
[20:07:01.563]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[20:07:01.563]              // -> [jep106id <= 0x00000020]
[20:07:01.563]          </block>
[20:07:01.563]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[20:07:01.564]            // if-block "jep106id != 0x20"
[20:07:01.564]              // =>  FALSE
[20:07:01.564]            // skip if-block "jep106id != 0x20"
[20:07:01.564]          </control>
[20:07:01.564]        </sequence>
[20:07:01.565]    </block>
[20:07:01.565]  </sequence>
[20:07:01.565]  
[20:07:01.584]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[20:07:01.584]  
[20:07:01.608]  <debugvars>
[20:07:01.608]    // Pre-defined
[20:07:01.608]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:07:01.608]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:07:01.609]    __dp=0x00000000
[20:07:01.609]    __ap=0x00000000
[20:07:01.609]    __traceout=0x00000000      (Trace Disabled)
[20:07:01.609]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:07:01.609]    __FlashAddr=0x00000000
[20:07:01.610]    __FlashLen=0x00000000
[20:07:01.610]    __FlashArg=0x00000000
[20:07:01.610]    __FlashOp=0x00000000
[20:07:01.610]    __Result=0x00000000
[20:07:01.611]    
[20:07:01.611]    // User-defined
[20:07:01.611]    DbgMCU_CR=0x00000007
[20:07:01.611]    DbgMCU_APB1_Fz=0x00000000
[20:07:01.611]    DbgMCU_APB2_Fz=0x00000000
[20:07:01.611]    DoOptionByteLoading=0x00000000
[20:07:01.612]  </debugvars>
[20:07:01.612]  
[20:07:01.612]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[20:07:01.612]    <block atomic="false" info="">
[20:07:01.612]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[20:07:01.613]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[20:07:01.614]    </block>
[20:07:01.614]    <block atomic="false" info="DbgMCU registers">
[20:07:01.614]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[20:07:01.615]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[20:07:01.616]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[20:07:01.617]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[20:07:01.619]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[20:07:01.619]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[20:07:01.620]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[20:07:01.620]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[20:07:01.621]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[20:07:01.621]    </block>
[20:07:01.622]  </sequence>
[20:07:01.622]  
[20:07:11.908]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[20:07:11.908]  
[20:07:11.909]  <debugvars>
[20:07:11.909]    // Pre-defined
[20:07:11.910]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:07:11.910]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:07:11.910]    __dp=0x00000000
[20:07:11.911]    __ap=0x00000000
[20:07:11.911]    __traceout=0x00000000      (Trace Disabled)
[20:07:11.911]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:07:11.911]    __FlashAddr=0x00000000
[20:07:11.912]    __FlashLen=0x00000000
[20:07:11.912]    __FlashArg=0x00000000
[20:07:11.912]    __FlashOp=0x00000000
[20:07:11.913]    __Result=0x00000000
[20:07:11.913]    
[20:07:11.913]    // User-defined
[20:07:11.913]    DbgMCU_CR=0x00000007
[20:07:11.914]    DbgMCU_APB1_Fz=0x00000000
[20:07:11.914]    DbgMCU_APB2_Fz=0x00000000
[20:07:11.914]    DoOptionByteLoading=0x00000000
[20:07:11.914]  </debugvars>
[20:07:11.915]  
[20:07:11.915]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[20:07:11.915]    <block atomic="false" info="">
[20:07:11.915]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[20:07:11.915]        // -> [connectionFlash <= 0x00000001]
[20:07:11.916]      __var FLASH_BASE = 0x40022000 ;
[20:07:11.916]        // -> [FLASH_BASE <= 0x40022000]
[20:07:11.916]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[20:07:11.916]        // -> [FLASH_CR <= 0x40022004]
[20:07:11.916]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[20:07:11.916]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[20:07:11.917]      __var LOCK_BIT = ( 1 << 0 ) ;
[20:07:11.917]        // -> [LOCK_BIT <= 0x00000001]
[20:07:11.917]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[20:07:11.917]        // -> [OPTLOCK_BIT <= 0x00000004]
[20:07:11.917]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[20:07:11.918]        // -> [FLASH_KEYR <= 0x4002200C]
[20:07:11.918]      __var FLASH_KEY1 = 0x89ABCDEF ;
[20:07:11.918]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[20:07:11.918]      __var FLASH_KEY2 = 0x02030405 ;
[20:07:11.919]        // -> [FLASH_KEY2 <= 0x02030405]
[20:07:11.919]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[20:07:11.920]        // -> [FLASH_OPTKEYR <= 0x40022014]
[20:07:11.920]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[20:07:11.920]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[20:07:11.921]      __var FLASH_OPTKEY2 = 0x24252627 ;
[20:07:11.921]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[20:07:11.921]      __var FLASH_CR_Value = 0 ;
[20:07:11.923]        // -> [FLASH_CR_Value <= 0x00000000]
[20:07:11.923]      __var DoDebugPortStop = 1 ;
[20:07:11.923]        // -> [DoDebugPortStop <= 0x00000001]
[20:07:11.924]      __var DP_CTRL_STAT = 0x4 ;
[20:07:11.924]        // -> [DP_CTRL_STAT <= 0x00000004]
[20:07:11.924]      __var DP_SELECT = 0x8 ;
[20:07:11.925]        // -> [DP_SELECT <= 0x00000008]
[20:07:11.925]    </block>
[20:07:11.925]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[20:07:11.925]      // if-block "connectionFlash && DoOptionByteLoading"
[20:07:11.926]        // =>  FALSE
[20:07:11.926]      // skip if-block "connectionFlash && DoOptionByteLoading"
[20:07:11.926]    </control>
[20:07:11.927]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[20:07:11.927]      // if-block "DoDebugPortStop"
[20:07:11.927]        // =>  TRUE
[20:07:11.927]      <block atomic="false" info="">
[20:07:11.928]        WriteDP(DP_SELECT, 0x00000000);
[20:07:11.928]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[20:07:11.929]        WriteDP(DP_CTRL_STAT, 0x00000000);
[20:07:11.929]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[20:07:11.929]      </block>
[20:07:11.931]      // end if-block "DoDebugPortStop"
[20:07:11.931]    </control>
[20:07:11.931]  </sequence>
[20:07:11.932]  
[20:07:12.310]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[20:07:12.310]  
[20:07:12.311]  <debugvars>
[20:07:12.311]    // Pre-defined
[20:07:12.311]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:07:12.312]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[20:07:12.312]    __dp=0x00000000
[20:07:12.312]    __ap=0x00000000
[20:07:12.313]    __traceout=0x00000000      (Trace Disabled)
[20:07:12.313]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:07:12.313]    __FlashAddr=0x00000000
[20:07:12.314]    __FlashLen=0x00000000
[20:07:12.314]    __FlashArg=0x00000000
[20:07:12.314]    __FlashOp=0x00000000
[20:07:12.315]    __Result=0x00000000
[20:07:12.315]    
[20:07:12.315]    // User-defined
[20:07:12.315]    DbgMCU_CR=0x00000007
[20:07:12.315]    DbgMCU_APB1_Fz=0x00000000
[20:07:12.315]    DbgMCU_APB2_Fz=0x00000000
[20:07:12.316]    DoOptionByteLoading=0x00000000
[20:07:12.316]  </debugvars>
[20:07:12.316]  
[20:07:12.316]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[20:07:12.316]    <block atomic="false" info="">
[20:07:12.317]      Sequence("CheckID");
[20:07:12.317]        <sequence name="CheckID" Pname="" disable="false" info="">
[20:07:12.317]          <block atomic="false" info="">
[20:07:12.317]            __var pidr1 = 0;
[20:07:12.317]              // -> [pidr1 <= 0x00000000]
[20:07:12.318]            __var pidr2 = 0;
[20:07:12.318]              // -> [pidr2 <= 0x00000000]
[20:07:12.318]            __var jep106id = 0;
[20:07:12.319]              // -> [jep106id <= 0x00000000]
[20:07:12.319]            __var ROMTableBase = 0;
[20:07:12.319]              // -> [ROMTableBase <= 0x00000000]
[20:07:12.320]            __ap = 0;      // AHB-AP
[20:07:12.320]              // -> [__ap <= 0x00000000]
[20:07:12.320]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[20:07:12.321]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[20:07:12.321]              // -> [ROMTableBase <= 0xF0000000]
[20:07:12.321]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[20:07:12.323]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[20:07:12.323]              // -> [pidr1 <= 0x00000004]
[20:07:12.323]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[20:07:12.324]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[20:07:12.325]              // -> [pidr2 <= 0x0000000A]
[20:07:12.325]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[20:07:12.325]              // -> [jep106id <= 0x00000020]
[20:07:12.326]          </block>
[20:07:12.326]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[20:07:12.326]            // if-block "jep106id != 0x20"
[20:07:12.326]              // =>  FALSE
[20:07:12.327]            // skip if-block "jep106id != 0x20"
[20:07:12.327]          </control>
[20:07:12.327]        </sequence>
[20:07:12.327]    </block>
[20:07:12.327]  </sequence>
[20:07:12.328]  
[20:07:12.349]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[20:07:12.349]  
[20:07:12.367]  <debugvars>
[20:07:12.368]    // Pre-defined
[20:07:12.368]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:07:12.369]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[20:07:12.370]    __dp=0x00000000
[20:07:12.370]    __ap=0x00000000
[20:07:12.371]    __traceout=0x00000000      (Trace Disabled)
[20:07:12.371]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:07:12.373]    __FlashAddr=0x00000000
[20:07:12.373]    __FlashLen=0x00000000
[20:07:12.374]    __FlashArg=0x00000000
[20:07:12.374]    __FlashOp=0x00000000
[20:07:12.375]    __Result=0x00000000
[20:07:12.376]    
[20:07:12.376]    // User-defined
[20:07:12.376]    DbgMCU_CR=0x00000007
[20:07:12.377]    DbgMCU_APB1_Fz=0x00000000
[20:07:12.377]    DbgMCU_APB2_Fz=0x00000000
[20:07:12.378]    DoOptionByteLoading=0x00000000
[20:07:12.378]  </debugvars>
[20:07:12.379]  
[20:07:12.379]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[20:07:12.380]    <block atomic="false" info="">
[20:07:12.381]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[20:07:12.382]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[20:07:12.383]    </block>
[20:07:12.383]    <block atomic="false" info="DbgMCU registers">
[20:07:12.384]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[20:07:12.386]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[20:07:12.387]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[20:07:12.388]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[20:07:12.389]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[20:07:12.390]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[20:07:12.391]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[20:07:12.392]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[20:07:12.393]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[20:07:12.393]    </block>
[20:07:12.394]  </sequence>
[20:07:12.394]  
[20:10:44.086]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[20:10:44.086]  
[20:10:44.086]  <debugvars>
[20:10:44.087]    // Pre-defined
[20:10:44.088]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:10:44.088]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[20:10:44.089]    __dp=0x00000000
[20:10:44.089]    __ap=0x00000000
[20:10:44.090]    __traceout=0x00000000      (Trace Disabled)
[20:10:44.090]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:10:44.091]    __FlashAddr=0x00000000
[20:10:44.091]    __FlashLen=0x00000000
[20:10:44.092]    __FlashArg=0x00000000
[20:10:44.092]    __FlashOp=0x00000000
[20:10:44.092]    __Result=0x00000000
[20:10:44.093]    
[20:10:44.093]    // User-defined
[20:10:44.093]    DbgMCU_CR=0x00000007
[20:10:44.093]    DbgMCU_APB1_Fz=0x00000000
[20:10:44.093]    DbgMCU_APB2_Fz=0x00000000
[20:10:44.094]    DoOptionByteLoading=0x00000000
[20:10:44.094]  </debugvars>
[20:10:44.094]  
[20:10:44.095]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[20:10:44.095]    <block atomic="false" info="">
[20:10:44.095]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[20:10:44.095]        // -> [connectionFlash <= 0x00000000]
[20:10:44.096]      __var FLASH_BASE = 0x40022000 ;
[20:10:44.097]        // -> [FLASH_BASE <= 0x40022000]
[20:10:44.097]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[20:10:44.097]        // -> [FLASH_CR <= 0x40022004]
[20:10:44.097]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[20:10:44.098]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[20:10:44.098]      __var LOCK_BIT = ( 1 << 0 ) ;
[20:10:44.099]        // -> [LOCK_BIT <= 0x00000001]
[20:10:44.099]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[20:10:44.099]        // -> [OPTLOCK_BIT <= 0x00000004]
[20:10:44.100]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[20:10:44.100]        // -> [FLASH_KEYR <= 0x4002200C]
[20:10:44.100]      __var FLASH_KEY1 = 0x89ABCDEF ;
[20:10:44.100]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[20:10:44.100]      __var FLASH_KEY2 = 0x02030405 ;
[20:10:44.100]        // -> [FLASH_KEY2 <= 0x02030405]
[20:10:44.101]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[20:10:44.101]        // -> [FLASH_OPTKEYR <= 0x40022014]
[20:10:44.101]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[20:10:44.101]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[20:10:44.102]      __var FLASH_OPTKEY2 = 0x24252627 ;
[20:10:44.102]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[20:10:44.103]      __var FLASH_CR_Value = 0 ;
[20:10:44.103]        // -> [FLASH_CR_Value <= 0x00000000]
[20:10:44.103]      __var DoDebugPortStop = 1 ;
[20:10:44.104]        // -> [DoDebugPortStop <= 0x00000001]
[20:10:44.104]      __var DP_CTRL_STAT = 0x4 ;
[20:10:44.104]        // -> [DP_CTRL_STAT <= 0x00000004]
[20:10:44.104]      __var DP_SELECT = 0x8 ;
[20:10:44.104]        // -> [DP_SELECT <= 0x00000008]
[20:10:44.105]    </block>
[20:10:44.105]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[20:10:44.105]      // if-block "connectionFlash && DoOptionByteLoading"
[20:10:44.105]        // =>  FALSE
[20:10:44.105]      // skip if-block "connectionFlash && DoOptionByteLoading"
[20:10:44.106]    </control>
[20:10:44.106]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[20:10:44.106]      // if-block "DoDebugPortStop"
[20:10:44.106]        // =>  TRUE
[20:10:44.106]      <block atomic="false" info="">
[20:10:44.106]        WriteDP(DP_SELECT, 0x00000000);
[20:10:44.107]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[20:10:44.107]        WriteDP(DP_CTRL_STAT, 0x00000000);
[20:10:44.108]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[20:10:44.108]      </block>
[20:10:44.108]      // end if-block "DoDebugPortStop"
[20:10:44.109]    </control>
[20:10:44.109]  </sequence>
[20:10:44.109]  
