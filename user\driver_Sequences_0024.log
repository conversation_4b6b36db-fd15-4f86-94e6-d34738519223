/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : D:\2025work\STM32L072PRO\source-application\user\driver_Sequences_0024.log
 *  Created     : 08:09:54 (21/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : D:\2025work\STM32L072PRO\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[08:09:54.949]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:09:54.949]  
[08:09:54.973]  <debugvars>
[08:09:54.991]    // Pre-defined
[08:09:55.015]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:09:55.032]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:09:55.033]    __dp=0x00000000
[08:09:55.034]    __ap=0x00000000
[08:09:55.036]    __traceout=0x00000000      (Trace Disabled)
[08:09:55.036]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:09:55.037]    __FlashAddr=0x00000000
[08:09:55.038]    __FlashLen=0x00000000
[08:09:55.038]    __FlashArg=0x00000000
[08:09:55.039]    __FlashOp=0x00000000
[08:09:55.040]    __Result=0x00000000
[08:09:55.041]    
[08:09:55.041]    // User-defined
[08:09:55.042]    DbgMCU_CR=0x00000007
[08:09:55.042]    DbgMCU_APB1_Fz=0x00000000
[08:09:55.043]    DbgMCU_APB2_Fz=0x00000000
[08:09:55.044]    DoOptionByteLoading=0x00000000
[08:09:55.044]  </debugvars>
[08:09:55.045]  
[08:09:55.046]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:09:55.046]    <block atomic="false" info="">
[08:09:55.047]      Sequence("CheckID");
[08:09:55.048]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:09:55.048]          <block atomic="false" info="">
[08:09:55.050]            __var pidr1 = 0;
[08:09:55.050]              // -> [pidr1 <= 0x00000000]
[08:09:55.051]            __var pidr2 = 0;
[08:09:55.052]              // -> [pidr2 <= 0x00000000]
[08:09:55.053]            __var jep106id = 0;
[08:09:55.053]              // -> [jep106id <= 0x00000000]
[08:09:55.054]            __var ROMTableBase = 0;
[08:09:55.055]              // -> [ROMTableBase <= 0x00000000]
[08:09:55.055]            __ap = 0;      // AHB-AP
[08:09:55.056]              // -> [__ap <= 0x00000000]
[08:09:55.057]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:09:55.058]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:09:55.059]              // -> [ROMTableBase <= 0xF0000000]
[08:09:55.059]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:09:55.061]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:09:55.061]              // -> [pidr1 <= 0x00000004]
[08:09:55.062]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:09:55.063]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:09:55.063]              // -> [pidr2 <= 0x0000000A]
[08:09:55.064]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:09:55.064]              // -> [jep106id <= 0x00000020]
[08:09:55.064]          </block>
[08:09:55.065]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:09:55.065]            // if-block "jep106id != 0x20"
[08:09:55.065]              // =>  FALSE
[08:09:55.065]            // skip if-block "jep106id != 0x20"
[08:09:55.065]          </control>
[08:09:55.065]        </sequence>
[08:09:55.066]    </block>
[08:09:55.066]  </sequence>
[08:09:55.066]  
[08:09:55.077]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:09:55.077]  
[08:09:55.082]  <debugvars>
[08:09:55.083]    // Pre-defined
[08:09:55.083]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:09:55.084]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:09:55.084]    __dp=0x00000000
[08:09:55.085]    __ap=0x00000000
[08:09:55.085]    __traceout=0x00000000      (Trace Disabled)
[08:09:55.086]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:09:55.086]    __FlashAddr=0x00000000
[08:09:55.087]    __FlashLen=0x00000000
[08:09:55.087]    __FlashArg=0x00000000
[08:09:55.088]    __FlashOp=0x00000000
[08:09:55.088]    __Result=0x00000000
[08:09:55.089]    
[08:09:55.089]    // User-defined
[08:09:55.089]    DbgMCU_CR=0x00000007
[08:09:55.090]    DbgMCU_APB1_Fz=0x00000000
[08:09:55.090]    DbgMCU_APB2_Fz=0x00000000
[08:09:55.091]    DoOptionByteLoading=0x00000000
[08:09:55.091]  </debugvars>
[08:09:55.091]  
[08:09:55.092]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:09:55.093]    <block atomic="false" info="">
[08:09:55.093]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:09:55.094]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:09:55.095]    </block>
[08:09:55.095]    <block atomic="false" info="DbgMCU registers">
[08:09:55.095]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:09:55.096]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[08:09:55.097]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[08:09:55.097]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:09:55.098]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:09:55.099]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:09:55.100]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:09:55.100]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:09:55.101]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:09:55.101]    </block>
[08:09:55.102]  </sequence>
[08:09:55.102]  
[08:10:03.088]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[08:10:03.088]  
[08:10:03.089]  <debugvars>
[08:10:03.090]    // Pre-defined
[08:10:03.091]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:10:03.091]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:10:03.092]    __dp=0x00000000
[08:10:03.092]    __ap=0x00000000
[08:10:03.092]    __traceout=0x00000000      (Trace Disabled)
[08:10:03.093]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:10:03.094]    __FlashAddr=0x00000000
[08:10:03.094]    __FlashLen=0x00000000
[08:10:03.094]    __FlashArg=0x00000000
[08:10:03.095]    __FlashOp=0x00000000
[08:10:03.095]    __Result=0x00000000
[08:10:03.095]    
[08:10:03.095]    // User-defined
[08:10:03.096]    DbgMCU_CR=0x00000007
[08:10:03.096]    DbgMCU_APB1_Fz=0x00000000
[08:10:03.096]    DbgMCU_APB2_Fz=0x00000000
[08:10:03.096]    DoOptionByteLoading=0x00000000
[08:10:03.097]  </debugvars>
[08:10:03.097]  
[08:10:03.097]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[08:10:03.097]    <block atomic="false" info="">
[08:10:03.097]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[08:10:03.098]        // -> [connectionFlash <= 0x00000001]
[08:10:03.098]      __var FLASH_BASE = 0x40022000 ;
[08:10:03.098]        // -> [FLASH_BASE <= 0x40022000]
[08:10:03.098]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[08:10:03.098]        // -> [FLASH_CR <= 0x40022004]
[08:10:03.098]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[08:10:03.099]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[08:10:03.099]      __var LOCK_BIT = ( 1 << 0 ) ;
[08:10:03.099]        // -> [LOCK_BIT <= 0x00000001]
[08:10:03.099]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[08:10:03.099]        // -> [OPTLOCK_BIT <= 0x00000004]
[08:10:03.100]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[08:10:03.100]        // -> [FLASH_KEYR <= 0x4002200C]
[08:10:03.100]      __var FLASH_KEY1 = 0x89ABCDEF ;
[08:10:03.100]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[08:10:03.100]      __var FLASH_KEY2 = 0x02030405 ;
[08:10:03.101]        // -> [FLASH_KEY2 <= 0x02030405]
[08:10:03.101]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[08:10:03.101]        // -> [FLASH_OPTKEYR <= 0x40022014]
[08:10:03.102]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[08:10:03.102]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[08:10:03.102]      __var FLASH_OPTKEY2 = 0x24252627 ;
[08:10:03.102]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[08:10:03.102]      __var FLASH_CR_Value = 0 ;
[08:10:03.103]        // -> [FLASH_CR_Value <= 0x00000000]
[08:10:03.103]      __var DoDebugPortStop = 1 ;
[08:10:03.103]        // -> [DoDebugPortStop <= 0x00000001]
[08:10:03.103]      __var DP_CTRL_STAT = 0x4 ;
[08:10:03.103]        // -> [DP_CTRL_STAT <= 0x00000004]
[08:10:03.103]      __var DP_SELECT = 0x8 ;
[08:10:03.104]        // -> [DP_SELECT <= 0x00000008]
[08:10:03.104]    </block>
[08:10:03.104]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[08:10:03.104]      // if-block "connectionFlash && DoOptionByteLoading"
[08:10:03.104]        // =>  FALSE
[08:10:03.104]      // skip if-block "connectionFlash && DoOptionByteLoading"
[08:10:03.105]    </control>
[08:10:03.105]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[08:10:03.105]      // if-block "DoDebugPortStop"
[08:10:03.106]        // =>  TRUE
[08:10:03.106]      <block atomic="false" info="">
[08:10:03.106]        WriteDP(DP_SELECT, 0x00000000);
[08:10:03.106]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[08:10:03.107]        WriteDP(DP_CTRL_STAT, 0x00000000);
[08:10:03.107]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[08:10:03.108]      </block>
[08:10:03.108]      // end if-block "DoDebugPortStop"
[08:10:03.108]    </control>
[08:10:03.109]  </sequence>
[08:10:03.109]  
[08:13:42.875]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:13:42.875]  
[08:13:42.875]  <debugvars>
[08:13:42.876]    // Pre-defined
[08:13:42.877]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:13:42.877]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:13:42.877]    __dp=0x00000000
[08:13:42.878]    __ap=0x00000000
[08:13:42.878]    __traceout=0x00000000      (Trace Disabled)
[08:13:42.878]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:13:42.878]    __FlashAddr=0x00000000
[08:13:42.878]    __FlashLen=0x00000000
[08:13:42.879]    __FlashArg=0x00000000
[08:13:42.879]    __FlashOp=0x00000000
[08:13:42.879]    __Result=0x00000000
[08:13:42.879]    
[08:13:42.879]    // User-defined
[08:13:42.879]    DbgMCU_CR=0x00000007
[08:13:42.879]    DbgMCU_APB1_Fz=0x00000000
[08:13:42.880]    DbgMCU_APB2_Fz=0x00000000
[08:13:42.880]    DoOptionByteLoading=0x00000000
[08:13:42.880]  </debugvars>
[08:13:42.880]  
[08:13:42.880]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:13:42.881]    <block atomic="false" info="">
[08:13:42.881]      Sequence("CheckID");
[08:13:42.881]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:13:42.881]          <block atomic="false" info="">
[08:13:42.882]            __var pidr1 = 0;
[08:13:42.882]              // -> [pidr1 <= 0x00000000]
[08:13:42.882]            __var pidr2 = 0;
[08:13:42.882]              // -> [pidr2 <= 0x00000000]
[08:13:42.883]            __var jep106id = 0;
[08:13:42.883]              // -> [jep106id <= 0x00000000]
[08:13:42.883]            __var ROMTableBase = 0;
[08:13:42.883]              // -> [ROMTableBase <= 0x00000000]
[08:13:42.883]            __ap = 0;      // AHB-AP
[08:13:42.884]              // -> [__ap <= 0x00000000]
[08:13:42.884]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:13:42.884]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:13:42.884]              // -> [ROMTableBase <= 0xF0000000]
[08:13:42.885]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:13:42.887]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:13:42.887]              // -> [pidr1 <= 0x00000004]
[08:13:42.887]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:13:42.888]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:13:42.888]              // -> [pidr2 <= 0x0000000A]
[08:13:42.888]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:13:42.888]              // -> [jep106id <= 0x00000020]
[08:13:42.888]          </block>
[08:13:42.889]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:13:42.889]            // if-block "jep106id != 0x20"
[08:13:42.889]              // =>  FALSE
[08:13:42.889]            // skip if-block "jep106id != 0x20"
[08:13:42.889]          </control>
[08:13:42.889]        </sequence>
[08:13:42.889]    </block>
[08:13:42.890]  </sequence>
[08:13:42.890]  
[08:13:42.901]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:13:42.901]  
[08:13:42.916]  <debugvars>
[08:13:42.917]    // Pre-defined
[08:13:42.917]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:13:42.917]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:13:42.918]    __dp=0x00000000
[08:13:42.918]    __ap=0x00000000
[08:13:42.919]    __traceout=0x00000000      (Trace Disabled)
[08:13:42.919]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:13:42.919]    __FlashAddr=0x00000000
[08:13:42.920]    __FlashLen=0x00000000
[08:13:42.920]    __FlashArg=0x00000000
[08:13:42.921]    __FlashOp=0x00000000
[08:13:42.921]    __Result=0x00000000
[08:13:42.922]    
[08:13:42.922]    // User-defined
[08:13:42.923]    DbgMCU_CR=0x00000007
[08:13:42.923]    DbgMCU_APB1_Fz=0x00000000
[08:13:42.924]    DbgMCU_APB2_Fz=0x00000000
[08:13:42.924]    DoOptionByteLoading=0x00000000
[08:13:42.924]  </debugvars>
[08:13:42.925]  
[08:13:42.925]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:13:42.926]    <block atomic="false" info="">
[08:13:42.926]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:13:42.928]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:13:42.929]    </block>
[08:13:42.929]    <block atomic="false" info="DbgMCU registers">
[08:13:42.930]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:13:42.932]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[08:13:42.934]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[08:13:42.935]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:13:42.937]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:13:42.938]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:13:42.939]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:13:42.939]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:13:42.941]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:13:42.941]    </block>
[08:13:42.941]  </sequence>
[08:13:42.941]  
[08:13:50.822]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[08:13:50.822]  
[08:13:50.823]  <debugvars>
[08:13:50.823]    // Pre-defined
[08:13:50.823]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:13:50.823]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:13:50.823]    __dp=0x00000000
[08:13:50.824]    __ap=0x00000000
[08:13:50.824]    __traceout=0x00000000      (Trace Disabled)
[08:13:50.824]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:13:50.824]    __FlashAddr=0x00000000
[08:13:50.824]    __FlashLen=0x00000000
[08:13:50.824]    __FlashArg=0x00000000
[08:13:50.824]    __FlashOp=0x00000000
[08:13:50.826]    __Result=0x00000000
[08:13:50.826]    
[08:13:50.826]    // User-defined
[08:13:50.826]    DbgMCU_CR=0x00000007
[08:13:50.826]    DbgMCU_APB1_Fz=0x00000000
[08:13:50.827]    DbgMCU_APB2_Fz=0x00000000
[08:13:50.827]    DoOptionByteLoading=0x00000000
[08:13:50.827]  </debugvars>
[08:13:50.828]  
[08:13:50.828]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[08:13:50.828]    <block atomic="false" info="">
[08:13:50.829]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[08:13:50.829]        // -> [connectionFlash <= 0x00000001]
[08:13:50.829]      __var FLASH_BASE = 0x40022000 ;
[08:13:50.829]        // -> [FLASH_BASE <= 0x40022000]
[08:13:50.830]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[08:13:50.830]        // -> [FLASH_CR <= 0x40022004]
[08:13:50.830]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[08:13:50.830]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[08:13:50.831]      __var LOCK_BIT = ( 1 << 0 ) ;
[08:13:50.831]        // -> [LOCK_BIT <= 0x00000001]
[08:13:50.831]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[08:13:50.831]        // -> [OPTLOCK_BIT <= 0x00000004]
[08:13:50.831]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[08:13:50.832]        // -> [FLASH_KEYR <= 0x4002200C]
[08:13:50.832]      __var FLASH_KEY1 = 0x89ABCDEF ;
[08:13:50.832]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[08:13:50.832]      __var FLASH_KEY2 = 0x02030405 ;
[08:13:50.832]        // -> [FLASH_KEY2 <= 0x02030405]
[08:13:50.833]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[08:13:50.833]        // -> [FLASH_OPTKEYR <= 0x40022014]
[08:13:50.833]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[08:13:50.833]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[08:13:50.833]      __var FLASH_OPTKEY2 = 0x24252627 ;
[08:13:50.833]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[08:13:50.833]      __var FLASH_CR_Value = 0 ;
[08:13:50.835]        // -> [FLASH_CR_Value <= 0x00000000]
[08:13:50.835]      __var DoDebugPortStop = 1 ;
[08:13:50.835]        // -> [DoDebugPortStop <= 0x00000001]
[08:13:50.835]      __var DP_CTRL_STAT = 0x4 ;
[08:13:50.835]        // -> [DP_CTRL_STAT <= 0x00000004]
[08:13:50.836]      __var DP_SELECT = 0x8 ;
[08:13:50.836]        // -> [DP_SELECT <= 0x00000008]
[08:13:50.836]    </block>
[08:13:50.836]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[08:13:50.836]      // if-block "connectionFlash && DoOptionByteLoading"
[08:13:50.837]        // =>  FALSE
[08:13:50.837]      // skip if-block "connectionFlash && DoOptionByteLoading"
[08:13:50.837]    </control>
[08:13:50.837]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[08:13:50.837]      // if-block "DoDebugPortStop"
[08:13:50.838]        // =>  TRUE
[08:13:50.838]      <block atomic="false" info="">
[08:13:50.838]        WriteDP(DP_SELECT, 0x00000000);
[08:13:50.838]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[08:13:50.839]        WriteDP(DP_CTRL_STAT, 0x00000000);
[08:13:50.839]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[08:13:50.839]      </block>
[08:13:50.840]      // end if-block "DoDebugPortStop"
[08:13:50.840]    </control>
[08:13:50.840]  </sequence>
[08:13:50.840]  
[08:19:02.339]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:19:02.339]  
[08:19:02.339]  <debugvars>
[08:19:02.341]    // Pre-defined
[08:19:02.341]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:19:02.341]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:19:02.341]    __dp=0x00000000
[08:19:02.342]    __ap=0x00000000
[08:19:02.342]    __traceout=0x00000000      (Trace Disabled)
[08:19:02.343]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:19:02.343]    __FlashAddr=0x00000000
[08:19:02.343]    __FlashLen=0x00000000
[08:19:02.343]    __FlashArg=0x00000000
[08:19:02.344]    __FlashOp=0x00000000
[08:19:02.344]    __Result=0x00000000
[08:19:02.344]    
[08:19:02.344]    // User-defined
[08:19:02.344]    DbgMCU_CR=0x00000007
[08:19:02.344]    DbgMCU_APB1_Fz=0x00000000
[08:19:02.345]    DbgMCU_APB2_Fz=0x00000000
[08:19:02.345]    DoOptionByteLoading=0x00000000
[08:19:02.345]  </debugvars>
[08:19:02.345]  
[08:19:02.345]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:19:02.346]    <block atomic="false" info="">
[08:19:02.346]      Sequence("CheckID");
[08:19:02.346]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:19:02.346]          <block atomic="false" info="">
[08:19:02.347]            __var pidr1 = 0;
[08:19:02.347]              // -> [pidr1 <= 0x00000000]
[08:19:02.347]            __var pidr2 = 0;
[08:19:02.347]              // -> [pidr2 <= 0x00000000]
[08:19:02.347]            __var jep106id = 0;
[08:19:02.347]              // -> [jep106id <= 0x00000000]
[08:19:02.347]            __var ROMTableBase = 0;
[08:19:02.349]              // -> [ROMTableBase <= 0x00000000]
[08:19:02.349]            __ap = 0;      // AHB-AP
[08:19:02.349]              // -> [__ap <= 0x00000000]
[08:19:02.349]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:19:02.350]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:19:02.350]              // -> [ROMTableBase <= 0xF0000000]
[08:19:02.350]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:19:02.351]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:19:02.352]              // -> [pidr1 <= 0x00000004]
[08:19:02.352]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:19:02.353]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:19:02.353]              // -> [pidr2 <= 0x0000000A]
[08:19:02.353]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:19:02.353]              // -> [jep106id <= 0x00000020]
[08:19:02.353]          </block>
[08:19:02.354]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:19:02.354]            // if-block "jep106id != 0x20"
[08:19:02.354]              // =>  FALSE
[08:19:02.354]            // skip if-block "jep106id != 0x20"
[08:19:02.354]          </control>
[08:19:02.354]        </sequence>
[08:19:02.355]    </block>
[08:19:02.355]  </sequence>
[08:19:02.355]  
[08:19:02.368]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:19:02.368]  
[08:19:02.369]  <debugvars>
[08:19:02.369]    // Pre-defined
[08:19:02.369]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:19:02.369]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:19:02.369]    __dp=0x00000000
[08:19:02.371]    __ap=0x00000000
[08:19:02.371]    __traceout=0x00000000      (Trace Disabled)
[08:19:02.371]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:19:02.371]    __FlashAddr=0x00000000
[08:19:02.372]    __FlashLen=0x00000000
[08:19:02.372]    __FlashArg=0x00000000
[08:19:02.372]    __FlashOp=0x00000000
[08:19:02.373]    __Result=0x00000000
[08:19:02.373]    
[08:19:02.373]    // User-defined
[08:19:02.373]    DbgMCU_CR=0x00000007
[08:19:02.373]    DbgMCU_APB1_Fz=0x00000000
[08:19:02.373]    DbgMCU_APB2_Fz=0x00000000
[08:19:02.373]    DoOptionByteLoading=0x00000000
[08:19:02.374]  </debugvars>
[08:19:02.374]  
[08:19:02.374]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:19:02.375]    <block atomic="false" info="">
[08:19:02.375]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:19:02.376]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:19:02.376]    </block>
[08:19:02.377]    <block atomic="false" info="DbgMCU registers">
[08:19:02.377]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:19:02.378]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[08:19:02.379]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[08:19:02.379]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:19:02.379]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:19:02.381]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:19:02.382]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:19:02.383]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:19:02.383]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:19:02.383]    </block>
[08:19:02.383]  </sequence>
[08:19:02.384]  
[08:19:10.262]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[08:19:10.262]  
[08:19:10.263]  <debugvars>
[08:19:10.263]    // Pre-defined
[08:19:10.264]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:19:10.264]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:19:10.265]    __dp=0x00000000
[08:19:10.265]    __ap=0x00000000
[08:19:10.265]    __traceout=0x00000000      (Trace Disabled)
[08:19:10.266]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:19:10.266]    __FlashAddr=0x00000000
[08:19:10.267]    __FlashLen=0x00000000
[08:19:10.267]    __FlashArg=0x00000000
[08:19:10.268]    __FlashOp=0x00000000
[08:19:10.268]    __Result=0x00000000
[08:19:10.269]    
[08:19:10.269]    // User-defined
[08:19:10.269]    DbgMCU_CR=0x00000007
[08:19:10.269]    DbgMCU_APB1_Fz=0x00000000
[08:19:10.270]    DbgMCU_APB2_Fz=0x00000000
[08:19:10.270]    DoOptionByteLoading=0x00000000
[08:19:10.270]  </debugvars>
[08:19:10.270]  
[08:19:10.270]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[08:19:10.270]    <block atomic="false" info="">
[08:19:10.271]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[08:19:10.271]        // -> [connectionFlash <= 0x00000001]
[08:19:10.272]      __var FLASH_BASE = 0x40022000 ;
[08:19:10.272]        // -> [FLASH_BASE <= 0x40022000]
[08:19:10.272]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[08:19:10.272]        // -> [FLASH_CR <= 0x40022004]
[08:19:10.273]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[08:19:10.273]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[08:19:10.273]      __var LOCK_BIT = ( 1 << 0 ) ;
[08:19:10.273]        // -> [LOCK_BIT <= 0x00000001]
[08:19:10.273]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[08:19:10.274]        // -> [OPTLOCK_BIT <= 0x00000004]
[08:19:10.274]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[08:19:10.274]        // -> [FLASH_KEYR <= 0x4002200C]
[08:19:10.274]      __var FLASH_KEY1 = 0x89ABCDEF ;
[08:19:10.274]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[08:19:10.275]      __var FLASH_KEY2 = 0x02030405 ;
[08:19:10.275]        // -> [FLASH_KEY2 <= 0x02030405]
[08:19:10.275]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[08:19:10.276]        // -> [FLASH_OPTKEYR <= 0x40022014]
[08:19:10.276]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[08:19:10.276]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[08:19:10.276]      __var FLASH_OPTKEY2 = 0x24252627 ;
[08:19:10.276]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[08:19:10.276]      __var FLASH_CR_Value = 0 ;
[08:19:10.277]        // -> [FLASH_CR_Value <= 0x00000000]
[08:19:10.277]      __var DoDebugPortStop = 1 ;
[08:19:10.277]        // -> [DoDebugPortStop <= 0x00000001]
[08:19:10.277]      __var DP_CTRL_STAT = 0x4 ;
[08:19:10.277]        // -> [DP_CTRL_STAT <= 0x00000004]
[08:19:10.278]      __var DP_SELECT = 0x8 ;
[08:19:10.278]        // -> [DP_SELECT <= 0x00000008]
[08:19:10.278]    </block>
[08:19:10.278]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[08:19:10.278]      // if-block "connectionFlash && DoOptionByteLoading"
[08:19:10.279]        // =>  FALSE
[08:19:10.279]      // skip if-block "connectionFlash && DoOptionByteLoading"
[08:19:10.279]    </control>
[08:19:10.279]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[08:19:10.279]      // if-block "DoDebugPortStop"
[08:19:10.280]        // =>  TRUE
[08:19:10.280]      <block atomic="false" info="">
[08:19:10.280]        WriteDP(DP_SELECT, 0x00000000);
[08:19:10.280]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[08:19:10.281]        WriteDP(DP_CTRL_STAT, 0x00000000);
[08:19:10.281]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[08:19:10.281]      </block>
[08:19:10.281]      // end if-block "DoDebugPortStop"
[08:19:10.282]    </control>
[08:19:10.282]  </sequence>
[08:19:10.282]  
[08:23:13.543]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:23:13.543]  
[08:23:13.544]  <debugvars>
[08:23:13.544]    // Pre-defined
[08:23:13.544]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:23:13.544]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:23:13.544]    __dp=0x00000000
[08:23:13.545]    __ap=0x00000000
[08:23:13.546]    __traceout=0x00000000      (Trace Disabled)
[08:23:13.546]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:23:13.546]    __FlashAddr=0x00000000
[08:23:13.547]    __FlashLen=0x00000000
[08:23:13.547]    __FlashArg=0x00000000
[08:23:13.547]    __FlashOp=0x00000000
[08:23:13.547]    __Result=0x00000000
[08:23:13.548]    
[08:23:13.548]    // User-defined
[08:23:13.548]    DbgMCU_CR=0x00000007
[08:23:13.548]    DbgMCU_APB1_Fz=0x00000000
[08:23:13.549]    DbgMCU_APB2_Fz=0x00000000
[08:23:13.549]    DoOptionByteLoading=0x00000000
[08:23:13.549]  </debugvars>
[08:23:13.549]  
[08:23:13.549]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:23:13.549]    <block atomic="false" info="">
[08:23:13.550]      Sequence("CheckID");
[08:23:13.550]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:23:13.550]          <block atomic="false" info="">
[08:23:13.550]            __var pidr1 = 0;
[08:23:13.550]              // -> [pidr1 <= 0x00000000]
[08:23:13.551]            __var pidr2 = 0;
[08:23:13.551]              // -> [pidr2 <= 0x00000000]
[08:23:13.551]            __var jep106id = 0;
[08:23:13.551]              // -> [jep106id <= 0x00000000]
[08:23:13.551]            __var ROMTableBase = 0;
[08:23:13.552]              // -> [ROMTableBase <= 0x00000000]
[08:23:13.552]            __ap = 0;      // AHB-AP
[08:23:13.552]              // -> [__ap <= 0x00000000]
[08:23:13.552]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:23:13.553]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:23:13.553]              // -> [ROMTableBase <= 0xF0000000]
[08:23:13.553]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:23:13.555]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:23:13.555]              // -> [pidr1 <= 0x00000004]
[08:23:13.555]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:23:13.556]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:23:13.557]              // -> [pidr2 <= 0x0000000A]
[08:23:13.558]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:23:13.558]              // -> [jep106id <= 0x00000020]
[08:23:13.558]          </block>
[08:23:13.558]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:23:13.559]            // if-block "jep106id != 0x20"
[08:23:13.559]              // =>  FALSE
[08:23:13.559]            // skip if-block "jep106id != 0x20"
[08:23:13.559]          </control>
[08:23:13.560]        </sequence>
[08:23:13.560]    </block>
[08:23:13.560]  </sequence>
[08:23:13.560]  
[08:23:13.572]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:23:13.572]  
[08:23:13.572]  <debugvars>
[08:23:13.573]    // Pre-defined
[08:23:13.573]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:23:13.573]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:23:13.573]    __dp=0x00000000
[08:23:13.574]    __ap=0x00000000
[08:23:13.574]    __traceout=0x00000000      (Trace Disabled)
[08:23:13.574]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:23:13.575]    __FlashAddr=0x00000000
[08:23:13.575]    __FlashLen=0x00000000
[08:23:13.576]    __FlashArg=0x00000000
[08:23:13.576]    __FlashOp=0x00000000
[08:23:13.576]    __Result=0x00000000
[08:23:13.576]    
[08:23:13.576]    // User-defined
[08:23:13.576]    DbgMCU_CR=0x00000007
[08:23:13.577]    DbgMCU_APB1_Fz=0x00000000
[08:23:13.577]    DbgMCU_APB2_Fz=0x00000000
[08:23:13.577]    DoOptionByteLoading=0x00000000
[08:23:13.577]  </debugvars>
[08:23:13.578]  
[08:23:13.578]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:23:13.578]    <block atomic="false" info="">
[08:23:13.578]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:23:13.579]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:23:13.579]    </block>
[08:23:13.580]    <block atomic="false" info="DbgMCU registers">
[08:23:13.580]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:23:13.581]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[08:23:13.582]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[08:23:13.582]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:23:13.583]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:23:13.583]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:23:13.584]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:23:13.584]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:23:13.585]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:23:13.585]    </block>
[08:23:13.585]  </sequence>
[08:23:13.585]  
[08:23:21.421]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[08:23:21.421]  
[08:23:21.421]  <debugvars>
[08:23:21.422]    // Pre-defined
[08:23:21.423]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:23:21.423]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:23:21.424]    __dp=0x00000000
[08:23:21.424]    __ap=0x00000000
[08:23:21.424]    __traceout=0x00000000      (Trace Disabled)
[08:23:21.424]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:23:21.426]    __FlashAddr=0x00000000
[08:23:21.426]    __FlashLen=0x00000000
[08:23:21.427]    __FlashArg=0x00000000
[08:23:21.427]    __FlashOp=0x00000000
[08:23:21.427]    __Result=0x00000000
[08:23:21.428]    
[08:23:21.428]    // User-defined
[08:23:21.428]    DbgMCU_CR=0x00000007
[08:23:21.428]    DbgMCU_APB1_Fz=0x00000000
[08:23:21.429]    DbgMCU_APB2_Fz=0x00000000
[08:23:21.429]    DoOptionByteLoading=0x00000000
[08:23:21.429]  </debugvars>
[08:23:21.430]  
[08:23:21.430]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[08:23:21.430]    <block atomic="false" info="">
[08:23:21.430]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[08:23:21.431]        // -> [connectionFlash <= 0x00000001]
[08:23:21.431]      __var FLASH_BASE = 0x40022000 ;
[08:23:21.431]        // -> [FLASH_BASE <= 0x40022000]
[08:23:21.431]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[08:23:21.431]        // -> [FLASH_CR <= 0x40022004]
[08:23:21.431]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[08:23:21.432]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[08:23:21.432]      __var LOCK_BIT = ( 1 << 0 ) ;
[08:23:21.432]        // -> [LOCK_BIT <= 0x00000001]
[08:23:21.432]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[08:23:21.432]        // -> [OPTLOCK_BIT <= 0x00000004]
[08:23:21.433]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[08:23:21.433]        // -> [FLASH_KEYR <= 0x4002200C]
[08:23:21.433]      __var FLASH_KEY1 = 0x89ABCDEF ;
[08:23:21.433]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[08:23:21.433]      __var FLASH_KEY2 = 0x02030405 ;
[08:23:21.434]        // -> [FLASH_KEY2 <= 0x02030405]
[08:23:21.434]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[08:23:21.434]        // -> [FLASH_OPTKEYR <= 0x40022014]
[08:23:21.434]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[08:23:21.434]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[08:23:21.434]      __var FLASH_OPTKEY2 = 0x24252627 ;
[08:23:21.435]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[08:23:21.435]      __var FLASH_CR_Value = 0 ;
[08:23:21.435]        // -> [FLASH_CR_Value <= 0x00000000]
[08:23:21.435]      __var DoDebugPortStop = 1 ;
[08:23:21.435]        // -> [DoDebugPortStop <= 0x00000001]
[08:23:21.435]      __var DP_CTRL_STAT = 0x4 ;
[08:23:21.436]        // -> [DP_CTRL_STAT <= 0x00000004]
[08:23:21.436]      __var DP_SELECT = 0x8 ;
[08:23:21.436]        // -> [DP_SELECT <= 0x00000008]
[08:23:21.436]    </block>
[08:23:21.436]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[08:23:21.437]      // if-block "connectionFlash && DoOptionByteLoading"
[08:23:21.437]        // =>  FALSE
[08:23:21.437]      // skip if-block "connectionFlash && DoOptionByteLoading"
[08:23:21.437]    </control>
[08:23:21.437]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[08:23:21.438]      // if-block "DoDebugPortStop"
[08:23:21.438]        // =>  TRUE
[08:23:21.438]      <block atomic="false" info="">
[08:23:21.438]        WriteDP(DP_SELECT, 0x00000000);
[08:23:21.439]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[08:23:21.439]        WriteDP(DP_CTRL_STAT, 0x00000000);
[08:23:21.439]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[08:23:21.440]      </block>
[08:23:21.440]      // end if-block "DoDebugPortStop"
[08:23:21.440]    </control>
[08:23:21.440]  </sequence>
[08:23:21.440]  
[08:28:31.877]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:28:31.877]  
[08:28:31.878]  <debugvars>
[08:28:31.879]    // Pre-defined
[08:28:31.879]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:28:31.879]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[08:28:31.879]    __dp=0x00000000
[08:28:31.880]    __ap=0x00000000
[08:28:31.880]    __traceout=0x00000000      (Trace Disabled)
[08:28:31.880]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:28:31.881]    __FlashAddr=0x00000000
[08:28:31.881]    __FlashLen=0x00000000
[08:28:31.881]    __FlashArg=0x00000000
[08:28:31.882]    __FlashOp=0x00000000
[08:28:31.882]    __Result=0x00000000
[08:28:31.882]    
[08:28:31.882]    // User-defined
[08:28:31.882]    DbgMCU_CR=0x00000007
[08:28:31.883]    DbgMCU_APB1_Fz=0x00000000
[08:28:31.883]    DbgMCU_APB2_Fz=0x00000000
[08:28:31.883]    DoOptionByteLoading=0x00000000
[08:28:31.884]  </debugvars>
[08:28:31.884]  
[08:28:31.884]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:28:31.885]    <block atomic="false" info="">
[08:28:31.885]      Sequence("CheckID");
[08:28:31.885]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:28:31.885]          <block atomic="false" info="">
[08:28:31.885]            __var pidr1 = 0;
[08:28:31.886]              // -> [pidr1 <= 0x00000000]
[08:28:31.886]            __var pidr2 = 0;
[08:28:31.886]              // -> [pidr2 <= 0x00000000]
[08:28:31.886]            __var jep106id = 0;
[08:28:31.886]              // -> [jep106id <= 0x00000000]
[08:28:31.886]            __var ROMTableBase = 0;
[08:28:31.886]              // -> [ROMTableBase <= 0x00000000]
[08:28:31.887]            __ap = 0;      // AHB-AP
[08:28:31.887]              // -> [__ap <= 0x00000000]
[08:28:31.888]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:28:31.889]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:28:31.889]              // -> [ROMTableBase <= 0xF0000000]
[08:28:31.889]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:28:31.890]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:28:31.891]              // -> [pidr1 <= 0x00000004]
[08:28:31.891]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:28:31.892]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:28:31.892]              // -> [pidr2 <= 0x0000000A]
[08:28:31.892]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:28:31.892]              // -> [jep106id <= 0x00000020]
[08:28:31.892]          </block>
[08:28:31.893]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:28:31.893]            // if-block "jep106id != 0x20"
[08:28:31.893]              // =>  FALSE
[08:28:31.893]            // skip if-block "jep106id != 0x20"
[08:28:31.893]          </control>
[08:28:31.894]        </sequence>
[08:28:31.894]    </block>
[08:28:31.894]  </sequence>
[08:28:31.894]  
[08:28:31.906]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:28:31.906]  
[08:28:31.924]  <debugvars>
[08:28:31.925]    // Pre-defined
[08:28:31.926]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:28:31.926]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[08:28:31.927]    __dp=0x00000000
[08:28:31.928]    __ap=0x00000000
[08:28:31.929]    __traceout=0x00000000      (Trace Disabled)
[08:28:31.929]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:28:31.930]    __FlashAddr=0x00000000
[08:28:31.931]    __FlashLen=0x00000000
[08:28:31.931]    __FlashArg=0x00000000
[08:28:31.931]    __FlashOp=0x00000000
[08:28:31.932]    __Result=0x00000000
[08:28:31.934]    
[08:28:31.934]    // User-defined
[08:28:31.934]    DbgMCU_CR=0x00000007
[08:28:31.934]    DbgMCU_APB1_Fz=0x00000000
[08:28:31.936]    DbgMCU_APB2_Fz=0x00000000
[08:28:31.936]    DoOptionByteLoading=0x00000000
[08:28:31.937]  </debugvars>
[08:28:31.938]  
[08:28:31.939]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:28:31.939]    <block atomic="false" info="">
[08:28:31.940]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:28:31.943]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:28:31.943]    </block>
[08:28:31.944]    <block atomic="false" info="DbgMCU registers">
[08:28:31.945]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:28:31.947]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[08:28:31.949]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[08:28:31.949]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:28:31.951]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:28:31.951]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:28:31.953]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:28:31.953]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:28:31.955]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:28:31.955]    </block>
[08:28:31.956]  </sequence>
[08:28:31.956]  
[08:42:53.956]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[08:42:53.956]  
[08:42:53.956]  <debugvars>
[08:42:53.957]    // Pre-defined
[08:42:53.958]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:42:53.958]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[08:42:53.959]    __dp=0x00000000
[08:42:53.959]    __ap=0x00000000
[08:42:53.960]    __traceout=0x00000000      (Trace Disabled)
[08:42:53.960]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:42:53.960]    __FlashAddr=0x00000000
[08:42:53.960]    __FlashLen=0x00000000
[08:42:53.961]    __FlashArg=0x00000000
[08:42:53.961]    __FlashOp=0x00000000
[08:42:53.962]    __Result=0x00000000
[08:42:53.962]    
[08:42:53.962]    // User-defined
[08:42:53.962]    DbgMCU_CR=0x00000007
[08:42:53.962]    DbgMCU_APB1_Fz=0x00000000
[08:42:53.963]    DbgMCU_APB2_Fz=0x00000000
[08:42:53.963]    DoOptionByteLoading=0x00000000
[08:42:53.963]  </debugvars>
[08:42:53.964]  
[08:42:53.965]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[08:42:53.965]    <block atomic="false" info="">
[08:42:53.965]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[08:42:53.965]        // -> [connectionFlash <= 0x00000000]
[08:42:53.966]      __var FLASH_BASE = 0x40022000 ;
[08:42:53.966]        // -> [FLASH_BASE <= 0x40022000]
[08:42:53.966]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[08:42:53.966]        // -> [FLASH_CR <= 0x40022004]
[08:42:53.967]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[08:42:53.967]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[08:42:53.967]      __var LOCK_BIT = ( 1 << 0 ) ;
[08:42:53.967]        // -> [LOCK_BIT <= 0x00000001]
[08:42:53.967]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[08:42:53.968]        // -> [OPTLOCK_BIT <= 0x00000004]
[08:42:53.968]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[08:42:53.968]        // -> [FLASH_KEYR <= 0x4002200C]
[08:42:53.968]      __var FLASH_KEY1 = 0x89ABCDEF ;
[08:42:53.968]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[08:42:53.968]      __var FLASH_KEY2 = 0x02030405 ;
[08:42:53.969]        // -> [FLASH_KEY2 <= 0x02030405]
[08:42:53.969]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[08:42:53.969]        // -> [FLASH_OPTKEYR <= 0x40022014]
[08:42:53.969]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[08:42:53.969]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[08:42:53.970]      __var FLASH_OPTKEY2 = 0x24252627 ;
[08:42:53.970]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[08:42:53.970]      __var FLASH_CR_Value = 0 ;
[08:42:53.970]        // -> [FLASH_CR_Value <= 0x00000000]
[08:42:53.971]      __var DoDebugPortStop = 1 ;
[08:42:53.971]        // -> [DoDebugPortStop <= 0x00000001]
[08:42:53.971]      __var DP_CTRL_STAT = 0x4 ;
[08:42:53.971]        // -> [DP_CTRL_STAT <= 0x00000004]
[08:42:53.971]      __var DP_SELECT = 0x8 ;
[08:42:53.972]        // -> [DP_SELECT <= 0x00000008]
[08:42:53.972]    </block>
[08:42:53.972]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[08:42:53.973]      // if-block "connectionFlash && DoOptionByteLoading"
[08:42:53.973]        // =>  FALSE
[08:42:53.973]      // skip if-block "connectionFlash && DoOptionByteLoading"
[08:42:53.973]    </control>
[08:42:53.973]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[08:42:53.973]      // if-block "DoDebugPortStop"
[08:42:53.974]        // =>  TRUE
[08:42:53.974]      <block atomic="false" info="">
[08:42:53.974]        WriteDP(DP_SELECT, 0x00000000);
[08:42:53.974]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[08:42:53.975]        WriteDP(DP_CTRL_STAT, 0x00000000);
[08:42:53.975]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[08:42:53.976]      </block>
[08:42:53.976]      // end if-block "DoDebugPortStop"
[08:42:53.976]    </control>
[08:42:53.976]  </sequence>
[08:42:53.976]  
[08:43:57.910]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:43:57.910]  
[08:43:57.910]  <debugvars>
[08:43:57.911]    // Pre-defined
[08:43:57.911]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:43:57.911]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:43:57.912]    __dp=0x00000000
[08:43:57.912]    __ap=0x00000000
[08:43:57.912]    __traceout=0x00000000      (Trace Disabled)
[08:43:57.912]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:43:57.913]    __FlashAddr=0x00000000
[08:43:57.913]    __FlashLen=0x00000000
[08:43:57.913]    __FlashArg=0x00000000
[08:43:57.914]    __FlashOp=0x00000000
[08:43:57.914]    __Result=0x00000000
[08:43:57.914]    
[08:43:57.914]    // User-defined
[08:43:57.915]    DbgMCU_CR=0x00000007
[08:43:57.915]    DbgMCU_APB1_Fz=0x00000000
[08:43:57.916]    DbgMCU_APB2_Fz=0x00000000
[08:43:57.916]    DoOptionByteLoading=0x00000000
[08:43:57.917]  </debugvars>
[08:43:57.917]  
[08:43:57.918]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:43:57.918]    <block atomic="false" info="">
[08:43:57.918]      Sequence("CheckID");
[08:43:57.918]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:43:57.918]          <block atomic="false" info="">
[08:43:57.919]            __var pidr1 = 0;
[08:43:57.919]              // -> [pidr1 <= 0x00000000]
[08:43:57.919]            __var pidr2 = 0;
[08:43:57.919]              // -> [pidr2 <= 0x00000000]
[08:43:57.919]            __var jep106id = 0;
[08:43:57.919]              // -> [jep106id <= 0x00000000]
[08:43:57.919]            __var ROMTableBase = 0;
[08:43:57.919]              // -> [ROMTableBase <= 0x00000000]
[08:43:57.919]            __ap = 0;      // AHB-AP
[08:43:57.920]              // -> [__ap <= 0x00000000]
[08:43:57.921]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:43:57.921]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:43:57.922]              // -> [ROMTableBase <= 0xF0000000]
[08:43:57.922]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:43:57.922]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:43:57.922]              // -> [pidr1 <= 0x00000004]
[08:43:57.923]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:43:57.924]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:43:57.925]              // -> [pidr2 <= 0x0000000A]
[08:43:57.925]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:43:57.925]              // -> [jep106id <= 0x00000020]
[08:43:57.926]          </block>
[08:43:57.926]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:43:57.926]            // if-block "jep106id != 0x20"
[08:43:57.926]              // =>  FALSE
[08:43:57.926]            // skip if-block "jep106id != 0x20"
[08:43:57.926]          </control>
[08:43:57.927]        </sequence>
[08:43:57.927]    </block>
[08:43:57.927]  </sequence>
[08:43:57.927]  
[08:43:57.939]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:43:57.939]  
[08:43:57.944]  <debugvars>
[08:43:57.944]    // Pre-defined
[08:43:57.945]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:43:57.946]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:43:57.947]    __dp=0x00000000
[08:43:57.947]    __ap=0x00000000
[08:43:57.948]    __traceout=0x00000000      (Trace Disabled)
[08:43:57.949]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:43:57.949]    __FlashAddr=0x00000000
[08:43:57.950]    __FlashLen=0x00000000
[08:43:57.951]    __FlashArg=0x00000000
[08:43:57.951]    __FlashOp=0x00000000
[08:43:57.952]    __Result=0x00000000
[08:43:57.952]    
[08:43:57.952]    // User-defined
[08:43:57.953]    DbgMCU_CR=0x00000007
[08:43:57.954]    DbgMCU_APB1_Fz=0x00000000
[08:43:57.954]    DbgMCU_APB2_Fz=0x00000000
[08:43:57.955]    DoOptionByteLoading=0x00000000
[08:43:57.956]  </debugvars>
[08:43:57.957]  
[08:43:57.957]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:43:57.958]    <block atomic="false" info="">
[08:43:57.959]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:43:57.961]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:43:57.961]    </block>
[08:43:57.961]    <block atomic="false" info="DbgMCU registers">
[08:43:57.962]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:43:57.964]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[08:43:57.966]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[08:43:57.966]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:43:57.968]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:43:57.968]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:43:57.969]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:43:57.970]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:43:57.971]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:43:57.971]    </block>
[08:43:57.971]  </sequence>
[08:43:57.972]  
[08:44:05.893]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[08:44:05.893]  
[08:44:05.894]  <debugvars>
[08:44:05.894]    // Pre-defined
[08:44:05.895]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:44:05.895]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:44:05.895]    __dp=0x00000000
[08:44:05.896]    __ap=0x00000000
[08:44:05.896]    __traceout=0x00000000      (Trace Disabled)
[08:44:05.897]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:44:05.897]    __FlashAddr=0x00000000
[08:44:05.898]    __FlashLen=0x00000000
[08:44:05.898]    __FlashArg=0x00000000
[08:44:05.899]    __FlashOp=0x00000000
[08:44:05.899]    __Result=0x00000000
[08:44:05.899]    
[08:44:05.899]    // User-defined
[08:44:05.900]    DbgMCU_CR=0x00000007
[08:44:05.900]    DbgMCU_APB1_Fz=0x00000000
[08:44:05.900]    DbgMCU_APB2_Fz=0x00000000
[08:44:05.901]    DoOptionByteLoading=0x00000000
[08:44:05.901]  </debugvars>
[08:44:05.901]  
[08:44:05.901]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[08:44:05.902]    <block atomic="false" info="">
[08:44:05.902]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[08:44:05.902]        // -> [connectionFlash <= 0x00000001]
[08:44:05.902]      __var FLASH_BASE = 0x40022000 ;
[08:44:05.903]        // -> [FLASH_BASE <= 0x40022000]
[08:44:05.903]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[08:44:05.903]        // -> [FLASH_CR <= 0x40022004]
[08:44:05.903]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[08:44:05.904]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[08:44:05.904]      __var LOCK_BIT = ( 1 << 0 ) ;
[08:44:05.904]        // -> [LOCK_BIT <= 0x00000001]
[08:44:05.904]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[08:44:05.904]        // -> [OPTLOCK_BIT <= 0x00000004]
[08:44:05.905]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[08:44:05.905]        // -> [FLASH_KEYR <= 0x4002200C]
[08:44:05.905]      __var FLASH_KEY1 = 0x89ABCDEF ;
[08:44:05.905]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[08:44:05.906]      __var FLASH_KEY2 = 0x02030405 ;
[08:44:05.906]        // -> [FLASH_KEY2 <= 0x02030405]
[08:44:05.906]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[08:44:05.906]        // -> [FLASH_OPTKEYR <= 0x40022014]
[08:44:05.906]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[08:44:05.906]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[08:44:05.907]      __var FLASH_OPTKEY2 = 0x24252627 ;
[08:44:05.907]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[08:44:05.907]      __var FLASH_CR_Value = 0 ;
[08:44:05.907]        // -> [FLASH_CR_Value <= 0x00000000]
[08:44:05.907]      __var DoDebugPortStop = 1 ;
[08:44:05.907]        // -> [DoDebugPortStop <= 0x00000001]
[08:44:05.908]      __var DP_CTRL_STAT = 0x4 ;
[08:44:05.908]        // -> [DP_CTRL_STAT <= 0x00000004]
[08:44:05.908]      __var DP_SELECT = 0x8 ;
[08:44:05.909]        // -> [DP_SELECT <= 0x00000008]
[08:44:05.909]    </block>
[08:44:05.909]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[08:44:05.909]      // if-block "connectionFlash && DoOptionByteLoading"
[08:44:05.910]        // =>  FALSE
[08:44:05.910]      // skip if-block "connectionFlash && DoOptionByteLoading"
[08:44:05.910]    </control>
[08:44:05.911]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[08:44:05.911]      // if-block "DoDebugPortStop"
[08:44:05.911]        // =>  TRUE
[08:44:05.911]      <block atomic="false" info="">
[08:44:05.911]        WriteDP(DP_SELECT, 0x00000000);
[08:44:05.912]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[08:44:05.912]        WriteDP(DP_CTRL_STAT, 0x00000000);
[08:44:05.913]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[08:44:05.913]      </block>
[08:44:05.913]      // end if-block "DoDebugPortStop"
[08:44:05.913]    </control>
[08:44:05.914]  </sequence>
[08:44:05.914]  
[08:57:31.292]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:57:31.292]  
[08:57:31.292]  <debugvars>
[08:57:31.293]    // Pre-defined
[08:57:31.293]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:57:31.293]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:57:31.294]    __dp=0x00000000
[08:57:31.294]    __ap=0x00000000
[08:57:31.294]    __traceout=0x00000000      (Trace Disabled)
[08:57:31.294]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:57:31.295]    __FlashAddr=0x00000000
[08:57:31.295]    __FlashLen=0x00000000
[08:57:31.295]    __FlashArg=0x00000000
[08:57:31.296]    __FlashOp=0x00000000
[08:57:31.296]    __Result=0x00000000
[08:57:31.296]    
[08:57:31.296]    // User-defined
[08:57:31.296]    DbgMCU_CR=0x00000007
[08:57:31.297]    DbgMCU_APB1_Fz=0x00000000
[08:57:31.298]    DbgMCU_APB2_Fz=0x00000000
[08:57:31.298]    DoOptionByteLoading=0x00000000
[08:57:31.298]  </debugvars>
[08:57:31.298]  
[08:57:31.299]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:57:31.299]    <block atomic="false" info="">
[08:57:31.299]      Sequence("CheckID");
[08:57:31.299]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:57:31.299]          <block atomic="false" info="">
[08:57:31.299]            __var pidr1 = 0;
[08:57:31.300]              // -> [pidr1 <= 0x00000000]
[08:57:31.300]            __var pidr2 = 0;
[08:57:31.300]              // -> [pidr2 <= 0x00000000]
[08:57:31.300]            __var jep106id = 0;
[08:57:31.300]              // -> [jep106id <= 0x00000000]
[08:57:31.301]            __var ROMTableBase = 0;
[08:57:31.301]              // -> [ROMTableBase <= 0x00000000]
[08:57:31.302]            __ap = 0;      // AHB-AP
[08:57:31.302]              // -> [__ap <= 0x00000000]
[08:57:31.302]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:57:31.303]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:57:31.303]              // -> [ROMTableBase <= 0xF0000000]
[08:57:31.304]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:57:31.305]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:57:31.305]              // -> [pidr1 <= 0x00000004]
[08:57:31.305]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:57:31.306]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:57:31.306]              // -> [pidr2 <= 0x0000000A]
[08:57:31.307]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:57:31.307]              // -> [jep106id <= 0x00000020]
[08:57:31.307]          </block>
[08:57:31.308]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:57:31.308]            // if-block "jep106id != 0x20"
[08:57:31.308]              // =>  FALSE
[08:57:31.308]            // skip if-block "jep106id != 0x20"
[08:57:31.309]          </control>
[08:57:31.309]        </sequence>
[08:57:31.309]    </block>
[08:57:31.309]  </sequence>
[08:57:31.309]  
[08:57:31.322]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:57:31.322]  
[08:57:31.326]  <debugvars>
[08:57:31.338]    // Pre-defined
[08:57:31.338]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:57:31.339]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:57:31.340]    __dp=0x00000000
[08:57:31.340]    __ap=0x00000000
[08:57:31.341]    __traceout=0x00000000      (Trace Disabled)
[08:57:31.341]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:57:31.342]    __FlashAddr=0x00000000
[08:57:31.342]    __FlashLen=0x00000000
[08:57:31.342]    __FlashArg=0x00000000
[08:57:31.343]    __FlashOp=0x00000000
[08:57:31.343]    __Result=0x00000000
[08:57:31.344]    
[08:57:31.344]    // User-defined
[08:57:31.344]    DbgMCU_CR=0x00000007
[08:57:31.345]    DbgMCU_APB1_Fz=0x00000000
[08:57:31.346]    DbgMCU_APB2_Fz=0x00000000
[08:57:31.346]    DoOptionByteLoading=0x00000000
[08:57:31.347]  </debugvars>
[08:57:31.347]  
[08:57:31.347]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:57:31.348]    <block atomic="false" info="">
[08:57:31.348]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:57:31.349]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:57:31.349]    </block>
[08:57:31.349]    <block atomic="false" info="DbgMCU registers">
[08:57:31.349]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:57:31.350]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[08:57:31.352]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[08:57:31.352]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:57:31.353]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:57:31.353]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:57:31.355]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:57:31.355]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:57:31.356]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:57:31.356]    </block>
[08:57:31.357]  </sequence>
[08:57:31.357]  
[08:57:39.426]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[08:57:39.426]  
[08:57:39.427]  <debugvars>
[08:57:39.428]    // Pre-defined
[08:57:39.428]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:57:39.428]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:57:39.429]    __dp=0x00000000
[08:57:39.429]    __ap=0x00000000
[08:57:39.430]    __traceout=0x00000000      (Trace Disabled)
[08:57:39.430]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:57:39.431]    __FlashAddr=0x00000000
[08:57:39.431]    __FlashLen=0x00000000
[08:57:39.432]    __FlashArg=0x00000000
[08:57:39.432]    __FlashOp=0x00000000
[08:57:39.432]    __Result=0x00000000
[08:57:39.432]    
[08:57:39.433]    // User-defined
[08:57:39.433]    DbgMCU_CR=0x00000007
[08:57:39.433]    DbgMCU_APB1_Fz=0x00000000
[08:57:39.433]    DbgMCU_APB2_Fz=0x00000000
[08:57:39.434]    DoOptionByteLoading=0x00000000
[08:57:39.434]  </debugvars>
[08:57:39.434]  
[08:57:39.434]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[08:57:39.434]    <block atomic="false" info="">
[08:57:39.435]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[08:57:39.435]        // -> [connectionFlash <= 0x00000001]
[08:57:39.435]      __var FLASH_BASE = 0x40022000 ;
[08:57:39.435]        // -> [FLASH_BASE <= 0x40022000]
[08:57:39.435]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[08:57:39.436]        // -> [FLASH_CR <= 0x40022004]
[08:57:39.436]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[08:57:39.436]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[08:57:39.436]      __var LOCK_BIT = ( 1 << 0 ) ;
[08:57:39.436]        // -> [LOCK_BIT <= 0x00000001]
[08:57:39.437]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[08:57:39.437]        // -> [OPTLOCK_BIT <= 0x00000004]
[08:57:39.438]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[08:57:39.438]        // -> [FLASH_KEYR <= 0x4002200C]
[08:57:39.439]      __var FLASH_KEY1 = 0x89ABCDEF ;
[08:57:39.439]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[08:57:39.439]      __var FLASH_KEY2 = 0x02030405 ;
[08:57:39.439]        // -> [FLASH_KEY2 <= 0x02030405]
[08:57:39.439]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[08:57:39.439]        // -> [FLASH_OPTKEYR <= 0x40022014]
[08:57:39.440]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[08:57:39.440]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[08:57:39.440]      __var FLASH_OPTKEY2 = 0x24252627 ;
[08:57:39.440]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[08:57:39.440]      __var FLASH_CR_Value = 0 ;
[08:57:39.441]        // -> [FLASH_CR_Value <= 0x00000000]
[08:57:39.441]      __var DoDebugPortStop = 1 ;
[08:57:39.441]        // -> [DoDebugPortStop <= 0x00000001]
[08:57:39.441]      __var DP_CTRL_STAT = 0x4 ;
[08:57:39.441]        // -> [DP_CTRL_STAT <= 0x00000004]
[08:57:39.441]      __var DP_SELECT = 0x8 ;
[08:57:39.442]        // -> [DP_SELECT <= 0x00000008]
[08:57:39.442]    </block>
[08:57:39.442]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[08:57:39.442]      // if-block "connectionFlash && DoOptionByteLoading"
[08:57:39.443]        // =>  FALSE
[08:57:39.443]      // skip if-block "connectionFlash && DoOptionByteLoading"
[08:57:39.443]    </control>
[08:57:39.443]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[08:57:39.443]      // if-block "DoDebugPortStop"
[08:57:39.443]        // =>  TRUE
[08:57:39.444]      <block atomic="false" info="">
[08:57:39.444]        WriteDP(DP_SELECT, 0x00000000);
[08:57:39.444]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[08:57:39.444]        WriteDP(DP_CTRL_STAT, 0x00000000);
[08:57:39.445]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[08:57:39.445]      </block>
[08:57:39.445]      // end if-block "DoDebugPortStop"
[08:57:39.445]    </control>
[08:57:39.445]  </sequence>
[08:57:39.446]  
[08:57:39.784]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:57:39.784]  
[08:57:39.785]  <debugvars>
[08:57:39.785]    // Pre-defined
[08:57:39.785]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:57:39.786]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[08:57:39.786]    __dp=0x00000000
[08:57:39.786]    __ap=0x00000000
[08:57:39.787]    __traceout=0x00000000      (Trace Disabled)
[08:57:39.787]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:57:39.787]    __FlashAddr=0x00000000
[08:57:39.788]    __FlashLen=0x00000000
[08:57:39.788]    __FlashArg=0x00000000
[08:57:39.788]    __FlashOp=0x00000000
[08:57:39.788]    __Result=0x00000000
[08:57:39.789]    
[08:57:39.789]    // User-defined
[08:57:39.789]    DbgMCU_CR=0x00000007
[08:57:39.789]    DbgMCU_APB1_Fz=0x00000000
[08:57:39.790]    DbgMCU_APB2_Fz=0x00000000
[08:57:39.790]    DoOptionByteLoading=0x00000000
[08:57:39.790]  </debugvars>
[08:57:39.791]  
[08:57:39.791]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:57:39.791]    <block atomic="false" info="">
[08:57:39.791]      Sequence("CheckID");
[08:57:39.791]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:57:39.791]          <block atomic="false" info="">
[08:57:39.791]            __var pidr1 = 0;
[08:57:39.791]              // -> [pidr1 <= 0x00000000]
[08:57:39.791]            __var pidr2 = 0;
[08:57:39.791]              // -> [pidr2 <= 0x00000000]
[08:57:39.792]            __var jep106id = 0;
[08:57:39.792]              // -> [jep106id <= 0x00000000]
[08:57:39.792]            __var ROMTableBase = 0;
[08:57:39.793]              // -> [ROMTableBase <= 0x00000000]
[08:57:39.793]            __ap = 0;      // AHB-AP
[08:57:39.793]              // -> [__ap <= 0x00000000]
[08:57:39.795]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:57:39.795]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:57:39.796]              // -> [ROMTableBase <= 0xF0000000]
[08:57:39.796]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:57:39.797]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:57:39.798]              // -> [pidr1 <= 0x00000004]
[08:57:39.798]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:57:39.799]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:57:39.799]              // -> [pidr2 <= 0x0000000A]
[08:57:39.799]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:57:39.800]              // -> [jep106id <= 0x00000020]
[08:57:39.800]          </block>
[08:57:39.800]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:57:39.800]            // if-block "jep106id != 0x20"
[08:57:39.800]              // =>  FALSE
[08:57:39.801]            // skip if-block "jep106id != 0x20"
[08:57:39.801]          </control>
[08:57:39.801]        </sequence>
[08:57:39.801]    </block>
[08:57:39.801]  </sequence>
[08:57:39.802]  
[08:57:39.813]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:57:39.813]  
[08:57:39.814]  <debugvars>
[08:57:39.814]    // Pre-defined
[08:57:39.814]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:57:39.815]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[08:57:39.815]    __dp=0x00000000
[08:57:39.815]    __ap=0x00000000
[08:57:39.816]    __traceout=0x00000000      (Trace Disabled)
[08:57:39.816]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:57:39.816]    __FlashAddr=0x00000000
[08:57:39.816]    __FlashLen=0x00000000
[08:57:39.816]    __FlashArg=0x00000000
[08:57:39.817]    __FlashOp=0x00000000
[08:57:39.817]    __Result=0x00000000
[08:57:39.817]    
[08:57:39.817]    // User-defined
[08:57:39.817]    DbgMCU_CR=0x00000007
[08:57:39.817]    DbgMCU_APB1_Fz=0x00000000
[08:57:39.818]    DbgMCU_APB2_Fz=0x00000000
[08:57:39.818]    DoOptionByteLoading=0x00000000
[08:57:39.818]  </debugvars>
[08:57:39.818]  
[08:57:39.818]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:57:39.818]    <block atomic="false" info="">
[08:57:39.819]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:57:39.819]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:57:39.820]    </block>
[08:57:39.820]    <block atomic="false" info="DbgMCU registers">
[08:57:39.820]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:57:39.821]        // -> [Read32(0x40021034) => 0x00000200]   (__dp=0x00000000, __ap=0x00000000)
[08:57:39.822]        // -> [Write32(0x40021034, 0x00400200)]   (__dp=0x00000000, __ap=0x00000000)
[08:57:39.822]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:57:39.823]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:57:39.823]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:57:39.823]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:57:39.823]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:57:39.824]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:57:39.825]    </block>
[08:57:39.825]  </sequence>
[08:57:39.826]  
[09:00:49.046]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:00:49.046]  
[09:00:49.047]  <debugvars>
[09:00:49.048]    // Pre-defined
[09:00:49.049]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:00:49.049]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[09:00:49.050]    __dp=0x00000000
[09:00:49.051]    __ap=0x00000000
[09:00:49.051]    __traceout=0x00000000      (Trace Disabled)
[09:00:49.052]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:00:49.054]    __FlashAddr=0x00000000
[09:00:49.054]    __FlashLen=0x00000000
[09:00:49.055]    __FlashArg=0x00000000
[09:00:49.056]    __FlashOp=0x00000000
[09:00:49.057]    __Result=0x00000000
[09:00:49.058]    
[09:00:49.058]    // User-defined
[09:00:49.059]    DbgMCU_CR=0x00000007
[09:00:49.060]    DbgMCU_APB1_Fz=0x00000000
[09:00:49.060]    DbgMCU_APB2_Fz=0x00000000
[09:00:49.061]    DoOptionByteLoading=0x00000000
[09:00:49.062]  </debugvars>
[09:00:49.062]  
[09:00:49.063]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:00:49.064]    <block atomic="false" info="">
[09:00:49.064]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:00:49.065]        // -> [connectionFlash <= 0x00000000]
[09:00:49.066]      __var FLASH_BASE = 0x40022000 ;
[09:00:49.066]        // -> [FLASH_BASE <= 0x40022000]
[09:00:49.067]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:00:49.067]        // -> [FLASH_CR <= 0x40022004]
[09:00:49.068]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:00:49.068]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:00:49.068]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:00:49.069]        // -> [LOCK_BIT <= 0x00000001]
[09:00:49.070]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:00:49.071]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:00:49.071]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:00:49.072]        // -> [FLASH_KEYR <= 0x4002200C]
[09:00:49.072]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:00:49.073]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:00:49.073]      __var FLASH_KEY2 = 0x02030405 ;
[09:00:49.074]        // -> [FLASH_KEY2 <= 0x02030405]
[09:00:49.075]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:00:49.075]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:00:49.076]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:00:49.076]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:00:49.077]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:00:49.077]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:00:49.078]      __var FLASH_CR_Value = 0 ;
[09:00:49.078]        // -> [FLASH_CR_Value <= 0x00000000]
[09:00:49.079]      __var DoDebugPortStop = 1 ;
[09:00:49.079]        // -> [DoDebugPortStop <= 0x00000001]
[09:00:49.080]      __var DP_CTRL_STAT = 0x4 ;
[09:00:49.080]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:00:49.081]      __var DP_SELECT = 0x8 ;
[09:00:49.082]        // -> [DP_SELECT <= 0x00000008]
[09:00:49.082]    </block>
[09:00:49.082]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:00:49.083]      // if-block "connectionFlash && DoOptionByteLoading"
[09:00:49.083]        // =>  FALSE
[09:00:49.084]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:00:49.085]    </control>
[09:00:49.085]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:00:49.085]      // if-block "DoDebugPortStop"
[09:00:49.086]        // =>  TRUE
[09:00:49.087]      <block atomic="false" info="">
[09:00:49.087]        WriteDP(DP_SELECT, 0x00000000);
[09:00:49.088]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:00:49.088]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:00:49.089]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:00:49.090]      </block>
[09:00:49.090]      // end if-block "DoDebugPortStop"
[09:00:49.090]    </control>
[09:00:49.091]  </sequence>
[09:00:49.091]  
[09:01:57.419]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:01:57.419]  
[09:01:57.419]  <debugvars>
[09:01:57.419]    // Pre-defined
[09:01:57.420]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:01:57.420]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:01:57.420]    __dp=0x00000000
[09:01:57.420]    __ap=0x00000000
[09:01:57.422]    __traceout=0x00000000      (Trace Disabled)
[09:01:57.422]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:01:57.422]    __FlashAddr=0x00000000
[09:01:57.422]    __FlashLen=0x00000000
[09:01:57.422]    __FlashArg=0x00000000
[09:01:57.422]    __FlashOp=0x00000000
[09:01:57.422]    __Result=0x00000000
[09:01:57.423]    
[09:01:57.423]    // User-defined
[09:01:57.423]    DbgMCU_CR=0x00000007
[09:01:57.423]    DbgMCU_APB1_Fz=0x00000000
[09:01:57.423]    DbgMCU_APB2_Fz=0x00000000
[09:01:57.424]    DoOptionByteLoading=0x00000000
[09:01:57.424]  </debugvars>
[09:01:57.424]  
[09:01:57.424]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:01:57.424]    <block atomic="false" info="">
[09:01:57.424]      Sequence("CheckID");
[09:01:57.426]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:01:57.426]          <block atomic="false" info="">
[09:01:57.426]            __var pidr1 = 0;
[09:01:57.426]              // -> [pidr1 <= 0x00000000]
[09:01:57.426]            __var pidr2 = 0;
[09:01:57.426]              // -> [pidr2 <= 0x00000000]
[09:01:57.426]            __var jep106id = 0;
[09:01:57.427]              // -> [jep106id <= 0x00000000]
[09:01:57.427]            __var ROMTableBase = 0;
[09:01:57.427]              // -> [ROMTableBase <= 0x00000000]
[09:01:57.427]            __ap = 0;      // AHB-AP
[09:01:57.427]              // -> [__ap <= 0x00000000]
[09:01:57.428]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:01:57.428]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:01:57.428]              // -> [ROMTableBase <= 0xF0000000]
[09:01:57.429]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:01:57.430]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:01:57.431]              // -> [pidr1 <= 0x00000004]
[09:01:57.431]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:01:57.432]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:01:57.432]              // -> [pidr2 <= 0x0000000A]
[09:01:57.432]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:01:57.432]              // -> [jep106id <= 0x00000020]
[09:01:57.433]          </block>
[09:01:57.433]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:01:57.433]            // if-block "jep106id != 0x20"
[09:01:57.433]              // =>  FALSE
[09:01:57.433]            // skip if-block "jep106id != 0x20"
[09:01:57.433]          </control>
[09:01:57.433]        </sequence>
[09:01:57.433]    </block>
[09:01:57.434]  </sequence>
[09:01:57.434]  
[09:01:57.448]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:01:57.448]  
[09:01:57.448]  <debugvars>
[09:01:57.448]    // Pre-defined
[09:01:57.449]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:01:57.449]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:01:57.449]    __dp=0x00000000
[09:01:57.450]    __ap=0x00000000
[09:01:57.450]    __traceout=0x00000000      (Trace Disabled)
[09:01:57.451]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:01:57.451]    __FlashAddr=0x00000000
[09:01:57.451]    __FlashLen=0x00000000
[09:01:57.452]    __FlashArg=0x00000000
[09:01:57.452]    __FlashOp=0x00000000
[09:01:57.453]    __Result=0x00000000
[09:01:57.453]    
[09:01:57.453]    // User-defined
[09:01:57.453]    DbgMCU_CR=0x00000007
[09:01:57.453]    DbgMCU_APB1_Fz=0x00000000
[09:01:57.453]    DbgMCU_APB2_Fz=0x00000000
[09:01:57.454]    DoOptionByteLoading=0x00000000
[09:01:57.454]  </debugvars>
[09:01:57.454]  
[09:01:57.454]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:01:57.454]    <block atomic="false" info="">
[09:01:57.455]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:01:57.455]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:01:57.456]    </block>
[09:01:57.456]    <block atomic="false" info="DbgMCU registers">
[09:01:57.456]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:01:57.457]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[09:01:57.459]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[09:01:57.459]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:01:57.460]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:01:57.461]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:01:57.462]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:01:57.462]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:01:57.463]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:01:57.464]    </block>
[09:01:57.464]  </sequence>
[09:01:57.464]  
[09:02:05.791]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:02:05.791]  
[09:02:05.792]  <debugvars>
[09:02:05.793]    // Pre-defined
[09:02:05.794]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:02:05.794]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:02:05.794]    __dp=0x00000000
[09:02:05.794]    __ap=0x00000000
[09:02:05.795]    __traceout=0x00000000      (Trace Disabled)
[09:02:05.796]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:02:05.796]    __FlashAddr=0x00000000
[09:02:05.796]    __FlashLen=0x00000000
[09:02:05.796]    __FlashArg=0x00000000
[09:02:05.796]    __FlashOp=0x00000000
[09:02:05.798]    __Result=0x00000000
[09:02:05.798]    
[09:02:05.798]    // User-defined
[09:02:05.799]    DbgMCU_CR=0x00000007
[09:02:05.799]    DbgMCU_APB1_Fz=0x00000000
[09:02:05.799]    DbgMCU_APB2_Fz=0x00000000
[09:02:05.800]    DoOptionByteLoading=0x00000000
[09:02:05.800]  </debugvars>
[09:02:05.800]  
[09:02:05.800]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:02:05.800]    <block atomic="false" info="">
[09:02:05.800]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:02:05.801]        // -> [connectionFlash <= 0x00000001]
[09:02:05.801]      __var FLASH_BASE = 0x40022000 ;
[09:02:05.801]        // -> [FLASH_BASE <= 0x40022000]
[09:02:05.801]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:02:05.801]        // -> [FLASH_CR <= 0x40022004]
[09:02:05.802]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:02:05.802]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:02:05.802]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:02:05.802]        // -> [LOCK_BIT <= 0x00000001]
[09:02:05.802]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:02:05.803]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:02:05.803]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:02:05.803]        // -> [FLASH_KEYR <= 0x4002200C]
[09:02:05.803]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:02:05.803]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:02:05.803]      __var FLASH_KEY2 = 0x02030405 ;
[09:02:05.804]        // -> [FLASH_KEY2 <= 0x02030405]
[09:02:05.804]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:02:05.804]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:02:05.804]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:02:05.804]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:02:05.805]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:02:05.805]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:02:05.805]      __var FLASH_CR_Value = 0 ;
[09:02:05.805]        // -> [FLASH_CR_Value <= 0x00000000]
[09:02:05.805]      __var DoDebugPortStop = 1 ;
[09:02:05.805]        // -> [DoDebugPortStop <= 0x00000001]
[09:02:05.806]      __var DP_CTRL_STAT = 0x4 ;
[09:02:05.806]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:02:05.806]      __var DP_SELECT = 0x8 ;
[09:02:05.806]        // -> [DP_SELECT <= 0x00000008]
[09:02:05.806]    </block>
[09:02:05.807]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:02:05.807]      // if-block "connectionFlash && DoOptionByteLoading"
[09:02:05.807]        // =>  FALSE
[09:02:05.807]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:02:05.807]    </control>
[09:02:05.807]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:02:05.808]      // if-block "DoDebugPortStop"
[09:02:05.808]        // =>  TRUE
[09:02:05.808]      <block atomic="false" info="">
[09:02:05.808]        WriteDP(DP_SELECT, 0x00000000);
[09:02:05.808]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:02:05.808]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:02:05.809]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:02:05.809]      </block>
[09:02:05.809]      // end if-block "DoDebugPortStop"
[09:02:05.809]    </control>
[09:02:05.810]  </sequence>
[09:02:05.810]  
[09:06:23.518]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:06:23.518]  
[09:06:23.518]  <debugvars>
[09:06:23.519]    // Pre-defined
[09:06:23.520]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:06:23.520]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:06:23.520]    __dp=0x00000000
[09:06:23.521]    __ap=0x00000000
[09:06:23.522]    __traceout=0x00000000      (Trace Disabled)
[09:06:23.522]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:06:23.522]    __FlashAddr=0x00000000
[09:06:23.522]    __FlashLen=0x00000000
[09:06:23.523]    __FlashArg=0x00000000
[09:06:23.523]    __FlashOp=0x00000000
[09:06:23.523]    __Result=0x00000000
[09:06:23.524]    
[09:06:23.524]    // User-defined
[09:06:23.524]    DbgMCU_CR=0x00000007
[09:06:23.524]    DbgMCU_APB1_Fz=0x00000000
[09:06:23.525]    DbgMCU_APB2_Fz=0x00000000
[09:06:23.525]    DoOptionByteLoading=0x00000000
[09:06:23.525]  </debugvars>
[09:06:23.525]  
[09:06:23.525]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:06:23.526]    <block atomic="false" info="">
[09:06:23.526]      Sequence("CheckID");
[09:06:23.526]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:06:23.526]          <block atomic="false" info="">
[09:06:23.526]            __var pidr1 = 0;
[09:06:23.526]              // -> [pidr1 <= 0x00000000]
[09:06:23.527]            __var pidr2 = 0;
[09:06:23.527]              // -> [pidr2 <= 0x00000000]
[09:06:23.527]            __var jep106id = 0;
[09:06:23.527]              // -> [jep106id <= 0x00000000]
[09:06:23.527]            __var ROMTableBase = 0;
[09:06:23.528]              // -> [ROMTableBase <= 0x00000000]
[09:06:23.528]            __ap = 0;      // AHB-AP
[09:06:23.528]              // -> [__ap <= 0x00000000]
[09:06:23.528]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:06:23.529]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:06:23.529]              // -> [ROMTableBase <= 0xF0000000]
[09:06:23.529]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:06:23.531]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:06:23.531]              // -> [pidr1 <= 0x00000004]
[09:06:23.531]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:06:23.532]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:06:23.532]              // -> [pidr2 <= 0x0000000A]
[09:06:23.532]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:06:23.532]              // -> [jep106id <= 0x00000020]
[09:06:23.532]          </block>
[09:06:23.533]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:06:23.534]            // if-block "jep106id != 0x20"
[09:06:23.534]              // =>  FALSE
[09:06:23.535]            // skip if-block "jep106id != 0x20"
[09:06:23.535]          </control>
[09:06:23.535]        </sequence>
[09:06:23.535]    </block>
[09:06:23.535]  </sequence>
[09:06:23.536]  
[09:06:23.547]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:06:23.547]  
[09:06:23.560]  <debugvars>
[09:06:23.561]    // Pre-defined
[09:06:23.561]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:06:23.562]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:06:23.562]    __dp=0x00000000
[09:06:23.562]    __ap=0x00000000
[09:06:23.563]    __traceout=0x00000000      (Trace Disabled)
[09:06:23.563]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:06:23.563]    __FlashAddr=0x00000000
[09:06:23.564]    __FlashLen=0x00000000
[09:06:23.565]    __FlashArg=0x00000000
[09:06:23.565]    __FlashOp=0x00000000
[09:06:23.565]    __Result=0x00000000
[09:06:23.566]    
[09:06:23.566]    // User-defined
[09:06:23.566]    DbgMCU_CR=0x00000007
[09:06:23.566]    DbgMCU_APB1_Fz=0x00000000
[09:06:23.567]    DbgMCU_APB2_Fz=0x00000000
[09:06:23.567]    DoOptionByteLoading=0x00000000
[09:06:23.567]  </debugvars>
[09:06:23.567]  
[09:06:23.567]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:06:23.568]    <block atomic="false" info="">
[09:06:23.568]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:06:23.569]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:06:23.569]    </block>
[09:06:23.569]    <block atomic="false" info="DbgMCU registers">
[09:06:23.570]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:06:23.571]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[09:06:23.572]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[09:06:23.572]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:06:23.573]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:06:23.573]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:06:23.574]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:06:23.574]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:06:23.575]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:06:23.575]    </block>
[09:06:23.576]  </sequence>
[09:06:23.576]  
[09:06:32.029]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:06:32.029]  
[09:06:32.029]  <debugvars>
[09:06:32.030]    // Pre-defined
[09:06:32.030]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:06:32.031]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:06:32.031]    __dp=0x00000000
[09:06:32.031]    __ap=0x00000000
[09:06:32.031]    __traceout=0x00000000      (Trace Disabled)
[09:06:32.031]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:06:32.032]    __FlashAddr=0x00000000
[09:06:32.033]    __FlashLen=0x00000000
[09:06:32.033]    __FlashArg=0x00000000
[09:06:32.034]    __FlashOp=0x00000000
[09:06:32.034]    __Result=0x00000000
[09:06:32.034]    
[09:06:32.034]    // User-defined
[09:06:32.034]    DbgMCU_CR=0x00000007
[09:06:32.035]    DbgMCU_APB1_Fz=0x00000000
[09:06:32.035]    DbgMCU_APB2_Fz=0x00000000
[09:06:32.035]    DoOptionByteLoading=0x00000000
[09:06:32.035]  </debugvars>
[09:06:32.035]  
[09:06:32.036]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:06:32.036]    <block atomic="false" info="">
[09:06:32.036]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:06:32.036]        // -> [connectionFlash <= 0x00000001]
[09:06:32.037]      __var FLASH_BASE = 0x40022000 ;
[09:06:32.037]        // -> [FLASH_BASE <= 0x40022000]
[09:06:32.037]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:06:32.037]        // -> [FLASH_CR <= 0x40022004]
[09:06:32.038]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:06:32.038]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:06:32.038]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:06:32.038]        // -> [LOCK_BIT <= 0x00000001]
[09:06:32.038]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:06:32.039]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:06:32.039]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:06:32.039]        // -> [FLASH_KEYR <= 0x4002200C]
[09:06:32.039]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:06:32.039]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:06:32.040]      __var FLASH_KEY2 = 0x02030405 ;
[09:06:32.040]        // -> [FLASH_KEY2 <= 0x02030405]
[09:06:32.040]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:06:32.040]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:06:32.040]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:06:32.040]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:06:32.041]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:06:32.041]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:06:32.041]      __var FLASH_CR_Value = 0 ;
[09:06:32.041]        // -> [FLASH_CR_Value <= 0x00000000]
[09:06:32.041]      __var DoDebugPortStop = 1 ;
[09:06:32.042]        // -> [DoDebugPortStop <= 0x00000001]
[09:06:32.042]      __var DP_CTRL_STAT = 0x4 ;
[09:06:32.042]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:06:32.042]      __var DP_SELECT = 0x8 ;
[09:06:32.042]        // -> [DP_SELECT <= 0x00000008]
[09:06:32.042]    </block>
[09:06:32.043]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:06:32.043]      // if-block "connectionFlash && DoOptionByteLoading"
[09:06:32.043]        // =>  FALSE
[09:06:32.043]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:06:32.043]    </control>
[09:06:32.044]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:06:32.044]      // if-block "DoDebugPortStop"
[09:06:32.044]        // =>  TRUE
[09:06:32.044]      <block atomic="false" info="">
[09:06:32.044]        WriteDP(DP_SELECT, 0x00000000);
[09:06:32.045]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:06:32.045]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:06:32.046]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:06:32.046]      </block>
[09:06:32.046]      // end if-block "DoDebugPortStop"
[09:06:32.046]    </control>
[09:06:32.046]  </sequence>
[09:06:32.046]  
[09:08:10.047]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:08:10.047]  
[09:08:10.047]  <debugvars>
[09:08:10.047]    // Pre-defined
[09:08:10.048]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:08:10.048]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:08:10.048]    __dp=0x00000000
[09:08:10.049]    __ap=0x00000000
[09:08:10.049]    __traceout=0x00000000      (Trace Disabled)
[09:08:10.049]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:08:10.049]    __FlashAddr=0x00000000
[09:08:10.050]    __FlashLen=0x00000000
[09:08:10.050]    __FlashArg=0x00000000
[09:08:10.050]    __FlashOp=0x00000000
[09:08:10.051]    __Result=0x00000000
[09:08:10.051]    
[09:08:10.051]    // User-defined
[09:08:10.051]    DbgMCU_CR=0x00000007
[09:08:10.051]    DbgMCU_APB1_Fz=0x00000000
[09:08:10.052]    DbgMCU_APB2_Fz=0x00000000
[09:08:10.052]    DoOptionByteLoading=0x00000000
[09:08:10.052]  </debugvars>
[09:08:10.053]  
[09:08:10.053]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:08:10.053]    <block atomic="false" info="">
[09:08:10.053]      Sequence("CheckID");
[09:08:10.053]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:08:10.054]          <block atomic="false" info="">
[09:08:10.054]            __var pidr1 = 0;
[09:08:10.054]              // -> [pidr1 <= 0x00000000]
[09:08:10.054]            __var pidr2 = 0;
[09:08:10.054]              // -> [pidr2 <= 0x00000000]
[09:08:10.055]            __var jep106id = 0;
[09:08:10.055]              // -> [jep106id <= 0x00000000]
[09:08:10.055]            __var ROMTableBase = 0;
[09:08:10.055]              // -> [ROMTableBase <= 0x00000000]
[09:08:10.055]            __ap = 0;      // AHB-AP
[09:08:10.055]              // -> [__ap <= 0x00000000]
[09:08:10.056]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:08:10.056]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:08:10.057]              // -> [ROMTableBase <= 0xF0000000]
[09:08:10.057]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:08:10.058]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:08:10.059]              // -> [pidr1 <= 0x00000004]
[09:08:10.059]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:08:10.060]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:08:10.060]              // -> [pidr2 <= 0x0000000A]
[09:08:10.060]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:08:10.061]              // -> [jep106id <= 0x00000020]
[09:08:10.061]          </block>
[09:08:10.061]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:08:10.062]            // if-block "jep106id != 0x20"
[09:08:10.062]              // =>  FALSE
[09:08:10.062]            // skip if-block "jep106id != 0x20"
[09:08:10.062]          </control>
[09:08:10.062]        </sequence>
[09:08:10.063]    </block>
[09:08:10.063]  </sequence>
[09:08:10.063]  
[09:08:10.075]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:08:10.075]  
[09:08:10.076]  <debugvars>
[09:08:10.076]    // Pre-defined
[09:08:10.076]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:08:10.076]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:08:10.077]    __dp=0x00000000
[09:08:10.077]    __ap=0x00000000
[09:08:10.077]    __traceout=0x00000000      (Trace Disabled)
[09:08:10.078]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:08:10.078]    __FlashAddr=0x00000000
[09:08:10.078]    __FlashLen=0x00000000
[09:08:10.078]    __FlashArg=0x00000000
[09:08:10.078]    __FlashOp=0x00000000
[09:08:10.079]    __Result=0x00000000
[09:08:10.079]    
[09:08:10.079]    // User-defined
[09:08:10.079]    DbgMCU_CR=0x00000007
[09:08:10.079]    DbgMCU_APB1_Fz=0x00000000
[09:08:10.079]    DbgMCU_APB2_Fz=0x00000000
[09:08:10.079]    DoOptionByteLoading=0x00000000
[09:08:10.080]  </debugvars>
[09:08:10.080]  
[09:08:10.080]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:08:10.080]    <block atomic="false" info="">
[09:08:10.080]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:08:10.081]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:08:10.081]    </block>
[09:08:10.082]    <block atomic="false" info="DbgMCU registers">
[09:08:10.082]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:08:10.083]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[09:08:10.083]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[09:08:10.084]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:08:10.084]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:08:10.085]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:08:10.086]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:08:10.086]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:08:10.087]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:08:10.087]    </block>
[09:08:10.087]  </sequence>
[09:08:10.087]  
[09:08:18.436]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:08:18.436]  
[09:08:18.437]  <debugvars>
[09:08:18.438]    // Pre-defined
[09:08:18.439]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:08:18.439]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:08:18.440]    __dp=0x00000000
[09:08:18.440]    __ap=0x00000000
[09:08:18.440]    __traceout=0x00000000      (Trace Disabled)
[09:08:18.441]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:08:18.442]    __FlashAddr=0x00000000
[09:08:18.442]    __FlashLen=0x00000000
[09:08:18.443]    __FlashArg=0x00000000
[09:08:18.443]    __FlashOp=0x00000000
[09:08:18.443]    __Result=0x00000000
[09:08:18.444]    
[09:08:18.444]    // User-defined
[09:08:18.444]    DbgMCU_CR=0x00000007
[09:08:18.445]    DbgMCU_APB1_Fz=0x00000000
[09:08:18.445]    DbgMCU_APB2_Fz=0x00000000
[09:08:18.445]    DoOptionByteLoading=0x00000000
[09:08:18.446]  </debugvars>
[09:08:18.446]  
[09:08:18.446]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:08:18.446]    <block atomic="false" info="">
[09:08:18.446]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:08:18.447]        // -> [connectionFlash <= 0x00000001]
[09:08:18.448]      __var FLASH_BASE = 0x40022000 ;
[09:08:18.448]        // -> [FLASH_BASE <= 0x40022000]
[09:08:18.448]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:08:18.448]        // -> [FLASH_CR <= 0x40022004]
[09:08:18.448]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:08:18.448]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:08:18.449]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:08:18.449]        // -> [LOCK_BIT <= 0x00000001]
[09:08:18.449]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:08:18.449]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:08:18.449]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:08:18.450]        // -> [FLASH_KEYR <= 0x4002200C]
[09:08:18.450]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:08:18.450]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:08:18.450]      __var FLASH_KEY2 = 0x02030405 ;
[09:08:18.450]        // -> [FLASH_KEY2 <= 0x02030405]
[09:08:18.450]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:08:18.451]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:08:18.451]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:08:18.451]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:08:18.451]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:08:18.451]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:08:18.452]      __var FLASH_CR_Value = 0 ;
[09:08:18.452]        // -> [FLASH_CR_Value <= 0x00000000]
[09:08:18.452]      __var DoDebugPortStop = 1 ;
[09:08:18.452]        // -> [DoDebugPortStop <= 0x00000001]
[09:08:18.452]      __var DP_CTRL_STAT = 0x4 ;
[09:08:18.453]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:08:18.453]      __var DP_SELECT = 0x8 ;
[09:08:18.453]        // -> [DP_SELECT <= 0x00000008]
[09:08:18.453]    </block>
[09:08:18.453]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:08:18.453]      // if-block "connectionFlash && DoOptionByteLoading"
[09:08:18.454]        // =>  FALSE
[09:08:18.454]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:08:18.454]    </control>
[09:08:18.454]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:08:18.454]      // if-block "DoDebugPortStop"
[09:08:18.455]        // =>  TRUE
[09:08:18.455]      <block atomic="false" info="">
[09:08:18.455]        WriteDP(DP_SELECT, 0x00000000);
[09:08:18.455]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:08:18.455]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:08:18.456]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:08:18.456]      </block>
[09:08:18.457]      // end if-block "DoDebugPortStop"
[09:08:18.457]    </control>
[09:08:18.458]  </sequence>
[09:08:18.458]  
[09:09:07.375]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:09:07.375]  
[09:09:07.376]  <debugvars>
[09:09:07.376]    // Pre-defined
[09:09:07.376]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:09:07.376]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[09:09:07.377]    __dp=0x00000000
[09:09:07.377]    __ap=0x00000000
[09:09:07.377]    __traceout=0x00000000      (Trace Disabled)
[09:09:07.377]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:09:07.377]    __FlashAddr=0x00000000
[09:09:07.377]    __FlashLen=0x00000000
[09:09:07.378]    __FlashArg=0x00000000
[09:09:07.378]    __FlashOp=0x00000000
[09:09:07.379]    __Result=0x00000000
[09:09:07.379]    
[09:09:07.379]    // User-defined
[09:09:07.379]    DbgMCU_CR=0x00000007
[09:09:07.380]    DbgMCU_APB1_Fz=0x00000000
[09:09:07.380]    DbgMCU_APB2_Fz=0x00000000
[09:09:07.380]    DoOptionByteLoading=0x00000000
[09:09:07.380]  </debugvars>
[09:09:07.380]  
[09:09:07.381]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:09:07.381]    <block atomic="false" info="">
[09:09:07.381]      Sequence("CheckID");
[09:09:07.381]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:09:07.381]          <block atomic="false" info="">
[09:09:07.382]            __var pidr1 = 0;
[09:09:07.382]              // -> [pidr1 <= 0x00000000]
[09:09:07.382]            __var pidr2 = 0;
[09:09:07.382]              // -> [pidr2 <= 0x00000000]
[09:09:07.382]            __var jep106id = 0;
[09:09:07.383]              // -> [jep106id <= 0x00000000]
[09:09:07.383]            __var ROMTableBase = 0;
[09:09:07.383]              // -> [ROMTableBase <= 0x00000000]
[09:09:07.383]            __ap = 0;      // AHB-AP
[09:09:07.384]              // -> [__ap <= 0x00000000]
[09:09:07.384]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:09:07.385]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:09:07.385]              // -> [ROMTableBase <= 0xF0000000]
[09:09:07.385]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:09:07.387]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:09:07.387]              // -> [pidr1 <= 0x00000004]
[09:09:07.388]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:09:07.388]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:09:07.389]              // -> [pidr2 <= 0x0000000A]
[09:09:07.389]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:09:07.389]              // -> [jep106id <= 0x00000020]
[09:09:07.390]          </block>
[09:09:07.390]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:09:07.390]            // if-block "jep106id != 0x20"
[09:09:07.390]              // =>  FALSE
[09:09:07.390]            // skip if-block "jep106id != 0x20"
[09:09:07.390]          </control>
[09:09:07.391]        </sequence>
[09:09:07.391]    </block>
[09:09:07.391]  </sequence>
[09:09:07.391]  
[09:09:07.404]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:09:07.404]  
[09:09:07.424]  <debugvars>
[09:09:07.424]    // Pre-defined
[09:09:07.425]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:09:07.425]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[09:09:07.425]    __dp=0x00000000
[09:09:07.426]    __ap=0x00000000
[09:09:07.426]    __traceout=0x00000000      (Trace Disabled)
[09:09:07.426]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:09:07.427]    __FlashAddr=0x00000000
[09:09:07.427]    __FlashLen=0x00000000
[09:09:07.427]    __FlashArg=0x00000000
[09:09:07.427]    __FlashOp=0x00000000
[09:09:07.429]    __Result=0x00000000
[09:09:07.429]    
[09:09:07.429]    // User-defined
[09:09:07.429]    DbgMCU_CR=0x00000007
[09:09:07.429]    DbgMCU_APB1_Fz=0x00000000
[09:09:07.429]    DbgMCU_APB2_Fz=0x00000000
[09:09:07.429]    DoOptionByteLoading=0x00000000
[09:09:07.429]  </debugvars>
[09:09:07.430]  
[09:09:07.430]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:09:07.430]    <block atomic="false" info="">
[09:09:07.430]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:09:07.431]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:09:07.431]    </block>
[09:09:07.432]    <block atomic="false" info="DbgMCU registers">
[09:09:07.432]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:09:07.434]        // -> [Read32(0x40021034) => 0x00000200]   (__dp=0x00000000, __ap=0x00000000)
[09:09:07.435]        // -> [Write32(0x40021034, 0x00400200)]   (__dp=0x00000000, __ap=0x00000000)
[09:09:07.435]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:09:07.436]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:09:07.436]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:09:07.437]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:09:07.437]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:09:07.438]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:09:07.438]    </block>
[09:09:07.438]  </sequence>
[09:09:07.438]  
[09:11:31.096]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:11:31.096]  
[09:11:31.097]  <debugvars>
[09:11:31.097]    // Pre-defined
[09:11:31.097]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:11:31.097]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[09:11:31.098]    __dp=0x00000000
[09:11:31.098]    __ap=0x00000000
[09:11:31.098]    __traceout=0x00000000      (Trace Disabled)
[09:11:31.098]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:11:31.098]    __FlashAddr=0x00000000
[09:11:31.100]    __FlashLen=0x00000000
[09:11:31.100]    __FlashArg=0x00000000
[09:11:31.100]    __FlashOp=0x00000000
[09:11:31.100]    __Result=0x00000000
[09:11:31.101]    
[09:11:31.101]    // User-defined
[09:11:31.101]    DbgMCU_CR=0x00000007
[09:11:31.101]    DbgMCU_APB1_Fz=0x00000000
[09:11:31.101]    DbgMCU_APB2_Fz=0x00000000
[09:11:31.101]    DoOptionByteLoading=0x00000000
[09:11:31.102]  </debugvars>
[09:11:31.102]  
[09:11:31.102]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:11:31.102]    <block atomic="false" info="">
[09:11:31.102]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:11:31.102]        // -> [connectionFlash <= 0x00000000]
[09:11:31.103]      __var FLASH_BASE = 0x40022000 ;
[09:11:31.103]        // -> [FLASH_BASE <= 0x40022000]
[09:11:31.103]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:11:31.103]        // -> [FLASH_CR <= 0x40022004]
[09:11:31.103]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:11:31.104]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:11:31.104]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:11:31.104]        // -> [LOCK_BIT <= 0x00000001]
[09:11:31.104]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:11:31.104]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:11:31.105]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:11:31.105]        // -> [FLASH_KEYR <= 0x4002200C]
[09:11:31.105]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:11:31.105]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:11:31.105]      __var FLASH_KEY2 = 0x02030405 ;
[09:11:31.105]        // -> [FLASH_KEY2 <= 0x02030405]
[09:11:31.106]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:11:31.106]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:11:31.106]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:11:31.106]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:11:31.106]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:11:31.107]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:11:31.107]      __var FLASH_CR_Value = 0 ;
[09:11:31.107]        // -> [FLASH_CR_Value <= 0x00000000]
[09:11:31.107]      __var DoDebugPortStop = 1 ;
[09:11:31.107]        // -> [DoDebugPortStop <= 0x00000001]
[09:11:31.108]      __var DP_CTRL_STAT = 0x4 ;
[09:11:31.108]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:11:31.108]      __var DP_SELECT = 0x8 ;
[09:11:31.108]        // -> [DP_SELECT <= 0x00000008]
[09:11:31.108]    </block>
[09:11:31.109]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:11:31.109]      // if-block "connectionFlash && DoOptionByteLoading"
[09:11:31.109]        // =>  FALSE
[09:11:31.109]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:11:31.109]    </control>
[09:11:31.110]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:11:31.110]      // if-block "DoDebugPortStop"
[09:11:31.110]        // =>  TRUE
[09:11:31.110]      <block atomic="false" info="">
[09:11:31.110]        WriteDP(DP_SELECT, 0x00000000);
[09:11:31.111]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:11:31.111]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:11:31.112]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:11:31.112]      </block>
[09:11:31.112]      // end if-block "DoDebugPortStop"
[09:11:31.112]    </control>
[09:11:31.112]  </sequence>
[09:11:31.113]  
[09:16:49.530]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:16:49.530]  
[09:16:49.530]  <debugvars>
[09:16:49.532]    // Pre-defined
[09:16:49.532]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:16:49.532]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:16:49.533]    __dp=0x00000000
[09:16:49.533]    __ap=0x00000000
[09:16:49.533]    __traceout=0x00000000      (Trace Disabled)
[09:16:49.534]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:16:49.534]    __FlashAddr=0x00000000
[09:16:49.534]    __FlashLen=0x00000000
[09:16:49.534]    __FlashArg=0x00000000
[09:16:49.535]    __FlashOp=0x00000000
[09:16:49.535]    __Result=0x00000000
[09:16:49.535]    
[09:16:49.535]    // User-defined
[09:16:49.535]    DbgMCU_CR=0x00000007
[09:16:49.535]    DbgMCU_APB1_Fz=0x00000000
[09:16:49.535]    DbgMCU_APB2_Fz=0x00000000
[09:16:49.535]    DoOptionByteLoading=0x00000000
[09:16:49.535]  </debugvars>
[09:16:49.536]  
[09:16:49.536]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:16:49.537]    <block atomic="false" info="">
[09:16:49.537]      Sequence("CheckID");
[09:16:49.538]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:16:49.538]          <block atomic="false" info="">
[09:16:49.538]            __var pidr1 = 0;
[09:16:49.538]              // -> [pidr1 <= 0x00000000]
[09:16:49.538]            __var pidr2 = 0;
[09:16:49.539]              // -> [pidr2 <= 0x00000000]
[09:16:49.539]            __var jep106id = 0;
[09:16:49.539]              // -> [jep106id <= 0x00000000]
[09:16:49.539]            __var ROMTableBase = 0;
[09:16:49.539]              // -> [ROMTableBase <= 0x00000000]
[09:16:49.540]            __ap = 0;      // AHB-AP
[09:16:49.540]              // -> [__ap <= 0x00000000]
[09:16:49.540]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:16:49.541]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:16:49.541]              // -> [ROMTableBase <= 0xF0000000]
[09:16:49.541]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:16:49.543]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:16:49.543]              // -> [pidr1 <= 0x00000004]
[09:16:49.543]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:16:49.545]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:16:49.545]              // -> [pidr2 <= 0x0000000A]
[09:16:49.545]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:16:49.545]              // -> [jep106id <= 0x00000020]
[09:16:49.546]          </block>
[09:16:49.546]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:16:49.546]            // if-block "jep106id != 0x20"
[09:16:49.546]              // =>  FALSE
[09:16:49.546]            // skip if-block "jep106id != 0x20"
[09:16:49.547]          </control>
[09:16:49.547]        </sequence>
[09:16:49.547]    </block>
[09:16:49.547]  </sequence>
[09:16:49.547]  
[09:16:49.562]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:16:49.562]  
[09:16:49.572]  <debugvars>
[09:16:49.573]    // Pre-defined
[09:16:49.573]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:16:49.574]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:16:49.574]    __dp=0x00000000
[09:16:49.575]    __ap=0x00000000
[09:16:49.575]    __traceout=0x00000000      (Trace Disabled)
[09:16:49.576]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:16:49.576]    __FlashAddr=0x00000000
[09:16:49.577]    __FlashLen=0x00000000
[09:16:49.577]    __FlashArg=0x00000000
[09:16:49.578]    __FlashOp=0x00000000
[09:16:49.578]    __Result=0x00000000
[09:16:49.579]    
[09:16:49.579]    // User-defined
[09:16:49.579]    DbgMCU_CR=0x00000007
[09:16:49.580]    DbgMCU_APB1_Fz=0x00000000
[09:16:49.580]    DbgMCU_APB2_Fz=0x00000000
[09:16:49.581]    DoOptionByteLoading=0x00000000
[09:16:49.581]  </debugvars>
[09:16:49.581]  
[09:16:49.581]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:16:49.581]    <block atomic="false" info="">
[09:16:49.582]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:16:49.584]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:16:49.584]    </block>
[09:16:49.584]    <block atomic="false" info="DbgMCU registers">
[09:16:49.585]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:16:49.586]        // -> [Read32(0x40021034) => 0x00000200]   (__dp=0x00000000, __ap=0x00000000)
[09:16:49.587]        // -> [Write32(0x40021034, 0x00400200)]   (__dp=0x00000000, __ap=0x00000000)
[09:16:49.587]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:16:49.588]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:16:49.588]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:16:49.590]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:16:49.590]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:16:49.591]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:16:49.591]    </block>
[09:16:49.592]  </sequence>
[09:16:49.592]  
[09:16:57.676]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:16:57.676]  
[09:16:57.725]  <debugvars>
[09:16:57.727]    // Pre-defined
[09:16:57.727]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:16:57.728]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:16:57.729]    __dp=0x00000000
[09:16:57.729]    __ap=0x00000000
[09:16:57.730]    __traceout=0x00000000      (Trace Disabled)
[09:16:57.731]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:16:57.732]    __FlashAddr=0x00000000
[09:16:57.732]    __FlashLen=0x00000000
[09:16:57.733]    __FlashArg=0x00000000
[09:16:57.734]    __FlashOp=0x00000000
[09:16:57.735]    __Result=0x00000000
[09:16:57.736]    
[09:16:57.736]    // User-defined
[09:16:57.737]    DbgMCU_CR=0x00000007
[09:16:57.737]    DbgMCU_APB1_Fz=0x00000000
[09:16:57.738]    DbgMCU_APB2_Fz=0x00000000
[09:16:57.739]    DoOptionByteLoading=0x00000000
[09:16:57.740]  </debugvars>
[09:16:57.740]  
[09:16:57.741]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:16:57.741]    <block atomic="false" info="">
[09:16:57.742]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:16:57.742]        // -> [connectionFlash <= 0x00000001]
[09:16:57.743]      __var FLASH_BASE = 0x40022000 ;
[09:16:57.744]        // -> [FLASH_BASE <= 0x40022000]
[09:16:57.744]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:16:57.745]        // -> [FLASH_CR <= 0x40022004]
[09:16:57.746]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:16:57.746]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:16:57.747]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:16:57.748]        // -> [LOCK_BIT <= 0x00000001]
[09:16:57.748]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:16:57.748]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:16:57.750]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:16:57.750]        // -> [FLASH_KEYR <= 0x4002200C]
[09:16:57.750]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:16:57.750]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:16:57.751]      __var FLASH_KEY2 = 0x02030405 ;
[09:16:57.751]        // -> [FLASH_KEY2 <= 0x02030405]
[09:16:57.752]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:16:57.752]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:16:57.752]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:16:57.752]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:16:57.752]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:16:57.753]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:16:57.753]      __var FLASH_CR_Value = 0 ;
[09:16:57.753]        // -> [FLASH_CR_Value <= 0x00000000]
[09:16:57.753]      __var DoDebugPortStop = 1 ;
[09:16:57.754]        // -> [DoDebugPortStop <= 0x00000001]
[09:16:57.754]      __var DP_CTRL_STAT = 0x4 ;
[09:16:57.754]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:16:57.754]      __var DP_SELECT = 0x8 ;
[09:16:57.755]        // -> [DP_SELECT <= 0x00000008]
[09:16:57.755]    </block>
[09:16:57.755]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:16:57.755]      // if-block "connectionFlash && DoOptionByteLoading"
[09:16:57.757]        // =>  FALSE
[09:16:57.757]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:16:57.757]    </control>
[09:16:57.757]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:16:57.757]      // if-block "DoDebugPortStop"
[09:16:57.758]        // =>  TRUE
[09:16:57.758]      <block atomic="false" info="">
[09:16:57.758]        WriteDP(DP_SELECT, 0x00000000);
[09:16:57.759]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:16:57.759]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:16:57.759]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:16:57.760]      </block>
[09:16:57.760]      // end if-block "DoDebugPortStop"
[09:16:57.760]    </control>
[09:16:57.760]  </sequence>
[09:16:57.760]  
[09:24:26.220]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:24:26.220]  
[09:24:26.221]  <debugvars>
[09:24:26.221]    // Pre-defined
[09:24:26.221]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:24:26.221]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:24:26.222]    __dp=0x00000000
[09:24:26.222]    __ap=0x00000000
[09:24:26.222]    __traceout=0x00000000      (Trace Disabled)
[09:24:26.222]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:24:26.222]    __FlashAddr=0x00000000
[09:24:26.222]    __FlashLen=0x00000000
[09:24:26.223]    __FlashArg=0x00000000
[09:24:26.224]    __FlashOp=0x00000000
[09:24:26.224]    __Result=0x00000000
[09:24:26.224]    
[09:24:26.224]    // User-defined
[09:24:26.224]    DbgMCU_CR=0x00000007
[09:24:26.225]    DbgMCU_APB1_Fz=0x00000000
[09:24:26.225]    DbgMCU_APB2_Fz=0x00000000
[09:24:26.225]    DoOptionByteLoading=0x00000000
[09:24:26.225]  </debugvars>
[09:24:26.225]  
[09:24:26.226]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:24:26.226]    <block atomic="false" info="">
[09:24:26.226]      Sequence("CheckID");
[09:24:26.226]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:24:26.226]          <block atomic="false" info="">
[09:24:26.226]            __var pidr1 = 0;
[09:24:26.227]              // -> [pidr1 <= 0x00000000]
[09:24:26.227]            __var pidr2 = 0;
[09:24:26.227]              // -> [pidr2 <= 0x00000000]
[09:24:26.227]            __var jep106id = 0;
[09:24:26.227]              // -> [jep106id <= 0x00000000]
[09:24:26.228]            __var ROMTableBase = 0;
[09:24:26.228]              // -> [ROMTableBase <= 0x00000000]
[09:24:26.228]            __ap = 0;      // AHB-AP
[09:24:26.228]              // -> [__ap <= 0x00000000]
[09:24:26.228]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:24:26.229]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:24:26.229]              // -> [ROMTableBase <= 0xF0000000]
[09:24:26.230]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:24:26.231]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:24:26.232]              // -> [pidr1 <= 0x00000004]
[09:24:26.232]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:24:26.233]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:24:26.233]              // -> [pidr2 <= 0x0000000A]
[09:24:26.233]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:24:26.234]              // -> [jep106id <= 0x00000020]
[09:24:26.234]          </block>
[09:24:26.234]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:24:26.234]            // if-block "jep106id != 0x20"
[09:24:26.234]              // =>  FALSE
[09:24:26.234]            // skip if-block "jep106id != 0x20"
[09:24:26.235]          </control>
[09:24:26.235]        </sequence>
[09:24:26.235]    </block>
[09:24:26.235]  </sequence>
[09:24:26.235]  
[09:24:26.248]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:24:26.248]  
[09:24:26.248]  <debugvars>
[09:24:26.248]    // Pre-defined
[09:24:26.249]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:24:26.249]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:24:26.249]    __dp=0x00000000
[09:24:26.250]    __ap=0x00000000
[09:24:26.250]    __traceout=0x00000000      (Trace Disabled)
[09:24:26.251]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:24:26.251]    __FlashAddr=0x00000000
[09:24:26.251]    __FlashLen=0x00000000
[09:24:26.251]    __FlashArg=0x00000000
[09:24:26.251]    __FlashOp=0x00000000
[09:24:26.252]    __Result=0x00000000
[09:24:26.252]    
[09:24:26.252]    // User-defined
[09:24:26.252]    DbgMCU_CR=0x00000007
[09:24:26.252]    DbgMCU_APB1_Fz=0x00000000
[09:24:26.252]    DbgMCU_APB2_Fz=0x00000000
[09:24:26.252]    DoOptionByteLoading=0x00000000
[09:24:26.252]  </debugvars>
[09:24:26.252]  
[09:24:26.252]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:24:26.253]    <block atomic="false" info="">
[09:24:26.253]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:24:26.255]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:24:26.255]    </block>
[09:24:26.255]    <block atomic="false" info="DbgMCU registers">
[09:24:26.256]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:24:26.256]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[09:24:26.257]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[09:24:26.258]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:24:26.258]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:24:26.259]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:24:26.259]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:24:26.260]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:24:26.260]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:24:26.261]    </block>
[09:24:26.261]  </sequence>
[09:24:26.261]  
[09:24:34.580]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:24:34.580]  
[09:24:34.581]  <debugvars>
[09:24:34.581]    // Pre-defined
[09:24:34.582]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:24:34.583]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:24:34.583]    __dp=0x00000000
[09:24:34.584]    __ap=0x00000000
[09:24:34.584]    __traceout=0x00000000      (Trace Disabled)
[09:24:34.585]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:24:34.585]    __FlashAddr=0x00000000
[09:24:34.586]    __FlashLen=0x00000000
[09:24:34.586]    __FlashArg=0x00000000
[09:24:34.587]    __FlashOp=0x00000000
[09:24:34.587]    __Result=0x00000000
[09:24:34.588]    
[09:24:34.588]    // User-defined
[09:24:34.588]    DbgMCU_CR=0x00000007
[09:24:34.589]    DbgMCU_APB1_Fz=0x00000000
[09:24:34.589]    DbgMCU_APB2_Fz=0x00000000
[09:24:34.590]    DoOptionByteLoading=0x00000000
[09:24:34.590]  </debugvars>
[09:24:34.591]  
[09:24:34.591]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:24:34.592]    <block atomic="false" info="">
[09:24:34.592]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:24:34.593]        // -> [connectionFlash <= 0x00000001]
[09:24:34.593]      __var FLASH_BASE = 0x40022000 ;
[09:24:34.593]        // -> [FLASH_BASE <= 0x40022000]
[09:24:34.593]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:24:34.594]        // -> [FLASH_CR <= 0x40022004]
[09:24:34.594]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:24:34.594]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:24:34.595]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:24:34.595]        // -> [LOCK_BIT <= 0x00000001]
[09:24:34.595]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:24:34.596]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:24:34.596]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:24:34.596]        // -> [FLASH_KEYR <= 0x4002200C]
[09:24:34.597]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:24:34.597]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:24:34.597]      __var FLASH_KEY2 = 0x02030405 ;
[09:24:34.598]        // -> [FLASH_KEY2 <= 0x02030405]
[09:24:34.598]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:24:34.598]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:24:34.599]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:24:34.599]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:24:34.599]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:24:34.600]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:24:34.600]      __var FLASH_CR_Value = 0 ;
[09:24:34.600]        // -> [FLASH_CR_Value <= 0x00000000]
[09:24:34.601]      __var DoDebugPortStop = 1 ;
[09:24:34.601]        // -> [DoDebugPortStop <= 0x00000001]
[09:24:34.601]      __var DP_CTRL_STAT = 0x4 ;
[09:24:34.602]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:24:34.602]      __var DP_SELECT = 0x8 ;
[09:24:34.602]        // -> [DP_SELECT <= 0x00000008]
[09:24:34.603]    </block>
[09:24:34.603]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:24:34.603]      // if-block "connectionFlash && DoOptionByteLoading"
[09:24:34.604]        // =>  FALSE
[09:24:34.604]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:24:34.604]    </control>
[09:24:34.605]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:24:34.605]      // if-block "DoDebugPortStop"
[09:24:34.606]        // =>  TRUE
[09:24:34.607]      <block atomic="false" info="">
[09:24:34.607]        WriteDP(DP_SELECT, 0x00000000);
[09:24:34.607]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:24:34.608]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:24:34.608]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:24:34.609]      </block>
[09:24:34.609]      // end if-block "DoDebugPortStop"
[09:24:34.609]    </control>
[09:24:34.610]  </sequence>
[09:24:34.610]  
[09:51:52.915]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:51:52.915]  
[09:51:52.916]  <debugvars>
[09:51:52.916]    // Pre-defined
[09:51:52.916]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:51:52.916]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:51:52.917]    __dp=0x00000000
[09:51:52.918]    __ap=0x00000000
[09:51:52.918]    __traceout=0x00000000      (Trace Disabled)
[09:51:52.919]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:51:52.919]    __FlashAddr=0x00000000
[09:51:52.920]    __FlashLen=0x00000000
[09:51:52.920]    __FlashArg=0x00000000
[09:51:52.920]    __FlashOp=0x00000000
[09:51:52.920]    __Result=0x00000000
[09:51:52.921]    
[09:51:52.921]    // User-defined
[09:51:52.921]    DbgMCU_CR=0x00000007
[09:51:52.921]    DbgMCU_APB1_Fz=0x00000000
[09:51:52.922]    DbgMCU_APB2_Fz=0x00000000
[09:51:52.922]    DoOptionByteLoading=0x00000000
[09:51:52.922]  </debugvars>
[09:51:52.922]  
[09:51:52.923]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:51:52.923]    <block atomic="false" info="">
[09:51:52.923]      Sequence("CheckID");
[09:51:52.924]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:51:52.924]          <block atomic="false" info="">
[09:51:52.924]            __var pidr1 = 0;
[09:51:52.924]              // -> [pidr1 <= 0x00000000]
[09:51:52.924]            __var pidr2 = 0;
[09:51:52.925]              // -> [pidr2 <= 0x00000000]
[09:51:52.925]            __var jep106id = 0;
[09:51:52.925]              // -> [jep106id <= 0x00000000]
[09:51:52.925]            __var ROMTableBase = 0;
[09:51:52.926]              // -> [ROMTableBase <= 0x00000000]
[09:51:52.926]            __ap = 0;      // AHB-AP
[09:51:52.926]              // -> [__ap <= 0x00000000]
[09:51:52.926]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:51:52.927]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:51:52.927]              // -> [ROMTableBase <= 0xF0000000]
[09:51:52.927]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:51:52.929]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:51:52.930]              // -> [pidr1 <= 0x00000004]
[09:51:52.930]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:51:52.931]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:51:52.931]              // -> [pidr2 <= 0x0000000A]
[09:51:52.931]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:51:52.932]              // -> [jep106id <= 0x00000020]
[09:51:52.932]          </block>
[09:51:52.932]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:51:52.933]            // if-block "jep106id != 0x20"
[09:51:52.933]              // =>  FALSE
[09:51:52.933]            // skip if-block "jep106id != 0x20"
[09:51:52.933]          </control>
[09:51:52.933]        </sequence>
[09:51:52.934]    </block>
[09:51:52.934]  </sequence>
[09:51:52.934]  
[09:51:52.946]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:51:52.946]  
[09:51:52.968]  <debugvars>
[09:51:52.968]    // Pre-defined
[09:51:52.969]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:51:52.969]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:51:52.970]    __dp=0x00000000
[09:51:52.970]    __ap=0x00000000
[09:51:52.970]    __traceout=0x00000000      (Trace Disabled)
[09:51:52.971]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:51:52.971]    __FlashAddr=0x00000000
[09:51:52.972]    __FlashLen=0x00000000
[09:51:52.972]    __FlashArg=0x00000000
[09:51:52.972]    __FlashOp=0x00000000
[09:51:52.973]    __Result=0x00000000
[09:51:52.973]    
[09:51:52.973]    // User-defined
[09:51:52.973]    DbgMCU_CR=0x00000007
[09:51:52.974]    DbgMCU_APB1_Fz=0x00000000
[09:51:52.974]    DbgMCU_APB2_Fz=0x00000000
[09:51:52.974]    DoOptionByteLoading=0x00000000
[09:51:52.974]  </debugvars>
[09:51:52.975]  
[09:51:52.975]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:51:52.975]    <block atomic="false" info="">
[09:51:52.975]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:51:52.976]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:51:52.976]    </block>
[09:51:52.977]    <block atomic="false" info="DbgMCU registers">
[09:51:52.977]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:51:52.978]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[09:51:52.979]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[09:51:52.980]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:51:52.981]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:51:52.981]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:51:52.982]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:51:52.983]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:51:52.984]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:51:52.984]    </block>
[09:51:52.984]  </sequence>
[09:51:52.984]  
[09:52:01.239]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:52:01.239]  
[09:52:01.240]  <debugvars>
[09:52:01.241]    // Pre-defined
[09:52:01.241]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:52:01.242]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:52:01.242]    __dp=0x00000000
[09:52:01.242]    __ap=0x00000000
[09:52:01.242]    __traceout=0x00000000      (Trace Disabled)
[09:52:01.242]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:52:01.243]    __FlashAddr=0x00000000
[09:52:01.243]    __FlashLen=0x00000000
[09:52:01.243]    __FlashArg=0x00000000
[09:52:01.244]    __FlashOp=0x00000000
[09:52:01.245]    __Result=0x00000000
[09:52:01.245]    
[09:52:01.245]    // User-defined
[09:52:01.245]    DbgMCU_CR=0x00000007
[09:52:01.246]    DbgMCU_APB1_Fz=0x00000000
[09:52:01.246]    DbgMCU_APB2_Fz=0x00000000
[09:52:01.246]    DoOptionByteLoading=0x00000000
[09:52:01.247]  </debugvars>
[09:52:01.247]  
[09:52:01.247]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:52:01.248]    <block atomic="false" info="">
[09:52:01.248]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:52:01.248]        // -> [connectionFlash <= 0x00000001]
[09:52:01.248]      __var FLASH_BASE = 0x40022000 ;
[09:52:01.248]        // -> [FLASH_BASE <= 0x40022000]
[09:52:01.249]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:52:01.249]        // -> [FLASH_CR <= 0x40022004]
[09:52:01.250]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:52:01.250]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:52:01.250]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:52:01.250]        // -> [LOCK_BIT <= 0x00000001]
[09:52:01.250]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:52:01.250]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:52:01.251]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:52:01.251]        // -> [FLASH_KEYR <= 0x4002200C]
[09:52:01.251]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:52:01.251]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:52:01.251]      __var FLASH_KEY2 = 0x02030405 ;
[09:52:01.251]        // -> [FLASH_KEY2 <= 0x02030405]
[09:52:01.251]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:52:01.251]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:52:01.252]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:52:01.253]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:52:01.253]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:52:01.253]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:52:01.254]      __var FLASH_CR_Value = 0 ;
[09:52:01.254]        // -> [FLASH_CR_Value <= 0x00000000]
[09:52:01.254]      __var DoDebugPortStop = 1 ;
[09:52:01.254]        // -> [DoDebugPortStop <= 0x00000001]
[09:52:01.254]      __var DP_CTRL_STAT = 0x4 ;
[09:52:01.254]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:52:01.255]      __var DP_SELECT = 0x8 ;
[09:52:01.255]        // -> [DP_SELECT <= 0x00000008]
[09:52:01.255]    </block>
[09:52:01.255]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:52:01.255]      // if-block "connectionFlash && DoOptionByteLoading"
[09:52:01.256]        // =>  FALSE
[09:52:01.256]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:52:01.256]    </control>
[09:52:01.256]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:52:01.257]      // if-block "DoDebugPortStop"
[09:52:01.257]        // =>  TRUE
[09:52:01.257]      <block atomic="false" info="">
[09:52:01.257]        WriteDP(DP_SELECT, 0x00000000);
[09:52:01.258]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:52:01.258]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:52:01.258]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:52:01.259]      </block>
[09:52:01.259]      // end if-block "DoDebugPortStop"
[09:52:01.259]    </control>
[09:52:01.259]  </sequence>
[09:52:01.260]  
[09:55:01.739]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:55:01.739]  
[09:55:01.739]  <debugvars>
[09:55:01.740]    // Pre-defined
[09:55:01.740]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:55:01.740]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:55:01.740]    __dp=0x00000000
[09:55:01.741]    __ap=0x00000000
[09:55:01.741]    __traceout=0x00000000      (Trace Disabled)
[09:55:01.741]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:55:01.741]    __FlashAddr=0x00000000
[09:55:01.741]    __FlashLen=0x00000000
[09:55:01.742]    __FlashArg=0x00000000
[09:55:01.742]    __FlashOp=0x00000000
[09:55:01.743]    __Result=0x00000000
[09:55:01.743]    
[09:55:01.743]    // User-defined
[09:55:01.743]    DbgMCU_CR=0x00000007
[09:55:01.743]    DbgMCU_APB1_Fz=0x00000000
[09:55:01.744]    DbgMCU_APB2_Fz=0x00000000
[09:55:01.744]    DoOptionByteLoading=0x00000000
[09:55:01.744]  </debugvars>
[09:55:01.744]  
[09:55:01.745]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:55:01.745]    <block atomic="false" info="">
[09:55:01.745]      Sequence("CheckID");
[09:55:01.745]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:55:01.745]          <block atomic="false" info="">
[09:55:01.746]            __var pidr1 = 0;
[09:55:01.746]              // -> [pidr1 <= 0x00000000]
[09:55:01.746]            __var pidr2 = 0;
[09:55:01.746]              // -> [pidr2 <= 0x00000000]
[09:55:01.746]            __var jep106id = 0;
[09:55:01.746]              // -> [jep106id <= 0x00000000]
[09:55:01.747]            __var ROMTableBase = 0;
[09:55:01.747]              // -> [ROMTableBase <= 0x00000000]
[09:55:01.747]            __ap = 0;      // AHB-AP
[09:55:01.747]              // -> [__ap <= 0x00000000]
[09:55:01.747]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:55:01.748]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:55:01.748]              // -> [ROMTableBase <= 0xF0000000]
[09:55:01.749]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:55:01.750]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:55:01.750]              // -> [pidr1 <= 0x00000004]
[09:55:01.750]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:55:01.751]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:55:01.752]              // -> [pidr2 <= 0x0000000A]
[09:55:01.752]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:55:01.752]              // -> [jep106id <= 0x00000020]
[09:55:01.753]          </block>
[09:55:01.754]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:55:01.754]            // if-block "jep106id != 0x20"
[09:55:01.754]              // =>  FALSE
[09:55:01.754]            // skip if-block "jep106id != 0x20"
[09:55:01.754]          </control>
[09:55:01.755]        </sequence>
[09:55:01.755]    </block>
[09:55:01.755]  </sequence>
[09:55:01.755]  
[09:55:01.767]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:55:01.767]  
[09:55:01.775]  <debugvars>
[09:55:01.776]    // Pre-defined
[09:55:01.776]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:55:01.777]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:55:01.777]    __dp=0x00000000
[09:55:01.778]    __ap=0x00000000
[09:55:01.779]    __traceout=0x00000000      (Trace Disabled)
[09:55:01.779]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:55:01.779]    __FlashAddr=0x00000000
[09:55:01.780]    __FlashLen=0x00000000
[09:55:01.780]    __FlashArg=0x00000000
[09:55:01.780]    __FlashOp=0x00000000
[09:55:01.781]    __Result=0x00000000
[09:55:01.782]    
[09:55:01.782]    // User-defined
[09:55:01.782]    DbgMCU_CR=0x00000007
[09:55:01.783]    DbgMCU_APB1_Fz=0x00000000
[09:55:01.783]    DbgMCU_APB2_Fz=0x00000000
[09:55:01.783]    DoOptionByteLoading=0x00000000
[09:55:01.784]  </debugvars>
[09:55:01.784]  
[09:55:01.785]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:55:01.785]    <block atomic="false" info="">
[09:55:01.785]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:55:01.786]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:55:01.787]    </block>
[09:55:01.787]    <block atomic="false" info="DbgMCU registers">
[09:55:01.787]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:55:01.788]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[09:55:01.789]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[09:55:01.790]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:55:01.791]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:55:01.791]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:55:01.792]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:55:01.792]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:55:01.793]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:55:01.793]    </block>
[09:55:01.793]  </sequence>
[09:55:01.793]  
[09:55:10.184]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:55:10.184]  
[09:55:10.185]  <debugvars>
[09:55:10.185]    // Pre-defined
[09:55:10.185]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:55:10.186]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:55:10.186]    __dp=0x00000000
[09:55:10.186]    __ap=0x00000000
[09:55:10.186]    __traceout=0x00000000      (Trace Disabled)
[09:55:10.188]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:55:10.188]    __FlashAddr=0x00000000
[09:55:10.188]    __FlashLen=0x00000000
[09:55:10.188]    __FlashArg=0x00000000
[09:55:10.188]    __FlashOp=0x00000000
[09:55:10.189]    __Result=0x00000000
[09:55:10.189]    
[09:55:10.189]    // User-defined
[09:55:10.189]    DbgMCU_CR=0x00000007
[09:55:10.189]    DbgMCU_APB1_Fz=0x00000000
[09:55:10.189]    DbgMCU_APB2_Fz=0x00000000
[09:55:10.190]    DoOptionByteLoading=0x00000000
[09:55:10.190]  </debugvars>
[09:55:10.190]  
[09:55:10.190]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:55:10.191]    <block atomic="false" info="">
[09:55:10.191]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:55:10.191]        // -> [connectionFlash <= 0x00000001]
[09:55:10.191]      __var FLASH_BASE = 0x40022000 ;
[09:55:10.191]        // -> [FLASH_BASE <= 0x40022000]
[09:55:10.193]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:55:10.193]        // -> [FLASH_CR <= 0x40022004]
[09:55:10.193]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:55:10.193]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:55:10.194]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:55:10.194]        // -> [LOCK_BIT <= 0x00000001]
[09:55:10.194]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:55:10.194]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:55:10.194]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:55:10.195]        // -> [FLASH_KEYR <= 0x4002200C]
[09:55:10.195]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:55:10.195]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:55:10.195]      __var FLASH_KEY2 = 0x02030405 ;
[09:55:10.195]        // -> [FLASH_KEY2 <= 0x02030405]
[09:55:10.195]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:55:10.195]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:55:10.196]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:55:10.196]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:55:10.196]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:55:10.196]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:55:10.196]      __var FLASH_CR_Value = 0 ;
[09:55:10.197]        // -> [FLASH_CR_Value <= 0x00000000]
[09:55:10.197]      __var DoDebugPortStop = 1 ;
[09:55:10.197]        // -> [DoDebugPortStop <= 0x00000001]
[09:55:10.198]      __var DP_CTRL_STAT = 0x4 ;
[09:55:10.198]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:55:10.198]      __var DP_SELECT = 0x8 ;
[09:55:10.198]        // -> [DP_SELECT <= 0x00000008]
[09:55:10.199]    </block>
[09:55:10.199]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:55:10.199]      // if-block "connectionFlash && DoOptionByteLoading"
[09:55:10.200]        // =>  FALSE
[09:55:10.200]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:55:10.200]    </control>
[09:55:10.201]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:55:10.201]      // if-block "DoDebugPortStop"
[09:55:10.201]        // =>  TRUE
[09:55:10.201]      <block atomic="false" info="">
[09:55:10.202]        WriteDP(DP_SELECT, 0x00000000);
[09:55:10.202]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:55:10.203]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:55:10.203]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:55:10.203]      </block>
[09:55:10.203]      // end if-block "DoDebugPortStop"
[09:55:10.204]    </control>
[09:55:10.204]  </sequence>
[09:55:10.204]  
[10:05:38.388]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:05:38.388]  
[10:05:38.388]  <debugvars>
[10:05:38.388]    // Pre-defined
[10:05:38.389]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:05:38.389]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:05:38.389]    __dp=0x00000000
[10:05:38.390]    __ap=0x00000000
[10:05:38.390]    __traceout=0x00000000      (Trace Disabled)
[10:05:38.390]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:05:38.390]    __FlashAddr=0x00000000
[10:05:38.391]    __FlashLen=0x00000000
[10:05:38.391]    __FlashArg=0x00000000
[10:05:38.391]    __FlashOp=0x00000000
[10:05:38.392]    __Result=0x00000000
[10:05:38.392]    
[10:05:38.392]    // User-defined
[10:05:38.392]    DbgMCU_CR=0x00000007
[10:05:38.392]    DbgMCU_APB1_Fz=0x00000000
[10:05:38.392]    DbgMCU_APB2_Fz=0x00000000
[10:05:38.392]    DoOptionByteLoading=0x00000000
[10:05:38.392]  </debugvars>
[10:05:38.392]  
[10:05:38.392]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:05:38.393]    <block atomic="false" info="">
[10:05:38.393]      Sequence("CheckID");
[10:05:38.393]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:05:38.393]          <block atomic="false" info="">
[10:05:38.393]            __var pidr1 = 0;
[10:05:38.394]              // -> [pidr1 <= 0x00000000]
[10:05:38.394]            __var pidr2 = 0;
[10:05:38.395]              // -> [pidr2 <= 0x00000000]
[10:05:38.395]            __var jep106id = 0;
[10:05:38.395]              // -> [jep106id <= 0x00000000]
[10:05:38.396]            __var ROMTableBase = 0;
[10:05:38.396]              // -> [ROMTableBase <= 0x00000000]
[10:05:38.396]            __ap = 0;      // AHB-AP
[10:05:38.396]              // -> [__ap <= 0x00000000]
[10:05:38.397]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:05:38.397]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:05:38.397]              // -> [ROMTableBase <= 0xF0000000]
[10:05:38.397]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:05:38.398]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:05:38.399]              // -> [pidr1 <= 0x00000004]
[10:05:38.399]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:05:38.399]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:05:38.400]              // -> [pidr2 <= 0x0000000A]
[10:05:38.400]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:05:38.400]              // -> [jep106id <= 0x00000020]
[10:05:38.400]          </block>
[10:05:38.401]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:05:38.401]            // if-block "jep106id != 0x20"
[10:05:38.402]              // =>  FALSE
[10:05:38.402]            // skip if-block "jep106id != 0x20"
[10:05:38.402]          </control>
[10:05:38.403]        </sequence>
[10:05:38.404]    </block>
[10:05:38.404]  </sequence>
[10:05:38.404]  
[10:05:38.416]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:05:38.416]  
[10:05:38.429]  <debugvars>
[10:05:38.430]    // Pre-defined
[10:05:38.430]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:05:38.430]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:05:38.430]    __dp=0x00000000
[10:05:38.431]    __ap=0x00000000
[10:05:38.431]    __traceout=0x00000000      (Trace Disabled)
[10:05:38.431]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:05:38.431]    __FlashAddr=0x00000000
[10:05:38.432]    __FlashLen=0x00000000
[10:05:38.432]    __FlashArg=0x00000000
[10:05:38.432]    __FlashOp=0x00000000
[10:05:38.433]    __Result=0x00000000
[10:05:38.433]    
[10:05:38.433]    // User-defined
[10:05:38.433]    DbgMCU_CR=0x00000007
[10:05:38.434]    DbgMCU_APB1_Fz=0x00000000
[10:05:38.434]    DbgMCU_APB2_Fz=0x00000000
[10:05:38.434]    DoOptionByteLoading=0x00000000
[10:05:38.434]  </debugvars>
[10:05:38.434]  
[10:05:38.435]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:05:38.435]    <block atomic="false" info="">
[10:05:38.435]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:05:38.436]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:05:38.436]    </block>
[10:05:38.436]    <block atomic="false" info="DbgMCU registers">
[10:05:38.436]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:05:38.437]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[10:05:38.438]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[10:05:38.438]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:05:38.439]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:05:38.439]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:05:38.440]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:05:38.440]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:05:38.441]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:05:38.441]    </block>
[10:05:38.441]  </sequence>
[10:05:38.442]  
[10:05:46.358]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:05:46.358]  
[10:05:46.358]  <debugvars>
[10:05:46.359]    // Pre-defined
[10:05:46.359]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:05:46.359]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:05:46.359]    __dp=0x00000000
[10:05:46.360]    __ap=0x00000000
[10:05:46.360]    __traceout=0x00000000      (Trace Disabled)
[10:05:46.360]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:05:46.362]    __FlashAddr=0x00000000
[10:05:46.362]    __FlashLen=0x00000000
[10:05:46.362]    __FlashArg=0x00000000
[10:05:46.363]    __FlashOp=0x00000000
[10:05:46.363]    __Result=0x00000000
[10:05:46.363]    
[10:05:46.363]    // User-defined
[10:05:46.363]    DbgMCU_CR=0x00000007
[10:05:46.363]    DbgMCU_APB1_Fz=0x00000000
[10:05:46.363]    DbgMCU_APB2_Fz=0x00000000
[10:05:46.364]    DoOptionByteLoading=0x00000000
[10:05:46.364]  </debugvars>
[10:05:46.364]  
[10:05:46.364]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:05:46.365]    <block atomic="false" info="">
[10:05:46.365]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:05:46.365]        // -> [connectionFlash <= 0x00000001]
[10:05:46.365]      __var FLASH_BASE = 0x40022000 ;
[10:05:46.366]        // -> [FLASH_BASE <= 0x40022000]
[10:05:46.366]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:05:46.366]        // -> [FLASH_CR <= 0x40022004]
[10:05:46.367]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:05:46.367]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:05:46.367]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:05:46.367]        // -> [LOCK_BIT <= 0x00000001]
[10:05:46.368]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:05:46.368]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:05:46.368]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:05:46.368]        // -> [FLASH_KEYR <= 0x4002200C]
[10:05:46.369]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:05:46.369]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:05:46.369]      __var FLASH_KEY2 = 0x02030405 ;
[10:05:46.369]        // -> [FLASH_KEY2 <= 0x02030405]
[10:05:46.369]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:05:46.370]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:05:46.370]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:05:46.370]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:05:46.370]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:05:46.370]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:05:46.370]      __var FLASH_CR_Value = 0 ;
[10:05:46.371]        // -> [FLASH_CR_Value <= 0x00000000]
[10:05:46.371]      __var DoDebugPortStop = 1 ;
[10:05:46.371]        // -> [DoDebugPortStop <= 0x00000001]
[10:05:46.371]      __var DP_CTRL_STAT = 0x4 ;
[10:05:46.371]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:05:46.372]      __var DP_SELECT = 0x8 ;
[10:05:46.372]        // -> [DP_SELECT <= 0x00000008]
[10:05:46.372]    </block>
[10:05:46.372]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:05:46.372]      // if-block "connectionFlash && DoOptionByteLoading"
[10:05:46.373]        // =>  FALSE
[10:05:46.373]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:05:46.373]    </control>
[10:05:46.373]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:05:46.374]      // if-block "DoDebugPortStop"
[10:05:46.374]        // =>  TRUE
[10:05:46.374]      <block atomic="false" info="">
[10:05:46.374]        WriteDP(DP_SELECT, 0x00000000);
[10:05:46.374]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:05:46.376]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:05:46.376]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:05:46.376]      </block>
[10:05:46.377]      // end if-block "DoDebugPortStop"
[10:05:46.377]    </control>
[10:05:46.377]  </sequence>
[10:05:46.377]  
[10:07:15.551]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:07:15.551]  
[10:07:15.551]  <debugvars>
[10:07:15.551]    // Pre-defined
[10:07:15.552]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:07:15.552]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:07:15.552]    __dp=0x00000000
[10:07:15.553]    __ap=0x00000000
[10:07:15.553]    __traceout=0x00000000      (Trace Disabled)
[10:07:15.553]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:07:15.553]    __FlashAddr=0x00000000
[10:07:15.554]    __FlashLen=0x00000000
[10:07:15.554]    __FlashArg=0x00000000
[10:07:15.554]    __FlashOp=0x00000000
[10:07:15.555]    __Result=0x00000000
[10:07:15.555]    
[10:07:15.555]    // User-defined
[10:07:15.555]    DbgMCU_CR=0x00000007
[10:07:15.555]    DbgMCU_APB1_Fz=0x00000000
[10:07:15.556]    DbgMCU_APB2_Fz=0x00000000
[10:07:15.556]    DoOptionByteLoading=0x00000000
[10:07:15.556]  </debugvars>
[10:07:15.556]  
[10:07:15.556]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:07:15.557]    <block atomic="false" info="">
[10:07:15.557]      Sequence("CheckID");
[10:07:15.557]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:07:15.557]          <block atomic="false" info="">
[10:07:15.557]            __var pidr1 = 0;
[10:07:15.558]              // -> [pidr1 <= 0x00000000]
[10:07:15.558]            __var pidr2 = 0;
[10:07:15.558]              // -> [pidr2 <= 0x00000000]
[10:07:15.558]            __var jep106id = 0;
[10:07:15.558]              // -> [jep106id <= 0x00000000]
[10:07:15.558]            __var ROMTableBase = 0;
[10:07:15.559]              // -> [ROMTableBase <= 0x00000000]
[10:07:15.559]            __ap = 0;      // AHB-AP
[10:07:15.559]              // -> [__ap <= 0x00000000]
[10:07:15.559]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:07:15.560]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:07:15.560]              // -> [ROMTableBase <= 0xF0000000]
[10:07:15.560]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:07:15.562]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:07:15.562]              // -> [pidr1 <= 0x00000004]
[10:07:15.562]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:07:15.563]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:07:15.564]              // -> [pidr2 <= 0x0000000A]
[10:07:15.564]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:07:15.564]              // -> [jep106id <= 0x00000020]
[10:07:15.564]          </block>
[10:07:15.565]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:07:15.565]            // if-block "jep106id != 0x20"
[10:07:15.565]              // =>  FALSE
[10:07:15.565]            // skip if-block "jep106id != 0x20"
[10:07:15.566]          </control>
[10:07:15.566]        </sequence>
[10:07:15.566]    </block>
[10:07:15.566]  </sequence>
[10:07:15.567]  
[10:07:15.579]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:07:15.579]  
[10:07:15.587]  <debugvars>
[10:07:15.588]    // Pre-defined
[10:07:15.589]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:07:15.589]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:07:15.589]    __dp=0x00000000
[10:07:15.590]    __ap=0x00000000
[10:07:15.590]    __traceout=0x00000000      (Trace Disabled)
[10:07:15.591]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:07:15.591]    __FlashAddr=0x00000000
[10:07:15.592]    __FlashLen=0x00000000
[10:07:15.592]    __FlashArg=0x00000000
[10:07:15.593]    __FlashOp=0x00000000
[10:07:15.593]    __Result=0x00000000
[10:07:15.594]    
[10:07:15.594]    // User-defined
[10:07:15.594]    DbgMCU_CR=0x00000007
[10:07:15.595]    DbgMCU_APB1_Fz=0x00000000
[10:07:15.595]    DbgMCU_APB2_Fz=0x00000000
[10:07:15.595]    DoOptionByteLoading=0x00000000
[10:07:15.596]  </debugvars>
[10:07:15.596]  
[10:07:15.596]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:07:15.597]    <block atomic="false" info="">
[10:07:15.597]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:07:15.598]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:07:15.598]    </block>
[10:07:15.598]    <block atomic="false" info="DbgMCU registers">
[10:07:15.599]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:07:15.600]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[10:07:15.601]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[10:07:15.601]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:07:15.602]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:07:15.603]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:07:15.604]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:07:15.604]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:07:15.605]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:07:15.605]    </block>
[10:07:15.605]  </sequence>
[10:07:15.606]  
[10:07:52.899]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:07:52.899]  
[10:07:52.899]  <debugvars>
[10:07:52.900]    // Pre-defined
[10:07:52.900]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:07:52.900]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:07:52.900]    __dp=0x00000000
[10:07:52.902]    __ap=0x00000000
[10:07:52.902]    __traceout=0x00000000      (Trace Disabled)
[10:07:52.902]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:07:52.902]    __FlashAddr=0x00000000
[10:07:52.902]    __FlashLen=0x00000000
[10:07:52.902]    __FlashArg=0x00000000
[10:07:52.903]    __FlashOp=0x00000000
[10:07:52.903]    __Result=0x00000000
[10:07:52.903]    
[10:07:52.903]    // User-defined
[10:07:52.904]    DbgMCU_CR=0x00000007
[10:07:52.904]    DbgMCU_APB1_Fz=0x00000000
[10:07:52.905]    DbgMCU_APB2_Fz=0x00000000
[10:07:52.905]    DoOptionByteLoading=0x00000000
[10:07:52.906]  </debugvars>
[10:07:52.906]  
[10:07:52.906]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:07:52.906]    <block atomic="false" info="">
[10:07:52.907]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:07:52.907]        // -> [connectionFlash <= 0x00000000]
[10:07:52.907]      __var FLASH_BASE = 0x40022000 ;
[10:07:52.908]        // -> [FLASH_BASE <= 0x40022000]
[10:07:52.908]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:07:52.908]        // -> [FLASH_CR <= 0x40022004]
[10:07:52.908]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:07:52.908]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:07:52.908]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:07:52.909]        // -> [LOCK_BIT <= 0x00000001]
[10:07:52.909]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:07:52.909]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:07:52.909]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:07:52.910]        // -> [FLASH_KEYR <= 0x4002200C]
[10:07:52.910]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:07:52.910]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:07:52.910]      __var FLASH_KEY2 = 0x02030405 ;
[10:07:52.910]        // -> [FLASH_KEY2 <= 0x02030405]
[10:07:52.910]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:07:52.911]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:07:52.911]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:07:52.911]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:07:52.911]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:07:52.911]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:07:52.911]      __var FLASH_CR_Value = 0 ;
[10:07:52.913]        // -> [FLASH_CR_Value <= 0x00000000]
[10:07:52.913]      __var DoDebugPortStop = 1 ;
[10:07:52.913]        // -> [DoDebugPortStop <= 0x00000001]
[10:07:52.914]      __var DP_CTRL_STAT = 0x4 ;
[10:07:52.914]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:07:52.914]      __var DP_SELECT = 0x8 ;
[10:07:52.914]        // -> [DP_SELECT <= 0x00000008]
[10:07:52.915]    </block>
[10:07:52.915]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:07:52.915]      // if-block "connectionFlash && DoOptionByteLoading"
[10:07:52.916]        // =>  FALSE
[10:07:52.916]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:07:52.917]    </control>
[10:07:52.918]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:07:52.918]      // if-block "DoDebugPortStop"
[10:07:52.918]        // =>  TRUE
[10:07:52.918]      <block atomic="false" info="">
[10:07:52.919]        WriteDP(DP_SELECT, 0x00000000);
[10:07:52.919]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:07:52.920]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:07:52.920]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:07:52.921]      </block>
[10:07:52.921]      // end if-block "DoDebugPortStop"
[10:07:52.921]    </control>
[10:07:52.921]  </sequence>
[10:07:52.921]  
