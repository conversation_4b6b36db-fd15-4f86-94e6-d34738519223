#ifndef __NET_PROTOCOL_H__
#define __NET_PROTOCOL_H__

#include "system.h"
#include "main.h"

#define NET_CONFIG_SAVE_ADDRESS EEPROM_BANK2_ADDR

typedef struct
{
  uint32_t positive_cumulative_flow;//正累积流量十进制， 单位： 0.01 立方米
  uint32_t negative_cumulative_flow;//负累积流量十进制， 单位： 0.01 立方米
  uint32_t net_cumulative_flow_rate;//净累积流量十进制， 单位： 0.01 立方米
  uint32_t instantaneous_flow;//瞬时流量十进制， 单位： 0.01 立方米/小时
  uint16_t instantaneous_flow_rate;//瞬时流速十进制， 单位： 0.01 米/秒
  uint8_t net_sig;//4G信号
  uint16_t battery;//电池电量十进制， 单位： 0.01V
  uint32_t water_meter_number;//水表编号
  uint8_t warning;//空管报警
}UPLOAD_DATA;


void net_protocol_parsing(void);
/**
 * 心跳，定时上报数据
*/
uint8_t net_heartbeat(uint8_t central_station_address,UPLOAD_DATA data);

#endif
