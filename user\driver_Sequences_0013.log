/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : D:\2025work\STM32L072PRO\source-application\user\driver_Sequences_0013.log
 *  Created     : 08:13:34 (15/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : D:\2025work\STM32L072PRO\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[08:13:34.605]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:13:34.605]  
[08:13:34.617]  <debugvars>
[08:13:34.629]    // Pre-defined
[08:13:34.650]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:13:34.662]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:13:34.664]    __dp=0x00000000
[08:13:34.664]    __ap=0x00000000
[08:13:34.665]    __traceout=0x00000000      (Trace Disabled)
[08:13:34.666]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:13:34.666]    __FlashAddr=0x00000000
[08:13:34.667]    __FlashLen=0x00000000
[08:13:34.668]    __FlashArg=0x00000000
[08:13:34.668]    __FlashOp=0x00000000
[08:13:34.669]    __Result=0x00000000
[08:13:34.670]    
[08:13:34.670]    // User-defined
[08:13:34.671]    DbgMCU_CR=0x00000007
[08:13:34.672]    DbgMCU_APB1_Fz=0x00000000
[08:13:34.673]    DbgMCU_APB2_Fz=0x00000000
[08:13:34.673]    DoOptionByteLoading=0x00000000
[08:13:34.674]  </debugvars>
[08:13:34.675]  
[08:13:34.675]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:13:34.676]    <block atomic="false" info="">
[08:13:34.676]      Sequence("CheckID");
[08:13:34.677]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:13:34.678]          <block atomic="false" info="">
[08:13:34.678]            __var pidr1 = 0;
[08:13:34.679]              // -> [pidr1 <= 0x00000000]
[08:13:34.679]            __var pidr2 = 0;
[08:13:34.680]              // -> [pidr2 <= 0x00000000]
[08:13:34.680]            __var jep106id = 0;
[08:13:34.681]              // -> [jep106id <= 0x00000000]
[08:13:34.681]            __var ROMTableBase = 0;
[08:13:34.682]              // -> [ROMTableBase <= 0x00000000]
[08:13:34.682]            __ap = 0;      // AHB-AP
[08:13:34.682]              // -> [__ap <= 0x00000000]
[08:13:34.683]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:13:34.684]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:13:34.685]              // -> [ROMTableBase <= 0xF0000000]
[08:13:34.685]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:13:34.690]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:13:34.690]              // -> [pidr1 <= 0x00000004]
[08:13:34.691]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:13:34.692]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:13:34.693]              // -> [pidr2 <= 0x0000000A]
[08:13:34.693]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:13:34.694]              // -> [jep106id <= 0x00000020]
[08:13:34.694]          </block>
[08:13:34.695]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:13:34.695]            // if-block "jep106id != 0x20"
[08:13:34.696]              // =>  FALSE
[08:13:34.696]            // skip if-block "jep106id != 0x20"
[08:13:34.697]          </control>
[08:13:34.697]        </sequence>
[08:13:34.698]    </block>
[08:13:34.698]  </sequence>
[08:13:34.699]  
[08:13:34.711]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:13:34.711]  
[08:13:34.712]  <debugvars>
[08:13:34.713]    // Pre-defined
[08:13:34.713]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:13:34.713]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:13:34.714]    __dp=0x00000000
[08:13:34.714]    __ap=0x00000000
[08:13:34.714]    __traceout=0x00000000      (Trace Disabled)
[08:13:34.714]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:13:34.715]    __FlashAddr=0x00000000
[08:13:34.715]    __FlashLen=0x00000000
[08:13:34.716]    __FlashArg=0x00000000
[08:13:34.716]    __FlashOp=0x00000000
[08:13:34.716]    __Result=0x00000000
[08:13:34.717]    
[08:13:34.717]    // User-defined
[08:13:34.717]    DbgMCU_CR=0x00000007
[08:13:34.717]    DbgMCU_APB1_Fz=0x00000000
[08:13:34.717]    DbgMCU_APB2_Fz=0x00000000
[08:13:34.717]    DoOptionByteLoading=0x00000000
[08:13:34.718]  </debugvars>
[08:13:34.718]  
[08:13:34.718]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:13:34.718]    <block atomic="false" info="">
[08:13:34.718]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:13:34.719]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:13:34.720]    </block>
[08:13:34.720]    <block atomic="false" info="DbgMCU registers">
[08:13:34.720]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:13:34.720]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[08:13:34.723]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[08:13:34.723]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:13:34.724]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:13:34.724]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:13:34.725]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:13:34.725]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:13:34.726]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:13:34.726]    </block>
[08:13:34.727]  </sequence>
[08:13:34.727]  
[08:13:42.241]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[08:13:42.241]  
[08:13:42.257]  <debugvars>
[08:13:42.258]    // Pre-defined
[08:13:42.258]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:13:42.259]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:13:42.259]    __dp=0x00000000
[08:13:42.260]    __ap=0x00000000
[08:13:42.260]    __traceout=0x00000000      (Trace Disabled)
[08:13:42.261]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:13:42.261]    __FlashAddr=0x00000000
[08:13:42.261]    __FlashLen=0x00000000
[08:13:42.262]    __FlashArg=0x00000000
[08:13:42.262]    __FlashOp=0x00000000
[08:13:42.263]    __Result=0x00000000
[08:13:42.263]    
[08:13:42.263]    // User-defined
[08:13:42.264]    DbgMCU_CR=0x00000007
[08:13:42.264]    DbgMCU_APB1_Fz=0x00000000
[08:13:42.265]    DbgMCU_APB2_Fz=0x00000000
[08:13:42.265]    DoOptionByteLoading=0x00000000
[08:13:42.266]  </debugvars>
[08:13:42.266]  
[08:13:42.267]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[08:13:42.267]    <block atomic="false" info="">
[08:13:42.268]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[08:13:42.268]        // -> [connectionFlash <= 0x00000001]
[08:13:42.269]      __var FLASH_BASE = 0x40022000 ;
[08:13:42.269]        // -> [FLASH_BASE <= 0x40022000]
[08:13:42.270]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[08:13:42.270]        // -> [FLASH_CR <= 0x40022004]
[08:13:42.271]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[08:13:42.271]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[08:13:42.271]      __var LOCK_BIT = ( 1 << 0 ) ;
[08:13:42.272]        // -> [LOCK_BIT <= 0x00000001]
[08:13:42.272]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[08:13:42.273]        // -> [OPTLOCK_BIT <= 0x00000004]
[08:13:42.273]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[08:13:42.273]        // -> [FLASH_KEYR <= 0x4002200C]
[08:13:42.274]      __var FLASH_KEY1 = 0x89ABCDEF ;
[08:13:42.274]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[08:13:42.274]      __var FLASH_KEY2 = 0x02030405 ;
[08:13:42.275]        // -> [FLASH_KEY2 <= 0x02030405]
[08:13:42.275]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[08:13:42.276]        // -> [FLASH_OPTKEYR <= 0x40022014]
[08:13:42.276]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[08:13:42.276]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[08:13:42.276]      __var FLASH_OPTKEY2 = 0x24252627 ;
[08:13:42.276]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[08:13:42.277]      __var FLASH_CR_Value = 0 ;
[08:13:42.277]        // -> [FLASH_CR_Value <= 0x00000000]
[08:13:42.277]      __var DoDebugPortStop = 1 ;
[08:13:42.277]        // -> [DoDebugPortStop <= 0x00000001]
[08:13:42.277]      __var DP_CTRL_STAT = 0x4 ;
[08:13:42.277]        // -> [DP_CTRL_STAT <= 0x00000004]
[08:13:42.278]      __var DP_SELECT = 0x8 ;
[08:13:42.278]        // -> [DP_SELECT <= 0x00000008]
[08:13:42.278]    </block>
[08:13:42.279]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[08:13:42.279]      // if-block "connectionFlash && DoOptionByteLoading"
[08:13:42.279]        // =>  FALSE
[08:13:42.280]      // skip if-block "connectionFlash && DoOptionByteLoading"
[08:13:42.280]    </control>
[08:13:42.280]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[08:13:42.280]      // if-block "DoDebugPortStop"
[08:13:42.280]        // =>  TRUE
[08:13:42.280]      <block atomic="false" info="">
[08:13:42.281]        WriteDP(DP_SELECT, 0x00000000);
[08:13:42.281]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[08:13:42.281]        WriteDP(DP_CTRL_STAT, 0x00000000);
[08:13:42.282]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[08:13:42.282]      </block>
[08:13:42.282]      // end if-block "DoDebugPortStop"
[08:13:42.283]    </control>
[08:13:42.283]  </sequence>
[08:13:42.283]  
[08:13:42.754]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:13:42.754]  
[08:13:42.755]  <debugvars>
[08:13:42.755]    // Pre-defined
[08:13:42.755]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:13:42.756]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[08:13:42.756]    __dp=0x00000000
[08:13:42.757]    __ap=0x00000000
[08:13:42.757]    __traceout=0x00000000      (Trace Disabled)
[08:13:42.758]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:13:42.758]    __FlashAddr=0x00000000
[08:13:42.758]    __FlashLen=0x00000000
[08:13:42.759]    __FlashArg=0x00000000
[08:13:42.759]    __FlashOp=0x00000000
[08:13:42.759]    __Result=0x00000000
[08:13:42.759]    
[08:13:42.759]    // User-defined
[08:13:42.760]    DbgMCU_CR=0x00000007
[08:13:42.760]    DbgMCU_APB1_Fz=0x00000000
[08:13:42.760]    DbgMCU_APB2_Fz=0x00000000
[08:13:42.761]    DoOptionByteLoading=0x00000000
[08:13:42.761]  </debugvars>
[08:13:42.761]  
[08:13:42.761]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:13:42.761]    <block atomic="false" info="">
[08:13:42.762]      Sequence("CheckID");
[08:13:42.762]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:13:42.762]          <block atomic="false" info="">
[08:13:42.762]            __var pidr1 = 0;
[08:13:42.762]              // -> [pidr1 <= 0x00000000]
[08:13:42.763]            __var pidr2 = 0;
[08:13:42.763]              // -> [pidr2 <= 0x00000000]
[08:13:42.763]            __var jep106id = 0;
[08:13:42.763]              // -> [jep106id <= 0x00000000]
[08:13:42.763]            __var ROMTableBase = 0;
[08:13:42.763]              // -> [ROMTableBase <= 0x00000000]
[08:13:42.764]            __ap = 0;      // AHB-AP
[08:13:42.764]              // -> [__ap <= 0x00000000]
[08:13:42.764]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:13:42.765]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:13:42.765]              // -> [ROMTableBase <= 0xF0000000]
[08:13:42.765]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:13:42.766]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:13:42.767]              // -> [pidr1 <= 0x00000004]
[08:13:42.767]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:13:42.768]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:13:42.768]              // -> [pidr2 <= 0x0000000A]
[08:13:42.768]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:13:42.769]              // -> [jep106id <= 0x00000020]
[08:13:42.769]          </block>
[08:13:42.769]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:13:42.770]            // if-block "jep106id != 0x20"
[08:13:42.770]              // =>  FALSE
[08:13:42.770]            // skip if-block "jep106id != 0x20"
[08:13:42.771]          </control>
[08:13:42.771]        </sequence>
[08:13:42.771]    </block>
[08:13:42.771]  </sequence>
[08:13:42.771]  
[08:13:42.783]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:13:42.783]  
[08:13:42.804]  <debugvars>
[08:13:42.805]    // Pre-defined
[08:13:42.805]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:13:42.806]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[08:13:42.806]    __dp=0x00000000
[08:13:42.807]    __ap=0x00000000
[08:13:42.808]    __traceout=0x00000000      (Trace Disabled)
[08:13:42.808]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:13:42.809]    __FlashAddr=0x00000000
[08:13:42.809]    __FlashLen=0x00000000
[08:13:42.810]    __FlashArg=0x00000000
[08:13:42.810]    __FlashOp=0x00000000
[08:13:42.811]    __Result=0x00000000
[08:13:42.812]    
[08:13:42.812]    // User-defined
[08:13:42.812]    DbgMCU_CR=0x00000007
[08:13:42.813]    DbgMCU_APB1_Fz=0x00000000
[08:13:42.813]    DbgMCU_APB2_Fz=0x00000000
[08:13:42.814]    DoOptionByteLoading=0x00000000
[08:13:42.814]  </debugvars>
[08:13:42.815]  
[08:13:42.815]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:13:42.816]    <block atomic="false" info="">
[08:13:42.816]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:13:42.817]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:13:42.818]    </block>
[08:13:42.818]    <block atomic="false" info="DbgMCU registers">
[08:13:42.819]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:13:42.820]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[08:13:42.821]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[08:13:42.822]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:13:42.823]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:13:42.823]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:13:42.824]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:13:42.824]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:13:42.825]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:13:42.825]    </block>
[08:13:42.826]  </sequence>
[08:13:42.826]  
[08:27:17.682]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[08:27:17.682]  
[08:27:17.683]  <debugvars>
[08:27:17.683]    // Pre-defined
[08:27:17.683]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:27:17.684]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[08:27:17.684]    __dp=0x00000000
[08:27:17.684]    __ap=0x00000000
[08:27:17.686]    __traceout=0x00000000      (Trace Disabled)
[08:27:17.686]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:27:17.687]    __FlashAddr=0x00000000
[08:27:17.687]    __FlashLen=0x00000000
[08:27:17.687]    __FlashArg=0x00000000
[08:27:17.687]    __FlashOp=0x00000000
[08:27:17.688]    __Result=0x00000000
[08:27:17.688]    
[08:27:17.688]    // User-defined
[08:27:17.688]    DbgMCU_CR=0x00000007
[08:27:17.689]    DbgMCU_APB1_Fz=0x00000000
[08:27:17.689]    DbgMCU_APB2_Fz=0x00000000
[08:27:17.689]    DoOptionByteLoading=0x00000000
[08:27:17.689]  </debugvars>
[08:27:17.689]  
[08:27:17.690]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[08:27:17.690]    <block atomic="false" info="">
[08:27:17.690]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[08:27:17.690]        // -> [connectionFlash <= 0x00000000]
[08:27:17.690]      __var FLASH_BASE = 0x40022000 ;
[08:27:17.692]        // -> [FLASH_BASE <= 0x40022000]
[08:27:17.692]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[08:27:17.692]        // -> [FLASH_CR <= 0x40022004]
[08:27:17.692]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[08:27:17.692]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[08:27:17.693]      __var LOCK_BIT = ( 1 << 0 ) ;
[08:27:17.693]        // -> [LOCK_BIT <= 0x00000001]
[08:27:17.693]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[08:27:17.693]        // -> [OPTLOCK_BIT <= 0x00000004]
[08:27:17.693]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[08:27:17.693]        // -> [FLASH_KEYR <= 0x4002200C]
[08:27:17.693]      __var FLASH_KEY1 = 0x89ABCDEF ;
[08:27:17.693]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[08:27:17.693]      __var FLASH_KEY2 = 0x02030405 ;
[08:27:17.693]        // -> [FLASH_KEY2 <= 0x02030405]
[08:27:17.693]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[08:27:17.693]        // -> [FLASH_OPTKEYR <= 0x40022014]
[08:27:17.693]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[08:27:17.693]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[08:27:17.693]      __var FLASH_OPTKEY2 = 0x24252627 ;
[08:27:17.693]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[08:27:17.693]      __var FLASH_CR_Value = 0 ;
[08:27:17.693]        // -> [FLASH_CR_Value <= 0x00000000]
[08:27:17.693]      __var DoDebugPortStop = 1 ;
[08:27:17.693]        // -> [DoDebugPortStop <= 0x00000001]
[08:27:17.693]      __var DP_CTRL_STAT = 0x4 ;
[08:27:17.693]        // -> [DP_CTRL_STAT <= 0x00000004]
[08:27:17.693]      __var DP_SELECT = 0x8 ;
[08:27:17.693]        // -> [DP_SELECT <= 0x00000008]
[08:27:17.693]    </block>
[08:27:17.693]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[08:27:17.693]      // if-block "connectionFlash && DoOptionByteLoading"
[08:27:17.693]        // =>  FALSE
[08:27:17.693]      // skip if-block "connectionFlash && DoOptionByteLoading"
[08:27:17.693]    </control>
[08:27:17.693]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[08:27:17.693]      // if-block "DoDebugPortStop"
[08:27:17.693]        // =>  TRUE
[08:27:17.693]      <block atomic="false" info="">
[08:27:17.693]        WriteDP(DP_SELECT, 0x00000000);
[08:27:17.693]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[08:27:17.693]        WriteDP(DP_CTRL_STAT, 0x00000000);
[08:27:17.693]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[08:27:17.693]      </block>
[08:27:17.693]      // end if-block "DoDebugPortStop"
[08:27:17.693]    </control>
[08:27:17.693]  </sequence>
[08:27:17.693]  
