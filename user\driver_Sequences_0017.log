/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : D:\2025work\STM32L072PRO\source-application\user\driver_Sequences_0017.log
 *  Created     : 16:48:20 (15/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : D:\2025work\STM32L072PRO\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[16:48:21.021]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:48:21.021]  
[16:48:21.044]  <debugvars>
[16:48:21.063]    // Pre-defined
[16:48:21.086]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:48:21.104]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:48:21.104]    __dp=0x00000000
[16:48:21.105]    __ap=0x00000000
[16:48:21.105]    __traceout=0x00000000      (Trace Disabled)
[16:48:21.105]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:48:21.105]    __FlashAddr=0x00000000
[16:48:21.105]    __FlashLen=0x00000000
[16:48:21.105]    __FlashArg=0x00000000
[16:48:21.106]    __FlashOp=0x00000000
[16:48:21.106]    __Result=0x00000000
[16:48:21.106]    
[16:48:21.106]    // User-defined
[16:48:21.107]    DbgMCU_CR=0x00000007
[16:48:21.107]    DbgMCU_APB1_Fz=0x00000000
[16:48:21.107]    DbgMCU_APB2_Fz=0x00000000
[16:48:21.107]    DoOptionByteLoading=0x00000000
[16:48:21.108]  </debugvars>
[16:48:21.108]  
[16:48:21.108]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:48:21.108]    <block atomic="false" info="">
[16:48:21.109]      Sequence("CheckID");
[16:48:21.109]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:48:21.109]          <block atomic="false" info="">
[16:48:21.109]            __var pidr1 = 0;
[16:48:21.110]              // -> [pidr1 <= 0x00000000]
[16:48:21.110]            __var pidr2 = 0;
[16:48:21.110]              // -> [pidr2 <= 0x00000000]
[16:48:21.110]            __var jep106id = 0;
[16:48:21.110]              // -> [jep106id <= 0x00000000]
[16:48:21.111]            __var ROMTableBase = 0;
[16:48:21.111]              // -> [ROMTableBase <= 0x00000000]
[16:48:21.111]            __ap = 0;      // AHB-AP
[16:48:21.111]              // -> [__ap <= 0x00000000]
[16:48:21.111]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:48:21.112]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:48:21.112]              // -> [ROMTableBase <= 0xF0000000]
[16:48:21.112]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:48:21.114]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:48:21.114]              // -> [pidr1 <= 0x00000004]
[16:48:21.115]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:48:21.115]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:48:21.116]              // -> [pidr2 <= 0x0000000A]
[16:48:21.116]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:48:21.117]              // -> [jep106id <= 0x00000020]
[16:48:21.117]          </block>
[16:48:21.117]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:48:21.117]            // if-block "jep106id != 0x20"
[16:48:21.117]              // =>  FALSE
[16:48:21.117]            // skip if-block "jep106id != 0x20"
[16:48:21.117]          </control>
[16:48:21.118]        </sequence>
[16:48:21.118]    </block>
[16:48:21.118]  </sequence>
[16:48:21.118]  
[16:48:21.130]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:48:21.130]  
[16:48:21.130]  <debugvars>
[16:48:21.130]    // Pre-defined
[16:48:21.130]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:48:21.131]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:48:21.131]    __dp=0x00000000
[16:48:21.132]    __ap=0x00000000
[16:48:21.132]    __traceout=0x00000000      (Trace Disabled)
[16:48:21.132]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:48:21.132]    __FlashAddr=0x00000000
[16:48:21.132]    __FlashLen=0x00000000
[16:48:21.132]    __FlashArg=0x00000000
[16:48:21.132]    __FlashOp=0x00000000
[16:48:21.134]    __Result=0x00000000
[16:48:21.134]    
[16:48:21.134]    // User-defined
[16:48:21.134]    DbgMCU_CR=0x00000007
[16:48:21.134]    DbgMCU_APB1_Fz=0x00000000
[16:48:21.134]    DbgMCU_APB2_Fz=0x00000000
[16:48:21.135]    DoOptionByteLoading=0x00000000
[16:48:21.135]  </debugvars>
[16:48:21.135]  
[16:48:21.135]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:48:21.135]    <block atomic="false" info="">
[16:48:21.135]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:48:21.136]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:48:21.136]    </block>
[16:48:21.136]    <block atomic="false" info="DbgMCU registers">
[16:48:21.137]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:48:21.137]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[16:48:21.138]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[16:48:21.138]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:48:21.139]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:48:21.139]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:48:21.140]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:48:21.141]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:48:21.141]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:48:21.141]    </block>
[16:48:21.142]  </sequence>
[16:48:21.142]  
[16:48:29.169]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:48:29.169]  
[16:48:29.169]  <debugvars>
[16:48:29.170]    // Pre-defined
[16:48:29.170]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:48:29.171]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:48:29.171]    __dp=0x00000000
[16:48:29.171]    __ap=0x00000000
[16:48:29.171]    __traceout=0x00000000      (Trace Disabled)
[16:48:29.171]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:48:29.172]    __FlashAddr=0x00000000
[16:48:29.172]    __FlashLen=0x00000000
[16:48:29.172]    __FlashArg=0x00000000
[16:48:29.172]    __FlashOp=0x00000000
[16:48:29.172]    __Result=0x00000000
[16:48:29.172]    
[16:48:29.172]    // User-defined
[16:48:29.172]    DbgMCU_CR=0x00000007
[16:48:29.173]    DbgMCU_APB1_Fz=0x00000000
[16:48:29.173]    DbgMCU_APB2_Fz=0x00000000
[16:48:29.174]    DoOptionByteLoading=0x00000000
[16:48:29.174]  </debugvars>
[16:48:29.174]  
[16:48:29.174]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:48:29.174]    <block atomic="false" info="">
[16:48:29.175]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:48:29.175]        // -> [connectionFlash <= 0x00000001]
[16:48:29.175]      __var FLASH_BASE = 0x40022000 ;
[16:48:29.175]        // -> [FLASH_BASE <= 0x40022000]
[16:48:29.175]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:48:29.175]        // -> [FLASH_CR <= 0x40022004]
[16:48:29.176]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:48:29.176]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:48:29.176]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:48:29.176]        // -> [LOCK_BIT <= 0x00000001]
[16:48:29.177]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:48:29.177]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:48:29.177]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:48:29.177]        // -> [FLASH_KEYR <= 0x4002200C]
[16:48:29.177]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:48:29.177]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:48:29.178]      __var FLASH_KEY2 = 0x02030405 ;
[16:48:29.178]        // -> [FLASH_KEY2 <= 0x02030405]
[16:48:29.178]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:48:29.179]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:48:29.179]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:48:29.179]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:48:29.179]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:48:29.180]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:48:29.180]      __var FLASH_CR_Value = 0 ;
[16:48:29.180]        // -> [FLASH_CR_Value <= 0x00000000]
[16:48:29.180]      __var DoDebugPortStop = 1 ;
[16:48:29.181]        // -> [DoDebugPortStop <= 0x00000001]
[16:48:29.181]      __var DP_CTRL_STAT = 0x4 ;
[16:48:29.181]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:48:29.181]      __var DP_SELECT = 0x8 ;
[16:48:29.181]        // -> [DP_SELECT <= 0x00000008]
[16:48:29.182]    </block>
[16:48:29.182]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:48:29.182]      // if-block "connectionFlash && DoOptionByteLoading"
[16:48:29.182]        // =>  FALSE
[16:48:29.182]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:48:29.182]    </control>
[16:48:29.182]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:48:29.182]      // if-block "DoDebugPortStop"
[16:48:29.182]        // =>  TRUE
[16:48:29.183]      <block atomic="false" info="">
[16:48:29.183]        WriteDP(DP_SELECT, 0x00000000);
[16:48:29.184]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:48:29.184]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:48:29.184]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:48:29.185]      </block>
[16:48:29.185]      // end if-block "DoDebugPortStop"
[16:48:29.185]    </control>
[16:48:29.185]  </sequence>
[16:48:29.185]  
[16:48:29.559]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:48:29.559]  
[16:48:29.559]  <debugvars>
[16:48:29.560]    // Pre-defined
[16:48:29.560]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:48:29.560]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[16:48:29.560]    __dp=0x00000000
[16:48:29.560]    __ap=0x00000000
[16:48:29.561]    __traceout=0x00000000      (Trace Disabled)
[16:48:29.561]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:48:29.561]    __FlashAddr=0x00000000
[16:48:29.561]    __FlashLen=0x00000000
[16:48:29.561]    __FlashArg=0x00000000
[16:48:29.562]    __FlashOp=0x00000000
[16:48:29.562]    __Result=0x00000000
[16:48:29.562]    
[16:48:29.562]    // User-defined
[16:48:29.562]    DbgMCU_CR=0x00000007
[16:48:29.562]    DbgMCU_APB1_Fz=0x00000000
[16:48:29.563]    DbgMCU_APB2_Fz=0x00000000
[16:48:29.563]    DoOptionByteLoading=0x00000000
[16:48:29.563]  </debugvars>
[16:48:29.563]  
[16:48:29.564]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:48:29.564]    <block atomic="false" info="">
[16:48:29.564]      Sequence("CheckID");
[16:48:29.564]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:48:29.564]          <block atomic="false" info="">
[16:48:29.565]            __var pidr1 = 0;
[16:48:29.565]              // -> [pidr1 <= 0x00000000]
[16:48:29.565]            __var pidr2 = 0;
[16:48:29.565]              // -> [pidr2 <= 0x00000000]
[16:48:29.565]            __var jep106id = 0;
[16:48:29.565]              // -> [jep106id <= 0x00000000]
[16:48:29.566]            __var ROMTableBase = 0;
[16:48:29.566]              // -> [ROMTableBase <= 0x00000000]
[16:48:29.566]            __ap = 0;      // AHB-AP
[16:48:29.566]              // -> [__ap <= 0x00000000]
[16:48:29.566]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:48:29.567]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:48:29.568]              // -> [ROMTableBase <= 0xF0000000]
[16:48:29.568]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:48:29.568]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:48:29.569]              // -> [pidr1 <= 0x00000004]
[16:48:29.569]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:48:29.569]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:48:29.570]              // -> [pidr2 <= 0x0000000A]
[16:48:29.570]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:48:29.571]              // -> [jep106id <= 0x00000020]
[16:48:29.571]          </block>
[16:48:29.571]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:48:29.571]            // if-block "jep106id != 0x20"
[16:48:29.572]              // =>  FALSE
[16:48:29.572]            // skip if-block "jep106id != 0x20"
[16:48:29.572]          </control>
[16:48:29.572]        </sequence>
[16:48:29.572]    </block>
[16:48:29.573]  </sequence>
[16:48:29.573]  
[16:48:29.585]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:48:29.585]  
[16:48:29.585]  <debugvars>
[16:48:29.585]    // Pre-defined
[16:48:29.585]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:48:29.585]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[16:48:29.586]    __dp=0x00000000
[16:48:29.586]    __ap=0x00000000
[16:48:29.586]    __traceout=0x00000000      (Trace Disabled)
[16:48:29.586]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:48:29.586]    __FlashAddr=0x00000000
[16:48:29.586]    __FlashLen=0x00000000
[16:48:29.587]    __FlashArg=0x00000000
[16:48:29.587]    __FlashOp=0x00000000
[16:48:29.587]    __Result=0x00000000
[16:48:29.587]    
[16:48:29.587]    // User-defined
[16:48:29.587]    DbgMCU_CR=0x00000007
[16:48:29.588]    DbgMCU_APB1_Fz=0x00000000
[16:48:29.588]    DbgMCU_APB2_Fz=0x00000000
[16:48:29.588]    DoOptionByteLoading=0x00000000
[16:48:29.588]  </debugvars>
[16:48:29.588]  
[16:48:29.589]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:48:29.589]    <block atomic="false" info="">
[16:48:29.589]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:48:29.590]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:48:29.590]    </block>
[16:48:29.590]    <block atomic="false" info="DbgMCU registers">
[16:48:29.590]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:48:29.591]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[16:48:29.592]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[16:48:29.592]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:48:29.593]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:48:29.593]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:48:29.594]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:48:29.594]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:48:29.595]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:48:29.596]    </block>
[16:48:29.596]  </sequence>
[16:48:29.596]  
[16:52:55.144]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:52:55.144]  
[16:52:55.144]  <debugvars>
[16:52:55.145]    // Pre-defined
[16:52:55.145]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:52:55.145]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[16:52:55.145]    __dp=0x00000000
[16:52:55.146]    __ap=0x00000000
[16:52:55.146]    __traceout=0x00000000      (Trace Disabled)
[16:52:55.146]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:52:55.147]    __FlashAddr=0x00000000
[16:52:55.147]    __FlashLen=0x00000000
[16:52:55.148]    __FlashArg=0x00000000
[16:52:55.148]    __FlashOp=0x00000000
[16:52:55.148]    __Result=0x00000000
[16:52:55.149]    
[16:52:55.149]    // User-defined
[16:52:55.149]    DbgMCU_CR=0x00000007
[16:52:55.149]    DbgMCU_APB1_Fz=0x00000000
[16:52:55.150]    DbgMCU_APB2_Fz=0x00000000
[16:52:55.150]    DoOptionByteLoading=0x00000000
[16:52:55.150]  </debugvars>
[16:52:55.150]  
[16:52:55.151]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:52:55.151]    <block atomic="false" info="">
[16:52:55.151]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:52:55.151]        // -> [connectionFlash <= 0x00000000]
[16:52:55.152]      __var FLASH_BASE = 0x40022000 ;
[16:52:55.152]        // -> [FLASH_BASE <= 0x40022000]
[16:52:55.152]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:52:55.153]        // -> [FLASH_CR <= 0x40022004]
[16:52:55.153]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:52:55.153]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:52:55.153]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:52:55.154]        // -> [LOCK_BIT <= 0x00000001]
[16:52:55.154]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:52:55.154]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:52:55.154]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:52:55.154]        // -> [FLASH_KEYR <= 0x4002200C]
[16:52:55.155]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:52:55.155]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:52:55.155]      __var FLASH_KEY2 = 0x02030405 ;
[16:52:55.155]        // -> [FLASH_KEY2 <= 0x02030405]
[16:52:55.155]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:52:55.155]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:52:55.155]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:52:55.155]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:52:55.155]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:52:55.155]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:52:55.155]      __var FLASH_CR_Value = 0 ;
[16:52:55.155]        // -> [FLASH_CR_Value <= 0x00000000]
[16:52:55.155]      __var DoDebugPortStop = 1 ;
[16:52:55.155]        // -> [DoDebugPortStop <= 0x00000001]
[16:52:55.155]      __var DP_CTRL_STAT = 0x4 ;
[16:52:55.155]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:52:55.155]      __var DP_SELECT = 0x8 ;
[16:52:55.155]        // -> [DP_SELECT <= 0x00000008]
[16:52:55.155]    </block>
[16:52:55.155]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:52:55.155]      // if-block "connectionFlash && DoOptionByteLoading"
[16:52:55.155]        // =>  FALSE
[16:52:55.155]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:52:55.155]    </control>
[16:52:55.155]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:52:55.155]      // if-block "DoDebugPortStop"
[16:52:55.155]        // =>  TRUE
[16:52:55.155]      <block atomic="false" info="">
[16:52:55.155]        WriteDP(DP_SELECT, 0x00000000);
[16:52:55.161]  
[16:52:55.161]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[16:52:55.161]  
[16:52:55.161]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:52:55.162]      </block>
[16:52:55.162]      // end if-block "DoDebugPortStop"
[16:52:55.162]    </control>
[16:52:55.162]  </sequence>
[16:52:55.163]  
[16:53:46.818]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:53:46.818]  
[16:53:46.818]  <debugvars>
[16:53:46.818]    // Pre-defined
[16:53:46.818]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:53:46.818]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[16:53:46.822]    __dp=0x00000000
[16:53:46.822]    __ap=0x00000000
[16:53:46.822]    __traceout=0x00000000      (Trace Disabled)
[16:53:46.822]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:53:46.824]    __FlashAddr=0x00000000
[16:53:46.824]    __FlashLen=0x00000000
[16:53:46.824]    __FlashArg=0x00000000
[16:53:46.825]    __FlashOp=0x00000000
[16:53:46.825]    __Result=0x00000000
[16:53:46.825]    
[16:53:46.825]    // User-defined
[16:53:46.826]    DbgMCU_CR=0x00000007
[16:53:46.826]    DbgMCU_APB1_Fz=0x00000000
[16:53:46.826]    DbgMCU_APB2_Fz=0x00000000
[16:53:46.826]    DoOptionByteLoading=0x00000000
[16:53:46.826]  </debugvars>
[16:53:46.827]  
[16:53:46.827]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:53:46.827]    <block atomic="false" info="">
[16:53:46.827]      Sequence("CheckID");
[16:53:46.827]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:53:46.828]          <block atomic="false" info="">
[16:53:46.828]            __var pidr1 = 0;
[16:53:46.828]              // -> [pidr1 <= 0x00000000]
[16:53:46.828]            __var pidr2 = 0;
[16:53:46.828]              // -> [pidr2 <= 0x00000000]
[16:53:46.829]            __var jep106id = 0;
[16:53:46.830]              // -> [jep106id <= 0x00000000]
[16:53:46.830]            __var ROMTableBase = 0;
[16:53:46.830]              // -> [ROMTableBase <= 0x00000000]
[16:53:46.830]            __ap = 0;      // AHB-AP
[16:53:46.830]              // -> [__ap <= 0x00000000]
[16:53:46.831]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:53:46.832]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:53:46.832]              // -> [ROMTableBase <= 0xF0000000]
[16:53:46.832]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:53:46.834]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:53:46.834]              // -> [pidr1 <= 0x00000004]
[16:53:46.834]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:53:46.835]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:53:46.836]              // -> [pidr2 <= 0x0000000A]
[16:53:46.836]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:53:46.836]              // -> [jep106id <= 0x00000020]
[16:53:46.837]          </block>
[16:53:46.837]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:53:46.837]            // if-block "jep106id != 0x20"
[16:53:46.837]              // =>  FALSE
[16:53:46.838]            // skip if-block "jep106id != 0x20"
[16:53:46.838]          </control>
[16:53:46.838]        </sequence>
[16:53:46.838]    </block>
[16:53:46.839]  </sequence>
[16:53:46.839]  
[16:53:46.852]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:53:46.852]  
[16:53:46.877]  <debugvars>
[16:53:46.877]    // Pre-defined
[16:53:46.878]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:53:46.878]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[16:53:46.879]    __dp=0x00000000
[16:53:46.880]    __ap=0x00000000
[16:53:46.880]    __traceout=0x00000000      (Trace Disabled)
[16:53:46.880]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:53:46.880]    __FlashAddr=0x00000000
[16:53:46.880]    __FlashLen=0x00000000
[16:53:46.880]    __FlashArg=0x00000000
[16:53:46.880]    __FlashOp=0x00000000
[16:53:46.880]    __Result=0x00000000
[16:53:46.880]    
[16:53:46.880]    // User-defined
[16:53:46.880]    DbgMCU_CR=0x00000007
[16:53:46.880]    DbgMCU_APB1_Fz=0x00000000
[16:53:46.880]    DbgMCU_APB2_Fz=0x00000000
[16:53:46.880]    DoOptionByteLoading=0x00000000
[16:53:46.880]  </debugvars>
[16:53:46.880]  
[16:53:46.880]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:53:46.880]    <block atomic="false" info="">
[16:53:46.884]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:53:46.884]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:53:46.884]    </block>
[16:53:46.884]    <block atomic="false" info="DbgMCU registers">
[16:53:46.884]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:53:46.884]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[16:53:46.888]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[16:53:46.888]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:53:46.889]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:53:46.889]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:53:46.889]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:53:46.889]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:53:46.889]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:53:46.889]    </block>
[16:53:46.889]  </sequence>
[16:53:46.889]  
[17:07:42.265]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:07:42.265]  
[17:07:42.266]  <debugvars>
[17:07:42.266]    // Pre-defined
[17:07:42.267]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:07:42.267]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[17:07:42.267]    __dp=0x00000000
[17:07:42.267]    __ap=0x00000000
[17:07:42.267]    __traceout=0x00000000      (Trace Disabled)
[17:07:42.267]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:07:42.268]    __FlashAddr=0x00000000
[17:07:42.268]    __FlashLen=0x00000000
[17:07:42.269]    __FlashArg=0x00000000
[17:07:42.269]    __FlashOp=0x00000000
[17:07:42.269]    __Result=0x00000000
[17:07:42.269]    
[17:07:42.269]    // User-defined
[17:07:42.270]    DbgMCU_CR=0x00000007
[17:07:42.270]    DbgMCU_APB1_Fz=0x00000000
[17:07:42.270]    DbgMCU_APB2_Fz=0x00000000
[17:07:42.270]    DoOptionByteLoading=0x00000000
[17:07:42.271]  </debugvars>
[17:07:42.271]  
[17:07:42.271]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:07:42.271]    <block atomic="false" info="">
[17:07:42.271]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:07:42.272]        // -> [connectionFlash <= 0x00000000]
[17:07:42.272]      __var FLASH_BASE = 0x40022000 ;
[17:07:42.273]        // -> [FLASH_BASE <= 0x40022000]
[17:07:42.273]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:07:42.273]        // -> [FLASH_CR <= 0x40022004]
[17:07:42.273]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:07:42.273]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:07:42.273]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:07:42.273]        // -> [LOCK_BIT <= 0x00000001]
[17:07:42.275]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:07:42.275]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:07:42.275]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:07:42.275]        // -> [FLASH_KEYR <= 0x4002200C]
[17:07:42.275]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:07:42.275]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:07:42.275]      __var FLASH_KEY2 = 0x02030405 ;
[17:07:42.275]        // -> [FLASH_KEY2 <= 0x02030405]
[17:07:42.275]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:07:42.275]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:07:42.277]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:07:42.277]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:07:42.277]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:07:42.277]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:07:42.277]      __var FLASH_CR_Value = 0 ;
[17:07:42.277]        // -> [FLASH_CR_Value <= 0x00000000]
[17:07:42.277]      __var DoDebugPortStop = 1 ;
[17:07:42.277]        // -> [DoDebugPortStop <= 0x00000001]
[17:07:42.277]      __var DP_CTRL_STAT = 0x4 ;
[17:07:42.277]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:07:42.277]      __var DP_SELECT = 0x8 ;
[17:07:42.277]        // -> [DP_SELECT <= 0x00000008]
[17:07:42.277]    </block>
[17:07:42.280]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:07:42.280]      // if-block "connectionFlash && DoOptionByteLoading"
[17:07:42.280]        // =>  FALSE
[17:07:42.280]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:07:42.280]    </control>
[17:07:42.281]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:07:42.281]      // if-block "DoDebugPortStop"
[17:07:42.281]        // =>  TRUE
[17:07:42.281]      <block atomic="false" info="">
[17:07:42.282]        WriteDP(DP_SELECT, 0x00000000);
[17:07:42.283]  
[17:07:42.283]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[17:07:42.283]  
[17:07:42.283]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:07:42.283]      </block>
[17:07:42.284]      // end if-block "DoDebugPortStop"
[17:07:42.284]    </control>
[17:07:42.284]  </sequence>
[17:07:42.284]  
[17:09:20.698]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:09:20.698]  
[17:09:20.699]  <debugvars>
[17:09:20.699]    // Pre-defined
[17:09:20.699]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:09:20.700]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[17:09:20.700]    __dp=0x00000000
[17:09:20.700]    __ap=0x00000000
[17:09:20.700]    __traceout=0x00000000      (Trace Disabled)
[17:09:20.701]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:09:20.701]    __FlashAddr=0x00000000
[17:09:20.701]    __FlashLen=0x00000000
[17:09:20.702]    __FlashArg=0x00000000
[17:09:20.702]    __FlashOp=0x00000000
[17:09:20.702]    __Result=0x00000000
[17:09:20.702]    
[17:09:20.702]    // User-defined
[17:09:20.703]    DbgMCU_CR=0x00000007
[17:09:20.703]    DbgMCU_APB1_Fz=0x00000000
[17:09:20.703]    DbgMCU_APB2_Fz=0x00000000
[17:09:20.703]    DoOptionByteLoading=0x00000000
[17:09:20.703]  </debugvars>
[17:09:20.703]  
[17:09:20.704]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:09:20.704]    <block atomic="false" info="">
[17:09:20.704]      Sequence("CheckID");
[17:09:20.704]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:09:20.704]          <block atomic="false" info="">
[17:09:20.705]            __var pidr1 = 0;
[17:09:20.705]              // -> [pidr1 <= 0x00000000]
[17:09:20.705]            __var pidr2 = 0;
[17:09:20.705]              // -> [pidr2 <= 0x00000000]
[17:09:20.706]            __var jep106id = 0;
[17:09:20.706]              // -> [jep106id <= 0x00000000]
[17:09:20.706]            __var ROMTableBase = 0;
[17:09:20.706]              // -> [ROMTableBase <= 0x00000000]
[17:09:20.706]            __ap = 0;      // AHB-AP
[17:09:20.707]              // -> [__ap <= 0x00000000]
[17:09:20.707]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:09:20.707]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:09:20.707]              // -> [ROMTableBase <= 0xF0000000]
[17:09:20.708]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:09:20.709]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:09:20.709]              // -> [pidr1 <= 0x00000004]
[17:09:20.710]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:09:20.711]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:09:20.711]              // -> [pidr2 <= 0x0000000A]
[17:09:20.711]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:09:20.711]              // -> [jep106id <= 0x00000020]
[17:09:20.711]          </block>
[17:09:20.711]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:09:20.711]            // if-block "jep106id != 0x20"
[17:09:20.711]              // =>  FALSE
[17:09:20.711]            // skip if-block "jep106id != 0x20"
[17:09:20.713]          </control>
[17:09:20.713]        </sequence>
[17:09:20.713]    </block>
[17:09:20.714]  </sequence>
[17:09:20.714]  
[17:09:20.729]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:09:20.729]  
[17:09:20.747]  <debugvars>
[17:09:20.747]    // Pre-defined
[17:09:20.748]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:09:20.748]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[17:09:20.749]    __dp=0x00000000
[17:09:20.749]    __ap=0x00000000
[17:09:20.750]    __traceout=0x00000000      (Trace Disabled)
[17:09:20.751]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:09:20.751]    __FlashAddr=0x00000000
[17:09:20.752]    __FlashLen=0x00000000
[17:09:20.752]    __FlashArg=0x00000000
[17:09:20.753]    __FlashOp=0x00000000
[17:09:20.753]    __Result=0x00000000
[17:09:20.754]    
[17:09:20.754]    // User-defined
[17:09:20.754]    DbgMCU_CR=0x00000007
[17:09:20.755]    DbgMCU_APB1_Fz=0x00000000
[17:09:20.755]    DbgMCU_APB2_Fz=0x00000000
[17:09:20.755]    DoOptionByteLoading=0x00000000
[17:09:20.756]  </debugvars>
[17:09:20.756]  
[17:09:20.756]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:09:20.756]    <block atomic="false" info="">
[17:09:20.757]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:09:20.757]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:09:20.758]    </block>
[17:09:20.758]    <block atomic="false" info="DbgMCU registers">
[17:09:20.758]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:09:20.759]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[17:09:20.760]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[17:09:20.760]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:09:20.761]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:09:20.761]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:09:20.761]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:09:20.761]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:09:20.763]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:09:20.763]    </block>
[17:09:20.764]  </sequence>
[17:09:20.764]  
[17:09:27.351]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:09:27.351]  
[17:09:27.351]  <debugvars>
[17:09:27.351]    // Pre-defined
[17:09:27.351]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:09:27.351]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[17:09:27.351]    __dp=0x00000000
[17:09:27.351]    __ap=0x00000000
[17:09:27.351]    __traceout=0x00000000      (Trace Disabled)
[17:09:27.351]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:09:27.351]    __FlashAddr=0x00000000
[17:09:27.351]    __FlashLen=0x00000000
[17:09:27.351]    __FlashArg=0x00000000
[17:09:27.351]    __FlashOp=0x00000000
[17:09:27.351]    __Result=0x00000000
[17:09:27.354]    
[17:09:27.354]    // User-defined
[17:09:27.354]    DbgMCU_CR=0x00000007
[17:09:27.355]    DbgMCU_APB1_Fz=0x00000000
[17:09:27.355]    DbgMCU_APB2_Fz=0x00000000
[17:09:27.355]    DoOptionByteLoading=0x00000000
[17:09:27.355]  </debugvars>
[17:09:27.356]  
[17:09:27.356]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:09:27.356]    <block atomic="false" info="">
[17:09:27.356]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:09:27.356]        // -> [connectionFlash <= 0x00000000]
[17:09:27.356]      __var FLASH_BASE = 0x40022000 ;
[17:09:27.357]        // -> [FLASH_BASE <= 0x40022000]
[17:09:27.357]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:09:27.357]        // -> [FLASH_CR <= 0x40022004]
[17:09:27.357]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:09:27.357]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:09:27.357]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:09:27.358]        // -> [LOCK_BIT <= 0x00000001]
[17:09:27.358]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:09:27.358]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:09:27.358]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:09:27.358]        // -> [FLASH_KEYR <= 0x4002200C]
[17:09:27.360]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:09:27.360]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:09:27.360]      __var FLASH_KEY2 = 0x02030405 ;
[17:09:27.360]        // -> [FLASH_KEY2 <= 0x02030405]
[17:09:27.360]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:09:27.360]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:09:27.361]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:09:27.361]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:09:27.361]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:09:27.361]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:09:27.361]      __var FLASH_CR_Value = 0 ;
[17:09:27.362]        // -> [FLASH_CR_Value <= 0x00000000]
[17:09:27.362]      __var DoDebugPortStop = 1 ;
[17:09:27.362]        // -> [DoDebugPortStop <= 0x00000001]
[17:09:27.362]      __var DP_CTRL_STAT = 0x4 ;
[17:09:27.362]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:09:27.363]      __var DP_SELECT = 0x8 ;
[17:09:27.363]        // -> [DP_SELECT <= 0x00000008]
[17:09:27.363]    </block>
[17:09:27.363]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:09:27.364]      // if-block "connectionFlash && DoOptionByteLoading"
[17:09:27.364]        // =>  FALSE
[17:09:27.364]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:09:27.364]    </control>
[17:09:27.364]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:09:27.365]      // if-block "DoDebugPortStop"
[17:09:27.365]        // =>  TRUE
[17:09:27.365]      <block atomic="false" info="">
[17:09:27.366]        WriteDP(DP_SELECT, 0x00000000);
[17:09:27.366]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:09:27.366]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:09:27.367]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:09:27.367]      </block>
[17:09:27.367]      // end if-block "DoDebugPortStop"
[17:09:27.367]    </control>
[17:09:27.369]  </sequence>
[17:09:27.369]  
[17:09:33.152]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:09:33.152]  
[17:09:33.152]  <debugvars>
[17:09:33.158]    // Pre-defined
[17:09:33.158]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:09:33.158]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:09:33.158]    __dp=0x00000000
[17:09:33.158]    __ap=0x00000000
[17:09:33.158]    __traceout=0x00000000      (Trace Disabled)
[17:09:33.158]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:09:33.158]    __FlashAddr=0x00000000
[17:09:33.158]    __FlashLen=0x00000000
[17:09:33.158]    __FlashArg=0x00000000
[17:09:33.158]    __FlashOp=0x00000000
[17:09:33.158]    __Result=0x00000000
[17:09:33.158]    
[17:09:33.158]    // User-defined
[17:09:33.158]    DbgMCU_CR=0x00000007
[17:09:33.158]    DbgMCU_APB1_Fz=0x00000000
[17:09:33.158]    DbgMCU_APB2_Fz=0x00000000
[17:09:33.158]    DoOptionByteLoading=0x00000000
[17:09:33.158]  </debugvars>
[17:09:33.158]  
[17:09:33.158]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:09:33.158]    <block atomic="false" info="">
[17:09:33.158]      Sequence("CheckID");
[17:09:33.158]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:09:33.158]          <block atomic="false" info="">
[17:09:33.158]            __var pidr1 = 0;
[17:09:33.163]              // -> [pidr1 <= 0x00000000]
[17:09:33.163]            __var pidr2 = 0;
[17:09:33.163]              // -> [pidr2 <= 0x00000000]
[17:09:33.163]            __var jep106id = 0;
[17:09:33.164]              // -> [jep106id <= 0x00000000]
[17:09:33.164]            __var ROMTableBase = 0;
[17:09:33.164]              // -> [ROMTableBase <= 0x00000000]
[17:09:33.165]            __ap = 0;      // AHB-AP
[17:09:33.165]              // -> [__ap <= 0x00000000]
[17:09:33.165]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:09:33.165]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:09:33.165]              // -> [ROMTableBase <= 0xF0000000]
[17:09:33.165]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:09:33.165]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:09:33.165]              // -> [pidr1 <= 0x00000004]
[17:09:33.165]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:09:33.169]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:09:33.169]              // -> [pidr2 <= 0x0000000A]
[17:09:33.169]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:09:33.169]              // -> [jep106id <= 0x00000020]
[17:09:33.169]          </block>
[17:09:33.169]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:09:33.171]            // if-block "jep106id != 0x20"
[17:09:33.171]              // =>  FALSE
[17:09:33.171]            // skip if-block "jep106id != 0x20"
[17:09:33.171]          </control>
[17:09:33.171]        </sequence>
[17:09:33.171]    </block>
[17:09:33.171]  </sequence>
[17:09:33.171]  
[17:09:33.187]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:09:33.187]  
[17:09:33.197]  <debugvars>
[17:09:33.197]    // Pre-defined
[17:09:33.197]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:09:33.198]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:09:33.198]    __dp=0x00000000
[17:09:33.199]    __ap=0x00000000
[17:09:33.199]    __traceout=0x00000000      (Trace Disabled)
[17:09:33.199]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:09:33.200]    __FlashAddr=0x00000000
[17:09:33.201]    __FlashLen=0x00000000
[17:09:33.201]    __FlashArg=0x00000000
[17:09:33.202]    __FlashOp=0x00000000
[17:09:33.202]    __Result=0x00000000
[17:09:33.202]    
[17:09:33.202]    // User-defined
[17:09:33.202]    DbgMCU_CR=0x00000007
[17:09:33.202]    DbgMCU_APB1_Fz=0x00000000
[17:09:33.203]    DbgMCU_APB2_Fz=0x00000000
[17:09:33.204]    DoOptionByteLoading=0x00000000
[17:09:33.204]  </debugvars>
[17:09:33.204]  
[17:09:33.205]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:09:33.205]    <block atomic="false" info="">
[17:09:33.205]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:09:33.206]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:09:33.207]    </block>
[17:09:33.207]    <block atomic="false" info="DbgMCU registers">
[17:09:33.207]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:09:33.208]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[17:09:33.209]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[17:09:33.209]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:09:33.210]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:09:33.211]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:09:33.211]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:09:33.212]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:09:33.212]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:09:33.212]    </block>
[17:09:33.212]  </sequence>
[17:09:33.212]  
[17:09:41.810]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:09:41.810]  
[17:09:41.810]  <debugvars>
[17:09:41.810]    // Pre-defined
[17:09:41.810]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:09:41.814]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:09:41.814]    __dp=0x00000000
[17:09:41.815]    __ap=0x00000000
[17:09:41.815]    __traceout=0x00000000      (Trace Disabled)
[17:09:41.815]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:09:41.815]    __FlashAddr=0x00000000
[17:09:41.815]    __FlashLen=0x00000000
[17:09:41.815]    __FlashArg=0x00000000
[17:09:41.815]    __FlashOp=0x00000000
[17:09:41.815]    __Result=0x00000000
[17:09:41.815]    
[17:09:41.815]    // User-defined
[17:09:41.815]    DbgMCU_CR=0x00000007
[17:09:41.815]    DbgMCU_APB1_Fz=0x00000000
[17:09:41.815]    DbgMCU_APB2_Fz=0x00000000
[17:09:41.815]    DoOptionByteLoading=0x00000000
[17:09:41.820]  </debugvars>
[17:09:41.820]  
[17:09:41.820]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:09:41.820]    <block atomic="false" info="">
[17:09:41.820]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:09:41.820]        // -> [connectionFlash <= 0x00000001]
[17:09:41.820]      __var FLASH_BASE = 0x40022000 ;
[17:09:41.820]        // -> [FLASH_BASE <= 0x40022000]
[17:09:41.820]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:09:41.820]        // -> [FLASH_CR <= 0x40022004]
[17:09:41.820]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:09:41.820]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:09:41.820]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:09:41.820]        // -> [LOCK_BIT <= 0x00000001]
[17:09:41.820]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:09:41.820]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:09:41.820]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:09:41.820]        // -> [FLASH_KEYR <= 0x4002200C]
[17:09:41.825]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:09:41.825]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:09:41.825]      __var FLASH_KEY2 = 0x02030405 ;
[17:09:41.825]        // -> [FLASH_KEY2 <= 0x02030405]
[17:09:41.825]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:09:41.825]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:09:41.825]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:09:41.825]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:09:41.825]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:09:41.825]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:09:41.828]      __var FLASH_CR_Value = 0 ;
[17:09:41.828]        // -> [FLASH_CR_Value <= 0x00000000]
[17:09:41.828]      __var DoDebugPortStop = 1 ;
[17:09:41.828]        // -> [DoDebugPortStop <= 0x00000001]
[17:09:41.828]      __var DP_CTRL_STAT = 0x4 ;
[17:09:41.828]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:09:41.828]      __var DP_SELECT = 0x8 ;
[17:09:41.828]        // -> [DP_SELECT <= 0x00000008]
[17:09:41.828]    </block>
[17:09:41.828]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:09:41.830]      // if-block "connectionFlash && DoOptionByteLoading"
[17:09:41.830]        // =>  FALSE
[17:09:41.830]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:09:41.831]    </control>
[17:09:41.831]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:09:41.831]      // if-block "DoDebugPortStop"
[17:09:41.832]        // =>  TRUE
[17:09:41.832]      <block atomic="false" info="">
[17:09:41.832]        WriteDP(DP_SELECT, 0x00000000);
[17:09:41.832]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:09:41.832]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:09:41.832]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:09:41.832]      </block>
[17:09:41.832]      // end if-block "DoDebugPortStop"
[17:09:41.832]    </control>
[17:09:41.832]  </sequence>
[17:09:41.832]  
[17:09:42.915]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:09:42.915]  
[17:09:42.915]  <debugvars>
[17:09:42.915]    // Pre-defined
[17:09:42.915]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:09:42.915]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[17:09:42.915]    __dp=0x00000000
[17:09:42.917]    __ap=0x00000000
[17:09:42.917]    __traceout=0x00000000      (Trace Disabled)
[17:09:42.917]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:09:42.917]    __FlashAddr=0x00000000
[17:09:42.917]    __FlashLen=0x00000000
[17:09:42.917]    __FlashArg=0x00000000
[17:09:42.917]    __FlashOp=0x00000000
[17:09:42.917]    __Result=0x00000000
[17:09:42.917]    
[17:09:42.917]    // User-defined
[17:09:42.919]    DbgMCU_CR=0x00000007
[17:09:42.919]    DbgMCU_APB1_Fz=0x00000000
[17:09:42.919]    DbgMCU_APB2_Fz=0x00000000
[17:09:42.919]    DoOptionByteLoading=0x00000000
[17:09:42.919]  </debugvars>
[17:09:42.919]  
[17:09:42.919]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:09:42.919]    <block atomic="false" info="">
[17:09:42.919]      Sequence("CheckID");
[17:09:42.919]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:09:42.919]          <block atomic="false" info="">
[17:09:42.921]            __var pidr1 = 0;
[17:09:42.921]              // -> [pidr1 <= 0x00000000]
[17:09:42.921]            __var pidr2 = 0;
[17:09:42.921]              // -> [pidr2 <= 0x00000000]
[17:09:42.921]            __var jep106id = 0;
[17:09:42.921]              // -> [jep106id <= 0x00000000]
[17:09:42.921]            __var ROMTableBase = 0;
[17:09:42.921]              // -> [ROMTableBase <= 0x00000000]
[17:09:42.921]            __ap = 0;      // AHB-AP
[17:09:42.921]              // -> [__ap <= 0x00000000]
[17:09:42.921]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:09:42.923]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:09:42.923]              // -> [ROMTableBase <= 0xF0000000]
[17:09:42.924]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:09:42.924]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:09:42.926]              // -> [pidr1 <= 0x00000004]
[17:09:42.926]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:09:42.926]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:09:42.926]              // -> [pidr2 <= 0x0000000A]
[17:09:42.926]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:09:42.926]              // -> [jep106id <= 0x00000020]
[17:09:42.926]          </block>
[17:09:42.929]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:09:42.929]            // if-block "jep106id != 0x20"
[17:09:42.929]              // =>  FALSE
[17:09:42.929]            // skip if-block "jep106id != 0x20"
[17:09:42.929]          </control>
[17:09:42.931]        </sequence>
[17:09:42.931]    </block>
[17:09:42.931]  </sequence>
[17:09:42.932]  
[17:09:42.945]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:09:42.945]  
[17:09:42.963]  <debugvars>
[17:09:42.964]    // Pre-defined
[17:09:42.964]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:09:42.964]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[17:09:42.965]    __dp=0x00000000
[17:09:42.965]    __ap=0x00000000
[17:09:42.965]    __traceout=0x00000000      (Trace Disabled)
[17:09:42.965]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:09:42.965]    __FlashAddr=0x00000000
[17:09:42.965]    __FlashLen=0x00000000
[17:09:42.965]    __FlashArg=0x00000000
[17:09:42.965]    __FlashOp=0x00000000
[17:09:42.965]    __Result=0x00000000
[17:09:42.965]    
[17:09:42.965]    // User-defined
[17:09:42.965]    DbgMCU_CR=0x00000007
[17:09:42.965]    DbgMCU_APB1_Fz=0x00000000
[17:09:42.965]    DbgMCU_APB2_Fz=0x00000000
[17:09:42.965]    DoOptionByteLoading=0x00000000
[17:09:42.965]  </debugvars>
[17:09:42.969]  
[17:09:42.969]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:09:42.969]    <block atomic="false" info="">
[17:09:42.969]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:09:42.969]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:09:42.969]    </block>
[17:09:42.969]    <block atomic="false" info="DbgMCU registers">
[17:09:42.971]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:09:42.971]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[17:09:42.971]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[17:09:42.971]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:09:42.974]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:09:42.974]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:09:42.974]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:09:42.974]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:09:42.974]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:09:42.974]    </block>
[17:09:42.974]  </sequence>
[17:09:42.974]  
[17:33:03.397]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:33:03.397]  
[17:33:03.397]  <debugvars>
[17:33:03.397]    // Pre-defined
[17:33:03.398]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:33:03.398]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[17:33:03.398]    __dp=0x00000000
[17:33:03.398]    __ap=0x00000000
[17:33:03.399]    __traceout=0x00000000      (Trace Disabled)
[17:33:03.399]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:33:03.399]    __FlashAddr=0x00000000
[17:33:03.400]    __FlashLen=0x00000000
[17:33:03.400]    __FlashArg=0x00000000
[17:33:03.400]    __FlashOp=0x00000000
[17:33:03.402]    __Result=0x00000000
[17:33:03.402]    
[17:33:03.402]    // User-defined
[17:33:03.402]    DbgMCU_CR=0x00000007
[17:33:03.403]    DbgMCU_APB1_Fz=0x00000000
[17:33:03.403]    DbgMCU_APB2_Fz=0x00000000
[17:33:03.403]    DoOptionByteLoading=0x00000000
[17:33:03.403]  </debugvars>
[17:33:03.403]  
[17:33:03.404]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:33:03.404]    <block atomic="false" info="">
[17:33:03.404]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:33:03.404]        // -> [connectionFlash <= 0x00000000]
[17:33:03.404]      __var FLASH_BASE = 0x40022000 ;
[17:33:03.405]        // -> [FLASH_BASE <= 0x40022000]
[17:33:03.406]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:33:03.406]        // -> [FLASH_CR <= 0x40022004]
[17:33:03.406]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:33:03.406]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:33:03.406]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:33:03.406]        // -> [LOCK_BIT <= 0x00000001]
[17:33:03.406]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:33:03.407]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:33:03.407]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:33:03.407]        // -> [FLASH_KEYR <= 0x4002200C]
[17:33:03.407]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:33:03.408]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:33:03.408]      __var FLASH_KEY2 = 0x02030405 ;
[17:33:03.408]        // -> [FLASH_KEY2 <= 0x02030405]
[17:33:03.408]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:33:03.408]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:33:03.408]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:33:03.408]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:33:03.408]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:33:03.408]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:33:03.408]      __var FLASH_CR_Value = 0 ;
[17:33:03.408]        // -> [FLASH_CR_Value <= 0x00000000]
[17:33:03.410]      __var DoDebugPortStop = 1 ;
[17:33:03.410]        // -> [DoDebugPortStop <= 0x00000001]
[17:33:03.410]      __var DP_CTRL_STAT = 0x4 ;
[17:33:03.410]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:33:03.411]      __var DP_SELECT = 0x8 ;
[17:33:03.411]        // -> [DP_SELECT <= 0x00000008]
[17:33:03.411]    </block>
[17:33:03.411]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:33:03.411]      // if-block "connectionFlash && DoOptionByteLoading"
[17:33:03.412]        // =>  FALSE
[17:33:03.412]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:33:03.412]    </control>
[17:33:03.412]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:33:03.412]      // if-block "DoDebugPortStop"
[17:33:03.413]        // =>  TRUE
[17:33:03.413]      <block atomic="false" info="">
[17:33:03.413]        WriteDP(DP_SELECT, 0x00000000);
[17:33:03.414]  
[17:33:03.414]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[17:33:03.414]  
[17:33:03.414]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:33:03.415]      </block>
[17:33:03.415]      // end if-block "DoDebugPortStop"
[17:33:03.415]    </control>
[17:33:03.415]  </sequence>
[17:33:03.415]  
[17:35:04.523]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:35:04.523]  
[17:35:04.523]  <debugvars>
[17:35:04.523]    // Pre-defined
[17:35:04.524]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:35:04.524]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:35:04.524]    __dp=0x00000000
[17:35:04.524]    __ap=0x00000000
[17:35:04.524]    __traceout=0x00000000      (Trace Disabled)
[17:35:04.525]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:35:04.526]    __FlashAddr=0x00000000
[17:35:04.526]    __FlashLen=0x00000000
[17:35:04.526]    __FlashArg=0x00000000
[17:35:04.526]    __FlashOp=0x00000000
[17:35:04.527]    __Result=0x00000000
[17:35:04.527]    
[17:35:04.527]    // User-defined
[17:35:04.527]    DbgMCU_CR=0x00000007
[17:35:04.528]    DbgMCU_APB1_Fz=0x00000000
[17:35:04.528]    DbgMCU_APB2_Fz=0x00000000
[17:35:04.528]    DoOptionByteLoading=0x00000000
[17:35:04.528]  </debugvars>
[17:35:04.529]  
[17:35:04.529]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:35:04.530]    <block atomic="false" info="">
[17:35:04.530]      Sequence("CheckID");
[17:35:04.530]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:35:04.530]          <block atomic="false" info="">
[17:35:04.531]            __var pidr1 = 0;
[17:35:04.531]              // -> [pidr1 <= 0x00000000]
[17:35:04.531]            __var pidr2 = 0;
[17:35:04.531]              // -> [pidr2 <= 0x00000000]
[17:35:04.532]            __var jep106id = 0;
[17:35:04.532]              // -> [jep106id <= 0x00000000]
[17:35:04.532]            __var ROMTableBase = 0;
[17:35:04.532]              // -> [ROMTableBase <= 0x00000000]
[17:35:04.532]            __ap = 0;      // AHB-AP
[17:35:04.533]              // -> [__ap <= 0x00000000]
[17:35:04.533]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:35:04.533]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:35:04.534]              // -> [ROMTableBase <= 0xF0000000]
[17:35:04.534]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:35:04.535]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:35:04.536]              // -> [pidr1 <= 0x00000004]
[17:35:04.536]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:35:04.538]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:35:04.538]              // -> [pidr2 <= 0x0000000A]
[17:35:04.538]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:35:04.539]              // -> [jep106id <= 0x00000020]
[17:35:04.539]          </block>
[17:35:04.539]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:35:04.539]            // if-block "jep106id != 0x20"
[17:35:04.539]              // =>  FALSE
[17:35:04.539]            // skip if-block "jep106id != 0x20"
[17:35:04.540]          </control>
[17:35:04.541]        </sequence>
[17:35:04.541]    </block>
[17:35:04.541]  </sequence>
[17:35:04.542]  
[17:35:04.557]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:35:04.557]  
[17:35:04.557]  <debugvars>
[17:35:04.557]    // Pre-defined
[17:35:04.557]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:35:04.558]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:35:04.558]    __dp=0x00000000
[17:35:04.558]    __ap=0x00000000
[17:35:04.558]    __traceout=0x00000000      (Trace Disabled)
[17:35:04.558]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:35:04.558]    __FlashAddr=0x00000000
[17:35:04.558]    __FlashLen=0x00000000
[17:35:04.558]    __FlashArg=0x00000000
[17:35:04.559]    __FlashOp=0x00000000
[17:35:04.559]    __Result=0x00000000
[17:35:04.559]    
[17:35:04.559]    // User-defined
[17:35:04.559]    DbgMCU_CR=0x00000007
[17:35:04.560]    DbgMCU_APB1_Fz=0x00000000
[17:35:04.560]    DbgMCU_APB2_Fz=0x00000000
[17:35:04.560]    DoOptionByteLoading=0x00000000
[17:35:04.560]  </debugvars>
[17:35:04.561]  
[17:35:04.562]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:35:04.562]    <block atomic="false" info="">
[17:35:04.562]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:35:04.562]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:35:04.562]    </block>
[17:35:04.562]    <block atomic="false" info="DbgMCU registers">
[17:35:04.562]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:35:04.562]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[17:35:04.562]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[17:35:04.562]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:35:04.566]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:35:04.566]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:35:04.567]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:35:04.568]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:35:04.569]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:35:04.569]    </block>
[17:35:04.569]  </sequence>
[17:35:04.569]  
[17:35:12.734]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:35:12.734]  
[17:35:12.734]  <debugvars>
[17:35:12.734]    // Pre-defined
[17:35:12.734]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:35:12.734]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:35:12.734]    __dp=0x00000000
[17:35:12.734]    __ap=0x00000000
[17:35:12.734]    __traceout=0x00000000      (Trace Disabled)
[17:35:12.738]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:35:12.738]    __FlashAddr=0x00000000
[17:35:12.738]    __FlashLen=0x00000000
[17:35:12.738]    __FlashArg=0x00000000
[17:35:12.738]    __FlashOp=0x00000000
[17:35:12.738]    __Result=0x00000000
[17:35:12.738]    
[17:35:12.738]    // User-defined
[17:35:12.738]    DbgMCU_CR=0x00000007
[17:35:12.738]    DbgMCU_APB1_Fz=0x00000000
[17:35:12.738]    DbgMCU_APB2_Fz=0x00000000
[17:35:12.738]    DoOptionByteLoading=0x00000000
[17:35:12.738]  </debugvars>
[17:35:12.738]  
[17:35:12.738]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:35:12.738]    <block atomic="false" info="">
[17:35:12.738]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:35:12.738]        // -> [connectionFlash <= 0x00000001]
[17:35:12.738]      __var FLASH_BASE = 0x40022000 ;
[17:35:12.738]        // -> [FLASH_BASE <= 0x40022000]
[17:35:12.738]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:35:12.738]        // -> [FLASH_CR <= 0x40022004]
[17:35:12.738]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:35:12.738]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:35:12.738]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:35:12.738]        // -> [LOCK_BIT <= 0x00000001]
[17:35:12.743]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:35:12.743]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:35:12.743]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:35:12.743]        // -> [FLASH_KEYR <= 0x4002200C]
[17:35:12.743]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:35:12.743]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:35:12.743]      __var FLASH_KEY2 = 0x02030405 ;
[17:35:12.743]        // -> [FLASH_KEY2 <= 0x02030405]
[17:35:12.743]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:35:12.743]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:35:12.743]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:35:12.743]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:35:12.743]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:35:12.743]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:35:12.743]      __var FLASH_CR_Value = 0 ;
[17:35:12.743]        // -> [FLASH_CR_Value <= 0x00000000]
[17:35:12.743]      __var DoDebugPortStop = 1 ;
[17:35:12.743]        // -> [DoDebugPortStop <= 0x00000001]
[17:35:12.743]      __var DP_CTRL_STAT = 0x4 ;
[17:35:12.743]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:35:12.743]      __var DP_SELECT = 0x8 ;
[17:35:12.743]        // -> [DP_SELECT <= 0x00000008]
[17:35:12.743]    </block>
[17:35:12.743]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:35:12.743]      // if-block "connectionFlash && DoOptionByteLoading"
[17:35:12.748]        // =>  FALSE
[17:35:12.748]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:35:12.748]    </control>
[17:35:12.750]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:35:12.750]      // if-block "DoDebugPortStop"
[17:35:12.750]        // =>  TRUE
[17:35:12.751]      <block atomic="false" info="">
[17:35:12.751]        WriteDP(DP_SELECT, 0x00000000);
[17:35:12.751]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:35:12.753]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:35:12.753]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:35:12.753]      </block>
[17:35:12.753]      // end if-block "DoDebugPortStop"
[17:35:12.755]    </control>
[17:35:12.755]  </sequence>
[17:35:12.755]  
[17:37:13.869]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:37:13.869]  
[17:37:13.870]  <debugvars>
[17:37:13.870]    // Pre-defined
[17:37:13.870]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:37:13.870]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:37:13.871]    __dp=0x00000000
[17:37:13.871]    __ap=0x00000000
[17:37:13.871]    __traceout=0x00000000      (Trace Disabled)
[17:37:13.872]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:37:13.872]    __FlashAddr=0x00000000
[17:37:13.872]    __FlashLen=0x00000000
[17:37:13.872]    __FlashArg=0x00000000
[17:37:13.873]    __FlashOp=0x00000000
[17:37:13.873]    __Result=0x00000000
[17:37:13.873]    
[17:37:13.873]    // User-defined
[17:37:13.873]    DbgMCU_CR=0x00000007
[17:37:13.874]    DbgMCU_APB1_Fz=0x00000000
[17:37:13.874]    DbgMCU_APB2_Fz=0x00000000
[17:37:13.874]    DoOptionByteLoading=0x00000000
[17:37:13.874]  </debugvars>
[17:37:13.875]  
[17:37:13.875]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:37:13.875]    <block atomic="false" info="">
[17:37:13.875]      Sequence("CheckID");
[17:37:13.875]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:37:13.875]          <block atomic="false" info="">
[17:37:13.876]            __var pidr1 = 0;
[17:37:13.876]              // -> [pidr1 <= 0x00000000]
[17:37:13.876]            __var pidr2 = 0;
[17:37:13.876]              // -> [pidr2 <= 0x00000000]
[17:37:13.877]            __var jep106id = 0;
[17:37:13.877]              // -> [jep106id <= 0x00000000]
[17:37:13.877]            __var ROMTableBase = 0;
[17:37:13.877]              // -> [ROMTableBase <= 0x00000000]
[17:37:13.877]            __ap = 0;      // AHB-AP
[17:37:13.877]              // -> [__ap <= 0x00000000]
[17:37:13.877]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:37:13.877]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:37:13.879]              // -> [ROMTableBase <= 0xF0000000]
[17:37:13.879]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:37:13.879]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:37:13.879]              // -> [pidr1 <= 0x00000004]
[17:37:13.879]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:37:13.879]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:37:13.879]              // -> [pidr2 <= 0x0000000A]
[17:37:13.879]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:37:13.879]              // -> [jep106id <= 0x00000020]
[17:37:13.879]          </block>
[17:37:13.879]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:37:13.883]            // if-block "jep106id != 0x20"
[17:37:13.884]              // =>  FALSE
[17:37:13.884]            // skip if-block "jep106id != 0x20"
[17:37:13.884]          </control>
[17:37:13.884]        </sequence>
[17:37:13.885]    </block>
[17:37:13.885]  </sequence>
[17:37:13.885]  
[17:37:13.898]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:37:13.898]  
[17:37:13.899]  <debugvars>
[17:37:13.899]    // Pre-defined
[17:37:13.899]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:37:13.899]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:37:13.899]    __dp=0x00000000
[17:37:13.900]    __ap=0x00000000
[17:37:13.900]    __traceout=0x00000000      (Trace Disabled)
[17:37:13.900]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:37:13.900]    __FlashAddr=0x00000000
[17:37:13.901]    __FlashLen=0x00000000
[17:37:13.901]    __FlashArg=0x00000000
[17:37:13.901]    __FlashOp=0x00000000
[17:37:13.902]    __Result=0x00000000
[17:37:13.902]    
[17:37:13.902]    // User-defined
[17:37:13.902]    DbgMCU_CR=0x00000007
[17:37:13.902]    DbgMCU_APB1_Fz=0x00000000
[17:37:13.902]    DbgMCU_APB2_Fz=0x00000000
[17:37:13.903]    DoOptionByteLoading=0x00000000
[17:37:13.903]  </debugvars>
[17:37:13.903]  
[17:37:13.903]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:37:13.903]    <block atomic="false" info="">
[17:37:13.903]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:37:13.904]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:37:13.905]    </block>
[17:37:13.905]    <block atomic="false" info="DbgMCU registers">
[17:37:13.905]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:37:13.906]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[17:37:13.907]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[17:37:13.907]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:37:13.908]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:37:13.909]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:37:13.910]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:37:13.910]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:37:13.911]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:37:13.911]    </block>
[17:37:13.911]  </sequence>
[17:37:13.912]  
[17:37:22.110]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:37:22.110]  
[17:37:22.110]  <debugvars>
[17:37:22.111]    // Pre-defined
[17:37:22.112]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:37:22.112]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:37:22.112]    __dp=0x00000000
[17:37:22.113]    __ap=0x00000000
[17:37:22.114]    __traceout=0x00000000      (Trace Disabled)
[17:37:22.114]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:37:22.115]    __FlashAddr=0x00000000
[17:37:22.115]    __FlashLen=0x00000000
[17:37:22.115]    __FlashArg=0x00000000
[17:37:22.116]    __FlashOp=0x00000000
[17:37:22.116]    __Result=0x00000000
[17:37:22.117]    
[17:37:22.117]    // User-defined
[17:37:22.117]    DbgMCU_CR=0x00000007
[17:37:22.117]    DbgMCU_APB1_Fz=0x00000000
[17:37:22.118]    DbgMCU_APB2_Fz=0x00000000
[17:37:22.118]    DoOptionByteLoading=0x00000000
[17:37:22.118]  </debugvars>
[17:37:22.118]  
[17:37:22.118]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:37:22.118]    <block atomic="false" info="">
[17:37:22.118]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:37:22.118]        // -> [connectionFlash <= 0x00000001]
[17:37:22.118]      __var FLASH_BASE = 0x40022000 ;
[17:37:22.118]        // -> [FLASH_BASE <= 0x40022000]
[17:37:22.118]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:37:22.118]        // -> [FLASH_CR <= 0x40022004]
[17:37:22.118]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:37:22.118]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:37:22.118]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:37:22.118]        // -> [LOCK_BIT <= 0x00000001]
[17:37:22.122]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:37:22.122]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:37:22.123]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:37:22.123]        // -> [FLASH_KEYR <= 0x4002200C]
[17:37:22.123]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:37:22.123]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:37:22.123]      __var FLASH_KEY2 = 0x02030405 ;
[17:37:22.123]        // -> [FLASH_KEY2 <= 0x02030405]
[17:37:22.123]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:37:22.123]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:37:22.123]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:37:22.123]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:37:22.123]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:37:22.123]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:37:22.123]      __var FLASH_CR_Value = 0 ;
[17:37:22.123]        // -> [FLASH_CR_Value <= 0x00000000]
[17:37:22.123]      __var DoDebugPortStop = 1 ;
[17:37:22.123]        // -> [DoDebugPortStop <= 0x00000001]
[17:37:22.123]      __var DP_CTRL_STAT = 0x4 ;
[17:37:22.128]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:37:22.128]      __var DP_SELECT = 0x8 ;
[17:37:22.128]        // -> [DP_SELECT <= 0x00000008]
[17:37:22.128]    </block>
[17:37:22.128]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:37:22.128]      // if-block "connectionFlash && DoOptionByteLoading"
[17:37:22.128]        // =>  FALSE
[17:37:22.128]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:37:22.128]    </control>
[17:37:22.128]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:37:22.128]      // if-block "DoDebugPortStop"
[17:37:22.128]        // =>  TRUE
[17:37:22.128]      <block atomic="false" info="">
[17:37:22.128]        WriteDP(DP_SELECT, 0x00000000);
[17:37:22.128]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:37:22.128]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:37:22.128]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:37:22.128]      </block>
[17:37:22.133]      // end if-block "DoDebugPortStop"
[17:37:22.133]    </control>
[17:37:22.133]  </sequence>
[17:37:22.134]  
[17:39:48.479]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:39:48.479]  
[17:39:48.479]  <debugvars>
[17:39:48.479]    // Pre-defined
[17:39:48.479]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:39:48.479]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:39:48.479]    __dp=0x00000000
[17:39:48.479]    __ap=0x00000000
[17:39:48.479]    __traceout=0x00000000      (Trace Disabled)
[17:39:48.479]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:39:48.479]    __FlashAddr=0x00000000
[17:39:48.479]    __FlashLen=0x00000000
[17:39:48.479]    __FlashArg=0x00000000
[17:39:48.479]    __FlashOp=0x00000000
[17:39:48.483]    __Result=0x00000000
[17:39:48.483]    
[17:39:48.483]    // User-defined
[17:39:48.483]    DbgMCU_CR=0x00000007
[17:39:48.483]    DbgMCU_APB1_Fz=0x00000000
[17:39:48.484]    DbgMCU_APB2_Fz=0x00000000
[17:39:48.484]    DoOptionByteLoading=0x00000000
[17:39:48.484]  </debugvars>
[17:39:48.484]  
[17:39:48.484]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:39:48.485]    <block atomic="false" info="">
[17:39:48.485]      Sequence("CheckID");
[17:39:48.485]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:39:48.485]          <block atomic="false" info="">
[17:39:48.485]            __var pidr1 = 0;
[17:39:48.486]              // -> [pidr1 <= 0x00000000]
[17:39:48.486]            __var pidr2 = 0;
[17:39:48.486]              // -> [pidr2 <= 0x00000000]
[17:39:48.486]            __var jep106id = 0;
[17:39:48.486]              // -> [jep106id <= 0x00000000]
[17:39:48.487]            __var ROMTableBase = 0;
[17:39:48.487]              // -> [ROMTableBase <= 0x00000000]
[17:39:48.487]            __ap = 0;      // AHB-AP
[17:39:48.487]              // -> [__ap <= 0x00000000]
[17:39:48.487]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:39:48.488]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:39:48.488]              // -> [ROMTableBase <= 0xF0000000]
[17:39:48.488]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:39:48.489]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:39:48.490]              // -> [pidr1 <= 0x00000004]
[17:39:48.490]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:39:48.491]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:39:48.491]              // -> [pidr2 <= 0x0000000A]
[17:39:48.492]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:39:48.492]              // -> [jep106id <= 0x00000020]
[17:39:48.492]          </block>
[17:39:48.492]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:39:48.492]            // if-block "jep106id != 0x20"
[17:39:48.493]              // =>  FALSE
[17:39:48.493]            // skip if-block "jep106id != 0x20"
[17:39:48.493]          </control>
[17:39:48.493]        </sequence>
[17:39:48.493]    </block>
[17:39:48.494]  </sequence>
[17:39:48.494]  
[17:39:48.508]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:39:48.508]  
[17:39:48.523]  <debugvars>
[17:39:48.523]    // Pre-defined
[17:39:48.523]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:39:48.523]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:39:48.523]    __dp=0x00000000
[17:39:48.524]    __ap=0x00000000
[17:39:48.524]    __traceout=0x00000000      (Trace Disabled)
[17:39:48.524]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:39:48.524]    __FlashAddr=0x00000000
[17:39:48.524]    __FlashLen=0x00000000
[17:39:48.525]    __FlashArg=0x00000000
[17:39:48.525]    __FlashOp=0x00000000
[17:39:48.525]    __Result=0x00000000
[17:39:48.525]    
[17:39:48.525]    // User-defined
[17:39:48.525]    DbgMCU_CR=0x00000007
[17:39:48.526]    DbgMCU_APB1_Fz=0x00000000
[17:39:48.526]    DbgMCU_APB2_Fz=0x00000000
[17:39:48.526]    DoOptionByteLoading=0x00000000
[17:39:48.526]  </debugvars>
[17:39:48.527]  
[17:39:48.527]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:39:48.527]    <block atomic="false" info="">
[17:39:48.527]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:39:48.528]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:39:48.529]    </block>
[17:39:48.529]    <block atomic="false" info="DbgMCU registers">
[17:39:48.529]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:39:48.530]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[17:39:48.532]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[17:39:48.532]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:39:48.534]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:39:48.534]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:39:48.534]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:39:48.536]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:39:48.536]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:39:48.537]    </block>
[17:39:48.537]  </sequence>
[17:39:48.537]  
[17:39:56.675]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:39:56.675]  
[17:39:56.675]  <debugvars>
[17:39:56.676]    // Pre-defined
[17:39:56.676]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:39:56.676]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:39:56.677]    __dp=0x00000000
[17:39:56.677]    __ap=0x00000000
[17:39:56.677]    __traceout=0x00000000      (Trace Disabled)
[17:39:56.678]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:39:56.678]    __FlashAddr=0x00000000
[17:39:56.679]    __FlashLen=0x00000000
[17:39:56.679]    __FlashArg=0x00000000
[17:39:56.679]    __FlashOp=0x00000000
[17:39:56.679]    __Result=0x00000000
[17:39:56.680]    
[17:39:56.680]    // User-defined
[17:39:56.680]    DbgMCU_CR=0x00000007
[17:39:56.680]    DbgMCU_APB1_Fz=0x00000000
[17:39:56.680]    DbgMCU_APB2_Fz=0x00000000
[17:39:56.681]    DoOptionByteLoading=0x00000000
[17:39:56.681]  </debugvars>
[17:39:56.681]  
[17:39:56.682]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:39:56.682]    <block atomic="false" info="">
[17:39:56.682]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:39:56.683]        // -> [connectionFlash <= 0x00000001]
[17:39:56.683]      __var FLASH_BASE = 0x40022000 ;
[17:39:56.683]        // -> [FLASH_BASE <= 0x40022000]
[17:39:56.684]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:39:56.684]        // -> [FLASH_CR <= 0x40022004]
[17:39:56.684]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:39:56.684]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:39:56.685]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:39:56.685]        // -> [LOCK_BIT <= 0x00000001]
[17:39:56.685]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:39:56.685]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:39:56.685]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:39:56.686]        // -> [FLASH_KEYR <= 0x4002200C]
[17:39:56.686]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:39:56.686]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:39:56.686]      __var FLASH_KEY2 = 0x02030405 ;
[17:39:56.687]        // -> [FLASH_KEY2 <= 0x02030405]
[17:39:56.687]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:39:56.687]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:39:56.687]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:39:56.688]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:39:56.688]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:39:56.688]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:39:56.688]      __var FLASH_CR_Value = 0 ;
[17:39:56.688]        // -> [FLASH_CR_Value <= 0x00000000]
[17:39:56.689]      __var DoDebugPortStop = 1 ;
[17:39:56.689]        // -> [DoDebugPortStop <= 0x00000001]
[17:39:56.689]      __var DP_CTRL_STAT = 0x4 ;
[17:39:56.690]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:39:56.690]      __var DP_SELECT = 0x8 ;
[17:39:56.690]        // -> [DP_SELECT <= 0x00000008]
[17:39:56.690]    </block>
[17:39:56.690]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:39:56.691]      // if-block "connectionFlash && DoOptionByteLoading"
[17:39:56.691]        // =>  FALSE
[17:39:56.691]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:39:56.692]    </control>
[17:39:56.692]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:39:56.692]      // if-block "DoDebugPortStop"
[17:39:56.692]        // =>  TRUE
[17:39:56.692]      <block atomic="false" info="">
[17:39:56.693]        WriteDP(DP_SELECT, 0x00000000);
[17:39:56.693]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:39:56.693]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:39:56.694]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:39:56.694]      </block>
[17:39:56.694]      // end if-block "DoDebugPortStop"
[17:39:56.695]    </control>
[17:39:56.695]  </sequence>
[17:39:56.695]  
[17:55:46.565]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:55:46.565]  
[17:55:46.565]  <debugvars>
[17:55:46.567]    // Pre-defined
[17:55:46.567]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:55:46.567]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:55:46.567]    __dp=0x00000000
[17:55:46.567]    __ap=0x00000000
[17:55:46.568]    __traceout=0x00000000      (Trace Disabled)
[17:55:46.568]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:55:46.568]    __FlashAddr=0x00000000
[17:55:46.568]    __FlashLen=0x00000000
[17:55:46.569]    __FlashArg=0x00000000
[17:55:46.569]    __FlashOp=0x00000000
[17:55:46.569]    __Result=0x00000000
[17:55:46.569]    
[17:55:46.569]    // User-defined
[17:55:46.569]    DbgMCU_CR=0x00000007
[17:55:46.570]    DbgMCU_APB1_Fz=0x00000000
[17:55:46.570]    DbgMCU_APB2_Fz=0x00000000
[17:55:46.570]    DoOptionByteLoading=0x00000000
[17:55:46.570]  </debugvars>
[17:55:46.571]  
[17:55:46.571]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:55:46.571]    <block atomic="false" info="">
[17:55:46.572]      Sequence("CheckID");
[17:55:46.572]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:55:46.573]          <block atomic="false" info="">
[17:55:46.573]            __var pidr1 = 0;
[17:55:46.573]              // -> [pidr1 <= 0x00000000]
[17:55:46.573]            __var pidr2 = 0;
[17:55:46.574]              // -> [pidr2 <= 0x00000000]
[17:55:46.574]            __var jep106id = 0;
[17:55:46.574]              // -> [jep106id <= 0x00000000]
[17:55:46.574]            __var ROMTableBase = 0;
[17:55:46.575]              // -> [ROMTableBase <= 0x00000000]
[17:55:46.575]            __ap = 0;      // AHB-AP
[17:55:46.575]              // -> [__ap <= 0x00000000]
[17:55:46.575]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:55:46.576]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:55:46.577]              // -> [ROMTableBase <= 0xF0000000]
[17:55:46.577]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:55:46.578]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:55:46.578]              // -> [pidr1 <= 0x00000004]
[17:55:46.578]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:55:46.580]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:55:46.580]              // -> [pidr2 <= 0x0000000A]
[17:55:46.581]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:55:46.581]              // -> [jep106id <= 0x00000020]
[17:55:46.581]          </block>
[17:55:46.581]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:55:46.582]            // if-block "jep106id != 0x20"
[17:55:46.582]              // =>  FALSE
[17:55:46.582]            // skip if-block "jep106id != 0x20"
[17:55:46.582]          </control>
[17:55:46.583]        </sequence>
[17:55:46.583]    </block>
[17:55:46.583]  </sequence>
[17:55:46.583]  
[17:55:46.597]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:55:46.597]  
[17:55:46.601]  <debugvars>
[17:55:46.615]    // Pre-defined
[17:55:46.615]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:55:46.615]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:55:46.615]    __dp=0x00000000
[17:55:46.615]    __ap=0x00000000
[17:55:46.616]    __traceout=0x00000000      (Trace Disabled)
[17:55:46.616]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:55:46.616]    __FlashAddr=0x00000000
[17:55:46.616]    __FlashLen=0x00000000
[17:55:46.617]    __FlashArg=0x00000000
[17:55:46.617]    __FlashOp=0x00000000
[17:55:46.617]    __Result=0x00000000
[17:55:46.617]    
[17:55:46.617]    // User-defined
[17:55:46.618]    DbgMCU_CR=0x00000007
[17:55:46.618]    DbgMCU_APB1_Fz=0x00000000
[17:55:46.619]    DbgMCU_APB2_Fz=0x00000000
[17:55:46.619]    DoOptionByteLoading=0x00000000
[17:55:46.619]  </debugvars>
[17:55:46.619]  
[17:55:46.619]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:55:46.620]    <block atomic="false" info="">
[17:55:46.620]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:55:46.621]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:55:46.621]    </block>
[17:55:46.621]    <block atomic="false" info="DbgMCU registers">
[17:55:46.621]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:55:46.622]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[17:55:46.623]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[17:55:46.625]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:55:46.625]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:55:46.626]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:55:46.627]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:55:46.627]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:55:46.628]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:55:46.628]    </block>
[17:55:46.628]  </sequence>
[17:55:46.628]  
[17:55:54.859]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:55:54.859]  
[17:55:54.860]  <debugvars>
[17:55:54.861]    // Pre-defined
[17:55:54.861]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:55:54.861]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:55:54.861]    __dp=0x00000000
[17:55:54.862]    __ap=0x00000000
[17:55:54.862]    __traceout=0x00000000      (Trace Disabled)
[17:55:54.862]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:55:54.862]    __FlashAddr=0x00000000
[17:55:54.862]    __FlashLen=0x00000000
[17:55:54.862]    __FlashArg=0x00000000
[17:55:54.862]    __FlashOp=0x00000000
[17:55:54.862]    __Result=0x00000000
[17:55:54.862]    
[17:55:54.862]    // User-defined
[17:55:54.862]    DbgMCU_CR=0x00000007
[17:55:54.865]    DbgMCU_APB1_Fz=0x00000000
[17:55:54.866]    DbgMCU_APB2_Fz=0x00000000
[17:55:54.866]    DoOptionByteLoading=0x00000000
[17:55:54.866]  </debugvars>
[17:55:54.867]  
[17:55:54.867]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:55:54.867]    <block atomic="false" info="">
[17:55:54.867]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:55:54.867]        // -> [connectionFlash <= 0x00000001]
[17:55:54.868]      __var FLASH_BASE = 0x40022000 ;
[17:55:54.868]        // -> [FLASH_BASE <= 0x40022000]
[17:55:54.868]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:55:54.868]        // -> [FLASH_CR <= 0x40022004]
[17:55:54.868]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:55:54.869]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:55:54.869]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:55:54.869]        // -> [LOCK_BIT <= 0x00000001]
[17:55:54.870]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:55:54.870]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:55:54.870]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:55:54.870]        // -> [FLASH_KEYR <= 0x4002200C]
[17:55:54.870]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:55:54.871]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:55:54.871]      __var FLASH_KEY2 = 0x02030405 ;
[17:55:54.872]        // -> [FLASH_KEY2 <= 0x02030405]
[17:55:54.872]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:55:54.872]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:55:54.873]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:55:54.873]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:55:54.873]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:55:54.873]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:55:54.873]      __var FLASH_CR_Value = 0 ;
[17:55:54.873]        // -> [FLASH_CR_Value <= 0x00000000]
[17:55:54.873]      __var DoDebugPortStop = 1 ;
[17:55:54.874]        // -> [DoDebugPortStop <= 0x00000001]
[17:55:54.874]      __var DP_CTRL_STAT = 0x4 ;
[17:55:54.875]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:55:54.875]      __var DP_SELECT = 0x8 ;
[17:55:54.875]        // -> [DP_SELECT <= 0x00000008]
[17:55:54.876]    </block>
[17:55:54.876]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:55:54.876]      // if-block "connectionFlash && DoOptionByteLoading"
[17:55:54.877]        // =>  FALSE
[17:55:54.877]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:55:54.877]    </control>
[17:55:54.877]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:55:54.877]      // if-block "DoDebugPortStop"
[17:55:54.877]        // =>  TRUE
[17:55:54.878]      <block atomic="false" info="">
[17:55:54.878]        WriteDP(DP_SELECT, 0x00000000);
[17:55:54.879]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:55:54.879]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:55:54.880]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:55:54.880]      </block>
[17:55:54.880]      // end if-block "DoDebugPortStop"
[17:55:54.880]    </control>
[17:55:54.880]  </sequence>
[17:55:54.881]  
[18:24:13.840]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[18:24:13.840]  
[18:24:13.840]  <debugvars>
[18:24:13.840]    // Pre-defined
[18:24:13.840]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[18:24:13.841]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[18:24:13.841]    __dp=0x00000000
[18:24:13.841]    __ap=0x00000000
[18:24:13.841]    __traceout=0x00000000      (Trace Disabled)
[18:24:13.841]    __errorcontrol=0x00000000  (Skip Errors="False")
[18:24:13.842]    __FlashAddr=0x00000000
[18:24:13.842]    __FlashLen=0x00000000
[18:24:13.842]    __FlashArg=0x00000000
[18:24:13.843]    __FlashOp=0x00000000
[18:24:13.843]    __Result=0x00000000
[18:24:13.843]    
[18:24:13.843]    // User-defined
[18:24:13.843]    DbgMCU_CR=0x00000007
[18:24:13.843]    DbgMCU_APB1_Fz=0x00000000
[18:24:13.844]    DbgMCU_APB2_Fz=0x00000000
[18:24:13.844]    DoOptionByteLoading=0x00000000
[18:24:13.844]  </debugvars>
[18:24:13.844]  
[18:24:13.844]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[18:24:13.844]    <block atomic="false" info="">
[18:24:13.845]      Sequence("CheckID");
[18:24:13.845]        <sequence name="CheckID" Pname="" disable="false" info="">
[18:24:13.845]          <block atomic="false" info="">
[18:24:13.845]            __var pidr1 = 0;
[18:24:13.845]              // -> [pidr1 <= 0x00000000]
[18:24:13.846]            __var pidr2 = 0;
[18:24:13.846]              // -> [pidr2 <= 0x00000000]
[18:24:13.846]            __var jep106id = 0;
[18:24:13.846]              // -> [jep106id <= 0x00000000]
[18:24:13.846]            __var ROMTableBase = 0;
[18:24:13.847]              // -> [ROMTableBase <= 0x00000000]
[18:24:13.847]            __ap = 0;      // AHB-AP
[18:24:13.847]              // -> [__ap <= 0x00000000]
[18:24:13.847]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[18:24:13.850]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[18:24:13.850]              // -> [ROMTableBase <= 0xF0000000]
[18:24:13.850]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[18:24:13.853]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[18:24:13.853]              // -> [pidr1 <= 0x00000004]
[18:24:13.854]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[18:24:13.856]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[18:24:13.856]              // -> [pidr2 <= 0x0000000A]
[18:24:13.857]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[18:24:13.857]              // -> [jep106id <= 0x00000020]
[18:24:13.857]          </block>
[18:24:13.857]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[18:24:13.857]            // if-block "jep106id != 0x20"
[18:24:13.857]              // =>  FALSE
[18:24:13.858]            // skip if-block "jep106id != 0x20"
[18:24:13.858]          </control>
[18:24:13.859]        </sequence>
[18:24:13.859]    </block>
[18:24:13.859]  </sequence>
[18:24:13.859]  
[18:24:13.897]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[18:24:13.897]  
[18:24:13.899]  <debugvars>
[18:24:13.899]    // Pre-defined
[18:24:13.899]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[18:24:13.900]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[18:24:13.900]    __dp=0x00000000
[18:24:13.900]    __ap=0x00000000
[18:24:13.901]    __traceout=0x00000000      (Trace Disabled)
[18:24:13.901]    __errorcontrol=0x00000000  (Skip Errors="False")
[18:24:13.901]    __FlashAddr=0x00000000
[18:24:13.901]    __FlashLen=0x00000000
[18:24:13.901]    __FlashArg=0x00000000
[18:24:13.902]    __FlashOp=0x00000000
[18:24:13.902]    __Result=0x00000000
[18:24:13.902]    
[18:24:13.902]    // User-defined
[18:24:13.902]    DbgMCU_CR=0x00000007
[18:24:13.902]    DbgMCU_APB1_Fz=0x00000000
[18:24:13.903]    DbgMCU_APB2_Fz=0x00000000
[18:24:13.903]    DoOptionByteLoading=0x00000000
[18:24:13.903]  </debugvars>
[18:24:13.903]  
[18:24:13.903]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[18:24:13.904]    <block atomic="false" info="">
[18:24:13.904]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[18:24:13.906]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[18:24:13.906]    </block>
[18:24:13.906]    <block atomic="false" info="DbgMCU registers">
[18:24:13.906]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[18:24:13.909]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[18:24:13.912]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[18:24:13.913]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[18:24:13.914]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[18:24:13.914]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[18:24:13.916]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[18:24:13.916]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[18:24:13.919]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[18:24:13.920]    </block>
[18:24:13.920]  </sequence>
[18:24:13.920]  
[18:24:24.884]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[18:24:24.884]  
[18:24:24.884]  <debugvars>
[18:24:24.885]    // Pre-defined
[18:24:24.885]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[18:24:24.886]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[18:24:24.886]    __dp=0x00000000
[18:24:24.886]    __ap=0x00000000
[18:24:24.887]    __traceout=0x00000000      (Trace Disabled)
[18:24:24.887]    __errorcontrol=0x00000000  (Skip Errors="False")
[18:24:24.887]    __FlashAddr=0x00000000
[18:24:24.888]    __FlashLen=0x00000000
[18:24:24.888]    __FlashArg=0x00000000
[18:24:24.889]    __FlashOp=0x00000000
[18:24:24.889]    __Result=0x00000000
[18:24:24.889]    
[18:24:24.889]    // User-defined
[18:24:24.889]    DbgMCU_CR=0x00000007
[18:24:24.889]    DbgMCU_APB1_Fz=0x00000000
[18:24:24.890]    DbgMCU_APB2_Fz=0x00000000
[18:24:24.890]    DoOptionByteLoading=0x00000000
[18:24:24.890]  </debugvars>
[18:24:24.890]  
[18:24:24.891]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[18:24:24.891]    <block atomic="false" info="">
[18:24:24.891]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[18:24:24.891]        // -> [connectionFlash <= 0x00000001]
[18:24:24.892]      __var FLASH_BASE = 0x40022000 ;
[18:24:24.892]        // -> [FLASH_BASE <= 0x40022000]
[18:24:24.892]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[18:24:24.892]        // -> [FLASH_CR <= 0x40022004]
[18:24:24.892]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[18:24:24.893]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[18:24:24.893]      __var LOCK_BIT = ( 1 << 0 ) ;
[18:24:24.894]        // -> [LOCK_BIT <= 0x00000001]
[18:24:24.894]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[18:24:24.894]        // -> [OPTLOCK_BIT <= 0x00000004]
[18:24:24.894]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[18:24:24.894]        // -> [FLASH_KEYR <= 0x4002200C]
[18:24:24.894]      __var FLASH_KEY1 = 0x89ABCDEF ;
[18:24:24.894]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[18:24:24.894]      __var FLASH_KEY2 = 0x02030405 ;
[18:24:24.894]        // -> [FLASH_KEY2 <= 0x02030405]
[18:24:24.894]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[18:24:24.894]        // -> [FLASH_OPTKEYR <= 0x40022014]
[18:24:24.894]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[18:24:24.896]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[18:24:24.897]      __var FLASH_OPTKEY2 = 0x24252627 ;
[18:24:24.897]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[18:24:24.898]      __var FLASH_CR_Value = 0 ;
[18:24:24.898]        // -> [FLASH_CR_Value <= 0x00000000]
[18:24:24.898]      __var DoDebugPortStop = 1 ;
[18:24:24.899]        // -> [DoDebugPortStop <= 0x00000001]
[18:24:24.899]      __var DP_CTRL_STAT = 0x4 ;
[18:24:24.899]        // -> [DP_CTRL_STAT <= 0x00000004]
[18:24:24.899]      __var DP_SELECT = 0x8 ;
[18:24:24.900]        // -> [DP_SELECT <= 0x00000008]
[18:24:24.900]    </block>
[18:24:24.900]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[18:24:24.900]      // if-block "connectionFlash && DoOptionByteLoading"
[18:24:24.900]        // =>  FALSE
[18:24:24.900]      // skip if-block "connectionFlash && DoOptionByteLoading"
[18:24:24.900]    </control>
[18:24:24.901]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[18:24:24.901]      // if-block "DoDebugPortStop"
[18:24:24.902]        // =>  TRUE
[18:24:24.902]      <block atomic="false" info="">
[18:24:24.902]        WriteDP(DP_SELECT, 0x00000000);
[18:24:24.902]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[18:24:24.903]        WriteDP(DP_CTRL_STAT, 0x00000000);
[18:24:24.904]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[18:24:24.904]      </block>
[18:24:24.904]      // end if-block "DoDebugPortStop"
[18:24:24.904]    </control>
[18:24:24.905]  </sequence>
[18:24:24.906]  
[18:37:30.698]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[18:37:30.698]  
[18:37:30.699]  <debugvars>
[18:37:30.699]    // Pre-defined
[18:37:30.699]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[18:37:30.700]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[18:37:30.700]    __dp=0x00000000
[18:37:30.700]    __ap=0x00000000
[18:37:30.700]    __traceout=0x00000000      (Trace Disabled)
[18:37:30.700]    __errorcontrol=0x00000000  (Skip Errors="False")
[18:37:30.701]    __FlashAddr=0x00000000
[18:37:30.701]    __FlashLen=0x00000000
[18:37:30.701]    __FlashArg=0x00000000
[18:37:30.701]    __FlashOp=0x00000000
[18:37:30.701]    __Result=0x00000000
[18:37:30.702]    
[18:37:30.702]    // User-defined
[18:37:30.702]    DbgMCU_CR=0x00000007
[18:37:30.702]    DbgMCU_APB1_Fz=0x00000000
[18:37:30.702]    DbgMCU_APB2_Fz=0x00000000
[18:37:30.702]    DoOptionByteLoading=0x00000000
[18:37:30.703]  </debugvars>
[18:37:30.703]  
[18:37:30.703]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[18:37:30.703]    <block atomic="false" info="">
[18:37:30.704]      Sequence("CheckID");
[18:37:30.704]        <sequence name="CheckID" Pname="" disable="false" info="">
[18:37:30.705]          <block atomic="false" info="">
[18:37:30.705]            __var pidr1 = 0;
[18:37:30.705]              // -> [pidr1 <= 0x00000000]
[18:37:30.705]            __var pidr2 = 0;
[18:37:30.706]              // -> [pidr2 <= 0x00000000]
[18:37:30.706]            __var jep106id = 0;
[18:37:30.706]              // -> [jep106id <= 0x00000000]
[18:37:30.706]            __var ROMTableBase = 0;
[18:37:30.707]              // -> [ROMTableBase <= 0x00000000]
[18:37:30.707]            __ap = 0;      // AHB-AP
[18:37:30.707]              // -> [__ap <= 0x00000000]
[18:37:30.707]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[18:37:30.709]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[18:37:30.709]              // -> [ROMTableBase <= 0xF0000000]
[18:37:30.709]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[18:37:30.712]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[18:37:30.713]              // -> [pidr1 <= 0x00000004]
[18:37:30.713]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[18:37:30.714]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[18:37:30.714]              // -> [pidr2 <= 0x0000000A]
[18:37:30.714]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[18:37:30.715]              // -> [jep106id <= 0x00000020]
[18:37:30.715]          </block>
[18:37:30.715]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[18:37:30.715]            // if-block "jep106id != 0x20"
[18:37:30.715]              // =>  FALSE
[18:37:30.716]            // skip if-block "jep106id != 0x20"
[18:37:30.716]          </control>
[18:37:30.716]        </sequence>
[18:37:30.716]    </block>
[18:37:30.716]  </sequence>
[18:37:30.717]  
[18:37:30.746]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[18:37:30.746]  
[18:37:30.746]  <debugvars>
[18:37:30.747]    // Pre-defined
[18:37:30.747]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[18:37:30.747]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[18:37:30.747]    __dp=0x00000000
[18:37:30.748]    __ap=0x00000000
[18:37:30.748]    __traceout=0x00000000      (Trace Disabled)
[18:37:30.748]    __errorcontrol=0x00000000  (Skip Errors="False")
[18:37:30.748]    __FlashAddr=0x00000000
[18:37:30.748]    __FlashLen=0x00000000
[18:37:30.748]    __FlashArg=0x00000000
[18:37:30.749]    __FlashOp=0x00000000
[18:37:30.749]    __Result=0x00000000
[18:37:30.750]    
[18:37:30.750]    // User-defined
[18:37:30.750]    DbgMCU_CR=0x00000007
[18:37:30.750]    DbgMCU_APB1_Fz=0x00000000
[18:37:30.750]    DbgMCU_APB2_Fz=0x00000000
[18:37:30.751]    DoOptionByteLoading=0x00000000
[18:37:30.751]  </debugvars>
[18:37:30.751]  
[18:37:30.751]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[18:37:30.752]    <block atomic="false" info="">
[18:37:30.752]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[18:37:30.754]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[18:37:30.754]    </block>
[18:37:30.754]    <block atomic="false" info="DbgMCU registers">
[18:37:30.754]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[18:37:30.757]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[18:37:30.757]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[18:37:30.757]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[18:37:30.761]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[18:37:30.761]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[18:37:30.763]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[18:37:30.763]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[18:37:30.764]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[18:37:30.765]    </block>
[18:37:30.765]  </sequence>
[18:37:30.765]  
[18:37:40.617]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[18:37:40.617]  
[18:37:40.617]  <debugvars>
[18:37:40.617]    // Pre-defined
[18:37:40.617]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[18:37:40.617]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[18:37:40.617]    __dp=0x00000000
[18:37:40.618]    __ap=0x00000000
[18:37:40.618]    __traceout=0x00000000      (Trace Disabled)
[18:37:40.619]    __errorcontrol=0x00000000  (Skip Errors="False")
[18:37:40.619]    __FlashAddr=0x00000000
[18:37:40.619]    __FlashLen=0x00000000
[18:37:40.619]    __FlashArg=0x00000000
[18:37:40.620]    __FlashOp=0x00000000
[18:37:40.620]    __Result=0x00000000
[18:37:40.620]    
[18:37:40.620]    // User-defined
[18:37:40.620]    DbgMCU_CR=0x00000007
[18:37:40.620]    DbgMCU_APB1_Fz=0x00000000
[18:37:40.621]    DbgMCU_APB2_Fz=0x00000000
[18:37:40.621]    DoOptionByteLoading=0x00000000
[18:37:40.621]  </debugvars>
[18:37:40.621]  
[18:37:40.621]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[18:37:40.621]    <block atomic="false" info="">
[18:37:40.622]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[18:37:40.622]        // -> [connectionFlash <= 0x00000001]
[18:37:40.622]      __var FLASH_BASE = 0x40022000 ;
[18:37:40.623]        // -> [FLASH_BASE <= 0x40022000]
[18:37:40.623]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[18:37:40.623]        // -> [FLASH_CR <= 0x40022004]
[18:37:40.623]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[18:37:40.623]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[18:37:40.624]      __var LOCK_BIT = ( 1 << 0 ) ;
[18:37:40.624]        // -> [LOCK_BIT <= 0x00000001]
[18:37:40.625]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[18:37:40.625]        // -> [OPTLOCK_BIT <= 0x00000004]
[18:37:40.625]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[18:37:40.625]        // -> [FLASH_KEYR <= 0x4002200C]
[18:37:40.626]      __var FLASH_KEY1 = 0x89ABCDEF ;
[18:37:40.626]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[18:37:40.626]      __var FLASH_KEY2 = 0x02030405 ;
[18:37:40.626]        // -> [FLASH_KEY2 <= 0x02030405]
[18:37:40.626]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[18:37:40.627]        // -> [FLASH_OPTKEYR <= 0x40022014]
[18:37:40.627]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[18:37:40.628]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[18:37:40.628]      __var FLASH_OPTKEY2 = 0x24252627 ;
[18:37:40.628]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[18:37:40.628]      __var FLASH_CR_Value = 0 ;
[18:37:40.629]        // -> [FLASH_CR_Value <= 0x00000000]
[18:37:40.629]      __var DoDebugPortStop = 1 ;
[18:37:40.629]        // -> [DoDebugPortStop <= 0x00000001]
[18:37:40.629]      __var DP_CTRL_STAT = 0x4 ;
[18:37:40.629]        // -> [DP_CTRL_STAT <= 0x00000004]
[18:37:40.630]      __var DP_SELECT = 0x8 ;
[18:37:40.630]        // -> [DP_SELECT <= 0x00000008]
[18:37:40.630]    </block>
[18:37:40.630]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[18:37:40.631]      // if-block "connectionFlash && DoOptionByteLoading"
[18:37:40.631]        // =>  FALSE
[18:37:40.631]      // skip if-block "connectionFlash && DoOptionByteLoading"
[18:37:40.632]    </control>
[18:37:40.632]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[18:37:40.632]      // if-block "DoDebugPortStop"
[18:37:40.632]        // =>  TRUE
[18:37:40.633]      <block atomic="false" info="">
[18:37:40.633]        WriteDP(DP_SELECT, 0x00000000);
[18:37:40.634]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[18:37:40.634]        WriteDP(DP_CTRL_STAT, 0x00000000);
[18:37:40.635]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[18:37:40.635]      </block>
[18:37:40.635]      // end if-block "DoDebugPortStop"
[18:37:40.635]    </control>
[18:37:40.635]  </sequence>
[18:37:40.636]  
[19:11:10.314]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[19:11:10.314]  
[19:11:10.342]  <debugvars>
[19:11:10.342]    // Pre-defined
[19:11:10.343]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:11:10.343]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:11:10.344]    __dp=0x00000000
[19:11:10.344]    __ap=0x00000000
[19:11:10.345]    __traceout=0x00000000      (Trace Disabled)
[19:11:10.345]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:11:10.345]    __FlashAddr=0x00000000
[19:11:10.346]    __FlashLen=0x00000000
[19:11:10.346]    __FlashArg=0x00000000
[19:11:10.347]    __FlashOp=0x00000000
[19:11:10.347]    __Result=0x00000000
[19:11:10.348]    
[19:11:10.348]    // User-defined
[19:11:10.348]    DbgMCU_CR=0x00000007
[19:11:10.349]    DbgMCU_APB1_Fz=0x00000000
[19:11:10.350]    DbgMCU_APB2_Fz=0x00000000
[19:11:10.350]    DoOptionByteLoading=0x00000000
[19:11:10.351]  </debugvars>
[19:11:10.352]  
[19:11:10.353]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[19:11:10.354]    <block atomic="false" info="">
[19:11:10.354]      Sequence("CheckID");
[19:11:10.355]        <sequence name="CheckID" Pname="" disable="false" info="">
[19:11:10.356]          <block atomic="false" info="">
[19:11:10.356]            __var pidr1 = 0;
[19:11:10.356]              // -> [pidr1 <= 0x00000000]
[19:11:10.357]            __var pidr2 = 0;
[19:11:10.358]              // -> [pidr2 <= 0x00000000]
[19:11:10.358]            __var jep106id = 0;
[19:11:10.359]              // -> [jep106id <= 0x00000000]
[19:11:10.359]            __var ROMTableBase = 0;
[19:11:10.361]              // -> [ROMTableBase <= 0x00000000]
[19:11:10.361]            __ap = 0;      // AHB-AP
[19:11:10.361]              // -> [__ap <= 0x00000000]
[19:11:10.361]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[19:11:10.363]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[19:11:10.363]              // -> [ROMTableBase <= 0xF0000000]
[19:11:10.363]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[19:11:10.369]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[19:11:10.369]              // -> [pidr1 <= 0x00000004]
[19:11:10.369]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[19:11:10.371]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[19:11:10.374]              // -> [pidr2 <= 0x0000000A]
[19:11:10.374]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[19:11:10.374]              // -> [jep106id <= 0x00000020]
[19:11:10.374]          </block>
[19:11:10.375]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[19:11:10.375]            // if-block "jep106id != 0x20"
[19:11:10.375]              // =>  FALSE
[19:11:10.376]            // skip if-block "jep106id != 0x20"
[19:11:10.376]          </control>
[19:11:10.376]        </sequence>
[19:11:10.376]    </block>
[19:11:10.377]  </sequence>
[19:11:10.377]  
[19:11:10.413]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[19:11:10.413]  
[19:11:10.414]  <debugvars>
[19:11:10.414]    // Pre-defined
[19:11:10.414]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:11:10.414]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:11:10.415]    __dp=0x00000000
[19:11:10.415]    __ap=0x00000000
[19:11:10.416]    __traceout=0x00000000      (Trace Disabled)
[19:11:10.416]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:11:10.417]    __FlashAddr=0x00000000
[19:11:10.417]    __FlashLen=0x00000000
[19:11:10.417]    __FlashArg=0x00000000
[19:11:10.417]    __FlashOp=0x00000000
[19:11:10.417]    __Result=0x00000000
[19:11:10.417]    
[19:11:10.417]    // User-defined
[19:11:10.417]    DbgMCU_CR=0x00000007
[19:11:10.418]    DbgMCU_APB1_Fz=0x00000000
[19:11:10.418]    DbgMCU_APB2_Fz=0x00000000
[19:11:10.419]    DoOptionByteLoading=0x00000000
[19:11:10.419]  </debugvars>
[19:11:10.419]  
[19:11:10.419]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[19:11:10.419]    <block atomic="false" info="">
[19:11:10.420]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[19:11:10.422]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[19:11:10.423]    </block>
[19:11:10.423]    <block atomic="false" info="DbgMCU registers">
[19:11:10.423]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[19:11:10.425]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[19:11:10.427]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[19:11:10.427]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[19:11:10.430]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[19:11:10.430]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[19:11:10.432]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[19:11:10.432]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[19:11:10.435]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[19:11:10.436]    </block>
[19:11:10.436]  </sequence>
[19:11:10.437]  
[19:11:21.909]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[19:11:21.909]  
[19:11:21.910]  <debugvars>
[19:11:21.910]    // Pre-defined
[19:11:21.910]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:11:21.910]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:11:21.911]    __dp=0x00000000
[19:11:21.912]    __ap=0x00000000
[19:11:21.912]    __traceout=0x00000000      (Trace Disabled)
[19:11:21.913]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:11:21.913]    __FlashAddr=0x00000000
[19:11:21.913]    __FlashLen=0x00000000
[19:11:21.913]    __FlashArg=0x00000000
[19:11:21.913]    __FlashOp=0x00000000
[19:11:21.915]    __Result=0x00000000
[19:11:21.915]    
[19:11:21.915]    // User-defined
[19:11:21.915]    DbgMCU_CR=0x00000007
[19:11:21.916]    DbgMCU_APB1_Fz=0x00000000
[19:11:21.916]    DbgMCU_APB2_Fz=0x00000000
[19:11:21.916]    DoOptionByteLoading=0x00000000
[19:11:21.917]  </debugvars>
[19:11:21.917]  
[19:11:21.917]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[19:11:21.918]    <block atomic="false" info="">
[19:11:21.918]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[19:11:21.918]        // -> [connectionFlash <= 0x00000001]
[19:11:21.918]      __var FLASH_BASE = 0x40022000 ;
[19:11:21.918]        // -> [FLASH_BASE <= 0x40022000]
[19:11:21.918]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[19:11:21.918]        // -> [FLASH_CR <= 0x40022004]
[19:11:21.918]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[19:11:21.919]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[19:11:21.920]      __var LOCK_BIT = ( 1 << 0 ) ;
[19:11:21.920]        // -> [LOCK_BIT <= 0x00000001]
[19:11:21.920]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[19:11:21.920]        // -> [OPTLOCK_BIT <= 0x00000004]
[19:11:21.921]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[19:11:21.921]        // -> [FLASH_KEYR <= 0x4002200C]
[19:11:21.921]      __var FLASH_KEY1 = 0x89ABCDEF ;
[19:11:21.922]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[19:11:21.922]      __var FLASH_KEY2 = 0x02030405 ;
[19:11:21.922]        // -> [FLASH_KEY2 <= 0x02030405]
[19:11:21.922]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[19:11:21.922]        // -> [FLASH_OPTKEYR <= 0x40022014]
[19:11:21.922]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[19:11:21.923]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[19:11:21.923]      __var FLASH_OPTKEY2 = 0x24252627 ;
[19:11:21.923]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[19:11:21.923]      __var FLASH_CR_Value = 0 ;
[19:11:21.923]        // -> [FLASH_CR_Value <= 0x00000000]
[19:11:21.923]      __var DoDebugPortStop = 1 ;
[19:11:21.924]        // -> [DoDebugPortStop <= 0x00000001]
[19:11:21.924]      __var DP_CTRL_STAT = 0x4 ;
[19:11:21.924]        // -> [DP_CTRL_STAT <= 0x00000004]
[19:11:21.924]      __var DP_SELECT = 0x8 ;
[19:11:21.924]        // -> [DP_SELECT <= 0x00000008]
[19:11:21.925]    </block>
[19:11:21.925]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[19:11:21.925]      // if-block "connectionFlash && DoOptionByteLoading"
[19:11:21.925]        // =>  FALSE
[19:11:21.925]      // skip if-block "connectionFlash && DoOptionByteLoading"
[19:11:21.925]    </control>
[19:11:21.926]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[19:11:21.926]      // if-block "DoDebugPortStop"
[19:11:21.926]        // =>  TRUE
[19:11:21.926]      <block atomic="false" info="">
[19:11:21.926]        WriteDP(DP_SELECT, 0x00000000);
[19:11:21.926]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[19:11:21.927]        WriteDP(DP_CTRL_STAT, 0x00000000);
[19:11:21.927]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[19:11:21.928]      </block>
[19:11:21.928]      // end if-block "DoDebugPortStop"
[19:11:21.929]    </control>
[19:11:21.929]  </sequence>
[19:11:21.930]  
[19:26:09.098]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[19:26:09.098]  
[19:26:09.098]  <debugvars>
[19:26:09.100]    // Pre-defined
[19:26:09.100]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:26:09.100]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:26:09.101]    __dp=0x00000000
[19:26:09.101]    __ap=0x00000000
[19:26:09.101]    __traceout=0x00000000      (Trace Disabled)
[19:26:09.101]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:26:09.101]    __FlashAddr=0x00000000
[19:26:09.102]    __FlashLen=0x00000000
[19:26:09.102]    __FlashArg=0x00000000
[19:26:09.102]    __FlashOp=0x00000000
[19:26:09.102]    __Result=0x00000000
[19:26:09.102]    
[19:26:09.102]    // User-defined
[19:26:09.103]    DbgMCU_CR=0x00000007
[19:26:09.103]    DbgMCU_APB1_Fz=0x00000000
[19:26:09.103]    DbgMCU_APB2_Fz=0x00000000
[19:26:09.103]    DoOptionByteLoading=0x00000000
[19:26:09.103]  </debugvars>
[19:26:09.103]  
[19:26:09.103]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[19:26:09.103]    <block atomic="false" info="">
[19:26:09.103]      Sequence("CheckID");
[19:26:09.103]        <sequence name="CheckID" Pname="" disable="false" info="">
[19:26:09.103]          <block atomic="false" info="">
[19:26:09.103]            __var pidr1 = 0;
[19:26:09.103]              // -> [pidr1 <= 0x00000000]
[19:26:09.105]            __var pidr2 = 0;
[19:26:09.105]              // -> [pidr2 <= 0x00000000]
[19:26:09.105]            __var jep106id = 0;
[19:26:09.106]              // -> [jep106id <= 0x00000000]
[19:26:09.106]            __var ROMTableBase = 0;
[19:26:09.106]              // -> [ROMTableBase <= 0x00000000]
[19:26:09.107]            __ap = 0;      // AHB-AP
[19:26:09.107]              // -> [__ap <= 0x00000000]
[19:26:09.107]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[19:26:09.109]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[19:26:09.109]              // -> [ROMTableBase <= 0xF0000000]
[19:26:09.109]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[19:26:09.112]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[19:26:09.112]              // -> [pidr1 <= 0x00000004]
[19:26:09.112]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[19:26:09.113]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[19:26:09.115]              // -> [pidr2 <= 0x0000000A]
[19:26:09.115]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[19:26:09.115]              // -> [jep106id <= 0x00000020]
[19:26:09.115]          </block>
[19:26:09.115]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[19:26:09.115]            // if-block "jep106id != 0x20"
[19:26:09.115]              // =>  FALSE
[19:26:09.115]            // skip if-block "jep106id != 0x20"
[19:26:09.115]          </control>
[19:26:09.117]        </sequence>
[19:26:09.117]    </block>
[19:26:09.117]  </sequence>
[19:26:09.117]  
[19:26:09.155]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[19:26:09.155]  
[19:26:09.155]  <debugvars>
[19:26:09.156]    // Pre-defined
[19:26:09.156]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:26:09.156]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:26:09.156]    __dp=0x00000000
[19:26:09.156]    __ap=0x00000000
[19:26:09.157]    __traceout=0x00000000      (Trace Disabled)
[19:26:09.157]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:26:09.157]    __FlashAddr=0x00000000
[19:26:09.157]    __FlashLen=0x00000000
[19:26:09.157]    __FlashArg=0x00000000
[19:26:09.157]    __FlashOp=0x00000000
[19:26:09.157]    __Result=0x00000000
[19:26:09.157]    
[19:26:09.157]    // User-defined
[19:26:09.159]    DbgMCU_CR=0x00000007
[19:26:09.159]    DbgMCU_APB1_Fz=0x00000000
[19:26:09.159]    DbgMCU_APB2_Fz=0x00000000
[19:26:09.159]    DoOptionByteLoading=0x00000000
[19:26:09.159]  </debugvars>
[19:26:09.159]  
[19:26:09.159]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[19:26:09.159]    <block atomic="false" info="">
[19:26:09.159]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[19:26:09.163]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[19:26:09.163]    </block>
[19:26:09.163]    <block atomic="false" info="DbgMCU registers">
[19:26:09.163]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[19:26:09.165]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[19:26:09.167]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[19:26:09.167]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[19:26:09.171]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[19:26:09.172]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[19:26:09.174]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[19:26:09.174]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[19:26:09.176]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[19:26:09.178]    </block>
[19:26:09.178]  </sequence>
[19:26:09.178]  
[19:26:21.109]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[19:26:21.109]  
[19:26:21.110]  <debugvars>
[19:26:21.110]    // Pre-defined
[19:26:21.110]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:26:21.110]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:26:21.111]    __dp=0x00000000
[19:26:21.111]    __ap=0x00000000
[19:26:21.111]    __traceout=0x00000000      (Trace Disabled)
[19:26:21.111]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:26:21.112]    __FlashAddr=0x00000000
[19:26:21.112]    __FlashLen=0x00000000
[19:26:21.113]    __FlashArg=0x00000000
[19:26:21.113]    __FlashOp=0x00000000
[19:26:21.113]    __Result=0x00000000
[19:26:21.114]    
[19:26:21.114]    // User-defined
[19:26:21.114]    DbgMCU_CR=0x00000007
[19:26:21.114]    DbgMCU_APB1_Fz=0x00000000
[19:26:21.114]    DbgMCU_APB2_Fz=0x00000000
[19:26:21.115]    DoOptionByteLoading=0x00000000
[19:26:21.115]  </debugvars>
[19:26:21.115]  
[19:26:21.116]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[19:26:21.116]    <block atomic="false" info="">
[19:26:21.116]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[19:26:21.116]        // -> [connectionFlash <= 0x00000001]
[19:26:21.117]      __var FLASH_BASE = 0x40022000 ;
[19:26:21.117]        // -> [FLASH_BASE <= 0x40022000]
[19:26:21.117]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[19:26:21.117]        // -> [FLASH_CR <= 0x40022004]
[19:26:21.117]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[19:26:21.117]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[19:26:21.118]      __var LOCK_BIT = ( 1 << 0 ) ;
[19:26:21.118]        // -> [LOCK_BIT <= 0x00000001]
[19:26:21.118]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[19:26:21.118]        // -> [OPTLOCK_BIT <= 0x00000004]
[19:26:21.118]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[19:26:21.119]        // -> [FLASH_KEYR <= 0x4002200C]
[19:26:21.119]      __var FLASH_KEY1 = 0x89ABCDEF ;
[19:26:21.119]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[19:26:21.120]      __var FLASH_KEY2 = 0x02030405 ;
[19:26:21.120]        // -> [FLASH_KEY2 <= 0x02030405]
[19:26:21.120]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[19:26:21.120]        // -> [FLASH_OPTKEYR <= 0x40022014]
[19:26:21.120]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[19:26:21.120]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[19:26:21.121]      __var FLASH_OPTKEY2 = 0x24252627 ;
[19:26:21.121]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[19:26:21.121]      __var FLASH_CR_Value = 0 ;
[19:26:21.121]        // -> [FLASH_CR_Value <= 0x00000000]
[19:26:21.121]      __var DoDebugPortStop = 1 ;
[19:26:21.121]        // -> [DoDebugPortStop <= 0x00000001]
[19:26:21.121]      __var DP_CTRL_STAT = 0x4 ;
[19:26:21.122]        // -> [DP_CTRL_STAT <= 0x00000004]
[19:26:21.122]      __var DP_SELECT = 0x8 ;
[19:26:21.123]        // -> [DP_SELECT <= 0x00000008]
[19:26:21.123]    </block>
[19:26:21.123]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[19:26:21.123]      // if-block "connectionFlash && DoOptionByteLoading"
[19:26:21.124]        // =>  FALSE
[19:26:21.124]      // skip if-block "connectionFlash && DoOptionByteLoading"
[19:26:21.124]    </control>
[19:26:21.124]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[19:26:21.124]      // if-block "DoDebugPortStop"
[19:26:21.125]        // =>  TRUE
[19:26:21.125]      <block atomic="false" info="">
[19:26:21.125]        WriteDP(DP_SELECT, 0x00000000);
[19:26:21.126]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[19:26:21.126]        WriteDP(DP_CTRL_STAT, 0x00000000);
[19:26:21.128]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[19:26:21.128]      </block>
[19:26:21.128]      // end if-block "DoDebugPortStop"
[19:26:21.128]    </control>
[19:26:21.129]  </sequence>
[19:26:21.129]  
[19:29:06.698]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[19:29:06.698]  
[19:29:06.698]  <debugvars>
[19:29:06.700]    // Pre-defined
[19:29:06.700]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:29:06.701]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:29:06.701]    __dp=0x00000000
[19:29:06.701]    __ap=0x00000000
[19:29:06.702]    __traceout=0x00000000      (Trace Disabled)
[19:29:06.702]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:29:06.702]    __FlashAddr=0x00000000
[19:29:06.702]    __FlashLen=0x00000000
[19:29:06.703]    __FlashArg=0x00000000
[19:29:06.703]    __FlashOp=0x00000000
[19:29:06.703]    __Result=0x00000000
[19:29:06.704]    
[19:29:06.704]    // User-defined
[19:29:06.704]    DbgMCU_CR=0x00000007
[19:29:06.704]    DbgMCU_APB1_Fz=0x00000000
[19:29:06.705]    DbgMCU_APB2_Fz=0x00000000
[19:29:06.705]    DoOptionByteLoading=0x00000000
[19:29:06.705]  </debugvars>
[19:29:06.705]  
[19:29:06.705]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[19:29:06.706]    <block atomic="false" info="">
[19:29:06.706]      Sequence("CheckID");
[19:29:06.706]        <sequence name="CheckID" Pname="" disable="false" info="">
[19:29:06.706]          <block atomic="false" info="">
[19:29:06.706]            __var pidr1 = 0;
[19:29:06.706]              // -> [pidr1 <= 0x00000000]
[19:29:06.707]            __var pidr2 = 0;
[19:29:06.707]              // -> [pidr2 <= 0x00000000]
[19:29:06.707]            __var jep106id = 0;
[19:29:06.707]              // -> [jep106id <= 0x00000000]
[19:29:06.707]            __var ROMTableBase = 0;
[19:29:06.707]              // -> [ROMTableBase <= 0x00000000]
[19:29:06.707]            __ap = 0;      // AHB-AP
[19:29:06.707]              // -> [__ap <= 0x00000000]
[19:29:06.707]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[19:29:06.710]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[19:29:06.710]              // -> [ROMTableBase <= 0xF0000000]
[19:29:06.710]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[19:29:06.713]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[19:29:06.714]              // -> [pidr1 <= 0x00000004]
[19:29:06.714]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[19:29:06.716]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[19:29:06.716]              // -> [pidr2 <= 0x0000000A]
[19:29:06.716]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[19:29:06.716]              // -> [jep106id <= 0x00000020]
[19:29:06.718]          </block>
[19:29:06.718]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[19:29:06.718]            // if-block "jep106id != 0x20"
[19:29:06.718]              // =>  FALSE
[19:29:06.719]            // skip if-block "jep106id != 0x20"
[19:29:06.719]          </control>
[19:29:06.719]        </sequence>
[19:29:06.719]    </block>
[19:29:06.720]  </sequence>
[19:29:06.720]  
[19:29:06.757]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[19:29:06.757]  
[19:29:06.758]  <debugvars>
[19:29:06.758]    // Pre-defined
[19:29:06.758]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:29:06.758]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:29:06.758]    __dp=0x00000000
[19:29:06.758]    __ap=0x00000000
[19:29:06.758]    __traceout=0x00000000      (Trace Disabled)
[19:29:06.758]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:29:06.758]    __FlashAddr=0x00000000
[19:29:06.758]    __FlashLen=0x00000000
[19:29:06.758]    __FlashArg=0x00000000
[19:29:06.758]    __FlashOp=0x00000000
[19:29:06.758]    __Result=0x00000000
[19:29:06.758]    
[19:29:06.758]    // User-defined
[19:29:06.758]    DbgMCU_CR=0x00000007
[19:29:06.758]    DbgMCU_APB1_Fz=0x00000000
[19:29:06.758]    DbgMCU_APB2_Fz=0x00000000
[19:29:06.762]    DoOptionByteLoading=0x00000000
[19:29:06.762]  </debugvars>
[19:29:06.762]  
[19:29:06.763]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[19:29:06.763]    <block atomic="false" info="">
[19:29:06.763]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[19:29:06.765]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[19:29:06.766]    </block>
[19:29:06.766]    <block atomic="false" info="DbgMCU registers">
[19:29:06.766]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[19:29:06.769]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[19:29:06.773]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[19:29:06.774]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[19:29:06.776]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[19:29:06.776]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[19:29:06.779]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[19:29:06.779]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[19:29:06.782]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[19:29:06.782]    </block>
[19:29:06.782]  </sequence>
[19:29:06.783]  
[19:29:17.375]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[19:29:17.375]  
[19:29:17.377]  <debugvars>
[19:29:17.377]    // Pre-defined
[19:29:17.378]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:29:17.378]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:29:17.379]    __dp=0x00000000
[19:29:17.379]    __ap=0x00000000
[19:29:17.379]    __traceout=0x00000000      (Trace Disabled)
[19:29:17.380]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:29:17.380]    __FlashAddr=0x00000000
[19:29:17.380]    __FlashLen=0x00000000
[19:29:17.381]    __FlashArg=0x00000000
[19:29:17.382]    __FlashOp=0x00000000
[19:29:17.382]    __Result=0x00000000
[19:29:17.383]    
[19:29:17.383]    // User-defined
[19:29:17.383]    DbgMCU_CR=0x00000007
[19:29:17.383]    DbgMCU_APB1_Fz=0x00000000
[19:29:17.384]    DbgMCU_APB2_Fz=0x00000000
[19:29:17.384]    DoOptionByteLoading=0x00000000
[19:29:17.384]  </debugvars>
[19:29:17.385]  
[19:29:17.385]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[19:29:17.385]    <block atomic="false" info="">
[19:29:17.386]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[19:29:17.386]        // -> [connectionFlash <= 0x00000001]
[19:29:17.386]      __var FLASH_BASE = 0x40022000 ;
[19:29:17.387]        // -> [FLASH_BASE <= 0x40022000]
[19:29:17.387]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[19:29:17.387]        // -> [FLASH_CR <= 0x40022004]
[19:29:17.387]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[19:29:17.387]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[19:29:17.388]      __var LOCK_BIT = ( 1 << 0 ) ;
[19:29:17.388]        // -> [LOCK_BIT <= 0x00000001]
[19:29:17.388]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[19:29:17.388]        // -> [OPTLOCK_BIT <= 0x00000004]
[19:29:17.388]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[19:29:17.388]        // -> [FLASH_KEYR <= 0x4002200C]
[19:29:17.388]      __var FLASH_KEY1 = 0x89ABCDEF ;
[19:29:17.388]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[19:29:17.388]      __var FLASH_KEY2 = 0x02030405 ;
[19:29:17.390]        // -> [FLASH_KEY2 <= 0x02030405]
[19:29:17.390]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[19:29:17.391]        // -> [FLASH_OPTKEYR <= 0x40022014]
[19:29:17.391]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[19:29:17.391]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[19:29:17.391]      __var FLASH_OPTKEY2 = 0x24252627 ;
[19:29:17.392]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[19:29:17.392]      __var FLASH_CR_Value = 0 ;
[19:29:17.392]        // -> [FLASH_CR_Value <= 0x00000000]
[19:29:17.392]      __var DoDebugPortStop = 1 ;
[19:29:17.392]        // -> [DoDebugPortStop <= 0x00000001]
[19:29:17.393]      __var DP_CTRL_STAT = 0x4 ;
[19:29:17.393]        // -> [DP_CTRL_STAT <= 0x00000004]
[19:29:17.393]      __var DP_SELECT = 0x8 ;
[19:29:17.393]        // -> [DP_SELECT <= 0x00000008]
[19:29:17.394]    </block>
[19:29:17.394]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[19:29:17.394]      // if-block "connectionFlash && DoOptionByteLoading"
[19:29:17.395]        // =>  FALSE
[19:29:17.395]      // skip if-block "connectionFlash && DoOptionByteLoading"
[19:29:17.395]    </control>
[19:29:17.396]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[19:29:17.396]      // if-block "DoDebugPortStop"
[19:29:17.396]        // =>  TRUE
[19:29:17.396]      <block atomic="false" info="">
[19:29:17.396]        WriteDP(DP_SELECT, 0x00000000);
[19:29:17.398]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[19:29:17.399]        WriteDP(DP_CTRL_STAT, 0x00000000);
[19:29:17.399]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[19:29:17.401]      </block>
[19:29:17.401]      // end if-block "DoDebugPortStop"
[19:29:17.401]    </control>
[19:29:17.402]  </sequence>
[19:29:17.402]  
[19:34:32.977]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[19:34:32.977]  
[19:34:32.977]  <debugvars>
[19:34:32.977]    // Pre-defined
[19:34:32.978]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:34:32.978]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:34:32.978]    __dp=0x00000000
[19:34:32.978]    __ap=0x00000000
[19:34:32.979]    __traceout=0x00000000      (Trace Disabled)
[19:34:32.979]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:34:32.979]    __FlashAddr=0x00000000
[19:34:32.980]    __FlashLen=0x00000000
[19:34:32.980]    __FlashArg=0x00000000
[19:34:32.980]    __FlashOp=0x00000000
[19:34:32.980]    __Result=0x00000000
[19:34:32.980]    
[19:34:32.980]    // User-defined
[19:34:32.980]    DbgMCU_CR=0x00000007
[19:34:32.980]    DbgMCU_APB1_Fz=0x00000000
[19:34:32.981]    DbgMCU_APB2_Fz=0x00000000
[19:34:32.981]    DoOptionByteLoading=0x00000000
[19:34:32.981]  </debugvars>
[19:34:32.981]  
[19:34:32.982]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[19:34:32.982]    <block atomic="false" info="">
[19:34:32.983]      Sequence("CheckID");
[19:34:32.983]        <sequence name="CheckID" Pname="" disable="false" info="">
[19:34:32.983]          <block atomic="false" info="">
[19:34:32.983]            __var pidr1 = 0;
[19:34:32.984]              // -> [pidr1 <= 0x00000000]
[19:34:32.984]            __var pidr2 = 0;
[19:34:32.984]              // -> [pidr2 <= 0x00000000]
[19:34:32.984]            __var jep106id = 0;
[19:34:32.984]              // -> [jep106id <= 0x00000000]
[19:34:32.985]            __var ROMTableBase = 0;
[19:34:32.985]              // -> [ROMTableBase <= 0x00000000]
[19:34:32.985]            __ap = 0;      // AHB-AP
[19:34:32.985]              // -> [__ap <= 0x00000000]
[19:34:32.985]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[19:34:32.987]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[19:34:32.987]              // -> [ROMTableBase <= 0xF0000000]
[19:34:32.988]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[19:34:32.991]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[19:34:32.991]              // -> [pidr1 <= 0x00000004]
[19:34:32.991]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[19:34:32.993]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[19:34:32.994]              // -> [pidr2 <= 0x0000000A]
[19:34:32.994]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[19:34:32.994]              // -> [jep106id <= 0x00000020]
[19:34:32.994]          </block>
[19:34:32.995]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[19:34:32.995]            // if-block "jep106id != 0x20"
[19:34:32.995]              // =>  FALSE
[19:34:32.995]            // skip if-block "jep106id != 0x20"
[19:34:32.995]          </control>
[19:34:32.995]        </sequence>
[19:34:32.995]    </block>
[19:34:32.997]  </sequence>
[19:34:32.997]  
[19:34:33.034]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[19:34:33.034]  
[19:34:33.035]  <debugvars>
[19:34:33.035]    // Pre-defined
[19:34:33.035]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:34:33.035]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:34:33.036]    __dp=0x00000000
[19:34:33.036]    __ap=0x00000000
[19:34:33.036]    __traceout=0x00000000      (Trace Disabled)
[19:34:33.036]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:34:33.037]    __FlashAddr=0x00000000
[19:34:33.037]    __FlashLen=0x00000000
[19:34:33.037]    __FlashArg=0x00000000
[19:34:33.037]    __FlashOp=0x00000000
[19:34:33.037]    __Result=0x00000000
[19:34:33.037]    
[19:34:33.037]    // User-defined
[19:34:33.037]    DbgMCU_CR=0x00000007
[19:34:33.038]    DbgMCU_APB1_Fz=0x00000000
[19:34:33.038]    DbgMCU_APB2_Fz=0x00000000
[19:34:33.038]    DoOptionByteLoading=0x00000000
[19:34:33.038]  </debugvars>
[19:34:33.038]  
[19:34:33.038]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[19:34:33.039]    <block atomic="false" info="">
[19:34:33.039]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[19:34:33.041]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[19:34:33.041]    </block>
[19:34:33.041]    <block atomic="false" info="DbgMCU registers">
[19:34:33.041]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[19:34:33.045]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[19:34:33.048]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[19:34:33.048]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[19:34:33.051]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[19:34:33.052]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[19:34:33.054]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[19:34:33.054]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[19:34:33.056]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[19:34:33.057]    </block>
[19:34:33.057]  </sequence>
[19:34:33.057]  
[19:34:44.272]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[19:34:44.272]  
[19:34:44.273]  <debugvars>
[19:34:44.273]    // Pre-defined
[19:34:44.273]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:34:44.275]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:34:44.275]    __dp=0x00000000
[19:34:44.276]    __ap=0x00000000
[19:34:44.276]    __traceout=0x00000000      (Trace Disabled)
[19:34:44.276]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:34:44.277]    __FlashAddr=0x00000000
[19:34:44.277]    __FlashLen=0x00000000
[19:34:44.277]    __FlashArg=0x00000000
[19:34:44.278]    __FlashOp=0x00000000
[19:34:44.278]    __Result=0x00000000
[19:34:44.279]    
[19:34:44.279]    // User-defined
[19:34:44.279]    DbgMCU_CR=0x00000007
[19:34:44.279]    DbgMCU_APB1_Fz=0x00000000
[19:34:44.280]    DbgMCU_APB2_Fz=0x00000000
[19:34:44.280]    DoOptionByteLoading=0x00000000
[19:34:44.280]  </debugvars>
[19:34:44.280]  
[19:34:44.281]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[19:34:44.281]    <block atomic="false" info="">
[19:34:44.281]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[19:34:44.282]        // -> [connectionFlash <= 0x00000001]
[19:34:44.282]      __var FLASH_BASE = 0x40022000 ;
[19:34:44.282]        // -> [FLASH_BASE <= 0x40022000]
[19:34:44.282]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[19:34:44.282]        // -> [FLASH_CR <= 0x40022004]
[19:34:44.282]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[19:34:44.283]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[19:34:44.283]      __var LOCK_BIT = ( 1 << 0 ) ;
[19:34:44.284]        // -> [LOCK_BIT <= 0x00000001]
[19:34:44.284]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[19:34:44.284]        // -> [OPTLOCK_BIT <= 0x00000004]
[19:34:44.284]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[19:34:44.284]        // -> [FLASH_KEYR <= 0x4002200C]
[19:34:44.284]      __var FLASH_KEY1 = 0x89ABCDEF ;
[19:34:44.284]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[19:34:44.286]      __var FLASH_KEY2 = 0x02030405 ;
[19:34:44.286]        // -> [FLASH_KEY2 <= 0x02030405]
[19:34:44.286]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[19:34:44.286]        // -> [FLASH_OPTKEYR <= 0x40022014]
[19:34:44.286]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[19:34:44.286]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[19:34:44.287]      __var FLASH_OPTKEY2 = 0x24252627 ;
[19:34:44.287]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[19:34:44.287]      __var FLASH_CR_Value = 0 ;
[19:34:44.287]        // -> [FLASH_CR_Value <= 0x00000000]
[19:34:44.288]      __var DoDebugPortStop = 1 ;
[19:34:44.288]        // -> [DoDebugPortStop <= 0x00000001]
[19:34:44.288]      __var DP_CTRL_STAT = 0x4 ;
[19:34:44.289]        // -> [DP_CTRL_STAT <= 0x00000004]
[19:34:44.289]      __var DP_SELECT = 0x8 ;
[19:34:44.289]        // -> [DP_SELECT <= 0x00000008]
[19:34:44.290]    </block>
[19:34:44.290]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[19:34:44.290]      // if-block "connectionFlash && DoOptionByteLoading"
[19:34:44.290]        // =>  FALSE
[19:34:44.291]      // skip if-block "connectionFlash && DoOptionByteLoading"
[19:34:44.291]    </control>
[19:34:44.291]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[19:34:44.291]      // if-block "DoDebugPortStop"
[19:34:44.292]        // =>  TRUE
[19:34:44.292]      <block atomic="false" info="">
[19:34:44.292]        WriteDP(DP_SELECT, 0x00000000);
[19:34:44.293]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[19:34:44.293]        WriteDP(DP_CTRL_STAT, 0x00000000);
[19:34:44.293]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[19:34:44.294]      </block>
[19:34:44.294]      // end if-block "DoDebugPortStop"
[19:34:44.294]    </control>
[19:34:44.294]  </sequence>
[19:34:44.296]  
[19:46:55.005]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[19:46:55.005]  
[19:46:55.006]  <debugvars>
[19:46:55.006]    // Pre-defined
[19:46:55.007]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:46:55.007]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:46:55.007]    __dp=0x00000000
[19:46:55.008]    __ap=0x00000000
[19:46:55.008]    __traceout=0x00000000      (Trace Disabled)
[19:46:55.008]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:46:55.008]    __FlashAddr=0x00000000
[19:46:55.008]    __FlashLen=0x00000000
[19:46:55.009]    __FlashArg=0x00000000
[19:46:55.009]    __FlashOp=0x00000000
[19:46:55.009]    __Result=0x00000000
[19:46:55.009]    
[19:46:55.009]    // User-defined
[19:46:55.009]    DbgMCU_CR=0x00000007
[19:46:55.010]    DbgMCU_APB1_Fz=0x00000000
[19:46:55.010]    DbgMCU_APB2_Fz=0x00000000
[19:46:55.010]    DoOptionByteLoading=0x00000000
[19:46:55.011]  </debugvars>
[19:46:55.011]  
[19:46:55.011]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[19:46:55.011]    <block atomic="false" info="">
[19:46:55.012]      Sequence("CheckID");
[19:46:55.012]        <sequence name="CheckID" Pname="" disable="false" info="">
[19:46:55.012]          <block atomic="false" info="">
[19:46:55.012]            __var pidr1 = 0;
[19:46:55.012]              // -> [pidr1 <= 0x00000000]
[19:46:55.012]            __var pidr2 = 0;
[19:46:55.012]              // -> [pidr2 <= 0x00000000]
[19:46:55.013]            __var jep106id = 0;
[19:46:55.014]              // -> [jep106id <= 0x00000000]
[19:46:55.014]            __var ROMTableBase = 0;
[19:46:55.014]              // -> [ROMTableBase <= 0x00000000]
[19:46:55.014]            __ap = 0;      // AHB-AP
[19:46:55.014]              // -> [__ap <= 0x00000000]
[19:46:55.015]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[19:46:55.017]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[19:46:55.017]              // -> [ROMTableBase <= 0xF0000000]
[19:46:55.017]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[19:46:55.021]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[19:46:55.021]              // -> [pidr1 <= 0x00000004]
[19:46:55.021]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[19:46:55.023]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[19:46:55.023]              // -> [pidr2 <= 0x0000000A]
[19:46:55.023]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[19:46:55.023]              // -> [jep106id <= 0x00000020]
[19:46:55.023]          </block>
[19:46:55.025]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[19:46:55.025]            // if-block "jep106id != 0x20"
[19:46:55.025]              // =>  FALSE
[19:46:55.026]            // skip if-block "jep106id != 0x20"
[19:46:55.026]          </control>
[19:46:55.026]        </sequence>
[19:46:55.026]    </block>
[19:46:55.026]  </sequence>
[19:46:55.026]  
[19:46:55.060]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[19:46:55.060]  
[19:46:55.061]  <debugvars>
[19:46:55.061]    // Pre-defined
[19:46:55.062]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:46:55.062]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:46:55.062]    __dp=0x00000000
[19:46:55.062]    __ap=0x00000000
[19:46:55.062]    __traceout=0x00000000      (Trace Disabled)
[19:46:55.062]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:46:55.062]    __FlashAddr=0x00000000
[19:46:55.063]    __FlashLen=0x00000000
[19:46:55.063]    __FlashArg=0x00000000
[19:46:55.063]    __FlashOp=0x00000000
[19:46:55.063]    __Result=0x00000000
[19:46:55.064]    
[19:46:55.064]    // User-defined
[19:46:55.064]    DbgMCU_CR=0x00000007
[19:46:55.064]    DbgMCU_APB1_Fz=0x00000000
[19:46:55.065]    DbgMCU_APB2_Fz=0x00000000
[19:46:55.065]    DoOptionByteLoading=0x00000000
[19:46:55.065]  </debugvars>
[19:46:55.065]  
[19:46:55.066]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[19:46:55.066]    <block atomic="false" info="">
[19:46:55.066]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[19:46:55.069]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[19:46:55.069]    </block>
[19:46:55.069]    <block atomic="false" info="DbgMCU registers">
[19:46:55.069]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[19:46:55.073]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[19:46:55.074]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[19:46:55.074]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[19:46:55.077]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[19:46:55.077]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[19:46:55.079]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[19:46:55.080]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[19:46:55.082]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[19:46:55.083]    </block>
[19:46:55.083]  </sequence>
[19:46:55.083]  
[19:47:06.000]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[19:47:06.000]  
[19:47:06.000]  <debugvars>
[19:47:06.000]    // Pre-defined
[19:47:06.001]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:47:06.001]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:47:06.001]    __dp=0x00000000
[19:47:06.002]    __ap=0x00000000
[19:47:06.002]    __traceout=0x00000000      (Trace Disabled)
[19:47:06.002]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:47:06.002]    __FlashAddr=0x00000000
[19:47:06.002]    __FlashLen=0x00000000
[19:47:06.003]    __FlashArg=0x00000000
[19:47:06.003]    __FlashOp=0x00000000
[19:47:06.003]    __Result=0x00000000
[19:47:06.003]    
[19:47:06.003]    // User-defined
[19:47:06.004]    DbgMCU_CR=0x00000007
[19:47:06.004]    DbgMCU_APB1_Fz=0x00000000
[19:47:06.004]    DbgMCU_APB2_Fz=0x00000000
[19:47:06.004]    DoOptionByteLoading=0x00000000
[19:47:06.004]  </debugvars>
[19:47:06.004]  
[19:47:06.005]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[19:47:06.005]    <block atomic="false" info="">
[19:47:06.005]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[19:47:06.006]        // -> [connectionFlash <= 0x00000001]
[19:47:06.006]      __var FLASH_BASE = 0x40022000 ;
[19:47:06.006]        // -> [FLASH_BASE <= 0x40022000]
[19:47:06.006]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[19:47:06.006]        // -> [FLASH_CR <= 0x40022004]
[19:47:06.006]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[19:47:06.007]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[19:47:06.007]      __var LOCK_BIT = ( 1 << 0 ) ;
[19:47:06.007]        // -> [LOCK_BIT <= 0x00000001]
[19:47:06.007]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[19:47:06.007]        // -> [OPTLOCK_BIT <= 0x00000004]
[19:47:06.007]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[19:47:06.008]        // -> [FLASH_KEYR <= 0x4002200C]
[19:47:06.008]      __var FLASH_KEY1 = 0x89ABCDEF ;
[19:47:06.009]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[19:47:06.009]      __var FLASH_KEY2 = 0x02030405 ;
[19:47:06.009]        // -> [FLASH_KEY2 <= 0x02030405]
[19:47:06.009]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[19:47:06.010]        // -> [FLASH_OPTKEYR <= 0x40022014]
[19:47:06.010]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[19:47:06.010]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[19:47:06.010]      __var FLASH_OPTKEY2 = 0x24252627 ;
[19:47:06.010]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[19:47:06.011]      __var FLASH_CR_Value = 0 ;
[19:47:06.011]        // -> [FLASH_CR_Value <= 0x00000000]
[19:47:06.011]      __var DoDebugPortStop = 1 ;
[19:47:06.011]        // -> [DoDebugPortStop <= 0x00000001]
[19:47:06.011]      __var DP_CTRL_STAT = 0x4 ;
[19:47:06.011]        // -> [DP_CTRL_STAT <= 0x00000004]
[19:47:06.012]      __var DP_SELECT = 0x8 ;
[19:47:06.012]        // -> [DP_SELECT <= 0x00000008]
[19:47:06.013]    </block>
[19:47:06.013]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[19:47:06.013]      // if-block "connectionFlash && DoOptionByteLoading"
[19:47:06.013]        // =>  FALSE
[19:47:06.013]      // skip if-block "connectionFlash && DoOptionByteLoading"
[19:47:06.014]    </control>
[19:47:06.014]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[19:47:06.014]      // if-block "DoDebugPortStop"
[19:47:06.014]        // =>  TRUE
[19:47:06.014]      <block atomic="false" info="">
[19:47:06.015]        WriteDP(DP_SELECT, 0x00000000);
[19:47:06.015]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[19:47:06.016]        WriteDP(DP_CTRL_STAT, 0x00000000);
[19:47:06.017]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[19:47:06.018]      </block>
[19:47:06.018]      // end if-block "DoDebugPortStop"
[19:47:06.018]    </control>
[19:47:06.018]  </sequence>
[19:47:06.018]  
[19:48:23.178]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[19:48:23.178]  
[19:48:23.178]  <debugvars>
[19:48:23.179]    // Pre-defined
[19:48:23.179]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:48:23.179]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[19:48:23.179]    __dp=0x00000000
[19:48:23.180]    __ap=0x00000000
[19:48:23.180]    __traceout=0x00000000      (Trace Disabled)
[19:48:23.180]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:48:23.180]    __FlashAddr=0x00000000
[19:48:23.180]    __FlashLen=0x00000000
[19:48:23.181]    __FlashArg=0x00000000
[19:48:23.181]    __FlashOp=0x00000000
[19:48:23.181]    __Result=0x00000000
[19:48:23.181]    
[19:48:23.181]    // User-defined
[19:48:23.181]    DbgMCU_CR=0x00000007
[19:48:23.181]    DbgMCU_APB1_Fz=0x00000000
[19:48:23.181]    DbgMCU_APB2_Fz=0x00000000
[19:48:23.182]    DoOptionByteLoading=0x00000000
[19:48:23.182]  </debugvars>
[19:48:23.182]  
[19:48:23.183]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[19:48:23.183]    <block atomic="false" info="">
[19:48:23.183]      Sequence("CheckID");
[19:48:23.183]        <sequence name="CheckID" Pname="" disable="false" info="">
[19:48:23.183]          <block atomic="false" info="">
[19:48:23.184]            __var pidr1 = 0;
[19:48:23.184]              // -> [pidr1 <= 0x00000000]
[19:48:23.184]            __var pidr2 = 0;
[19:48:23.184]              // -> [pidr2 <= 0x00000000]
[19:48:23.184]            __var jep106id = 0;
[19:48:23.185]              // -> [jep106id <= 0x00000000]
[19:48:23.185]            __var ROMTableBase = 0;
[19:48:23.185]              // -> [ROMTableBase <= 0x00000000]
[19:48:23.186]            __ap = 0;      // AHB-AP
[19:48:23.186]              // -> [__ap <= 0x00000000]
[19:48:23.186]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[19:48:23.188]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[19:48:23.188]              // -> [ROMTableBase <= 0xF0000000]
[19:48:23.188]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[19:48:23.192]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[19:48:23.192]              // -> [pidr1 <= 0x00000004]
[19:48:23.192]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[19:48:23.195]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[19:48:23.195]              // -> [pidr2 <= 0x0000000A]
[19:48:23.195]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[19:48:23.196]              // -> [jep106id <= 0x00000020]
[19:48:23.196]          </block>
[19:48:23.196]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[19:48:23.196]            // if-block "jep106id != 0x20"
[19:48:23.196]              // =>  FALSE
[19:48:23.196]            // skip if-block "jep106id != 0x20"
[19:48:23.196]          </control>
[19:48:23.197]        </sequence>
[19:48:23.197]    </block>
[19:48:23.197]  </sequence>
[19:48:23.197]  
[19:48:23.240]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[19:48:23.240]  
[19:48:23.240]  <debugvars>
[19:48:23.240]    // Pre-defined
[19:48:23.240]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:48:23.241]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[19:48:23.241]    __dp=0x00000000
[19:48:23.242]    __ap=0x00000000
[19:48:23.242]    __traceout=0x00000000      (Trace Disabled)
[19:48:23.242]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:48:23.242]    __FlashAddr=0x00000000
[19:48:23.242]    __FlashLen=0x00000000
[19:48:23.242]    __FlashArg=0x00000000
[19:48:23.243]    __FlashOp=0x00000000
[19:48:23.243]    __Result=0x00000000
[19:48:23.243]    
[19:48:23.243]    // User-defined
[19:48:23.244]    DbgMCU_CR=0x00000007
[19:48:23.244]    DbgMCU_APB1_Fz=0x00000000
[19:48:23.244]    DbgMCU_APB2_Fz=0x00000000
[19:48:23.244]    DoOptionByteLoading=0x00000000
[19:48:23.244]  </debugvars>
[19:48:23.245]  
[19:48:23.245]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[19:48:23.245]    <block atomic="false" info="">
[19:48:23.245]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[19:48:23.246]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[19:48:23.246]    </block>
[19:48:23.247]    <block atomic="false" info="DbgMCU registers">
[19:48:23.247]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[19:48:23.249]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[19:48:23.252]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[19:48:23.252]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[19:48:23.255]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[19:48:23.256]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[19:48:23.257]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[19:48:23.258]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[19:48:23.260]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[19:48:23.260]    </block>
[19:48:23.261]  </sequence>
[19:48:23.261]  
[19:52:56.454]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[19:52:56.454]  
[19:52:56.456]  <debugvars>
[19:52:56.456]    // Pre-defined
[19:52:56.456]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:52:56.456]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[19:52:56.456]    __dp=0x00000000
[19:52:56.456]    __ap=0x00000000
[19:52:56.456]    __traceout=0x00000000      (Trace Disabled)
[19:52:56.457]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:52:56.457]    __FlashAddr=0x00000000
[19:52:56.457]    __FlashLen=0x00000000
[19:52:56.457]    __FlashArg=0x00000000
[19:52:56.457]    __FlashOp=0x00000000
[19:52:56.459]    __Result=0x00000000
[19:52:56.459]    
[19:52:56.459]    // User-defined
[19:52:56.459]    DbgMCU_CR=0x00000007
[19:52:56.459]    DbgMCU_APB1_Fz=0x00000000
[19:52:56.459]    DbgMCU_APB2_Fz=0x00000000
[19:52:56.459]    DoOptionByteLoading=0x00000000
[19:52:56.460]  </debugvars>
[19:52:56.460]  
[19:52:56.460]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[19:52:56.460]    <block atomic="false" info="">
[19:52:56.460]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[19:52:56.461]        // -> [connectionFlash <= 0x00000000]
[19:52:56.461]      __var FLASH_BASE = 0x40022000 ;
[19:52:56.461]        // -> [FLASH_BASE <= 0x40022000]
[19:52:56.461]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[19:52:56.461]        // -> [FLASH_CR <= 0x40022004]
[19:52:56.462]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[19:52:56.462]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[19:52:56.462]      __var LOCK_BIT = ( 1 << 0 ) ;
[19:52:56.462]        // -> [LOCK_BIT <= 0x00000001]
[19:52:56.462]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[19:52:56.462]        // -> [OPTLOCK_BIT <= 0x00000004]
[19:52:56.462]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[19:52:56.462]        // -> [FLASH_KEYR <= 0x4002200C]
[19:52:56.462]      __var FLASH_KEY1 = 0x89ABCDEF ;
[19:52:56.462]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[19:52:56.462]      __var FLASH_KEY2 = 0x02030405 ;
[19:52:56.464]        // -> [FLASH_KEY2 <= 0x02030405]
[19:52:56.464]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[19:52:56.464]        // -> [FLASH_OPTKEYR <= 0x40022014]
[19:52:56.464]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[19:52:56.464]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[19:52:56.465]      __var FLASH_OPTKEY2 = 0x24252627 ;
[19:52:56.465]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[19:52:56.465]      __var FLASH_CR_Value = 0 ;
[19:52:56.466]        // -> [FLASH_CR_Value <= 0x00000000]
[19:52:56.466]      __var DoDebugPortStop = 1 ;
[19:52:56.466]        // -> [DoDebugPortStop <= 0x00000001]
[19:52:56.466]      __var DP_CTRL_STAT = 0x4 ;
[19:52:56.467]        // -> [DP_CTRL_STAT <= 0x00000004]
[19:52:56.467]      __var DP_SELECT = 0x8 ;
[19:52:56.468]        // -> [DP_SELECT <= 0x00000008]
[19:52:56.468]    </block>
[19:52:56.468]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[19:52:56.469]      // if-block "connectionFlash && DoOptionByteLoading"
[19:52:56.469]        // =>  FALSE
[19:52:56.469]      // skip if-block "connectionFlash && DoOptionByteLoading"
[19:52:56.470]    </control>
[19:52:56.470]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[19:52:56.470]      // if-block "DoDebugPortStop"
[19:52:56.471]        // =>  TRUE
[19:52:56.471]      <block atomic="false" info="">
[19:52:56.471]        WriteDP(DP_SELECT, 0x00000000);
[19:52:56.472]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[19:52:56.472]        WriteDP(DP_CTRL_STAT, 0x00000000);
[19:52:56.472]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[19:52:56.472]      </block>
[19:52:56.472]      // end if-block "DoDebugPortStop"
[19:52:56.472]    </control>
[19:52:56.472]  </sequence>
[19:52:56.473]  
[19:54:24.335]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[19:54:24.335]  
[19:54:24.335]  <debugvars>
[19:54:24.335]    // Pre-defined
[19:54:24.335]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:54:24.335]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:54:24.337]    __dp=0x00000000
[19:54:24.337]    __ap=0x00000000
[19:54:24.337]    __traceout=0x00000000      (Trace Disabled)
[19:54:24.337]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:54:24.337]    __FlashAddr=0x00000000
[19:54:24.338]    __FlashLen=0x00000000
[19:54:24.338]    __FlashArg=0x00000000
[19:54:24.338]    __FlashOp=0x00000000
[19:54:24.338]    __Result=0x00000000
[19:54:24.338]    
[19:54:24.338]    // User-defined
[19:54:24.338]    DbgMCU_CR=0x00000007
[19:54:24.338]    DbgMCU_APB1_Fz=0x00000000
[19:54:24.338]    DbgMCU_APB2_Fz=0x00000000
[19:54:24.338]    DoOptionByteLoading=0x00000000
[19:54:24.338]  </debugvars>
[19:54:24.340]  
[19:54:24.340]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[19:54:24.340]    <block atomic="false" info="">
[19:54:24.340]      Sequence("CheckID");
[19:54:24.340]        <sequence name="CheckID" Pname="" disable="false" info="">
[19:54:24.340]          <block atomic="false" info="">
[19:54:24.340]            __var pidr1 = 0;
[19:54:24.340]              // -> [pidr1 <= 0x00000000]
[19:54:24.340]            __var pidr2 = 0;
[19:54:24.340]              // -> [pidr2 <= 0x00000000]
[19:54:24.342]            __var jep106id = 0;
[19:54:24.342]              // -> [jep106id <= 0x00000000]
[19:54:24.342]            __var ROMTableBase = 0;
[19:54:24.343]              // -> [ROMTableBase <= 0x00000000]
[19:54:24.343]            __ap = 0;      // AHB-AP
[19:54:24.343]              // -> [__ap <= 0x00000000]
[19:54:24.343]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[19:54:24.345]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[19:54:24.345]              // -> [ROMTableBase <= 0xF0000000]
[19:54:24.346]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[19:54:24.349]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[19:54:24.349]              // -> [pidr1 <= 0x00000004]
[19:54:24.349]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[19:54:24.352]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[19:54:24.352]              // -> [pidr2 <= 0x0000000A]
[19:54:24.352]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[19:54:24.352]              // -> [jep106id <= 0x00000020]
[19:54:24.353]          </block>
[19:54:24.353]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[19:54:24.353]            // if-block "jep106id != 0x20"
[19:54:24.353]              // =>  FALSE
[19:54:24.353]            // skip if-block "jep106id != 0x20"
[19:54:24.353]          </control>
[19:54:24.354]        </sequence>
[19:54:24.354]    </block>
[19:54:24.354]  </sequence>
[19:54:24.354]  
[19:54:24.392]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[19:54:24.392]  
[19:54:24.392]  <debugvars>
[19:54:24.392]    // Pre-defined
[19:54:24.393]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:54:24.393]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:54:24.394]    __dp=0x00000000
[19:54:24.394]    __ap=0x00000000
[19:54:24.394]    __traceout=0x00000000      (Trace Disabled)
[19:54:24.394]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:54:24.394]    __FlashAddr=0x00000000
[19:54:24.395]    __FlashLen=0x00000000
[19:54:24.395]    __FlashArg=0x00000000
[19:54:24.395]    __FlashOp=0x00000000
[19:54:24.396]    __Result=0x00000000
[19:54:24.396]    
[19:54:24.396]    // User-defined
[19:54:24.396]    DbgMCU_CR=0x00000007
[19:54:24.396]    DbgMCU_APB1_Fz=0x00000000
[19:54:24.396]    DbgMCU_APB2_Fz=0x00000000
[19:54:24.396]    DoOptionByteLoading=0x00000000
[19:54:24.396]  </debugvars>
[19:54:24.397]  
[19:54:24.397]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[19:54:24.397]    <block atomic="false" info="">
[19:54:24.397]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[19:54:24.400]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[19:54:24.400]    </block>
[19:54:24.400]    <block atomic="false" info="DbgMCU registers">
[19:54:24.400]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[19:54:24.402]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[19:54:24.406]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[19:54:24.423]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[19:54:24.425]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[19:54:24.426]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[19:54:24.430]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[19:54:24.430]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[19:54:24.433]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[19:54:24.433]    </block>
[19:54:24.433]  </sequence>
[19:54:24.433]  
[19:54:35.244]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[19:54:35.244]  
[19:54:35.244]  <debugvars>
[19:54:35.245]    // Pre-defined
[19:54:35.245]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:54:35.245]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:54:35.245]    __dp=0x00000000
[19:54:35.245]    __ap=0x00000000
[19:54:35.246]    __traceout=0x00000000      (Trace Disabled)
[19:54:35.246]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:54:35.246]    __FlashAddr=0x00000000
[19:54:35.247]    __FlashLen=0x00000000
[19:54:35.247]    __FlashArg=0x00000000
[19:54:35.247]    __FlashOp=0x00000000
[19:54:35.247]    __Result=0x00000000
[19:54:35.248]    
[19:54:35.248]    // User-defined
[19:54:35.248]    DbgMCU_CR=0x00000007
[19:54:35.248]    DbgMCU_APB1_Fz=0x00000000
[19:54:35.248]    DbgMCU_APB2_Fz=0x00000000
[19:54:35.249]    DoOptionByteLoading=0x00000000
[19:54:35.249]  </debugvars>
[19:54:35.249]  
[19:54:35.249]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[19:54:35.250]    <block atomic="false" info="">
[19:54:35.250]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[19:54:35.250]        // -> [connectionFlash <= 0x00000001]
[19:54:35.250]      __var FLASH_BASE = 0x40022000 ;
[19:54:35.250]        // -> [FLASH_BASE <= 0x40022000]
[19:54:35.250]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[19:54:35.252]        // -> [FLASH_CR <= 0x40022004]
[19:54:35.252]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[19:54:35.252]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[19:54:35.252]      __var LOCK_BIT = ( 1 << 0 ) ;
[19:54:35.253]        // -> [LOCK_BIT <= 0x00000001]
[19:54:35.253]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[19:54:35.253]        // -> [OPTLOCK_BIT <= 0x00000004]
[19:54:35.253]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[19:54:35.253]        // -> [FLASH_KEYR <= 0x4002200C]
[19:54:35.254]      __var FLASH_KEY1 = 0x89ABCDEF ;
[19:54:35.254]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[19:54:35.254]      __var FLASH_KEY2 = 0x02030405 ;
[19:54:35.254]        // -> [FLASH_KEY2 <= 0x02030405]
[19:54:35.254]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[19:54:35.255]        // -> [FLASH_OPTKEYR <= 0x40022014]
[19:54:35.255]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[19:54:35.255]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[19:54:35.255]      __var FLASH_OPTKEY2 = 0x24252627 ;
[19:54:35.255]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[19:54:35.255]      __var FLASH_CR_Value = 0 ;
[19:54:35.255]        // -> [FLASH_CR_Value <= 0x00000000]
[19:54:35.256]      __var DoDebugPortStop = 1 ;
[19:54:35.256]        // -> [DoDebugPortStop <= 0x00000001]
[19:54:35.256]      __var DP_CTRL_STAT = 0x4 ;
[19:54:35.256]        // -> [DP_CTRL_STAT <= 0x00000004]
[19:54:35.256]      __var DP_SELECT = 0x8 ;
[19:54:35.257]        // -> [DP_SELECT <= 0x00000008]
[19:54:35.257]    </block>
[19:54:35.257]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[19:54:35.257]      // if-block "connectionFlash && DoOptionByteLoading"
[19:54:35.257]        // =>  FALSE
[19:54:35.257]      // skip if-block "connectionFlash && DoOptionByteLoading"
[19:54:35.258]    </control>
[19:54:35.258]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[19:54:35.258]      // if-block "DoDebugPortStop"
[19:54:35.258]        // =>  TRUE
[19:54:35.258]      <block atomic="false" info="">
[19:54:35.259]        WriteDP(DP_SELECT, 0x00000000);
[19:54:35.259]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[19:54:35.260]        WriteDP(DP_CTRL_STAT, 0x00000000);
[19:54:35.261]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[19:54:35.261]      </block>
[19:54:35.261]      // end if-block "DoDebugPortStop"
[19:54:35.261]    </control>
[19:54:35.263]  </sequence>
[19:54:35.263]  
