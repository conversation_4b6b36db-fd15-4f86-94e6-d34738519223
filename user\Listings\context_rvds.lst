


ARM Macro Assembler    Page 1 


    1 00000000         ;/*
    2 00000000         ; * Copyright (c) 2006-2018, RT-Thread Development Team
    3 00000000         ; *
    4 00000000         ; * SPDX-License-Identifier: Apache-2.0
    5 00000000         ; *
    6 00000000         ; * Change Logs:
    7 00000000         ; * Date           Author       Notes
    8 00000000         ; * 2010-01-25     Bernard      first version
    9 00000000         ; * 2012-06-01     aozima       set pendsv priority to 0
                       xFF.
   10 00000000         ; * 2012-08-17     aozima       fixed bug: store r8 - r1
                       1.
   11 00000000         ; * 2013-06-18     aozima       add restore MSP feature.
                       
   12 00000000         ; */
   13 00000000         
   14 00000000         ;/**
   15 00000000         ; * @addtogroup CORTEX-M0
   16 00000000         ; */
   17 00000000         ;/*@{*/
   18 00000000         
   19 00000000 E000ED08 
                       SCB_VTOR
                               EQU              0xE000ED08  ; Vector Table Offs
                                                            et Register
   20 00000000 E000ED04 
                       NVIC_INT_CTRL
                               EQU              0xE000ED04  ; interrupt control
                                                             state register
   21 00000000 E000ED20 
                       NVIC_SHPR3
                               EQU              0xE000ED20  ; system priority r
                                                            egister (2)
   22 00000000 00FF0000 
                       NVIC_PENDSV_PRI
                               EQU              0x00FF0000  ; PendSV priority v
                                                            alue (lowest)
   23 00000000 10000000 
                       NVIC_PENDSVSET
                               EQU              0x10000000  ; value to trigger 
                                                            PendSV exception
   24 00000000         
   25 00000000                 AREA             |.text|, CODE, READONLY, ALIGN=
2
   26 00000000                 THUMB
   27 00000000                 REQUIRE8
   28 00000000                 PRESERVE8
   29 00000000         
   30 00000000                 IMPORT           rt_thread_switch_interrupt_flag
   31 00000000                 IMPORT           rt_interrupt_from_thread
   32 00000000                 IMPORT           rt_interrupt_to_thread
   33 00000000         
   34 00000000         ;/*
   35 00000000         ; * rt_base_t rt_hw_interrupt_disable();
   36 00000000         ; */
   37 00000000         rt_hw_interrupt_disable
                               PROC
   38 00000000                 EXPORT           rt_hw_interrupt_disable
   39 00000000 F3EF 8010       MRS              r0, PRIMASK



ARM Macro Assembler    Page 2 


   40 00000004 B672            CPSID            I
   41 00000006 4770            BX               LR
   42 00000008                 ENDP
   43 00000008         
   44 00000008         ;/*
   45 00000008         ; * void rt_hw_interrupt_enable(rt_base_t level);
   46 00000008         ; */
   47 00000008         rt_hw_interrupt_enable
                               PROC
   48 00000008                 EXPORT           rt_hw_interrupt_enable
   49 00000008 F380 8810       MSR              PRIMASK, r0
   50 0000000C 4770            BX               LR
   51 0000000E                 ENDP
   52 0000000E         
   53 0000000E         ;/*
   54 0000000E         ; * void rt_hw_context_switch(rt_uint32 from, rt_uint32 
                       to);
   55 0000000E         ; * r0 --> from
   56 0000000E         ; * r1 --> to
   57 0000000E         ; */
   58 0000000E         rt_hw_context_switch_interrupt
   59 0000000E                 EXPORT           rt_hw_context_switch_interrupt
   60 0000000E         rt_hw_context_switch
                               PROC
   61 0000000E                 EXPORT           rt_hw_context_switch
   62 0000000E         
   63 0000000E         ; set rt_thread_switch_interrupt_flag to 1
   64 0000000E 4A2A            LDR              r2, =rt_thread_switch_interrupt
_flag
   65 00000010 6813            LDR              r3, [r2]
   66 00000012 2B01            CMP              r3, #1
   67 00000014 D003            BEQ              _reswitch
   68 00000016 2301            MOVS             r3, #0x01
   69 00000018 6013            STR              r3, [r2]
   70 0000001A         
   71 0000001A 4A28            LDR              r2, =rt_interrupt_from_thread ;
                                                             set rt_interrupt_f
                                                            rom_thread
   72 0000001C 6010            STR              r0, [r2]
   73 0000001E         
   74 0000001E         _reswitch
   75 0000001E 4A28            LDR              r2, =rt_interrupt_to_thread ; s
                                                            et rt_interrupt_to_
                                                            thread
   76 00000020 6011            STR              r1, [r2]
   77 00000022         
   78 00000022 4828            LDR              r0, =NVIC_INT_CTRL ; trigger th
                                                            e PendSV exception 
                                                            (causes context swi
                                                            tch)
   79 00000024 4928            LDR              r1, =NVIC_PENDSVSET
   80 00000026 6001            STR              r1, [r0]
   81 00000028 4770            BX               LR
   82 0000002A                 ENDP
   83 0000002A         
   84 0000002A         ; r0 --> switch from thread stack
   85 0000002A         ; r1 --> switch to thread stack
   86 0000002A         ; psr, pc, lr, r12, r3, r2, r1, r0 are pushed into [from
                       ] stack



ARM Macro Assembler    Page 3 


   87 0000002A         PendSV_Handler
                               PROC
   88 0000002A                 EXPORT           PendSV_Handler
   89 0000002A         
   90 0000002A         ; disable interrupt to protect context switch
   91 0000002A F3EF 8210       MRS              r2, PRIMASK
   92 0000002E B672            CPSID            I
   93 00000030         
   94 00000030         ; get rt_thread_switch_interrupt_flag
   95 00000030 4821            LDR              r0, =rt_thread_switch_interrupt
_flag
   96 00000032 6801            LDR              r1, [r0]
   97 00000034 2900            CMP              r1, #0x00
   98 00000036 D01D            BEQ              pendsv_exit ; pendsv already ha
                                                            ndled
   99 00000038         
  100 00000038         ; clear rt_thread_switch_interrupt_flag to 0
  101 00000038 2100            MOVS             r1, #0x00
  102 0000003A 6001            STR              r1, [r0]
  103 0000003C         
  104 0000003C 481F            LDR              r0, =rt_interrupt_from_thread
  105 0000003E 6801            LDR              r1, [r0]
  106 00000040 2900            CMP              r1, #0x00
  107 00000042 D00A            BEQ              switch_to_thread ; skip registe
                                                            r save at the first
                                                             time
  108 00000044         
  109 00000044 F3EF 8109       MRS              r1, psp     ; get from thread s
                                                            tack pointer
  110 00000048         
  111 00000048 3920            SUBS             r1, r1, #0x20 ; space for {r4 -
                                                             r7} and {r8 - r11}
                                                            
  112 0000004A 6800            LDR              r0, [r0]
  113 0000004C 6001            STR              r1, [r0]    ; update from threa
                                                            d stack pointer
  114 0000004E         
  115 0000004E C1F0            STMIA            r1!, {r4 - r7} ; push thread {r
                                                            4 - r7} register to
                                                             thread stack
  116 00000050         
  117 00000050 4644            MOV              r4, r8      ; mov thread {r8 - 
                                                            r11} to {r4 - r7}
  118 00000052 464D            MOV              r5, r9
  119 00000054 4656            MOV              r6, r10
  120 00000056 465F            MOV              r7, r11
  121 00000058 C1F0            STMIA            r1!, {r4 - r7} ; push thread {r
                                                            8 - r11} high regis
                                                            ter to thread stack
                                                            
  122 0000005A         
  123 0000005A         switch_to_thread
  124 0000005A 4919            LDR              r1, =rt_interrupt_to_thread
  125 0000005C 6809            LDR              r1, [r1]
  126 0000005E 6809            LDR              r1, [r1]    ; load thread stack
                                                             pointer
  127 00000060         
  128 00000060 C9F0            LDMIA            r1!, {r4 - r7} ; pop thread {r4
                                                             - r7} register fro



ARM Macro Assembler    Page 4 


                                                            m thread stack
  129 00000062 B4F0            PUSH             {r4 - r7}   ; push {r4 - r7} to
                                                             MSP for copy {r8 -
                                                             r11}
  130 00000064         
  131 00000064 C9F0            LDMIA            r1!, {r4 - r7} ; pop thread {r8
                                                             - r11} high regist
                                                            er from thread stac
                                                            k to {r4 - r7}
  132 00000066 46A0            MOV              r8,  r4     ; mov {r4 - r7} to 
                                                            {r8 - r11}
  133 00000068 46A9            MOV              r9,  r5
  134 0000006A 46B2            MOV              r10, r6
  135 0000006C 46BB            MOV              r11, r7
  136 0000006E         
  137 0000006E BCF0            POP              {r4 - r7}   ; pop {r4 - r7} fro
                                                            m MSP
  138 00000070         
  139 00000070 F381 8809       MSR              psp, r1     ; update stack poin
                                                            ter
  140 00000074         
  141 00000074         pendsv_exit
  142 00000074         ; restore interrupt
  143 00000074 F382 8810       MSR              PRIMASK, r2
  144 00000078         
  145 00000078 2004            MOVS             r0, #0x04
  146 0000007A 4240            RSBS             r0, r0, #0x00
  147 0000007C 4700            BX               r0
  148 0000007E                 ENDP
  149 0000007E         
  150 0000007E         ;/*
  151 0000007E         ; * void rt_hw_context_switch_to(rt_uint32 to);
  152 0000007E         ; * r0 --> to
  153 0000007E         ; * this fucntion is used to perform the first thread sw
                       itch
  154 0000007E         ; */
  155 0000007E         rt_hw_context_switch_to
                               PROC
  156 0000007E                 EXPORT           rt_hw_context_switch_to
  157 0000007E         ; set to thread
  158 0000007E 4910            LDR              r1, =rt_interrupt_to_thread
  159 00000080 6008            STR              r0, [r1]
  160 00000082         
  161 00000082         ; set from thread to 0
  162 00000082 490E            LDR              r1, =rt_interrupt_from_thread
  163 00000084 2000            MOVS             r0, #0x0
  164 00000086 6008            STR              r0, [r1]
  165 00000088         
  166 00000088         ; set interrupt flag to 1
  167 00000088 490B            LDR              r1, =rt_thread_switch_interrupt
_flag
  168 0000008A 2001            MOVS             r0, #1
  169 0000008C 6008            STR              r0, [r1]
  170 0000008E         
  171 0000008E         ; set the PendSV exception priority
  172 0000008E 480F            LDR              r0, =NVIC_SHPR3
  173 00000090 490F            LDR              r1, =NVIC_PENDSV_PRI
  174 00000092 6802            LDR              r2, [r0,#0x00] ; read
  175 00000094 4311            ORRS             r1,r1,r2    ; modify



ARM Macro Assembler    Page 5 


  176 00000096 6001            STR              r1, [r0]    ; write-back
  177 00000098         
  178 00000098         ; trigger the PendSV exception (causes context switch)
  179 00000098 480A            LDR              r0, =NVIC_INT_CTRL
  180 0000009A 490B            LDR              r1, =NVIC_PENDSVSET
  181 0000009C 6001            STR              r1, [r0]
  182 0000009E         
  183 0000009E         ; restore MSP
  184 0000009E 480D            LDR              r0, =SCB_VTOR
  185 000000A0 6800            LDR              r0, [r0]
  186 000000A2 6800            LDR              r0, [r0]
  187 000000A4 F380 8808       MSR              msp, r0
  188 000000A8         
  189 000000A8         ; enable interrupts at processor level
  190 000000A8 B662            CPSIE            I
  191 000000AA         
  192 000000AA         ; never reach here!
  193 000000AA                 ENDP
  194 000000AA         
  195 000000AA         ; compatible with old version
  196 000000AA         rt_hw_interrupt_thread_switch
                               PROC
  197 000000AA                 EXPORT           rt_hw_interrupt_thread_switch
  198 000000AA 4770            BX               lr
  199 000000AC                 ENDP
  200 000000AC         
  201 000000AC                 IMPORT           rt_hw_hard_fault_exception
  202 000000AC         
  203 000000AC         HardFault_Handler
                               PROC
  204 000000AC                 EXPORT           HardFault_Handler
  205 000000AC         
  206 000000AC         ; get current context
  207 000000AC F3EF 8009       MRS              r0, psp     ; get fault thread 
                                                            stack pointer
  208 000000B0 B500            PUSH             {lr}
  209 000000B2 F7FF FFFE       BL               rt_hw_hard_fault_exception
  210 000000B6 BD00            POP              {pc}
  211 000000B8                 ENDP
  212 000000B8         
  213 000000B8                 ALIGN            4
  214 000000B8         
  215 000000B8                 END
              00000000 
              00000000 
              00000000 
              E000ED04 
              10000000 
              E000ED20 
              00FF0000 
              E000ED08 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M0+ --apcs=inter
work --depend=.\objects\context_rvds.d -o.\objects\context_rvds.o --predefine="
__MICROLIB SETA 1" --predefine="__UVISION_VERSION SETA 538" --predefine="STM32L
072xx SETA 1" --list=.\listings\context_rvds.lst ..\rtthread\libcpu\arm\cortex-
m0\context_rvds.S



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 25 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      None
Comment: .text unused
HardFault_Handler 000000AC

Symbol: HardFault_Handler
   Definitions
      At line 203 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      At line 204 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
Comment: HardFault_Handler used once
PendSV_Handler 0000002A

Symbol: PendSV_Handler
   Definitions
      At line 87 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      At line 88 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
Comment: PendSV_Handler used once
_reswitch 0000001E

Symbol: _reswitch
   Definitions
      At line 74 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      At line 67 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
Comment: _reswitch used once
pendsv_exit 00000074

Symbol: pendsv_exit
   Definitions
      At line 141 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      At line 98 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
Comment: pendsv_exit used once
rt_hw_context_switch 0000000E

Symbol: rt_hw_context_switch
   Definitions
      At line 60 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      At line 61 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
Comment: rt_hw_context_switch used once
rt_hw_context_switch_interrupt 0000000E

Symbol: rt_hw_context_switch_interrupt
   Definitions
      At line 58 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      At line 59 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
Comment: rt_hw_context_switch_interrupt used once
rt_hw_context_switch_to 0000007E

Symbol: rt_hw_context_switch_to



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 155 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      At line 156 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
Comment: rt_hw_context_switch_to used once
rt_hw_interrupt_disable 00000000

Symbol: rt_hw_interrupt_disable
   Definitions
      At line 37 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      At line 38 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
Comment: rt_hw_interrupt_disable used once
rt_hw_interrupt_enable 00000008

Symbol: rt_hw_interrupt_enable
   Definitions
      At line 47 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      At line 48 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
Comment: rt_hw_interrupt_enable used once
rt_hw_interrupt_thread_switch 000000AA

Symbol: rt_hw_interrupt_thread_switch
   Definitions
      At line 196 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      At line 197 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
Comment: rt_hw_interrupt_thread_switch used once
switch_to_thread 0000005A

Symbol: switch_to_thread
   Definitions
      At line 123 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      At line 107 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
Comment: switch_to_thread used once
12 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

NVIC_INT_CTRL E000ED04

Symbol: NVIC_INT_CTRL
   Definitions
      At line 20 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      At line 78 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
      At line 179 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S

NVIC_PENDSVSET 10000000

Symbol: NVIC_PENDSVSET
   Definitions
      At line 23 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      At line 79 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
      At line 180 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S

NVIC_PENDSV_PRI 00FF0000

Symbol: NVIC_PENDSV_PRI
   Definitions
      At line 22 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      At line 173 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
Comment: NVIC_PENDSV_PRI used once
NVIC_SHPR3 E000ED20

Symbol: NVIC_SHPR3
   Definitions
      At line 21 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      At line 172 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
Comment: NVIC_SHPR3 used once
SCB_VTOR E000ED08

Symbol: SCB_VTOR
   Definitions
      At line 19 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      At line 184 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
Comment: SCB_VTOR used once
5 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

rt_hw_hard_fault_exception 00000000

Symbol: rt_hw_hard_fault_exception
   Definitions
      At line 201 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      At line 209 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
Comment: rt_hw_hard_fault_exception used once
rt_interrupt_from_thread 00000000

Symbol: rt_interrupt_from_thread
   Definitions
      At line 31 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      At line 71 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
      At line 104 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
      At line 162 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S

rt_interrupt_to_thread 00000000

Symbol: rt_interrupt_to_thread
   Definitions
      At line 32 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      At line 75 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
      At line 124 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
      At line 158 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S

rt_thread_switch_interrupt_flag 00000000

Symbol: rt_thread_switch_interrupt_flag
   Definitions
      At line 30 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
   Uses
      At line 64 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
      At line 95 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S
      At line 167 in file ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S

4 symbols
356 symbols in table
