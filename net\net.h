#ifndef __NET_H__
#define __NET_H__

#include "system.h"

#define NET_RECV_BUF_SIZE 512
#define NET_RECV_BYTE_TIMEOUT 50

typedef enum
{
  NET_ERROR_NONE = 0,
  NET_ERROR_FAIL,
  NET_ERROR_TIMEOUT,

  NET_ERROR_MEM,
  NET_ERROT_CONNECT,
}NET_ERR;
/*串口相关定义*/
typedef struct
{
  void (*init)(uint32_t baud_rate);
  void (*clean)(void);
  void (*send_data)(uint8_t *data, uint16_t len);
  void (*send_cmd)(char *cmd,uint16_t len);

  uint8_t recv[NET_RECV_BUF_SIZE];
  uint16_t count;
  uint8_t byte_timeout;
}NET_SERIAL_PORT;
/*模块基础AT指令功能，和公共接口*/
typedef struct
{
  NET_ERR (*send_cmd)(char *cmd,char *ack,uint32_t timeout);
  NET_ERR (*waitting)(char *ack,uint32_t timeout);
  NET_ERR (*send_data)(uint8_t *data,uint16_t len);
  NET_ERR (*sync_time)(uint16_t *year,uint8_t *month,uint8_t *day,uint8_t *hour,uint8_t *minute,uint8_t *second );
  NET_ERR (*get_info)(char *iccid,char *imei);
	NET_ERR (*get_net_sig)(uint8_t *sig);
}NET_BASE_FUNCTIONS;
/*TCPIP部分的公共接口*/
typedef struct
{
  NET_ERR (*connect)(char *ip,uint16_t port);
  NET_ERR (*send)(uint8_t *data, uint16_t len);
  NET_ERR (*recv)(uint8_t *data, uint16_t *len);
  NET_ERR (*close)(void);
}NET_TCPIP;

/*网络接口，仅此变量对外开放访问*/
typedef struct
{
  NET_SERIAL_PORT *serial_port;
  NET_BASE_FUNCTIONS *base_function;
  NET_TCPIP *tcpip;
}NET_INFO;

extern NET_SERIAL_PORT serial_port;

uint8_t net_module_reboot(void);
/*调用此接口，返回成功，接口自动绑定相关硬件模块和功能*/
NET_ERR net_init(NET_INFO *info,uint32_t baud_rate);

#endif
