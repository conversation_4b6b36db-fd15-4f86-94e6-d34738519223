#include "adc_voltage.h"
#include "stm32l0xx_hal.h"
#include <stdio.h>

static ADC_HandleTypeDef hadc;

void ADC_Voltage_Init(void) {
    // 0. 检查ADC外设时钟
    if(__HAL_RCC_GET_ADC1_SOURCE() == RCC_ADC1CLKSOURCE_NONE) {
        printf("[ERROR] ADC clock not enabled!\r\n");
        return;
    }
    
    // 1. 初始化GPIO
    __HAL_RCC_GPIOA_CLK_ENABLE();
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = GPIO_PIN_4;
    GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    // 2. 配置ADC
    hadc.Instance = ADC1;
    hadc.Init.ClockPrescaler = ADC_CLOCK_ASYNC_DIV2;
    hadc.Init.Resolution = ADC_RESOLUTION_12B;
    hadc.Init.DataAlign = ADC_DATAALIGN_RIGHT;
    hadc.Init.ScanConvMode = DISABLE;
    hadc.Init.EOCSelection = ADC_EOC_SINGLE_CONV;
    hadc.Init.LowPowerAutoWait = DISABLE;
    hadc.Init.LowPowerAutoPowerOff = DISABLE;
    hadc.Init.ContinuousConvMode = DISABLE;
    hadc.Init.DiscontinuousConvMode = DISABLE;
    hadc.Init.ExternalTrigConv = ADC_SOFTWARE_START;
    hadc.Init.Overrun = ADC_OVR_DATA_OVERWRITTEN;
    hadc.Init.SamplingTime = ADC_SAMPLETIME_160CYCLES_5;
    HAL_ADC_Init(&hadc);

    // 3. 校准ADC
    if(HAL_ADCEx_Calibration_Start(&hadc, ADC_SINGLE_ENDED) != HAL_OK) {
        printf("[ERROR] ADC calibration failed!\r\n");
        return;
    }
    printf("[INFO] ADC calibration success\r\n");

    // 4. 配置ADC通道（PA4对应ADC_IN4）
    ADC_ChannelConfTypeDef sConfig = {0};
    sConfig.Channel = ADC_CHANNEL_4;
    sConfig.Rank = ADC_RANK_CHANNEL_NUMBER;
    HAL_ADC_ConfigChannel(&hadc, &sConfig);
}

float Get_Power_Voltage(void) {
    // 1. 启动ADC转换
    HAL_StatusTypeDef start_status = HAL_ADC_Start(&hadc);
    if(start_status != HAL_OK) {
        printf("[ERROR] ADC start failed! Status: %d\r\n", start_status);
        printf("ADC1->CR: 0x%08lX\r\n", ADC1->CR);
        printf("ADC1->ISR: 0x%08lX\r\n", ADC1->ISR);
        return 0;
    }
    
    HAL_StatusTypeDef poll_status = HAL_ADC_PollForConversion(&hadc, 10);
    if(poll_status != HAL_OK) {
        printf("[ERROR] ADC timeout! Status: %d\r\n", poll_status);
        return 0;
    }
    
    // 2. 读取ADC值（0-4095）
    uint32_t adc_value = HAL_ADC_GetValue(&hadc);
    HAL_ADC_Stop(&hadc);
    
    // 调试输出原始ADC值
    printf("[DEBUG] ADC RAW: %lu\r\n", adc_value);
    
    // 3. 计算实际电压（假设VDDA=3.0V，分压比2倍）
    uint32_t voltage_mv = (adc_value * 6000) / 4095; // 3.0V*2*1000=6000
    
    return (uint16_t)voltage_mv;
}