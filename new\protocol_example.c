/**
 * @file protocol_example.c
 * @brief 水表采集器配置工具通信协议使用示例
 * <AUTHOR> Assistant
 * @date 2025-08-26
 * 
 * 本文件展示如何在STM32项目中集成和使用协议库
 */

#include "protocol.h"
#include <stdio.h>
#include <string.h>

// 模拟STM32 HAL库的UART句柄（实际项目中需要包含相应的头文件）
// #include "stm32f1xx_hal.h"
// extern UART_HandleTypeDef huart1;

// 接收缓冲区
#define RX_BUFFER_SIZE 256
static uint8_t rx_buffer[RX_BUFFER_SIZE];
static volatile int rx_index = 0;
static volatile int frame_ready = 0;

/**
 * @brief UART发送数据函数实现
 * @param data 数据指针
 * @param length 数据长度
 */
void uart_send_data(uint8_t *data, int length)
{
    // 实际STM32项目中的实现示例：
    // HAL_UART_Transmit(&huart1, data, length, 1000);
    
    // 调试输出（可选）
    printf("UART Send (%d bytes): ", length);
    for (int i = 0; i < length; i++) {
        printf("%02X ", data[i]);
    }
    printf("\n");
}

/**
 * @brief UART接收中断回调函数
 * @param byte 接收到的字节
 * 
 * 在STM32 HAL库中，可以在HAL_UART_RxCpltCallback中调用此函数
 */
void uart_rx_callback(uint8_t byte)
{
    static int frame_started = 0;
    
    if (byte == FRAME_HEAD) {
        // 帧头，开始新帧
        rx_index = 0;
        frame_started = 1;
        rx_buffer[rx_index++] = byte;
    } else if (frame_started && rx_index < RX_BUFFER_SIZE) {
        rx_buffer[rx_index++] = byte;
        
        if (byte == FRAME_TAIL) {
            // 帧尾，帧接收完成
            frame_ready = 1;
            frame_started = 0;
        }
    }
}

/**
 * @brief 处理接收到的协议帧
 */
void process_protocol_frame(void)
{
    if (frame_ready) {
        frame_ready = 0;
        
        // 调用协议处理函数
        int result = protocol_process(rx_index, (char*)rx_buffer);
        
        if (result == 0) {
            printf("Protocol frame processed successfully\n");
        } else {
            printf("Protocol frame processing failed\n");
        }
        
        rx_index = 0;
    }
}

/**
 * @brief 配置初始化示例
 */
void protocol_config_init(void)
{
    // 设置默认的中心1配置
    center_config_t center1 = {
        .ip = {192, 168, 1, 100},
        .port = 8016,
        .period = 1
    };
    protocol_set_center_config(1, &center1);
    
    // 设置默认的厂家协议配置
    protocol_config_t protocol = {
        .manufacturer = MANUFACTURER_DALIAN,
        .baud_code = BAUD_9600,
        .data_bits = 8,
        .stop_bits = 1,
        .parity = PARITY_NONE
    };
    protocol_set_protocol_config(&protocol);
    
    // 设置默认的终端ID
    uint8_t terminal_id[5] = {0x65, 0x32, 0x23, 0x2D, 0x92};
    protocol_set_terminal_id(terminal_id);
    
    // 设置系统版本
    uint8_t version[3] = {'1', '2', '3'};  // V1.2.3
    protocol_set_system_version(version);
    
    printf("Protocol configuration initialized\n");
}

/**
 * @brief 读取配置示例
 */
void protocol_config_read_example(void)
{
    center_config_t center;
    protocol_config_t protocol;
    other_config_t other;
    uint8_t terminal_id[5];
    uint8_t version[3];
    
    // 读取中心1配置
    if (protocol_get_center_config(1, &center) == 0) {
        printf("Center1 Config: IP=%d.%d.%d.%d, Port=%d, Period=%d\n",
               center.ip[0], center.ip[1], center.ip[2], center.ip[3],
               center.port, center.period);
    }
    
    // 读取厂家协议配置
    if (protocol_get_protocol_config(&protocol) == 0) {
        printf("Protocol Config: Manufacturer=%d, Baud=%d, DataBits=%d, StopBits=%d, Parity=%d\n",
               protocol.manufacturer, protocol.baud_code, protocol.data_bits,
               protocol.stop_bits, protocol.parity);
    }
    
    // 读取其他配置
    if (protocol_get_other_config(&other) == 0) {
        printf("Other Config: DataCompatMode=%d\n", other.data_compat_mode);
    }
    
    // 读取终端ID
    if (protocol_get_terminal_id(terminal_id) == 0) {
        printf("Terminal ID: %02X %02X %02X %02X %02X\n",
               terminal_id[0], terminal_id[1], terminal_id[2], terminal_id[3], terminal_id[4]);
    }
    
    // 读取系统版本
    if (protocol_get_system_version(version) == 0) {
        printf("System Version: V%c.%c.%c\n", version[0], version[1], version[2]);
    }
}

/**
 * @brief 主循环示例
 */
void main_loop_example(void)
{
    // 初始化配置
    protocol_config_init();
    
    // 读取并显示配置
    protocol_config_read_example();
    
    // 主循环
    while (1) {
        // 处理协议帧
        process_protocol_frame();
        
        // 其他应用逻辑...
        
        // 延时或等待中断
        // HAL_Delay(10);
    }
}

/**
 * @brief STM32 HAL库UART接收中断回调示例
 * 
 * 在实际STM32项目中，可以这样实现：
 */
/*
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART1) {
        static uint8_t rx_byte;
        
        // 处理接收到的字节
        uart_rx_callback(rx_byte);
        
        // 重新启动接收
        HAL_UART_Receive_IT(&huart1, &rx_byte, 1);
    }
}
*/

/**
 * @brief 测试函数 - 模拟接收协议数据包
 */
void test_protocol_packets(void)
{
    printf("=== Protocol Test ===\n");

    // 测试心跳包: 7E 02 02 CE (长度2=长度字段1+命令码1)
    uint8_t heartbeat[] = {0x7E, 0x02, 0x02, 0xCE};
    printf("Testing heartbeat packet...\n");
    protocol_process(4, (char*)heartbeat);

    // 测试查询系统版本: 7E 02 05 CE (长度2=长度字段1+命令码1)
    uint8_t version_query[] = {0x7E, 0x02, 0x05, 0xCE};
    printf("Testing version query packet...\n");
    protocol_process(4, (char*)version_query);

    // 测试读取中心1配置: 7E 02 11 CE (长度2=长度字段1+命令码1)
    uint8_t center1_read[] = {0x7E, 0x02, 0x11, 0xCE};
    printf("Testing center1 read packet...\n");
    protocol_process(4, (char*)center1_read);

    // 测试设置中心1配置: 7E 0A 11 C0 A8 01 64 50 1F 01 00 CE (长度0A=长度字段1+命令码1+数据8)
    uint8_t center1_set[] = {0x7E, 0x0A, 0x11, 0xC0, 0xA8, 0x01, 0x64, 0x50, 0x1F, 0x01, 0x00, 0xCE};
    printf("Testing center1 set packet...\n");
    protocol_process(12, (char*)center1_set);

    // 测试读取厂家协议: 7E 02 15 CE (长度2=长度字段1+命令码1)
    uint8_t protocol_read[] = {0x7E, 0x02, 0x15, 0xCE};
    printf("Testing protocol read packet...\n");
    protocol_process(4, (char*)protocol_read);

    printf("=== Test Complete ===\n");
}

/**
 * @brief 主函数示例（用于测试）
 */
int main(void)
{
    printf("Water Meter Collector Protocol Example\n");
    
    // 初始化配置
    protocol_config_init();
    
    // 运行测试
    test_protocol_packets();
    
    // 显示配置
    protocol_config_read_example();
    
    return 0;
}
