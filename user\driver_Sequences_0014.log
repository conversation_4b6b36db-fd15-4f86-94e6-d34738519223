/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : D:\2025work\STM32L072PRO\source-application\user\driver_Sequences_0014.log
 *  Created     : 08:39:51 (15/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : D:\2025work\STM32L072PRO\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[08:39:51.686]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:39:51.686]  
[08:39:51.703]  <debugvars>
[08:39:51.724]    // Pre-defined
[08:39:51.744]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:39:51.766]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:39:51.767]    __dp=0x00000000
[08:39:51.768]    __ap=0x00000000
[08:39:51.769]    __traceout=0x00000000      (Trace Disabled)
[08:39:51.769]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:39:51.771]    __FlashAddr=0x00000000
[08:39:51.772]    __FlashLen=0x00000000
[08:39:51.772]    __FlashArg=0x00000000
[08:39:51.773]    __FlashOp=0x00000000
[08:39:51.774]    __Result=0x00000000
[08:39:51.774]    
[08:39:51.774]    // User-defined
[08:39:51.774]    DbgMCU_CR=0x00000007
[08:39:51.774]    DbgMCU_APB1_Fz=0x00000000
[08:39:51.777]    DbgMCU_APB2_Fz=0x00000000
[08:39:51.777]    DoOptionByteLoading=0x00000000
[08:39:51.778]  </debugvars>
[08:39:51.779]  
[08:39:51.779]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:39:51.780]    <block atomic="false" info="">
[08:39:51.781]      Sequence("CheckID");
[08:39:51.782]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:39:51.782]          <block atomic="false" info="">
[08:39:51.783]            __var pidr1 = 0;
[08:39:51.784]              // -> [pidr1 <= 0x00000000]
[08:39:51.785]            __var pidr2 = 0;
[08:39:51.785]              // -> [pidr2 <= 0x00000000]
[08:39:51.786]            __var jep106id = 0;
[08:39:51.787]              // -> [jep106id <= 0x00000000]
[08:39:51.788]            __var ROMTableBase = 0;
[08:39:51.788]              // -> [ROMTableBase <= 0x00000000]
[08:39:51.789]            __ap = 0;      // AHB-AP
[08:39:51.790]              // -> [__ap <= 0x00000000]
[08:39:51.791]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:39:51.792]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:39:51.793]              // -> [ROMTableBase <= 0xF0000000]
[08:39:51.794]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:39:51.796]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:39:51.796]              // -> [pidr1 <= 0x00000004]
[08:39:51.797]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:39:51.798]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:39:51.799]              // -> [pidr2 <= 0x0000000A]
[08:39:51.799]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:39:51.800]              // -> [jep106id <= 0x00000020]
[08:39:51.800]          </block>
[08:39:51.800]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:39:51.801]            // if-block "jep106id != 0x20"
[08:39:51.801]              // =>  FALSE
[08:39:51.802]            // skip if-block "jep106id != 0x20"
[08:39:51.802]          </control>
[08:39:51.803]        </sequence>
[08:39:51.803]    </block>
[08:39:51.803]  </sequence>
[08:39:51.803]  
[08:39:51.816]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:39:51.816]  
[08:39:51.827]  <debugvars>
[08:39:51.828]    // Pre-defined
[08:39:51.829]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:39:51.829]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:39:51.830]    __dp=0x00000000
[08:39:51.830]    __ap=0x00000000
[08:39:51.831]    __traceout=0x00000000      (Trace Disabled)
[08:39:51.831]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:39:51.832]    __FlashAddr=0x00000000
[08:39:51.833]    __FlashLen=0x00000000
[08:39:51.834]    __FlashArg=0x00000000
[08:39:51.834]    __FlashOp=0x00000000
[08:39:51.834]    __Result=0x00000000
[08:39:51.835]    
[08:39:51.835]    // User-defined
[08:39:51.836]    DbgMCU_CR=0x00000007
[08:39:51.836]    DbgMCU_APB1_Fz=0x00000000
[08:39:51.837]    DbgMCU_APB2_Fz=0x00000000
[08:39:51.837]    DoOptionByteLoading=0x00000000
[08:39:51.838]  </debugvars>
[08:39:51.839]  
[08:39:51.839]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:39:51.839]    <block atomic="false" info="">
[08:39:51.840]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:39:51.841]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:39:51.842]    </block>
[08:39:51.842]    <block atomic="false" info="DbgMCU registers">
[08:39:51.843]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:39:51.844]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[08:39:51.845]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[08:39:51.845]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:39:51.846]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:39:51.847]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:39:51.848]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:39:51.848]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:39:51.848]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:39:51.849]    </block>
[08:39:51.849]  </sequence>
[08:39:51.849]  
[08:39:59.217]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[08:39:59.217]  
[08:39:59.218]  <debugvars>
[08:39:59.218]    // Pre-defined
[08:39:59.219]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:39:59.220]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:39:59.220]    __dp=0x00000000
[08:39:59.221]    __ap=0x00000000
[08:39:59.221]    __traceout=0x00000000      (Trace Disabled)
[08:39:59.222]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:39:59.222]    __FlashAddr=0x00000000
[08:39:59.223]    __FlashLen=0x00000000
[08:39:59.223]    __FlashArg=0x00000000
[08:39:59.223]    __FlashOp=0x00000000
[08:39:59.224]    __Result=0x00000000
[08:39:59.225]    
[08:39:59.225]    // User-defined
[08:39:59.225]    DbgMCU_CR=0x00000007
[08:39:59.225]    DbgMCU_APB1_Fz=0x00000000
[08:39:59.226]    DbgMCU_APB2_Fz=0x00000000
[08:39:59.226]    DoOptionByteLoading=0x00000000
[08:39:59.226]  </debugvars>
[08:39:59.227]  
[08:39:59.227]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[08:39:59.227]    <block atomic="false" info="">
[08:39:59.228]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[08:39:59.228]        // -> [connectionFlash <= 0x00000001]
[08:39:59.228]      __var FLASH_BASE = 0x40022000 ;
[08:39:59.229]        // -> [FLASH_BASE <= 0x40022000]
[08:39:59.229]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[08:39:59.229]        // -> [FLASH_CR <= 0x40022004]
[08:39:59.229]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[08:39:59.230]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[08:39:59.230]      __var LOCK_BIT = ( 1 << 0 ) ;
[08:39:59.230]        // -> [LOCK_BIT <= 0x00000001]
[08:39:59.230]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[08:39:59.230]        // -> [OPTLOCK_BIT <= 0x00000004]
[08:39:59.231]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[08:39:59.231]        // -> [FLASH_KEYR <= 0x4002200C]
[08:39:59.231]      __var FLASH_KEY1 = 0x89ABCDEF ;
[08:39:59.231]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[08:39:59.231]      __var FLASH_KEY2 = 0x02030405 ;
[08:39:59.232]        // -> [FLASH_KEY2 <= 0x02030405]
[08:39:59.232]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[08:39:59.232]        // -> [FLASH_OPTKEYR <= 0x40022014]
[08:39:59.233]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[08:39:59.233]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[08:39:59.233]      __var FLASH_OPTKEY2 = 0x24252627 ;
[08:39:59.233]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[08:39:59.233]      __var FLASH_CR_Value = 0 ;
[08:39:59.233]        // -> [FLASH_CR_Value <= 0x00000000]
[08:39:59.234]      __var DoDebugPortStop = 1 ;
[08:39:59.234]        // -> [DoDebugPortStop <= 0x00000001]
[08:39:59.234]      __var DP_CTRL_STAT = 0x4 ;
[08:39:59.234]        // -> [DP_CTRL_STAT <= 0x00000004]
[08:39:59.234]      __var DP_SELECT = 0x8 ;
[08:39:59.234]        // -> [DP_SELECT <= 0x00000008]
[08:39:59.235]    </block>
[08:39:59.235]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[08:39:59.235]      // if-block "connectionFlash && DoOptionByteLoading"
[08:39:59.236]        // =>  FALSE
[08:39:59.236]      // skip if-block "connectionFlash && DoOptionByteLoading"
[08:39:59.236]    </control>
[08:39:59.236]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[08:39:59.236]      // if-block "DoDebugPortStop"
[08:39:59.237]        // =>  TRUE
[08:39:59.237]      <block atomic="false" info="">
[08:39:59.237]        WriteDP(DP_SELECT, 0x00000000);
[08:39:59.237]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[08:39:59.238]        WriteDP(DP_CTRL_STAT, 0x00000000);
[08:39:59.238]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[08:39:59.238]      </block>
[08:39:59.238]      // end if-block "DoDebugPortStop"
[08:39:59.239]    </control>
[08:39:59.239]  </sequence>
[08:39:59.239]  
[08:49:31.272]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:49:31.272]  
[08:49:31.273]  <debugvars>
[08:49:31.273]    // Pre-defined
[08:49:31.273]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:49:31.273]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:49:31.274]    __dp=0x00000000
[08:49:31.274]    __ap=0x00000000
[08:49:31.274]    __traceout=0x00000000      (Trace Disabled)
[08:49:31.274]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:49:31.275]    __FlashAddr=0x00000000
[08:49:31.275]    __FlashLen=0x00000000
[08:49:31.276]    __FlashArg=0x00000000
[08:49:31.276]    __FlashOp=0x00000000
[08:49:31.276]    __Result=0x00000000
[08:49:31.276]    
[08:49:31.276]    // User-defined
[08:49:31.277]    DbgMCU_CR=0x00000007
[08:49:31.277]    DbgMCU_APB1_Fz=0x00000000
[08:49:31.277]    DbgMCU_APB2_Fz=0x00000000
[08:49:31.277]    DoOptionByteLoading=0x00000000
[08:49:31.277]  </debugvars>
[08:49:31.277]  
[08:49:31.278]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:49:31.278]    <block atomic="false" info="">
[08:49:31.278]      Sequence("CheckID");
[08:49:31.279]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:49:31.279]          <block atomic="false" info="">
[08:49:31.279]            __var pidr1 = 0;
[08:49:31.280]              // -> [pidr1 <= 0x00000000]
[08:49:31.280]            __var pidr2 = 0;
[08:49:31.280]              // -> [pidr2 <= 0x00000000]
[08:49:31.280]            __var jep106id = 0;
[08:49:31.281]              // -> [jep106id <= 0x00000000]
[08:49:31.281]            __var ROMTableBase = 0;
[08:49:31.281]              // -> [ROMTableBase <= 0x00000000]
[08:49:31.281]            __ap = 0;      // AHB-AP
[08:49:31.281]              // -> [__ap <= 0x00000000]
[08:49:31.282]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:49:31.282]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:49:31.283]              // -> [ROMTableBase <= 0xF0000000]
[08:49:31.283]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:49:31.284]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:49:31.284]              // -> [pidr1 <= 0x00000004]
[08:49:31.284]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:49:31.285]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:49:31.285]              // -> [pidr2 <= 0x0000000A]
[08:49:31.286]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:49:31.286]              // -> [jep106id <= 0x00000020]
[08:49:31.286]          </block>
[08:49:31.286]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:49:31.286]            // if-block "jep106id != 0x20"
[08:49:31.287]              // =>  FALSE
[08:49:31.287]            // skip if-block "jep106id != 0x20"
[08:49:31.287]          </control>
[08:49:31.287]        </sequence>
[08:49:31.287]    </block>
[08:49:31.287]  </sequence>
[08:49:31.288]  
[08:49:31.300]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:49:31.300]  
[08:49:31.300]  <debugvars>
[08:49:31.300]    // Pre-defined
[08:49:31.301]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:49:31.301]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:49:31.301]    __dp=0x00000000
[08:49:31.302]    __ap=0x00000000
[08:49:31.302]    __traceout=0x00000000      (Trace Disabled)
[08:49:31.302]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:49:31.302]    __FlashAddr=0x00000000
[08:49:31.302]    __FlashLen=0x00000000
[08:49:31.303]    __FlashArg=0x00000000
[08:49:31.303]    __FlashOp=0x00000000
[08:49:31.304]    __Result=0x00000000
[08:49:31.304]    
[08:49:31.304]    // User-defined
[08:49:31.304]    DbgMCU_CR=0x00000007
[08:49:31.305]    DbgMCU_APB1_Fz=0x00000000
[08:49:31.305]    DbgMCU_APB2_Fz=0x00000000
[08:49:31.305]    DoOptionByteLoading=0x00000000
[08:49:31.306]  </debugvars>
[08:49:31.306]  
[08:49:31.306]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:49:31.307]    <block atomic="false" info="">
[08:49:31.307]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:49:31.308]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:49:31.308]    </block>
[08:49:31.309]    <block atomic="false" info="DbgMCU registers">
[08:49:31.309]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:49:31.310]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[08:49:31.311]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[08:49:31.311]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:49:31.312]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:49:31.312]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:49:31.313]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:49:31.313]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:49:31.314]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:49:31.314]    </block>
[08:49:31.314]  </sequence>
[08:49:31.315]  
[08:49:38.827]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[08:49:38.827]  
[08:49:38.827]  <debugvars>
[08:49:38.827]    // Pre-defined
[08:49:38.827]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:49:38.827]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:49:38.827]    __dp=0x00000000
[08:49:38.827]    __ap=0x00000000
[08:49:38.827]    __traceout=0x00000000      (Trace Disabled)
[08:49:38.827]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:49:38.827]    __FlashAddr=0x00000000
[08:49:38.827]    __FlashLen=0x00000000
[08:49:38.827]    __FlashArg=0x00000000
[08:49:38.827]    __FlashOp=0x00000000
[08:49:38.827]    __Result=0x00000000
[08:49:38.827]    
[08:49:38.827]    // User-defined
[08:49:38.827]    DbgMCU_CR=0x00000007
[08:49:38.827]    DbgMCU_APB1_Fz=0x00000000
[08:49:38.827]    DbgMCU_APB2_Fz=0x00000000
[08:49:38.827]    DoOptionByteLoading=0x00000000
[08:49:38.827]  </debugvars>
[08:49:38.832]  
[08:49:38.832]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[08:49:38.832]    <block atomic="false" info="">
[08:49:38.832]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[08:49:38.832]        // -> [connectionFlash <= 0x00000001]
[08:49:38.832]      __var FLASH_BASE = 0x40022000 ;
[08:49:38.833]        // -> [FLASH_BASE <= 0x40022000]
[08:49:38.833]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[08:49:38.833]        // -> [FLASH_CR <= 0x40022004]
[08:49:38.833]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[08:49:38.833]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[08:49:38.833]      __var LOCK_BIT = ( 1 << 0 ) ;
[08:49:38.835]        // -> [LOCK_BIT <= 0x00000001]
[08:49:38.835]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[08:49:38.835]        // -> [OPTLOCK_BIT <= 0x00000004]
[08:49:38.835]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[08:49:38.835]        // -> [FLASH_KEYR <= 0x4002200C]
[08:49:38.836]      __var FLASH_KEY1 = 0x89ABCDEF ;
[08:49:38.836]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[08:49:38.836]      __var FLASH_KEY2 = 0x02030405 ;
[08:49:38.836]        // -> [FLASH_KEY2 <= 0x02030405]
[08:49:38.837]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[08:49:38.837]        // -> [FLASH_OPTKEYR <= 0x40022014]
[08:49:38.837]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[08:49:38.837]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[08:49:38.837]      __var FLASH_OPTKEY2 = 0x24252627 ;
[08:49:38.838]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[08:49:38.838]      __var FLASH_CR_Value = 0 ;
[08:49:38.838]        // -> [FLASH_CR_Value <= 0x00000000]
[08:49:38.838]      __var DoDebugPortStop = 1 ;
[08:49:38.838]        // -> [DoDebugPortStop <= 0x00000001]
[08:49:38.839]      __var DP_CTRL_STAT = 0x4 ;
[08:49:38.839]        // -> [DP_CTRL_STAT <= 0x00000004]
[08:49:38.839]      __var DP_SELECT = 0x8 ;
[08:49:38.839]        // -> [DP_SELECT <= 0x00000008]
[08:49:38.839]    </block>
[08:49:38.839]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[08:49:38.840]      // if-block "connectionFlash && DoOptionByteLoading"
[08:49:38.840]        // =>  FALSE
[08:49:38.840]      // skip if-block "connectionFlash && DoOptionByteLoading"
[08:49:38.840]    </control>
[08:49:38.840]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[08:49:38.840]      // if-block "DoDebugPortStop"
[08:49:38.841]        // =>  TRUE
[08:49:38.841]      <block atomic="false" info="">
[08:49:38.841]        WriteDP(DP_SELECT, 0x00000000);
[08:49:38.841]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[08:49:38.842]        WriteDP(DP_CTRL_STAT, 0x00000000);
[08:49:38.842]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[08:49:38.842]      </block>
[08:49:38.842]      // end if-block "DoDebugPortStop"
[08:49:38.843]    </control>
[08:49:38.843]  </sequence>
[08:49:38.843]  
[08:56:59.034]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:56:59.034]  
[08:56:59.034]  <debugvars>
[08:56:59.034]    // Pre-defined
[08:56:59.034]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:56:59.034]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:56:59.034]    __dp=0x00000000
[08:56:59.034]    __ap=0x00000000
[08:56:59.034]    __traceout=0x00000000      (Trace Disabled)
[08:56:59.034]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:56:59.034]    __FlashAddr=0x00000000
[08:56:59.034]    __FlashLen=0x00000000
[08:56:59.034]    __FlashArg=0x00000000
[08:56:59.034]    __FlashOp=0x00000000
[08:56:59.034]    __Result=0x00000000
[08:56:59.034]    
[08:56:59.034]    // User-defined
[08:56:59.034]    DbgMCU_CR=0x00000007
[08:56:59.034]    DbgMCU_APB1_Fz=0x00000000
[08:56:59.034]    DbgMCU_APB2_Fz=0x00000000
[08:56:59.034]    DoOptionByteLoading=0x00000000
[08:56:59.034]  </debugvars>
[08:56:59.034]  
[08:56:59.034]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:56:59.034]    <block atomic="false" info="">
[08:56:59.044]      Sequence("CheckID");
[08:56:59.044]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:56:59.044]          <block atomic="false" info="">
[08:56:59.044]            __var pidr1 = 0;
[08:56:59.044]              // -> [pidr1 <= 0x00000000]
[08:56:59.044]            __var pidr2 = 0;
[08:56:59.044]              // -> [pidr2 <= 0x00000000]
[08:56:59.044]            __var jep106id = 0;
[08:56:59.044]              // -> [jep106id <= 0x00000000]
[08:56:59.044]            __var ROMTableBase = 0;
[08:56:59.044]              // -> [ROMTableBase <= 0x00000000]
[08:56:59.044]            __ap = 0;      // AHB-AP
[08:56:59.044]              // -> [__ap <= 0x00000000]
[08:56:59.044]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:56:59.044]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:56:59.044]              // -> [ROMTableBase <= 0xF0000000]
[08:56:59.044]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:56:59.044]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:56:59.044]              // -> [pidr1 <= 0x00000004]
[08:56:59.044]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:56:59.044]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:56:59.044]              // -> [pidr2 <= 0x0000000A]
[08:56:59.044]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:56:59.044]              // -> [jep106id <= 0x00000020]
[08:56:59.044]          </block>
[08:56:59.044]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:56:59.044]            // if-block "jep106id != 0x20"
[08:56:59.044]              // =>  FALSE
[08:56:59.044]            // skip if-block "jep106id != 0x20"
[08:56:59.044]          </control>
[08:56:59.044]        </sequence>
[08:56:59.044]    </block>
[08:56:59.044]  </sequence>
[08:56:59.044]  
[08:56:59.059]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:56:59.059]  
[08:56:59.070]  <debugvars>
[08:56:59.070]    // Pre-defined
[08:56:59.070]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:56:59.070]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:56:59.070]    __dp=0x00000000
[08:56:59.074]    __ap=0x00000000
[08:56:59.074]    __traceout=0x00000000      (Trace Disabled)
[08:56:59.074]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:56:59.074]    __FlashAddr=0x00000000
[08:56:59.074]    __FlashLen=0x00000000
[08:56:59.074]    __FlashArg=0x00000000
[08:56:59.074]    __FlashOp=0x00000000
[08:56:59.074]    __Result=0x00000000
[08:56:59.074]    
[08:56:59.074]    // User-defined
[08:56:59.074]    DbgMCU_CR=0x00000007
[08:56:59.074]    DbgMCU_APB1_Fz=0x00000000
[08:56:59.074]    DbgMCU_APB2_Fz=0x00000000
[08:56:59.077]    DoOptionByteLoading=0x00000000
[08:56:59.077]  </debugvars>
[08:56:59.077]  
[08:56:59.077]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:56:59.077]    <block atomic="false" info="">
[08:56:59.077]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:56:59.079]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:56:59.079]    </block>
[08:56:59.079]    <block atomic="false" info="DbgMCU registers">
[08:56:59.079]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:56:59.081]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[08:56:59.081]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[08:56:59.081]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:56:59.083]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:56:59.083]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:56:59.083]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:56:59.083]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:56:59.085]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:56:59.085]    </block>
[08:56:59.086]  </sequence>
[08:56:59.086]  
[08:57:06.655]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[08:57:06.655]  
[08:57:06.656]  <debugvars>
[08:57:06.657]    // Pre-defined
[08:57:06.657]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:57:06.658]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:57:06.658]    __dp=0x00000000
[08:57:06.658]    __ap=0x00000000
[08:57:06.659]    __traceout=0x00000000      (Trace Disabled)
[08:57:06.660]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:57:06.660]    __FlashAddr=0x00000000
[08:57:06.661]    __FlashLen=0x00000000
[08:57:06.661]    __FlashArg=0x00000000
[08:57:06.661]    __FlashOp=0x00000000
[08:57:06.662]    __Result=0x00000000
[08:57:06.662]    
[08:57:06.662]    // User-defined
[08:57:06.662]    DbgMCU_CR=0x00000007
[08:57:06.663]    DbgMCU_APB1_Fz=0x00000000
[08:57:06.663]    DbgMCU_APB2_Fz=0x00000000
[08:57:06.663]    DoOptionByteLoading=0x00000000
[08:57:06.664]  </debugvars>
[08:57:06.664]  
[08:57:06.664]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[08:57:06.665]    <block atomic="false" info="">
[08:57:06.665]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[08:57:06.665]        // -> [connectionFlash <= 0x00000001]
[08:57:06.665]      __var FLASH_BASE = 0x40022000 ;
[08:57:06.665]        // -> [FLASH_BASE <= 0x40022000]
[08:57:06.666]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[08:57:06.666]        // -> [FLASH_CR <= 0x40022004]
[08:57:06.666]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[08:57:06.667]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[08:57:06.667]      __var LOCK_BIT = ( 1 << 0 ) ;
[08:57:06.667]        // -> [LOCK_BIT <= 0x00000001]
[08:57:06.668]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[08:57:06.668]        // -> [OPTLOCK_BIT <= 0x00000004]
[08:57:06.668]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[08:57:06.668]        // -> [FLASH_KEYR <= 0x4002200C]
[08:57:06.668]      __var FLASH_KEY1 = 0x89ABCDEF ;
[08:57:06.668]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[08:57:06.669]      __var FLASH_KEY2 = 0x02030405 ;
[08:57:06.669]        // -> [FLASH_KEY2 <= 0x02030405]
[08:57:06.669]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[08:57:06.669]        // -> [FLASH_OPTKEYR <= 0x40022014]
[08:57:06.669]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[08:57:06.670]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[08:57:06.670]      __var FLASH_OPTKEY2 = 0x24252627 ;
[08:57:06.670]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[08:57:06.670]      __var FLASH_CR_Value = 0 ;
[08:57:06.670]        // -> [FLASH_CR_Value <= 0x00000000]
[08:57:06.671]      __var DoDebugPortStop = 1 ;
[08:57:06.671]        // -> [DoDebugPortStop <= 0x00000001]
[08:57:06.671]      __var DP_CTRL_STAT = 0x4 ;
[08:57:06.672]        // -> [DP_CTRL_STAT <= 0x00000004]
[08:57:06.672]      __var DP_SELECT = 0x8 ;
[08:57:06.672]        // -> [DP_SELECT <= 0x00000008]
[08:57:06.673]    </block>
[08:57:06.673]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[08:57:06.673]      // if-block "connectionFlash && DoOptionByteLoading"
[08:57:06.673]        // =>  FALSE
[08:57:06.673]      // skip if-block "connectionFlash && DoOptionByteLoading"
[08:57:06.674]    </control>
[08:57:06.674]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[08:57:06.674]      // if-block "DoDebugPortStop"
[08:57:06.674]        // =>  TRUE
[08:57:06.674]      <block atomic="false" info="">
[08:57:06.674]        WriteDP(DP_SELECT, 0x00000000);
[08:57:06.675]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[08:57:06.675]        WriteDP(DP_CTRL_STAT, 0x00000000);
[08:57:06.676]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[08:57:06.676]      </block>
[08:57:06.677]      // end if-block "DoDebugPortStop"
[08:57:06.677]    </control>
[08:57:06.677]  </sequence>
[08:57:06.677]  
[09:04:19.297]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:04:19.297]  
[09:04:19.298]  <debugvars>
[09:04:19.298]    // Pre-defined
[09:04:19.298]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:04:19.299]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:04:19.299]    __dp=0x00000000
[09:04:19.299]    __ap=0x00000000
[09:04:19.300]    __traceout=0x00000000      (Trace Disabled)
[09:04:19.300]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:04:19.300]    __FlashAddr=0x00000000
[09:04:19.301]    __FlashLen=0x00000000
[09:04:19.301]    __FlashArg=0x00000000
[09:04:19.301]    __FlashOp=0x00000000
[09:04:19.301]    __Result=0x00000000
[09:04:19.301]    
[09:04:19.301]    // User-defined
[09:04:19.302]    DbgMCU_CR=0x00000007
[09:04:19.302]    DbgMCU_APB1_Fz=0x00000000
[09:04:19.302]    DbgMCU_APB2_Fz=0x00000000
[09:04:19.302]    DoOptionByteLoading=0x00000000
[09:04:19.303]  </debugvars>
[09:04:19.303]  
[09:04:19.303]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:04:19.303]    <block atomic="false" info="">
[09:04:19.304]      Sequence("CheckID");
[09:04:19.304]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:04:19.304]          <block atomic="false" info="">
[09:04:19.304]            __var pidr1 = 0;
[09:04:19.304]              // -> [pidr1 <= 0x00000000]
[09:04:19.305]            __var pidr2 = 0;
[09:04:19.305]              // -> [pidr2 <= 0x00000000]
[09:04:19.305]            __var jep106id = 0;
[09:04:19.305]              // -> [jep106id <= 0x00000000]
[09:04:19.305]            __var ROMTableBase = 0;
[09:04:19.306]              // -> [ROMTableBase <= 0x00000000]
[09:04:19.306]            __ap = 0;      // AHB-AP
[09:04:19.306]              // -> [__ap <= 0x00000000]
[09:04:19.306]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:04:19.307]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:04:19.308]              // -> [ROMTableBase <= 0xF0000000]
[09:04:19.308]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:04:19.309]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:04:19.309]              // -> [pidr1 <= 0x00000004]
[09:04:19.310]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:04:19.311]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:04:19.311]              // -> [pidr2 <= 0x0000000A]
[09:04:19.312]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:04:19.312]              // -> [jep106id <= 0x00000020]
[09:04:19.312]          </block>
[09:04:19.313]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:04:19.313]            // if-block "jep106id != 0x20"
[09:04:19.313]              // =>  FALSE
[09:04:19.314]            // skip if-block "jep106id != 0x20"
[09:04:19.314]          </control>
[09:04:19.314]        </sequence>
[09:04:19.315]    </block>
[09:04:19.315]  </sequence>
[09:04:19.315]  
[09:04:19.328]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:04:19.328]  
[09:04:19.352]  <debugvars>
[09:04:19.353]    // Pre-defined
[09:04:19.353]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:04:19.354]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:04:19.354]    __dp=0x00000000
[09:04:19.355]    __ap=0x00000000
[09:04:19.355]    __traceout=0x00000000      (Trace Disabled)
[09:04:19.357]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:04:19.357]    __FlashAddr=0x00000000
[09:04:19.358]    __FlashLen=0x00000000
[09:04:19.358]    __FlashArg=0x00000000
[09:04:19.359]    __FlashOp=0x00000000
[09:04:19.359]    __Result=0x00000000
[09:04:19.359]    
[09:04:19.359]    // User-defined
[09:04:19.360]    DbgMCU_CR=0x00000007
[09:04:19.360]    DbgMCU_APB1_Fz=0x00000000
[09:04:19.361]    DbgMCU_APB2_Fz=0x00000000
[09:04:19.361]    DoOptionByteLoading=0x00000000
[09:04:19.361]  </debugvars>
[09:04:19.361]  
[09:04:19.362]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:04:19.363]    <block atomic="false" info="">
[09:04:19.363]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:04:19.364]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:04:19.364]    </block>
[09:04:19.365]    <block atomic="false" info="DbgMCU registers">
[09:04:19.366]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:04:19.367]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[09:04:19.368]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[09:04:19.368]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:04:19.369]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:04:19.370]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:04:19.371]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:04:19.371]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:04:19.372]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:04:19.372]    </block>
[09:04:19.372]  </sequence>
[09:04:19.373]  
[09:04:27.120]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:04:27.120]  
[09:04:27.120]  <debugvars>
[09:04:27.122]    // Pre-defined
[09:04:27.123]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:04:27.123]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:04:27.124]    __dp=0x00000000
[09:04:27.124]    __ap=0x00000000
[09:04:27.125]    __traceout=0x00000000      (Trace Disabled)
[09:04:27.125]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:04:27.126]    __FlashAddr=0x00000000
[09:04:27.126]    __FlashLen=0x00000000
[09:04:27.127]    __FlashArg=0x00000000
[09:04:27.127]    __FlashOp=0x00000000
[09:04:27.127]    __Result=0x00000000
[09:04:27.128]    
[09:04:27.128]    // User-defined
[09:04:27.128]    DbgMCU_CR=0x00000007
[09:04:27.129]    DbgMCU_APB1_Fz=0x00000000
[09:04:27.129]    DbgMCU_APB2_Fz=0x00000000
[09:04:27.130]    DoOptionByteLoading=0x00000000
[09:04:27.130]  </debugvars>
[09:04:27.130]  
[09:04:27.131]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:04:27.131]    <block atomic="false" info="">
[09:04:27.131]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:04:27.132]        // -> [connectionFlash <= 0x00000001]
[09:04:27.132]      __var FLASH_BASE = 0x40022000 ;
[09:04:27.133]        // -> [FLASH_BASE <= 0x40022000]
[09:04:27.133]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:04:27.134]        // -> [FLASH_CR <= 0x40022004]
[09:04:27.134]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:04:27.134]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:04:27.134]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:04:27.134]        // -> [LOCK_BIT <= 0x00000001]
[09:04:27.134]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:04:27.135]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:04:27.135]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:04:27.135]        // -> [FLASH_KEYR <= 0x4002200C]
[09:04:27.135]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:04:27.135]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:04:27.135]      __var FLASH_KEY2 = 0x02030405 ;
[09:04:27.136]        // -> [FLASH_KEY2 <= 0x02030405]
[09:04:27.136]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:04:27.136]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:04:27.136]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:04:27.137]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:04:27.137]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:04:27.137]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:04:27.137]      __var FLASH_CR_Value = 0 ;
[09:04:27.137]        // -> [FLASH_CR_Value <= 0x00000000]
[09:04:27.138]      __var DoDebugPortStop = 1 ;
[09:04:27.138]        // -> [DoDebugPortStop <= 0x00000001]
[09:04:27.138]      __var DP_CTRL_STAT = 0x4 ;
[09:04:27.138]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:04:27.138]      __var DP_SELECT = 0x8 ;
[09:04:27.138]        // -> [DP_SELECT <= 0x00000008]
[09:04:27.139]    </block>
[09:04:27.139]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:04:27.139]      // if-block "connectionFlash && DoOptionByteLoading"
[09:04:27.139]        // =>  FALSE
[09:04:27.139]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:04:27.140]    </control>
[09:04:27.140]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:04:27.140]      // if-block "DoDebugPortStop"
[09:04:27.140]        // =>  TRUE
[09:04:27.140]      <block atomic="false" info="">
[09:04:27.141]        WriteDP(DP_SELECT, 0x00000000);
[09:04:27.141]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:04:27.141]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:04:27.142]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:04:27.142]      </block>
[09:04:27.142]      // end if-block "DoDebugPortStop"
[09:04:27.142]    </control>
[09:04:27.142]  </sequence>
[09:04:27.143]  
[09:34:21.246]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:34:21.246]  
[09:34:21.247]  <debugvars>
[09:34:21.247]    // Pre-defined
[09:34:21.247]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:34:21.248]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:34:21.248]    __dp=0x00000000
[09:34:21.248]    __ap=0x00000000
[09:34:21.248]    __traceout=0x00000000      (Trace Disabled)
[09:34:21.248]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:34:21.249]    __FlashAddr=0x00000000
[09:34:21.249]    __FlashLen=0x00000000
[09:34:21.249]    __FlashArg=0x00000000
[09:34:21.249]    __FlashOp=0x00000000
[09:34:21.249]    __Result=0x00000000
[09:34:21.250]    
[09:34:21.250]    // User-defined
[09:34:21.250]    DbgMCU_CR=0x00000007
[09:34:21.250]    DbgMCU_APB1_Fz=0x00000000
[09:34:21.250]    DbgMCU_APB2_Fz=0x00000000
[09:34:21.251]    DoOptionByteLoading=0x00000000
[09:34:21.251]  </debugvars>
[09:34:21.251]  
[09:34:21.251]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:34:21.251]    <block atomic="false" info="">
[09:34:21.252]      Sequence("CheckID");
[09:34:21.252]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:34:21.252]          <block atomic="false" info="">
[09:34:21.252]            __var pidr1 = 0;
[09:34:21.252]              // -> [pidr1 <= 0x00000000]
[09:34:21.253]            __var pidr2 = 0;
[09:34:21.253]              // -> [pidr2 <= 0x00000000]
[09:34:21.253]            __var jep106id = 0;
[09:34:21.253]              // -> [jep106id <= 0x00000000]
[09:34:21.253]            __var ROMTableBase = 0;
[09:34:21.254]              // -> [ROMTableBase <= 0x00000000]
[09:34:21.254]            __ap = 0;      // AHB-AP
[09:34:21.254]              // -> [__ap <= 0x00000000]
[09:34:21.254]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:34:21.255]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:34:21.255]              // -> [ROMTableBase <= 0xF0000000]
[09:34:21.255]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:34:21.256]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:34:21.256]              // -> [pidr1 <= 0x00000004]
[09:34:21.257]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:34:21.258]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:34:21.258]              // -> [pidr2 <= 0x0000000A]
[09:34:21.258]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:34:21.258]              // -> [jep106id <= 0x00000020]
[09:34:21.258]          </block>
[09:34:21.259]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:34:21.259]            // if-block "jep106id != 0x20"
[09:34:21.259]              // =>  FALSE
[09:34:21.259]            // skip if-block "jep106id != 0x20"
[09:34:21.260]          </control>
[09:34:21.260]        </sequence>
[09:34:21.260]    </block>
[09:34:21.260]  </sequence>
[09:34:21.260]  
[09:34:21.272]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:34:21.272]  
[09:34:21.272]  <debugvars>
[09:34:21.272]    // Pre-defined
[09:34:21.272]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:34:21.273]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:34:21.273]    __dp=0x00000000
[09:34:21.273]    __ap=0x00000000
[09:34:21.274]    __traceout=0x00000000      (Trace Disabled)
[09:34:21.274]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:34:21.274]    __FlashAddr=0x00000000
[09:34:21.275]    __FlashLen=0x00000000
[09:34:21.275]    __FlashArg=0x00000000
[09:34:21.275]    __FlashOp=0x00000000
[09:34:21.275]    __Result=0x00000000
[09:34:21.275]    
[09:34:21.275]    // User-defined
[09:34:21.276]    DbgMCU_CR=0x00000007
[09:34:21.276]    DbgMCU_APB1_Fz=0x00000000
[09:34:21.276]    DbgMCU_APB2_Fz=0x00000000
[09:34:21.276]    DoOptionByteLoading=0x00000000
[09:34:21.276]  </debugvars>
[09:34:21.277]  
[09:34:21.277]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:34:21.277]    <block atomic="false" info="">
[09:34:21.277]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:34:21.278]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:34:21.278]    </block>
[09:34:21.279]    <block atomic="false" info="DbgMCU registers">
[09:34:21.279]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:34:21.279]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[09:34:21.280]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[09:34:21.281]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:34:21.281]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:34:21.282]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:34:21.282]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:34:21.283]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:34:21.283]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:34:21.284]    </block>
[09:34:21.284]  </sequence>
[09:34:21.284]  
[09:34:28.871]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:34:28.871]  
[09:34:28.874]  <debugvars>
[09:34:28.874]    // Pre-defined
[09:34:28.874]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:34:28.875]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:34:28.875]    __dp=0x00000000
[09:34:28.876]    __ap=0x00000000
[09:34:28.876]    __traceout=0x00000000      (Trace Disabled)
[09:34:28.876]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:34:28.876]    __FlashAddr=0x00000000
[09:34:28.877]    __FlashLen=0x00000000
[09:34:28.877]    __FlashArg=0x00000000
[09:34:28.877]    __FlashOp=0x00000000
[09:34:28.877]    __Result=0x00000000
[09:34:28.877]    
[09:34:28.877]    // User-defined
[09:34:28.877]    DbgMCU_CR=0x00000007
[09:34:28.878]    DbgMCU_APB1_Fz=0x00000000
[09:34:28.878]    DbgMCU_APB2_Fz=0x00000000
[09:34:28.878]    DoOptionByteLoading=0x00000000
[09:34:28.878]  </debugvars>
[09:34:28.878]  
[09:34:28.879]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:34:28.879]    <block atomic="false" info="">
[09:34:28.879]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:34:28.879]        // -> [connectionFlash <= 0x00000001]
[09:34:28.879]      __var FLASH_BASE = 0x40022000 ;
[09:34:28.880]        // -> [FLASH_BASE <= 0x40022000]
[09:34:28.880]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:34:28.880]        // -> [FLASH_CR <= 0x40022004]
[09:34:28.881]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:34:28.881]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:34:28.881]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:34:28.881]        // -> [LOCK_BIT <= 0x00000001]
[09:34:28.881]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:34:28.882]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:34:28.882]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:34:28.882]        // -> [FLASH_KEYR <= 0x4002200C]
[09:34:28.882]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:34:28.882]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:34:28.882]      __var FLASH_KEY2 = 0x02030405 ;
[09:34:28.882]        // -> [FLASH_KEY2 <= 0x02030405]
[09:34:28.883]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:34:28.883]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:34:28.884]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:34:28.884]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:34:28.885]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:34:28.885]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:34:28.886]      __var FLASH_CR_Value = 0 ;
[09:34:28.886]        // -> [FLASH_CR_Value <= 0x00000000]
[09:34:28.886]      __var DoDebugPortStop = 1 ;
[09:34:28.886]        // -> [DoDebugPortStop <= 0x00000001]
[09:34:28.887]      __var DP_CTRL_STAT = 0x4 ;
[09:34:28.887]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:34:28.887]      __var DP_SELECT = 0x8 ;
[09:34:28.887]        // -> [DP_SELECT <= 0x00000008]
[09:34:28.888]    </block>
[09:34:28.888]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:34:28.888]      // if-block "connectionFlash && DoOptionByteLoading"
[09:34:28.888]        // =>  FALSE
[09:34:28.889]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:34:28.889]    </control>
[09:34:28.889]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:34:28.889]      // if-block "DoDebugPortStop"
[09:34:28.891]        // =>  TRUE
[09:34:28.891]      <block atomic="false" info="">
[09:34:28.891]        WriteDP(DP_SELECT, 0x00000000);
[09:34:28.892]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:34:28.892]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:34:28.892]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:34:28.893]      </block>
[09:34:28.893]      // end if-block "DoDebugPortStop"
[09:34:28.893]    </control>
[09:34:28.893]  </sequence>
[09:34:28.893]  
[09:34:39.792]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:34:39.792]  
[09:34:39.792]  <debugvars>
[09:34:39.792]    // Pre-defined
[09:34:39.792]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:34:39.792]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[09:34:39.792]    __dp=0x00000000
[09:34:39.792]    __ap=0x00000000
[09:34:39.792]    __traceout=0x00000000      (Trace Disabled)
[09:34:39.797]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:34:39.797]    __FlashAddr=0x00000000
[09:34:39.797]    __FlashLen=0x00000000
[09:34:39.797]    __FlashArg=0x00000000
[09:34:39.797]    __FlashOp=0x00000000
[09:34:39.797]    __Result=0x00000000
[09:34:39.797]    
[09:34:39.797]    // User-defined
[09:34:39.797]    DbgMCU_CR=0x00000007
[09:34:39.797]    DbgMCU_APB1_Fz=0x00000000
[09:34:39.797]    DbgMCU_APB2_Fz=0x00000000
[09:34:39.797]    DoOptionByteLoading=0x00000000
[09:34:39.797]  </debugvars>
[09:34:39.797]  
[09:34:39.797]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:34:39.797]    <block atomic="false" info="">
[09:34:39.797]      Sequence("CheckID");
[09:34:39.797]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:34:39.797]          <block atomic="false" info="">
[09:34:39.802]            __var pidr1 = 0;
[09:34:39.802]              // -> [pidr1 <= 0x00000000]
[09:34:39.802]            __var pidr2 = 0;
[09:34:39.802]              // -> [pidr2 <= 0x00000000]
[09:34:39.802]            __var jep106id = 0;
[09:34:39.802]              // -> [jep106id <= 0x00000000]
[09:34:39.802]            __var ROMTableBase = 0;
[09:34:39.802]              // -> [ROMTableBase <= 0x00000000]
[09:34:39.804]            __ap = 0;      // AHB-AP
[09:34:39.804]              // -> [__ap <= 0x00000000]
[09:34:39.804]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:34:39.804]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:34:39.804]              // -> [ROMTableBase <= 0xF0000000]
[09:34:39.804]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:34:39.804]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:34:39.807]              // -> [pidr1 <= 0x00000004]
[09:34:39.807]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:34:39.807]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:34:39.807]              // -> [pidr2 <= 0x0000000A]
[09:34:39.807]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:34:39.807]              // -> [jep106id <= 0x00000020]
[09:34:39.807]          </block>
[09:34:39.807]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:34:39.807]            // if-block "jep106id != 0x20"
[09:34:39.807]              // =>  FALSE
[09:34:39.807]            // skip if-block "jep106id != 0x20"
[09:34:39.807]          </control>
[09:34:39.807]        </sequence>
[09:34:39.807]    </block>
[09:34:39.807]  </sequence>
[09:34:39.807]  
[09:34:39.823]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:34:39.823]  
[09:34:39.842]  <debugvars>
[09:34:39.843]    // Pre-defined
[09:34:39.843]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:34:39.843]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[09:34:39.843]    __dp=0x00000000
[09:34:39.843]    __ap=0x00000000
[09:34:39.843]    __traceout=0x00000000      (Trace Disabled)
[09:34:39.847]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:34:39.847]    __FlashAddr=0x00000000
[09:34:39.847]    __FlashLen=0x00000000
[09:34:39.847]    __FlashArg=0x00000000
[09:34:39.847]    __FlashOp=0x00000000
[09:34:39.847]    __Result=0x00000000
[09:34:39.847]    
[09:34:39.847]    // User-defined
[09:34:39.847]    DbgMCU_CR=0x00000007
[09:34:39.852]    DbgMCU_APB1_Fz=0x00000000
[09:34:39.852]    DbgMCU_APB2_Fz=0x00000000
[09:34:39.852]    DoOptionByteLoading=0x00000000
[09:34:39.852]  </debugvars>
[09:34:39.852]  
[09:34:39.852]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:34:39.852]    <block atomic="false" info="">
[09:34:39.852]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:34:39.859]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:34:39.892]    </block>
[09:34:39.894]    <block atomic="false" info="DbgMCU registers">
[09:34:39.895]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:34:39.897]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[09:34:39.899]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[09:34:39.899]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:34:39.901]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:34:39.901]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:34:39.903]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:34:39.903]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:34:39.904]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:34:39.906]    </block>
[09:34:39.906]  </sequence>
[09:34:39.906]  
[09:35:19.852]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:35:19.852]  
[09:35:19.853]  <debugvars>
[09:35:19.854]    // Pre-defined
[09:35:19.854]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:35:19.854]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[09:35:19.855]    __dp=0x00000000
[09:35:19.855]    __ap=0x00000000
[09:35:19.855]    __traceout=0x00000000      (Trace Disabled)
[09:35:19.855]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:35:19.856]    __FlashAddr=0x00000000
[09:35:19.856]    __FlashLen=0x00000000
[09:35:19.856]    __FlashArg=0x00000000
[09:35:19.856]    __FlashOp=0x00000000
[09:35:19.857]    __Result=0x00000000
[09:35:19.857]    
[09:35:19.857]    // User-defined
[09:35:19.857]    DbgMCU_CR=0x00000007
[09:35:19.857]    DbgMCU_APB1_Fz=0x00000000
[09:35:19.857]    DbgMCU_APB2_Fz=0x00000000
[09:35:19.857]    DoOptionByteLoading=0x00000000
[09:35:19.857]  </debugvars>
[09:35:19.857]  
[09:35:19.857]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:35:19.857]    <block atomic="false" info="">
[09:35:19.857]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:35:19.857]        // -> [connectionFlash <= 0x00000000]
[09:35:19.857]      __var FLASH_BASE = 0x40022000 ;
[09:35:19.857]        // -> [FLASH_BASE <= 0x40022000]
[09:35:19.857]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:35:19.857]        // -> [FLASH_CR <= 0x40022004]
[09:35:19.859]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:35:19.860]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:35:19.860]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:35:19.860]        // -> [LOCK_BIT <= 0x00000001]
[09:35:19.860]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:35:19.860]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:35:19.861]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:35:19.861]        // -> [FLASH_KEYR <= 0x4002200C]
[09:35:19.861]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:35:19.861]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:35:19.861]      __var FLASH_KEY2 = 0x02030405 ;
[09:35:19.861]        // -> [FLASH_KEY2 <= 0x02030405]
[09:35:19.861]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:35:19.861]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:35:19.862]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:35:19.862]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:35:19.862]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:35:19.863]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:35:19.863]      __var FLASH_CR_Value = 0 ;
[09:35:19.863]        // -> [FLASH_CR_Value <= 0x00000000]
[09:35:19.863]      __var DoDebugPortStop = 1 ;
[09:35:19.863]        // -> [DoDebugPortStop <= 0x00000001]
[09:35:19.863]      __var DP_CTRL_STAT = 0x4 ;
[09:35:19.864]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:35:19.864]      __var DP_SELECT = 0x8 ;
[09:35:19.864]        // -> [DP_SELECT <= 0x00000008]
[09:35:19.864]    </block>
[09:35:19.864]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:35:19.864]      // if-block "connectionFlash && DoOptionByteLoading"
[09:35:19.864]        // =>  FALSE
[09:35:19.864]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:35:19.865]    </control>
[09:35:19.865]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:35:19.866]      // if-block "DoDebugPortStop"
[09:35:19.866]        // =>  TRUE
[09:35:19.866]      <block atomic="false" info="">
[09:35:19.866]        WriteDP(DP_SELECT, 0x00000000);
[09:35:19.866]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:35:19.867]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:35:19.867]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:35:19.867]      </block>
[09:35:19.867]      // end if-block "DoDebugPortStop"
[09:35:19.868]    </control>
[09:35:19.868]  </sequence>
[09:35:19.868]  
[10:06:20.174]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:06:20.174]  
[10:06:20.175]  <debugvars>
[10:06:20.175]    // Pre-defined
[10:06:20.175]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:06:20.176]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:06:20.176]    __dp=0x00000000
[10:06:20.176]    __ap=0x00000000
[10:06:20.176]    __traceout=0x00000000      (Trace Disabled)
[10:06:20.177]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:06:20.177]    __FlashAddr=0x00000000
[10:06:20.177]    __FlashLen=0x00000000
[10:06:20.177]    __FlashArg=0x00000000
[10:06:20.178]    __FlashOp=0x00000000
[10:06:20.178]    __Result=0x00000000
[10:06:20.178]    
[10:06:20.178]    // User-defined
[10:06:20.178]    DbgMCU_CR=0x00000007
[10:06:20.178]    DbgMCU_APB1_Fz=0x00000000
[10:06:20.178]    DbgMCU_APB2_Fz=0x00000000
[10:06:20.179]    DoOptionByteLoading=0x00000000
[10:06:20.179]  </debugvars>
[10:06:20.179]  
[10:06:20.179]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:06:20.179]    <block atomic="false" info="">
[10:06:20.180]      Sequence("CheckID");
[10:06:20.180]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:06:20.180]          <block atomic="false" info="">
[10:06:20.180]            __var pidr1 = 0;
[10:06:20.180]              // -> [pidr1 <= 0x00000000]
[10:06:20.181]            __var pidr2 = 0;
[10:06:20.181]              // -> [pidr2 <= 0x00000000]
[10:06:20.181]            __var jep106id = 0;
[10:06:20.181]              // -> [jep106id <= 0x00000000]
[10:06:20.181]            __var ROMTableBase = 0;
[10:06:20.181]              // -> [ROMTableBase <= 0x00000000]
[10:06:20.182]            __ap = 0;      // AHB-AP
[10:06:20.182]              // -> [__ap <= 0x00000000]
[10:06:20.182]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:06:20.183]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:06:20.183]              // -> [ROMTableBase <= 0xF0000000]
[10:06:20.184]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:06:20.185]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:06:20.185]              // -> [pidr1 <= 0x00000004]
[10:06:20.186]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:06:20.186]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:06:20.187]              // -> [pidr2 <= 0x0000000A]
[10:06:20.187]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:06:20.187]              // -> [jep106id <= 0x00000020]
[10:06:20.187]          </block>
[10:06:20.188]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:06:20.188]            // if-block "jep106id != 0x20"
[10:06:20.188]              // =>  FALSE
[10:06:20.189]            // skip if-block "jep106id != 0x20"
[10:06:20.189]          </control>
[10:06:20.189]        </sequence>
[10:06:20.189]    </block>
[10:06:20.189]  </sequence>
[10:06:20.190]  
[10:06:20.202]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:06:20.202]  
[10:06:20.216]  <debugvars>
[10:06:20.217]    // Pre-defined
[10:06:20.218]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:06:20.218]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:06:20.219]    __dp=0x00000000
[10:06:20.219]    __ap=0x00000000
[10:06:20.220]    __traceout=0x00000000      (Trace Disabled)
[10:06:20.220]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:06:20.221]    __FlashAddr=0x00000000
[10:06:20.222]    __FlashLen=0x00000000
[10:06:20.222]    __FlashArg=0x00000000
[10:06:20.223]    __FlashOp=0x00000000
[10:06:20.223]    __Result=0x00000000
[10:06:20.224]    
[10:06:20.224]    // User-defined
[10:06:20.224]    DbgMCU_CR=0x00000007
[10:06:20.224]    DbgMCU_APB1_Fz=0x00000000
[10:06:20.224]    DbgMCU_APB2_Fz=0x00000000
[10:06:20.224]    DoOptionByteLoading=0x00000000
[10:06:20.224]  </debugvars>
[10:06:20.227]  
[10:06:20.227]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:06:20.227]    <block atomic="false" info="">
[10:06:20.228]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:06:20.229]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:06:20.229]    </block>
[10:06:20.229]    <block atomic="false" info="DbgMCU registers">
[10:06:20.230]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:06:20.231]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[10:06:20.231]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[10:06:20.232]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:06:20.232]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:06:20.233]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:06:20.234]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:06:20.234]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:06:20.235]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:06:20.235]    </block>
[10:06:20.236]  </sequence>
[10:06:20.236]  
[10:06:27.809]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:06:27.809]  
[10:06:27.810]  <debugvars>
[10:06:27.810]    // Pre-defined
[10:06:27.811]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:06:27.811]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:06:27.812]    __dp=0x00000000
[10:06:27.812]    __ap=0x00000000
[10:06:27.812]    __traceout=0x00000000      (Trace Disabled)
[10:06:27.813]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:06:27.813]    __FlashAddr=0x00000000
[10:06:27.813]    __FlashLen=0x00000000
[10:06:27.813]    __FlashArg=0x00000000
[10:06:27.813]    __FlashOp=0x00000000
[10:06:27.814]    __Result=0x00000000
[10:06:27.814]    
[10:06:27.814]    // User-defined
[10:06:27.814]    DbgMCU_CR=0x00000007
[10:06:27.814]    DbgMCU_APB1_Fz=0x00000000
[10:06:27.814]    DbgMCU_APB2_Fz=0x00000000
[10:06:27.815]    DoOptionByteLoading=0x00000000
[10:06:27.815]  </debugvars>
[10:06:27.815]  
[10:06:27.815]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:06:27.815]    <block atomic="false" info="">
[10:06:27.815]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:06:27.816]        // -> [connectionFlash <= 0x00000001]
[10:06:27.816]      __var FLASH_BASE = 0x40022000 ;
[10:06:27.816]        // -> [FLASH_BASE <= 0x40022000]
[10:06:27.816]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:06:27.816]        // -> [FLASH_CR <= 0x40022004]
[10:06:27.816]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:06:27.817]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:06:27.817]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:06:27.817]        // -> [LOCK_BIT <= 0x00000001]
[10:06:27.817]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:06:27.817]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:06:27.818]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:06:27.818]        // -> [FLASH_KEYR <= 0x4002200C]
[10:06:27.818]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:06:27.818]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:06:27.818]      __var FLASH_KEY2 = 0x02030405 ;
[10:06:27.819]        // -> [FLASH_KEY2 <= 0x02030405]
[10:06:27.819]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:06:27.819]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:06:27.819]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:06:27.819]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:06:27.820]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:06:27.820]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:06:27.820]      __var FLASH_CR_Value = 0 ;
[10:06:27.820]        // -> [FLASH_CR_Value <= 0x00000000]
[10:06:27.820]      __var DoDebugPortStop = 1 ;
[10:06:27.820]        // -> [DoDebugPortStop <= 0x00000001]
[10:06:27.821]      __var DP_CTRL_STAT = 0x4 ;
[10:06:27.821]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:06:27.821]      __var DP_SELECT = 0x8 ;
[10:06:27.821]        // -> [DP_SELECT <= 0x00000008]
[10:06:27.821]    </block>
[10:06:27.821]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:06:27.821]      // if-block "connectionFlash && DoOptionByteLoading"
[10:06:27.822]        // =>  FALSE
[10:06:27.822]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:06:27.822]    </control>
[10:06:27.822]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:06:27.824]      // if-block "DoDebugPortStop"
[10:06:27.824]        // =>  TRUE
[10:06:27.825]      <block atomic="false" info="">
[10:06:27.825]        WriteDP(DP_SELECT, 0x00000000);
[10:06:27.826]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:06:27.826]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:06:27.826]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:06:27.827]      </block>
[10:06:27.827]      // end if-block "DoDebugPortStop"
[10:06:27.827]    </control>
[10:06:27.827]  </sequence>
[10:06:27.827]  
[10:45:26.409]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:45:26.409]  
[10:45:26.410]  <debugvars>
[10:45:26.410]    // Pre-defined
[10:45:26.410]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:45:26.410]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:45:26.411]    __dp=0x00000000
[10:45:26.411]    __ap=0x00000000
[10:45:26.411]    __traceout=0x00000000      (Trace Disabled)
[10:45:26.411]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:45:26.412]    __FlashAddr=0x00000000
[10:45:26.412]    __FlashLen=0x00000000
[10:45:26.412]    __FlashArg=0x00000000
[10:45:26.413]    __FlashOp=0x00000000
[10:45:26.413]    __Result=0x00000000
[10:45:26.413]    
[10:45:26.413]    // User-defined
[10:45:26.413]    DbgMCU_CR=0x00000007
[10:45:26.413]    DbgMCU_APB1_Fz=0x00000000
[10:45:26.413]    DbgMCU_APB2_Fz=0x00000000
[10:45:26.414]    DoOptionByteLoading=0x00000000
[10:45:26.414]  </debugvars>
[10:45:26.414]  
[10:45:26.414]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:45:26.414]    <block atomic="false" info="">
[10:45:26.415]      Sequence("CheckID");
[10:45:26.415]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:45:26.415]          <block atomic="false" info="">
[10:45:26.415]            __var pidr1 = 0;
[10:45:26.415]              // -> [pidr1 <= 0x00000000]
[10:45:26.416]            __var pidr2 = 0;
[10:45:26.416]              // -> [pidr2 <= 0x00000000]
[10:45:26.416]            __var jep106id = 0;
[10:45:26.416]              // -> [jep106id <= 0x00000000]
[10:45:26.416]            __var ROMTableBase = 0;
[10:45:26.416]              // -> [ROMTableBase <= 0x00000000]
[10:45:26.417]            __ap = 0;      // AHB-AP
[10:45:26.417]              // -> [__ap <= 0x00000000]
[10:45:26.417]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:45:26.418]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:45:26.418]              // -> [ROMTableBase <= 0xF0000000]
[10:45:26.418]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:45:26.421]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:45:26.421]              // -> [pidr1 <= 0x00000004]
[10:45:26.421]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:45:26.422]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:45:26.422]              // -> [pidr2 <= 0x0000000A]
[10:45:26.422]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:45:26.423]              // -> [jep106id <= 0x00000020]
[10:45:26.423]          </block>
[10:45:26.424]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:45:26.424]            // if-block "jep106id != 0x20"
[10:45:26.424]              // =>  FALSE
[10:45:26.425]            // skip if-block "jep106id != 0x20"
[10:45:26.425]          </control>
[10:45:26.425]        </sequence>
[10:45:26.426]    </block>
[10:45:26.426]  </sequence>
[10:45:26.426]  
[10:45:26.438]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:45:26.438]  
[10:45:26.439]  <debugvars>
[10:45:26.439]    // Pre-defined
[10:45:26.440]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:45:26.440]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:45:26.440]    __dp=0x00000000
[10:45:26.440]    __ap=0x00000000
[10:45:26.441]    __traceout=0x00000000      (Trace Disabled)
[10:45:26.441]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:45:26.441]    __FlashAddr=0x00000000
[10:45:26.441]    __FlashLen=0x00000000
[10:45:26.442]    __FlashArg=0x00000000
[10:45:26.442]    __FlashOp=0x00000000
[10:45:26.442]    __Result=0x00000000
[10:45:26.443]    
[10:45:26.443]    // User-defined
[10:45:26.443]    DbgMCU_CR=0x00000007
[10:45:26.443]    DbgMCU_APB1_Fz=0x00000000
[10:45:26.443]    DbgMCU_APB2_Fz=0x00000000
[10:45:26.444]    DoOptionByteLoading=0x00000000
[10:45:26.444]  </debugvars>
[10:45:26.444]  
[10:45:26.444]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:45:26.444]    <block atomic="false" info="">
[10:45:26.445]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:45:26.446]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:45:26.446]    </block>
[10:45:26.446]    <block atomic="false" info="DbgMCU registers">
[10:45:26.446]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:45:26.446]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[10:45:26.446]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[10:45:26.446]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:45:26.448]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:45:26.449]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:45:26.450]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:45:26.450]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:45:26.451]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:45:26.451]    </block>
[10:45:26.451]  </sequence>
[10:45:26.451]  
[10:45:34.385]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:45:34.385]  
[10:45:34.385]  <debugvars>
[10:45:34.385]    // Pre-defined
[10:45:34.385]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:45:34.385]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:45:34.387]    __dp=0x00000000
[10:45:34.387]    __ap=0x00000000
[10:45:34.387]    __traceout=0x00000000      (Trace Disabled)
[10:45:34.387]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:45:34.388]    __FlashAddr=0x00000000
[10:45:34.389]    __FlashLen=0x00000000
[10:45:34.389]    __FlashArg=0x00000000
[10:45:34.389]    __FlashOp=0x00000000
[10:45:34.389]    __Result=0x00000000
[10:45:34.389]    
[10:45:34.389]    // User-defined
[10:45:34.389]    DbgMCU_CR=0x00000007
[10:45:34.390]    DbgMCU_APB1_Fz=0x00000000
[10:45:34.390]    DbgMCU_APB2_Fz=0x00000000
[10:45:34.390]    DoOptionByteLoading=0x00000000
[10:45:34.392]  </debugvars>
[10:45:34.392]  
[10:45:34.392]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:45:34.394]    <block atomic="false" info="">
[10:45:34.395]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:45:34.395]        // -> [connectionFlash <= 0x00000001]
[10:45:34.395]      __var FLASH_BASE = 0x40022000 ;
[10:45:34.395]        // -> [FLASH_BASE <= 0x40022000]
[10:45:34.395]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:45:34.395]        // -> [FLASH_CR <= 0x40022004]
[10:45:34.395]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:45:34.397]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:45:34.397]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:45:34.397]        // -> [LOCK_BIT <= 0x00000001]
[10:45:34.397]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:45:34.397]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:45:34.397]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:45:34.397]        // -> [FLASH_KEYR <= 0x4002200C]
[10:45:34.397]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:45:34.399]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:45:34.399]      __var FLASH_KEY2 = 0x02030405 ;
[10:45:34.399]        // -> [FLASH_KEY2 <= 0x02030405]
[10:45:34.399]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:45:34.400]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:45:34.400]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:45:34.400]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:45:34.400]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:45:34.400]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:45:34.400]      __var FLASH_CR_Value = 0 ;
[10:45:34.400]        // -> [FLASH_CR_Value <= 0x00000000]
[10:45:34.402]      __var DoDebugPortStop = 1 ;
[10:45:34.402]        // -> [DoDebugPortStop <= 0x00000001]
[10:45:34.402]      __var DP_CTRL_STAT = 0x4 ;
[10:45:34.402]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:45:34.402]      __var DP_SELECT = 0x8 ;
[10:45:34.402]        // -> [DP_SELECT <= 0x00000008]
[10:45:34.402]    </block>
[10:45:34.402]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:45:34.402]      // if-block "connectionFlash && DoOptionByteLoading"
[10:45:34.402]        // =>  FALSE
[10:45:34.404]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:45:34.404]    </control>
[10:45:34.405]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:45:34.405]      // if-block "DoDebugPortStop"
[10:45:34.406]        // =>  TRUE
[10:45:34.406]      <block atomic="false" info="">
[10:45:34.406]        WriteDP(DP_SELECT, 0x00000000);
[10:45:34.406]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:45:34.407]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:45:34.407]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:45:34.409]      </block>
[10:45:34.409]      // end if-block "DoDebugPortStop"
[10:45:34.410]    </control>
[10:45:34.410]  </sequence>
[10:45:34.410]  
[10:51:16.831]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:51:16.831]  
[10:51:16.831]  <debugvars>
[10:51:16.831]    // Pre-defined
[10:51:16.831]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:51:16.831]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:51:16.831]    __dp=0x00000000
[10:51:16.833]    __ap=0x00000000
[10:51:16.833]    __traceout=0x00000000      (Trace Disabled)
[10:51:16.833]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:51:16.833]    __FlashAddr=0x00000000
[10:51:16.833]    __FlashLen=0x00000000
[10:51:16.833]    __FlashArg=0x00000000
[10:51:16.834]    __FlashOp=0x00000000
[10:51:16.834]    __Result=0x00000000
[10:51:16.834]    
[10:51:16.834]    // User-defined
[10:51:16.834]    DbgMCU_CR=0x00000007
[10:51:16.834]    DbgMCU_APB1_Fz=0x00000000
[10:51:16.834]    DbgMCU_APB2_Fz=0x00000000
[10:51:16.834]    DoOptionByteLoading=0x00000000
[10:51:16.834]  </debugvars>
[10:51:16.834]  
[10:51:16.834]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:51:16.834]    <block atomic="false" info="">
[10:51:16.834]      Sequence("CheckID");
[10:51:16.834]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:51:16.836]          <block atomic="false" info="">
[10:51:16.836]            __var pidr1 = 0;
[10:51:16.836]              // -> [pidr1 <= 0x00000000]
[10:51:16.836]            __var pidr2 = 0;
[10:51:16.836]              // -> [pidr2 <= 0x00000000]
[10:51:16.836]            __var jep106id = 0;
[10:51:16.836]              // -> [jep106id <= 0x00000000]
[10:51:16.838]            __var ROMTableBase = 0;
[10:51:16.838]              // -> [ROMTableBase <= 0x00000000]
[10:51:16.838]            __ap = 0;      // AHB-AP
[10:51:16.838]              // -> [__ap <= 0x00000000]
[10:51:16.838]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:51:16.838]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:51:16.838]              // -> [ROMTableBase <= 0xF0000000]
[10:51:16.838]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:51:16.840]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:51:16.840]              // -> [pidr1 <= 0x00000004]
[10:51:16.840]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:51:16.842]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:51:16.842]              // -> [pidr2 <= 0x0000000A]
[10:51:16.843]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:51:16.843]              // -> [jep106id <= 0x00000020]
[10:51:16.843]          </block>
[10:51:16.843]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:51:16.843]            // if-block "jep106id != 0x20"
[10:51:16.843]              // =>  FALSE
[10:51:16.844]            // skip if-block "jep106id != 0x20"
[10:51:16.844]          </control>
[10:51:16.844]        </sequence>
[10:51:16.844]    </block>
[10:51:16.845]  </sequence>
[10:51:16.845]  
[10:51:16.858]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:51:16.858]  
[10:51:16.866]  <debugvars>
[10:51:16.866]    // Pre-defined
[10:51:16.867]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:51:16.867]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:51:16.867]    __dp=0x00000000
[10:51:16.867]    __ap=0x00000000
[10:51:16.867]    __traceout=0x00000000      (Trace Disabled)
[10:51:16.867]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:51:16.867]    __FlashAddr=0x00000000
[10:51:16.867]    __FlashLen=0x00000000
[10:51:16.867]    __FlashArg=0x00000000
[10:51:16.868]    __FlashOp=0x00000000
[10:51:16.868]    __Result=0x00000000
[10:51:16.868]    
[10:51:16.868]    // User-defined
[10:51:16.868]    DbgMCU_CR=0x00000007
[10:51:16.868]    DbgMCU_APB1_Fz=0x00000000
[10:51:16.868]    DbgMCU_APB2_Fz=0x00000000
[10:51:16.870]    DoOptionByteLoading=0x00000000
[10:51:16.870]  </debugvars>
[10:51:16.870]  
[10:51:16.870]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:51:16.870]    <block atomic="false" info="">
[10:51:16.871]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:51:16.871]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:51:16.872]    </block>
[10:51:16.872]    <block atomic="false" info="DbgMCU registers">
[10:51:16.872]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:51:16.873]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[10:51:16.873]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[10:51:16.873]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:51:16.874]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:51:16.874]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:51:16.875]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:51:16.875]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:51:16.876]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:51:16.876]    </block>
[10:51:16.876]  </sequence>
[10:51:16.877]  
[10:51:24.727]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:51:24.727]  
[10:51:24.728]  <debugvars>
[10:51:24.728]    // Pre-defined
[10:51:24.729]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:51:24.729]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:51:24.729]    __dp=0x00000000
[10:51:24.729]    __ap=0x00000000
[10:51:24.730]    __traceout=0x00000000      (Trace Disabled)
[10:51:24.730]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:51:24.730]    __FlashAddr=0x00000000
[10:51:24.731]    __FlashLen=0x00000000
[10:51:24.731]    __FlashArg=0x00000000
[10:51:24.731]    __FlashOp=0x00000000
[10:51:24.732]    __Result=0x00000000
[10:51:24.732]    
[10:51:24.732]    // User-defined
[10:51:24.733]    DbgMCU_CR=0x00000007
[10:51:24.733]    DbgMCU_APB1_Fz=0x00000000
[10:51:24.733]    DbgMCU_APB2_Fz=0x00000000
[10:51:24.734]    DoOptionByteLoading=0x00000000
[10:51:24.734]  </debugvars>
[10:51:24.734]  
[10:51:24.734]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:51:24.734]    <block atomic="false" info="">
[10:51:24.734]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:51:24.734]        // -> [connectionFlash <= 0x00000001]
[10:51:24.734]      __var FLASH_BASE = 0x40022000 ;
[10:51:24.735]        // -> [FLASH_BASE <= 0x40022000]
[10:51:24.735]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:51:24.735]        // -> [FLASH_CR <= 0x40022004]
[10:51:24.735]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:51:24.735]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:51:24.735]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:51:24.735]        // -> [LOCK_BIT <= 0x00000001]
[10:51:24.735]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:51:24.735]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:51:24.735]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:51:24.735]        // -> [FLASH_KEYR <= 0x4002200C]
[10:51:24.735]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:51:24.735]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:51:24.735]      __var FLASH_KEY2 = 0x02030405 ;
[10:51:24.735]        // -> [FLASH_KEY2 <= 0x02030405]
[10:51:24.735]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:51:24.735]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:51:24.735]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:51:24.735]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:51:24.735]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:51:24.735]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:51:24.735]      __var FLASH_CR_Value = 0 ;
[10:51:24.735]        // -> [FLASH_CR_Value <= 0x00000000]
[10:51:24.735]      __var DoDebugPortStop = 1 ;
[10:51:24.735]        // -> [DoDebugPortStop <= 0x00000001]
[10:51:24.740]      __var DP_CTRL_STAT = 0x4 ;
[10:51:24.740]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:51:24.740]      __var DP_SELECT = 0x8 ;
[10:51:24.740]        // -> [DP_SELECT <= 0x00000008]
[10:51:24.740]    </block>
[10:51:24.740]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:51:24.740]      // if-block "connectionFlash && DoOptionByteLoading"
[10:51:24.740]        // =>  FALSE
[10:51:24.740]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:51:24.742]    </control>
[10:51:24.742]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:51:24.743]      // if-block "DoDebugPortStop"
[10:51:24.743]        // =>  TRUE
[10:51:24.743]      <block atomic="false" info="">
[10:51:24.743]        WriteDP(DP_SELECT, 0x00000000);
[10:51:24.744]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:51:24.744]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:51:24.744]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:51:24.745]      </block>
[10:51:24.745]      // end if-block "DoDebugPortStop"
[10:51:24.745]    </control>
[10:51:24.746]  </sequence>
[10:51:24.746]  
[10:53:41.797]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:53:41.797]  
[10:53:41.798]  <debugvars>
[10:53:41.798]    // Pre-defined
[10:53:41.798]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:53:41.798]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:53:41.798]    __dp=0x00000000
[10:53:41.798]    __ap=0x00000000
[10:53:41.798]    __traceout=0x00000000      (Trace Disabled)
[10:53:41.798]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:53:41.798]    __FlashAddr=0x00000000
[10:53:41.798]    __FlashLen=0x00000000
[10:53:41.801]    __FlashArg=0x00000000
[10:53:41.801]    __FlashOp=0x00000000
[10:53:41.801]    __Result=0x00000000
[10:53:41.801]    
[10:53:41.801]    // User-defined
[10:53:41.802]    DbgMCU_CR=0x00000007
[10:53:41.802]    DbgMCU_APB1_Fz=0x00000000
[10:53:41.802]    DbgMCU_APB2_Fz=0x00000000
[10:53:41.802]    DoOptionByteLoading=0x00000000
[10:53:41.803]  </debugvars>
[10:53:41.803]  
[10:53:41.803]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:53:41.803]    <block atomic="false" info="">
[10:53:41.803]      Sequence("CheckID");
[10:53:41.804]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:53:41.804]          <block atomic="false" info="">
[10:53:41.804]            __var pidr1 = 0;
[10:53:41.804]              // -> [pidr1 <= 0x00000000]
[10:53:41.805]            __var pidr2 = 0;
[10:53:41.805]              // -> [pidr2 <= 0x00000000]
[10:53:41.805]            __var jep106id = 0;
[10:53:41.806]              // -> [jep106id <= 0x00000000]
[10:53:41.806]            __var ROMTableBase = 0;
[10:53:41.806]              // -> [ROMTableBase <= 0x00000000]
[10:53:41.807]            __ap = 0;      // AHB-AP
[10:53:41.807]              // -> [__ap <= 0x00000000]
[10:53:41.807]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:53:41.808]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:53:41.808]              // -> [ROMTableBase <= 0xF0000000]
[10:53:41.809]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:53:41.810]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:53:41.810]              // -> [pidr1 <= 0x00000004]
[10:53:41.810]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:53:41.811]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:53:41.812]              // -> [pidr2 <= 0x0000000A]
[10:53:41.812]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:53:41.812]              // -> [jep106id <= 0x00000020]
[10:53:41.813]          </block>
[10:53:41.813]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:53:41.813]            // if-block "jep106id != 0x20"
[10:53:41.813]              // =>  FALSE
[10:53:41.813]            // skip if-block "jep106id != 0x20"
[10:53:41.813]          </control>
[10:53:41.814]        </sequence>
[10:53:41.814]    </block>
[10:53:41.814]  </sequence>
[10:53:41.814]  
[10:53:41.826]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:53:41.826]  
[10:53:41.827]  <debugvars>
[10:53:41.827]    // Pre-defined
[10:53:41.827]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:53:41.828]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:53:41.828]    __dp=0x00000000
[10:53:41.828]    __ap=0x00000000
[10:53:41.828]    __traceout=0x00000000      (Trace Disabled)
[10:53:41.829]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:53:41.829]    __FlashAddr=0x00000000
[10:53:41.829]    __FlashLen=0x00000000
[10:53:41.830]    __FlashArg=0x00000000
[10:53:41.830]    __FlashOp=0x00000000
[10:53:41.830]    __Result=0x00000000
[10:53:41.830]    
[10:53:41.830]    // User-defined
[10:53:41.830]    DbgMCU_CR=0x00000007
[10:53:41.831]    DbgMCU_APB1_Fz=0x00000000
[10:53:41.831]    DbgMCU_APB2_Fz=0x00000000
[10:53:41.831]    DoOptionByteLoading=0x00000000
[10:53:41.831]  </debugvars>
[10:53:41.831]  
[10:53:41.831]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:53:41.832]    <block atomic="false" info="">
[10:53:41.832]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:53:41.833]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:53:41.833]    </block>
[10:53:41.833]    <block atomic="false" info="DbgMCU registers">
[10:53:41.833]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:53:41.834]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[10:53:41.835]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[10:53:41.835]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:53:41.836]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:53:41.836]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:53:41.838]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:53:41.838]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:53:41.839]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:53:41.839]    </block>
[10:53:41.839]  </sequence>
[10:53:41.840]  
[10:56:04.127]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:56:04.127]  
[10:56:04.128]  <debugvars>
[10:56:04.128]    // Pre-defined
[10:56:04.128]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:56:04.129]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:56:04.129]    __dp=0x00000000
[10:56:04.129]    __ap=0x00000000
[10:56:04.130]    __traceout=0x00000000      (Trace Disabled)
[10:56:04.130]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:56:04.131]    __FlashAddr=0x00000000
[10:56:04.131]    __FlashLen=0x00000000
[10:56:04.131]    __FlashArg=0x00000000
[10:56:04.131]    __FlashOp=0x00000000
[10:56:04.131]    __Result=0x00000000
[10:56:04.132]    
[10:56:04.132]    // User-defined
[10:56:04.133]    DbgMCU_CR=0x00000007
[10:56:04.133]    DbgMCU_APB1_Fz=0x00000000
[10:56:04.133]    DbgMCU_APB2_Fz=0x00000000
[10:56:04.134]    DoOptionByteLoading=0x00000000
[10:56:04.134]  </debugvars>
[10:56:04.134]  
[10:56:04.135]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:56:04.135]    <block atomic="false" info="">
[10:56:04.135]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:56:04.135]        // -> [connectionFlash <= 0x00000000]
[10:56:04.136]      __var FLASH_BASE = 0x40022000 ;
[10:56:04.136]        // -> [FLASH_BASE <= 0x40022000]
[10:56:04.136]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:56:04.136]        // -> [FLASH_CR <= 0x40022004]
[10:56:04.136]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:56:04.136]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:56:04.137]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:56:04.138]        // -> [LOCK_BIT <= 0x00000001]
[10:56:04.138]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:56:04.138]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:56:04.138]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:56:04.138]        // -> [FLASH_KEYR <= 0x4002200C]
[10:56:04.139]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:56:04.139]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:56:04.139]      __var FLASH_KEY2 = 0x02030405 ;
[10:56:04.139]        // -> [FLASH_KEY2 <= 0x02030405]
[10:56:04.139]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:56:04.139]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:56:04.140]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:56:04.140]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:56:04.140]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:56:04.141]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:56:04.141]      __var FLASH_CR_Value = 0 ;
[10:56:04.141]        // -> [FLASH_CR_Value <= 0x00000000]
[10:56:04.141]      __var DoDebugPortStop = 1 ;
[10:56:04.141]        // -> [DoDebugPortStop <= 0x00000001]
[10:56:04.141]      __var DP_CTRL_STAT = 0x4 ;
[10:56:04.142]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:56:04.143]      __var DP_SELECT = 0x8 ;
[10:56:04.143]        // -> [DP_SELECT <= 0x00000008]
[10:56:04.143]    </block>
[10:56:04.143]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:56:04.144]      // if-block "connectionFlash && DoOptionByteLoading"
[10:56:04.144]        // =>  FALSE
[10:56:04.144]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:56:04.145]    </control>
[10:56:04.145]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:56:04.145]      // if-block "DoDebugPortStop"
[10:56:04.146]        // =>  TRUE
[10:56:04.146]      <block atomic="false" info="">
[10:56:04.146]        WriteDP(DP_SELECT, 0x00000000);
[10:56:04.147]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:56:04.147]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:56:04.147]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:56:04.148]      </block>
[10:56:04.148]      // end if-block "DoDebugPortStop"
[10:56:04.148]    </control>
[10:56:04.148]  </sequence>
[10:56:04.149]  
[10:56:10.948]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:56:10.948]  
[10:56:10.948]  <debugvars>
[10:56:10.948]    // Pre-defined
[10:56:10.948]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:56:10.948]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:56:10.948]    __dp=0x00000000
[10:56:10.949]    __ap=0x00000000
[10:56:10.949]    __traceout=0x00000000      (Trace Disabled)
[10:56:10.949]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:56:10.949]    __FlashAddr=0x00000000
[10:56:10.950]    __FlashLen=0x00000000
[10:56:10.950]    __FlashArg=0x00000000
[10:56:10.950]    __FlashOp=0x00000000
[10:56:10.950]    __Result=0x00000000
[10:56:10.951]    
[10:56:10.951]    // User-defined
[10:56:10.951]    DbgMCU_CR=0x00000007
[10:56:10.951]    DbgMCU_APB1_Fz=0x00000000
[10:56:10.951]    DbgMCU_APB2_Fz=0x00000000
[10:56:10.951]    DoOptionByteLoading=0x00000000
[10:56:10.952]  </debugvars>
[10:56:10.952]  
[10:56:10.952]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:56:10.952]    <block atomic="false" info="">
[10:56:10.952]      Sequence("CheckID");
[10:56:10.953]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:56:10.953]          <block atomic="false" info="">
[10:56:10.953]            __var pidr1 = 0;
[10:56:10.953]              // -> [pidr1 <= 0x00000000]
[10:56:10.953]            __var pidr2 = 0;
[10:56:10.954]              // -> [pidr2 <= 0x00000000]
[10:56:10.954]            __var jep106id = 0;
[10:56:10.954]              // -> [jep106id <= 0x00000000]
[10:56:10.954]            __var ROMTableBase = 0;
[10:56:10.954]              // -> [ROMTableBase <= 0x00000000]
[10:56:10.955]            __ap = 0;      // AHB-AP
[10:56:10.955]              // -> [__ap <= 0x00000000]
[10:56:10.955]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:56:10.956]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:56:10.956]              // -> [ROMTableBase <= 0xF0000000]
[10:56:10.956]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:56:10.957]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:56:10.957]              // -> [pidr1 <= 0x00000004]
[10:56:10.958]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:56:10.958]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:56:10.959]              // -> [pidr2 <= 0x0000000A]
[10:56:10.959]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:56:10.959]              // -> [jep106id <= 0x00000020]
[10:56:10.959]          </block>
[10:56:10.959]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:56:10.960]            // if-block "jep106id != 0x20"
[10:56:10.960]              // =>  FALSE
[10:56:10.960]            // skip if-block "jep106id != 0x20"
[10:56:10.960]          </control>
[10:56:10.960]        </sequence>
[10:56:10.961]    </block>
[10:56:10.961]  </sequence>
[10:56:10.961]  
[10:56:10.973]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:56:10.973]  
[10:56:10.974]  <debugvars>
[10:56:10.974]    // Pre-defined
[10:56:10.974]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:56:10.974]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:56:10.975]    __dp=0x00000000
[10:56:10.975]    __ap=0x00000000
[10:56:10.975]    __traceout=0x00000000      (Trace Disabled)
[10:56:10.975]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:56:10.975]    __FlashAddr=0x00000000
[10:56:10.976]    __FlashLen=0x00000000
[10:56:10.976]    __FlashArg=0x00000000
[10:56:10.977]    __FlashOp=0x00000000
[10:56:10.977]    __Result=0x00000000
[10:56:10.977]    
[10:56:10.977]    // User-defined
[10:56:10.977]    DbgMCU_CR=0x00000007
[10:56:10.977]    DbgMCU_APB1_Fz=0x00000000
[10:56:10.977]    DbgMCU_APB2_Fz=0x00000000
[10:56:10.977]    DoOptionByteLoading=0x00000000
[10:56:10.977]  </debugvars>
[10:56:10.977]  
[10:56:10.977]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:56:10.977]    <block atomic="false" info="">
[10:56:10.977]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:56:10.979]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:56:10.979]    </block>
[10:56:10.979]    <block atomic="false" info="DbgMCU registers">
[10:56:10.979]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:56:10.981]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[10:56:10.981]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[10:56:10.981]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:56:10.983]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:56:10.983]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:56:10.983]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:56:10.983]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:56:10.985]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:56:10.985]    </block>
[10:56:10.985]  </sequence>
[10:56:10.985]  
[10:56:18.820]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:56:18.820]  
[10:56:18.820]  <debugvars>
[10:56:18.821]    // Pre-defined
[10:56:18.822]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:56:18.822]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:56:18.823]    __dp=0x00000000
[10:56:18.823]    __ap=0x00000000
[10:56:18.824]    __traceout=0x00000000      (Trace Disabled)
[10:56:18.824]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:56:18.825]    __FlashAddr=0x00000000
[10:56:18.825]    __FlashLen=0x00000000
[10:56:18.826]    __FlashArg=0x00000000
[10:56:18.826]    __FlashOp=0x00000000
[10:56:18.826]    __Result=0x00000000
[10:56:18.827]    
[10:56:18.827]    // User-defined
[10:56:18.827]    DbgMCU_CR=0x00000007
[10:56:18.828]    DbgMCU_APB1_Fz=0x00000000
[10:56:18.828]    DbgMCU_APB2_Fz=0x00000000
[10:56:18.829]    DoOptionByteLoading=0x00000000
[10:56:18.829]  </debugvars>
[10:56:18.829]  
[10:56:18.829]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:56:18.830]    <block atomic="false" info="">
[10:56:18.830]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:56:18.830]        // -> [connectionFlash <= 0x00000001]
[10:56:18.830]      __var FLASH_BASE = 0x40022000 ;
[10:56:18.831]        // -> [FLASH_BASE <= 0x40022000]
[10:56:18.831]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:56:18.831]        // -> [FLASH_CR <= 0x40022004]
[10:56:18.831]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:56:18.831]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:56:18.831]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:56:18.832]        // -> [LOCK_BIT <= 0x00000001]
[10:56:18.832]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:56:18.832]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:56:18.832]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:56:18.832]        // -> [FLASH_KEYR <= 0x4002200C]
[10:56:18.833]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:56:18.833]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:56:18.833]      __var FLASH_KEY2 = 0x02030405 ;
[10:56:18.834]        // -> [FLASH_KEY2 <= 0x02030405]
[10:56:18.834]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:56:18.835]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:56:18.835]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:56:18.835]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:56:18.835]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:56:18.835]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:56:18.835]      __var FLASH_CR_Value = 0 ;
[10:56:18.836]        // -> [FLASH_CR_Value <= 0x00000000]
[10:56:18.836]      __var DoDebugPortStop = 1 ;
[10:56:18.836]        // -> [DoDebugPortStop <= 0x00000001]
[10:56:18.836]      __var DP_CTRL_STAT = 0x4 ;
[10:56:18.837]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:56:18.837]      __var DP_SELECT = 0x8 ;
[10:56:18.837]        // -> [DP_SELECT <= 0x00000008]
[10:56:18.838]    </block>
[10:56:18.838]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:56:18.838]      // if-block "connectionFlash && DoOptionByteLoading"
[10:56:18.838]        // =>  FALSE
[10:56:18.839]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:56:18.839]    </control>
[10:56:18.839]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:56:18.839]      // if-block "DoDebugPortStop"
[10:56:18.839]        // =>  TRUE
[10:56:18.840]      <block atomic="false" info="">
[10:56:18.840]        WriteDP(DP_SELECT, 0x00000000);
[10:56:18.840]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:56:18.840]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:56:18.841]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:56:18.841]      </block>
[10:56:18.841]      // end if-block "DoDebugPortStop"
[10:56:18.842]    </control>
[10:56:18.842]  </sequence>
[10:56:18.842]  
[11:00:12.620]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:00:12.620]  
[11:00:12.621]  <debugvars>
[11:00:12.621]    // Pre-defined
[11:00:12.622]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:00:12.622]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:00:12.622]    __dp=0x00000000
[11:00:12.622]    __ap=0x00000000
[11:00:12.623]    __traceout=0x00000000      (Trace Disabled)
[11:00:12.623]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:00:12.623]    __FlashAddr=0x00000000
[11:00:12.623]    __FlashLen=0x00000000
[11:00:12.624]    __FlashArg=0x00000000
[11:00:12.624]    __FlashOp=0x00000000
[11:00:12.624]    __Result=0x00000000
[11:00:12.624]    
[11:00:12.624]    // User-defined
[11:00:12.624]    DbgMCU_CR=0x00000007
[11:00:12.625]    DbgMCU_APB1_Fz=0x00000000
[11:00:12.625]    DbgMCU_APB2_Fz=0x00000000
[11:00:12.625]    DoOptionByteLoading=0x00000000
[11:00:12.625]  </debugvars>
[11:00:12.625]  
[11:00:12.625]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:00:12.626]    <block atomic="false" info="">
[11:00:12.626]      Sequence("CheckID");
[11:00:12.626]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:00:12.627]          <block atomic="false" info="">
[11:00:12.627]            __var pidr1 = 0;
[11:00:12.627]              // -> [pidr1 <= 0x00000000]
[11:00:12.627]            __var pidr2 = 0;
[11:00:12.627]              // -> [pidr2 <= 0x00000000]
[11:00:12.628]            __var jep106id = 0;
[11:00:12.628]              // -> [jep106id <= 0x00000000]
[11:00:12.628]            __var ROMTableBase = 0;
[11:00:12.628]              // -> [ROMTableBase <= 0x00000000]
[11:00:12.628]            __ap = 0;      // AHB-AP
[11:00:12.628]              // -> [__ap <= 0x00000000]
[11:00:12.628]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:00:12.629]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:00:12.630]              // -> [ROMTableBase <= 0xF0000000]
[11:00:12.630]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:00:12.631]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:00:12.632]              // -> [pidr1 <= 0x00000004]
[11:00:12.633]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:00:12.633]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:00:12.634]              // -> [pidr2 <= 0x0000000A]
[11:00:12.634]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:00:12.634]              // -> [jep106id <= 0x00000020]
[11:00:12.635]          </block>
[11:00:12.635]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:00:12.635]            // if-block "jep106id != 0x20"
[11:00:12.636]              // =>  FALSE
[11:00:12.636]            // skip if-block "jep106id != 0x20"
[11:00:12.636]          </control>
[11:00:12.637]        </sequence>
[11:00:12.637]    </block>
[11:00:12.637]  </sequence>
[11:00:12.637]  
[11:00:12.650]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:00:12.650]  
[11:00:12.650]  <debugvars>
[11:00:12.650]    // Pre-defined
[11:00:12.651]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:00:12.651]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:00:12.651]    __dp=0x00000000
[11:00:12.652]    __ap=0x00000000
[11:00:12.652]    __traceout=0x00000000      (Trace Disabled)
[11:00:12.652]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:00:12.653]    __FlashAddr=0x00000000
[11:00:12.653]    __FlashLen=0x00000000
[11:00:12.653]    __FlashArg=0x00000000
[11:00:12.653]    __FlashOp=0x00000000
[11:00:12.653]    __Result=0x00000000
[11:00:12.653]    
[11:00:12.653]    // User-defined
[11:00:12.654]    DbgMCU_CR=0x00000007
[11:00:12.654]    DbgMCU_APB1_Fz=0x00000000
[11:00:12.654]    DbgMCU_APB2_Fz=0x00000000
[11:00:12.654]    DoOptionByteLoading=0x00000000
[11:00:12.654]  </debugvars>
[11:00:12.655]  
[11:00:12.655]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:00:12.655]    <block atomic="false" info="">
[11:00:12.655]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:00:12.656]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:00:12.656]    </block>
[11:00:12.656]    <block atomic="false" info="DbgMCU registers">
[11:00:12.657]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:00:12.657]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[11:00:12.658]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[11:00:12.658]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:00:12.659]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:00:12.659]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:00:12.660]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:00:12.660]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:00:12.661]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:00:12.662]    </block>
[11:00:12.662]  </sequence>
[11:00:12.662]  
[11:00:20.252]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:00:20.252]  
[11:00:20.252]  <debugvars>
[11:00:20.253]    // Pre-defined
[11:00:20.253]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:00:20.253]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:00:20.253]    __dp=0x00000000
[11:00:20.254]    __ap=0x00000000
[11:00:20.254]    __traceout=0x00000000      (Trace Disabled)
[11:00:20.255]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:00:20.255]    __FlashAddr=0x00000000
[11:00:20.255]    __FlashLen=0x00000000
[11:00:20.256]    __FlashArg=0x00000000
[11:00:20.256]    __FlashOp=0x00000000
[11:00:20.256]    __Result=0x00000000
[11:00:20.256]    
[11:00:20.256]    // User-defined
[11:00:20.257]    DbgMCU_CR=0x00000007
[11:00:20.257]    DbgMCU_APB1_Fz=0x00000000
[11:00:20.257]    DbgMCU_APB2_Fz=0x00000000
[11:00:20.257]    DoOptionByteLoading=0x00000000
[11:00:20.257]  </debugvars>
[11:00:20.257]  
[11:00:20.258]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:00:20.258]    <block atomic="false" info="">
[11:00:20.258]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:00:20.258]        // -> [connectionFlash <= 0x00000001]
[11:00:20.258]      __var FLASH_BASE = 0x40022000 ;
[11:00:20.259]        // -> [FLASH_BASE <= 0x40022000]
[11:00:20.259]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:00:20.259]        // -> [FLASH_CR <= 0x40022004]
[11:00:20.259]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:00:20.259]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:00:20.259]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:00:20.259]        // -> [LOCK_BIT <= 0x00000001]
[11:00:20.261]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:00:20.261]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:00:20.261]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:00:20.261]        // -> [FLASH_KEYR <= 0x4002200C]
[11:00:20.261]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:00:20.262]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:00:20.262]      __var FLASH_KEY2 = 0x02030405 ;
[11:00:20.262]        // -> [FLASH_KEY2 <= 0x02030405]
[11:00:20.262]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:00:20.262]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:00:20.262]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:00:20.263]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:00:20.263]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:00:20.263]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:00:20.263]      __var FLASH_CR_Value = 0 ;
[11:00:20.264]        // -> [FLASH_CR_Value <= 0x00000000]
[11:00:20.264]      __var DoDebugPortStop = 1 ;
[11:00:20.264]        // -> [DoDebugPortStop <= 0x00000001]
[11:00:20.264]      __var DP_CTRL_STAT = 0x4 ;
[11:00:20.265]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:00:20.265]      __var DP_SELECT = 0x8 ;
[11:00:20.265]        // -> [DP_SELECT <= 0x00000008]
[11:00:20.265]    </block>
[11:00:20.265]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:00:20.266]      // if-block "connectionFlash && DoOptionByteLoading"
[11:00:20.266]        // =>  FALSE
[11:00:20.266]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:00:20.266]    </control>
[11:00:20.267]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:00:20.267]      // if-block "DoDebugPortStop"
[11:00:20.267]        // =>  TRUE
[11:00:20.267]      <block atomic="false" info="">
[11:00:20.267]        WriteDP(DP_SELECT, 0x00000000);
[11:00:20.267]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:00:20.269]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:00:20.269]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:00:20.269]      </block>
[11:00:20.270]      // end if-block "DoDebugPortStop"
[11:00:20.270]    </control>
[11:00:20.270]  </sequence>
[11:00:20.270]  
[11:13:07.893]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:13:07.893]  
[11:13:07.893]  <debugvars>
[11:13:07.894]    // Pre-defined
[11:13:07.894]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:13:07.894]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[11:13:07.894]    __dp=0x00000000
[11:13:07.895]    __ap=0x00000000
[11:13:07.895]    __traceout=0x00000000      (Trace Disabled)
[11:13:07.895]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:13:07.896]    __FlashAddr=0x00000000
[11:13:07.896]    __FlashLen=0x00000000
[11:13:07.896]    __FlashArg=0x00000000
[11:13:07.896]    __FlashOp=0x00000000
[11:13:07.897]    __Result=0x00000000
[11:13:07.897]    
[11:13:07.897]    // User-defined
[11:13:07.897]    DbgMCU_CR=0x00000007
[11:13:07.897]    DbgMCU_APB1_Fz=0x00000000
[11:13:07.897]    DbgMCU_APB2_Fz=0x00000000
[11:13:07.897]    DoOptionByteLoading=0x00000000
[11:13:07.899]  </debugvars>
[11:13:07.899]  
[11:13:07.899]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:13:07.899]    <block atomic="false" info="">
[11:13:07.899]      Sequence("CheckID");
[11:13:07.900]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:13:07.900]          <block atomic="false" info="">
[11:13:07.900]            __var pidr1 = 0;
[11:13:07.900]              // -> [pidr1 <= 0x00000000]
[11:13:07.900]            __var pidr2 = 0;
[11:13:07.900]              // -> [pidr2 <= 0x00000000]
[11:13:07.900]            __var jep106id = 0;
[11:13:07.900]              // -> [jep106id <= 0x00000000]
[11:13:07.901]            __var ROMTableBase = 0;
[11:13:07.901]              // -> [ROMTableBase <= 0x00000000]
[11:13:07.902]            __ap = 0;      // AHB-AP
[11:13:07.902]              // -> [__ap <= 0x00000000]
[11:13:07.902]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:13:07.903]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:13:07.903]              // -> [ROMTableBase <= 0xF0000000]
[11:13:07.903]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:13:07.904]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:13:07.905]              // -> [pidr1 <= 0x00000004]
[11:13:07.905]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:13:07.906]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:13:07.906]              // -> [pidr2 <= 0x0000000A]
[11:13:07.906]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:13:07.907]              // -> [jep106id <= 0x00000020]
[11:13:07.907]          </block>
[11:13:07.907]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:13:07.907]            // if-block "jep106id != 0x20"
[11:13:07.907]              // =>  FALSE
[11:13:07.908]            // skip if-block "jep106id != 0x20"
[11:13:07.908]          </control>
[11:13:07.908]        </sequence>
[11:13:07.909]    </block>
[11:13:07.909]  </sequence>
[11:13:07.909]  
[11:13:07.921]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:13:07.921]  
[11:13:07.942]  <debugvars>
[11:13:07.943]    // Pre-defined
[11:13:07.943]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:13:07.944]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[11:13:07.944]    __dp=0x00000000
[11:13:07.945]    __ap=0x00000000
[11:13:07.945]    __traceout=0x00000000      (Trace Disabled)
[11:13:07.946]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:13:07.946]    __FlashAddr=0x00000000
[11:13:07.947]    __FlashLen=0x00000000
[11:13:07.947]    __FlashArg=0x00000000
[11:13:07.948]    __FlashOp=0x00000000
[11:13:07.948]    __Result=0x00000000
[11:13:07.949]    
[11:13:07.949]    // User-defined
[11:13:07.949]    DbgMCU_CR=0x00000007
[11:13:07.949]    DbgMCU_APB1_Fz=0x00000000
[11:13:07.950]    DbgMCU_APB2_Fz=0x00000000
[11:13:07.950]    DoOptionByteLoading=0x00000000
[11:13:07.951]  </debugvars>
[11:13:07.951]  
[11:13:07.952]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:13:07.952]    <block atomic="false" info="">
[11:13:07.953]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:13:07.953]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:13:07.953]    </block>
[11:13:07.953]    <block atomic="false" info="DbgMCU registers">
[11:13:07.953]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:13:07.953]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[11:13:07.956]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[11:13:07.956]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:13:07.956]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:13:07.956]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:13:07.958]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:13:07.958]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:13:07.958]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:13:07.960]    </block>
[11:13:07.960]  </sequence>
[11:13:07.960]  
[11:13:40.399]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:13:40.399]  
[11:13:40.400]  <debugvars>
[11:13:40.400]    // Pre-defined
[11:13:40.401]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:13:40.401]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[11:13:40.402]    __dp=0x00000000
[11:13:40.402]    __ap=0x00000000
[11:13:40.403]    __traceout=0x00000000      (Trace Disabled)
[11:13:40.403]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:13:40.403]    __FlashAddr=0x00000000
[11:13:40.404]    __FlashLen=0x00000000
[11:13:40.404]    __FlashArg=0x00000000
[11:13:40.404]    __FlashOp=0x00000000
[11:13:40.405]    __Result=0x00000000
[11:13:40.405]    
[11:13:40.405]    // User-defined
[11:13:40.405]    DbgMCU_CR=0x00000007
[11:13:40.406]    DbgMCU_APB1_Fz=0x00000000
[11:13:40.406]    DbgMCU_APB2_Fz=0x00000000
[11:13:40.406]    DoOptionByteLoading=0x00000000
[11:13:40.406]  </debugvars>
[11:13:40.407]  
[11:13:40.407]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:13:40.407]    <block atomic="false" info="">
[11:13:40.407]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:13:40.407]        // -> [connectionFlash <= 0x00000000]
[11:13:40.407]      __var FLASH_BASE = 0x40022000 ;
[11:13:40.408]        // -> [FLASH_BASE <= 0x40022000]
[11:13:40.408]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:13:40.408]        // -> [FLASH_CR <= 0x40022004]
[11:13:40.408]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:13:40.408]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:13:40.409]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:13:40.409]        // -> [LOCK_BIT <= 0x00000001]
[11:13:40.409]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:13:40.409]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:13:40.409]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:13:40.410]        // -> [FLASH_KEYR <= 0x4002200C]
[11:13:40.410]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:13:40.410]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:13:40.410]      __var FLASH_KEY2 = 0x02030405 ;
[11:13:40.410]        // -> [FLASH_KEY2 <= 0x02030405]
[11:13:40.411]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:13:40.411]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:13:40.412]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:13:40.412]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:13:40.412]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:13:40.412]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:13:40.412]      __var FLASH_CR_Value = 0 ;
[11:13:40.413]        // -> [FLASH_CR_Value <= 0x00000000]
[11:13:40.413]      __var DoDebugPortStop = 1 ;
[11:13:40.414]        // -> [DoDebugPortStop <= 0x00000001]
[11:13:40.414]      __var DP_CTRL_STAT = 0x4 ;
[11:13:40.414]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:13:40.415]      __var DP_SELECT = 0x8 ;
[11:13:40.415]        // -> [DP_SELECT <= 0x00000008]
[11:13:40.415]    </block>
[11:13:40.415]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:13:40.415]      // if-block "connectionFlash && DoOptionByteLoading"
[11:13:40.416]        // =>  FALSE
[11:13:40.416]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:13:40.416]    </control>
[11:13:40.417]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:13:40.417]      // if-block "DoDebugPortStop"
[11:13:40.417]        // =>  TRUE
[11:13:40.417]      <block atomic="false" info="">
[11:13:40.417]        WriteDP(DP_SELECT, 0x00000000);
[11:13:40.418]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:13:40.418]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:13:40.419]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:13:40.419]      </block>
[11:13:40.419]      // end if-block "DoDebugPortStop"
[11:13:40.419]    </control>
[11:13:40.419]  </sequence>
[11:13:40.420]  
[11:14:32.133]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:14:32.133]  
[11:14:32.133]  <debugvars>
[11:14:32.133]    // Pre-defined
[11:14:32.133]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:14:32.134]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[11:14:32.134]    __dp=0x00000000
[11:14:32.135]    __ap=0x00000000
[11:14:32.136]    __traceout=0x00000000      (Trace Disabled)
[11:14:32.136]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:14:32.136]    __FlashAddr=0x00000000
[11:14:32.136]    __FlashLen=0x00000000
[11:14:32.137]    __FlashArg=0x00000000
[11:14:32.137]    __FlashOp=0x00000000
[11:14:32.137]    __Result=0x00000000
[11:14:32.137]    
[11:14:32.137]    // User-defined
[11:14:32.138]    DbgMCU_CR=0x00000007
[11:14:32.138]    DbgMCU_APB1_Fz=0x00000000
[11:14:32.138]    DbgMCU_APB2_Fz=0x00000000
[11:14:32.138]    DoOptionByteLoading=0x00000000
[11:14:32.138]  </debugvars>
[11:14:32.138]  
[11:14:32.139]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:14:32.139]    <block atomic="false" info="">
[11:14:32.139]      Sequence("CheckID");
[11:14:32.139]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:14:32.139]          <block atomic="false" info="">
[11:14:32.139]            __var pidr1 = 0;
[11:14:32.140]              // -> [pidr1 <= 0x00000000]
[11:14:32.140]            __var pidr2 = 0;
[11:14:32.140]              // -> [pidr2 <= 0x00000000]
[11:14:32.140]            __var jep106id = 0;
[11:14:32.140]              // -> [jep106id <= 0x00000000]
[11:14:32.141]            __var ROMTableBase = 0;
[11:14:32.141]              // -> [ROMTableBase <= 0x00000000]
[11:14:32.141]            __ap = 0;      // AHB-AP
[11:14:32.141]              // -> [__ap <= 0x00000000]
[11:14:32.141]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:14:32.142]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:14:32.142]              // -> [ROMTableBase <= 0xF0000000]
[11:14:32.142]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:14:32.143]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:14:32.143]              // -> [pidr1 <= 0x00000004]
[11:14:32.144]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:14:32.145]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:14:32.145]              // -> [pidr2 <= 0x0000000A]
[11:14:32.145]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:14:32.146]              // -> [jep106id <= 0x00000020]
[11:14:32.147]          </block>
[11:14:32.147]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:14:32.147]            // if-block "jep106id != 0x20"
[11:14:32.148]              // =>  FALSE
[11:14:32.148]            // skip if-block "jep106id != 0x20"
[11:14:32.148]          </control>
[11:14:32.148]        </sequence>
[11:14:32.149]    </block>
[11:14:32.149]  </sequence>
[11:14:32.149]  
[11:14:32.161]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:14:32.161]  
[11:14:32.161]  <debugvars>
[11:14:32.161]    // Pre-defined
[11:14:32.162]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:14:32.162]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[11:14:32.162]    __dp=0x00000000
[11:14:32.162]    __ap=0x00000000
[11:14:32.163]    __traceout=0x00000000      (Trace Disabled)
[11:14:32.163]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:14:32.163]    __FlashAddr=0x00000000
[11:14:32.164]    __FlashLen=0x00000000
[11:14:32.164]    __FlashArg=0x00000000
[11:14:32.164]    __FlashOp=0x00000000
[11:14:32.164]    __Result=0x00000000
[11:14:32.164]    
[11:14:32.164]    // User-defined
[11:14:32.165]    DbgMCU_CR=0x00000007
[11:14:32.165]    DbgMCU_APB1_Fz=0x00000000
[11:14:32.165]    DbgMCU_APB2_Fz=0x00000000
[11:14:32.165]    DoOptionByteLoading=0x00000000
[11:14:32.166]  </debugvars>
[11:14:32.166]  
[11:14:32.166]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:14:32.166]    <block atomic="false" info="">
[11:14:32.166]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:14:32.167]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:14:32.167]    </block>
[11:14:32.168]    <block atomic="false" info="DbgMCU registers">
[11:14:32.168]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:14:32.169]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[11:14:32.170]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[11:14:32.170]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:14:32.171]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:14:32.171]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:14:32.171]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:14:32.171]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:14:32.172]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:14:32.173]    </block>
[11:14:32.173]  </sequence>
[11:14:32.173]  
[11:22:00.277]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:22:00.277]  
[11:22:00.277]  <debugvars>
[11:22:00.277]    // Pre-defined
[11:22:00.279]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:22:00.279]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[11:22:00.280]    __dp=0x00000000
[11:22:00.280]    __ap=0x00000000
[11:22:00.281]    __traceout=0x00000000      (Trace Disabled)
[11:22:00.281]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:22:00.281]    __FlashAddr=0x00000000
[11:22:00.282]    __FlashLen=0x00000000
[11:22:00.282]    __FlashArg=0x00000000
[11:22:00.283]    __FlashOp=0x00000000
[11:22:00.283]    __Result=0x00000000
[11:22:00.283]    
[11:22:00.283]    // User-defined
[11:22:00.284]    DbgMCU_CR=0x00000007
[11:22:00.284]    DbgMCU_APB1_Fz=0x00000000
[11:22:00.284]    DbgMCU_APB2_Fz=0x00000000
[11:22:00.284]    DoOptionByteLoading=0x00000000
[11:22:00.285]  </debugvars>
[11:22:00.285]  
[11:22:00.285]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:22:00.285]    <block atomic="false" info="">
[11:22:00.285]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:22:00.285]        // -> [connectionFlash <= 0x00000000]
[11:22:00.286]      __var FLASH_BASE = 0x40022000 ;
[11:22:00.286]        // -> [FLASH_BASE <= 0x40022000]
[11:22:00.286]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:22:00.286]        // -> [FLASH_CR <= 0x40022004]
[11:22:00.286]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:22:00.286]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:22:00.286]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:22:00.286]        // -> [LOCK_BIT <= 0x00000001]
[11:22:00.287]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:22:00.288]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:22:00.288]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:22:00.288]        // -> [FLASH_KEYR <= 0x4002200C]
[11:22:00.288]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:22:00.288]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:22:00.289]      __var FLASH_KEY2 = 0x02030405 ;
[11:22:00.289]        // -> [FLASH_KEY2 <= 0x02030405]
[11:22:00.289]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:22:00.290]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:22:00.290]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:22:00.290]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:22:00.290]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:22:00.290]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:22:00.290]      __var FLASH_CR_Value = 0 ;
[11:22:00.291]        // -> [FLASH_CR_Value <= 0x00000000]
[11:22:00.291]      __var DoDebugPortStop = 1 ;
[11:22:00.291]        // -> [DoDebugPortStop <= 0x00000001]
[11:22:00.291]      __var DP_CTRL_STAT = 0x4 ;
[11:22:00.291]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:22:00.292]      __var DP_SELECT = 0x8 ;
[11:22:00.292]        // -> [DP_SELECT <= 0x00000008]
[11:22:00.292]    </block>
[11:22:00.292]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:22:00.292]      // if-block "connectionFlash && DoOptionByteLoading"
[11:22:00.292]        // =>  FALSE
[11:22:00.293]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:22:00.293]    </control>
[11:22:00.293]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:22:00.293]      // if-block "DoDebugPortStop"
[11:22:00.293]        // =>  TRUE
[11:22:00.294]      <block atomic="false" info="">
[11:22:00.294]        WriteDP(DP_SELECT, 0x00000000);
[11:22:00.294]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:22:00.294]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:22:00.295]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:22:00.295]      </block>
[11:22:00.296]      // end if-block "DoDebugPortStop"
[11:22:00.296]    </control>
[11:22:00.296]  </sequence>
[11:22:00.296]  
[11:22:12.742]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:22:12.742]  
[11:22:12.742]  <debugvars>
[11:22:12.746]    // Pre-defined
[11:22:12.746]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:22:12.746]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[11:22:12.746]    __dp=0x00000000
[11:22:12.746]    __ap=0x00000000
[11:22:12.746]    __traceout=0x00000000      (Trace Disabled)
[11:22:12.746]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:22:12.746]    __FlashAddr=0x00000000
[11:22:12.746]    __FlashLen=0x00000000
[11:22:12.746]    __FlashArg=0x00000000
[11:22:12.746]    __FlashOp=0x00000000
[11:22:12.746]    __Result=0x00000000
[11:22:12.746]    
[11:22:12.746]    // User-defined
[11:22:12.746]    DbgMCU_CR=0x00000007
[11:22:12.746]    DbgMCU_APB1_Fz=0x00000000
[11:22:12.746]    DbgMCU_APB2_Fz=0x00000000
[11:22:12.746]    DoOptionByteLoading=0x00000000
[11:22:12.746]  </debugvars>
[11:22:12.746]  
[11:22:12.751]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:22:12.751]    <block atomic="false" info="">
[11:22:12.751]      Sequence("CheckID");
[11:22:12.752]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:22:12.752]          <block atomic="false" info="">
[11:22:12.753]            __var pidr1 = 0;
[11:22:12.753]              // -> [pidr1 <= 0x00000000]
[11:22:12.754]            __var pidr2 = 0;
[11:22:12.754]              // -> [pidr2 <= 0x00000000]
[11:22:12.754]            __var jep106id = 0;
[11:22:12.754]              // -> [jep106id <= 0x00000000]
[11:22:12.755]            __var ROMTableBase = 0;
[11:22:12.755]              // -> [ROMTableBase <= 0x00000000]
[11:22:12.756]            __ap = 0;      // AHB-AP
[11:22:12.756]              // -> [__ap <= 0x00000000]
[11:22:12.756]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:22:12.757]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:22:12.757]              // -> [ROMTableBase <= 0xF0000000]
[11:22:12.758]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:22:12.759]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:22:12.759]              // -> [pidr1 <= 0x00000004]
[11:22:12.759]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:22:12.760]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:22:12.761]              // -> [pidr2 <= 0x0000000A]
[11:22:12.761]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:22:12.761]              // -> [jep106id <= 0x00000020]
[11:22:12.762]          </block>
[11:22:12.762]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:22:12.762]            // if-block "jep106id != 0x20"
[11:22:12.762]              // =>  FALSE
[11:22:12.762]            // skip if-block "jep106id != 0x20"
[11:22:12.762]          </control>
[11:22:12.763]        </sequence>
[11:22:12.763]    </block>
[11:22:12.763]  </sequence>
[11:22:12.763]  
[11:22:12.776]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:22:12.776]  
[11:22:12.798]  <debugvars>
[11:22:12.798]    // Pre-defined
[11:22:12.799]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:22:12.800]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[11:22:12.801]    __dp=0x00000000
[11:22:12.801]    __ap=0x00000000
[11:22:12.802]    __traceout=0x00000000      (Trace Disabled)
[11:22:12.803]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:22:12.803]    __FlashAddr=0x00000000
[11:22:12.803]    __FlashLen=0x00000000
[11:22:12.803]    __FlashArg=0x00000000
[11:22:12.803]    __FlashOp=0x00000000
[11:22:12.803]    __Result=0x00000000
[11:22:12.803]    
[11:22:12.803]    // User-defined
[11:22:12.807]    DbgMCU_CR=0x00000007
[11:22:12.807]    DbgMCU_APB1_Fz=0x00000000
[11:22:12.807]    DbgMCU_APB2_Fz=0x00000000
[11:22:12.807]    DoOptionByteLoading=0x00000000
[11:22:12.807]  </debugvars>
[11:22:12.807]  
[11:22:12.807]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:22:12.812]    <block atomic="false" info="">
[11:22:12.812]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:22:12.812]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:22:12.812]    </block>
[11:22:12.812]    <block atomic="false" info="DbgMCU registers">
[11:22:12.812]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:22:12.818]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[11:22:12.818]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[11:22:12.818]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:22:12.818]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:22:12.818]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:22:12.818]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:22:12.818]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:22:12.818]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:22:12.823]    </block>
[11:22:12.823]  </sequence>
[11:22:12.823]  
[11:24:18.771]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:24:18.771]  
[11:24:18.771]  <debugvars>
[11:24:18.771]    // Pre-defined
[11:24:18.772]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:24:18.772]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[11:24:18.773]    __dp=0x00000000
[11:24:18.774]    __ap=0x00000000
[11:24:18.774]    __traceout=0x00000000      (Trace Disabled)
[11:24:18.774]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:24:18.775]    __FlashAddr=0x00000000
[11:24:18.775]    __FlashLen=0x00000000
[11:24:18.775]    __FlashArg=0x00000000
[11:24:18.775]    __FlashOp=0x00000000
[11:24:18.775]    __Result=0x00000000
[11:24:18.775]    
[11:24:18.775]    // User-defined
[11:24:18.775]    DbgMCU_CR=0x00000007
[11:24:18.775]    DbgMCU_APB1_Fz=0x00000000
[11:24:18.775]    DbgMCU_APB2_Fz=0x00000000
[11:24:18.775]    DoOptionByteLoading=0x00000000
[11:24:18.775]  </debugvars>
[11:24:18.777]  
[11:24:18.777]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:24:18.777]    <block atomic="false" info="">
[11:24:18.777]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:24:18.779]        // -> [connectionFlash <= 0x00000000]
[11:24:18.779]      __var FLASH_BASE = 0x40022000 ;
[11:24:18.779]        // -> [FLASH_BASE <= 0x40022000]
[11:24:18.779]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:24:18.780]        // -> [FLASH_CR <= 0x40022004]
[11:24:18.780]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:24:18.780]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:24:18.780]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:24:18.781]        // -> [LOCK_BIT <= 0x00000001]
[11:24:18.781]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:24:18.781]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:24:18.781]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:24:18.781]        // -> [FLASH_KEYR <= 0x4002200C]
[11:24:18.782]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:24:18.782]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:24:18.782]      __var FLASH_KEY2 = 0x02030405 ;
[11:24:18.782]        // -> [FLASH_KEY2 <= 0x02030405]
[11:24:18.782]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:24:18.782]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:24:18.783]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:24:18.783]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:24:18.783]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:24:18.783]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:24:18.783]      __var FLASH_CR_Value = 0 ;
[11:24:18.784]        // -> [FLASH_CR_Value <= 0x00000000]
[11:24:18.784]      __var DoDebugPortStop = 1 ;
[11:24:18.784]        // -> [DoDebugPortStop <= 0x00000001]
[11:24:18.784]      __var DP_CTRL_STAT = 0x4 ;
[11:24:18.784]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:24:18.784]      __var DP_SELECT = 0x8 ;
[11:24:18.785]        // -> [DP_SELECT <= 0x00000008]
[11:24:18.785]    </block>
[11:24:18.785]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:24:18.785]      // if-block "connectionFlash && DoOptionByteLoading"
[11:24:18.785]        // =>  FALSE
[11:24:18.786]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:24:18.786]    </control>
[11:24:18.786]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:24:18.786]      // if-block "DoDebugPortStop"
[11:24:18.786]        // =>  TRUE
[11:24:18.786]      <block atomic="false" info="">
[11:24:18.787]        WriteDP(DP_SELECT, 0x00000000);
[11:24:18.787]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:24:18.787]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:24:18.788]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:24:18.788]      </block>
[11:24:18.788]      // end if-block "DoDebugPortStop"
[11:24:18.788]    </control>
[11:24:18.788]  </sequence>
[11:24:18.788]  
[11:24:24.338]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:24:24.338]  
[11:24:24.338]  <debugvars>
[11:24:24.338]    // Pre-defined
[11:24:24.338]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:24:24.338]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[11:24:24.338]    __dp=0x00000000
[11:24:24.338]    __ap=0x00000000
[11:24:24.338]    __traceout=0x00000000      (Trace Disabled)
[11:24:24.338]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:24:24.338]    __FlashAddr=0x00000000
[11:24:24.338]    __FlashLen=0x00000000
[11:24:24.343]    __FlashArg=0x00000000
[11:24:24.343]    __FlashOp=0x00000000
[11:24:24.344]    __Result=0x00000000
[11:24:24.344]    
[11:24:24.344]    // User-defined
[11:24:24.344]    DbgMCU_CR=0x00000007
[11:24:24.344]    DbgMCU_APB1_Fz=0x00000000
[11:24:24.344]    DbgMCU_APB2_Fz=0x00000000
[11:24:24.344]    DoOptionByteLoading=0x00000000
[11:24:24.345]  </debugvars>
[11:24:24.345]  
[11:24:24.345]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:24:24.346]    <block atomic="false" info="">
[11:24:24.346]      Sequence("CheckID");
[11:24:24.346]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:24:24.346]          <block atomic="false" info="">
[11:24:24.347]            __var pidr1 = 0;
[11:24:24.347]              // -> [pidr1 <= 0x00000000]
[11:24:24.347]            __var pidr2 = 0;
[11:24:24.347]              // -> [pidr2 <= 0x00000000]
[11:24:24.348]            __var jep106id = 0;
[11:24:24.348]              // -> [jep106id <= 0x00000000]
[11:24:24.348]            __var ROMTableBase = 0;
[11:24:24.348]              // -> [ROMTableBase <= 0x00000000]
[11:24:24.348]            __ap = 0;      // AHB-AP
[11:24:24.348]              // -> [__ap <= 0x00000000]
[11:24:24.349]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:24:24.349]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:24:24.350]              // -> [ROMTableBase <= 0xF0000000]
[11:24:24.350]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:24:24.351]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:24:24.351]              // -> [pidr1 <= 0x00000004]
[11:24:24.352]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:24:24.352]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:24:24.353]              // -> [pidr2 <= 0x0000000A]
[11:24:24.353]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:24:24.353]              // -> [jep106id <= 0x00000020]
[11:24:24.353]          </block>
[11:24:24.353]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:24:24.354]            // if-block "jep106id != 0x20"
[11:24:24.354]              // =>  FALSE
[11:24:24.354]            // skip if-block "jep106id != 0x20"
[11:24:24.354]          </control>
[11:24:24.354]        </sequence>
[11:24:24.354]    </block>
[11:24:24.355]  </sequence>
[11:24:24.355]  
[11:24:24.366]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:24:24.366]  
[11:24:24.366]  <debugvars>
[11:24:24.366]    // Pre-defined
[11:24:24.367]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:24:24.367]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[11:24:24.367]    __dp=0x00000000
[11:24:24.368]    __ap=0x00000000
[11:24:24.368]    __traceout=0x00000000      (Trace Disabled)
[11:24:24.368]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:24:24.369]    __FlashAddr=0x00000000
[11:24:24.369]    __FlashLen=0x00000000
[11:24:24.369]    __FlashArg=0x00000000
[11:24:24.369]    __FlashOp=0x00000000
[11:24:24.369]    __Result=0x00000000
[11:24:24.369]    
[11:24:24.369]    // User-defined
[11:24:24.370]    DbgMCU_CR=0x00000007
[11:24:24.370]    DbgMCU_APB1_Fz=0x00000000
[11:24:24.370]    DbgMCU_APB2_Fz=0x00000000
[11:24:24.371]    DoOptionByteLoading=0x00000000
[11:24:24.371]  </debugvars>
[11:24:24.371]  
[11:24:24.372]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:24:24.372]    <block atomic="false" info="">
[11:24:24.372]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:24:24.373]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:24:24.373]    </block>
[11:24:24.373]    <block atomic="false" info="DbgMCU registers">
[11:24:24.374]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:24:24.375]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:24:24.376]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:24:24.376]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:24:24.377]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:24:24.377]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:24:24.378]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:24:24.378]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:24:24.379]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:24:24.379]    </block>
[11:24:24.379]  </sequence>
[11:24:24.379]  
[11:39:27.426]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:39:27.426]  
[11:39:27.427]  <debugvars>
[11:39:27.427]    // Pre-defined
[11:39:27.427]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:39:27.427]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[11:39:27.428]    __dp=0x00000000
[11:39:27.428]    __ap=0x00000000
[11:39:27.428]    __traceout=0x00000000      (Trace Disabled)
[11:39:27.429]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:39:27.429]    __FlashAddr=0x00000000
[11:39:27.429]    __FlashLen=0x00000000
[11:39:27.429]    __FlashArg=0x00000000
[11:39:27.429]    __FlashOp=0x00000000
[11:39:27.429]    __Result=0x00000000
[11:39:27.430]    
[11:39:27.430]    // User-defined
[11:39:27.430]    DbgMCU_CR=0x00000007
[11:39:27.431]    DbgMCU_APB1_Fz=0x00000000
[11:39:27.431]    DbgMCU_APB2_Fz=0x00000000
[11:39:27.431]    DoOptionByteLoading=0x00000000
[11:39:27.431]  </debugvars>
[11:39:27.431]  
[11:39:27.432]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:39:27.432]    <block atomic="false" info="">
[11:39:27.432]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:39:27.432]        // -> [connectionFlash <= 0x00000000]
[11:39:27.432]      __var FLASH_BASE = 0x40022000 ;
[11:39:27.433]        // -> [FLASH_BASE <= 0x40022000]
[11:39:27.433]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:39:27.433]        // -> [FLASH_CR <= 0x40022004]
[11:39:27.433]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:39:27.433]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:39:27.433]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:39:27.434]        // -> [LOCK_BIT <= 0x00000001]
[11:39:27.434]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:39:27.434]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:39:27.434]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:39:27.434]        // -> [FLASH_KEYR <= 0x4002200C]
[11:39:27.435]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:39:27.435]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:39:27.435]      __var FLASH_KEY2 = 0x02030405 ;
[11:39:27.435]        // -> [FLASH_KEY2 <= 0x02030405]
[11:39:27.435]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:39:27.435]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:39:27.436]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:39:27.436]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:39:27.436]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:39:27.436]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:39:27.436]      __var FLASH_CR_Value = 0 ;
[11:39:27.437]        // -> [FLASH_CR_Value <= 0x00000000]
[11:39:27.437]      __var DoDebugPortStop = 1 ;
[11:39:27.437]        // -> [DoDebugPortStop <= 0x00000001]
[11:39:27.437]      __var DP_CTRL_STAT = 0x4 ;
[11:39:27.437]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:39:27.437]      __var DP_SELECT = 0x8 ;
[11:39:27.438]        // -> [DP_SELECT <= 0x00000008]
[11:39:27.438]    </block>
[11:39:27.438]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:39:27.438]      // if-block "connectionFlash && DoOptionByteLoading"
[11:39:27.438]        // =>  FALSE
[11:39:27.439]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:39:27.439]    </control>
[11:39:27.439]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:39:27.439]      // if-block "DoDebugPortStop"
[11:39:27.439]        // =>  TRUE
[11:39:27.440]      <block atomic="false" info="">
[11:39:27.440]        WriteDP(DP_SELECT, 0x00000000);
[11:39:27.441]  
[11:39:27.441]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:39:27.441]  
[11:39:27.441]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:39:27.442]      </block>
[11:39:27.442]      // end if-block "DoDebugPortStop"
[11:39:27.442]    </control>
[11:39:27.442]  </sequence>
[11:39:27.443]  
[11:39:34.383]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:39:34.383]  
[11:39:34.383]  <debugvars>
[11:39:34.383]    // Pre-defined
[11:39:34.383]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:39:34.383]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:39:34.385]    __dp=0x00000000
[11:39:34.385]    __ap=0x00000000
[11:39:34.385]    __traceout=0x00000000      (Trace Disabled)
[11:39:34.385]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:39:34.385]    __FlashAddr=0x00000000
[11:39:34.385]    __FlashLen=0x00000000
[11:39:34.385]    __FlashArg=0x00000000
[11:39:34.385]    __FlashOp=0x00000000
[11:39:34.385]    __Result=0x00000000
[11:39:34.385]    
[11:39:34.385]    // User-defined
[11:39:34.387]    DbgMCU_CR=0x00000007
[11:39:34.387]    DbgMCU_APB1_Fz=0x00000000
[11:39:34.387]    DbgMCU_APB2_Fz=0x00000000
[11:39:34.387]    DoOptionByteLoading=0x00000000
[11:39:34.387]  </debugvars>
[11:39:34.388]  
[11:39:34.388]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:39:34.388]    <block atomic="false" info="">
[11:39:34.388]      Sequence("CheckID");
[11:39:34.388]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:39:34.388]          <block atomic="false" info="">
[11:39:34.388]            __var pidr1 = 0;
[11:39:34.388]              // -> [pidr1 <= 0x00000000]
[11:39:34.388]            __var pidr2 = 0;
[11:39:34.388]              // -> [pidr2 <= 0x00000000]
[11:39:34.390]            __var jep106id = 0;
[11:39:34.390]              // -> [jep106id <= 0x00000000]
[11:39:34.390]            __var ROMTableBase = 0;
[11:39:34.390]              // -> [ROMTableBase <= 0x00000000]
[11:39:34.390]            __ap = 0;      // AHB-AP
[11:39:34.390]              // -> [__ap <= 0x00000000]
[11:39:34.390]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:39:34.390]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:39:34.392]              // -> [ROMTableBase <= 0xF0000000]
[11:39:34.392]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:39:34.393]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:39:34.393]              // -> [pidr1 <= 0x00000004]
[11:39:34.393]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:39:34.395]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:39:34.395]              // -> [pidr2 <= 0x0000000A]
[11:39:34.395]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:39:34.397]              // -> [jep106id <= 0x00000020]
[11:39:34.397]          </block>
[11:39:34.397]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:39:34.398]            // if-block "jep106id != 0x20"
[11:39:34.398]              // =>  FALSE
[11:39:34.398]            // skip if-block "jep106id != 0x20"
[11:39:34.398]          </control>
[11:39:34.398]        </sequence>
[11:39:34.398]    </block>
[11:39:34.398]  </sequence>
[11:39:34.398]  
[11:39:34.408]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:39:34.408]  
[11:39:34.413]  <debugvars>
[11:39:34.413]    // Pre-defined
[11:39:34.413]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:39:34.413]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:39:34.413]    __dp=0x00000000
[11:39:34.413]    __ap=0x00000000
[11:39:34.413]    __traceout=0x00000000      (Trace Disabled)
[11:39:34.413]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:39:34.413]    __FlashAddr=0x00000000
[11:39:34.413]    __FlashLen=0x00000000
[11:39:34.413]    __FlashArg=0x00000000
[11:39:34.413]    __FlashOp=0x00000000
[11:39:34.413]    __Result=0x00000000
[11:39:34.413]    
[11:39:34.413]    // User-defined
[11:39:34.413]    DbgMCU_CR=0x00000007
[11:39:34.413]    DbgMCU_APB1_Fz=0x00000000
[11:39:34.413]    DbgMCU_APB2_Fz=0x00000000
[11:39:34.413]    DoOptionByteLoading=0x00000000
[11:39:34.413]  </debugvars>
[11:39:34.418]  
[11:39:34.418]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:39:34.418]    <block atomic="false" info="">
[11:39:34.419]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:39:34.419]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:39:34.419]    </block>
[11:39:34.419]    <block atomic="false" info="DbgMCU registers">
[11:39:34.419]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:39:34.421]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[11:39:34.421]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[11:39:34.421]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:39:34.423]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:39:34.423]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:39:34.423]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:39:34.423]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:39:34.425]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:39:34.425]    </block>
[11:39:34.425]  </sequence>
[11:39:34.425]  
[11:39:41.966]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:39:41.966]  
[11:39:41.966]  <debugvars>
[11:39:41.967]    // Pre-defined
[11:39:41.967]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:39:41.968]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:39:41.968]    __dp=0x00000000
[11:39:41.969]    __ap=0x00000000
[11:39:41.969]    __traceout=0x00000000      (Trace Disabled)
[11:39:41.970]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:39:41.970]    __FlashAddr=0x00000000
[11:39:41.970]    __FlashLen=0x00000000
[11:39:41.971]    __FlashArg=0x00000000
[11:39:41.971]    __FlashOp=0x00000000
[11:39:41.971]    __Result=0x00000000
[11:39:41.971]    
[11:39:41.971]    // User-defined
[11:39:41.972]    DbgMCU_CR=0x00000007
[11:39:41.972]    DbgMCU_APB1_Fz=0x00000000
[11:39:41.973]    DbgMCU_APB2_Fz=0x00000000
[11:39:41.973]    DoOptionByteLoading=0x00000000
[11:39:41.973]  </debugvars>
[11:39:41.973]  
[11:39:41.973]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:39:41.973]    <block atomic="false" info="">
[11:39:41.973]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:39:41.973]        // -> [connectionFlash <= 0x00000001]
[11:39:41.973]      __var FLASH_BASE = 0x40022000 ;
[11:39:41.975]        // -> [FLASH_BASE <= 0x40022000]
[11:39:41.975]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:39:41.975]        // -> [FLASH_CR <= 0x40022004]
[11:39:41.975]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:39:41.975]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:39:41.975]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:39:41.975]        // -> [LOCK_BIT <= 0x00000001]
[11:39:41.975]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:39:41.975]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:39:41.975]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:39:41.975]        // -> [FLASH_KEYR <= 0x4002200C]
[11:39:41.977]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:39:41.977]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:39:41.977]      __var FLASH_KEY2 = 0x02030405 ;
[11:39:41.977]        // -> [FLASH_KEY2 <= 0x02030405]
[11:39:41.977]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:39:41.977]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:39:41.977]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:39:41.977]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:39:41.977]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:39:41.977]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:39:41.977]      __var FLASH_CR_Value = 0 ;
[11:39:41.979]        // -> [FLASH_CR_Value <= 0x00000000]
[11:39:41.979]      __var DoDebugPortStop = 1 ;
[11:39:41.979]        // -> [DoDebugPortStop <= 0x00000001]
[11:39:41.980]      __var DP_CTRL_STAT = 0x4 ;
[11:39:41.980]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:39:41.980]      __var DP_SELECT = 0x8 ;
[11:39:41.980]        // -> [DP_SELECT <= 0x00000008]
[11:39:41.980]    </block>
[11:39:41.980]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:39:41.980]      // if-block "connectionFlash && DoOptionByteLoading"
[11:39:41.980]        // =>  FALSE
[11:39:41.980]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:39:41.980]    </control>
[11:39:41.982]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:39:41.982]      // if-block "DoDebugPortStop"
[11:39:41.982]        // =>  TRUE
[11:39:41.982]      <block atomic="false" info="">
[11:39:41.982]        WriteDP(DP_SELECT, 0x00000000);
[11:39:41.982]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:39:41.982]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:39:41.984]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:39:41.984]      </block>
[11:39:41.984]      // end if-block "DoDebugPortStop"
[11:39:41.984]    </control>
[11:39:41.984]  </sequence>
[11:39:41.985]  
[13:50:39.263]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[13:50:39.263]  
[13:50:39.264]  <debugvars>
[13:50:39.264]    // Pre-defined
[13:50:39.264]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:50:39.264]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[13:50:39.265]    __dp=0x00000000
[13:50:39.265]    __ap=0x00000000
[13:50:39.265]    __traceout=0x00000000      (Trace Disabled)
[13:50:39.265]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:50:39.265]    __FlashAddr=0x00000000
[13:50:39.266]    __FlashLen=0x00000000
[13:50:39.266]    __FlashArg=0x00000000
[13:50:39.266]    __FlashOp=0x00000000
[13:50:39.266]    __Result=0x00000000
[13:50:39.266]    
[13:50:39.266]    // User-defined
[13:50:39.267]    DbgMCU_CR=0x00000007
[13:50:39.267]    DbgMCU_APB1_Fz=0x00000000
[13:50:39.267]    DbgMCU_APB2_Fz=0x00000000
[13:50:39.267]    DoOptionByteLoading=0x00000000
[13:50:39.268]  </debugvars>
[13:50:39.268]  
[13:50:39.268]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[13:50:39.268]    <block atomic="false" info="">
[13:50:39.269]      Sequence("CheckID");
[13:50:39.269]        <sequence name="CheckID" Pname="" disable="false" info="">
[13:50:39.270]          <block atomic="false" info="">
[13:50:39.270]            __var pidr1 = 0;
[13:50:39.270]              // -> [pidr1 <= 0x00000000]
[13:50:39.271]            __var pidr2 = 0;
[13:50:39.271]              // -> [pidr2 <= 0x00000000]
[13:50:39.271]            __var jep106id = 0;
[13:50:39.271]              // -> [jep106id <= 0x00000000]
[13:50:39.272]            __var ROMTableBase = 0;
[13:50:39.272]              // -> [ROMTableBase <= 0x00000000]
[13:50:39.272]            __ap = 0;      // AHB-AP
[13:50:39.272]              // -> [__ap <= 0x00000000]
[13:50:39.272]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[13:50:39.274]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[13:50:39.274]              // -> [ROMTableBase <= 0xF0000000]
[13:50:39.275]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[13:50:39.276]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[13:50:39.276]              // -> [pidr1 <= 0x00000004]
[13:50:39.276]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[13:50:39.277]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[13:50:39.277]              // -> [pidr2 <= 0x0000000A]
[13:50:39.278]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[13:50:39.278]              // -> [jep106id <= 0x00000020]
[13:50:39.278]          </block>
[13:50:39.278]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[13:50:39.278]            // if-block "jep106id != 0x20"
[13:50:39.279]              // =>  FALSE
[13:50:39.279]            // skip if-block "jep106id != 0x20"
[13:50:39.279]          </control>
[13:50:39.279]        </sequence>
[13:50:39.279]    </block>
[13:50:39.280]  </sequence>
[13:50:39.280]  
[13:50:39.288]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[13:50:39.288]  
[13:50:39.288]  <debugvars>
[13:50:39.288]    // Pre-defined
[13:50:39.288]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:50:39.288]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[13:50:39.288]    __dp=0x00000000
[13:50:39.288]    __ap=0x00000000
[13:50:39.294]    __traceout=0x00000000      (Trace Disabled)
[13:50:39.294]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:50:39.294]    __FlashAddr=0x00000000
[13:50:39.294]    __FlashLen=0x00000000
[13:50:39.294]    __FlashArg=0x00000000
[13:50:39.294]    __FlashOp=0x00000000
[13:50:39.294]    __Result=0x00000000
[13:50:39.294]    
[13:50:39.294]    // User-defined
[13:50:39.294]    DbgMCU_CR=0x00000007
[13:50:39.294]    DbgMCU_APB1_Fz=0x00000000
[13:50:39.294]    DbgMCU_APB2_Fz=0x00000000
[13:50:39.294]    DoOptionByteLoading=0x00000000
[13:50:39.294]  </debugvars>
[13:50:39.294]  
[13:50:39.294]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[13:50:39.294]    <block atomic="false" info="">
[13:50:39.294]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[13:50:39.294]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[13:50:39.299]    </block>
[13:50:39.299]    <block atomic="false" info="DbgMCU registers">
[13:50:39.299]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[13:50:39.299]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[13:50:39.301]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[13:50:39.301]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[13:50:39.302]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[13:50:39.302]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[13:50:39.302]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:50:39.302]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[13:50:39.305]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:50:39.305]    </block>
[13:50:39.305]  </sequence>
[13:50:39.305]  
[13:52:43.819]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[13:52:43.819]  
[13:52:43.820]  <debugvars>
[13:52:43.821]    // Pre-defined
[13:52:43.821]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:52:43.821]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[13:52:43.822]    __dp=0x00000000
[13:52:43.822]    __ap=0x00000000
[13:52:43.822]    __traceout=0x00000000      (Trace Disabled)
[13:52:43.823]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:52:43.823]    __FlashAddr=0x00000000
[13:52:43.823]    __FlashLen=0x00000000
[13:52:43.824]    __FlashArg=0x00000000
[13:52:43.824]    __FlashOp=0x00000000
[13:52:43.824]    __Result=0x00000000
[13:52:43.824]    
[13:52:43.824]    // User-defined
[13:52:43.825]    DbgMCU_CR=0x00000007
[13:52:43.825]    DbgMCU_APB1_Fz=0x00000000
[13:52:43.825]    DbgMCU_APB2_Fz=0x00000000
[13:52:43.826]    DoOptionByteLoading=0x00000000
[13:52:43.826]  </debugvars>
[13:52:43.826]  
[13:52:43.826]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[13:52:43.826]    <block atomic="false" info="">
[13:52:43.826]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[13:52:43.826]        // -> [connectionFlash <= 0x00000000]
[13:52:43.826]      __var FLASH_BASE = 0x40022000 ;
[13:52:43.826]        // -> [FLASH_BASE <= 0x40022000]
[13:52:43.826]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[13:52:43.826]        // -> [FLASH_CR <= 0x40022004]
[13:52:43.826]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[13:52:43.826]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[13:52:43.826]      __var LOCK_BIT = ( 1 << 0 ) ;
[13:52:43.826]        // -> [LOCK_BIT <= 0x00000001]
[13:52:43.829]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[13:52:43.829]        // -> [OPTLOCK_BIT <= 0x00000004]
[13:52:43.830]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[13:52:43.830]        // -> [FLASH_KEYR <= 0x4002200C]
[13:52:43.830]      __var FLASH_KEY1 = 0x89ABCDEF ;
[13:52:43.830]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[13:52:43.831]      __var FLASH_KEY2 = 0x02030405 ;
[13:52:43.831]        // -> [FLASH_KEY2 <= 0x02030405]
[13:52:43.831]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[13:52:43.831]        // -> [FLASH_OPTKEYR <= 0x40022014]
[13:52:43.831]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[13:52:43.832]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[13:52:43.832]      __var FLASH_OPTKEY2 = 0x24252627 ;
[13:52:43.832]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[13:52:43.832]      __var FLASH_CR_Value = 0 ;
[13:52:43.832]        // -> [FLASH_CR_Value <= 0x00000000]
[13:52:43.833]      __var DoDebugPortStop = 1 ;
[13:52:43.834]        // -> [DoDebugPortStop <= 0x00000001]
[13:52:43.834]      __var DP_CTRL_STAT = 0x4 ;
[13:52:43.835]        // -> [DP_CTRL_STAT <= 0x00000004]
[13:52:43.835]      __var DP_SELECT = 0x8 ;
[13:52:43.835]        // -> [DP_SELECT <= 0x00000008]
[13:52:43.836]    </block>
[13:52:43.836]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[13:52:43.836]      // if-block "connectionFlash && DoOptionByteLoading"
[13:52:43.836]        // =>  FALSE
[13:52:43.837]      // skip if-block "connectionFlash && DoOptionByteLoading"
[13:52:43.837]    </control>
[13:52:43.837]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[13:52:43.838]      // if-block "DoDebugPortStop"
[13:52:43.838]        // =>  TRUE
[13:52:43.838]      <block atomic="false" info="">
[13:52:43.838]        WriteDP(DP_SELECT, 0x00000000);
[13:52:43.839]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[13:52:43.839]        WriteDP(DP_CTRL_STAT, 0x00000000);
[13:52:43.839]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[13:52:43.840]      </block>
[13:52:43.840]      // end if-block "DoDebugPortStop"
[13:52:43.840]    </control>
[13:52:43.841]  </sequence>
[13:52:43.841]  
[13:53:57.187]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[13:53:57.187]  
[13:53:57.188]  <debugvars>
[13:53:57.188]    // Pre-defined
[13:53:57.188]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:53:57.188]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:53:57.189]    __dp=0x00000000
[13:53:57.189]    __ap=0x00000000
[13:53:57.189]    __traceout=0x00000000      (Trace Disabled)
[13:53:57.189]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:53:57.189]    __FlashAddr=0x00000000
[13:53:57.190]    __FlashLen=0x00000000
[13:53:57.190]    __FlashArg=0x00000000
[13:53:57.190]    __FlashOp=0x00000000
[13:53:57.190]    __Result=0x00000000
[13:53:57.190]    
[13:53:57.190]    // User-defined
[13:53:57.191]    DbgMCU_CR=0x00000007
[13:53:57.191]    DbgMCU_APB1_Fz=0x00000000
[13:53:57.191]    DbgMCU_APB2_Fz=0x00000000
[13:53:57.191]    DoOptionByteLoading=0x00000000
[13:53:57.191]  </debugvars>
[13:53:57.192]  
[13:53:57.192]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[13:53:57.192]    <block atomic="false" info="">
[13:53:57.192]      Sequence("CheckID");
[13:53:57.192]        <sequence name="CheckID" Pname="" disable="false" info="">
[13:53:57.193]          <block atomic="false" info="">
[13:53:57.193]            __var pidr1 = 0;
[13:53:57.193]              // -> [pidr1 <= 0x00000000]
[13:53:57.193]            __var pidr2 = 0;
[13:53:57.193]              // -> [pidr2 <= 0x00000000]
[13:53:57.194]            __var jep106id = 0;
[13:53:57.194]              // -> [jep106id <= 0x00000000]
[13:53:57.194]            __var ROMTableBase = 0;
[13:53:57.194]              // -> [ROMTableBase <= 0x00000000]
[13:53:57.194]            __ap = 0;      // AHB-AP
[13:53:57.195]              // -> [__ap <= 0x00000000]
[13:53:57.195]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[13:53:57.195]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[13:53:57.196]              // -> [ROMTableBase <= 0xF0000000]
[13:53:57.196]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[13:53:57.197]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[13:53:57.197]              // -> [pidr1 <= 0x00000004]
[13:53:57.197]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[13:53:57.198]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[13:53:57.199]              // -> [pidr2 <= 0x0000000A]
[13:53:57.199]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[13:53:57.199]              // -> [jep106id <= 0x00000020]
[13:53:57.199]          </block>
[13:53:57.199]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[13:53:57.199]            // if-block "jep106id != 0x20"
[13:53:57.199]              // =>  FALSE
[13:53:57.199]            // skip if-block "jep106id != 0x20"
[13:53:57.201]          </control>
[13:53:57.201]        </sequence>
[13:53:57.201]    </block>
[13:53:57.201]  </sequence>
[13:53:57.202]  
[13:53:57.212]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[13:53:57.212]  
[13:53:57.240]  <debugvars>
[13:53:57.241]    // Pre-defined
[13:53:57.241]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:53:57.241]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:53:57.241]    __dp=0x00000000
[13:53:57.241]    __ap=0x00000000
[13:53:57.241]    __traceout=0x00000000      (Trace Disabled)
[13:53:57.241]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:53:57.242]    __FlashAddr=0x00000000
[13:53:57.242]    __FlashLen=0x00000000
[13:53:57.242]    __FlashArg=0x00000000
[13:53:57.242]    __FlashOp=0x00000000
[13:53:57.242]    __Result=0x00000000
[13:53:57.242]    
[13:53:57.242]    // User-defined
[13:53:57.242]    DbgMCU_CR=0x00000007
[13:53:57.244]    DbgMCU_APB1_Fz=0x00000000
[13:53:57.244]    DbgMCU_APB2_Fz=0x00000000
[13:53:57.244]    DoOptionByteLoading=0x00000000
[13:53:57.244]  </debugvars>
[13:53:57.245]  
[13:53:57.245]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[13:53:57.245]    <block atomic="false" info="">
[13:53:57.245]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[13:53:57.246]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[13:53:57.247]    </block>
[13:53:57.247]    <block atomic="false" info="DbgMCU registers">
[13:53:57.247]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[13:53:57.248]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[13:53:57.249]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[13:53:57.249]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[13:53:57.250]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[13:53:57.250]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[13:53:57.251]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:53:57.251]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[13:53:57.251]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:53:57.252]    </block>
[13:53:57.252]  </sequence>
[13:53:57.252]  
[13:54:04.952]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[13:54:04.952]  
[13:54:04.953]  <debugvars>
[13:54:04.953]    // Pre-defined
[13:54:04.953]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:54:04.953]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:54:04.954]    __dp=0x00000000
[13:54:04.954]    __ap=0x00000000
[13:54:04.954]    __traceout=0x00000000      (Trace Disabled)
[13:54:04.954]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:54:04.954]    __FlashAddr=0x00000000
[13:54:04.955]    __FlashLen=0x00000000
[13:54:04.955]    __FlashArg=0x00000000
[13:54:04.956]    __FlashOp=0x00000000
[13:54:04.956]    __Result=0x00000000
[13:54:04.956]    
[13:54:04.956]    // User-defined
[13:54:04.956]    DbgMCU_CR=0x00000007
[13:54:04.957]    DbgMCU_APB1_Fz=0x00000000
[13:54:04.957]    DbgMCU_APB2_Fz=0x00000000
[13:54:04.957]    DoOptionByteLoading=0x00000000
[13:54:04.957]  </debugvars>
[13:54:04.957]  
[13:54:04.957]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[13:54:04.958]    <block atomic="false" info="">
[13:54:04.958]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[13:54:04.958]        // -> [connectionFlash <= 0x00000001]
[13:54:04.958]      __var FLASH_BASE = 0x40022000 ;
[13:54:04.958]        // -> [FLASH_BASE <= 0x40022000]
[13:54:04.958]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[13:54:04.959]        // -> [FLASH_CR <= 0x40022004]
[13:54:04.959]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[13:54:04.959]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[13:54:04.959]      __var LOCK_BIT = ( 1 << 0 ) ;
[13:54:04.959]        // -> [LOCK_BIT <= 0x00000001]
[13:54:04.959]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[13:54:04.959]        // -> [OPTLOCK_BIT <= 0x00000004]
[13:54:04.959]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[13:54:04.961]        // -> [FLASH_KEYR <= 0x4002200C]
[13:54:04.961]      __var FLASH_KEY1 = 0x89ABCDEF ;
[13:54:04.961]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[13:54:04.961]      __var FLASH_KEY2 = 0x02030405 ;
[13:54:04.961]        // -> [FLASH_KEY2 <= 0x02030405]
[13:54:04.961]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[13:54:04.961]        // -> [FLASH_OPTKEYR <= 0x40022014]
[13:54:04.961]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[13:54:04.961]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[13:54:04.961]      __var FLASH_OPTKEY2 = 0x24252627 ;
[13:54:04.963]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[13:54:04.963]      __var FLASH_CR_Value = 0 ;
[13:54:04.964]        // -> [FLASH_CR_Value <= 0x00000000]
[13:54:04.965]      __var DoDebugPortStop = 1 ;
[13:54:04.965]        // -> [DoDebugPortStop <= 0x00000001]
[13:54:04.965]      __var DP_CTRL_STAT = 0x4 ;
[13:54:04.965]        // -> [DP_CTRL_STAT <= 0x00000004]
[13:54:04.965]      __var DP_SELECT = 0x8 ;
[13:54:04.965]        // -> [DP_SELECT <= 0x00000008]
[13:54:04.965]    </block>
[13:54:04.965]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[13:54:04.965]      // if-block "connectionFlash && DoOptionByteLoading"
[13:54:04.967]        // =>  FALSE
[13:54:04.967]      // skip if-block "connectionFlash && DoOptionByteLoading"
[13:54:04.968]    </control>
[13:54:04.968]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[13:54:04.968]      // if-block "DoDebugPortStop"
[13:54:04.968]        // =>  TRUE
[13:54:04.969]      <block atomic="false" info="">
[13:54:04.969]        WriteDP(DP_SELECT, 0x00000000);
[13:54:04.969]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[13:54:04.969]        WriteDP(DP_CTRL_STAT, 0x00000000);
[13:54:04.969]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[13:54:04.970]      </block>
[13:54:04.970]      // end if-block "DoDebugPortStop"
[13:54:04.971]    </control>
[13:54:04.971]  </sequence>
[13:54:04.971]  
[13:54:06.474]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[13:54:06.474]  
[13:54:06.475]  <debugvars>
[13:54:06.475]    // Pre-defined
[13:54:06.475]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:54:06.475]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[13:54:06.475]    __dp=0x00000000
[13:54:06.475]    __ap=0x00000000
[13:54:06.475]    __traceout=0x00000000      (Trace Disabled)
[13:54:06.475]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:54:06.476]    __FlashAddr=0x00000000
[13:54:06.476]    __FlashLen=0x00000000
[13:54:06.476]    __FlashArg=0x00000000
[13:54:06.476]    __FlashOp=0x00000000
[13:54:06.477]    __Result=0x00000000
[13:54:06.478]    
[13:54:06.478]    // User-defined
[13:54:06.479]    DbgMCU_CR=0x00000007
[13:54:06.479]    DbgMCU_APB1_Fz=0x00000000
[13:54:06.479]    DbgMCU_APB2_Fz=0x00000000
[13:54:06.479]    DoOptionByteLoading=0x00000000
[13:54:06.480]  </debugvars>
[13:54:06.480]  
[13:54:06.480]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[13:54:06.480]    <block atomic="false" info="">
[13:54:06.481]      Sequence("CheckID");
[13:54:06.481]        <sequence name="CheckID" Pname="" disable="false" info="">
[13:54:06.481]          <block atomic="false" info="">
[13:54:06.481]            __var pidr1 = 0;
[13:54:06.482]              // -> [pidr1 <= 0x00000000]
[13:54:06.482]            __var pidr2 = 0;
[13:54:06.482]              // -> [pidr2 <= 0x00000000]
[13:54:06.482]            __var jep106id = 0;
[13:54:06.483]              // -> [jep106id <= 0x00000000]
[13:54:06.483]            __var ROMTableBase = 0;
[13:54:06.483]              // -> [ROMTableBase <= 0x00000000]
[13:54:06.483]            __ap = 0;      // AHB-AP
[13:54:06.483]              // -> [__ap <= 0x00000000]
[13:54:06.484]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[13:54:06.484]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[13:54:06.484]              // -> [ROMTableBase <= 0xF0000000]
[13:54:06.485]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[13:54:06.485]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[13:54:06.485]              // -> [pidr1 <= 0x00000004]
[13:54:06.486]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[13:54:06.487]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[13:54:06.487]              // -> [pidr2 <= 0x0000000A]
[13:54:06.488]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[13:54:06.488]              // -> [jep106id <= 0x00000020]
[13:54:06.488]          </block>
[13:54:06.488]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[13:54:06.489]            // if-block "jep106id != 0x20"
[13:54:06.489]              // =>  FALSE
[13:54:06.489]            // skip if-block "jep106id != 0x20"
[13:54:06.489]          </control>
[13:54:06.489]        </sequence>
[13:54:06.490]    </block>
[13:54:06.490]  </sequence>
[13:54:06.490]  
[13:54:06.503]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[13:54:06.503]  
[13:54:06.524]  <debugvars>
[13:54:06.524]    // Pre-defined
[13:54:06.524]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:54:06.525]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[13:54:06.525]    __dp=0x00000000
[13:54:06.525]    __ap=0x00000000
[13:54:06.526]    __traceout=0x00000000      (Trace Disabled)
[13:54:06.526]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:54:06.526]    __FlashAddr=0x00000000
[13:54:06.526]    __FlashLen=0x00000000
[13:54:06.526]    __FlashArg=0x00000000
[13:54:06.526]    __FlashOp=0x00000000
[13:54:06.526]    __Result=0x00000000
[13:54:06.527]    
[13:54:06.527]    // User-defined
[13:54:06.527]    DbgMCU_CR=0x00000007
[13:54:06.528]    DbgMCU_APB1_Fz=0x00000000
[13:54:06.528]    DbgMCU_APB2_Fz=0x00000000
[13:54:06.528]    DoOptionByteLoading=0x00000000
[13:54:06.528]  </debugvars>
[13:54:06.528]  
[13:54:06.528]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[13:54:06.528]    <block atomic="false" info="">
[13:54:06.528]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[13:54:06.530]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[13:54:06.530]    </block>
[13:54:06.530]    <block atomic="false" info="DbgMCU registers">
[13:54:06.530]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[13:54:06.531]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[13:54:06.532]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[13:54:06.532]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[13:54:06.533]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[13:54:06.534]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[13:54:06.534]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:54:06.535]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[13:54:06.535]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:54:06.536]    </block>
[13:54:06.536]  </sequence>
[13:54:06.537]  
[13:55:25.240]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[13:55:25.240]  
[13:55:25.240]  <debugvars>
[13:55:25.240]    // Pre-defined
[13:55:25.241]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:55:25.241]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[13:55:25.242]    __dp=0x00000000
[13:55:25.242]    __ap=0x00000000
[13:55:25.242]    __traceout=0x00000000      (Trace Disabled)
[13:55:25.242]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:55:25.242]    __FlashAddr=0x00000000
[13:55:25.243]    __FlashLen=0x00000000
[13:55:25.243]    __FlashArg=0x00000000
[13:55:25.243]    __FlashOp=0x00000000
[13:55:25.243]    __Result=0x00000000
[13:55:25.243]    
[13:55:25.243]    // User-defined
[13:55:25.244]    DbgMCU_CR=0x00000007
[13:55:25.244]    DbgMCU_APB1_Fz=0x00000000
[13:55:25.245]    DbgMCU_APB2_Fz=0x00000000
[13:55:25.245]    DoOptionByteLoading=0x00000000
[13:55:25.245]  </debugvars>
[13:55:25.245]  
[13:55:25.245]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[13:55:25.246]    <block atomic="false" info="">
[13:55:25.246]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[13:55:25.246]        // -> [connectionFlash <= 0x00000000]
[13:55:25.246]      __var FLASH_BASE = 0x40022000 ;
[13:55:25.246]        // -> [FLASH_BASE <= 0x40022000]
[13:55:25.247]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[13:55:25.247]        // -> [FLASH_CR <= 0x40022004]
[13:55:25.247]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[13:55:25.247]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[13:55:25.248]      __var LOCK_BIT = ( 1 << 0 ) ;
[13:55:25.248]        // -> [LOCK_BIT <= 0x00000001]
[13:55:25.248]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[13:55:25.248]        // -> [OPTLOCK_BIT <= 0x00000004]
[13:55:25.248]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[13:55:25.249]        // -> [FLASH_KEYR <= 0x4002200C]
[13:55:25.249]      __var FLASH_KEY1 = 0x89ABCDEF ;
[13:55:25.249]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[13:55:25.249]      __var FLASH_KEY2 = 0x02030405 ;
[13:55:25.249]        // -> [FLASH_KEY2 <= 0x02030405]
[13:55:25.250]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[13:55:25.250]        // -> [FLASH_OPTKEYR <= 0x40022014]
[13:55:25.250]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[13:55:25.250]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[13:55:25.250]      __var FLASH_OPTKEY2 = 0x24252627 ;
[13:55:25.250]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[13:55:25.251]      __var FLASH_CR_Value = 0 ;
[13:55:25.251]        // -> [FLASH_CR_Value <= 0x00000000]
[13:55:25.251]      __var DoDebugPortStop = 1 ;
[13:55:25.251]        // -> [DoDebugPortStop <= 0x00000001]
[13:55:25.251]      __var DP_CTRL_STAT = 0x4 ;
[13:55:25.252]        // -> [DP_CTRL_STAT <= 0x00000004]
[13:55:25.252]      __var DP_SELECT = 0x8 ;
[13:55:25.252]        // -> [DP_SELECT <= 0x00000008]
[13:55:25.252]    </block>
[13:55:25.252]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[13:55:25.253]      // if-block "connectionFlash && DoOptionByteLoading"
[13:55:25.253]        // =>  FALSE
[13:55:25.253]      // skip if-block "connectionFlash && DoOptionByteLoading"
[13:55:25.253]    </control>
[13:55:25.253]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[13:55:25.254]      // if-block "DoDebugPortStop"
[13:55:25.254]        // =>  TRUE
[13:55:25.254]      <block atomic="false" info="">
[13:55:25.254]        WriteDP(DP_SELECT, 0x00000000);
[13:55:25.255]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[13:55:25.255]        WriteDP(DP_CTRL_STAT, 0x00000000);
[13:55:25.256]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[13:55:25.256]      </block>
[13:55:25.256]      // end if-block "DoDebugPortStop"
[13:55:25.256]    </control>
[13:55:25.256]  </sequence>
[13:55:25.256]  
[14:02:45.213]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:02:45.213]  
[14:02:45.213]  <debugvars>
[14:02:45.214]    // Pre-defined
[14:02:45.214]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:02:45.215]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:02:45.215]    __dp=0x00000000
[14:02:45.215]    __ap=0x00000000
[14:02:45.215]    __traceout=0x00000000      (Trace Disabled)
[14:02:45.216]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:02:45.216]    __FlashAddr=0x00000000
[14:02:45.216]    __FlashLen=0x00000000
[14:02:45.217]    __FlashArg=0x00000000
[14:02:45.217]    __FlashOp=0x00000000
[14:02:45.217]    __Result=0x00000000
[14:02:45.217]    
[14:02:45.217]    // User-defined
[14:02:45.218]    DbgMCU_CR=0x00000007
[14:02:45.218]    DbgMCU_APB1_Fz=0x00000000
[14:02:45.218]    DbgMCU_APB2_Fz=0x00000000
[14:02:45.218]    DoOptionByteLoading=0x00000000
[14:02:45.218]  </debugvars>
[14:02:45.218]  
[14:02:45.219]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:02:45.219]    <block atomic="false" info="">
[14:02:45.219]      Sequence("CheckID");
[14:02:45.219]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:02:45.219]          <block atomic="false" info="">
[14:02:45.220]            __var pidr1 = 0;
[14:02:45.220]              // -> [pidr1 <= 0x00000000]
[14:02:45.220]            __var pidr2 = 0;
[14:02:45.220]              // -> [pidr2 <= 0x00000000]
[14:02:45.220]            __var jep106id = 0;
[14:02:45.220]              // -> [jep106id <= 0x00000000]
[14:02:45.221]            __var ROMTableBase = 0;
[14:02:45.221]              // -> [ROMTableBase <= 0x00000000]
[14:02:45.221]            __ap = 0;      // AHB-AP
[14:02:45.221]              // -> [__ap <= 0x00000000]
[14:02:45.221]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:02:45.222]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:02:45.222]              // -> [ROMTableBase <= 0xF0000000]
[14:02:45.222]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:02:45.223]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:02:45.223]              // -> [pidr1 <= 0x00000004]
[14:02:45.224]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:02:45.224]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:02:45.225]              // -> [pidr2 <= 0x0000000A]
[14:02:45.225]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:02:45.225]              // -> [jep106id <= 0x00000020]
[14:02:45.225]          </block>
[14:02:45.226]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:02:45.226]            // if-block "jep106id != 0x20"
[14:02:45.226]              // =>  FALSE
[14:02:45.227]            // skip if-block "jep106id != 0x20"
[14:02:45.227]          </control>
[14:02:45.227]        </sequence>
[14:02:45.228]    </block>
[14:02:45.228]  </sequence>
[14:02:45.228]  
[14:02:45.239]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:02:45.239]  
[14:02:45.240]  <debugvars>
[14:02:45.240]    // Pre-defined
[14:02:45.240]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:02:45.240]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:02:45.240]    __dp=0x00000000
[14:02:45.242]    __ap=0x00000000
[14:02:45.242]    __traceout=0x00000000      (Trace Disabled)
[14:02:45.242]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:02:45.242]    __FlashAddr=0x00000000
[14:02:45.243]    __FlashLen=0x00000000
[14:02:45.243]    __FlashArg=0x00000000
[14:02:45.243]    __FlashOp=0x00000000
[14:02:45.243]    __Result=0x00000000
[14:02:45.243]    
[14:02:45.243]    // User-defined
[14:02:45.244]    DbgMCU_CR=0x00000007
[14:02:45.244]    DbgMCU_APB1_Fz=0x00000000
[14:02:45.244]    DbgMCU_APB2_Fz=0x00000000
[14:02:45.244]    DoOptionByteLoading=0x00000000
[14:02:45.244]  </debugvars>
[14:02:45.245]  
[14:02:45.245]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:02:45.245]    <block atomic="false" info="">
[14:02:45.245]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:02:45.246]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:02:45.246]    </block>
[14:02:45.247]    <block atomic="false" info="DbgMCU registers">
[14:02:45.247]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:02:45.248]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[14:02:45.249]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[14:02:45.249]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:02:45.250]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:02:45.251]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:02:45.251]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:02:45.251]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:02:45.251]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:02:45.251]    </block>
[14:02:45.251]  </sequence>
[14:02:45.251]  
[14:02:52.911]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:02:52.911]  
[14:02:52.911]  <debugvars>
[14:02:52.911]    // Pre-defined
[14:02:52.912]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:02:52.912]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:02:52.912]    __dp=0x00000000
[14:02:52.913]    __ap=0x00000000
[14:02:52.913]    __traceout=0x00000000      (Trace Disabled)
[14:02:52.914]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:02:52.914]    __FlashAddr=0x00000000
[14:02:52.914]    __FlashLen=0x00000000
[14:02:52.914]    __FlashArg=0x00000000
[14:02:52.915]    __FlashOp=0x00000000
[14:02:52.915]    __Result=0x00000000
[14:02:52.915]    
[14:02:52.915]    // User-defined
[14:02:52.915]    DbgMCU_CR=0x00000007
[14:02:52.915]    DbgMCU_APB1_Fz=0x00000000
[14:02:52.916]    DbgMCU_APB2_Fz=0x00000000
[14:02:52.916]    DoOptionByteLoading=0x00000000
[14:02:52.916]  </debugvars>
[14:02:52.916]  
[14:02:52.916]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:02:52.916]    <block atomic="false" info="">
[14:02:52.917]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:02:52.917]        // -> [connectionFlash <= 0x00000001]
[14:02:52.917]      __var FLASH_BASE = 0x40022000 ;
[14:02:52.917]        // -> [FLASH_BASE <= 0x40022000]
[14:02:52.917]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:02:52.918]        // -> [FLASH_CR <= 0x40022004]
[14:02:52.918]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:02:52.918]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:02:52.918]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:02:52.918]        // -> [LOCK_BIT <= 0x00000001]
[14:02:52.918]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:02:52.918]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:02:52.919]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:02:52.919]        // -> [FLASH_KEYR <= 0x4002200C]
[14:02:52.919]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:02:52.920]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:02:52.920]      __var FLASH_KEY2 = 0x02030405 ;
[14:02:52.921]        // -> [FLASH_KEY2 <= 0x02030405]
[14:02:52.921]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:02:52.921]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:02:52.922]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:02:52.922]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:02:52.922]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:02:52.922]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:02:52.922]      __var FLASH_CR_Value = 0 ;
[14:02:52.922]        // -> [FLASH_CR_Value <= 0x00000000]
[14:02:52.922]      __var DoDebugPortStop = 1 ;
[14:02:52.923]        // -> [DoDebugPortStop <= 0x00000001]
[14:02:52.923]      __var DP_CTRL_STAT = 0x4 ;
[14:02:52.923]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:02:52.923]      __var DP_SELECT = 0x8 ;
[14:02:52.923]        // -> [DP_SELECT <= 0x00000008]
[14:02:52.923]    </block>
[14:02:52.923]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:02:52.924]      // if-block "connectionFlash && DoOptionByteLoading"
[14:02:52.924]        // =>  FALSE
[14:02:52.924]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:02:52.924]    </control>
[14:02:52.924]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:02:52.924]      // if-block "DoDebugPortStop"
[14:02:52.925]        // =>  TRUE
[14:02:52.925]      <block atomic="false" info="">
[14:02:52.925]        WriteDP(DP_SELECT, 0x00000000);
[14:02:52.926]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:02:52.926]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:02:52.927]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:02:52.927]      </block>
[14:02:52.927]      // end if-block "DoDebugPortStop"
[14:02:52.928]    </control>
[14:02:52.928]  </sequence>
[14:02:52.928]  
