/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : D:\2025work\STM32L072PRO\source-application\user\driver_Sequences_0018.log
 *  Created     : 10:43:16 (16/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : D:\2025work\STM32L072PRO\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[10:43:16.746]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:43:16.746]  
[10:43:16.777]  <debugvars>
[10:43:16.798]    // Pre-defined
[10:43:16.818]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:43:16.835]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:43:16.840]    __dp=0x00000000
[10:43:16.840]    __ap=0x00000000
[10:43:16.840]    __traceout=0x00000000      (Trace Disabled)
[10:43:16.840]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:43:16.840]    __FlashAddr=0x00000000
[10:43:16.840]    __FlashLen=0x00000000
[10:43:16.840]    __FlashArg=0x00000000
[10:43:16.840]    __FlashOp=0x00000000
[10:43:16.840]    __Result=0x00000000
[10:43:16.840]    
[10:43:16.840]    // User-defined
[10:43:16.840]    DbgMCU_CR=0x00000007
[10:43:16.840]    DbgMCU_APB1_Fz=0x00000000
[10:43:16.840]    DbgMCU_APB2_Fz=0x00000000
[10:43:16.840]    DoOptionByteLoading=0x00000000
[10:43:16.840]  </debugvars>
[10:43:16.845]  
[10:43:16.845]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:43:16.845]    <block atomic="false" info="">
[10:43:16.845]      Sequence("CheckID");
[10:43:16.845]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:43:16.845]          <block atomic="false" info="">
[10:43:16.845]            __var pidr1 = 0;
[10:43:16.845]              // -> [pidr1 <= 0x00000000]
[10:43:16.845]            __var pidr2 = 0;
[10:43:16.845]              // -> [pidr2 <= 0x00000000]
[10:43:16.848]            __var jep106id = 0;
[10:43:16.848]              // -> [jep106id <= 0x00000000]
[10:43:16.848]            __var ROMTableBase = 0;
[10:43:16.848]              // -> [ROMTableBase <= 0x00000000]
[10:43:16.848]            __ap = 0;      // AHB-AP
[10:43:16.848]              // -> [__ap <= 0x00000000]
[10:43:16.848]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:43:16.850]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:43:16.850]              // -> [ROMTableBase <= 0xF0000000]
[10:43:16.850]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:43:16.850]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:43:16.850]              // -> [pidr1 <= 0x00000004]
[10:43:16.850]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:43:16.850]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:43:16.850]              // -> [pidr2 <= 0x0000000A]
[10:43:16.850]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:43:16.850]              // -> [jep106id <= 0x00000020]
[10:43:16.850]          </block>
[10:43:16.850]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:43:16.850]            // if-block "jep106id != 0x20"
[10:43:16.850]              // =>  FALSE
[10:43:16.855]            // skip if-block "jep106id != 0x20"
[10:43:16.855]          </control>
[10:43:16.855]        </sequence>
[10:43:16.855]    </block>
[10:43:16.855]  </sequence>
[10:43:16.855]  
[10:43:16.866]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:43:16.866]  
[10:43:16.876]  <debugvars>
[10:43:16.876]    // Pre-defined
[10:43:16.876]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:43:16.876]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:43:16.876]    __dp=0x00000000
[10:43:16.876]    __ap=0x00000000
[10:43:16.876]    __traceout=0x00000000      (Trace Disabled)
[10:43:16.876]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:43:16.876]    __FlashAddr=0x00000000
[10:43:16.876]    __FlashLen=0x00000000
[10:43:16.876]    __FlashArg=0x00000000
[10:43:16.876]    __FlashOp=0x00000000
[10:43:16.876]    __Result=0x00000000
[10:43:16.876]    
[10:43:16.876]    // User-defined
[10:43:16.876]    DbgMCU_CR=0x00000007
[10:43:16.882]    DbgMCU_APB1_Fz=0x00000000
[10:43:16.882]    DbgMCU_APB2_Fz=0x00000000
[10:43:16.882]    DoOptionByteLoading=0x00000000
[10:43:16.882]  </debugvars>
[10:43:16.882]  
[10:43:16.882]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:43:16.882]    <block atomic="false" info="">
[10:43:16.882]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:43:16.884]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:43:16.884]    </block>
[10:43:16.884]    <block atomic="false" info="DbgMCU registers">
[10:43:16.884]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:43:16.887]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[10:43:16.887]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[10:43:16.887]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:43:16.887]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:43:16.887]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:43:16.887]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:43:16.887]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:43:16.887]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:43:16.887]    </block>
[10:43:16.892]  </sequence>
[10:43:16.892]  
[10:43:25.133]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:43:25.133]  
[10:43:25.133]  <debugvars>
[10:43:25.133]    // Pre-defined
[10:43:25.133]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:43:25.133]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:43:25.133]    __dp=0x00000000
[10:43:25.133]    __ap=0x00000000
[10:43:25.133]    __traceout=0x00000000      (Trace Disabled)
[10:43:25.133]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:43:25.138]    __FlashAddr=0x00000000
[10:43:25.138]    __FlashLen=0x00000000
[10:43:25.138]    __FlashArg=0x00000000
[10:43:25.138]    __FlashOp=0x00000000
[10:43:25.138]    __Result=0x00000000
[10:43:25.138]    
[10:43:25.138]    // User-defined
[10:43:25.138]    DbgMCU_CR=0x00000007
[10:43:25.138]    DbgMCU_APB1_Fz=0x00000000
[10:43:25.138]    DbgMCU_APB2_Fz=0x00000000
[10:43:25.138]    DoOptionByteLoading=0x00000000
[10:43:25.138]  </debugvars>
[10:43:25.138]  
[10:43:25.138]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:43:25.138]    <block atomic="false" info="">
[10:43:25.138]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:43:25.138]        // -> [connectionFlash <= 0x00000001]
[10:43:25.138]      __var FLASH_BASE = 0x40022000 ;
[10:43:25.138]        // -> [FLASH_BASE <= 0x40022000]
[10:43:25.138]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:43:25.138]        // -> [FLASH_CR <= 0x40022004]
[10:43:25.138]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:43:25.143]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:43:25.143]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:43:25.143]        // -> [LOCK_BIT <= 0x00000001]
[10:43:25.143]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:43:25.143]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:43:25.143]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:43:25.143]        // -> [FLASH_KEYR <= 0x4002200C]
[10:43:25.143]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:43:25.143]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:43:25.143]      __var FLASH_KEY2 = 0x02030405 ;
[10:43:25.143]        // -> [FLASH_KEY2 <= 0x02030405]
[10:43:25.143]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:43:25.143]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:43:25.143]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:43:25.143]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:43:25.143]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:43:25.143]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:43:25.143]      __var FLASH_CR_Value = 0 ;
[10:43:25.143]        // -> [FLASH_CR_Value <= 0x00000000]
[10:43:25.143]      __var DoDebugPortStop = 1 ;
[10:43:25.143]        // -> [DoDebugPortStop <= 0x00000001]
[10:43:25.143]      __var DP_CTRL_STAT = 0x4 ;
[10:43:25.143]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:43:25.143]      __var DP_SELECT = 0x8 ;
[10:43:25.143]        // -> [DP_SELECT <= 0x00000008]
[10:43:25.143]    </block>
[10:43:25.143]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:43:25.148]      // if-block "connectionFlash && DoOptionByteLoading"
[10:43:25.148]        // =>  FALSE
[10:43:25.148]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:43:25.148]    </control>
[10:43:25.148]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:43:25.148]      // if-block "DoDebugPortStop"
[10:43:25.148]        // =>  TRUE
[10:43:25.148]      <block atomic="false" info="">
[10:43:25.148]        WriteDP(DP_SELECT, 0x00000000);
[10:43:25.148]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:43:25.148]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:43:25.148]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:43:25.148]      </block>
[10:43:25.148]      // end if-block "DoDebugPortStop"
[10:43:25.148]    </control>
[10:43:25.148]  </sequence>
[10:43:25.148]  
[10:46:37.160]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:46:37.160]  
[10:46:37.160]  <debugvars>
[10:46:37.160]    // Pre-defined
[10:46:37.161]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:46:37.161]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:46:37.161]    __dp=0x00000000
[10:46:37.162]    __ap=0x00000000
[10:46:37.162]    __traceout=0x00000000      (Trace Disabled)
[10:46:37.162]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:46:37.162]    __FlashAddr=0x00000000
[10:46:37.163]    __FlashLen=0x00000000
[10:46:37.163]    __FlashArg=0x00000000
[10:46:37.163]    __FlashOp=0x00000000
[10:46:37.163]    __Result=0x00000000
[10:46:37.163]    
[10:46:37.163]    // User-defined
[10:46:37.163]    DbgMCU_CR=0x00000007
[10:46:37.164]    DbgMCU_APB1_Fz=0x00000000
[10:46:37.164]    DbgMCU_APB2_Fz=0x00000000
[10:46:37.164]    DoOptionByteLoading=0x00000000
[10:46:37.164]  </debugvars>
[10:46:37.164]  
[10:46:37.165]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:46:37.165]    <block atomic="false" info="">
[10:46:37.165]      Sequence("CheckID");
[10:46:37.165]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:46:37.165]          <block atomic="false" info="">
[10:46:37.165]            __var pidr1 = 0;
[10:46:37.166]              // -> [pidr1 <= 0x00000000]
[10:46:37.166]            __var pidr2 = 0;
[10:46:37.166]              // -> [pidr2 <= 0x00000000]
[10:46:37.166]            __var jep106id = 0;
[10:46:37.166]              // -> [jep106id <= 0x00000000]
[10:46:37.167]            __var ROMTableBase = 0;
[10:46:37.167]              // -> [ROMTableBase <= 0x00000000]
[10:46:37.167]            __ap = 0;      // AHB-AP
[10:46:37.167]              // -> [__ap <= 0x00000000]
[10:46:37.167]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:46:37.168]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:46:37.168]              // -> [ROMTableBase <= 0xF0000000]
[10:46:37.169]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:46:37.170]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:46:37.170]              // -> [pidr1 <= 0x00000004]
[10:46:37.171]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:46:37.172]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:46:37.172]              // -> [pidr2 <= 0x0000000A]
[10:46:37.172]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:46:37.172]              // -> [jep106id <= 0x00000020]
[10:46:37.172]          </block>
[10:46:37.172]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:46:37.173]            // if-block "jep106id != 0x20"
[10:46:37.173]              // =>  FALSE
[10:46:37.173]            // skip if-block "jep106id != 0x20"
[10:46:37.173]          </control>
[10:46:37.173]        </sequence>
[10:46:37.174]    </block>
[10:46:37.174]  </sequence>
[10:46:37.174]  
[10:46:37.185]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:46:37.185]  
[10:46:37.185]  <debugvars>
[10:46:37.186]    // Pre-defined
[10:46:37.186]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:46:37.186]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:46:37.186]    __dp=0x00000000
[10:46:37.187]    __ap=0x00000000
[10:46:37.187]    __traceout=0x00000000      (Trace Disabled)
[10:46:37.187]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:46:37.187]    __FlashAddr=0x00000000
[10:46:37.187]    __FlashLen=0x00000000
[10:46:37.188]    __FlashArg=0x00000000
[10:46:37.188]    __FlashOp=0x00000000
[10:46:37.188]    __Result=0x00000000
[10:46:37.188]    
[10:46:37.188]    // User-defined
[10:46:37.188]    DbgMCU_CR=0x00000007
[10:46:37.189]    DbgMCU_APB1_Fz=0x00000000
[10:46:37.189]    DbgMCU_APB2_Fz=0x00000000
[10:46:37.189]    DoOptionByteLoading=0x00000000
[10:46:37.189]  </debugvars>
[10:46:37.189]  
[10:46:37.189]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:46:37.190]    <block atomic="false" info="">
[10:46:37.190]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:46:37.191]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:46:37.191]    </block>
[10:46:37.191]    <block atomic="false" info="DbgMCU registers">
[10:46:37.192]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:46:37.192]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[10:46:37.193]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[10:46:37.193]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:46:37.194]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:46:37.194]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:46:37.195]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:46:37.195]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:46:37.196]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:46:37.196]    </block>
[10:46:37.197]  </sequence>
[10:46:37.197]  
[10:46:45.189]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:46:45.189]  
[10:46:45.189]  <debugvars>
[10:46:45.190]    // Pre-defined
[10:46:45.191]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:46:45.191]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:46:45.192]    __dp=0x00000000
[10:46:45.192]    __ap=0x00000000
[10:46:45.193]    __traceout=0x00000000      (Trace Disabled)
[10:46:45.194]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:46:45.194]    __FlashAddr=0x00000000
[10:46:45.195]    __FlashLen=0x00000000
[10:46:45.195]    __FlashArg=0x00000000
[10:46:45.196]    __FlashOp=0x00000000
[10:46:45.196]    __Result=0x00000000
[10:46:45.197]    
[10:46:45.197]    // User-defined
[10:46:45.197]    DbgMCU_CR=0x00000007
[10:46:45.198]    DbgMCU_APB1_Fz=0x00000000
[10:46:45.198]    DbgMCU_APB2_Fz=0x00000000
[10:46:45.199]    DoOptionByteLoading=0x00000000
[10:46:45.199]  </debugvars>
[10:46:45.199]  
[10:46:45.199]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:46:45.200]    <block atomic="false" info="">
[10:46:45.201]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:46:45.201]        // -> [connectionFlash <= 0x00000001]
[10:46:45.202]      __var FLASH_BASE = 0x40022000 ;
[10:46:45.203]        // -> [FLASH_BASE <= 0x40022000]
[10:46:45.203]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:46:45.203]        // -> [FLASH_CR <= 0x40022004]
[10:46:45.204]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:46:45.204]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:46:45.205]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:46:45.205]        // -> [LOCK_BIT <= 0x00000001]
[10:46:45.206]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:46:45.206]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:46:45.206]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:46:45.207]        // -> [FLASH_KEYR <= 0x4002200C]
[10:46:45.207]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:46:45.207]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:46:45.208]      __var FLASH_KEY2 = 0x02030405 ;
[10:46:45.208]        // -> [FLASH_KEY2 <= 0x02030405]
[10:46:45.208]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:46:45.208]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:46:45.208]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:46:45.208]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:46:45.209]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:46:45.209]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:46:45.209]      __var FLASH_CR_Value = 0 ;
[10:46:45.209]        // -> [FLASH_CR_Value <= 0x00000000]
[10:46:45.209]      __var DoDebugPortStop = 1 ;
[10:46:45.210]        // -> [DoDebugPortStop <= 0x00000001]
[10:46:45.210]      __var DP_CTRL_STAT = 0x4 ;
[10:46:45.210]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:46:45.210]      __var DP_SELECT = 0x8 ;
[10:46:45.210]        // -> [DP_SELECT <= 0x00000008]
[10:46:45.210]    </block>
[10:46:45.211]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:46:45.211]      // if-block "connectionFlash && DoOptionByteLoading"
[10:46:45.211]        // =>  FALSE
[10:46:45.211]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:46:45.212]    </control>
[10:46:45.212]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:46:45.212]      // if-block "DoDebugPortStop"
[10:46:45.212]        // =>  TRUE
[10:46:45.212]      <block atomic="false" info="">
[10:46:45.213]        WriteDP(DP_SELECT, 0x00000000);
[10:46:45.213]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:46:45.213]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:46:45.214]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:46:45.214]      </block>
[10:46:45.214]      // end if-block "DoDebugPortStop"
[10:46:45.215]    </control>
[10:46:45.215]  </sequence>
[10:46:45.215]  
[10:48:49.447]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:48:49.447]  
[10:48:49.448]  <debugvars>
[10:48:49.448]    // Pre-defined
[10:48:49.449]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:48:49.449]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:48:49.449]    __dp=0x00000000
[10:48:49.449]    __ap=0x00000000
[10:48:49.450]    __traceout=0x00000000      (Trace Disabled)
[10:48:49.450]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:48:49.450]    __FlashAddr=0x00000000
[10:48:49.450]    __FlashLen=0x00000000
[10:48:49.451]    __FlashArg=0x00000000
[10:48:49.452]    __FlashOp=0x00000000
[10:48:49.452]    __Result=0x00000000
[10:48:49.453]    
[10:48:49.453]    // User-defined
[10:48:49.453]    DbgMCU_CR=0x00000007
[10:48:49.453]    DbgMCU_APB1_Fz=0x00000000
[10:48:49.453]    DbgMCU_APB2_Fz=0x00000000
[10:48:49.454]    DoOptionByteLoading=0x00000000
[10:48:49.454]  </debugvars>
[10:48:49.454]  
[10:48:49.454]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:48:49.455]    <block atomic="false" info="">
[10:48:49.455]      Sequence("CheckID");
[10:48:49.455]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:48:49.455]          <block atomic="false" info="">
[10:48:49.455]            __var pidr1 = 0;
[10:48:49.455]              // -> [pidr1 <= 0x00000000]
[10:48:49.456]            __var pidr2 = 0;
[10:48:49.456]              // -> [pidr2 <= 0x00000000]
[10:48:49.456]            __var jep106id = 0;
[10:48:49.456]              // -> [jep106id <= 0x00000000]
[10:48:49.456]            __var ROMTableBase = 0;
[10:48:49.457]              // -> [ROMTableBase <= 0x00000000]
[10:48:49.457]            __ap = 0;      // AHB-AP
[10:48:49.457]              // -> [__ap <= 0x00000000]
[10:48:49.457]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:48:49.458]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:48:49.458]              // -> [ROMTableBase <= 0xF0000000]
[10:48:49.458]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:48:49.460]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:48:49.460]              // -> [pidr1 <= 0x00000004]
[10:48:49.460]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:48:49.461]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:48:49.461]              // -> [pidr2 <= 0x0000000A]
[10:48:49.462]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:48:49.462]              // -> [jep106id <= 0x00000020]
[10:48:49.462]          </block>
[10:48:49.462]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:48:49.463]            // if-block "jep106id != 0x20"
[10:48:49.463]              // =>  FALSE
[10:48:49.463]            // skip if-block "jep106id != 0x20"
[10:48:49.463]          </control>
[10:48:49.463]        </sequence>
[10:48:49.464]    </block>
[10:48:49.464]  </sequence>
[10:48:49.464]  
[10:48:49.477]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:48:49.477]  
[10:48:49.477]  <debugvars>
[10:48:49.477]    // Pre-defined
[10:48:49.478]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:48:49.478]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:48:49.478]    __dp=0x00000000
[10:48:49.479]    __ap=0x00000000
[10:48:49.479]    __traceout=0x00000000      (Trace Disabled)
[10:48:49.479]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:48:49.479]    __FlashAddr=0x00000000
[10:48:49.480]    __FlashLen=0x00000000
[10:48:49.480]    __FlashArg=0x00000000
[10:48:49.480]    __FlashOp=0x00000000
[10:48:49.480]    __Result=0x00000000
[10:48:49.480]    
[10:48:49.480]    // User-defined
[10:48:49.481]    DbgMCU_CR=0x00000007
[10:48:49.481]    DbgMCU_APB1_Fz=0x00000000
[10:48:49.481]    DbgMCU_APB2_Fz=0x00000000
[10:48:49.481]    DoOptionByteLoading=0x00000000
[10:48:49.481]  </debugvars>
[10:48:49.481]  
[10:48:49.482]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:48:49.482]    <block atomic="false" info="">
[10:48:49.482]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:48:49.483]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:48:49.483]    </block>
[10:48:49.483]    <block atomic="false" info="DbgMCU registers">
[10:48:49.484]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:48:49.485]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[10:48:49.485]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[10:48:49.486]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:48:49.486]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:48:49.487]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:48:49.488]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:48:49.488]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:48:49.489]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:48:49.489]    </block>
[10:48:49.489]  </sequence>
[10:48:49.490]  
[10:48:57.543]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:48:57.543]  
[10:48:57.543]  <debugvars>
[10:48:57.546]    // Pre-defined
[10:48:57.546]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:48:57.546]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:48:57.546]    __dp=0x00000000
[10:48:57.546]    __ap=0x00000000
[10:48:57.546]    __traceout=0x00000000      (Trace Disabled)
[10:48:57.548]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:48:57.548]    __FlashAddr=0x00000000
[10:48:57.549]    __FlashLen=0x00000000
[10:48:57.549]    __FlashArg=0x00000000
[10:48:57.550]    __FlashOp=0x00000000
[10:48:57.550]    __Result=0x00000000
[10:48:57.550]    
[10:48:57.550]    // User-defined
[10:48:57.551]    DbgMCU_CR=0x00000007
[10:48:57.551]    DbgMCU_APB1_Fz=0x00000000
[10:48:57.551]    DbgMCU_APB2_Fz=0x00000000
[10:48:57.552]    DoOptionByteLoading=0x00000000
[10:48:57.552]  </debugvars>
[10:48:57.552]  
[10:48:57.552]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:48:57.553]    <block atomic="false" info="">
[10:48:57.553]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:48:57.553]        // -> [connectionFlash <= 0x00000001]
[10:48:57.553]      __var FLASH_BASE = 0x40022000 ;
[10:48:57.553]        // -> [FLASH_BASE <= 0x40022000]
[10:48:57.553]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:48:57.554]        // -> [FLASH_CR <= 0x40022004]
[10:48:57.554]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:48:57.554]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:48:57.554]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:48:57.554]        // -> [LOCK_BIT <= 0x00000001]
[10:48:57.555]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:48:57.555]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:48:57.555]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:48:57.556]        // -> [FLASH_KEYR <= 0x4002200C]
[10:48:57.556]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:48:57.556]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:48:57.556]      __var FLASH_KEY2 = 0x02030405 ;
[10:48:57.556]        // -> [FLASH_KEY2 <= 0x02030405]
[10:48:57.556]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:48:57.556]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:48:57.558]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:48:57.558]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:48:57.558]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:48:57.558]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:48:57.559]      __var FLASH_CR_Value = 0 ;
[10:48:57.559]        // -> [FLASH_CR_Value <= 0x00000000]
[10:48:57.559]      __var DoDebugPortStop = 1 ;
[10:48:57.559]        // -> [DoDebugPortStop <= 0x00000001]
[10:48:57.559]      __var DP_CTRL_STAT = 0x4 ;
[10:48:57.559]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:48:57.560]      __var DP_SELECT = 0x8 ;
[10:48:57.560]        // -> [DP_SELECT <= 0x00000008]
[10:48:57.560]    </block>
[10:48:57.560]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:48:57.560]      // if-block "connectionFlash && DoOptionByteLoading"
[10:48:57.561]        // =>  FALSE
[10:48:57.561]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:48:57.561]    </control>
[10:48:57.561]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:48:57.561]      // if-block "DoDebugPortStop"
[10:48:57.561]        // =>  TRUE
[10:48:57.562]      <block atomic="false" info="">
[10:48:57.562]        WriteDP(DP_SELECT, 0x00000000);
[10:48:57.562]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:48:57.563]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:48:57.563]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:48:57.563]      </block>
[10:48:57.563]      // end if-block "DoDebugPortStop"
[10:48:57.563]    </control>
[10:48:57.563]  </sequence>
[10:48:57.563]  
[10:50:35.239]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:50:35.239]  
[10:50:35.239]  <debugvars>
[10:50:35.239]    // Pre-defined
[10:50:35.239]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:50:35.239]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:50:35.239]    __dp=0x00000000
[10:50:35.239]    __ap=0x00000000
[10:50:35.239]    __traceout=0x00000000      (Trace Disabled)
[10:50:35.239]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:50:35.239]    __FlashAddr=0x00000000
[10:50:35.239]    __FlashLen=0x00000000
[10:50:35.239]    __FlashArg=0x00000000
[10:50:35.239]    __FlashOp=0x00000000
[10:50:35.243]    __Result=0x00000000
[10:50:35.243]    
[10:50:35.243]    // User-defined
[10:50:35.243]    DbgMCU_CR=0x00000007
[10:50:35.243]    DbgMCU_APB1_Fz=0x00000000
[10:50:35.243]    DbgMCU_APB2_Fz=0x00000000
[10:50:35.243]    DoOptionByteLoading=0x00000000
[10:50:35.243]  </debugvars>
[10:50:35.243]  
[10:50:35.243]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:50:35.243]    <block atomic="false" info="">
[10:50:35.243]      Sequence("CheckID");
[10:50:35.243]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:50:35.243]          <block atomic="false" info="">
[10:50:35.243]            __var pidr1 = 0;
[10:50:35.243]              // -> [pidr1 <= 0x00000000]
[10:50:35.243]            __var pidr2 = 0;
[10:50:35.243]              // -> [pidr2 <= 0x00000000]
[10:50:35.243]            __var jep106id = 0;
[10:50:35.243]              // -> [jep106id <= 0x00000000]
[10:50:35.243]            __var ROMTableBase = 0;
[10:50:35.243]              // -> [ROMTableBase <= 0x00000000]
[10:50:35.243]            __ap = 0;      // AHB-AP
[10:50:35.243]              // -> [__ap <= 0x00000000]
[10:50:35.243]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:50:35.249]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:50:35.249]              // -> [ROMTableBase <= 0xF0000000]
[10:50:35.249]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:50:35.249]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:50:35.249]              // -> [pidr1 <= 0x00000004]
[10:50:35.249]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:50:35.249]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:50:35.249]              // -> [pidr2 <= 0x0000000A]
[10:50:35.249]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:50:35.249]              // -> [jep106id <= 0x00000020]
[10:50:35.249]          </block>
[10:50:35.249]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:50:35.254]            // if-block "jep106id != 0x20"
[10:50:35.254]              // =>  FALSE
[10:50:35.254]            // skip if-block "jep106id != 0x20"
[10:50:35.254]          </control>
[10:50:35.254]        </sequence>
[10:50:35.254]    </block>
[10:50:35.254]  </sequence>
[10:50:35.254]  
[10:50:35.268]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:50:35.268]  
[10:50:35.268]  <debugvars>
[10:50:35.268]    // Pre-defined
[10:50:35.269]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:50:35.269]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:50:35.269]    __dp=0x00000000
[10:50:35.270]    __ap=0x00000000
[10:50:35.270]    __traceout=0x00000000      (Trace Disabled)
[10:50:35.270]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:50:35.271]    __FlashAddr=0x00000000
[10:50:35.271]    __FlashLen=0x00000000
[10:50:35.271]    __FlashArg=0x00000000
[10:50:35.272]    __FlashOp=0x00000000
[10:50:35.272]    __Result=0x00000000
[10:50:35.272]    
[10:50:35.272]    // User-defined
[10:50:35.272]    DbgMCU_CR=0x00000007
[10:50:35.272]    DbgMCU_APB1_Fz=0x00000000
[10:50:35.272]    DbgMCU_APB2_Fz=0x00000000
[10:50:35.273]    DoOptionByteLoading=0x00000000
[10:50:35.273]  </debugvars>
[10:50:35.273]  
[10:50:35.273]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:50:35.273]    <block atomic="false" info="">
[10:50:35.274]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:50:35.274]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:50:35.275]    </block>
[10:50:35.275]    <block atomic="false" info="DbgMCU registers">
[10:50:35.275]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:50:35.276]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[10:50:35.277]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[10:50:35.277]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:50:35.278]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:50:35.278]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:50:35.279]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:50:35.279]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:50:35.280]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:50:35.281]    </block>
[10:50:35.281]  </sequence>
[10:50:35.281]  
[10:50:43.532]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:50:43.532]  
[10:50:43.532]  <debugvars>
[10:50:43.532]    // Pre-defined
[10:50:43.532]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:50:43.532]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:50:43.537]    __dp=0x00000000
[10:50:43.537]    __ap=0x00000000
[10:50:43.537]    __traceout=0x00000000      (Trace Disabled)
[10:50:43.537]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:50:43.537]    __FlashAddr=0x00000000
[10:50:43.537]    __FlashLen=0x00000000
[10:50:43.537]    __FlashArg=0x00000000
[10:50:43.537]    __FlashOp=0x00000000
[10:50:43.540]    __Result=0x00000000
[10:50:43.540]    
[10:50:43.540]    // User-defined
[10:50:43.541]    DbgMCU_CR=0x00000007
[10:50:43.541]    DbgMCU_APB1_Fz=0x00000000
[10:50:43.541]    DbgMCU_APB2_Fz=0x00000000
[10:50:43.541]    DoOptionByteLoading=0x00000000
[10:50:43.541]  </debugvars>
[10:50:43.541]  
[10:50:43.542]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:50:43.542]    <block atomic="false" info="">
[10:50:43.542]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:50:43.542]        // -> [connectionFlash <= 0x00000001]
[10:50:43.542]      __var FLASH_BASE = 0x40022000 ;
[10:50:43.543]        // -> [FLASH_BASE <= 0x40022000]
[10:50:43.543]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:50:43.543]        // -> [FLASH_CR <= 0x40022004]
[10:50:43.543]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:50:43.543]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:50:43.544]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:50:43.544]        // -> [LOCK_BIT <= 0x00000001]
[10:50:43.544]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:50:43.544]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:50:43.544]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:50:43.544]        // -> [FLASH_KEYR <= 0x4002200C]
[10:50:43.545]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:50:43.545]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:50:43.545]      __var FLASH_KEY2 = 0x02030405 ;
[10:50:43.545]        // -> [FLASH_KEY2 <= 0x02030405]
[10:50:43.545]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:50:43.546]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:50:43.546]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:50:43.546]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:50:43.546]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:50:43.546]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:50:43.547]      __var FLASH_CR_Value = 0 ;
[10:50:43.547]        // -> [FLASH_CR_Value <= 0x00000000]
[10:50:43.547]      __var DoDebugPortStop = 1 ;
[10:50:43.547]        // -> [DoDebugPortStop <= 0x00000001]
[10:50:43.547]      __var DP_CTRL_STAT = 0x4 ;
[10:50:43.547]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:50:43.548]      __var DP_SELECT = 0x8 ;
[10:50:43.548]        // -> [DP_SELECT <= 0x00000008]
[10:50:43.548]    </block>
[10:50:43.548]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:50:43.548]      // if-block "connectionFlash && DoOptionByteLoading"
[10:50:43.549]        // =>  FALSE
[10:50:43.549]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:50:43.549]    </control>
[10:50:43.549]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:50:43.550]      // if-block "DoDebugPortStop"
[10:50:43.550]        // =>  TRUE
[10:50:43.550]      <block atomic="false" info="">
[10:50:43.551]        WriteDP(DP_SELECT, 0x00000000);
[10:50:43.551]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:50:43.552]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:50:43.552]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:50:43.553]      </block>
[10:50:43.553]      // end if-block "DoDebugPortStop"
[10:50:43.553]    </control>
[10:50:43.553]  </sequence>
[10:50:43.554]  
[11:52:03.686]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:52:03.686]  
[11:52:03.700]  <debugvars>
[11:52:03.700]    // Pre-defined
[11:52:03.700]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:52:03.700]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:52:03.700]    __dp=0x00000000
[11:52:03.701]    __ap=0x00000000
[11:52:03.701]    __traceout=0x00000000      (Trace Disabled)
[11:52:03.701]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:52:03.701]    __FlashAddr=0x00000000
[11:52:03.703]    __FlashLen=0x00000000
[11:52:03.703]    __FlashArg=0x00000000
[11:52:03.703]    __FlashOp=0x00000000
[11:52:03.703]    __Result=0x00000000
[11:52:03.704]    
[11:52:03.704]    // User-defined
[11:52:03.704]    DbgMCU_CR=0x00000007
[11:52:03.704]    DbgMCU_APB1_Fz=0x00000000
[11:52:03.704]    DbgMCU_APB2_Fz=0x00000000
[11:52:03.704]    DoOptionByteLoading=0x00000000
[11:52:03.704]  </debugvars>
[11:52:03.705]  
[11:52:03.705]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:52:03.705]    <block atomic="false" info="">
[11:52:03.705]      Sequence("CheckID");
[11:52:03.706]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:52:03.706]          <block atomic="false" info="">
[11:52:03.706]            __var pidr1 = 0;
[11:52:03.706]              // -> [pidr1 <= 0x00000000]
[11:52:03.707]            __var pidr2 = 0;
[11:52:03.707]              // -> [pidr2 <= 0x00000000]
[11:52:03.707]            __var jep106id = 0;
[11:52:03.708]              // -> [jep106id <= 0x00000000]
[11:52:03.708]            __var ROMTableBase = 0;
[11:52:03.708]              // -> [ROMTableBase <= 0x00000000]
[11:52:03.708]            __ap = 0;      // AHB-AP
[11:52:03.708]              // -> [__ap <= 0x00000000]
[11:52:03.708]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:52:03.714]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:52:03.714]              // -> [ROMTableBase <= 0xF0000000]
[11:52:03.714]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:52:03.723]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:52:03.723]              // -> [pidr1 <= 0x00000004]
[11:52:03.723]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:52:03.730]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:52:03.752]              // -> [pidr2 <= 0x0000000A]
[11:52:03.752]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:52:03.752]              // -> [jep106id <= 0x00000020]
[11:52:03.753]          </block>
[11:52:03.753]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:52:03.753]            // if-block "jep106id != 0x20"
[11:52:03.754]              // =>  FALSE
[11:52:03.754]            // skip if-block "jep106id != 0x20"
[11:52:03.754]          </control>
[11:52:03.754]        </sequence>
[11:52:03.755]    </block>
[11:52:03.755]  </sequence>
[11:52:03.755]  
[11:52:03.861]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:52:03.861]  
[11:52:03.862]  <debugvars>
[11:52:03.862]    // Pre-defined
[11:52:03.862]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:52:03.863]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:52:03.863]    __dp=0x00000000
[11:52:03.863]    __ap=0x00000000
[11:52:03.863]    __traceout=0x00000000      (Trace Disabled)
[11:52:03.864]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:52:03.864]    __FlashAddr=0x00000000
[11:52:03.864]    __FlashLen=0x00000000
[11:52:03.864]    __FlashArg=0x00000000
[11:52:03.865]    __FlashOp=0x00000000
[11:52:03.865]    __Result=0x00000000
[11:52:03.865]    
[11:52:03.865]    // User-defined
[11:52:03.865]    DbgMCU_CR=0x00000007
[11:52:03.866]    DbgMCU_APB1_Fz=0x00000000
[11:52:03.866]    DbgMCU_APB2_Fz=0x00000000
[11:52:03.866]    DoOptionByteLoading=0x00000000
[11:52:03.866]  </debugvars>
[11:52:03.866]  
[11:52:03.866]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:52:03.866]    <block atomic="false" info="">
[11:52:03.866]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:52:03.873]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:52:03.875]    </block>
[11:52:03.894]    <block atomic="false" info="DbgMCU registers">
[11:52:03.894]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:52:03.900]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[11:52:03.908]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[11:52:03.910]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:52:03.916]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:52:03.918]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:52:03.924]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:52:03.927]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:52:03.933]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:52:03.935]    </block>
[11:52:03.935]  </sequence>
[11:52:03.935]  
[11:52:22.522]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:52:22.522]  
[11:52:22.522]  <debugvars>
[11:52:22.523]    // Pre-defined
[11:52:22.523]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:52:22.523]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:52:22.525]    __dp=0x00000000
[11:52:22.525]    __ap=0x00000000
[11:52:22.525]    __traceout=0x00000000      (Trace Disabled)
[11:52:22.526]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:52:22.526]    __FlashAddr=0x00000000
[11:52:22.526]    __FlashLen=0x00000000
[11:52:22.527]    __FlashArg=0x00000000
[11:52:22.527]    __FlashOp=0x00000000
[11:52:22.527]    __Result=0x00000000
[11:52:22.527]    
[11:52:22.527]    // User-defined
[11:52:22.528]    DbgMCU_CR=0x00000007
[11:52:22.528]    DbgMCU_APB1_Fz=0x00000000
[11:52:22.528]    DbgMCU_APB2_Fz=0x00000000
[11:52:22.528]    DoOptionByteLoading=0x00000000
[11:52:22.528]  </debugvars>
[11:52:22.528]  
[11:52:22.528]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:52:22.528]    <block atomic="false" info="">
[11:52:22.528]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:52:22.530]        // -> [connectionFlash <= 0x00000001]
[11:52:22.530]      __var FLASH_BASE = 0x40022000 ;
[11:52:22.530]        // -> [FLASH_BASE <= 0x40022000]
[11:52:22.530]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:52:22.530]        // -> [FLASH_CR <= 0x40022004]
[11:52:22.531]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:52:22.531]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:52:22.531]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:52:22.531]        // -> [LOCK_BIT <= 0x00000001]
[11:52:22.532]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:52:22.532]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:52:22.532]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:52:22.532]        // -> [FLASH_KEYR <= 0x4002200C]
[11:52:22.532]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:52:22.532]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:52:22.533]      __var FLASH_KEY2 = 0x02030405 ;
[11:52:22.533]        // -> [FLASH_KEY2 <= 0x02030405]
[11:52:22.534]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:52:22.534]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:52:22.534]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:52:22.534]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:52:22.535]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:52:22.535]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:52:22.535]      __var FLASH_CR_Value = 0 ;
[11:52:22.535]        // -> [FLASH_CR_Value <= 0x00000000]
[11:52:22.535]      __var DoDebugPortStop = 1 ;
[11:52:22.536]        // -> [DoDebugPortStop <= 0x00000001]
[11:52:22.536]      __var DP_CTRL_STAT = 0x4 ;
[11:52:22.536]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:52:22.536]      __var DP_SELECT = 0x8 ;
[11:52:22.536]        // -> [DP_SELECT <= 0x00000008]
[11:52:22.537]    </block>
[11:52:22.537]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:52:22.537]      // if-block "connectionFlash && DoOptionByteLoading"
[11:52:22.537]        // =>  FALSE
[11:52:22.537]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:52:22.537]    </control>
[11:52:22.537]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:52:22.537]      // if-block "DoDebugPortStop"
[11:52:22.537]        // =>  TRUE
[11:52:22.538]      <block atomic="false" info="">
[11:52:22.538]        WriteDP(DP_SELECT, 0x00000000);
[11:52:22.558]  
[11:52:22.558]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:52:22.558]  
[11:52:22.561]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:52:22.561]      </block>
[11:52:22.561]      // end if-block "DoDebugPortStop"
[11:52:22.561]    </control>
[11:52:22.561]  </sequence>
[11:52:22.561]  
[11:52:25.110]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:52:25.110]  
[11:52:25.110]  <debugvars>
[11:52:25.110]    // Pre-defined
[11:52:25.111]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:52:25.111]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[11:52:25.112]    __dp=0x00000000
[11:52:25.112]    __ap=0x00000000
[11:52:25.112]    __traceout=0x00000000      (Trace Disabled)
[11:52:25.113]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:52:25.113]    __FlashAddr=0x00000000
[11:52:25.113]    __FlashLen=0x00000000
[11:52:25.114]    __FlashArg=0x00000000
[11:52:25.114]    __FlashOp=0x00000000
[11:52:25.115]    __Result=0x00000000
[11:52:25.115]    
[11:52:25.115]    // User-defined
[11:52:25.116]    DbgMCU_CR=0x00000007
[11:52:25.116]    DbgMCU_APB1_Fz=0x00000000
[11:52:25.116]    DbgMCU_APB2_Fz=0x00000000
[11:52:25.116]    DoOptionByteLoading=0x00000000
[11:52:25.117]  </debugvars>
[11:52:25.117]  
[11:52:25.117]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:52:25.117]    <block atomic="false" info="">
[11:52:25.118]      Sequence("CheckID");
[11:52:25.118]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:52:25.119]          <block atomic="false" info="">
[11:52:25.119]            __var pidr1 = 0;
[11:52:25.119]              // -> [pidr1 <= 0x00000000]
[11:52:25.119]            __var pidr2 = 0;
[11:52:25.119]              // -> [pidr2 <= 0x00000000]
[11:52:25.119]            __var jep106id = 0;
[11:52:25.120]              // -> [jep106id <= 0x00000000]
[11:52:25.120]            __var ROMTableBase = 0;
[11:52:25.120]              // -> [ROMTableBase <= 0x00000000]
[11:52:25.120]            __ap = 0;      // AHB-AP
[11:52:25.120]              // -> [__ap <= 0x00000000]
[11:52:25.120]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:52:25.125]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:52:25.126]              // -> [ROMTableBase <= 0xF0000000]
[11:52:25.135]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:52:25.143]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:52:25.144]              // -> [pidr1 <= 0x00000004]
[11:52:25.144]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:52:25.150]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:52:25.150]              // -> [pidr2 <= 0x0000000A]
[11:52:25.151]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:52:25.151]              // -> [jep106id <= 0x00000020]
[11:52:25.151]          </block>
[11:52:25.152]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:52:25.152]            // if-block "jep106id != 0x20"
[11:52:25.152]              // =>  FALSE
[11:52:25.152]            // skip if-block "jep106id != 0x20"
[11:52:25.152]          </control>
[11:52:25.154]        </sequence>
[11:52:25.154]    </block>
[11:52:25.154]  </sequence>
[11:52:25.154]  
[11:52:25.258]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:52:25.258]  
[11:52:25.260]  <debugvars>
[11:52:25.260]    // Pre-defined
[11:52:25.260]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:52:25.260]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[11:52:25.260]    __dp=0x00000000
[11:52:25.261]    __ap=0x00000000
[11:52:25.261]    __traceout=0x00000000      (Trace Disabled)
[11:52:25.261]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:52:25.261]    __FlashAddr=0x00000000
[11:52:25.263]    __FlashLen=0x00000000
[11:52:25.286]    __FlashArg=0x00000000
[11:52:25.286]    __FlashOp=0x00000000
[11:52:25.287]    __Result=0x00000000
[11:52:25.287]    
[11:52:25.287]    // User-defined
[11:52:25.287]    DbgMCU_CR=0x00000007
[11:52:25.288]    DbgMCU_APB1_Fz=0x00000000
[11:52:25.288]    DbgMCU_APB2_Fz=0x00000000
[11:52:25.288]    DoOptionByteLoading=0x00000000
[11:52:25.289]  </debugvars>
[11:52:25.289]  
[11:52:25.289]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:52:25.289]    <block atomic="false" info="">
[11:52:25.289]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:52:25.295]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:52:25.296]    </block>
[11:52:25.296]    <block atomic="false" info="DbgMCU registers">
[11:52:25.297]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:52:25.304]        // -> [Read32(0x40021034) => 0x00000200]   (__dp=0x00000000, __ap=0x00000000)
[11:52:25.311]        // -> [Write32(0x40021034, 0x00400200)]   (__dp=0x00000000, __ap=0x00000000)
[11:52:25.311]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:52:25.317]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:52:25.318]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:52:25.325]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:52:25.325]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:52:25.331]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:52:25.331]    </block>
[11:52:25.331]  </sequence>
[11:52:25.332]  
[11:55:36.528]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:55:36.528]  
[11:55:36.531]  <debugvars>
[11:55:36.532]    // Pre-defined
[11:55:36.532]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:55:36.532]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[11:55:36.532]    __dp=0x00000000
[11:55:36.532]    __ap=0x00000000
[11:55:36.533]    __traceout=0x00000000      (Trace Disabled)
[11:55:36.533]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:55:36.533]    __FlashAddr=0x00000000
[11:55:36.533]    __FlashLen=0x00000000
[11:55:36.534]    __FlashArg=0x00000000
[11:55:36.534]    __FlashOp=0x00000000
[11:55:36.534]    __Result=0x00000000
[11:55:36.534]    
[11:55:36.534]    // User-defined
[11:55:36.534]    DbgMCU_CR=0x00000007
[11:55:36.535]    DbgMCU_APB1_Fz=0x00000000
[11:55:36.535]    DbgMCU_APB2_Fz=0x00000000
[11:55:36.535]    DoOptionByteLoading=0x00000000
[11:55:36.536]  </debugvars>
[11:55:36.536]  
[11:55:36.536]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:55:36.536]    <block atomic="false" info="">
[11:55:36.537]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:55:36.537]        // -> [connectionFlash <= 0x00000000]
[11:55:36.537]      __var FLASH_BASE = 0x40022000 ;
[11:55:36.537]        // -> [FLASH_BASE <= 0x40022000]
[11:55:36.538]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:55:36.538]        // -> [FLASH_CR <= 0x40022004]
[11:55:36.538]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:55:36.538]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:55:36.538]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:55:36.539]        // -> [LOCK_BIT <= 0x00000001]
[11:55:36.539]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:55:36.539]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:55:36.539]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:55:36.539]        // -> [FLASH_KEYR <= 0x4002200C]
[11:55:36.540]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:55:36.540]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:55:36.540]      __var FLASH_KEY2 = 0x02030405 ;
[11:55:36.540]        // -> [FLASH_KEY2 <= 0x02030405]
[11:55:36.540]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:55:36.540]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:55:36.540]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:55:36.541]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:55:36.541]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:55:36.541]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:55:36.541]      __var FLASH_CR_Value = 0 ;
[11:55:36.543]        // -> [FLASH_CR_Value <= 0x00000000]
[11:55:36.543]      __var DoDebugPortStop = 1 ;
[11:55:36.543]        // -> [DoDebugPortStop <= 0x00000001]
[11:55:36.543]      __var DP_CTRL_STAT = 0x4 ;
[11:55:36.543]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:55:36.544]      __var DP_SELECT = 0x8 ;
[11:55:36.544]        // -> [DP_SELECT <= 0x00000008]
[11:55:36.544]    </block>
[11:55:36.544]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:55:36.544]      // if-block "connectionFlash && DoOptionByteLoading"
[11:55:36.545]        // =>  FALSE
[11:55:36.545]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:55:36.545]    </control>
[11:55:36.545]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:55:36.545]      // if-block "DoDebugPortStop"
[11:55:36.545]        // =>  TRUE
[11:55:36.546]      <block atomic="false" info="">
[11:55:36.546]        WriteDP(DP_SELECT, 0x00000000);
[11:55:36.547]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:55:36.548]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:55:36.551]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:55:36.552]      </block>
[11:55:36.552]      // end if-block "DoDebugPortStop"
[11:55:36.552]    </control>
[11:55:36.553]  </sequence>
[11:55:36.553]  
[11:55:43.793]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:55:43.793]  
[11:55:43.794]  <debugvars>
[11:55:43.794]    // Pre-defined
[11:55:43.794]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:55:43.795]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:55:43.795]    __dp=0x00000000
[11:55:43.795]    __ap=0x00000000
[11:55:43.795]    __traceout=0x00000000      (Trace Disabled)
[11:55:43.796]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:55:43.796]    __FlashAddr=0x00000000
[11:55:43.796]    __FlashLen=0x00000000
[11:55:43.797]    __FlashArg=0x00000000
[11:55:43.797]    __FlashOp=0x00000000
[11:55:43.797]    __Result=0x00000000
[11:55:43.797]    
[11:55:43.797]    // User-defined
[11:55:43.797]    DbgMCU_CR=0x00000007
[11:55:43.798]    DbgMCU_APB1_Fz=0x00000000
[11:55:43.798]    DbgMCU_APB2_Fz=0x00000000
[11:55:43.798]    DoOptionByteLoading=0x00000000
[11:55:43.798]  </debugvars>
[11:55:43.798]  
[11:55:43.798]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:55:43.799]    <block atomic="false" info="">
[11:55:43.799]      Sequence("CheckID");
[11:55:43.799]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:55:43.799]          <block atomic="false" info="">
[11:55:43.799]            __var pidr1 = 0;
[11:55:43.800]              // -> [pidr1 <= 0x00000000]
[11:55:43.801]            __var pidr2 = 0;
[11:55:43.801]              // -> [pidr2 <= 0x00000000]
[11:55:43.801]            __var jep106id = 0;
[11:55:43.801]              // -> [jep106id <= 0x00000000]
[11:55:43.802]            __var ROMTableBase = 0;
[11:55:43.802]              // -> [ROMTableBase <= 0x00000000]
[11:55:43.802]            __ap = 0;      // AHB-AP
[11:55:43.802]              // -> [__ap <= 0x00000000]
[11:55:43.802]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:55:43.806]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:55:43.806]              // -> [ROMTableBase <= 0xF0000000]
[11:55:43.806]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:55:43.814]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:55:43.842]              // -> [pidr1 <= 0x00000004]
[11:55:43.843]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:55:43.849]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:55:43.849]              // -> [pidr2 <= 0x0000000A]
[11:55:43.849]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:55:43.850]              // -> [jep106id <= 0x00000020]
[11:55:43.850]          </block>
[11:55:43.851]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:55:43.851]            // if-block "jep106id != 0x20"
[11:55:43.851]              // =>  FALSE
[11:55:43.852]            // skip if-block "jep106id != 0x20"
[11:55:43.852]          </control>
[11:55:43.852]        </sequence>
[11:55:43.852]    </block>
[11:55:43.853]  </sequence>
[11:55:43.853]  
[11:55:43.948]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:55:43.948]  
[11:55:43.948]  <debugvars>
[11:55:43.948]    // Pre-defined
[11:55:43.949]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:55:43.949]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:55:43.949]    __dp=0x00000000
[11:55:43.950]    __ap=0x00000000
[11:55:43.950]    __traceout=0x00000000      (Trace Disabled)
[11:55:43.950]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:55:43.950]    __FlashAddr=0x00000000
[11:55:43.952]    __FlashLen=0x00000000
[11:55:43.952]    __FlashArg=0x00000000
[11:55:43.952]    __FlashOp=0x00000000
[11:55:43.953]    __Result=0x00000000
[11:55:43.953]    
[11:55:43.953]    // User-defined
[11:55:43.953]    DbgMCU_CR=0x00000007
[11:55:43.953]    DbgMCU_APB1_Fz=0x00000000
[11:55:43.954]    DbgMCU_APB2_Fz=0x00000000
[11:55:43.954]    DoOptionByteLoading=0x00000000
[11:55:43.954]  </debugvars>
[11:55:43.954]  
[11:55:43.954]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:55:43.955]    <block atomic="false" info="">
[11:55:43.955]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:55:43.960]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:55:43.961]    </block>
[11:55:43.962]    <block atomic="false" info="DbgMCU registers">
[11:55:43.962]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:55:43.969]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:55:43.975]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:55:43.975]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:55:43.982]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:55:43.997]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:55:44.005]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:55:44.016]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:55:44.023]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:55:44.026]    </block>
[11:55:44.026]  </sequence>
[11:55:44.026]  
[11:56:02.218]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:56:02.218]  
[11:56:02.219]  <debugvars>
[11:56:02.219]    // Pre-defined
[11:56:02.220]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:56:02.220]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:56:02.220]    __dp=0x00000000
[11:56:02.221]    __ap=0x00000000
[11:56:02.221]    __traceout=0x00000000      (Trace Disabled)
[11:56:02.221]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:56:02.222]    __FlashAddr=0x00000000
[11:56:02.222]    __FlashLen=0x00000000
[11:56:02.222]    __FlashArg=0x00000000
[11:56:02.223]    __FlashOp=0x00000000
[11:56:02.223]    __Result=0x00000000
[11:56:02.223]    
[11:56:02.223]    // User-defined
[11:56:02.223]    DbgMCU_CR=0x00000007
[11:56:02.224]    DbgMCU_APB1_Fz=0x00000000
[11:56:02.224]    DbgMCU_APB2_Fz=0x00000000
[11:56:02.224]    DoOptionByteLoading=0x00000000
[11:56:02.224]  </debugvars>
[11:56:02.225]  
[11:56:02.225]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:56:02.225]    <block atomic="false" info="">
[11:56:02.225]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:56:02.226]        // -> [connectionFlash <= 0x00000001]
[11:56:02.227]      __var FLASH_BASE = 0x40022000 ;
[11:56:02.227]        // -> [FLASH_BASE <= 0x40022000]
[11:56:02.227]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:56:02.228]        // -> [FLASH_CR <= 0x40022004]
[11:56:02.228]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:56:02.228]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:56:02.229]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:56:02.229]        // -> [LOCK_BIT <= 0x00000001]
[11:56:02.229]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:56:02.230]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:56:02.230]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:56:02.231]        // -> [FLASH_KEYR <= 0x4002200C]
[11:56:02.231]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:56:02.231]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:56:02.231]      __var FLASH_KEY2 = 0x02030405 ;
[11:56:02.231]        // -> [FLASH_KEY2 <= 0x02030405]
[11:56:02.231]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:56:02.231]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:56:02.232]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:56:02.232]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:56:02.232]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:56:02.233]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:56:02.233]      __var FLASH_CR_Value = 0 ;
[11:56:02.233]        // -> [FLASH_CR_Value <= 0x00000000]
[11:56:02.233]      __var DoDebugPortStop = 1 ;
[11:56:02.233]        // -> [DoDebugPortStop <= 0x00000001]
[11:56:02.233]      __var DP_CTRL_STAT = 0x4 ;
[11:56:02.233]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:56:02.234]      __var DP_SELECT = 0x8 ;
[11:56:02.234]        // -> [DP_SELECT <= 0x00000008]
[11:56:02.235]    </block>
[11:56:02.235]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:56:02.235]      // if-block "connectionFlash && DoOptionByteLoading"
[11:56:02.235]        // =>  FALSE
[11:56:02.236]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:56:02.236]    </control>
[11:56:02.236]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:56:02.236]      // if-block "DoDebugPortStop"
[11:56:02.236]        // =>  TRUE
[11:56:02.236]      <block atomic="false" info="">
[11:56:02.237]        WriteDP(DP_SELECT, 0x00000000);
[11:56:02.253]  
[11:56:02.253]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:56:02.253]  
[11:56:02.286]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:56:02.287]      </block>
[11:56:02.288]      // end if-block "DoDebugPortStop"
[11:56:02.288]    </control>
[11:56:02.288]  </sequence>
[11:56:02.289]  
[12:01:43.010]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[12:01:43.011]  
[12:01:43.011]  <debugvars>
[12:01:43.011]    // Pre-defined
[12:01:43.011]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:01:43.012]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:01:43.012]    __dp=0x00000000
[12:01:43.012]    __ap=0x00000000
[12:01:43.012]    __traceout=0x00000000      (Trace Disabled)
[12:01:43.014]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:01:43.014]    __FlashAddr=0x00000000
[12:01:43.014]    __FlashLen=0x00000000
[12:01:43.014]    __FlashArg=0x00000000
[12:01:43.015]    __FlashOp=0x00000000
[12:01:43.015]    __Result=0x00000000
[12:01:43.015]    
[12:01:43.015]    // User-defined
[12:01:43.015]    DbgMCU_CR=0x00000007
[12:01:43.015]    DbgMCU_APB1_Fz=0x00000000
[12:01:43.015]    DbgMCU_APB2_Fz=0x00000000
[12:01:43.015]    DoOptionByteLoading=0x00000000
[12:01:43.016]  </debugvars>
[12:01:43.016]  
[12:01:43.016]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[12:01:43.016]    <block atomic="false" info="">
[12:01:43.016]      Sequence("CheckID");
[12:01:43.016]        <sequence name="CheckID" Pname="" disable="false" info="">
[12:01:43.017]          <block atomic="false" info="">
[12:01:43.017]            __var pidr1 = 0;
[12:01:43.017]              // -> [pidr1 <= 0x00000000]
[12:01:43.017]            __var pidr2 = 0;
[12:01:43.017]              // -> [pidr2 <= 0x00000000]
[12:01:43.018]            __var jep106id = 0;
[12:01:43.018]              // -> [jep106id <= 0x00000000]
[12:01:43.019]            __var ROMTableBase = 0;
[12:01:43.019]              // -> [ROMTableBase <= 0x00000000]
[12:01:43.020]            __ap = 0;      // AHB-AP
[12:01:43.020]              // -> [__ap <= 0x00000000]
[12:01:43.020]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[12:01:43.020]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[12:01:43.020]              // -> [ROMTableBase <= 0xF0000000]
[12:01:43.020]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[12:01:43.022]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[12:01:43.022]              // -> [pidr1 <= 0x00000004]
[12:01:43.023]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[12:01:43.023]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[12:01:43.023]              // -> [pidr2 <= 0x0000000A]
[12:01:43.024]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[12:01:43.024]              // -> [jep106id <= 0x00000020]
[12:01:43.024]          </block>
[12:01:43.024]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[12:01:43.025]            // if-block "jep106id != 0x20"
[12:01:43.025]              // =>  FALSE
[12:01:43.025]            // skip if-block "jep106id != 0x20"
[12:01:43.025]          </control>
[12:01:43.025]        </sequence>
[12:01:43.025]    </block>
[12:01:43.026]  </sequence>
[12:01:43.026]  
[12:01:43.038]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[12:01:43.038]  
[12:01:43.038]  <debugvars>
[12:01:43.039]    // Pre-defined
[12:01:43.039]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:01:43.039]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:01:43.039]    __dp=0x00000000
[12:01:43.040]    __ap=0x00000000
[12:01:43.040]    __traceout=0x00000000      (Trace Disabled)
[12:01:43.040]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:01:43.041]    __FlashAddr=0x00000000
[12:01:43.041]    __FlashLen=0x00000000
[12:01:43.041]    __FlashArg=0x00000000
[12:01:43.043]    __FlashOp=0x00000000
[12:01:43.043]    __Result=0x00000000
[12:01:43.043]    
[12:01:43.043]    // User-defined
[12:01:43.044]    DbgMCU_CR=0x00000007
[12:01:43.044]    DbgMCU_APB1_Fz=0x00000000
[12:01:43.044]    DbgMCU_APB2_Fz=0x00000000
[12:01:43.045]    DoOptionByteLoading=0x00000000
[12:01:43.045]  </debugvars>
[12:01:43.045]  
[12:01:43.045]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[12:01:43.046]    <block atomic="false" info="">
[12:01:43.046]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[12:01:43.047]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[12:01:43.047]    </block>
[12:01:43.048]    <block atomic="false" info="DbgMCU registers">
[12:01:43.048]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[12:01:43.049]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[12:01:43.050]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[12:01:43.050]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[12:01:43.051]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[12:01:43.051]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[12:01:43.052]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[12:01:43.052]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[12:01:43.053]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[12:01:43.053]    </block>
[12:01:43.054]  </sequence>
[12:01:43.054]  
[12:01:51.341]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[12:01:51.341]  
[12:01:51.341]  <debugvars>
[12:01:51.342]    // Pre-defined
[12:01:51.342]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:01:51.343]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:01:51.343]    __dp=0x00000000
[12:01:51.344]    __ap=0x00000000
[12:01:51.344]    __traceout=0x00000000      (Trace Disabled)
[12:01:51.345]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:01:51.345]    __FlashAddr=0x00000000
[12:01:51.345]    __FlashLen=0x00000000
[12:01:51.346]    __FlashArg=0x00000000
[12:01:51.346]    __FlashOp=0x00000000
[12:01:51.346]    __Result=0x00000000
[12:01:51.346]    
[12:01:51.346]    // User-defined
[12:01:51.346]    DbgMCU_CR=0x00000007
[12:01:51.346]    DbgMCU_APB1_Fz=0x00000000
[12:01:51.348]    DbgMCU_APB2_Fz=0x00000000
[12:01:51.348]    DoOptionByteLoading=0x00000000
[12:01:51.348]  </debugvars>
[12:01:51.348]  
[12:01:51.348]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[12:01:51.349]    <block atomic="false" info="">
[12:01:51.349]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[12:01:51.349]        // -> [connectionFlash <= 0x00000001]
[12:01:51.349]      __var FLASH_BASE = 0x40022000 ;
[12:01:51.349]        // -> [FLASH_BASE <= 0x40022000]
[12:01:51.349]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[12:01:51.350]        // -> [FLASH_CR <= 0x40022004]
[12:01:51.350]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[12:01:51.350]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[12:01:51.350]      __var LOCK_BIT = ( 1 << 0 ) ;
[12:01:51.350]        // -> [LOCK_BIT <= 0x00000001]
[12:01:51.351]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[12:01:51.351]        // -> [OPTLOCK_BIT <= 0x00000004]
[12:01:51.351]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[12:01:51.351]        // -> [FLASH_KEYR <= 0x4002200C]
[12:01:51.352]      __var FLASH_KEY1 = 0x89ABCDEF ;
[12:01:51.352]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[12:01:51.352]      __var FLASH_KEY2 = 0x02030405 ;
[12:01:51.353]        // -> [FLASH_KEY2 <= 0x02030405]
[12:01:51.353]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[12:01:51.353]        // -> [FLASH_OPTKEYR <= 0x40022014]
[12:01:51.354]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[12:01:51.354]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[12:01:51.354]      __var FLASH_OPTKEY2 = 0x24252627 ;
[12:01:51.355]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[12:01:51.355]      __var FLASH_CR_Value = 0 ;
[12:01:51.355]        // -> [FLASH_CR_Value <= 0x00000000]
[12:01:51.355]      __var DoDebugPortStop = 1 ;
[12:01:51.356]        // -> [DoDebugPortStop <= 0x00000001]
[12:01:51.356]      __var DP_CTRL_STAT = 0x4 ;
[12:01:51.356]        // -> [DP_CTRL_STAT <= 0x00000004]
[12:01:51.356]      __var DP_SELECT = 0x8 ;
[12:01:51.356]        // -> [DP_SELECT <= 0x00000008]
[12:01:51.356]    </block>
[12:01:51.357]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[12:01:51.357]      // if-block "connectionFlash && DoOptionByteLoading"
[12:01:51.357]        // =>  FALSE
[12:01:51.357]      // skip if-block "connectionFlash && DoOptionByteLoading"
[12:01:51.358]    </control>
[12:01:51.358]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[12:01:51.358]      // if-block "DoDebugPortStop"
[12:01:51.359]        // =>  TRUE
[12:01:51.359]      <block atomic="false" info="">
[12:01:51.359]        WriteDP(DP_SELECT, 0x00000000);
[12:01:51.359]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[12:01:51.360]        WriteDP(DP_CTRL_STAT, 0x00000000);
[12:01:51.360]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[12:01:51.361]      </block>
[12:01:51.361]      // end if-block "DoDebugPortStop"
[12:01:51.361]    </control>
[12:01:51.361]  </sequence>
[12:01:51.361]  
[13:35:45.803]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[13:35:45.803]  
[13:35:45.814]  <debugvars>
[13:35:45.815]    // Pre-defined
[13:35:45.815]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:35:45.815]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:35:45.816]    __dp=0x00000000
[13:35:45.816]    __ap=0x00000000
[13:35:45.816]    __traceout=0x00000000      (Trace Disabled)
[13:35:45.817]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:35:45.817]    __FlashAddr=0x00000000
[13:35:45.817]    __FlashLen=0x00000000
[13:35:45.817]    __FlashArg=0x00000000
[13:35:45.817]    __FlashOp=0x00000000
[13:35:45.817]    __Result=0x00000000
[13:35:45.819]    
[13:35:45.819]    // User-defined
[13:35:45.819]    DbgMCU_CR=0x00000007
[13:35:45.819]    DbgMCU_APB1_Fz=0x00000000
[13:35:45.819]    DbgMCU_APB2_Fz=0x00000000
[13:35:45.820]    DoOptionByteLoading=0x00000000
[13:35:45.820]  </debugvars>
[13:35:45.820]  
[13:35:45.821]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[13:35:45.821]    <block atomic="false" info="">
[13:35:45.821]      Sequence("CheckID");
[13:35:45.821]        <sequence name="CheckID" Pname="" disable="false" info="">
[13:35:45.822]          <block atomic="false" info="">
[13:35:45.822]            __var pidr1 = 0;
[13:35:45.822]              // -> [pidr1 <= 0x00000000]
[13:35:45.822]            __var pidr2 = 0;
[13:35:45.822]              // -> [pidr2 <= 0x00000000]
[13:35:45.823]            __var jep106id = 0;
[13:35:45.823]              // -> [jep106id <= 0x00000000]
[13:35:45.823]            __var ROMTableBase = 0;
[13:35:45.823]              // -> [ROMTableBase <= 0x00000000]
[13:35:45.823]            __ap = 0;      // AHB-AP
[13:35:45.823]              // -> [__ap <= 0x00000000]
[13:35:45.824]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[13:35:45.824]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[13:35:45.826]              // -> [ROMTableBase <= 0xF0000000]
[13:35:45.826]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[13:35:45.827]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[13:35:45.828]              // -> [pidr1 <= 0x00000004]
[13:35:45.828]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[13:35:45.829]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[13:35:45.829]              // -> [pidr2 <= 0x0000000A]
[13:35:45.829]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[13:35:45.830]              // -> [jep106id <= 0x00000020]
[13:35:45.830]          </block>
[13:35:45.830]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[13:35:45.830]            // if-block "jep106id != 0x20"
[13:35:45.830]              // =>  FALSE
[13:35:45.831]            // skip if-block "jep106id != 0x20"
[13:35:45.831]          </control>
[13:35:45.831]        </sequence>
[13:35:45.831]    </block>
[13:35:45.831]  </sequence>
[13:35:45.831]  
[13:35:45.846]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[13:35:45.846]  
[13:35:45.874]  <debugvars>
[13:35:45.874]    // Pre-defined
[13:35:45.875]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:35:45.876]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:35:45.876]    __dp=0x00000000
[13:35:45.877]    __ap=0x00000000
[13:35:45.877]    __traceout=0x00000000      (Trace Disabled)
[13:35:45.878]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:35:45.878]    __FlashAddr=0x00000000
[13:35:45.879]    __FlashLen=0x00000000
[13:35:45.879]    __FlashArg=0x00000000
[13:35:45.879]    __FlashOp=0x00000000
[13:35:45.880]    __Result=0x00000000
[13:35:45.880]    
[13:35:45.880]    // User-defined
[13:35:45.881]    DbgMCU_CR=0x00000007
[13:35:45.882]    DbgMCU_APB1_Fz=0x00000000
[13:35:45.882]    DbgMCU_APB2_Fz=0x00000000
[13:35:45.883]    DoOptionByteLoading=0x00000000
[13:35:45.883]  </debugvars>
[13:35:45.884]  
[13:35:45.884]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[13:35:45.885]    <block atomic="false" info="">
[13:35:45.885]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[13:35:45.887]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[13:35:45.887]    </block>
[13:35:45.888]    <block atomic="false" info="DbgMCU registers">
[13:35:45.888]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[13:35:45.890]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[13:35:45.892]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[13:35:45.892]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[13:35:45.894]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[13:35:45.894]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[13:35:45.894]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:35:45.895]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[13:35:45.897]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:35:45.897]    </block>
[13:35:45.897]  </sequence>
[13:35:45.897]  
[13:35:53.769]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[13:35:53.769]  
[13:35:53.771]  <debugvars>
[13:35:53.772]    // Pre-defined
[13:35:53.772]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:35:53.772]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:35:53.773]    __dp=0x00000000
[13:35:53.773]    __ap=0x00000000
[13:35:53.774]    __traceout=0x00000000      (Trace Disabled)
[13:35:53.774]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:35:53.775]    __FlashAddr=0x00000000
[13:35:53.775]    __FlashLen=0x00000000
[13:35:53.776]    __FlashArg=0x00000000
[13:35:53.776]    __FlashOp=0x00000000
[13:35:53.777]    __Result=0x00000000
[13:35:53.777]    
[13:35:53.777]    // User-defined
[13:35:53.777]    DbgMCU_CR=0x00000007
[13:35:53.777]    DbgMCU_APB1_Fz=0x00000000
[13:35:53.778]    DbgMCU_APB2_Fz=0x00000000
[13:35:53.778]    DoOptionByteLoading=0x00000000
[13:35:53.778]  </debugvars>
[13:35:53.779]  
[13:35:53.779]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[13:35:53.779]    <block atomic="false" info="">
[13:35:53.779]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[13:35:53.779]        // -> [connectionFlash <= 0x00000001]
[13:35:53.780]      __var FLASH_BASE = 0x40022000 ;
[13:35:53.780]        // -> [FLASH_BASE <= 0x40022000]
[13:35:53.780]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[13:35:53.780]        // -> [FLASH_CR <= 0x40022004]
[13:35:53.780]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[13:35:53.781]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[13:35:53.781]      __var LOCK_BIT = ( 1 << 0 ) ;
[13:35:53.781]        // -> [LOCK_BIT <= 0x00000001]
[13:35:53.781]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[13:35:53.781]        // -> [OPTLOCK_BIT <= 0x00000004]
[13:35:53.781]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[13:35:53.781]        // -> [FLASH_KEYR <= 0x4002200C]
[13:35:53.782]      __var FLASH_KEY1 = 0x89ABCDEF ;
[13:35:53.782]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[13:35:53.782]      __var FLASH_KEY2 = 0x02030405 ;
[13:35:53.782]        // -> [FLASH_KEY2 <= 0x02030405]
[13:35:53.783]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[13:35:53.783]        // -> [FLASH_OPTKEYR <= 0x40022014]
[13:35:53.784]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[13:35:53.784]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[13:35:53.784]      __var FLASH_OPTKEY2 = 0x24252627 ;
[13:35:53.785]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[13:35:53.785]      __var FLASH_CR_Value = 0 ;
[13:35:53.785]        // -> [FLASH_CR_Value <= 0x00000000]
[13:35:53.785]      __var DoDebugPortStop = 1 ;
[13:35:53.785]        // -> [DoDebugPortStop <= 0x00000001]
[13:35:53.786]      __var DP_CTRL_STAT = 0x4 ;
[13:35:53.786]        // -> [DP_CTRL_STAT <= 0x00000004]
[13:35:53.787]      __var DP_SELECT = 0x8 ;
[13:35:53.787]        // -> [DP_SELECT <= 0x00000008]
[13:35:53.787]    </block>
[13:35:53.787]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[13:35:53.788]      // if-block "connectionFlash && DoOptionByteLoading"
[13:35:53.788]        // =>  FALSE
[13:35:53.788]      // skip if-block "connectionFlash && DoOptionByteLoading"
[13:35:53.788]    </control>
[13:35:53.789]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[13:35:53.789]      // if-block "DoDebugPortStop"
[13:35:53.789]        // =>  TRUE
[13:35:53.789]      <block atomic="false" info="">
[13:35:53.789]        WriteDP(DP_SELECT, 0x00000000);
[13:35:53.790]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[13:35:53.790]        WriteDP(DP_CTRL_STAT, 0x00000000);
[13:35:53.791]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[13:35:53.791]      </block>
[13:35:53.791]      // end if-block "DoDebugPortStop"
[13:35:53.791]    </control>
[13:35:53.791]  </sequence>
[13:35:53.792]  
[13:40:26.116]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[13:40:26.116]  
[13:40:26.116]  <debugvars>
[13:40:26.116]    // Pre-defined
[13:40:26.116]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:40:26.116]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[13:40:26.116]    __dp=0x00000000
[13:40:26.117]    __ap=0x00000000
[13:40:26.118]    __traceout=0x00000000      (Trace Disabled)
[13:40:26.118]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:40:26.118]    __FlashAddr=0x00000000
[13:40:26.119]    __FlashLen=0x00000000
[13:40:26.119]    __FlashArg=0x00000000
[13:40:26.119]    __FlashOp=0x00000000
[13:40:26.119]    __Result=0x00000000
[13:40:26.120]    
[13:40:26.120]    // User-defined
[13:40:26.120]    DbgMCU_CR=0x00000007
[13:40:26.121]    DbgMCU_APB1_Fz=0x00000000
[13:40:26.121]    DbgMCU_APB2_Fz=0x00000000
[13:40:26.121]    DoOptionByteLoading=0x00000000
[13:40:26.121]  </debugvars>
[13:40:26.122]  
[13:40:26.122]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[13:40:26.122]    <block atomic="false" info="">
[13:40:26.122]      Sequence("CheckID");
[13:40:26.122]        <sequence name="CheckID" Pname="" disable="false" info="">
[13:40:26.123]          <block atomic="false" info="">
[13:40:26.123]            __var pidr1 = 0;
[13:40:26.123]              // -> [pidr1 <= 0x00000000]
[13:40:26.123]            __var pidr2 = 0;
[13:40:26.123]              // -> [pidr2 <= 0x00000000]
[13:40:26.124]            __var jep106id = 0;
[13:40:26.124]              // -> [jep106id <= 0x00000000]
[13:40:26.124]            __var ROMTableBase = 0;
[13:40:26.124]              // -> [ROMTableBase <= 0x00000000]
[13:40:26.124]            __ap = 0;      // AHB-AP
[13:40:26.125]              // -> [__ap <= 0x00000000]
[13:40:26.125]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[13:40:26.125]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[13:40:26.126]              // -> [ROMTableBase <= 0xF0000000]
[13:40:26.126]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[13:40:26.126]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[13:40:26.128]              // -> [pidr1 <= 0x00000004]
[13:40:26.128]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[13:40:26.129]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[13:40:26.129]              // -> [pidr2 <= 0x0000000A]
[13:40:26.129]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[13:40:26.130]              // -> [jep106id <= 0x00000020]
[13:40:26.130]          </block>
[13:40:26.130]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[13:40:26.130]            // if-block "jep106id != 0x20"
[13:40:26.131]              // =>  FALSE
[13:40:26.131]            // skip if-block "jep106id != 0x20"
[13:40:26.131]          </control>
[13:40:26.131]        </sequence>
[13:40:26.131]    </block>
[13:40:26.131]  </sequence>
[13:40:26.132]  
[13:40:26.144]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[13:40:26.144]  
[13:40:26.155]  <debugvars>
[13:40:26.156]    // Pre-defined
[13:40:26.156]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:40:26.157]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[13:40:26.158]    __dp=0x00000000
[13:40:26.159]    __ap=0x00000000
[13:40:26.159]    __traceout=0x00000000      (Trace Disabled)
[13:40:26.160]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:40:26.161]    __FlashAddr=0x00000000
[13:40:26.162]    __FlashLen=0x00000000
[13:40:26.162]    __FlashArg=0x00000000
[13:40:26.163]    __FlashOp=0x00000000
[13:40:26.163]    __Result=0x00000000
[13:40:26.164]    
[13:40:26.164]    // User-defined
[13:40:26.165]    DbgMCU_CR=0x00000007
[13:40:26.165]    DbgMCU_APB1_Fz=0x00000000
[13:40:26.166]    DbgMCU_APB2_Fz=0x00000000
[13:40:26.167]    DoOptionByteLoading=0x00000000
[13:40:26.168]  </debugvars>
[13:40:26.168]  
[13:40:26.168]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[13:40:26.169]    <block atomic="false" info="">
[13:40:26.170]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[13:40:26.173]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[13:40:26.173]    </block>
[13:40:26.174]    <block atomic="false" info="DbgMCU registers">
[13:40:26.174]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[13:40:26.176]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[13:40:26.177]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[13:40:26.178]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[13:40:26.179]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[13:40:26.179]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[13:40:26.181]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:40:26.182]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[13:40:26.183]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:40:26.184]    </block>
[13:40:26.184]  </sequence>
[13:40:26.184]  
[14:00:16.040]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:00:16.040]  
[14:00:16.040]  <debugvars>
[14:00:16.041]    // Pre-defined
[14:00:16.041]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:00:16.042]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[14:00:16.042]    __dp=0x00000000
[14:00:16.043]    __ap=0x00000000
[14:00:16.043]    __traceout=0x00000000      (Trace Disabled)
[14:00:16.043]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:00:16.044]    __FlashAddr=0x00000000
[14:00:16.044]    __FlashLen=0x00000000
[14:00:16.044]    __FlashArg=0x00000000
[14:00:16.045]    __FlashOp=0x00000000
[14:00:16.045]    __Result=0x00000000
[14:00:16.045]    
[14:00:16.045]    // User-defined
[14:00:16.045]    DbgMCU_CR=0x00000007
[14:00:16.045]    DbgMCU_APB1_Fz=0x00000000
[14:00:16.045]    DbgMCU_APB2_Fz=0x00000000
[14:00:16.046]    DoOptionByteLoading=0x00000000
[14:00:16.046]  </debugvars>
[14:00:16.046]  
[14:00:16.046]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:00:16.047]    <block atomic="false" info="">
[14:00:16.047]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:00:16.047]        // -> [connectionFlash <= 0x00000000]
[14:00:16.048]      __var FLASH_BASE = 0x40022000 ;
[14:00:16.048]        // -> [FLASH_BASE <= 0x40022000]
[14:00:16.049]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:00:16.049]        // -> [FLASH_CR <= 0x40022004]
[14:00:16.049]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:00:16.049]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:00:16.050]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:00:16.050]        // -> [LOCK_BIT <= 0x00000001]
[14:00:16.050]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:00:16.050]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:00:16.050]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:00:16.051]        // -> [FLASH_KEYR <= 0x4002200C]
[14:00:16.051]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:00:16.051]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:00:16.051]      __var FLASH_KEY2 = 0x02030405 ;
[14:00:16.051]        // -> [FLASH_KEY2 <= 0x02030405]
[14:00:16.052]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:00:16.052]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:00:16.052]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:00:16.052]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:00:16.052]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:00:16.052]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:00:16.053]      __var FLASH_CR_Value = 0 ;
[14:00:16.053]        // -> [FLASH_CR_Value <= 0x00000000]
[14:00:16.053]      __var DoDebugPortStop = 1 ;
[14:00:16.053]        // -> [DoDebugPortStop <= 0x00000001]
[14:00:16.053]      __var DP_CTRL_STAT = 0x4 ;
[14:00:16.054]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:00:16.054]      __var DP_SELECT = 0x8 ;
[14:00:16.054]        // -> [DP_SELECT <= 0x00000008]
[14:00:16.054]    </block>
[14:00:16.054]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:00:16.054]      // if-block "connectionFlash && DoOptionByteLoading"
[14:00:16.054]        // =>  FALSE
[14:00:16.055]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:00:16.056]    </control>
[14:00:16.056]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:00:16.056]      // if-block "DoDebugPortStop"
[14:00:16.057]        // =>  TRUE
[14:00:16.057]      <block atomic="false" info="">
[14:00:16.057]        WriteDP(DP_SELECT, 0x00000000);
[14:00:16.058]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:00:16.058]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:00:16.058]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:00:16.058]      </block>
[14:00:16.059]      // end if-block "DoDebugPortStop"
[14:00:16.059]    </control>
[14:00:16.060]  </sequence>
[14:00:16.060]  
[14:04:01.054]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:04:01.054]  
[14:04:01.054]  <debugvars>
[14:04:01.054]    // Pre-defined
[14:04:01.054]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:04:01.055]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:04:01.055]    __dp=0x00000000
[14:04:01.056]    __ap=0x00000000
[14:04:01.056]    __traceout=0x00000000      (Trace Disabled)
[14:04:01.056]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:04:01.056]    __FlashAddr=0x00000000
[14:04:01.057]    __FlashLen=0x00000000
[14:04:01.057]    __FlashArg=0x00000000
[14:04:01.057]    __FlashOp=0x00000000
[14:04:01.058]    __Result=0x00000000
[14:04:01.058]    
[14:04:01.058]    // User-defined
[14:04:01.058]    DbgMCU_CR=0x00000007
[14:04:01.058]    DbgMCU_APB1_Fz=0x00000000
[14:04:01.059]    DbgMCU_APB2_Fz=0x00000000
[14:04:01.059]    DoOptionByteLoading=0x00000000
[14:04:01.059]  </debugvars>
[14:04:01.059]  
[14:04:01.059]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:04:01.059]    <block atomic="false" info="">
[14:04:01.060]      Sequence("CheckID");
[14:04:01.060]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:04:01.060]          <block atomic="false" info="">
[14:04:01.060]            __var pidr1 = 0;
[14:04:01.060]              // -> [pidr1 <= 0x00000000]
[14:04:01.061]            __var pidr2 = 0;
[14:04:01.061]              // -> [pidr2 <= 0x00000000]
[14:04:01.061]            __var jep106id = 0;
[14:04:01.062]              // -> [jep106id <= 0x00000000]
[14:04:01.062]            __var ROMTableBase = 0;
[14:04:01.062]              // -> [ROMTableBase <= 0x00000000]
[14:04:01.062]            __ap = 0;      // AHB-AP
[14:04:01.062]              // -> [__ap <= 0x00000000]
[14:04:01.063]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:04:01.063]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:04:01.064]              // -> [ROMTableBase <= 0xF0000000]
[14:04:01.064]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:04:01.064]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:04:01.065]              // -> [pidr1 <= 0x00000004]
[14:04:01.065]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:04:01.067]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:04:01.067]              // -> [pidr2 <= 0x0000000A]
[14:04:01.067]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:04:01.068]              // -> [jep106id <= 0x00000020]
[14:04:01.068]          </block>
[14:04:01.068]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:04:01.069]            // if-block "jep106id != 0x20"
[14:04:01.069]              // =>  FALSE
[14:04:01.069]            // skip if-block "jep106id != 0x20"
[14:04:01.070]          </control>
[14:04:01.070]        </sequence>
[14:04:01.070]    </block>
[14:04:01.070]  </sequence>
[14:04:01.070]  
[14:04:01.083]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:04:01.083]  
[14:04:01.083]  <debugvars>
[14:04:01.084]    // Pre-defined
[14:04:01.084]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:04:01.084]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:04:01.084]    __dp=0x00000000
[14:04:01.085]    __ap=0x00000000
[14:04:01.085]    __traceout=0x00000000      (Trace Disabled)
[14:04:01.085]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:04:01.086]    __FlashAddr=0x00000000
[14:04:01.086]    __FlashLen=0x00000000
[14:04:01.086]    __FlashArg=0x00000000
[14:04:01.086]    __FlashOp=0x00000000
[14:04:01.087]    __Result=0x00000000
[14:04:01.087]    
[14:04:01.087]    // User-defined
[14:04:01.087]    DbgMCU_CR=0x00000007
[14:04:01.087]    DbgMCU_APB1_Fz=0x00000000
[14:04:01.087]    DbgMCU_APB2_Fz=0x00000000
[14:04:01.087]    DoOptionByteLoading=0x00000000
[14:04:01.087]  </debugvars>
[14:04:01.088]  
[14:04:01.088]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:04:01.089]    <block atomic="false" info="">
[14:04:01.089]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:04:01.090]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:04:01.090]    </block>
[14:04:01.090]    <block atomic="false" info="DbgMCU registers">
[14:04:01.091]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:04:01.092]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[14:04:01.093]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[14:04:01.093]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:04:01.094]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:04:01.094]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:04:01.094]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:04:01.094]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:04:01.096]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:04:01.096]    </block>
[14:04:01.096]  </sequence>
[14:04:01.097]  
[14:04:08.952]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:04:08.952]  
[14:04:08.953]  <debugvars>
[14:04:08.953]    // Pre-defined
[14:04:08.954]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:04:08.954]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:04:08.954]    __dp=0x00000000
[14:04:08.954]    __ap=0x00000000
[14:04:08.955]    __traceout=0x00000000      (Trace Disabled)
[14:04:08.956]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:04:08.956]    __FlashAddr=0x00000000
[14:04:08.956]    __FlashLen=0x00000000
[14:04:08.957]    __FlashArg=0x00000000
[14:04:08.957]    __FlashOp=0x00000000
[14:04:08.957]    __Result=0x00000000
[14:04:08.957]    
[14:04:08.957]    // User-defined
[14:04:08.959]    DbgMCU_CR=0x00000007
[14:04:08.959]    DbgMCU_APB1_Fz=0x00000000
[14:04:08.959]    DbgMCU_APB2_Fz=0x00000000
[14:04:08.960]    DoOptionByteLoading=0x00000000
[14:04:08.960]  </debugvars>
[14:04:08.960]  
[14:04:08.960]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:04:08.960]    <block atomic="false" info="">
[14:04:08.961]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:04:08.961]        // -> [connectionFlash <= 0x00000001]
[14:04:08.961]      __var FLASH_BASE = 0x40022000 ;
[14:04:08.961]        // -> [FLASH_BASE <= 0x40022000]
[14:04:08.961]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:04:08.961]        // -> [FLASH_CR <= 0x40022004]
[14:04:08.962]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:04:08.962]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:04:08.962]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:04:08.962]        // -> [LOCK_BIT <= 0x00000001]
[14:04:08.962]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:04:08.963]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:04:08.963]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:04:08.963]        // -> [FLASH_KEYR <= 0x4002200C]
[14:04:08.963]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:04:08.963]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:04:08.963]      __var FLASH_KEY2 = 0x02030405 ;
[14:04:08.964]        // -> [FLASH_KEY2 <= 0x02030405]
[14:04:08.964]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:04:08.964]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:04:08.964]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:04:08.965]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:04:08.965]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:04:08.965]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:04:08.965]      __var FLASH_CR_Value = 0 ;
[14:04:08.965]        // -> [FLASH_CR_Value <= 0x00000000]
[14:04:08.966]      __var DoDebugPortStop = 1 ;
[14:04:08.966]        // -> [DoDebugPortStop <= 0x00000001]
[14:04:08.966]      __var DP_CTRL_STAT = 0x4 ;
[14:04:08.966]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:04:08.966]      __var DP_SELECT = 0x8 ;
[14:04:08.966]        // -> [DP_SELECT <= 0x00000008]
[14:04:08.967]    </block>
[14:04:08.967]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:04:08.967]      // if-block "connectionFlash && DoOptionByteLoading"
[14:04:08.967]        // =>  FALSE
[14:04:08.967]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:04:08.967]    </control>
[14:04:08.967]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:04:08.967]      // if-block "DoDebugPortStop"
[14:04:08.967]        // =>  TRUE
[14:04:08.968]      <block atomic="false" info="">
[14:04:08.969]        WriteDP(DP_SELECT, 0x00000000);
[14:04:08.969]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:04:08.970]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:04:08.970]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:04:08.970]      </block>
[14:04:08.970]      // end if-block "DoDebugPortStop"
[14:04:08.971]    </control>
[14:04:08.972]  </sequence>
[14:04:08.973]  
[14:06:32.941]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:06:32.941]  
[14:06:32.941]  <debugvars>
[14:06:32.942]    // Pre-defined
[14:06:32.942]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:06:32.942]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:06:32.942]    __dp=0x00000000
[14:06:32.943]    __ap=0x00000000
[14:06:32.943]    __traceout=0x00000000      (Trace Disabled)
[14:06:32.943]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:06:32.943]    __FlashAddr=0x00000000
[14:06:32.944]    __FlashLen=0x00000000
[14:06:32.944]    __FlashArg=0x00000000
[14:06:32.944]    __FlashOp=0x00000000
[14:06:32.944]    __Result=0x00000000
[14:06:32.944]    
[14:06:32.944]    // User-defined
[14:06:32.945]    DbgMCU_CR=0x00000007
[14:06:32.945]    DbgMCU_APB1_Fz=0x00000000
[14:06:32.945]    DbgMCU_APB2_Fz=0x00000000
[14:06:32.945]    DoOptionByteLoading=0x00000000
[14:06:32.945]  </debugvars>
[14:06:32.946]  
[14:06:32.946]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:06:32.946]    <block atomic="false" info="">
[14:06:32.946]      Sequence("CheckID");
[14:06:32.946]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:06:32.946]          <block atomic="false" info="">
[14:06:32.947]            __var pidr1 = 0;
[14:06:32.947]              // -> [pidr1 <= 0x00000000]
[14:06:32.947]            __var pidr2 = 0;
[14:06:32.947]              // -> [pidr2 <= 0x00000000]
[14:06:32.947]            __var jep106id = 0;
[14:06:32.947]              // -> [jep106id <= 0x00000000]
[14:06:32.949]            __var ROMTableBase = 0;
[14:06:32.949]              // -> [ROMTableBase <= 0x00000000]
[14:06:32.949]            __ap = 0;      // AHB-AP
[14:06:32.949]              // -> [__ap <= 0x00000000]
[14:06:32.949]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:06:32.950]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:06:32.950]              // -> [ROMTableBase <= 0xF0000000]
[14:06:32.951]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:06:32.952]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:06:32.952]              // -> [pidr1 <= 0x00000004]
[14:06:32.952]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:06:32.953]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:06:32.953]              // -> [pidr2 <= 0x0000000A]
[14:06:32.954]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:06:32.954]              // -> [jep106id <= 0x00000020]
[14:06:32.954]          </block>
[14:06:32.955]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:06:32.955]            // if-block "jep106id != 0x20"
[14:06:32.955]              // =>  FALSE
[14:06:32.955]            // skip if-block "jep106id != 0x20"
[14:06:32.956]          </control>
[14:06:32.956]        </sequence>
[14:06:32.956]    </block>
[14:06:32.956]  </sequence>
[14:06:32.956]  
[14:06:32.971]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:06:32.971]  
[14:06:32.971]  <debugvars>
[14:06:32.971]    // Pre-defined
[14:06:32.971]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:06:32.971]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:06:32.971]    __dp=0x00000000
[14:06:32.971]    __ap=0x00000000
[14:06:32.972]    __traceout=0x00000000      (Trace Disabled)
[14:06:32.972]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:06:32.972]    __FlashAddr=0x00000000
[14:06:32.972]    __FlashLen=0x00000000
[14:06:32.973]    __FlashArg=0x00000000
[14:06:32.973]    __FlashOp=0x00000000
[14:06:32.973]    __Result=0x00000000
[14:06:32.973]    
[14:06:32.973]    // User-defined
[14:06:32.974]    DbgMCU_CR=0x00000007
[14:06:32.974]    DbgMCU_APB1_Fz=0x00000000
[14:06:32.974]    DbgMCU_APB2_Fz=0x00000000
[14:06:32.974]    DoOptionByteLoading=0x00000000
[14:06:32.974]  </debugvars>
[14:06:32.975]  
[14:06:32.975]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:06:32.975]    <block atomic="false" info="">
[14:06:32.975]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:06:32.976]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:06:32.976]    </block>
[14:06:32.977]    <block atomic="false" info="DbgMCU registers">
[14:06:32.977]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:06:32.979]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[14:06:32.980]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[14:06:32.980]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:06:32.981]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:06:32.981]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:06:32.982]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:06:32.983]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:06:32.983]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:06:32.984]    </block>
[14:06:32.984]  </sequence>
[14:06:32.984]  
[14:06:41.066]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:06:41.066]  
[14:06:41.067]  <debugvars>
[14:06:41.067]    // Pre-defined
[14:06:41.068]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:06:41.068]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:06:41.068]    __dp=0x00000000
[14:06:41.069]    __ap=0x00000000
[14:06:41.070]    __traceout=0x00000000      (Trace Disabled)
[14:06:41.071]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:06:41.071]    __FlashAddr=0x00000000
[14:06:41.072]    __FlashLen=0x00000000
[14:06:41.072]    __FlashArg=0x00000000
[14:06:41.072]    __FlashOp=0x00000000
[14:06:41.073]    __Result=0x00000000
[14:06:41.073]    
[14:06:41.073]    // User-defined
[14:06:41.073]    DbgMCU_CR=0x00000007
[14:06:41.074]    DbgMCU_APB1_Fz=0x00000000
[14:06:41.074]    DbgMCU_APB2_Fz=0x00000000
[14:06:41.074]    DoOptionByteLoading=0x00000000
[14:06:41.075]  </debugvars>
[14:06:41.075]  
[14:06:41.075]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:06:41.075]    <block atomic="false" info="">
[14:06:41.076]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:06:41.076]        // -> [connectionFlash <= 0x00000001]
[14:06:41.076]      __var FLASH_BASE = 0x40022000 ;
[14:06:41.076]        // -> [FLASH_BASE <= 0x40022000]
[14:06:41.077]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:06:41.077]        // -> [FLASH_CR <= 0x40022004]
[14:06:41.077]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:06:41.078]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:06:41.078]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:06:41.078]        // -> [LOCK_BIT <= 0x00000001]
[14:06:41.078]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:06:41.078]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:06:41.078]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:06:41.079]        // -> [FLASH_KEYR <= 0x4002200C]
[14:06:41.079]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:06:41.079]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:06:41.079]      __var FLASH_KEY2 = 0x02030405 ;
[14:06:41.079]        // -> [FLASH_KEY2 <= 0x02030405]
[14:06:41.080]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:06:41.080]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:06:41.080]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:06:41.080]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:06:41.080]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:06:41.081]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:06:41.081]      __var FLASH_CR_Value = 0 ;
[14:06:41.081]        // -> [FLASH_CR_Value <= 0x00000000]
[14:06:41.081]      __var DoDebugPortStop = 1 ;
[14:06:41.081]        // -> [DoDebugPortStop <= 0x00000001]
[14:06:41.081]      __var DP_CTRL_STAT = 0x4 ;
[14:06:41.082]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:06:41.082]      __var DP_SELECT = 0x8 ;
[14:06:41.082]        // -> [DP_SELECT <= 0x00000008]
[14:06:41.082]    </block>
[14:06:41.082]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:06:41.083]      // if-block "connectionFlash && DoOptionByteLoading"
[14:06:41.083]        // =>  FALSE
[14:06:41.083]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:06:41.083]    </control>
[14:06:41.083]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:06:41.083]      // if-block "DoDebugPortStop"
[14:06:41.083]        // =>  TRUE
[14:06:41.083]      <block atomic="false" info="">
[14:06:41.084]        WriteDP(DP_SELECT, 0x00000000);
[14:06:41.085]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:06:41.086]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:06:41.086]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:06:41.087]      </block>
[14:06:41.087]      // end if-block "DoDebugPortStop"
[14:06:41.087]    </control>
[14:06:41.087]  </sequence>
[14:06:41.088]  
[15:31:04.845]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:31:04.845]  
[15:31:04.858]  <debugvars>
[15:31:04.858]    // Pre-defined
[15:31:04.858]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:31:04.858]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:31:04.858]    __dp=0x00000000
[15:31:04.858]    __ap=0x00000000
[15:31:04.859]    __traceout=0x00000000      (Trace Disabled)
[15:31:04.859]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:31:04.859]    __FlashAddr=0x00000000
[15:31:04.859]    __FlashLen=0x00000000
[15:31:04.859]    __FlashArg=0x00000000
[15:31:04.860]    __FlashOp=0x00000000
[15:31:04.860]    __Result=0x00000000
[15:31:04.861]    
[15:31:04.861]    // User-defined
[15:31:04.861]    DbgMCU_CR=0x00000007
[15:31:04.861]    DbgMCU_APB1_Fz=0x00000000
[15:31:04.861]    DbgMCU_APB2_Fz=0x00000000
[15:31:04.861]    DoOptionByteLoading=0x00000000
[15:31:04.862]  </debugvars>
[15:31:04.862]  
[15:31:04.862]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:31:04.862]    <block atomic="false" info="">
[15:31:04.862]      Sequence("CheckID");
[15:31:04.863]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:31:04.863]          <block atomic="false" info="">
[15:31:04.863]            __var pidr1 = 0;
[15:31:04.863]              // -> [pidr1 <= 0x00000000]
[15:31:04.863]            __var pidr2 = 0;
[15:31:04.863]              // -> [pidr2 <= 0x00000000]
[15:31:04.864]            __var jep106id = 0;
[15:31:04.864]              // -> [jep106id <= 0x00000000]
[15:31:04.864]            __var ROMTableBase = 0;
[15:31:04.864]              // -> [ROMTableBase <= 0x00000000]
[15:31:04.864]            __ap = 0;      // AHB-AP
[15:31:04.865]              // -> [__ap <= 0x00000000]
[15:31:04.865]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:31:04.865]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:31:04.866]              // -> [ROMTableBase <= 0xF0000000]
[15:31:04.866]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:31:04.867]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:31:04.868]              // -> [pidr1 <= 0x00000004]
[15:31:04.868]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:31:04.869]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:31:04.870]              // -> [pidr2 <= 0x0000000A]
[15:31:04.870]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:31:04.870]              // -> [jep106id <= 0x00000020]
[15:31:04.870]          </block>
[15:31:04.870]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:31:04.871]            // if-block "jep106id != 0x20"
[15:31:04.871]              // =>  FALSE
[15:31:04.871]            // skip if-block "jep106id != 0x20"
[15:31:04.871]          </control>
[15:31:04.871]        </sequence>
[15:31:04.871]    </block>
[15:31:04.872]  </sequence>
[15:31:04.872]  
[15:31:04.884]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:31:04.884]  
[15:31:04.907]  <debugvars>
[15:31:04.908]    // Pre-defined
[15:31:04.908]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:31:04.908]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:31:04.908]    __dp=0x00000000
[15:31:04.908]    __ap=0x00000000
[15:31:04.908]    __traceout=0x00000000      (Trace Disabled)
[15:31:04.908]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:31:04.910]    __FlashAddr=0x00000000
[15:31:04.910]    __FlashLen=0x00000000
[15:31:04.910]    __FlashArg=0x00000000
[15:31:04.910]    __FlashOp=0x00000000
[15:31:04.910]    __Result=0x00000000
[15:31:04.911]    
[15:31:04.911]    // User-defined
[15:31:04.911]    DbgMCU_CR=0x00000007
[15:31:04.911]    DbgMCU_APB1_Fz=0x00000000
[15:31:04.911]    DbgMCU_APB2_Fz=0x00000000
[15:31:04.911]    DoOptionByteLoading=0x00000000
[15:31:04.911]  </debugvars>
[15:31:04.911]  
[15:31:04.912]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:31:04.912]    <block atomic="false" info="">
[15:31:04.912]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:31:04.913]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:31:04.913]    </block>
[15:31:04.913]    <block atomic="false" info="DbgMCU registers">
[15:31:04.913]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:31:04.914]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[15:31:04.915]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[15:31:04.915]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:31:04.916]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:31:04.917]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:31:04.917]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:31:04.917]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:31:04.919]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:31:04.919]    </block>
[15:31:04.919]  </sequence>
[15:31:04.920]  
[15:31:12.899]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:31:12.899]  
[15:31:12.899]  <debugvars>
[15:31:12.901]    // Pre-defined
[15:31:12.902]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:31:12.902]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:31:12.902]    __dp=0x00000000
[15:31:12.903]    __ap=0x00000000
[15:31:12.903]    __traceout=0x00000000      (Trace Disabled)
[15:31:12.903]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:31:12.903]    __FlashAddr=0x00000000
[15:31:12.904]    __FlashLen=0x00000000
[15:31:12.904]    __FlashArg=0x00000000
[15:31:12.904]    __FlashOp=0x00000000
[15:31:12.904]    __Result=0x00000000
[15:31:12.904]    
[15:31:12.904]    // User-defined
[15:31:12.906]    DbgMCU_CR=0x00000007
[15:31:12.906]    DbgMCU_APB1_Fz=0x00000000
[15:31:12.906]    DbgMCU_APB2_Fz=0x00000000
[15:31:12.906]    DoOptionByteLoading=0x00000000
[15:31:12.906]  </debugvars>
[15:31:12.906]  
[15:31:12.907]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:31:12.907]    <block atomic="false" info="">
[15:31:12.907]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:31:12.907]        // -> [connectionFlash <= 0x00000001]
[15:31:12.907]      __var FLASH_BASE = 0x40022000 ;
[15:31:12.908]        // -> [FLASH_BASE <= 0x40022000]
[15:31:12.908]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:31:12.908]        // -> [FLASH_CR <= 0x40022004]
[15:31:12.908]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:31:12.908]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:31:12.909]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:31:12.909]        // -> [LOCK_BIT <= 0x00000001]
[15:31:12.909]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:31:12.909]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:31:12.909]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:31:12.909]        // -> [FLASH_KEYR <= 0x4002200C]
[15:31:12.910]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:31:12.910]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:31:12.910]      __var FLASH_KEY2 = 0x02030405 ;
[15:31:12.910]        // -> [FLASH_KEY2 <= 0x02030405]
[15:31:12.910]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:31:12.911]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:31:12.911]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:31:12.911]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:31:12.911]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:31:12.911]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:31:12.912]      __var FLASH_CR_Value = 0 ;
[15:31:12.912]        // -> [FLASH_CR_Value <= 0x00000000]
[15:31:12.913]      __var DoDebugPortStop = 1 ;
[15:31:12.913]        // -> [DoDebugPortStop <= 0x00000001]
[15:31:12.913]      __var DP_CTRL_STAT = 0x4 ;
[15:31:12.913]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:31:12.913]      __var DP_SELECT = 0x8 ;
[15:31:12.914]        // -> [DP_SELECT <= 0x00000008]
[15:31:12.914]    </block>
[15:31:12.914]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:31:12.914]      // if-block "connectionFlash && DoOptionByteLoading"
[15:31:12.914]        // =>  FALSE
[15:31:12.914]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:31:12.915]    </control>
[15:31:12.915]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:31:12.915]      // if-block "DoDebugPortStop"
[15:31:12.915]        // =>  TRUE
[15:31:12.915]      <block atomic="false" info="">
[15:31:12.916]        WriteDP(DP_SELECT, 0x00000000);
[15:31:12.916]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:31:12.916]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:31:12.917]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:31:12.917]      </block>
[15:31:12.917]      // end if-block "DoDebugPortStop"
[15:31:12.918]    </control>
[15:31:12.918]  </sequence>
[15:31:12.918]  
[15:34:12.904]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:34:12.904]  
[15:34:12.905]  <debugvars>
[15:34:12.905]    // Pre-defined
[15:34:12.906]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:34:12.906]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:34:12.906]    __dp=0x00000000
[15:34:12.907]    __ap=0x00000000
[15:34:12.907]    __traceout=0x00000000      (Trace Disabled)
[15:34:12.907]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:34:12.907]    __FlashAddr=0x00000000
[15:34:12.908]    __FlashLen=0x00000000
[15:34:12.908]    __FlashArg=0x00000000
[15:34:12.908]    __FlashOp=0x00000000
[15:34:12.908]    __Result=0x00000000
[15:34:12.909]    
[15:34:12.909]    // User-defined
[15:34:12.909]    DbgMCU_CR=0x00000007
[15:34:12.909]    DbgMCU_APB1_Fz=0x00000000
[15:34:12.909]    DbgMCU_APB2_Fz=0x00000000
[15:34:12.909]    DoOptionByteLoading=0x00000000
[15:34:12.910]  </debugvars>
[15:34:12.910]  
[15:34:12.910]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:34:12.910]    <block atomic="false" info="">
[15:34:12.910]      Sequence("CheckID");
[15:34:12.910]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:34:12.910]          <block atomic="false" info="">
[15:34:12.910]            __var pidr1 = 0;
[15:34:12.911]              // -> [pidr1 <= 0x00000000]
[15:34:12.912]            __var pidr2 = 0;
[15:34:12.912]              // -> [pidr2 <= 0x00000000]
[15:34:12.912]            __var jep106id = 0;
[15:34:12.912]              // -> [jep106id <= 0x00000000]
[15:34:12.913]            __var ROMTableBase = 0;
[15:34:12.913]              // -> [ROMTableBase <= 0x00000000]
[15:34:12.913]            __ap = 0;      // AHB-AP
[15:34:12.914]              // -> [__ap <= 0x00000000]
[15:34:12.914]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:34:12.914]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:34:12.915]              // -> [ROMTableBase <= 0xF0000000]
[15:34:12.915]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:34:12.916]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:34:12.917]              // -> [pidr1 <= 0x00000004]
[15:34:12.917]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:34:12.918]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:34:12.918]              // -> [pidr2 <= 0x0000000A]
[15:34:12.918]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:34:12.918]              // -> [jep106id <= 0x00000020]
[15:34:12.919]          </block>
[15:34:12.919]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:34:12.919]            // if-block "jep106id != 0x20"
[15:34:12.919]              // =>  FALSE
[15:34:12.919]            // skip if-block "jep106id != 0x20"
[15:34:12.919]          </control>
[15:34:12.920]        </sequence>
[15:34:12.920]    </block>
[15:34:12.920]  </sequence>
[15:34:12.921]  
[15:34:12.932]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:34:12.932]  
[15:34:12.958]  <debugvars>
[15:34:12.958]    // Pre-defined
[15:34:12.959]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:34:12.960]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:34:12.960]    __dp=0x00000000
[15:34:12.961]    __ap=0x00000000
[15:34:12.961]    __traceout=0x00000000      (Trace Disabled)
[15:34:12.962]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:34:12.962]    __FlashAddr=0x00000000
[15:34:12.963]    __FlashLen=0x00000000
[15:34:12.964]    __FlashArg=0x00000000
[15:34:12.964]    __FlashOp=0x00000000
[15:34:12.965]    __Result=0x00000000
[15:34:12.965]    
[15:34:12.965]    // User-defined
[15:34:12.965]    DbgMCU_CR=0x00000007
[15:34:12.965]    DbgMCU_APB1_Fz=0x00000000
[15:34:12.966]    DbgMCU_APB2_Fz=0x00000000
[15:34:12.966]    DoOptionByteLoading=0x00000000
[15:34:12.966]  </debugvars>
[15:34:12.966]  
[15:34:12.968]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:34:12.968]    <block atomic="false" info="">
[15:34:12.969]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:34:12.969]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:34:12.970]    </block>
[15:34:12.970]    <block atomic="false" info="DbgMCU registers">
[15:34:12.970]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:34:12.971]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[15:34:12.973]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[15:34:12.973]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:34:12.974]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:34:12.974]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:34:12.975]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:34:12.975]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:34:12.976]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:34:12.977]    </block>
[15:34:12.977]  </sequence>
[15:34:12.977]  
[15:34:21.053]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:34:21.053]  
[15:34:21.054]  <debugvars>
[15:34:21.054]    // Pre-defined
[15:34:21.054]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:34:21.055]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:34:21.055]    __dp=0x00000000
[15:34:21.055]    __ap=0x00000000
[15:34:21.056]    __traceout=0x00000000      (Trace Disabled)
[15:34:21.056]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:34:21.057]    __FlashAddr=0x00000000
[15:34:21.057]    __FlashLen=0x00000000
[15:34:21.057]    __FlashArg=0x00000000
[15:34:21.057]    __FlashOp=0x00000000
[15:34:21.058]    __Result=0x00000000
[15:34:21.058]    
[15:34:21.058]    // User-defined
[15:34:21.058]    DbgMCU_CR=0x00000007
[15:34:21.058]    DbgMCU_APB1_Fz=0x00000000
[15:34:21.059]    DbgMCU_APB2_Fz=0x00000000
[15:34:21.059]    DoOptionByteLoading=0x00000000
[15:34:21.059]  </debugvars>
[15:34:21.059]  
[15:34:21.059]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:34:21.059]    <block atomic="false" info="">
[15:34:21.061]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:34:21.061]        // -> [connectionFlash <= 0x00000001]
[15:34:21.061]      __var FLASH_BASE = 0x40022000 ;
[15:34:21.061]        // -> [FLASH_BASE <= 0x40022000]
[15:34:21.061]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:34:21.061]        // -> [FLASH_CR <= 0x40022004]
[15:34:21.061]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:34:21.062]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:34:21.062]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:34:21.062]        // -> [LOCK_BIT <= 0x00000001]
[15:34:21.063]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:34:21.063]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:34:21.063]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:34:21.064]        // -> [FLASH_KEYR <= 0x4002200C]
[15:34:21.064]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:34:21.065]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:34:21.065]      __var FLASH_KEY2 = 0x02030405 ;
[15:34:21.065]        // -> [FLASH_KEY2 <= 0x02030405]
[15:34:21.065]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:34:21.066]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:34:21.066]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:34:21.066]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:34:21.066]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:34:21.066]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:34:21.066]      __var FLASH_CR_Value = 0 ;
[15:34:21.067]        // -> [FLASH_CR_Value <= 0x00000000]
[15:34:21.067]      __var DoDebugPortStop = 1 ;
[15:34:21.067]        // -> [DoDebugPortStop <= 0x00000001]
[15:34:21.067]      __var DP_CTRL_STAT = 0x4 ;
[15:34:21.067]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:34:21.068]      __var DP_SELECT = 0x8 ;
[15:34:21.068]        // -> [DP_SELECT <= 0x00000008]
[15:34:21.068]    </block>
[15:34:21.068]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:34:21.068]      // if-block "connectionFlash && DoOptionByteLoading"
[15:34:21.068]        // =>  FALSE
[15:34:21.069]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:34:21.069]    </control>
[15:34:21.069]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:34:21.069]      // if-block "DoDebugPortStop"
[15:34:21.069]        // =>  TRUE
[15:34:21.069]      <block atomic="false" info="">
[15:34:21.069]        WriteDP(DP_SELECT, 0x00000000);
[15:34:21.069]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:34:21.070]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:34:21.071]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:34:21.071]      </block>
[15:34:21.072]      // end if-block "DoDebugPortStop"
[15:34:21.073]    </control>
[15:34:21.073]  </sequence>
[15:34:21.073]  
[15:53:28.532]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:53:28.532]  
[15:53:28.541]  <debugvars>
[15:53:28.541]    // Pre-defined
[15:53:28.541]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:53:28.541]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:53:28.543]    __dp=0x00000000
[15:53:28.543]    __ap=0x00000000
[15:53:28.543]    __traceout=0x00000000      (Trace Disabled)
[15:53:28.543]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:53:28.544]    __FlashAddr=0x00000000
[15:53:28.544]    __FlashLen=0x00000000
[15:53:28.544]    __FlashArg=0x00000000
[15:53:28.544]    __FlashOp=0x00000000
[15:53:28.545]    __Result=0x00000000
[15:53:28.545]    
[15:53:28.545]    // User-defined
[15:53:28.545]    DbgMCU_CR=0x00000007
[15:53:28.545]    DbgMCU_APB1_Fz=0x00000000
[15:53:28.545]    DbgMCU_APB2_Fz=0x00000000
[15:53:28.546]    DoOptionByteLoading=0x00000000
[15:53:28.546]  </debugvars>
[15:53:28.546]  
[15:53:28.546]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:53:28.546]    <block atomic="false" info="">
[15:53:28.546]      Sequence("CheckID");
[15:53:28.547]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:53:28.547]          <block atomic="false" info="">
[15:53:28.547]            __var pidr1 = 0;
[15:53:28.547]              // -> [pidr1 <= 0x00000000]
[15:53:28.547]            __var pidr2 = 0;
[15:53:28.547]              // -> [pidr2 <= 0x00000000]
[15:53:28.548]            __var jep106id = 0;
[15:53:28.548]              // -> [jep106id <= 0x00000000]
[15:53:28.548]            __var ROMTableBase = 0;
[15:53:28.548]              // -> [ROMTableBase <= 0x00000000]
[15:53:28.549]            __ap = 0;      // AHB-AP
[15:53:28.549]              // -> [__ap <= 0x00000000]
[15:53:28.549]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:53:28.550]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:53:28.551]              // -> [ROMTableBase <= 0xF0000000]
[15:53:28.551]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:53:28.553]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:53:28.553]              // -> [pidr1 <= 0x00000004]
[15:53:28.554]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:53:28.554]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:53:28.554]              // -> [pidr2 <= 0x0000000A]
[15:53:28.555]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:53:28.555]              // -> [jep106id <= 0x00000020]
[15:53:28.556]          </block>
[15:53:28.556]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:53:28.556]            // if-block "jep106id != 0x20"
[15:53:28.556]              // =>  FALSE
[15:53:28.556]            // skip if-block "jep106id != 0x20"
[15:53:28.556]          </control>
[15:53:28.557]        </sequence>
[15:53:28.557]    </block>
[15:53:28.558]  </sequence>
[15:53:28.558]  
[15:53:28.571]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:53:28.571]  
[15:53:28.600]  <debugvars>
[15:53:28.600]    // Pre-defined
[15:53:28.600]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:53:28.601]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:53:28.601]    __dp=0x00000000
[15:53:28.601]    __ap=0x00000000
[15:53:28.601]    __traceout=0x00000000      (Trace Disabled)
[15:53:28.602]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:53:28.602]    __FlashAddr=0x00000000
[15:53:28.602]    __FlashLen=0x00000000
[15:53:28.603]    __FlashArg=0x00000000
[15:53:28.603]    __FlashOp=0x00000000
[15:53:28.603]    __Result=0x00000000
[15:53:28.603]    
[15:53:28.603]    // User-defined
[15:53:28.603]    DbgMCU_CR=0x00000007
[15:53:28.604]    DbgMCU_APB1_Fz=0x00000000
[15:53:28.604]    DbgMCU_APB2_Fz=0x00000000
[15:53:28.604]    DoOptionByteLoading=0x00000000
[15:53:28.604]  </debugvars>
[15:53:28.604]  
[15:53:28.605]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:53:28.605]    <block atomic="false" info="">
[15:53:28.605]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:53:28.606]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:53:28.606]    </block>
[15:53:28.606]    <block atomic="false" info="DbgMCU registers">
[15:53:28.607]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:53:28.608]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[15:53:28.608]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[15:53:28.609]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:53:28.610]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:53:28.610]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:53:28.610]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:53:28.610]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:53:28.612]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:53:28.612]    </block>
[15:53:28.612]  </sequence>
[15:53:28.612]  
[15:53:36.501]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:53:36.501]  
[15:53:36.501]  <debugvars>
[15:53:36.501]    // Pre-defined
[15:53:36.502]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:53:36.502]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:53:36.502]    __dp=0x00000000
[15:53:36.503]    __ap=0x00000000
[15:53:36.503]    __traceout=0x00000000      (Trace Disabled)
[15:53:36.503]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:53:36.503]    __FlashAddr=0x00000000
[15:53:36.503]    __FlashLen=0x00000000
[15:53:36.504]    __FlashArg=0x00000000
[15:53:36.504]    __FlashOp=0x00000000
[15:53:36.505]    __Result=0x00000000
[15:53:36.505]    
[15:53:36.505]    // User-defined
[15:53:36.505]    DbgMCU_CR=0x00000007
[15:53:36.505]    DbgMCU_APB1_Fz=0x00000000
[15:53:36.505]    DbgMCU_APB2_Fz=0x00000000
[15:53:36.505]    DoOptionByteLoading=0x00000000
[15:53:36.506]  </debugvars>
[15:53:36.506]  
[15:53:36.506]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:53:36.506]    <block atomic="false" info="">
[15:53:36.507]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:53:36.507]        // -> [connectionFlash <= 0x00000001]
[15:53:36.507]      __var FLASH_BASE = 0x40022000 ;
[15:53:36.507]        // -> [FLASH_BASE <= 0x40022000]
[15:53:36.507]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:53:36.507]        // -> [FLASH_CR <= 0x40022004]
[15:53:36.508]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:53:36.508]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:53:36.508]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:53:36.508]        // -> [LOCK_BIT <= 0x00000001]
[15:53:36.508]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:53:36.509]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:53:36.509]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:53:36.509]        // -> [FLASH_KEYR <= 0x4002200C]
[15:53:36.509]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:53:36.509]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:53:36.510]      __var FLASH_KEY2 = 0x02030405 ;
[15:53:36.510]        // -> [FLASH_KEY2 <= 0x02030405]
[15:53:36.510]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:53:36.510]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:53:36.510]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:53:36.511]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:53:36.511]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:53:36.511]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:53:36.511]      __var FLASH_CR_Value = 0 ;
[15:53:36.511]        // -> [FLASH_CR_Value <= 0x00000000]
[15:53:36.512]      __var DoDebugPortStop = 1 ;
[15:53:36.512]        // -> [DoDebugPortStop <= 0x00000001]
[15:53:36.512]      __var DP_CTRL_STAT = 0x4 ;
[15:53:36.512]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:53:36.513]      __var DP_SELECT = 0x8 ;
[15:53:36.513]        // -> [DP_SELECT <= 0x00000008]
[15:53:36.513]    </block>
[15:53:36.513]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:53:36.513]      // if-block "connectionFlash && DoOptionByteLoading"
[15:53:36.514]        // =>  FALSE
[15:53:36.514]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:53:36.514]    </control>
[15:53:36.514]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:53:36.514]      // if-block "DoDebugPortStop"
[15:53:36.514]        // =>  TRUE
[15:53:36.515]      <block atomic="false" info="">
[15:53:36.515]        WriteDP(DP_SELECT, 0x00000000);
[15:53:36.515]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:53:36.515]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:53:36.516]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:53:36.516]      </block>
[15:53:36.516]      // end if-block "DoDebugPortStop"
[15:53:36.517]    </control>
[15:53:36.517]  </sequence>
[15:53:36.517]  
[15:55:37.048]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:55:37.048]  
[15:55:37.049]  <debugvars>
[15:55:37.049]    // Pre-defined
[15:55:37.049]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:55:37.049]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[15:55:37.049]    __dp=0x00000000
[15:55:37.049]    __ap=0x00000000
[15:55:37.050]    __traceout=0x00000000      (Trace Disabled)
[15:55:37.050]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:55:37.051]    __FlashAddr=0x00000000
[15:55:37.051]    __FlashLen=0x00000000
[15:55:37.051]    __FlashArg=0x00000000
[15:55:37.051]    __FlashOp=0x00000000
[15:55:37.051]    __Result=0x00000000
[15:55:37.051]    
[15:55:37.051]    // User-defined
[15:55:37.052]    DbgMCU_CR=0x00000007
[15:55:37.052]    DbgMCU_APB1_Fz=0x00000000
[15:55:37.052]    DbgMCU_APB2_Fz=0x00000000
[15:55:37.052]    DoOptionByteLoading=0x00000000
[15:55:37.052]  </debugvars>
[15:55:37.053]  
[15:55:37.053]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:55:37.053]    <block atomic="false" info="">
[15:55:37.053]      Sequence("CheckID");
[15:55:37.053]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:55:37.053]          <block atomic="false" info="">
[15:55:37.054]            __var pidr1 = 0;
[15:55:37.054]              // -> [pidr1 <= 0x00000000]
[15:55:37.054]            __var pidr2 = 0;
[15:55:37.054]              // -> [pidr2 <= 0x00000000]
[15:55:37.054]            __var jep106id = 0;
[15:55:37.054]              // -> [jep106id <= 0x00000000]
[15:55:37.055]            __var ROMTableBase = 0;
[15:55:37.055]              // -> [ROMTableBase <= 0x00000000]
[15:55:37.055]            __ap = 0;      // AHB-AP
[15:55:37.055]              // -> [__ap <= 0x00000000]
[15:55:37.055]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:55:37.056]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:55:37.057]              // -> [ROMTableBase <= 0xF0000000]
[15:55:37.057]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:55:37.057]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:55:37.058]              // -> [pidr1 <= 0x00000004]
[15:55:37.058]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:55:37.059]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:55:37.059]              // -> [pidr2 <= 0x0000000A]
[15:55:37.060]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:55:37.060]              // -> [jep106id <= 0x00000020]
[15:55:37.060]          </block>
[15:55:37.060]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:55:37.060]            // if-block "jep106id != 0x20"
[15:55:37.061]              // =>  FALSE
[15:55:37.061]            // skip if-block "jep106id != 0x20"
[15:55:37.062]          </control>
[15:55:37.062]        </sequence>
[15:55:37.062]    </block>
[15:55:37.062]  </sequence>
[15:55:37.062]  
[15:55:37.074]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:55:37.074]  
[15:55:37.074]  <debugvars>
[15:55:37.074]    // Pre-defined
[15:55:37.074]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:55:37.075]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[15:55:37.075]    __dp=0x00000000
[15:55:37.075]    __ap=0x00000000
[15:55:37.075]    __traceout=0x00000000      (Trace Disabled)
[15:55:37.075]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:55:37.075]    __FlashAddr=0x00000000
[15:55:37.076]    __FlashLen=0x00000000
[15:55:37.076]    __FlashArg=0x00000000
[15:55:37.076]    __FlashOp=0x00000000
[15:55:37.076]    __Result=0x00000000
[15:55:37.076]    
[15:55:37.076]    // User-defined
[15:55:37.077]    DbgMCU_CR=0x00000007
[15:55:37.077]    DbgMCU_APB1_Fz=0x00000000
[15:55:37.077]    DbgMCU_APB2_Fz=0x00000000
[15:55:37.077]    DoOptionByteLoading=0x00000000
[15:55:37.077]  </debugvars>
[15:55:37.077]  
[15:55:37.078]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:55:37.078]    <block atomic="false" info="">
[15:55:37.078]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:55:37.079]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:55:37.079]    </block>
[15:55:37.079]    <block atomic="false" info="DbgMCU registers">
[15:55:37.079]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:55:37.081]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[15:55:37.081]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[15:55:37.081]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:55:37.082]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:55:37.083]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:55:37.084]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:55:37.084]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:55:37.085]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:55:37.085]    </block>
[15:55:37.085]  </sequence>
[15:55:37.085]  
[16:00:45.760]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:00:45.760]  
[16:00:45.760]  <debugvars>
[16:00:45.760]    // Pre-defined
[16:00:45.761]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:00:45.761]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[16:00:45.761]    __dp=0x00000000
[16:00:45.763]    __ap=0x00000000
[16:00:45.763]    __traceout=0x00000000      (Trace Disabled)
[16:00:45.763]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:00:45.763]    __FlashAddr=0x00000000
[16:00:45.764]    __FlashLen=0x00000000
[16:00:45.764]    __FlashArg=0x00000000
[16:00:45.764]    __FlashOp=0x00000000
[16:00:45.765]    __Result=0x00000000
[16:00:45.765]    
[16:00:45.765]    // User-defined
[16:00:45.765]    DbgMCU_CR=0x00000007
[16:00:45.765]    DbgMCU_APB1_Fz=0x00000000
[16:00:45.766]    DbgMCU_APB2_Fz=0x00000000
[16:00:45.766]    DoOptionByteLoading=0x00000000
[16:00:45.766]  </debugvars>
[16:00:45.766]  
[16:00:45.767]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:00:45.767]    <block atomic="false" info="">
[16:00:45.767]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:00:45.767]        // -> [connectionFlash <= 0x00000000]
[16:00:45.767]      __var FLASH_BASE = 0x40022000 ;
[16:00:45.767]        // -> [FLASH_BASE <= 0x40022000]
[16:00:45.768]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:00:45.768]        // -> [FLASH_CR <= 0x40022004]
[16:00:45.769]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:00:45.769]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:00:45.769]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:00:45.769]        // -> [LOCK_BIT <= 0x00000001]
[16:00:45.770]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:00:45.770]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:00:45.770]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:00:45.771]        // -> [FLASH_KEYR <= 0x4002200C]
[16:00:45.771]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:00:45.771]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:00:45.771]      __var FLASH_KEY2 = 0x02030405 ;
[16:00:45.771]        // -> [FLASH_KEY2 <= 0x02030405]
[16:00:45.772]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:00:45.772]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:00:45.772]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:00:45.772]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:00:45.772]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:00:45.773]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:00:45.773]      __var FLASH_CR_Value = 0 ;
[16:00:45.773]        // -> [FLASH_CR_Value <= 0x00000000]
[16:00:45.773]      __var DoDebugPortStop = 1 ;
[16:00:45.773]        // -> [DoDebugPortStop <= 0x00000001]
[16:00:45.773]      __var DP_CTRL_STAT = 0x4 ;
[16:00:45.774]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:00:45.774]      __var DP_SELECT = 0x8 ;
[16:00:45.774]        // -> [DP_SELECT <= 0x00000008]
[16:00:45.774]    </block>
[16:00:45.774]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:00:45.775]      // if-block "connectionFlash && DoOptionByteLoading"
[16:00:45.775]        // =>  FALSE
[16:00:45.775]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:00:45.775]    </control>
[16:00:45.775]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:00:45.775]      // if-block "DoDebugPortStop"
[16:00:45.776]        // =>  TRUE
[16:00:45.776]      <block atomic="false" info="">
[16:00:45.776]        WriteDP(DP_SELECT, 0x00000000);
[16:00:45.776]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:00:45.776]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:00:45.777]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:00:45.777]      </block>
[16:00:45.777]      // end if-block "DoDebugPortStop"
[16:00:45.778]    </control>
[16:00:45.778]  </sequence>
[16:00:45.778]  
[16:02:10.979]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:02:10.979]  
[16:02:10.980]  <debugvars>
[16:02:10.980]    // Pre-defined
[16:02:10.980]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:02:10.980]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:02:10.980]    __dp=0x00000000
[16:02:10.981]    __ap=0x00000000
[16:02:10.981]    __traceout=0x00000000      (Trace Disabled)
[16:02:10.981]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:02:10.981]    __FlashAddr=0x00000000
[16:02:10.981]    __FlashLen=0x00000000
[16:02:10.982]    __FlashArg=0x00000000
[16:02:10.982]    __FlashOp=0x00000000
[16:02:10.982]    __Result=0x00000000
[16:02:10.982]    
[16:02:10.982]    // User-defined
[16:02:10.982]    DbgMCU_CR=0x00000007
[16:02:10.982]    DbgMCU_APB1_Fz=0x00000000
[16:02:10.982]    DbgMCU_APB2_Fz=0x00000000
[16:02:10.983]    DoOptionByteLoading=0x00000000
[16:02:10.983]  </debugvars>
[16:02:10.983]  
[16:02:10.984]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:02:10.984]    <block atomic="false" info="">
[16:02:10.984]      Sequence("CheckID");
[16:02:10.984]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:02:10.985]          <block atomic="false" info="">
[16:02:10.985]            __var pidr1 = 0;
[16:02:10.985]              // -> [pidr1 <= 0x00000000]
[16:02:10.985]            __var pidr2 = 0;
[16:02:10.985]              // -> [pidr2 <= 0x00000000]
[16:02:10.986]            __var jep106id = 0;
[16:02:10.986]              // -> [jep106id <= 0x00000000]
[16:02:10.986]            __var ROMTableBase = 0;
[16:02:10.986]              // -> [ROMTableBase <= 0x00000000]
[16:02:10.986]            __ap = 0;      // AHB-AP
[16:02:10.986]              // -> [__ap <= 0x00000000]
[16:02:10.987]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:02:10.987]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:02:10.987]              // -> [ROMTableBase <= 0xF0000000]
[16:02:10.988]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:02:10.988]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:02:10.988]              // -> [pidr1 <= 0x00000004]
[16:02:10.989]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:02:10.990]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:02:10.990]              // -> [pidr2 <= 0x0000000A]
[16:02:10.991]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:02:10.991]              // -> [jep106id <= 0x00000020]
[16:02:10.991]          </block>
[16:02:10.991]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:02:10.992]            // if-block "jep106id != 0x20"
[16:02:10.992]              // =>  FALSE
[16:02:10.992]            // skip if-block "jep106id != 0x20"
[16:02:10.992]          </control>
[16:02:10.992]        </sequence>
[16:02:10.993]    </block>
[16:02:10.993]  </sequence>
[16:02:10.993]  
[16:02:11.004]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:02:11.004]  
[16:02:11.005]  <debugvars>
[16:02:11.006]    // Pre-defined
[16:02:11.006]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:02:11.006]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:02:11.006]    __dp=0x00000000
[16:02:11.006]    __ap=0x00000000
[16:02:11.007]    __traceout=0x00000000      (Trace Disabled)
[16:02:11.007]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:02:11.007]    __FlashAddr=0x00000000
[16:02:11.007]    __FlashLen=0x00000000
[16:02:11.007]    __FlashArg=0x00000000
[16:02:11.008]    __FlashOp=0x00000000
[16:02:11.008]    __Result=0x00000000
[16:02:11.008]    
[16:02:11.008]    // User-defined
[16:02:11.008]    DbgMCU_CR=0x00000007
[16:02:11.009]    DbgMCU_APB1_Fz=0x00000000
[16:02:11.009]    DbgMCU_APB2_Fz=0x00000000
[16:02:11.009]    DoOptionByteLoading=0x00000000
[16:02:11.011]  </debugvars>
[16:02:11.011]  
[16:02:11.011]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:02:11.011]    <block atomic="false" info="">
[16:02:11.011]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:02:11.012]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:02:11.012]    </block>
[16:02:11.013]    <block atomic="false" info="DbgMCU registers">
[16:02:11.013]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:02:11.013]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:02:11.014]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:02:11.014]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:02:11.015]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:02:11.015]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:02:11.016]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:02:11.016]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:02:11.017]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:02:11.017]    </block>
[16:02:11.017]  </sequence>
[16:02:11.017]  
[16:02:18.870]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:02:18.870]  
[16:02:18.871]  <debugvars>
[16:02:18.871]    // Pre-defined
[16:02:18.871]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:02:18.872]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:02:18.872]    __dp=0x00000000
[16:02:18.873]    __ap=0x00000000
[16:02:18.873]    __traceout=0x00000000      (Trace Disabled)
[16:02:18.873]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:02:18.874]    __FlashAddr=0x00000000
[16:02:18.874]    __FlashLen=0x00000000
[16:02:18.874]    __FlashArg=0x00000000
[16:02:18.874]    __FlashOp=0x00000000
[16:02:18.874]    __Result=0x00000000
[16:02:18.875]    
[16:02:18.875]    // User-defined
[16:02:18.875]    DbgMCU_CR=0x00000007
[16:02:18.875]    DbgMCU_APB1_Fz=0x00000000
[16:02:18.875]    DbgMCU_APB2_Fz=0x00000000
[16:02:18.877]    DoOptionByteLoading=0x00000000
[16:02:18.877]  </debugvars>
[16:02:18.877]  
[16:02:18.877]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:02:18.878]    <block atomic="false" info="">
[16:02:18.878]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:02:18.878]        // -> [connectionFlash <= 0x00000001]
[16:02:18.878]      __var FLASH_BASE = 0x40022000 ;
[16:02:18.879]        // -> [FLASH_BASE <= 0x40022000]
[16:02:18.879]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:02:18.879]        // -> [FLASH_CR <= 0x40022004]
[16:02:18.880]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:02:18.880]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:02:18.880]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:02:18.880]        // -> [LOCK_BIT <= 0x00000001]
[16:02:18.881]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:02:18.881]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:02:18.881]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:02:18.882]        // -> [FLASH_KEYR <= 0x4002200C]
[16:02:18.882]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:02:18.882]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:02:18.883]      __var FLASH_KEY2 = 0x02030405 ;
[16:02:18.883]        // -> [FLASH_KEY2 <= 0x02030405]
[16:02:18.885]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:02:18.885]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:02:18.885]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:02:18.886]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:02:18.886]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:02:18.886]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:02:18.886]      __var FLASH_CR_Value = 0 ;
[16:02:18.887]        // -> [FLASH_CR_Value <= 0x00000000]
[16:02:18.887]      __var DoDebugPortStop = 1 ;
[16:02:18.887]        // -> [DoDebugPortStop <= 0x00000001]
[16:02:18.887]      __var DP_CTRL_STAT = 0x4 ;
[16:02:18.887]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:02:18.888]      __var DP_SELECT = 0x8 ;
[16:02:18.888]        // -> [DP_SELECT <= 0x00000008]
[16:02:18.888]    </block>
[16:02:18.888]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:02:18.889]      // if-block "connectionFlash && DoOptionByteLoading"
[16:02:18.889]        // =>  FALSE
[16:02:18.889]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:02:18.890]    </control>
[16:02:18.890]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:02:18.890]      // if-block "DoDebugPortStop"
[16:02:18.890]        // =>  TRUE
[16:02:18.891]      <block atomic="false" info="">
[16:02:18.891]        WriteDP(DP_SELECT, 0x00000000);
[16:02:18.892]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:02:18.892]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:02:18.893]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:02:18.893]      </block>
[16:02:18.893]      // end if-block "DoDebugPortStop"
[16:02:18.893]    </control>
[16:02:18.893]  </sequence>
[16:02:18.894]  
[16:02:19.205]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:02:19.205]  
[16:02:19.205]  <debugvars>
[16:02:19.206]    // Pre-defined
[16:02:19.206]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:02:19.206]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[16:02:19.206]    __dp=0x00000000
[16:02:19.207]    __ap=0x00000000
[16:02:19.207]    __traceout=0x00000000      (Trace Disabled)
[16:02:19.207]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:02:19.208]    __FlashAddr=0x00000000
[16:02:19.208]    __FlashLen=0x00000000
[16:02:19.208]    __FlashArg=0x00000000
[16:02:19.208]    __FlashOp=0x00000000
[16:02:19.208]    __Result=0x00000000
[16:02:19.208]    
[16:02:19.208]    // User-defined
[16:02:19.209]    DbgMCU_CR=0x00000007
[16:02:19.209]    DbgMCU_APB1_Fz=0x00000000
[16:02:19.209]    DbgMCU_APB2_Fz=0x00000000
[16:02:19.209]    DoOptionByteLoading=0x00000000
[16:02:19.209]  </debugvars>
[16:02:19.209]  
[16:02:19.210]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:02:19.210]    <block atomic="false" info="">
[16:02:19.211]      Sequence("CheckID");
[16:02:19.211]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:02:19.211]          <block atomic="false" info="">
[16:02:19.211]            __var pidr1 = 0;
[16:02:19.211]              // -> [pidr1 <= 0x00000000]
[16:02:19.212]            __var pidr2 = 0;
[16:02:19.212]              // -> [pidr2 <= 0x00000000]
[16:02:19.212]            __var jep106id = 0;
[16:02:19.212]              // -> [jep106id <= 0x00000000]
[16:02:19.212]            __var ROMTableBase = 0;
[16:02:19.212]              // -> [ROMTableBase <= 0x00000000]
[16:02:19.213]            __ap = 0;      // AHB-AP
[16:02:19.213]              // -> [__ap <= 0x00000000]
[16:02:19.213]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:02:19.213]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:02:19.213]              // -> [ROMTableBase <= 0xF0000000]
[16:02:19.213]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:02:19.215]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:02:19.216]              // -> [pidr1 <= 0x00000004]
[16:02:19.216]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:02:19.217]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:02:19.217]              // -> [pidr2 <= 0x0000000A]
[16:02:19.218]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:02:19.218]              // -> [jep106id <= 0x00000020]
[16:02:19.218]          </block>
[16:02:19.219]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:02:19.219]            // if-block "jep106id != 0x20"
[16:02:19.219]              // =>  FALSE
[16:02:19.219]            // skip if-block "jep106id != 0x20"
[16:02:19.220]          </control>
[16:02:19.220]        </sequence>
[16:02:19.220]    </block>
[16:02:19.220]  </sequence>
[16:02:19.220]  
[16:02:19.232]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:02:19.232]  
[16:02:19.232]  <debugvars>
[16:02:19.233]    // Pre-defined
[16:02:19.233]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:02:19.233]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[16:02:19.233]    __dp=0x00000000
[16:02:19.234]    __ap=0x00000000
[16:02:19.234]    __traceout=0x00000000      (Trace Disabled)
[16:02:19.234]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:02:19.234]    __FlashAddr=0x00000000
[16:02:19.234]    __FlashLen=0x00000000
[16:02:19.235]    __FlashArg=0x00000000
[16:02:19.235]    __FlashOp=0x00000000
[16:02:19.235]    __Result=0x00000000
[16:02:19.235]    
[16:02:19.235]    // User-defined
[16:02:19.236]    DbgMCU_CR=0x00000007
[16:02:19.236]    DbgMCU_APB1_Fz=0x00000000
[16:02:19.236]    DbgMCU_APB2_Fz=0x00000000
[16:02:19.236]    DoOptionByteLoading=0x00000000
[16:02:19.236]  </debugvars>
[16:02:19.237]  
[16:02:19.237]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:02:19.237]    <block atomic="false" info="">
[16:02:19.237]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:02:19.238]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:02:19.238]    </block>
[16:02:19.238]    <block atomic="false" info="DbgMCU registers">
[16:02:19.240]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:02:19.241]        // -> [Read32(0x40021034) => 0x00000200]   (__dp=0x00000000, __ap=0x00000000)
[16:02:19.241]        // -> [Write32(0x40021034, 0x00400200)]   (__dp=0x00000000, __ap=0x00000000)
[16:02:19.242]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:02:19.243]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:02:19.243]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:02:19.244]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:02:19.244]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:02:19.244]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:02:19.245]    </block>
[16:02:19.245]  </sequence>
[16:02:19.245]  
[16:03:27.035]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:03:27.035]  
[16:03:27.035]  <debugvars>
[16:03:27.036]    // Pre-defined
[16:03:27.036]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:03:27.036]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[16:03:27.036]    __dp=0x00000000
[16:03:27.036]    __ap=0x00000000
[16:03:27.037]    __traceout=0x00000000      (Trace Disabled)
[16:03:27.037]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:03:27.037]    __FlashAddr=0x00000000
[16:03:27.037]    __FlashLen=0x00000000
[16:03:27.038]    __FlashArg=0x00000000
[16:03:27.038]    __FlashOp=0x00000000
[16:03:27.038]    __Result=0x00000000
[16:03:27.039]    
[16:03:27.039]    // User-defined
[16:03:27.039]    DbgMCU_CR=0x00000007
[16:03:27.039]    DbgMCU_APB1_Fz=0x00000000
[16:03:27.039]    DbgMCU_APB2_Fz=0x00000000
[16:03:27.039]    DoOptionByteLoading=0x00000000
[16:03:27.040]  </debugvars>
[16:03:27.040]  
[16:03:27.040]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:03:27.040]    <block atomic="false" info="">
[16:03:27.040]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:03:27.041]        // -> [connectionFlash <= 0x00000000]
[16:03:27.041]      __var FLASH_BASE = 0x40022000 ;
[16:03:27.041]        // -> [FLASH_BASE <= 0x40022000]
[16:03:27.041]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:03:27.041]        // -> [FLASH_CR <= 0x40022004]
[16:03:27.041]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:03:27.041]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:03:27.042]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:03:27.042]        // -> [LOCK_BIT <= 0x00000001]
[16:03:27.042]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:03:27.043]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:03:27.043]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:03:27.043]        // -> [FLASH_KEYR <= 0x4002200C]
[16:03:27.043]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:03:27.043]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:03:27.044]      __var FLASH_KEY2 = 0x02030405 ;
[16:03:27.044]        // -> [FLASH_KEY2 <= 0x02030405]
[16:03:27.044]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:03:27.044]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:03:27.044]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:03:27.045]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:03:27.045]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:03:27.045]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:03:27.045]      __var FLASH_CR_Value = 0 ;
[16:03:27.045]        // -> [FLASH_CR_Value <= 0x00000000]
[16:03:27.046]      __var DoDebugPortStop = 1 ;
[16:03:27.046]        // -> [DoDebugPortStop <= 0x00000001]
[16:03:27.046]      __var DP_CTRL_STAT = 0x4 ;
[16:03:27.046]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:03:27.047]      __var DP_SELECT = 0x8 ;
[16:03:27.047]        // -> [DP_SELECT <= 0x00000008]
[16:03:27.047]    </block>
[16:03:27.047]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:03:27.048]      // if-block "connectionFlash && DoOptionByteLoading"
[16:03:27.048]        // =>  FALSE
[16:03:27.048]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:03:27.048]    </control>
[16:03:27.049]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:03:27.049]      // if-block "DoDebugPortStop"
[16:03:27.049]        // =>  TRUE
[16:03:27.049]      <block atomic="false" info="">
[16:03:27.049]        WriteDP(DP_SELECT, 0x00000000);
[16:03:27.050]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:03:27.050]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:03:27.050]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:03:27.051]      </block>
[16:03:27.051]      // end if-block "DoDebugPortStop"
[16:03:27.051]    </control>
[16:03:27.051]  </sequence>
[16:03:27.051]  
[16:05:03.205]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:05:03.205]  
[16:05:03.205]  <debugvars>
[16:05:03.206]    // Pre-defined
[16:05:03.206]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:05:03.207]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:05:03.207]    __dp=0x00000000
[16:05:03.207]    __ap=0x00000000
[16:05:03.208]    __traceout=0x00000000      (Trace Disabled)
[16:05:03.208]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:05:03.208]    __FlashAddr=0x00000000
[16:05:03.209]    __FlashLen=0x00000000
[16:05:03.209]    __FlashArg=0x00000000
[16:05:03.209]    __FlashOp=0x00000000
[16:05:03.209]    __Result=0x00000000
[16:05:03.210]    
[16:05:03.210]    // User-defined
[16:05:03.210]    DbgMCU_CR=0x00000007
[16:05:03.210]    DbgMCU_APB1_Fz=0x00000000
[16:05:03.211]    DbgMCU_APB2_Fz=0x00000000
[16:05:03.211]    DoOptionByteLoading=0x00000000
[16:05:03.211]  </debugvars>
[16:05:03.211]  
[16:05:03.211]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:05:03.211]    <block atomic="false" info="">
[16:05:03.212]      Sequence("CheckID");
[16:05:03.212]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:05:03.212]          <block atomic="false" info="">
[16:05:03.212]            __var pidr1 = 0;
[16:05:03.212]              // -> [pidr1 <= 0x00000000]
[16:05:03.213]            __var pidr2 = 0;
[16:05:03.213]              // -> [pidr2 <= 0x00000000]
[16:05:03.213]            __var jep106id = 0;
[16:05:03.213]              // -> [jep106id <= 0x00000000]
[16:05:03.213]            __var ROMTableBase = 0;
[16:05:03.214]              // -> [ROMTableBase <= 0x00000000]
[16:05:03.215]            __ap = 0;      // AHB-AP
[16:05:03.215]              // -> [__ap <= 0x00000000]
[16:05:03.215]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:05:03.216]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:05:03.217]              // -> [ROMTableBase <= 0xF0000000]
[16:05:03.217]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:05:03.218]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:05:03.218]              // -> [pidr1 <= 0x00000004]
[16:05:03.218]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:05:03.219]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:05:03.219]              // -> [pidr2 <= 0x0000000A]
[16:05:03.220]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:05:03.220]              // -> [jep106id <= 0x00000020]
[16:05:03.220]          </block>
[16:05:03.221]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:05:03.221]            // if-block "jep106id != 0x20"
[16:05:03.221]              // =>  FALSE
[16:05:03.222]            // skip if-block "jep106id != 0x20"
[16:05:03.222]          </control>
[16:05:03.222]        </sequence>
[16:05:03.223]    </block>
[16:05:03.223]  </sequence>
[16:05:03.223]  
[16:05:03.236]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:05:03.236]  
[16:05:03.237]  <debugvars>
[16:05:03.237]    // Pre-defined
[16:05:03.237]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:05:03.238]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:05:03.238]    __dp=0x00000000
[16:05:03.238]    __ap=0x00000000
[16:05:03.238]    __traceout=0x00000000      (Trace Disabled)
[16:05:03.239]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:05:03.239]    __FlashAddr=0x00000000
[16:05:03.239]    __FlashLen=0x00000000
[16:05:03.240]    __FlashArg=0x00000000
[16:05:03.240]    __FlashOp=0x00000000
[16:05:03.240]    __Result=0x00000000
[16:05:03.241]    
[16:05:03.241]    // User-defined
[16:05:03.241]    DbgMCU_CR=0x00000007
[16:05:03.241]    DbgMCU_APB1_Fz=0x00000000
[16:05:03.241]    DbgMCU_APB2_Fz=0x00000000
[16:05:03.241]    DoOptionByteLoading=0x00000000
[16:05:03.241]  </debugvars>
[16:05:03.242]  
[16:05:03.242]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:05:03.242]    <block atomic="false" info="">
[16:05:03.242]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:05:03.243]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:05:03.243]    </block>
[16:05:03.244]    <block atomic="false" info="DbgMCU registers">
[16:05:03.244]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:05:03.245]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:05:03.246]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:05:03.246]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:05:03.247]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:05:03.248]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:05:03.248]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:05:03.249]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:05:03.249]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:05:03.250]    </block>
[16:05:03.250]  </sequence>
[16:05:03.250]  
[16:05:11.171]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:05:11.171]  
[16:05:11.171]  <debugvars>
[16:05:11.171]    // Pre-defined
[16:05:11.172]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:05:11.172]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:05:11.172]    __dp=0x00000000
[16:05:11.172]    __ap=0x00000000
[16:05:11.172]    __traceout=0x00000000      (Trace Disabled)
[16:05:11.173]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:05:11.173]    __FlashAddr=0x00000000
[16:05:11.173]    __FlashLen=0x00000000
[16:05:11.173]    __FlashArg=0x00000000
[16:05:11.173]    __FlashOp=0x00000000
[16:05:11.174]    __Result=0x00000000
[16:05:11.174]    
[16:05:11.174]    // User-defined
[16:05:11.174]    DbgMCU_CR=0x00000007
[16:05:11.174]    DbgMCU_APB1_Fz=0x00000000
[16:05:11.174]    DbgMCU_APB2_Fz=0x00000000
[16:05:11.175]    DoOptionByteLoading=0x00000000
[16:05:11.175]  </debugvars>
[16:05:11.175]  
[16:05:11.175]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:05:11.175]    <block atomic="false" info="">
[16:05:11.176]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:05:11.176]        // -> [connectionFlash <= 0x00000001]
[16:05:11.176]      __var FLASH_BASE = 0x40022000 ;
[16:05:11.176]        // -> [FLASH_BASE <= 0x40022000]
[16:05:11.176]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:05:11.177]        // -> [FLASH_CR <= 0x40022004]
[16:05:11.177]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:05:11.177]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:05:11.177]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:05:11.177]        // -> [LOCK_BIT <= 0x00000001]
[16:05:11.178]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:05:11.178]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:05:11.178]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:05:11.178]        // -> [FLASH_KEYR <= 0x4002200C]
[16:05:11.178]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:05:11.179]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:05:11.179]      __var FLASH_KEY2 = 0x02030405 ;
[16:05:11.180]        // -> [FLASH_KEY2 <= 0x02030405]
[16:05:11.180]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:05:11.180]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:05:11.180]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:05:11.180]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:05:11.180]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:05:11.181]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:05:11.181]      __var FLASH_CR_Value = 0 ;
[16:05:11.181]        // -> [FLASH_CR_Value <= 0x00000000]
[16:05:11.181]      __var DoDebugPortStop = 1 ;
[16:05:11.181]        // -> [DoDebugPortStop <= 0x00000001]
[16:05:11.182]      __var DP_CTRL_STAT = 0x4 ;
[16:05:11.182]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:05:11.182]      __var DP_SELECT = 0x8 ;
[16:05:11.182]        // -> [DP_SELECT <= 0x00000008]
[16:05:11.182]    </block>
[16:05:11.183]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:05:11.183]      // if-block "connectionFlash && DoOptionByteLoading"
[16:05:11.183]        // =>  FALSE
[16:05:11.183]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:05:11.183]    </control>
[16:05:11.184]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:05:11.184]      // if-block "DoDebugPortStop"
[16:05:11.184]        // =>  TRUE
[16:05:11.184]      <block atomic="false" info="">
[16:05:11.184]        WriteDP(DP_SELECT, 0x00000000);
[16:05:11.185]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:05:11.185]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:05:11.186]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:05:11.186]      </block>
[16:05:11.186]      // end if-block "DoDebugPortStop"
[16:05:11.186]    </control>
[16:05:11.186]  </sequence>
[16:05:11.187]  
[16:09:17.234]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:09:17.234]  
[16:09:17.234]  <debugvars>
[16:09:17.235]    // Pre-defined
[16:09:17.235]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:09:17.235]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:09:17.235]    __dp=0x00000000
[16:09:17.236]    __ap=0x00000000
[16:09:17.236]    __traceout=0x00000000      (Trace Disabled)
[16:09:17.236]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:09:17.236]    __FlashAddr=0x00000000
[16:09:17.237]    __FlashLen=0x00000000
[16:09:17.237]    __FlashArg=0x00000000
[16:09:17.237]    __FlashOp=0x00000000
[16:09:17.237]    __Result=0x00000000
[16:09:17.238]    
[16:09:17.238]    // User-defined
[16:09:17.238]    DbgMCU_CR=0x00000007
[16:09:17.238]    DbgMCU_APB1_Fz=0x00000000
[16:09:17.238]    DbgMCU_APB2_Fz=0x00000000
[16:09:17.238]    DoOptionByteLoading=0x00000000
[16:09:17.238]  </debugvars>
[16:09:17.239]  
[16:09:17.239]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:09:17.239]    <block atomic="false" info="">
[16:09:17.239]      Sequence("CheckID");
[16:09:17.239]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:09:17.240]          <block atomic="false" info="">
[16:09:17.240]            __var pidr1 = 0;
[16:09:17.240]              // -> [pidr1 <= 0x00000000]
[16:09:17.240]            __var pidr2 = 0;
[16:09:17.240]              // -> [pidr2 <= 0x00000000]
[16:09:17.240]            __var jep106id = 0;
[16:09:17.241]              // -> [jep106id <= 0x00000000]
[16:09:17.241]            __var ROMTableBase = 0;
[16:09:17.241]              // -> [ROMTableBase <= 0x00000000]
[16:09:17.242]            __ap = 0;      // AHB-AP
[16:09:17.242]              // -> [__ap <= 0x00000000]
[16:09:17.242]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:09:17.243]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:09:17.243]              // -> [ROMTableBase <= 0xF0000000]
[16:09:17.243]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:09:17.244]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:09:17.244]              // -> [pidr1 <= 0x00000004]
[16:09:17.244]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:09:17.246]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:09:17.246]              // -> [pidr2 <= 0x0000000A]
[16:09:17.246]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:09:17.247]              // -> [jep106id <= 0x00000020]
[16:09:17.247]          </block>
[16:09:17.247]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:09:17.247]            // if-block "jep106id != 0x20"
[16:09:17.247]              // =>  FALSE
[16:09:17.248]            // skip if-block "jep106id != 0x20"
[16:09:17.248]          </control>
[16:09:17.248]        </sequence>
[16:09:17.248]    </block>
[16:09:17.249]  </sequence>
[16:09:17.249]  
[16:09:17.262]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:09:17.262]  
[16:09:17.285]  <debugvars>
[16:09:17.286]    // Pre-defined
[16:09:17.286]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:09:17.286]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:09:17.287]    __dp=0x00000000
[16:09:17.287]    __ap=0x00000000
[16:09:17.287]    __traceout=0x00000000      (Trace Disabled)
[16:09:17.287]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:09:17.289]    __FlashAddr=0x00000000
[16:09:17.289]    __FlashLen=0x00000000
[16:09:17.289]    __FlashArg=0x00000000
[16:09:17.289]    __FlashOp=0x00000000
[16:09:17.289]    __Result=0x00000000
[16:09:17.290]    
[16:09:17.290]    // User-defined
[16:09:17.290]    DbgMCU_CR=0x00000007
[16:09:17.290]    DbgMCU_APB1_Fz=0x00000000
[16:09:17.290]    DbgMCU_APB2_Fz=0x00000000
[16:09:17.290]    DoOptionByteLoading=0x00000000
[16:09:17.291]  </debugvars>
[16:09:17.291]  
[16:09:17.291]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:09:17.291]    <block atomic="false" info="">
[16:09:17.291]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:09:17.292]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:09:17.292]    </block>
[16:09:17.292]    <block atomic="false" info="DbgMCU registers">
[16:09:17.293]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:09:17.293]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:09:17.294]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:09:17.294]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:09:17.296]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:09:17.296]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:09:17.297]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:09:17.297]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:09:17.297]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:09:17.298]    </block>
[16:09:17.298]  </sequence>
[16:09:17.298]  
[16:09:25.253]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:09:25.253]  
[16:09:25.254]  <debugvars>
[16:09:25.254]    // Pre-defined
[16:09:25.254]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:09:25.255]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:09:25.255]    __dp=0x00000000
[16:09:25.255]    __ap=0x00000000
[16:09:25.255]    __traceout=0x00000000      (Trace Disabled)
[16:09:25.256]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:09:25.256]    __FlashAddr=0x00000000
[16:09:25.256]    __FlashLen=0x00000000
[16:09:25.256]    __FlashArg=0x00000000
[16:09:25.257]    __FlashOp=0x00000000
[16:09:25.257]    __Result=0x00000000
[16:09:25.257]    
[16:09:25.257]    // User-defined
[16:09:25.257]    DbgMCU_CR=0x00000007
[16:09:25.257]    DbgMCU_APB1_Fz=0x00000000
[16:09:25.258]    DbgMCU_APB2_Fz=0x00000000
[16:09:25.258]    DoOptionByteLoading=0x00000000
[16:09:25.258]  </debugvars>
[16:09:25.258]  
[16:09:25.259]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:09:25.259]    <block atomic="false" info="">
[16:09:25.259]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:09:25.259]        // -> [connectionFlash <= 0x00000001]
[16:09:25.259]      __var FLASH_BASE = 0x40022000 ;
[16:09:25.260]        // -> [FLASH_BASE <= 0x40022000]
[16:09:25.260]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:09:25.260]        // -> [FLASH_CR <= 0x40022004]
[16:09:25.260]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:09:25.260]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:09:25.261]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:09:25.261]        // -> [LOCK_BIT <= 0x00000001]
[16:09:25.261]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:09:25.261]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:09:25.261]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:09:25.262]        // -> [FLASH_KEYR <= 0x4002200C]
[16:09:25.262]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:09:25.262]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:09:25.262]      __var FLASH_KEY2 = 0x02030405 ;
[16:09:25.262]        // -> [FLASH_KEY2 <= 0x02030405]
[16:09:25.263]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:09:25.263]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:09:25.263]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:09:25.263]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:09:25.264]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:09:25.264]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:09:25.264]      __var FLASH_CR_Value = 0 ;
[16:09:25.265]        // -> [FLASH_CR_Value <= 0x00000000]
[16:09:25.265]      __var DoDebugPortStop = 1 ;
[16:09:25.265]        // -> [DoDebugPortStop <= 0x00000001]
[16:09:25.265]      __var DP_CTRL_STAT = 0x4 ;
[16:09:25.266]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:09:25.266]      __var DP_SELECT = 0x8 ;
[16:09:25.266]        // -> [DP_SELECT <= 0x00000008]
[16:09:25.266]    </block>
[16:09:25.267]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:09:25.267]      // if-block "connectionFlash && DoOptionByteLoading"
[16:09:25.267]        // =>  FALSE
[16:09:25.267]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:09:25.267]    </control>
[16:09:25.267]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:09:25.268]      // if-block "DoDebugPortStop"
[16:09:25.269]        // =>  TRUE
[16:09:25.269]      <block atomic="false" info="">
[16:09:25.269]        WriteDP(DP_SELECT, 0x00000000);
[16:09:25.269]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:09:25.270]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:09:25.270]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:09:25.270]      </block>
[16:09:25.270]      // end if-block "DoDebugPortStop"
[16:09:25.270]    </control>
[16:09:25.270]  </sequence>
[16:09:25.270]  
[16:12:03.883]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:12:03.883]  
[16:12:03.883]  <debugvars>
[16:12:03.883]    // Pre-defined
[16:12:03.884]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:12:03.884]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:12:03.884]    __dp=0x00000000
[16:12:03.885]    __ap=0x00000000
[16:12:03.885]    __traceout=0x00000000      (Trace Disabled)
[16:12:03.885]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:12:03.885]    __FlashAddr=0x00000000
[16:12:03.885]    __FlashLen=0x00000000
[16:12:03.886]    __FlashArg=0x00000000
[16:12:03.886]    __FlashOp=0x00000000
[16:12:03.886]    __Result=0x00000000
[16:12:03.886]    
[16:12:03.886]    // User-defined
[16:12:03.887]    DbgMCU_CR=0x00000007
[16:12:03.887]    DbgMCU_APB1_Fz=0x00000000
[16:12:03.887]    DbgMCU_APB2_Fz=0x00000000
[16:12:03.887]    DoOptionByteLoading=0x00000000
[16:12:03.887]  </debugvars>
[16:12:03.887]  
[16:12:03.888]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:12:03.888]    <block atomic="false" info="">
[16:12:03.888]      Sequence("CheckID");
[16:12:03.888]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:12:03.888]          <block atomic="false" info="">
[16:12:03.888]            __var pidr1 = 0;
[16:12:03.889]              // -> [pidr1 <= 0x00000000]
[16:12:03.889]            __var pidr2 = 0;
[16:12:03.889]              // -> [pidr2 <= 0x00000000]
[16:12:03.889]            __var jep106id = 0;
[16:12:03.889]              // -> [jep106id <= 0x00000000]
[16:12:03.889]            __var ROMTableBase = 0;
[16:12:03.890]              // -> [ROMTableBase <= 0x00000000]
[16:12:03.890]            __ap = 0;      // AHB-AP
[16:12:03.890]              // -> [__ap <= 0x00000000]
[16:12:03.890]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:12:03.891]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:12:03.892]              // -> [ROMTableBase <= 0xF0000000]
[16:12:03.893]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:12:03.894]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:12:03.894]              // -> [pidr1 <= 0x00000004]
[16:12:03.894]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:12:03.895]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:12:03.895]              // -> [pidr2 <= 0x0000000A]
[16:12:03.895]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:12:03.896]              // -> [jep106id <= 0x00000020]
[16:12:03.896]          </block>
[16:12:03.897]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:12:03.897]            // if-block "jep106id != 0x20"
[16:12:03.897]              // =>  FALSE
[16:12:03.897]            // skip if-block "jep106id != 0x20"
[16:12:03.898]          </control>
[16:12:03.898]        </sequence>
[16:12:03.898]    </block>
[16:12:03.899]  </sequence>
[16:12:03.899]  
[16:12:03.910]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:12:03.910]  
[16:12:03.919]  <debugvars>
[16:12:03.919]    // Pre-defined
[16:12:03.920]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:12:03.921]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:12:03.921]    __dp=0x00000000
[16:12:03.922]    __ap=0x00000000
[16:12:03.922]    __traceout=0x00000000      (Trace Disabled)
[16:12:03.923]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:12:03.923]    __FlashAddr=0x00000000
[16:12:03.924]    __FlashLen=0x00000000
[16:12:03.924]    __FlashArg=0x00000000
[16:12:03.924]    __FlashOp=0x00000000
[16:12:03.925]    __Result=0x00000000
[16:12:03.925]    
[16:12:03.925]    // User-defined
[16:12:03.925]    DbgMCU_CR=0x00000007
[16:12:03.927]    DbgMCU_APB1_Fz=0x00000000
[16:12:03.927]    DbgMCU_APB2_Fz=0x00000000
[16:12:03.928]    DoOptionByteLoading=0x00000000
[16:12:03.928]  </debugvars>
[16:12:03.929]  
[16:12:03.929]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:12:03.929]    <block atomic="false" info="">
[16:12:03.930]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:12:03.931]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:12:03.932]    </block>
[16:12:03.932]    <block atomic="false" info="DbgMCU registers">
[16:12:03.932]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:12:03.933]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:12:03.934]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:12:03.934]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:12:03.935]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:12:03.935]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:12:03.936]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:12:03.936]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:12:03.937]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:12:03.937]    </block>
[16:12:03.937]  </sequence>
[16:12:03.938]  
[16:12:11.662]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:12:11.662]  
[16:12:11.662]  <debugvars>
[16:12:11.663]    // Pre-defined
[16:12:11.663]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:12:11.664]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:12:11.664]    __dp=0x00000000
[16:12:11.664]    __ap=0x00000000
[16:12:11.665]    __traceout=0x00000000      (Trace Disabled)
[16:12:11.665]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:12:11.665]    __FlashAddr=0x00000000
[16:12:11.665]    __FlashLen=0x00000000
[16:12:11.666]    __FlashArg=0x00000000
[16:12:11.666]    __FlashOp=0x00000000
[16:12:11.666]    __Result=0x00000000
[16:12:11.666]    
[16:12:11.666]    // User-defined
[16:12:11.667]    DbgMCU_CR=0x00000007
[16:12:11.667]    DbgMCU_APB1_Fz=0x00000000
[16:12:11.667]    DbgMCU_APB2_Fz=0x00000000
[16:12:11.667]    DoOptionByteLoading=0x00000000
[16:12:11.667]  </debugvars>
[16:12:11.668]  
[16:12:11.668]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:12:11.668]    <block atomic="false" info="">
[16:12:11.668]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:12:11.669]        // -> [connectionFlash <= 0x00000001]
[16:12:11.669]      __var FLASH_BASE = 0x40022000 ;
[16:12:11.669]        // -> [FLASH_BASE <= 0x40022000]
[16:12:11.669]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:12:11.669]        // -> [FLASH_CR <= 0x40022004]
[16:12:11.669]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:12:11.670]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:12:11.670]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:12:11.670]        // -> [LOCK_BIT <= 0x00000001]
[16:12:11.671]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:12:11.671]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:12:11.671]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:12:11.671]        // -> [FLASH_KEYR <= 0x4002200C]
[16:12:11.671]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:12:11.672]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:12:11.672]      __var FLASH_KEY2 = 0x02030405 ;
[16:12:11.672]        // -> [FLASH_KEY2 <= 0x02030405]
[16:12:11.672]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:12:11.672]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:12:11.673]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:12:11.673]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:12:11.673]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:12:11.673]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:12:11.673]      __var FLASH_CR_Value = 0 ;
[16:12:11.674]        // -> [FLASH_CR_Value <= 0x00000000]
[16:12:11.674]      __var DoDebugPortStop = 1 ;
[16:12:11.674]        // -> [DoDebugPortStop <= 0x00000001]
[16:12:11.674]      __var DP_CTRL_STAT = 0x4 ;
[16:12:11.674]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:12:11.674]      __var DP_SELECT = 0x8 ;
[16:12:11.675]        // -> [DP_SELECT <= 0x00000008]
[16:12:11.675]    </block>
[16:12:11.675]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:12:11.675]      // if-block "connectionFlash && DoOptionByteLoading"
[16:12:11.675]        // =>  FALSE
[16:12:11.676]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:12:11.676]    </control>
[16:12:11.676]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:12:11.676]      // if-block "DoDebugPortStop"
[16:12:11.676]        // =>  TRUE
[16:12:11.676]      <block atomic="false" info="">
[16:12:11.677]        WriteDP(DP_SELECT, 0x00000000);
[16:12:11.677]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:12:11.677]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:12:11.677]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:12:11.678]      </block>
[16:12:11.680]      // end if-block "DoDebugPortStop"
[16:12:11.680]    </control>
[16:12:11.680]  </sequence>
[16:12:11.681]  
[16:25:34.621]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:25:34.621]  
[16:25:34.634]  <debugvars>
[16:25:34.634]    // Pre-defined
[16:25:34.635]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:25:34.635]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:25:34.636]    __dp=0x00000000
[16:25:34.636]    __ap=0x00000000
[16:25:34.636]    __traceout=0x00000000      (Trace Disabled)
[16:25:34.636]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:25:34.637]    __FlashAddr=0x00000000
[16:25:34.637]    __FlashLen=0x00000000
[16:25:34.637]    __FlashArg=0x00000000
[16:25:34.637]    __FlashOp=0x00000000
[16:25:34.637]    __Result=0x00000000
[16:25:34.637]    
[16:25:34.637]    // User-defined
[16:25:34.638]    DbgMCU_CR=0x00000007
[16:25:34.638]    DbgMCU_APB1_Fz=0x00000000
[16:25:34.638]    DbgMCU_APB2_Fz=0x00000000
[16:25:34.638]    DoOptionByteLoading=0x00000000
[16:25:34.639]  </debugvars>
[16:25:34.639]  
[16:25:34.639]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:25:34.639]    <block atomic="false" info="">
[16:25:34.639]      Sequence("CheckID");
[16:25:34.640]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:25:34.640]          <block atomic="false" info="">
[16:25:34.640]            __var pidr1 = 0;
[16:25:34.640]              // -> [pidr1 <= 0x00000000]
[16:25:34.640]            __var pidr2 = 0;
[16:25:34.640]              // -> [pidr2 <= 0x00000000]
[16:25:34.641]            __var jep106id = 0;
[16:25:34.641]              // -> [jep106id <= 0x00000000]
[16:25:34.641]            __var ROMTableBase = 0;
[16:25:34.641]              // -> [ROMTableBase <= 0x00000000]
[16:25:34.641]            __ap = 0;      // AHB-AP
[16:25:34.641]              // -> [__ap <= 0x00000000]
[16:25:34.642]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:25:34.642]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:25:34.642]              // -> [ROMTableBase <= 0xF0000000]
[16:25:34.642]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:25:34.644]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:25:34.645]              // -> [pidr1 <= 0x00000004]
[16:25:34.645]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:25:34.646]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:25:34.647]              // -> [pidr2 <= 0x0000000A]
[16:25:34.647]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:25:34.647]              // -> [jep106id <= 0x00000020]
[16:25:34.647]          </block>
[16:25:34.648]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:25:34.648]            // if-block "jep106id != 0x20"
[16:25:34.648]              // =>  FALSE
[16:25:34.648]            // skip if-block "jep106id != 0x20"
[16:25:34.649]          </control>
[16:25:34.649]        </sequence>
[16:25:34.649]    </block>
[16:25:34.650]  </sequence>
[16:25:34.650]  
[16:25:34.662]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:25:34.662]  
[16:25:34.688]  <debugvars>
[16:25:34.688]    // Pre-defined
[16:25:34.688]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:25:34.690]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:25:34.690]    __dp=0x00000000
[16:25:34.690]    __ap=0x00000000
[16:25:34.690]    __traceout=0x00000000      (Trace Disabled)
[16:25:34.690]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:25:34.691]    __FlashAddr=0x00000000
[16:25:34.691]    __FlashLen=0x00000000
[16:25:34.691]    __FlashArg=0x00000000
[16:25:34.692]    __FlashOp=0x00000000
[16:25:34.692]    __Result=0x00000000
[16:25:34.692]    
[16:25:34.692]    // User-defined
[16:25:34.692]    DbgMCU_CR=0x00000007
[16:25:34.693]    DbgMCU_APB1_Fz=0x00000000
[16:25:34.693]    DbgMCU_APB2_Fz=0x00000000
[16:25:34.693]    DoOptionByteLoading=0x00000000
[16:25:34.694]  </debugvars>
[16:25:34.694]  
[16:25:34.694]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:25:34.695]    <block atomic="false" info="">
[16:25:34.696]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:25:34.696]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:25:34.697]    </block>
[16:25:34.697]    <block atomic="false" info="DbgMCU registers">
[16:25:34.697]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:25:34.698]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:25:34.698]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:25:34.698]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:25:34.699]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:25:34.699]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:25:34.700]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:25:34.700]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:25:34.701]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:25:34.701]    </block>
[16:25:34.702]  </sequence>
[16:25:34.702]  
[16:25:42.580]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:25:42.580]  
[16:25:42.580]  <debugvars>
[16:25:42.581]    // Pre-defined
[16:25:42.581]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:25:42.581]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:25:42.581]    __dp=0x00000000
[16:25:42.582]    __ap=0x00000000
[16:25:42.582]    __traceout=0x00000000      (Trace Disabled)
[16:25:42.582]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:25:42.583]    __FlashAddr=0x00000000
[16:25:42.583]    __FlashLen=0x00000000
[16:25:42.583]    __FlashArg=0x00000000
[16:25:42.583]    __FlashOp=0x00000000
[16:25:42.583]    __Result=0x00000000
[16:25:42.584]    
[16:25:42.584]    // User-defined
[16:25:42.584]    DbgMCU_CR=0x00000007
[16:25:42.584]    DbgMCU_APB1_Fz=0x00000000
[16:25:42.584]    DbgMCU_APB2_Fz=0x00000000
[16:25:42.584]    DoOptionByteLoading=0x00000000
[16:25:42.585]  </debugvars>
[16:25:42.585]  
[16:25:42.585]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:25:42.586]    <block atomic="false" info="">
[16:25:42.586]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:25:42.586]        // -> [connectionFlash <= 0x00000001]
[16:25:42.586]      __var FLASH_BASE = 0x40022000 ;
[16:25:42.586]        // -> [FLASH_BASE <= 0x40022000]
[16:25:42.587]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:25:42.587]        // -> [FLASH_CR <= 0x40022004]
[16:25:42.587]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:25:42.587]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:25:42.588]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:25:42.588]        // -> [LOCK_BIT <= 0x00000001]
[16:25:42.588]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:25:42.588]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:25:42.588]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:25:42.589]        // -> [FLASH_KEYR <= 0x4002200C]
[16:25:42.589]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:25:42.589]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:25:42.589]      __var FLASH_KEY2 = 0x02030405 ;
[16:25:42.589]        // -> [FLASH_KEY2 <= 0x02030405]
[16:25:42.589]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:25:42.590]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:25:42.590]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:25:42.590]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:25:42.590]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:25:42.590]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:25:42.590]      __var FLASH_CR_Value = 0 ;
[16:25:42.591]        // -> [FLASH_CR_Value <= 0x00000000]
[16:25:42.591]      __var DoDebugPortStop = 1 ;
[16:25:42.591]        // -> [DoDebugPortStop <= 0x00000001]
[16:25:42.592]      __var DP_CTRL_STAT = 0x4 ;
[16:25:42.592]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:25:42.592]      __var DP_SELECT = 0x8 ;
[16:25:42.592]        // -> [DP_SELECT <= 0x00000008]
[16:25:42.593]    </block>
[16:25:42.593]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:25:42.593]      // if-block "connectionFlash && DoOptionByteLoading"
[16:25:42.593]        // =>  FALSE
[16:25:42.593]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:25:42.593]    </control>
[16:25:42.594]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:25:42.594]      // if-block "DoDebugPortStop"
[16:25:42.594]        // =>  TRUE
[16:25:42.594]      <block atomic="false" info="">
[16:25:42.594]        WriteDP(DP_SELECT, 0x00000000);
[16:25:42.595]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:25:42.595]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:25:42.596]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:25:42.596]      </block>
[16:25:42.597]      // end if-block "DoDebugPortStop"
[16:25:42.597]    </control>
[16:25:42.598]  </sequence>
[16:25:42.598]  
[16:36:51.474]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:36:51.474]  
[16:36:51.474]  <debugvars>
[16:36:51.475]    // Pre-defined
[16:36:51.475]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:36:51.475]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:36:51.476]    __dp=0x00000000
[16:36:51.476]    __ap=0x00000000
[16:36:51.476]    __traceout=0x00000000      (Trace Disabled)
[16:36:51.477]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:36:51.477]    __FlashAddr=0x00000000
[16:36:51.477]    __FlashLen=0x00000000
[16:36:51.477]    __FlashArg=0x00000000
[16:36:51.477]    __FlashOp=0x00000000
[16:36:51.478]    __Result=0x00000000
[16:36:51.478]    
[16:36:51.478]    // User-defined
[16:36:51.478]    DbgMCU_CR=0x00000007
[16:36:51.479]    DbgMCU_APB1_Fz=0x00000000
[16:36:51.479]    DbgMCU_APB2_Fz=0x00000000
[16:36:51.479]    DoOptionByteLoading=0x00000000
[16:36:51.480]  </debugvars>
[16:36:51.480]  
[16:36:51.480]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:36:51.480]    <block atomic="false" info="">
[16:36:51.481]      Sequence("CheckID");
[16:36:51.481]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:36:51.481]          <block atomic="false" info="">
[16:36:51.482]            __var pidr1 = 0;
[16:36:51.482]              // -> [pidr1 <= 0x00000000]
[16:36:51.482]            __var pidr2 = 0;
[16:36:51.482]              // -> [pidr2 <= 0x00000000]
[16:36:51.482]            __var jep106id = 0;
[16:36:51.483]              // -> [jep106id <= 0x00000000]
[16:36:51.483]            __var ROMTableBase = 0;
[16:36:51.483]              // -> [ROMTableBase <= 0x00000000]
[16:36:51.484]            __ap = 0;      // AHB-AP
[16:36:51.484]              // -> [__ap <= 0x00000000]
[16:36:51.484]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:36:51.485]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:36:51.486]              // -> [ROMTableBase <= 0xF0000000]
[16:36:51.486]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:36:51.487]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:36:51.487]              // -> [pidr1 <= 0x00000004]
[16:36:51.487]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:36:51.488]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:36:51.488]              // -> [pidr2 <= 0x0000000A]
[16:36:51.488]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:36:51.489]              // -> [jep106id <= 0x00000020]
[16:36:51.489]          </block>
[16:36:51.490]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:36:51.490]            // if-block "jep106id != 0x20"
[16:36:51.490]              // =>  FALSE
[16:36:51.490]            // skip if-block "jep106id != 0x20"
[16:36:51.490]          </control>
[16:36:51.491]        </sequence>
[16:36:51.491]    </block>
[16:36:51.491]  </sequence>
[16:36:51.491]  
[16:36:51.504]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:36:51.504]  
[16:36:51.505]  <debugvars>
[16:36:51.505]    // Pre-defined
[16:36:51.505]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:36:51.505]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:36:51.506]    __dp=0x00000000
[16:36:51.506]    __ap=0x00000000
[16:36:51.507]    __traceout=0x00000000      (Trace Disabled)
[16:36:51.507]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:36:51.507]    __FlashAddr=0x00000000
[16:36:51.507]    __FlashLen=0x00000000
[16:36:51.508]    __FlashArg=0x00000000
[16:36:51.508]    __FlashOp=0x00000000
[16:36:51.508]    __Result=0x00000000
[16:36:51.508]    
[16:36:51.508]    // User-defined
[16:36:51.509]    DbgMCU_CR=0x00000007
[16:36:51.509]    DbgMCU_APB1_Fz=0x00000000
[16:36:51.509]    DbgMCU_APB2_Fz=0x00000000
[16:36:51.509]    DoOptionByteLoading=0x00000000
[16:36:51.510]  </debugvars>
[16:36:51.510]  
[16:36:51.510]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:36:51.510]    <block atomic="false" info="">
[16:36:51.510]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:36:51.511]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:36:51.511]    </block>
[16:36:51.511]    <block atomic="false" info="DbgMCU registers">
[16:36:51.512]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:36:51.513]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:36:51.514]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:36:51.514]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:36:51.515]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:36:51.515]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:36:51.516]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:36:51.517]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:36:51.518]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:36:51.518]    </block>
[16:36:51.518]  </sequence>
[16:36:51.518]  
[16:36:59.499]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:36:59.499]  
[16:36:59.500]  <debugvars>
[16:36:59.500]    // Pre-defined
[16:36:59.500]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:36:59.501]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:36:59.501]    __dp=0x00000000
[16:36:59.501]    __ap=0x00000000
[16:36:59.501]    __traceout=0x00000000      (Trace Disabled)
[16:36:59.502]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:36:59.502]    __FlashAddr=0x00000000
[16:36:59.502]    __FlashLen=0x00000000
[16:36:59.502]    __FlashArg=0x00000000
[16:36:59.502]    __FlashOp=0x00000000
[16:36:59.503]    __Result=0x00000000
[16:36:59.503]    
[16:36:59.503]    // User-defined
[16:36:59.504]    DbgMCU_CR=0x00000007
[16:36:59.504]    DbgMCU_APB1_Fz=0x00000000
[16:36:59.504]    DbgMCU_APB2_Fz=0x00000000
[16:36:59.504]    DoOptionByteLoading=0x00000000
[16:36:59.504]  </debugvars>
[16:36:59.505]  
[16:36:59.505]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:36:59.505]    <block atomic="false" info="">
[16:36:59.505]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:36:59.505]        // -> [connectionFlash <= 0x00000001]
[16:36:59.505]      __var FLASH_BASE = 0x40022000 ;
[16:36:59.506]        // -> [FLASH_BASE <= 0x40022000]
[16:36:59.506]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:36:59.507]        // -> [FLASH_CR <= 0x40022004]
[16:36:59.507]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:36:59.507]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:36:59.507]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:36:59.508]        // -> [LOCK_BIT <= 0x00000001]
[16:36:59.508]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:36:59.508]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:36:59.508]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:36:59.508]        // -> [FLASH_KEYR <= 0x4002200C]
[16:36:59.508]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:36:59.508]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:36:59.509]      __var FLASH_KEY2 = 0x02030405 ;
[16:36:59.509]        // -> [FLASH_KEY2 <= 0x02030405]
[16:36:59.509]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:36:59.510]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:36:59.510]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:36:59.510]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:36:59.510]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:36:59.510]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:36:59.510]      __var FLASH_CR_Value = 0 ;
[16:36:59.510]        // -> [FLASH_CR_Value <= 0x00000000]
[16:36:59.510]      __var DoDebugPortStop = 1 ;
[16:36:59.510]        // -> [DoDebugPortStop <= 0x00000001]
[16:36:59.510]      __var DP_CTRL_STAT = 0x4 ;
[16:36:59.510]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:36:59.510]      __var DP_SELECT = 0x8 ;
[16:36:59.512]        // -> [DP_SELECT <= 0x00000008]
[16:36:59.512]    </block>
[16:36:59.512]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:36:59.513]      // if-block "connectionFlash && DoOptionByteLoading"
[16:36:59.513]        // =>  FALSE
[16:36:59.513]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:36:59.513]    </control>
[16:36:59.513]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:36:59.513]      // if-block "DoDebugPortStop"
[16:36:59.514]        // =>  TRUE
[16:36:59.514]      <block atomic="false" info="">
[16:36:59.514]        WriteDP(DP_SELECT, 0x00000000);
[16:36:59.515]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:36:59.515]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:36:59.515]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:36:59.515]      </block>
[16:36:59.516]      // end if-block "DoDebugPortStop"
[16:36:59.516]    </control>
[16:36:59.516]  </sequence>
[16:36:59.516]  
[16:42:56.220]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:42:56.220]  
[16:42:56.220]  <debugvars>
[16:42:56.222]    // Pre-defined
[16:42:56.222]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:42:56.222]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:42:56.223]    __dp=0x00000000
[16:42:56.223]    __ap=0x00000000
[16:42:56.223]    __traceout=0x00000000      (Trace Disabled)
[16:42:56.223]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:42:56.224]    __FlashAddr=0x00000000
[16:42:56.224]    __FlashLen=0x00000000
[16:42:56.225]    __FlashArg=0x00000000
[16:42:56.225]    __FlashOp=0x00000000
[16:42:56.225]    __Result=0x00000000
[16:42:56.225]    
[16:42:56.225]    // User-defined
[16:42:56.226]    DbgMCU_CR=0x00000007
[16:42:56.226]    DbgMCU_APB1_Fz=0x00000000
[16:42:56.226]    DbgMCU_APB2_Fz=0x00000000
[16:42:56.226]    DoOptionByteLoading=0x00000000
[16:42:56.227]  </debugvars>
[16:42:56.227]  
[16:42:56.227]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:42:56.227]    <block atomic="false" info="">
[16:42:56.227]      Sequence("CheckID");
[16:42:56.227]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:42:56.228]          <block atomic="false" info="">
[16:42:56.228]            __var pidr1 = 0;
[16:42:56.228]              // -> [pidr1 <= 0x00000000]
[16:42:56.228]            __var pidr2 = 0;
[16:42:56.228]              // -> [pidr2 <= 0x00000000]
[16:42:56.229]            __var jep106id = 0;
[16:42:56.229]              // -> [jep106id <= 0x00000000]
[16:42:56.229]            __var ROMTableBase = 0;
[16:42:56.229]              // -> [ROMTableBase <= 0x00000000]
[16:42:56.229]            __ap = 0;      // AHB-AP
[16:42:56.230]              // -> [__ap <= 0x00000000]
[16:42:56.230]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:42:56.230]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:42:56.231]              // -> [ROMTableBase <= 0xF0000000]
[16:42:56.231]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:42:56.232]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:42:56.232]              // -> [pidr1 <= 0x00000004]
[16:42:56.232]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:42:56.233]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:42:56.234]              // -> [pidr2 <= 0x0000000A]
[16:42:56.234]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:42:56.234]              // -> [jep106id <= 0x00000020]
[16:42:56.235]          </block>
[16:42:56.236]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:42:56.236]            // if-block "jep106id != 0x20"
[16:42:56.236]              // =>  FALSE
[16:42:56.237]            // skip if-block "jep106id != 0x20"
[16:42:56.237]          </control>
[16:42:56.237]        </sequence>
[16:42:56.237]    </block>
[16:42:56.237]  </sequence>
[16:42:56.238]  
[16:42:56.250]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:42:56.250]  
[16:42:56.250]  <debugvars>
[16:42:56.251]    // Pre-defined
[16:42:56.251]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:42:56.251]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:42:56.251]    __dp=0x00000000
[16:42:56.252]    __ap=0x00000000
[16:42:56.252]    __traceout=0x00000000      (Trace Disabled)
[16:42:56.252]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:42:56.252]    __FlashAddr=0x00000000
[16:42:56.253]    __FlashLen=0x00000000
[16:42:56.253]    __FlashArg=0x00000000
[16:42:56.253]    __FlashOp=0x00000000
[16:42:56.253]    __Result=0x00000000
[16:42:56.253]    
[16:42:56.253]    // User-defined
[16:42:56.254]    DbgMCU_CR=0x00000007
[16:42:56.254]    DbgMCU_APB1_Fz=0x00000000
[16:42:56.254]    DbgMCU_APB2_Fz=0x00000000
[16:42:56.254]    DoOptionByteLoading=0x00000000
[16:42:56.254]  </debugvars>
[16:42:56.254]  
[16:42:56.255]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:42:56.255]    <block atomic="false" info="">
[16:42:56.255]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:42:56.256]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:42:56.256]    </block>
[16:42:56.256]    <block atomic="false" info="DbgMCU registers">
[16:42:56.256]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:42:56.257]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:42:56.258]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:42:56.258]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:42:56.259]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:42:56.260]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:42:56.261]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:42:56.262]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:42:56.262]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:42:56.262]    </block>
[16:42:56.262]  </sequence>
[16:42:56.263]  
[16:43:04.286]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:43:04.286]  
[16:43:04.287]  <debugvars>
[16:43:04.287]    // Pre-defined
[16:43:04.288]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:43:04.288]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:43:04.288]    __dp=0x00000000
[16:43:04.288]    __ap=0x00000000
[16:43:04.289]    __traceout=0x00000000      (Trace Disabled)
[16:43:04.289]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:43:04.289]    __FlashAddr=0x00000000
[16:43:04.291]    __FlashLen=0x00000000
[16:43:04.291]    __FlashArg=0x00000000
[16:43:04.291]    __FlashOp=0x00000000
[16:43:04.292]    __Result=0x00000000
[16:43:04.292]    
[16:43:04.292]    // User-defined
[16:43:04.292]    DbgMCU_CR=0x00000007
[16:43:04.292]    DbgMCU_APB1_Fz=0x00000000
[16:43:04.293]    DbgMCU_APB2_Fz=0x00000000
[16:43:04.293]    DoOptionByteLoading=0x00000000
[16:43:04.293]  </debugvars>
[16:43:04.293]  
[16:43:04.294]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:43:04.294]    <block atomic="false" info="">
[16:43:04.294]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:43:04.294]        // -> [connectionFlash <= 0x00000001]
[16:43:04.294]      __var FLASH_BASE = 0x40022000 ;
[16:43:04.294]        // -> [FLASH_BASE <= 0x40022000]
[16:43:04.295]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:43:04.295]        // -> [FLASH_CR <= 0x40022004]
[16:43:04.295]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:43:04.295]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:43:04.295]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:43:04.296]        // -> [LOCK_BIT <= 0x00000001]
[16:43:04.296]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:43:04.296]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:43:04.296]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:43:04.296]        // -> [FLASH_KEYR <= 0x4002200C]
[16:43:04.296]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:43:04.297]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:43:04.297]      __var FLASH_KEY2 = 0x02030405 ;
[16:43:04.297]        // -> [FLASH_KEY2 <= 0x02030405]
[16:43:04.297]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:43:04.297]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:43:04.298]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:43:04.298]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:43:04.298]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:43:04.298]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:43:04.298]      __var FLASH_CR_Value = 0 ;
[16:43:04.298]        // -> [FLASH_CR_Value <= 0x00000000]
[16:43:04.299]      __var DoDebugPortStop = 1 ;
[16:43:04.299]        // -> [DoDebugPortStop <= 0x00000001]
[16:43:04.299]      __var DP_CTRL_STAT = 0x4 ;
[16:43:04.299]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:43:04.300]      __var DP_SELECT = 0x8 ;
[16:43:04.300]        // -> [DP_SELECT <= 0x00000008]
[16:43:04.300]    </block>
[16:43:04.301]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:43:04.301]      // if-block "connectionFlash && DoOptionByteLoading"
[16:43:04.301]        // =>  FALSE
[16:43:04.301]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:43:04.302]    </control>
[16:43:04.302]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:43:04.302]      // if-block "DoDebugPortStop"
[16:43:04.302]        // =>  TRUE
[16:43:04.302]      <block atomic="false" info="">
[16:43:04.302]        WriteDP(DP_SELECT, 0x00000000);
[16:43:04.302]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:43:04.303]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:43:04.303]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:43:04.303]      </block>
[16:43:04.304]      // end if-block "DoDebugPortStop"
[16:43:04.304]    </control>
[16:43:04.304]  </sequence>
[16:43:04.304]  
