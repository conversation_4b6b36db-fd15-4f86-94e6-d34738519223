/**
  ******************************************************************************
  * @file    main_l072.c 
  * <AUTHOR> for STM32L072
  * @version V1.0.0
  * @date    2024/12/26
  * @brief   Bootloader main program for STM32L072 water meter collector
  *          固件升级引导程序主文件，适配STM32L072水表采集器
  ******************************************************************************
  * @attention
  * 
  * 本文件是基于STM32F103的bootloader适配到STM32L072的版本
  * 保持原有的业务逻辑和数据解析逻辑不变，仅修改硬件相关部分
  * 
  * 主要功能：
  * 1. 检查升级标志，决定是否进入升级模式
  * 2. 如果有有效应用程序且无升级标志，则跳转到应用程序
  * 3. 如果需要升级，则进入IAP模式接收固件数据
  * 4. 支持加密固件数据的接收和解密
  * 5. 升级完成后重启进入新应用程序
  ******************************************************************************
  */ 

/* Includes ------------------------------------------------------------------*/
#include <stdio.h>                    /* 标准I/O库 */
#include <ctype.h>                    /* 字符处理函数 */
#include <string.h>                   /* 字符串和内存操作函数 */
#include <stdint.h>                   /* 标准整型定义 */
#include "stm32l0xx_hal.h"           /* STM32L0 HAL库 */
#include "stm32l0xx_hal_flash.h"     /* Flash操作库 */
#include "stm32l0xx_hal_iwdg.h"      /* 独立看门狗库 */
#include "stm32l0xx_hal_uart.h"      /* UART库 */
#include "bsp_board.h"               /* 板级支持包 */
#include "uart.h"                    /* 串口驱动 */
#include "stm32_flash.h"             /* Flash操作接口 */
#include "user_config.h"             /* 用户配置 */

/* 数据类型定义 ------------------------------------------------------------------*/
typedef void (*pFunction)(void);        /* 函数指针类型定义 */

/* 宏定义和常量 ------------------------------------------------------------------*/
#define ALARM_CONFIG_KEY_LEN 8          /* 加密密钥长度 */
#define BUFF_LEN 1032                   /* 数据缓冲区长度 */
#define UPDATA_FLAG_ADDR 128            /* 升级标志存储地址(EEPROM偏移地址) */
#define UPDATA_FLAG 0xFF                /* 升级标志值 */

/* STM32L072应用程序相关定义 */
#define STM32L072_APP_SIZE 96           /* 应用程序最大占用空间(KB) - STM32L072有128KB Flash */
#define USER_FLASH_FIRST_PAGE_ADDRESS 0x08004000  /* 应用程序起始地址(16KB bootloader) */

/* UART相关定义 */
#define BOOTLOADER_UART_BAUDRATE 115200 /* Bootloader串口波特率 */

/* 全局变量定义 ------------------------------------------------------------------*/
pFunction Jump_To_Application;          /* 应用程序跳转函数指针 */
uint32_t JumpAddress;                   /* 跳转地址 */
static volatile uint32_t FlashWriteAddress; /* Flash写入地址 */
uint8_t Parity = 0;                     /* 校验位 */
uint8_t configKey[ALARM_CONFIG_KEY_LEN]; /* 加密密钥数组 */

/* 串口相关变量 - 使用项目中的serial_485 */
extern SERIAL serial_485;               /* 使用项目中的485串口 */
uint8_t uart_rx_buffer[BUFF_LEN];       /* UART接收缓冲区 */
uint16_t uart_rx_count = 0;             /* UART接收计数 */

/* 应答帧定义 */
uint8_t reCmd[6] = {0x7E, 0x00, 0x04, 0x00, 0x00, 0xCE}; /* 应答帧格式 */

/* CRC16校验表 - 保持原有的CRC校验逻辑不变 */
uint8_t aucCRCHi[] = {
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
    0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
    0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40
};

uint8_t aucCRCLo[] = {
    0x00, 0xC0, 0xC1, 0x01, 0xC3, 0x03, 0x02, 0xC2, 0xC6, 0x06, 0x07, 0xC7,
    0x05, 0xC5, 0xC4, 0x04, 0xCC, 0x0C, 0x0D, 0xCD, 0x0F, 0xCF, 0xCE, 0x0E,
    0x0A, 0xCA, 0xCB, 0x0B, 0xC9, 0x09, 0x08, 0xC8, 0xD8, 0x18, 0x19, 0xD9,
    0x1B, 0xDB, 0xDA, 0x1A, 0x1E, 0xDE, 0xDF, 0x1F, 0xDD, 0x1D, 0x1C, 0xDC,
    0x14, 0xD4, 0xD5, 0x15, 0xD7, 0x17, 0x16, 0xD6, 0xD2, 0x12, 0x13, 0xD3,
    0x11, 0xD1, 0xD0, 0x10, 0xF0, 0x30, 0x31, 0xF1, 0x33, 0xF3, 0xF2, 0x32,
    0x36, 0xF6, 0xF7, 0x37, 0xF5, 0x35, 0x34, 0xF4, 0x3C, 0xFC, 0xFD, 0x3D,
    0xFF, 0x3F, 0x3E, 0xFE, 0xFA, 0x3A, 0x3B, 0xFB, 0x39, 0xF9, 0xF8, 0x38,
    0x28, 0xE8, 0xE9, 0x29, 0xEB, 0x2B, 0x2A, 0xEA, 0xEE, 0x2E, 0x2F, 0xEF,
    0x2D, 0xED, 0xEC, 0x2C, 0xE4, 0x24, 0x25, 0xE5, 0x27, 0xE7, 0xE6, 0x26,
    0x22, 0xE2, 0xE3, 0x23, 0xE1, 0x21, 0x20, 0xE0, 0xA0, 0x60, 0x61, 0xA1,
    0x63, 0xA3, 0xA2, 0x62, 0x66, 0xA6, 0xA7, 0x67, 0xA5, 0x65, 0x64, 0xA4,
    0x6C, 0xAC, 0xAD, 0x6D, 0xAF, 0x6F, 0x6E, 0xAE, 0xAA, 0x6A, 0x6B, 0xAB,
    0x69, 0xA9, 0xA8, 0x68, 0x78, 0xB8, 0xB9, 0x79, 0xBB, 0x7B, 0x7A, 0xBA,
    0xBE, 0x7E, 0x7F, 0xBF, 0x7D, 0xBD, 0xBC, 0x7C, 0xB4, 0x74, 0x75, 0xB5,
    0x77, 0xB7, 0xB6, 0x76, 0x72, 0xB2, 0xB3, 0x73, 0xB1, 0x71, 0x70, 0xB0,
    0x50, 0x90, 0x91, 0x51, 0x93, 0x53, 0x52, 0x92, 0x96, 0x56, 0x57, 0x97,
    0x55, 0x95, 0x94, 0x54, 0x9C, 0x5C, 0x5D, 0x9D, 0x5F, 0x9F, 0x9E, 0x5E,
    0x5A, 0x9A, 0x9B, 0x5B, 0x99, 0x59, 0x58, 0x98, 0x88, 0x48, 0x49, 0x89,
    0x4B, 0x8B, 0x8A, 0x4A, 0x4E, 0x8E, 0x8F, 0x4F, 0x8D, 0x4D, 0x4C, 0x8C,
    0x44, 0x84, 0x85, 0x45, 0x87, 0x47, 0x46, 0x86, 0x82, 0x42, 0x43, 0x83,
    0x41, 0x81, 0x80, 0x40
};

/* 函数声明 ------------------------------------------------------------------*/
void Bootloader_Init(void);
void IWDG_Init(void);
void InterruptConfig(void);
uint8_t DecryptConfigure(uint8_t frameNo, uint16_t cmdLen, uint8_t* configUsartRcvDataBuffer);
void memCpy(void* psrc, void* pdest, uint32_t size);
void DecryptKeyConfigure(uint8_t a, uint8_t b);
uint16_t usMBCRC16(uint8_t* pucFrame, uint16_t usLen);
void returnFrame(uint8_t type, uint8_t status);
void recvData(uint8_t *pframe, uint16_t dataLen);
uint16_t uart_receive_data(uint8_t *buffer, uint16_t max_len, uint32_t timeout);
void uart_send_data(uint8_t *data, uint16_t len);
HAL_StatusTypeDef Flash_Erase_Pages(uint32_t start_address, uint32_t num_pages);
HAL_StatusTypeDef Flash_Write_Data(uint32_t address, uint32_t *data, uint32_t length);
uint8_t EEPROM_Read_Byte(uint32_t address);
void EEPROM_Write_Byte(uint32_t address, uint8_t data);
void HAL_Delay_Custom(uint32_t delay_ms);
uint16_t get_serial_data(uint8_t *buffer, uint16_t max_len);

/**
  * @brief  中断配置函数 - 适配STM32L072
  * @details 设置向量表基地址为Flash起始地址
  * @param  None
  * @retval None
  */
void InterruptConfig(void)
{   
  /* 设置向量表基地址为Flash起始地址0x08000000 */
  SCB->VTOR = FLASH_BASE;
}

/**
  * @brief  Bootloader初始化函数 - 参考app/main.c的初始化方式
  * @details 初始化系统必要的外设，包括时钟、GPIO、串口、EEPROM等
  * @param  None
  * @retval None
  */
void Bootloader_Init(void)
{
  /* HAL库初始化 */
  HAL_Init();

  /* 使能PWR时钟 - 参考app/main.c */
  __HAL_RCC_PWR_CLK_ENABLE();

  /* 初始化EEPROM - 参考app/main.c */
  EEPROM_Init();

  /* 读取配置信息 - 使用默认配置用于串口初始化 */
  WATER_METER_INFO default_uart_config;
  default_uart_config.baud_rate_id = BAUD_RATE_ID_115200;  /* Bootloader使用115200波特率 */
  default_uart_config.data_bit_id = DATA_BIT_ID_8;
  default_uart_config.parity_id = PARITY_ID_NONE;
  default_uart_config.stop_bit_id = STOP_BIT_ID_1;

  /* 初始化板级支持包 - 参考app/main.c */
  bsp_board_init();

  /* 初始化485串口 - 参考app/main.c，使用项目中的serial_485 */
  serial_485_init(default_uart_config);
}

/**
  * @brief  独立看门狗初始化 - 适配STM32L072
  * @details 配置IWDG，超时时间约为1秒
  * @param  None
  * @retval None
  */
void IWDG_Init(void)
{
  IWDG_HandleTypeDef hiwdg;
  
  hiwdg.Instance = IWDG;
  hiwdg.Init.Prescaler = IWDG_PRESCALER_32;  /* LSI/32 = 37KHz/32 ≈ 1.15KHz */
  hiwdg.Init.Reload = 1000;                  /* 约1秒超时 */
  
  if (HAL_IWDG_Init(&hiwdg) != HAL_OK)
  {
    while(1); /* 看门狗初始化失败 */
  }
}

/**
  * @brief  UART数据发送函数 - 使用项目中的serial_485
  * @details 通过serial_485发送数据
  * @param  data: 要发送的数据指针
  * @param  len: 数据长度
  * @retval None
  */
void uart_send_data(uint8_t *data, uint16_t len)
{
  serial_485_send_dat(data, len);
}

/**
  * @brief  获取串口接收数据函数 - 使用项目中的serial_485
  * @details 从serial_485获取接收到的数据
  * @param  buffer: 接收缓冲区指针
  * @param  max_len: 最大接收长度
  * @retval 实际接收到的数据长度
  */
uint16_t get_serial_data(uint8_t *buffer, uint16_t max_len)
{
  uint16_t len = 0;

  /* 检查是否有数据接收 */
  if (serial_485.rx_count > 0 && serial_485.byte_timeout == 0)
  {
    /* 复制数据到缓冲区 */
    len = (serial_485.rx_count > max_len) ? max_len : serial_485.rx_count;
    memcpy(buffer, serial_485.rx_buffer, len);

    /* 清空接收缓冲区 */
    memset((void*)&serial_485, 0, sizeof(SERIAL));
  }

  return len;
}

/**
  * @brief  UART数据接收函数 - 带超时的数据接收
  * @details 等待并接收数据，带超时处理
  * @param  buffer: 接收缓冲区指针
  * @param  max_len: 最大接收长度
  * @param  timeout: 超时时间(ms)
  * @retval 实际接收到的数据长度
  */
uint16_t uart_receive_data(uint8_t *buffer, uint16_t max_len, uint32_t timeout)
{
  uint32_t start_time = HAL_GetTick();
  uint16_t len = 0;

  /* 清空接收缓冲区 */
  memset((void*)&serial_485, 0, sizeof(SERIAL));

  /* 等待数据接收 */
  while ((HAL_GetTick() - start_time) < timeout)
  {
    len = get_serial_data(buffer, max_len);
    if (len > 0)
    {
      break;
    }
    HAL_Delay(1); /* 短暂延时，避免CPU占用过高 */
  }

  return len;
}

/**
  * @brief  自定义延时函数
  * @details 使用HAL_Delay实现毫秒级延时
  * @param  delay_ms: 延时时间(毫秒)
  * @retval None
  */
void HAL_Delay_Custom(uint32_t delay_ms)
{
  HAL_Delay(delay_ms);
}

/**
  * @brief  EEPROM字节读取函数 - 适配STM32L072
  * @details 从内部EEPROM读取一个字节
  * @param  address: EEPROM地址偏移
  * @retval 读取的字节值
  */
uint8_t EEPROM_Read_Byte(uint32_t address)
{
  /* STM32L072内部EEPROM起始地址为0x08080000 */
  uint32_t eeprom_addr = 0x08080000 + address;
  return *(uint8_t*)eeprom_addr;
}

/**
  * @brief  EEPROM字节写入函数 - 适配STM32L072
  * @details 向内部EEPROM写入一个字节
  * @param  address: EEPROM地址偏移
  * @param  data: 要写入的字节值
  * @retval None
  */
void EEPROM_Write_Byte(uint32_t address, uint8_t data)
{
  uint32_t eeprom_addr = 0x08080000 + address;

  /* 解锁EEPROM */
  HAL_FLASHEx_DATAEEPROM_Unlock();

  /* 写入数据 */
  HAL_FLASHEx_DATAEEPROM_Program(FLASH_TYPEPROGRAMDATA_BYTE, eeprom_addr, data);

  /* 锁定EEPROM */
  HAL_FLASHEx_DATAEEPROM_Lock();
}

/**
  * @brief  Flash页擦除函数 - 适配STM32L072
  * @details 擦除指定起始地址的Flash页
  * @param  start_address: 起始地址
  * @param  num_pages: 要擦除的页数
  * @retval HAL状态
  */
HAL_StatusTypeDef Flash_Erase_Pages(uint32_t start_address, uint32_t num_pages)
{
  FLASH_EraseInitTypeDef EraseInitStruct;
  uint32_t PageError = 0;
  HAL_StatusTypeDef status;

  /* 解锁Flash */
  HAL_FLASH_Unlock();

  /* 配置擦除参数 */
  EraseInitStruct.TypeErase = FLASH_TYPEERASE_PAGES;
  EraseInitStruct.PageAddress = start_address;
  EraseInitStruct.NbPages = num_pages;

  /* 执行擦除 */
  status = HAL_FLASHEx_Erase(&EraseInitStruct, &PageError);

  /* 锁定Flash */
  HAL_FLASH_Lock();

  return status;
}

/**
  * @brief  Flash数据写入函数 - 适配STM32L072
  * @details 向Flash写入数据
  * @param  address: 写入地址
  * @param  data: 数据指针
  * @param  length: 数据长度(字节数)
  * @retval HAL状态
  */
HAL_StatusTypeDef Flash_Write_Data(uint32_t address, uint32_t *data, uint32_t length)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint32_t i;

  /* 解锁Flash */
  HAL_FLASH_Unlock();

  /* 按字(32位)写入数据 */
  for (i = 0; i < length / 4; i++)
  {
    status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, address + (i * 4), data[i]);
    if (status != HAL_OK)
    {
      break;
    }
  }

  /* 锁定Flash */
  HAL_FLASH_Lock();

  return status;
}

/**
  * @brief  内存拷贝函数 - 保持原有逻辑不变
  * @details 自定义内存拷贝函数，从源地址拷贝数据到目标地址
  * @param  psrc: 源地址指针
  * @param  pdest: 目标地址指针
  * @param  size: 拷贝字节数
  * @retval None
  */
void memCpy(void* psrc, void* pdest, uint32_t size)
{
  while(size > 0)
  {
    *((int8_t*)pdest + size - 1) = *((int8_t*)psrc + size - 1);
    size--;
  }
}

/**
  * @brief  密钥配置函数 - 保持原有逻辑不变
  * @details 交换密钥数组中指定位置的两个字节，用于密钥变换
  * @param  a: 第一个位置索引
  * @param  b: 第二个位置索引
  * @retval None
  */
void DecryptKeyConfigure(uint8_t a, uint8_t b)
{
  uint8_t configKey_temp = 0x00;

  configKey_temp = configKey[a];
  configKey[a] = configKey[b];
  configKey[b] = configKey_temp;
}

/**
  * @brief  CRC16校验函数 - 保持原有逻辑不变
  * @details 计算数据的CRC16校验值，使用Modbus CRC16算法
  * @param  pucFrame: 数据帧指针
  * @param  usLen: 数据长度
  * @retval CRC16校验值
  */
uint16_t usMBCRC16(uint8_t* pucFrame, uint16_t usLen)
{
  uint8_t ucCRCHi = 0xFF;
  uint8_t ucCRCLo = 0xFF;
  int iIndex;
  int i = 0;

  while(usLen-- > 0)
  {
    iIndex = ucCRCLo ^ pucFrame[i++];
    ucCRCLo = (uint8_t)(ucCRCHi ^ aucCRCHi[iIndex]);
    ucCRCHi = aucCRCLo[iIndex];
  }
  return (((uint16_t)ucCRCHi << 8) | ucCRCLo);
}

/**
  * @brief  应答帧发送函数 - 使用项目中的serial_485
  * @details 构造并发送应答帧给上位机
  * @param  type: 应答类型
  * @param  status: 状态码
  * @retval None
  */
void returnFrame(uint8_t type, uint8_t status)
{
  reCmd[3] = type;
  reCmd[4] = status;

  /* 清空接收缓冲区 */
  memset((void*)&serial_485, 0, sizeof(SERIAL));

  /* 发送应答帧 */
  uart_send_data(reCmd, 6);

  /* 延时10ms */
  HAL_Delay(10);
}

/**
  * @brief  数据解密和Flash写入函数 - 保持原有业务逻辑不变
  * @details 对接收到的加密数据进行解密，然后写入Flash
  * @param  frameNo: 帧序号
  * @param  cmdLen: 命令数据长度
  * @param  configUsartRcvDataBuffer: 接收数据缓冲区指针
  * @retval 1-成功，0-失败
  */
uint8_t DecryptConfigure(uint8_t frameNo, uint16_t cmdLen, uint8_t* configUsartRcvDataBuffer)
{
  uint8_t decryptRound = 0;  /* 解密轮次 */
  uint8_t* pCmdData = NULL;
  uint8_t countRound = 0;
  uint8_t countNum = 0;

  /* 数据长度必须是密钥长度的整数倍 */
  if((cmdLen % ALARM_CONFIG_KEY_LEN) != 0)
  {
    return 0;
  }

  decryptRound = cmdLen / 8;
  pCmdData = configUsartRcvDataBuffer; /* 指向密钥后的数据 */

  /* 数据解密，解密方法为：数据的每个字节与密钥进行异或运算，每一轮ALARM_CONFIG_KEY_LEN字节 */
  for(countRound = 0; countRound < decryptRound; countRound++)
  {
    for(countNum = 0; countNum < ALARM_CONFIG_KEY_LEN; countNum++)
    {
      pCmdData[countRound * ALARM_CONFIG_KEY_LEN + countNum] ^= configKey[countNum];
    }
  }

  /* 计算Flash写入地址 */
  FlashWriteAddress = USER_FLASH_FIRST_PAGE_ADDRESS + (frameNo) * (BUFF_LEN - 8);

  /* 写入Flash */
  if(Flash_Write_Data(FlashWriteAddress, (uint32_t*)(pCmdData), cmdLen) == HAL_OK)
  {
    return 1;
  }
  else
  {
    return 0;
  }
}

/**
  * @brief  接收数据处理函数 - 保持原有业务逻辑不变
  * @details 处理接收到的数据帧，进行CRC校验和解密写入
  * @param  pframe: 数据帧指针
  * @param  dataLen: 数据长度
  * @retval None
  */
void recvData(uint8_t *pframe, uint16_t dataLen)
{
  uint16_t recvCRC16 = (pframe[dataLen-1] << 8) | pframe[dataLen-2];

  if(recvCRC16 == usMBCRC16(pframe, dataLen-2))  /* CRC校验通过 */
  {
    /* 解密并写入Flash */
    if (DecryptConfigure(pframe[3], dataLen-6, pframe+4))
    {
      /* 成功 */
      HAL_Delay(10);
      returnFrame(0x04, pframe[3]);
    }
    else
    {
      /* 解密失败应答 */
      HAL_Delay(10);
      returnFrame(0x04, pframe[3]-1);
    }
  }
  else
  {
    /* CRC校验失败应答 */
    HAL_Delay(10);
    returnFrame(0x04, pframe[3]-1);
  }
}

/**
  * @brief  主函数 - 适配STM32L072，保持原有业务逻辑
  * @details Bootloader主程序，处理固件升级流程
  *
  * 业务流程：
  * 1. 系统初始化（时钟、UART、中断等）
  * 2. 读取升级标志，判断是否需要升级
  * 3. 如果标志为0xFF且应用程序有效，则跳转到应用程序
  * 4. 否则进入IAP升级模式，接收固件数据
  * 5. 处理升级命令：开始升级(0x01)、数据传输(0x03)、升级结束(0x05)
  * 6. 升级完成后设置标志并重启
  *
  * @param  None
  * @retval 0 (实际不会返回)
  */
int main(void)
{
  uint8_t updataFlag = 0xFF;           /* 升级标志 */
  uint8_t buff[BUFF_LEN];              /* 数据缓冲区 */
  uint16_t validLen = 0;               /* 有效数据长度 */
  uint16_t len = 0;                    /* 接收数据长度 */

  /* 系统初始化 - 参考app/main.c的初始化方式 */
  InterruptConfig();                   /* 中断配置 */
  Bootloader_Init();                   /* Bootloader初始化(包含HAL、时钟、串口等) */

  /* 读取升级标志 */
  updataFlag = EEPROM_Read_Byte(UPDATA_FLAG_ADDR);

  if(UPDATA_FLAG == updataFlag)
  {
    /* 检查应用程序是否有效 (检查栈指针是否指向RAM区域) */
    if (((*(volatile uint32_t*)USER_FLASH_FIRST_PAGE_ADDRESS) & 0x2FFE0000) == 0x20000000)
    {
      /* 跳转到用户应用程序 */
      JumpAddress = *(volatile uint32_t*) (USER_FLASH_FIRST_PAGE_ADDRESS + 4);
      Jump_To_Application = (pFunction) JumpAddress;

      /* 初始化用户应用程序的栈指针 */
      __set_MSP(*(volatile uint32_t*) USER_FLASH_FIRST_PAGE_ADDRESS);

      /* 跳转到应用程序 */
      Jump_To_Application();
    }
    else
    {
      /* 应用程序无效，清除升级标志并重启进入升级模式 */
      updataFlag = 0x00;
      EEPROM_Write_Byte(UPDATA_FLAG_ADDR, updataFlag);

      HAL_Delay(10);

      /* 初始化看门狗并等待复位 */
      IWDG_Init();
      while(1); /* 等待看门狗复位 */
    }
  }
  /* 进入IAP升级模式 */
  else
  {
    /* 初始化Flash操作 */
    HAL_FLASH_Unlock();

    /* 擦除用户Flash区域 */
    Flash_Erase_Pages(USER_FLASH_FIRST_PAGE_ADDRESS, STM32L072_APP_SIZE);

    HAL_FLASH_Lock();

    /* 主循环：等待并处理升级命令 */
    while (1)
    {
      /* 清空缓冲区 */
      memset(buff, 0, BUFF_LEN);

      /* 接收数据 */
      len = uart_receive_data(buff, BUFF_LEN, 5000); /* 5秒超时 */

      if(len > 0)
      {
        validLen = buff[1] * 256 + buff[2];  /* 有效数据长度 */

        /* 检查数据帧格式：帧头0x7E，帧尾0xCE，长度匹配 */
        if((len == validLen + 2) && (buff[0] == 0x7E) && (buff[len-1] == 0xCE))
        {
          switch(buff[3])  /* 命令字节 */
          {
            case 0x01:     /* 开始升级命令 */
              /* 获取密钥 */
              memCpy(buff+5, configKey, ALARM_CONFIG_KEY_LEN);

              /* 密钥配置：交换位置0-2, 1-6, 3-7 */
              DecryptKeyConfigure(0, 2);
              DecryptKeyConfigure(1, 6);
              DecryptKeyConfigure(3, 7);

              /* 发送应答 */
              returnFrame(0x04, 0xff);
              break;

            case 0x03:    /* 数据传输命令 */
              recvData(&buff[1], validLen);
              break;

            case 0x05:    /* 升级结束命令 */
              returnFrame(0x06, 0x00);

              /* 设置升级完成标志 */
              updataFlag = 0xff;
              EEPROM_Write_Byte(UPDATA_FLAG_ADDR, updataFlag);
              HAL_Delay(10);

              /* 初始化看门狗并重启 */
              IWDG_Init();
              while(1); /* 等待看门狗复位 */
              break;

            default:
              returnFrame(0x0A, 0xff);   /* 命令错误 */
              break;
          }
        }
        else if(len > 0)
        {
          returnFrame(0x0A, 0xff);   /* 数据格式错误 */
        }
      }
    }
  }

  return 0;
}
