.\objects\object.o: ..\rtthread\src\object.c
.\objects\object.o: ..\rtthread\include\rtthread.h
.\objects\object.o: ..\rtthread\bsp\rtconfig.h
.\objects\object.o: ..\rtthread\include\rtdebug.h
.\objects\object.o: ..\rtthread\include\rtdef.h
.\objects\object.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdarg.h
.\objects\object.o: ..\rtthread\include\rtlibc.h
.\objects\object.o: ..\rtthread\include\libc/libc_stat.h
.\objects\object.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\object.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\time.h
.\objects\object.o: ..\rtthread\include\libc/libc_errno.h
.\objects\object.o: ..\rtthread\include\libc/libc_fcntl.h
.\objects\object.o: ..\rtthread\include\libc/libc_ioctl.h
.\objects\object.o: ..\rtthread\include\libc/libc_dirent.h
.\objects\object.o: ..\rtthread\include\libc/libc_signal.h
.\objects\object.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\signal.h
.\objects\object.o: ..\rtthread\include\libc/libc_fdset.h
.\objects\object.o: ..\rtthread\include\rtservice.h
.\objects\object.o: ..\rtthread\include\rtm.h
.\objects\object.o: ..\rtthread\include\rtthread.h
.\objects\object.o: ..\rtthread\include\rthw.h
