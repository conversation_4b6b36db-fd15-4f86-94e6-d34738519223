#include "ec600m_common.h"
#include "ec600m_tcpip.h"
#include <string.h>
#include <stdlib.h>

static NET_ERR connect(char *ip,uint16_t port)
{
	NET_ERR err = NET_ERROR_NONE;
	uint8_t err_count = 0;
	char *p = RT_NULL;
	char *cmd_temp = (char *)rt_malloc(128);
	if(cmd_temp == RT_NULL)
	{
		err =  NET_ERROR_MEM;
		goto end;
	}
	rt_memset(cmd_temp,0,128);

	if(ec600x_base_functions.send_cmd("AT+QICSGP=1,1,\"CMIOT\",\"\",\"\",1","OK",1000) != NET_ERROR_NONE)
	{
		err = NET_ERROR_TIMEOUT;
		goto end;
	}
check_active:
	if(ec600x_base_functions.send_cmd("AT+QIACT?","OK",1000) != NET_ERROR_NONE)
	{
		err = NET_ERROR_TIMEOUT;
		goto end;
	}
	p = strchr((char *)serial_port.recv,',');
	if(p == RT_NULL)
	{
		err = NET_ERROR_MEM;
		goto end;
	}
	if(atoi(p) == 0)
	{
		if(ec600x_base_functions.send_cmd("AT+QIACT=1","OK",150000) != NET_ERROR_NONE)
		{
			err = NET_ERROR_TIMEOUT;
			goto end;
		}
		if(err_count++ < 3)//激活三次失败者返回失败
			goto check_active;
		else
		{
			err = NET_ERROR_TIMEOUT;
			goto end;
		}
	}

	rt_sprintf(cmd_temp,"AT+QIOPEN=1,0,\"TCP\",\"%s\",%d,0,2",ip,port);//透传模式
	if(ec600x_base_functions.send_cmd(cmd_temp,"CONNECT",1000) != NET_ERROR_NONE)
	{
		err = NET_ERROT_CONNECT;
		goto end;
	}
end:
	rt_free(cmd_temp);
	cmd_temp = RT_NULL;
	return err;
}
static NET_ERR send(uint8_t *data,uint16_t len)
{
	serial_port.clean();
	serial_port.send_data(data,len);
	return NET_ERROR_NONE;
}
static NET_ERR recv(uint8_t *data,uint16_t *len)
{
	if(serial_port.count && !serial_port.byte_timeout)
	{
		*len = serial_port.count;
		rt_memcpy(data,serial_port.recv,*len);
	}
	return NET_ERROR_NONE;
}
static NET_ERR close(void)
{
	serial_port.send_data("+++",3);//退出透传
	rt_thread_mdelay(300);
	if(strstr((char *)serial_port.recv,"OK") == RT_NULL)
		return NET_ERROR_TIMEOUT;
	ec600x_base_functions.send_cmd("AT+QICLOSE","OK",10000);
	return NET_ERROR_NONE;
}

static NET_TCPIP ec600m_tcpip = {
	.connect = connect,
	.send = send,
	.recv = recv,
	.close = close,
};

NET_ERR ec600m_tcpip_register(NET_TCPIP **info)
{
	*info = &ec600m_tcpip;
	return NET_ERROR_NONE;
}
