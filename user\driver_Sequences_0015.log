/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : D:\2025work\STM32L072PRO\source-application\user\driver_Sequences_0015.log
 *  Created     : 14:25:35 (15/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : D:\2025work\STM32L072PRO\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[14:25:35.563]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:25:35.563]  
[14:25:35.588]  <debugvars>
[14:25:35.612]    // Pre-defined
[14:25:35.634]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:25:35.653]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:25:35.654]    __dp=0x00000000
[14:25:35.654]    __ap=0x00000000
[14:25:35.654]    __traceout=0x00000000      (Trace Disabled)
[14:25:35.655]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:25:35.655]    __FlashAddr=0x00000000
[14:25:35.655]    __FlashLen=0x00000000
[14:25:35.655]    __FlashArg=0x00000000
[14:25:35.656]    __FlashOp=0x00000000
[14:25:35.656]    __Result=0x00000000
[14:25:35.656]    
[14:25:35.656]    // User-defined
[14:25:35.656]    DbgMCU_CR=0x00000007
[14:25:35.659]    DbgMCU_APB1_Fz=0x00000000
[14:25:35.659]    DbgMCU_APB2_Fz=0x00000000
[14:25:35.659]    DoOptionByteLoading=0x00000000
[14:25:35.660]  </debugvars>
[14:25:35.660]  
[14:25:35.660]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:25:35.661]    <block atomic="false" info="">
[14:25:35.661]      Sequence("CheckID");
[14:25:35.661]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:25:35.662]          <block atomic="false" info="">
[14:25:35.662]            __var pidr1 = 0;
[14:25:35.662]              // -> [pidr1 <= 0x00000000]
[14:25:35.662]            __var pidr2 = 0;
[14:25:35.663]              // -> [pidr2 <= 0x00000000]
[14:25:35.663]            __var jep106id = 0;
[14:25:35.663]              // -> [jep106id <= 0x00000000]
[14:25:35.663]            __var ROMTableBase = 0;
[14:25:35.663]              // -> [ROMTableBase <= 0x00000000]
[14:25:35.663]            __ap = 0;      // AHB-AP
[14:25:35.664]              // -> [__ap <= 0x00000000]
[14:25:35.664]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:25:35.665]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:25:35.665]              // -> [ROMTableBase <= 0xF0000000]
[14:25:35.665]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:25:35.666]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:25:35.667]              // -> [pidr1 <= 0x00000004]
[14:25:35.667]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:25:35.668]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:25:35.668]              // -> [pidr2 <= 0x0000000A]
[14:25:35.668]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:25:35.668]              // -> [jep106id <= 0x00000020]
[14:25:35.668]          </block>
[14:25:35.669]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:25:35.669]            // if-block "jep106id != 0x20"
[14:25:35.669]              // =>  FALSE
[14:25:35.669]            // skip if-block "jep106id != 0x20"
[14:25:35.670]          </control>
[14:25:35.671]        </sequence>
[14:25:35.671]    </block>
[14:25:35.671]  </sequence>
[14:25:35.671]  
[14:25:35.681]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:25:35.681]  
[14:25:35.686]  <debugvars>
[14:25:35.687]    // Pre-defined
[14:25:35.687]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:25:35.688]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:25:35.688]    __dp=0x00000000
[14:25:35.688]    __ap=0x00000000
[14:25:35.689]    __traceout=0x00000000      (Trace Disabled)
[14:25:35.690]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:25:35.690]    __FlashAddr=0x00000000
[14:25:35.691]    __FlashLen=0x00000000
[14:25:35.691]    __FlashArg=0x00000000
[14:25:35.692]    __FlashOp=0x00000000
[14:25:35.692]    __Result=0x00000000
[14:25:35.693]    
[14:25:35.693]    // User-defined
[14:25:35.693]    DbgMCU_CR=0x00000007
[14:25:35.693]    DbgMCU_APB1_Fz=0x00000000
[14:25:35.694]    DbgMCU_APB2_Fz=0x00000000
[14:25:35.694]    DoOptionByteLoading=0x00000000
[14:25:35.694]  </debugvars>
[14:25:35.695]  
[14:25:35.695]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:25:35.695]    <block atomic="false" info="">
[14:25:35.696]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:25:35.697]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:25:35.698]    </block>
[14:25:35.698]    <block atomic="false" info="DbgMCU registers">
[14:25:35.698]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:25:35.699]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[14:25:35.700]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[14:25:35.700]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:25:35.701]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:25:35.701]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:25:35.702]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:25:35.702]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:25:35.703]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:25:35.703]    </block>
[14:25:35.703]  </sequence>
[14:25:35.704]  
[14:25:43.909]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:25:43.909]  
[14:25:43.909]  <debugvars>
[14:25:43.909]    // Pre-defined
[14:25:43.912]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:25:43.912]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:25:43.912]    __dp=0x00000000
[14:25:43.912]    __ap=0x00000000
[14:25:43.912]    __traceout=0x00000000      (Trace Disabled)
[14:25:43.912]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:25:43.912]    __FlashAddr=0x00000000
[14:25:43.912]    __FlashLen=0x00000000
[14:25:43.912]    __FlashArg=0x00000000
[14:25:43.912]    __FlashOp=0x00000000
[14:25:43.912]    __Result=0x00000000
[14:25:43.912]    
[14:25:43.912]    // User-defined
[14:25:43.912]    DbgMCU_CR=0x00000007
[14:25:43.912]    DbgMCU_APB1_Fz=0x00000000
[14:25:43.912]    DbgMCU_APB2_Fz=0x00000000
[14:25:43.912]    DoOptionByteLoading=0x00000000
[14:25:43.912]  </debugvars>
[14:25:43.912]  
[14:25:43.912]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:25:43.912]    <block atomic="false" info="">
[14:25:43.912]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:25:43.917]        // -> [connectionFlash <= 0x00000001]
[14:25:43.917]      __var FLASH_BASE = 0x40022000 ;
[14:25:43.917]        // -> [FLASH_BASE <= 0x40022000]
[14:25:43.917]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:25:43.917]        // -> [FLASH_CR <= 0x40022004]
[14:25:43.917]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:25:43.917]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:25:43.917]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:25:43.917]        // -> [LOCK_BIT <= 0x00000001]
[14:25:43.917]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:25:43.917]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:25:43.917]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:25:43.917]        // -> [FLASH_KEYR <= 0x4002200C]
[14:25:43.917]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:25:43.917]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:25:43.917]      __var FLASH_KEY2 = 0x02030405 ;
[14:25:43.917]        // -> [FLASH_KEY2 <= 0x02030405]
[14:25:43.917]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:25:43.917]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:25:43.917]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:25:43.917]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:25:43.917]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:25:43.922]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:25:43.922]      __var FLASH_CR_Value = 0 ;
[14:25:43.922]        // -> [FLASH_CR_Value <= 0x00000000]
[14:25:43.922]      __var DoDebugPortStop = 1 ;
[14:25:43.922]        // -> [DoDebugPortStop <= 0x00000001]
[14:25:43.922]      __var DP_CTRL_STAT = 0x4 ;
[14:25:43.922]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:25:43.922]      __var DP_SELECT = 0x8 ;
[14:25:43.924]        // -> [DP_SELECT <= 0x00000008]
[14:25:43.924]    </block>
[14:25:43.924]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:25:43.924]      // if-block "connectionFlash && DoOptionByteLoading"
[14:25:43.924]        // =>  FALSE
[14:25:43.924]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:25:43.924]    </control>
[14:25:43.924]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:25:43.924]      // if-block "DoDebugPortStop"
[14:25:43.924]        // =>  TRUE
[14:25:43.924]      <block atomic="false" info="">
[14:25:43.927]        WriteDP(DP_SELECT, 0x00000000);
[14:25:43.927]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:25:43.927]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:25:43.927]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:25:43.927]      </block>
[14:25:43.927]      // end if-block "DoDebugPortStop"
[14:25:43.927]    </control>
[14:25:43.927]  </sequence>
[14:25:43.927]  
[14:26:30.782]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:26:30.782]  
[14:26:30.782]  <debugvars>
[14:26:30.782]    // Pre-defined
[14:26:30.787]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:26:30.787]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[14:26:30.787]    __dp=0x00000000
[14:26:30.787]    __ap=0x00000000
[14:26:30.788]    __traceout=0x00000000      (Trace Disabled)
[14:26:30.788]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:26:30.789]    __FlashAddr=0x00000000
[14:26:30.789]    __FlashLen=0x00000000
[14:26:30.789]    __FlashArg=0x00000000
[14:26:30.789]    __FlashOp=0x00000000
[14:26:30.789]    __Result=0x00000000
[14:26:30.789]    
[14:26:30.789]    // User-defined
[14:26:30.789]    DbgMCU_CR=0x00000007
[14:26:30.789]    DbgMCU_APB1_Fz=0x00000000
[14:26:30.789]    DbgMCU_APB2_Fz=0x00000000
[14:26:30.792]    DoOptionByteLoading=0x00000000
[14:26:30.792]  </debugvars>
[14:26:30.792]  
[14:26:30.792]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:26:30.793]    <block atomic="false" info="">
[14:26:30.793]      Sequence("CheckID");
[14:26:30.793]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:26:30.793]          <block atomic="false" info="">
[14:26:30.793]            __var pidr1 = 0;
[14:26:30.793]              // -> [pidr1 <= 0x00000000]
[14:26:30.793]            __var pidr2 = 0;
[14:26:30.793]              // -> [pidr2 <= 0x00000000]
[14:26:30.793]            __var jep106id = 0;
[14:26:30.793]              // -> [jep106id <= 0x00000000]
[14:26:30.793]            __var ROMTableBase = 0;
[14:26:30.796]              // -> [ROMTableBase <= 0x00000000]
[14:26:30.796]            __ap = 0;      // AHB-AP
[14:26:30.796]              // -> [__ap <= 0x00000000]
[14:26:30.796]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:26:30.796]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:26:30.796]              // -> [ROMTableBase <= 0xF0000000]
[14:26:30.796]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:26:30.797]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:26:30.797]              // -> [pidr1 <= 0x00000004]
[14:26:30.797]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:26:30.797]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:26:30.797]              // -> [pidr2 <= 0x0000000A]
[14:26:30.797]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:26:30.797]              // -> [jep106id <= 0x00000020]
[14:26:30.797]          </block>
[14:26:30.797]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:26:30.797]            // if-block "jep106id != 0x20"
[14:26:30.797]              // =>  FALSE
[14:26:30.797]            // skip if-block "jep106id != 0x20"
[14:26:30.802]          </control>
[14:26:30.802]        </sequence>
[14:26:30.802]    </block>
[14:26:30.803]  </sequence>
[14:26:30.803]  
[14:26:30.813]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:26:30.813]  
[14:26:30.834]  <debugvars>
[14:26:30.834]    // Pre-defined
[14:26:30.834]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:26:30.834]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[14:26:30.834]    __dp=0x00000000
[14:26:30.834]    __ap=0x00000000
[14:26:30.834]    __traceout=0x00000000      (Trace Disabled)
[14:26:30.834]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:26:30.834]    __FlashAddr=0x00000000
[14:26:30.839]    __FlashLen=0x00000000
[14:26:30.839]    __FlashArg=0x00000000
[14:26:30.839]    __FlashOp=0x00000000
[14:26:30.839]    __Result=0x00000000
[14:26:30.839]    
[14:26:30.839]    // User-defined
[14:26:30.839]    DbgMCU_CR=0x00000007
[14:26:30.839]    DbgMCU_APB1_Fz=0x00000000
[14:26:30.839]    DbgMCU_APB2_Fz=0x00000000
[14:26:30.839]    DoOptionByteLoading=0x00000000
[14:26:30.839]  </debugvars>
[14:26:30.842]  
[14:26:30.842]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:26:30.842]    <block atomic="false" info="">
[14:26:30.842]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:26:30.844]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:26:30.844]    </block>
[14:26:30.844]    <block atomic="false" info="DbgMCU registers">
[14:26:30.844]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:26:30.845]        // -> [Read32(0x40021034) => 0x00004201]   (__dp=0x00000000, __ap=0x00000000)
[14:26:30.845]        // -> [Write32(0x40021034, 0x00404201)]   (__dp=0x00000000, __ap=0x00000000)
[14:26:30.845]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:26:30.845]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:26:30.845]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:26:30.849]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:26:30.849]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:26:30.850]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:26:30.850]    </block>
[14:26:30.851]  </sequence>
[14:26:30.851]  
[14:27:24.209]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:27:24.209]  
[14:27:24.209]  <debugvars>
[14:27:24.210]    // Pre-defined
[14:27:24.210]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:27:24.210]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[14:27:24.210]    __dp=0x00000000
[14:27:24.210]    __ap=0x00000000
[14:27:24.210]    __traceout=0x00000000      (Trace Disabled)
[14:27:24.211]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:27:24.211]    __FlashAddr=0x00000000
[14:27:24.211]    __FlashLen=0x00000000
[14:27:24.211]    __FlashArg=0x00000000
[14:27:24.211]    __FlashOp=0x00000000
[14:27:24.212]    __Result=0x00000000
[14:27:24.212]    
[14:27:24.212]    // User-defined
[14:27:24.212]    DbgMCU_CR=0x00000007
[14:27:24.212]    DbgMCU_APB1_Fz=0x00000000
[14:27:24.212]    DbgMCU_APB2_Fz=0x00000000
[14:27:24.213]    DoOptionByteLoading=0x00000000
[14:27:24.213]  </debugvars>
[14:27:24.213]  
[14:27:24.213]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:27:24.213]    <block atomic="false" info="">
[14:27:24.214]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:27:24.214]        // -> [connectionFlash <= 0x00000000]
[14:27:24.214]      __var FLASH_BASE = 0x40022000 ;
[14:27:24.214]        // -> [FLASH_BASE <= 0x40022000]
[14:27:24.214]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:27:24.215]        // -> [FLASH_CR <= 0x40022004]
[14:27:24.215]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:27:24.215]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:27:24.215]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:27:24.215]        // -> [LOCK_BIT <= 0x00000001]
[14:27:24.215]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:27:24.216]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:27:24.216]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:27:24.216]        // -> [FLASH_KEYR <= 0x4002200C]
[14:27:24.216]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:27:24.217]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:27:24.217]      __var FLASH_KEY2 = 0x02030405 ;
[14:27:24.217]        // -> [FLASH_KEY2 <= 0x02030405]
[14:27:24.217]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:27:24.217]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:27:24.217]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:27:24.219]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:27:24.219]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:27:24.219]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:27:24.219]      __var FLASH_CR_Value = 0 ;
[14:27:24.220]        // -> [FLASH_CR_Value <= 0x00000000]
[14:27:24.220]      __var DoDebugPortStop = 1 ;
[14:27:24.220]        // -> [DoDebugPortStop <= 0x00000001]
[14:27:24.220]      __var DP_CTRL_STAT = 0x4 ;
[14:27:24.220]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:27:24.221]      __var DP_SELECT = 0x8 ;
[14:27:24.221]        // -> [DP_SELECT <= 0x00000008]
[14:27:24.221]    </block>
[14:27:24.221]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:27:24.222]      // if-block "connectionFlash && DoOptionByteLoading"
[14:27:24.222]        // =>  FALSE
[14:27:24.222]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:27:24.223]    </control>
[14:27:24.223]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:27:24.223]      // if-block "DoDebugPortStop"
[14:27:24.223]        // =>  TRUE
[14:27:24.223]      <block atomic="false" info="">
[14:27:24.224]        WriteDP(DP_SELECT, 0x00000000);
[14:27:24.224]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:27:24.224]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:27:24.225]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:27:24.225]      </block>
[14:27:24.225]      // end if-block "DoDebugPortStop"
[14:27:24.225]    </control>
[14:27:24.225]  </sequence>
[14:27:24.226]  
[14:41:48.817]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:41:48.817]  
[14:41:48.817]  <debugvars>
[14:41:48.817]    // Pre-defined
[14:41:48.817]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:41:48.817]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:41:48.817]    __dp=0x00000000
[14:41:48.818]    __ap=0x00000000
[14:41:48.818]    __traceout=0x00000000      (Trace Disabled)
[14:41:48.818]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:41:48.818]    __FlashAddr=0x00000000
[14:41:48.818]    __FlashLen=0x00000000
[14:41:48.819]    __FlashArg=0x00000000
[14:41:48.819]    __FlashOp=0x00000000
[14:41:48.820]    __Result=0x00000000
[14:41:48.820]    
[14:41:48.820]    // User-defined
[14:41:48.820]    DbgMCU_CR=0x00000007
[14:41:48.820]    DbgMCU_APB1_Fz=0x00000000
[14:41:48.820]    DbgMCU_APB2_Fz=0x00000000
[14:41:48.820]    DoOptionByteLoading=0x00000000
[14:41:48.820]  </debugvars>
[14:41:48.821]  
[14:41:48.821]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:41:48.821]    <block atomic="false" info="">
[14:41:48.821]      Sequence("CheckID");
[14:41:48.821]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:41:48.822]          <block atomic="false" info="">
[14:41:48.822]            __var pidr1 = 0;
[14:41:48.822]              // -> [pidr1 <= 0x00000000]
[14:41:48.822]            __var pidr2 = 0;
[14:41:48.822]              // -> [pidr2 <= 0x00000000]
[14:41:48.822]            __var jep106id = 0;
[14:41:48.822]              // -> [jep106id <= 0x00000000]
[14:41:48.822]            __var ROMTableBase = 0;
[14:41:48.823]              // -> [ROMTableBase <= 0x00000000]
[14:41:48.823]            __ap = 0;      // AHB-AP
[14:41:48.823]              // -> [__ap <= 0x00000000]
[14:41:48.824]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:41:48.825]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:41:48.825]              // -> [ROMTableBase <= 0xF0000000]
[14:41:48.825]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:41:48.827]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:41:48.827]              // -> [pidr1 <= 0x00000004]
[14:41:48.827]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:41:48.828]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:41:48.828]              // -> [pidr2 <= 0x0000000A]
[14:41:48.828]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:41:48.828]              // -> [jep106id <= 0x00000020]
[14:41:48.828]          </block>
[14:41:48.829]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:41:48.829]            // if-block "jep106id != 0x20"
[14:41:48.830]              // =>  FALSE
[14:41:48.830]            // skip if-block "jep106id != 0x20"
[14:41:48.830]          </control>
[14:41:48.830]        </sequence>
[14:41:48.830]    </block>
[14:41:48.830]  </sequence>
[14:41:48.831]  
[14:41:48.842]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:41:48.842]  
[14:41:48.843]  <debugvars>
[14:41:48.843]    // Pre-defined
[14:41:48.843]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:41:48.843]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:41:48.843]    __dp=0x00000000
[14:41:48.844]    __ap=0x00000000
[14:41:48.844]    __traceout=0x00000000      (Trace Disabled)
[14:41:48.845]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:41:48.845]    __FlashAddr=0x00000000
[14:41:48.845]    __FlashLen=0x00000000
[14:41:48.845]    __FlashArg=0x00000000
[14:41:48.845]    __FlashOp=0x00000000
[14:41:48.846]    __Result=0x00000000
[14:41:48.846]    
[14:41:48.846]    // User-defined
[14:41:48.846]    DbgMCU_CR=0x00000007
[14:41:48.846]    DbgMCU_APB1_Fz=0x00000000
[14:41:48.846]    DbgMCU_APB2_Fz=0x00000000
[14:41:48.847]    DoOptionByteLoading=0x00000000
[14:41:48.847]  </debugvars>
[14:41:48.847]  
[14:41:48.847]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:41:48.847]    <block atomic="false" info="">
[14:41:48.847]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:41:48.848]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:41:48.848]    </block>
[14:41:48.849]    <block atomic="false" info="DbgMCU registers">
[14:41:48.849]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:41:48.850]        // -> [Read32(0x40021034) => 0x00004001]   (__dp=0x00000000, __ap=0x00000000)
[14:41:48.850]        // -> [Write32(0x40021034, 0x00404001)]   (__dp=0x00000000, __ap=0x00000000)
[14:41:48.850]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:41:48.851]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:41:48.851]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:41:48.852]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:41:48.853]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:41:48.853]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:41:48.854]    </block>
[14:41:48.854]  </sequence>
[14:41:48.854]  
[14:41:57.048]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:41:57.048]  
[14:41:57.049]  <debugvars>
[14:41:57.049]    // Pre-defined
[14:41:57.049]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:41:57.050]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:41:57.050]    __dp=0x00000000
[14:41:57.050]    __ap=0x00000000
[14:41:57.050]    __traceout=0x00000000      (Trace Disabled)
[14:41:57.051]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:41:57.051]    __FlashAddr=0x00000000
[14:41:57.051]    __FlashLen=0x00000000
[14:41:57.051]    __FlashArg=0x00000000
[14:41:57.051]    __FlashOp=0x00000000
[14:41:57.051]    __Result=0x00000000
[14:41:57.052]    
[14:41:57.052]    // User-defined
[14:41:57.052]    DbgMCU_CR=0x00000007
[14:41:57.052]    DbgMCU_APB1_Fz=0x00000000
[14:41:57.052]    DbgMCU_APB2_Fz=0x00000000
[14:41:57.052]    DoOptionByteLoading=0x00000000
[14:41:57.053]  </debugvars>
[14:41:57.053]  
[14:41:57.053]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:41:57.054]    <block atomic="false" info="">
[14:41:57.054]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:41:57.054]        // -> [connectionFlash <= 0x00000001]
[14:41:57.054]      __var FLASH_BASE = 0x40022000 ;
[14:41:57.054]        // -> [FLASH_BASE <= 0x40022000]
[14:41:57.054]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:41:57.055]        // -> [FLASH_CR <= 0x40022004]
[14:41:57.055]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:41:57.055]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:41:57.055]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:41:57.055]        // -> [LOCK_BIT <= 0x00000001]
[14:41:57.056]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:41:57.056]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:41:57.056]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:41:57.056]        // -> [FLASH_KEYR <= 0x4002200C]
[14:41:57.056]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:41:57.057]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:41:57.057]      __var FLASH_KEY2 = 0x02030405 ;
[14:41:57.057]        // -> [FLASH_KEY2 <= 0x02030405]
[14:41:57.057]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:41:57.057]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:41:57.057]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:41:57.058]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:41:57.058]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:41:57.059]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:41:57.059]      __var FLASH_CR_Value = 0 ;
[14:41:57.059]        // -> [FLASH_CR_Value <= 0x00000000]
[14:41:57.059]      __var DoDebugPortStop = 1 ;
[14:41:57.059]        // -> [DoDebugPortStop <= 0x00000001]
[14:41:57.060]      __var DP_CTRL_STAT = 0x4 ;
[14:41:57.060]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:41:57.060]      __var DP_SELECT = 0x8 ;
[14:41:57.060]        // -> [DP_SELECT <= 0x00000008]
[14:41:57.060]    </block>
[14:41:57.061]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:41:57.061]      // if-block "connectionFlash && DoOptionByteLoading"
[14:41:57.061]        // =>  FALSE
[14:41:57.061]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:41:57.061]    </control>
[14:41:57.062]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:41:57.062]      // if-block "DoDebugPortStop"
[14:41:57.062]        // =>  TRUE
[14:41:57.062]      <block atomic="false" info="">
[14:41:57.062]        WriteDP(DP_SELECT, 0x00000000);
[14:41:57.063]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:41:57.063]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:41:57.063]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:41:57.064]      </block>
[14:41:57.064]      // end if-block "DoDebugPortStop"
[14:41:57.064]    </control>
[14:41:57.064]  </sequence>
[14:41:57.064]  
[14:41:57.386]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:41:57.386]  
[14:41:57.386]  <debugvars>
[14:41:57.387]    // Pre-defined
[14:41:57.387]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:41:57.387]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[14:41:57.387]    __dp=0x00000000
[14:41:57.387]    __ap=0x00000000
[14:41:57.387]    __traceout=0x00000000      (Trace Disabled)
[14:41:57.389]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:41:57.389]    __FlashAddr=0x00000000
[14:41:57.389]    __FlashLen=0x00000000
[14:41:57.389]    __FlashArg=0x00000000
[14:41:57.389]    __FlashOp=0x00000000
[14:41:57.390]    __Result=0x00000000
[14:41:57.390]    
[14:41:57.390]    // User-defined
[14:41:57.390]    DbgMCU_CR=0x00000007
[14:41:57.390]    DbgMCU_APB1_Fz=0x00000000
[14:41:57.390]    DbgMCU_APB2_Fz=0x00000000
[14:41:57.391]    DoOptionByteLoading=0x00000000
[14:41:57.391]  </debugvars>
[14:41:57.391]  
[14:41:57.391]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:41:57.391]    <block atomic="false" info="">
[14:41:57.392]      Sequence("CheckID");
[14:41:57.392]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:41:57.392]          <block atomic="false" info="">
[14:41:57.392]            __var pidr1 = 0;
[14:41:57.392]              // -> [pidr1 <= 0x00000000]
[14:41:57.392]            __var pidr2 = 0;
[14:41:57.393]              // -> [pidr2 <= 0x00000000]
[14:41:57.393]            __var jep106id = 0;
[14:41:57.393]              // -> [jep106id <= 0x00000000]
[14:41:57.393]            __var ROMTableBase = 0;
[14:41:57.393]              // -> [ROMTableBase <= 0x00000000]
[14:41:57.394]            __ap = 0;      // AHB-AP
[14:41:57.394]              // -> [__ap <= 0x00000000]
[14:41:57.394]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:41:57.395]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:41:57.395]              // -> [ROMTableBase <= 0xF0000000]
[14:41:57.395]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:41:57.396]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:41:57.396]              // -> [pidr1 <= 0x00000004]
[14:41:57.396]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:41:57.397]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:41:57.397]              // -> [pidr2 <= 0x0000000A]
[14:41:57.397]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:41:57.398]              // -> [jep106id <= 0x00000020]
[14:41:57.398]          </block>
[14:41:57.398]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:41:57.398]            // if-block "jep106id != 0x20"
[14:41:57.399]              // =>  FALSE
[14:41:57.399]            // skip if-block "jep106id != 0x20"
[14:41:57.399]          </control>
[14:41:57.399]        </sequence>
[14:41:57.399]    </block>
[14:41:57.400]  </sequence>
[14:41:57.400]  
[14:41:57.412]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:41:57.412]  
[14:41:57.413]  <debugvars>
[14:41:57.413]    // Pre-defined
[14:41:57.413]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:41:57.413]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[14:41:57.413]    __dp=0x00000000
[14:41:57.414]    __ap=0x00000000
[14:41:57.414]    __traceout=0x00000000      (Trace Disabled)
[14:41:57.414]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:41:57.414]    __FlashAddr=0x00000000
[14:41:57.414]    __FlashLen=0x00000000
[14:41:57.414]    __FlashArg=0x00000000
[14:41:57.415]    __FlashOp=0x00000000
[14:41:57.415]    __Result=0x00000000
[14:41:57.417]    
[14:41:57.417]    // User-defined
[14:41:57.417]    DbgMCU_CR=0x00000007
[14:41:57.417]    DbgMCU_APB1_Fz=0x00000000
[14:41:57.417]    DbgMCU_APB2_Fz=0x00000000
[14:41:57.417]    DoOptionByteLoading=0x00000000
[14:41:57.418]  </debugvars>
[14:41:57.418]  
[14:41:57.418]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:41:57.418]    <block atomic="false" info="">
[14:41:57.418]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:41:57.418]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:41:57.420]    </block>
[14:41:57.420]    <block atomic="false" info="DbgMCU registers">
[14:41:57.420]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:41:57.421]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[14:41:57.421]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[14:41:57.422]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:41:57.423]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:41:57.423]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:41:57.423]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:41:57.424]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:41:57.424]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:41:57.425]    </block>
[14:41:57.425]  </sequence>
[14:41:57.425]  
