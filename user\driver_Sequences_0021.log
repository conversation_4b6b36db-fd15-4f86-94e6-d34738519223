/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : D:\2025work\STM32L072PRO\source-application\user\driver_Sequences_0021.log
 *  Created     : 11:09:35 (19/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : D:\2025work\STM32L072PRO\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[11:09:35.663]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:09:35.663]  
[11:09:35.684]  <debugvars>
[11:09:35.704]    // Pre-defined
[11:09:35.727]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:09:35.745]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:09:35.746]    __dp=0x00000000
[11:09:35.746]    __ap=0x00000000
[11:09:35.747]    __traceout=0x00000000      (Trace Disabled)
[11:09:35.747]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:09:35.747]    __FlashAddr=0x00000000
[11:09:35.747]    __FlashLen=0x00000000
[11:09:35.748]    __FlashArg=0x00000000
[11:09:35.748]    __FlashOp=0x00000000
[11:09:35.749]    __Result=0x00000000
[11:09:35.749]    
[11:09:35.749]    // User-defined
[11:09:35.749]    DbgMCU_CR=0x00000007
[11:09:35.750]    DbgMCU_APB1_Fz=0x00000000
[11:09:35.750]    DbgMCU_APB2_Fz=0x00000000
[11:09:35.750]    DoOptionByteLoading=0x00000000
[11:09:35.750]  </debugvars>
[11:09:35.750]  
[11:09:35.751]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:09:35.751]    <block atomic="false" info="">
[11:09:35.751]      Sequence("CheckID");
[11:09:35.751]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:09:35.751]          <block atomic="false" info="">
[11:09:35.751]            __var pidr1 = 0;
[11:09:35.751]              // -> [pidr1 <= 0x00000000]
[11:09:35.751]            __var pidr2 = 0;
[11:09:35.752]              // -> [pidr2 <= 0x00000000]
[11:09:35.752]            __var jep106id = 0;
[11:09:35.752]              // -> [jep106id <= 0x00000000]
[11:09:35.753]            __var ROMTableBase = 0;
[11:09:35.753]              // -> [ROMTableBase <= 0x00000000]
[11:09:35.754]            __ap = 0;      // AHB-AP
[11:09:35.754]              // -> [__ap <= 0x00000000]
[11:09:35.754]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:09:35.755]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:09:35.755]              // -> [ROMTableBase <= 0xF0000000]
[11:09:35.756]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:09:35.757]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:09:35.757]              // -> [pidr1 <= 0x00000004]
[11:09:35.757]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:09:35.759]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:09:35.759]              // -> [pidr2 <= 0x0000000A]
[11:09:35.759]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:09:35.760]              // -> [jep106id <= 0x00000020]
[11:09:35.760]          </block>
[11:09:35.760]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:09:35.760]            // if-block "jep106id != 0x20"
[11:09:35.761]              // =>  FALSE
[11:09:35.761]            // skip if-block "jep106id != 0x20"
[11:09:35.761]          </control>
[11:09:35.761]        </sequence>
[11:09:35.761]    </block>
[11:09:35.762]  </sequence>
[11:09:35.762]  
[11:09:35.774]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:09:35.774]  
[11:09:35.775]  <debugvars>
[11:09:35.775]    // Pre-defined
[11:09:35.775]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:09:35.775]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:09:35.776]    __dp=0x00000000
[11:09:35.776]    __ap=0x00000000
[11:09:35.777]    __traceout=0x00000000      (Trace Disabled)
[11:09:35.777]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:09:35.778]    __FlashAddr=0x00000000
[11:09:35.778]    __FlashLen=0x00000000
[11:09:35.779]    __FlashArg=0x00000000
[11:09:35.779]    __FlashOp=0x00000000
[11:09:35.779]    __Result=0x00000000
[11:09:35.780]    
[11:09:35.780]    // User-defined
[11:09:35.780]    DbgMCU_CR=0x00000007
[11:09:35.780]    DbgMCU_APB1_Fz=0x00000000
[11:09:35.780]    DbgMCU_APB2_Fz=0x00000000
[11:09:35.781]    DoOptionByteLoading=0x00000000
[11:09:35.781]  </debugvars>
[11:09:35.781]  
[11:09:35.781]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:09:35.781]    <block atomic="false" info="">
[11:09:35.781]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:09:35.782]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:09:35.782]    </block>
[11:09:35.782]    <block atomic="false" info="DbgMCU registers">
[11:09:35.783]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:09:35.784]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:09:35.784]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:09:35.785]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:09:35.785]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:09:35.786]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:09:35.788]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:09:35.788]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:09:35.788]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:09:35.789]    </block>
[11:09:35.789]  </sequence>
[11:09:35.789]  
[11:09:43.778]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:09:43.778]  
[11:09:43.779]  <debugvars>
[11:09:43.779]    // Pre-defined
[11:09:43.779]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:09:43.780]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:09:43.780]    __dp=0x00000000
[11:09:43.780]    __ap=0x00000000
[11:09:43.780]    __traceout=0x00000000      (Trace Disabled)
[11:09:43.780]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:09:43.780]    __FlashAddr=0x00000000
[11:09:43.780]    __FlashLen=0x00000000
[11:09:43.781]    __FlashArg=0x00000000
[11:09:43.781]    __FlashOp=0x00000000
[11:09:43.781]    __Result=0x00000000
[11:09:43.781]    
[11:09:43.781]    // User-defined
[11:09:43.781]    DbgMCU_CR=0x00000007
[11:09:43.782]    DbgMCU_APB1_Fz=0x00000000
[11:09:43.782]    DbgMCU_APB2_Fz=0x00000000
[11:09:43.782]    DoOptionByteLoading=0x00000000
[11:09:43.782]  </debugvars>
[11:09:43.782]  
[11:09:43.783]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:09:43.783]    <block atomic="false" info="">
[11:09:43.783]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:09:43.784]        // -> [connectionFlash <= 0x00000001]
[11:09:43.784]      __var FLASH_BASE = 0x40022000 ;
[11:09:43.784]        // -> [FLASH_BASE <= 0x40022000]
[11:09:43.784]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:09:43.784]        // -> [FLASH_CR <= 0x40022004]
[11:09:43.784]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:09:43.785]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:09:43.785]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:09:43.785]        // -> [LOCK_BIT <= 0x00000001]
[11:09:43.785]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:09:43.785]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:09:43.786]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:09:43.786]        // -> [FLASH_KEYR <= 0x4002200C]
[11:09:43.786]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:09:43.786]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:09:43.786]      __var FLASH_KEY2 = 0x02030405 ;
[11:09:43.787]        // -> [FLASH_KEY2 <= 0x02030405]
[11:09:43.787]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:09:43.787]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:09:43.788]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:09:43.788]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:09:43.788]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:09:43.789]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:09:43.789]      __var FLASH_CR_Value = 0 ;
[11:09:43.789]        // -> [FLASH_CR_Value <= 0x00000000]
[11:09:43.789]      __var DoDebugPortStop = 1 ;
[11:09:43.789]        // -> [DoDebugPortStop <= 0x00000001]
[11:09:43.789]      __var DP_CTRL_STAT = 0x4 ;
[11:09:43.789]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:09:43.789]      __var DP_SELECT = 0x8 ;
[11:09:43.790]        // -> [DP_SELECT <= 0x00000008]
[11:09:43.790]    </block>
[11:09:43.790]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:09:43.790]      // if-block "connectionFlash && DoOptionByteLoading"
[11:09:43.790]        // =>  FALSE
[11:09:43.791]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:09:43.791]    </control>
[11:09:43.791]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:09:43.791]      // if-block "DoDebugPortStop"
[11:09:43.791]        // =>  TRUE
[11:09:43.791]      <block atomic="false" info="">
[11:09:43.792]        WriteDP(DP_SELECT, 0x00000000);
[11:09:43.792]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:09:43.793]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:09:43.793]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:09:43.794]      </block>
[11:09:43.794]      // end if-block "DoDebugPortStop"
[11:09:43.794]    </control>
[11:09:43.794]  </sequence>
[11:09:43.795]  
[11:21:02.808]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:21:02.808]  
[11:21:02.819]  <debugvars>
[11:21:02.819]    // Pre-defined
[11:21:02.819]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:21:02.820]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:21:02.820]    __dp=0x00000000
[11:21:02.821]    __ap=0x00000000
[11:21:02.821]    __traceout=0x00000000      (Trace Disabled)
[11:21:02.821]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:21:02.821]    __FlashAddr=0x00000000
[11:21:02.822]    __FlashLen=0x00000000
[11:21:02.822]    __FlashArg=0x00000000
[11:21:02.822]    __FlashOp=0x00000000
[11:21:02.822]    __Result=0x00000000
[11:21:02.823]    
[11:21:02.823]    // User-defined
[11:21:02.823]    DbgMCU_CR=0x00000007
[11:21:02.823]    DbgMCU_APB1_Fz=0x00000000
[11:21:02.824]    DbgMCU_APB2_Fz=0x00000000
[11:21:02.824]    DoOptionByteLoading=0x00000000
[11:21:02.825]  </debugvars>
[11:21:02.825]  
[11:21:02.825]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:21:02.825]    <block atomic="false" info="">
[11:21:02.825]      Sequence("CheckID");
[11:21:02.825]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:21:02.826]          <block atomic="false" info="">
[11:21:02.826]            __var pidr1 = 0;
[11:21:02.826]              // -> [pidr1 <= 0x00000000]
[11:21:02.826]            __var pidr2 = 0;
[11:21:02.826]              // -> [pidr2 <= 0x00000000]
[11:21:02.826]            __var jep106id = 0;
[11:21:02.827]              // -> [jep106id <= 0x00000000]
[11:21:02.827]            __var ROMTableBase = 0;
[11:21:02.827]              // -> [ROMTableBase <= 0x00000000]
[11:21:02.827]            __ap = 0;      // AHB-AP
[11:21:02.827]              // -> [__ap <= 0x00000000]
[11:21:02.827]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:21:02.829]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:21:02.829]              // -> [ROMTableBase <= 0xF0000000]
[11:21:02.829]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:21:02.830]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:21:02.830]              // -> [pidr1 <= 0x00000004]
[11:21:02.831]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:21:02.832]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:21:02.832]              // -> [pidr2 <= 0x0000000A]
[11:21:02.832]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:21:02.832]              // -> [jep106id <= 0x00000020]
[11:21:02.833]          </block>
[11:21:02.833]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:21:02.833]            // if-block "jep106id != 0x20"
[11:21:02.833]              // =>  FALSE
[11:21:02.834]            // skip if-block "jep106id != 0x20"
[11:21:02.834]          </control>
[11:21:02.834]        </sequence>
[11:21:02.834]    </block>
[11:21:02.834]  </sequence>
[11:21:02.835]  
[11:21:02.847]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:21:02.847]  
[11:21:02.877]  <debugvars>
[11:21:02.877]    // Pre-defined
[11:21:02.878]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:21:02.878]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:21:02.878]    __dp=0x00000000
[11:21:02.878]    __ap=0x00000000
[11:21:02.878]    __traceout=0x00000000      (Trace Disabled)
[11:21:02.879]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:21:02.879]    __FlashAddr=0x00000000
[11:21:02.879]    __FlashLen=0x00000000
[11:21:02.880]    __FlashArg=0x00000000
[11:21:02.880]    __FlashOp=0x00000000
[11:21:02.881]    __Result=0x00000000
[11:21:02.881]    
[11:21:02.881]    // User-defined
[11:21:02.881]    DbgMCU_CR=0x00000007
[11:21:02.881]    DbgMCU_APB1_Fz=0x00000000
[11:21:02.882]    DbgMCU_APB2_Fz=0x00000000
[11:21:02.882]    DoOptionByteLoading=0x00000000
[11:21:02.882]  </debugvars>
[11:21:02.882]  
[11:21:02.882]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:21:02.882]    <block atomic="false" info="">
[11:21:02.883]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:21:02.884]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:21:02.884]    </block>
[11:21:02.884]    <block atomic="false" info="DbgMCU registers">
[11:21:02.884]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:21:02.885]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:21:02.886]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:21:02.886]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:21:02.887]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:21:02.888]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:21:02.889]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:21:02.889]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:21:02.890]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:21:02.890]    </block>
[11:21:02.890]  </sequence>
[11:21:02.891]  
[11:21:10.765]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:21:10.765]  
[11:21:10.765]  <debugvars>
[11:21:10.766]    // Pre-defined
[11:21:10.766]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:21:10.766]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:21:10.767]    __dp=0x00000000
[11:21:10.767]    __ap=0x00000000
[11:21:10.768]    __traceout=0x00000000      (Trace Disabled)
[11:21:10.768]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:21:10.768]    __FlashAddr=0x00000000
[11:21:10.769]    __FlashLen=0x00000000
[11:21:10.769]    __FlashArg=0x00000000
[11:21:10.770]    __FlashOp=0x00000000
[11:21:10.770]    __Result=0x00000000
[11:21:10.770]    
[11:21:10.770]    // User-defined
[11:21:10.771]    DbgMCU_CR=0x00000007
[11:21:10.771]    DbgMCU_APB1_Fz=0x00000000
[11:21:10.771]    DbgMCU_APB2_Fz=0x00000000
[11:21:10.771]    DoOptionByteLoading=0x00000000
[11:21:10.772]  </debugvars>
[11:21:10.772]  
[11:21:10.773]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:21:10.773]    <block atomic="false" info="">
[11:21:10.774]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:21:10.774]        // -> [connectionFlash <= 0x00000001]
[11:21:10.774]      __var FLASH_BASE = 0x40022000 ;
[11:21:10.774]        // -> [FLASH_BASE <= 0x40022000]
[11:21:10.775]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:21:10.775]        // -> [FLASH_CR <= 0x40022004]
[11:21:10.775]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:21:10.775]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:21:10.775]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:21:10.775]        // -> [LOCK_BIT <= 0x00000001]
[11:21:10.776]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:21:10.776]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:21:10.776]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:21:10.776]        // -> [FLASH_KEYR <= 0x4002200C]
[11:21:10.776]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:21:10.776]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:21:10.777]      __var FLASH_KEY2 = 0x02030405 ;
[11:21:10.777]        // -> [FLASH_KEY2 <= 0x02030405]
[11:21:10.777]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:21:10.777]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:21:10.777]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:21:10.777]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:21:10.778]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:21:10.778]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:21:10.778]      __var FLASH_CR_Value = 0 ;
[11:21:10.779]        // -> [FLASH_CR_Value <= 0x00000000]
[11:21:10.779]      __var DoDebugPortStop = 1 ;
[11:21:10.779]        // -> [DoDebugPortStop <= 0x00000001]
[11:21:10.779]      __var DP_CTRL_STAT = 0x4 ;
[11:21:10.779]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:21:10.779]      __var DP_SELECT = 0x8 ;
[11:21:10.780]        // -> [DP_SELECT <= 0x00000008]
[11:21:10.780]    </block>
[11:21:10.780]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:21:10.780]      // if-block "connectionFlash && DoOptionByteLoading"
[11:21:10.781]        // =>  FALSE
[11:21:10.781]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:21:10.781]    </control>
[11:21:10.781]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:21:10.781]      // if-block "DoDebugPortStop"
[11:21:10.782]        // =>  TRUE
[11:21:10.782]      <block atomic="false" info="">
[11:21:10.782]        WriteDP(DP_SELECT, 0x00000000);
[11:21:10.782]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:21:10.782]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:21:10.783]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:21:10.783]      </block>
[11:21:10.783]      // end if-block "DoDebugPortStop"
[11:21:10.783]    </control>
[11:21:10.784]  </sequence>
[11:21:10.784]  
[11:43:59.468]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:43:59.468]  
[11:43:59.475]  <debugvars>
[11:43:59.476]    // Pre-defined
[11:43:59.476]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:43:59.477]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:43:59.477]    __dp=0x00000000
[11:43:59.477]    __ap=0x00000000
[11:43:59.477]    __traceout=0x00000000      (Trace Disabled)
[11:43:59.477]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:43:59.478]    __FlashAddr=0x00000000
[11:43:59.478]    __FlashLen=0x00000000
[11:43:59.478]    __FlashArg=0x00000000
[11:43:59.478]    __FlashOp=0x00000000
[11:43:59.478]    __Result=0x00000000
[11:43:59.478]    
[11:43:59.478]    // User-defined
[11:43:59.478]    DbgMCU_CR=0x00000007
[11:43:59.478]    DbgMCU_APB1_Fz=0x00000000
[11:43:59.479]    DbgMCU_APB2_Fz=0x00000000
[11:43:59.479]    DoOptionByteLoading=0x00000000
[11:43:59.480]  </debugvars>
[11:43:59.480]  
[11:43:59.480]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:43:59.481]    <block atomic="false" info="">
[11:43:59.481]      Sequence("CheckID");
[11:43:59.481]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:43:59.481]          <block atomic="false" info="">
[11:43:59.482]            __var pidr1 = 0;
[11:43:59.482]              // -> [pidr1 <= 0x00000000]
[11:43:59.483]            __var pidr2 = 0;
[11:43:59.483]              // -> [pidr2 <= 0x00000000]
[11:43:59.483]            __var jep106id = 0;
[11:43:59.483]              // -> [jep106id <= 0x00000000]
[11:43:59.483]            __var ROMTableBase = 0;
[11:43:59.484]              // -> [ROMTableBase <= 0x00000000]
[11:43:59.484]            __ap = 0;      // AHB-AP
[11:43:59.484]              // -> [__ap <= 0x00000000]
[11:43:59.484]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:43:59.485]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:43:59.485]              // -> [ROMTableBase <= 0xF0000000]
[11:43:59.485]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:43:59.486]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:43:59.486]              // -> [pidr1 <= 0x00000004]
[11:43:59.487]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:43:59.487]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:43:59.488]              // -> [pidr2 <= 0x0000000A]
[11:43:59.488]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:43:59.488]              // -> [jep106id <= 0x00000020]
[11:43:59.488]          </block>
[11:43:59.489]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:43:59.489]            // if-block "jep106id != 0x20"
[11:43:59.489]              // =>  FALSE
[11:43:59.489]            // skip if-block "jep106id != 0x20"
[11:43:59.489]          </control>
[11:43:59.489]        </sequence>
[11:43:59.490]    </block>
[11:43:59.490]  </sequence>
[11:43:59.490]  
[11:43:59.502]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:43:59.502]  
[11:43:59.502]  <debugvars>
[11:43:59.503]    // Pre-defined
[11:43:59.503]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:43:59.503]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:43:59.503]    __dp=0x00000000
[11:43:59.503]    __ap=0x00000000
[11:43:59.503]    __traceout=0x00000000      (Trace Disabled)
[11:43:59.504]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:43:59.504]    __FlashAddr=0x00000000
[11:43:59.504]    __FlashLen=0x00000000
[11:43:59.504]    __FlashArg=0x00000000
[11:43:59.505]    __FlashOp=0x00000000
[11:43:59.505]    __Result=0x00000000
[11:43:59.505]    
[11:43:59.505]    // User-defined
[11:43:59.505]    DbgMCU_CR=0x00000007
[11:43:59.505]    DbgMCU_APB1_Fz=0x00000000
[11:43:59.505]    DbgMCU_APB2_Fz=0x00000000
[11:43:59.506]    DoOptionByteLoading=0x00000000
[11:43:59.506]  </debugvars>
[11:43:59.506]  
[11:43:59.506]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:43:59.507]    <block atomic="false" info="">
[11:43:59.507]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:43:59.508]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:43:59.508]    </block>
[11:43:59.509]    <block atomic="false" info="DbgMCU registers">
[11:43:59.509]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:43:59.509]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:43:59.510]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:43:59.510]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:43:59.512]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:43:59.512]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:43:59.513]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:43:59.513]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:43:59.515]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:43:59.515]    </block>
[11:43:59.515]  </sequence>
[11:43:59.516]  
[11:44:07.459]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:44:07.459]  
[11:44:07.460]  <debugvars>
[11:44:07.460]    // Pre-defined
[11:44:07.462]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:44:07.462]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:44:07.462]    __dp=0x00000000
[11:44:07.464]    __ap=0x00000000
[11:44:07.464]    __traceout=0x00000000      (Trace Disabled)
[11:44:07.465]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:44:07.465]    __FlashAddr=0x00000000
[11:44:07.466]    __FlashLen=0x00000000
[11:44:07.466]    __FlashArg=0x00000000
[11:44:07.466]    __FlashOp=0x00000000
[11:44:07.466]    __Result=0x00000000
[11:44:07.467]    
[11:44:07.467]    // User-defined
[11:44:07.467]    DbgMCU_CR=0x00000007
[11:44:07.467]    DbgMCU_APB1_Fz=0x00000000
[11:44:07.467]    DbgMCU_APB2_Fz=0x00000000
[11:44:07.468]    DoOptionByteLoading=0x00000000
[11:44:07.468]  </debugvars>
[11:44:07.468]  
[11:44:07.468]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:44:07.470]    <block atomic="false" info="">
[11:44:07.470]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:44:07.470]        // -> [connectionFlash <= 0x00000001]
[11:44:07.470]      __var FLASH_BASE = 0x40022000 ;
[11:44:07.470]        // -> [FLASH_BASE <= 0x40022000]
[11:44:07.471]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:44:07.471]        // -> [FLASH_CR <= 0x40022004]
[11:44:07.471]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:44:07.471]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:44:07.472]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:44:07.472]        // -> [LOCK_BIT <= 0x00000001]
[11:44:07.472]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:44:07.472]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:44:07.472]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:44:07.473]        // -> [FLASH_KEYR <= 0x4002200C]
[11:44:07.473]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:44:07.473]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:44:07.473]      __var FLASH_KEY2 = 0x02030405 ;
[11:44:07.474]        // -> [FLASH_KEY2 <= 0x02030405]
[11:44:07.474]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:44:07.474]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:44:07.474]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:44:07.475]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:44:07.475]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:44:07.475]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:44:07.475]      __var FLASH_CR_Value = 0 ;
[11:44:07.475]        // -> [FLASH_CR_Value <= 0x00000000]
[11:44:07.476]      __var DoDebugPortStop = 1 ;
[11:44:07.476]        // -> [DoDebugPortStop <= 0x00000001]
[11:44:07.476]      __var DP_CTRL_STAT = 0x4 ;
[11:44:07.476]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:44:07.476]      __var DP_SELECT = 0x8 ;
[11:44:07.476]        // -> [DP_SELECT <= 0x00000008]
[11:44:07.477]    </block>
[11:44:07.477]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:44:07.477]      // if-block "connectionFlash && DoOptionByteLoading"
[11:44:07.477]        // =>  FALSE
[11:44:07.477]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:44:07.478]    </control>
[11:44:07.478]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:44:07.478]      // if-block "DoDebugPortStop"
[11:44:07.478]        // =>  TRUE
[11:44:07.478]      <block atomic="false" info="">
[11:44:07.478]        WriteDP(DP_SELECT, 0x00000000);
[11:44:07.478]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:44:07.479]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:44:07.479]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:44:07.480]      </block>
[11:44:07.480]      // end if-block "DoDebugPortStop"
[11:44:07.480]    </control>
[11:44:07.481]  </sequence>
[11:44:07.481]  
[12:02:55.676]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[12:02:55.676]  
[12:02:55.676]  <debugvars>
[12:02:55.676]    // Pre-defined
[12:02:55.677]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:02:55.677]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:02:55.677]    __dp=0x00000000
[12:02:55.677]    __ap=0x00000000
[12:02:55.677]    __traceout=0x00000000      (Trace Disabled)
[12:02:55.678]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:02:55.678]    __FlashAddr=0x00000000
[12:02:55.678]    __FlashLen=0x00000000
[12:02:55.678]    __FlashArg=0x00000000
[12:02:55.678]    __FlashOp=0x00000000
[12:02:55.679]    __Result=0x00000000
[12:02:55.680]    
[12:02:55.680]    // User-defined
[12:02:55.680]    DbgMCU_CR=0x00000007
[12:02:55.680]    DbgMCU_APB1_Fz=0x00000000
[12:02:55.680]    DbgMCU_APB2_Fz=0x00000000
[12:02:55.681]    DoOptionByteLoading=0x00000000
[12:02:55.681]  </debugvars>
[12:02:55.681]  
[12:02:55.681]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[12:02:55.681]    <block atomic="false" info="">
[12:02:55.681]      Sequence("CheckID");
[12:02:55.681]        <sequence name="CheckID" Pname="" disable="false" info="">
[12:02:55.681]          <block atomic="false" info="">
[12:02:55.682]            __var pidr1 = 0;
[12:02:55.682]              // -> [pidr1 <= 0x00000000]
[12:02:55.682]            __var pidr2 = 0;
[12:02:55.682]              // -> [pidr2 <= 0x00000000]
[12:02:55.682]            __var jep106id = 0;
[12:02:55.683]              // -> [jep106id <= 0x00000000]
[12:02:55.683]            __var ROMTableBase = 0;
[12:02:55.683]              // -> [ROMTableBase <= 0x00000000]
[12:02:55.684]            __ap = 0;      // AHB-AP
[12:02:55.684]              // -> [__ap <= 0x00000000]
[12:02:55.684]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[12:02:55.685]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[12:02:55.685]              // -> [ROMTableBase <= 0xF0000000]
[12:02:55.685]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[12:02:55.687]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[12:02:55.687]              // -> [pidr1 <= 0x00000004]
[12:02:55.688]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[12:02:55.688]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[12:02:55.689]              // -> [pidr2 <= 0x0000000A]
[12:02:55.689]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[12:02:55.689]              // -> [jep106id <= 0x00000020]
[12:02:55.689]          </block>
[12:02:55.690]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[12:02:55.690]            // if-block "jep106id != 0x20"
[12:02:55.690]              // =>  FALSE
[12:02:55.690]            // skip if-block "jep106id != 0x20"
[12:02:55.691]          </control>
[12:02:55.691]        </sequence>
[12:02:55.691]    </block>
[12:02:55.691]  </sequence>
[12:02:55.691]  
[12:02:55.705]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[12:02:55.705]  
[12:02:55.709]  <debugvars>
[12:02:55.710]    // Pre-defined
[12:02:55.711]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:02:55.711]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:02:55.711]    __dp=0x00000000
[12:02:55.711]    __ap=0x00000000
[12:02:55.711]    __traceout=0x00000000      (Trace Disabled)
[12:02:55.711]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:02:55.712]    __FlashAddr=0x00000000
[12:02:55.712]    __FlashLen=0x00000000
[12:02:55.712]    __FlashArg=0x00000000
[12:02:55.712]    __FlashOp=0x00000000
[12:02:55.712]    __Result=0x00000000
[12:02:55.713]    
[12:02:55.713]    // User-defined
[12:02:55.713]    DbgMCU_CR=0x00000007
[12:02:55.714]    DbgMCU_APB1_Fz=0x00000000
[12:02:55.714]    DbgMCU_APB2_Fz=0x00000000
[12:02:55.714]    DoOptionByteLoading=0x00000000
[12:02:55.714]  </debugvars>
[12:02:55.714]  
[12:02:55.715]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[12:02:55.715]    <block atomic="false" info="">
[12:02:55.715]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[12:02:55.715]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[12:02:55.716]    </block>
[12:02:55.716]    <block atomic="false" info="DbgMCU registers">
[12:02:55.716]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[12:02:55.718]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[12:02:55.719]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[12:02:55.719]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[12:02:55.720]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[12:02:55.721]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[12:02:55.721]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[12:02:55.721]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[12:02:55.722]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[12:02:55.723]    </block>
[12:02:55.723]  </sequence>
[12:02:55.723]  
[12:03:03.677]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[12:03:03.677]  
[12:03:03.678]  <debugvars>
[12:03:03.679]    // Pre-defined
[12:03:03.679]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:03:03.680]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:03:03.680]    __dp=0x00000000
[12:03:03.681]    __ap=0x00000000
[12:03:03.681]    __traceout=0x00000000      (Trace Disabled)
[12:03:03.681]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:03:03.681]    __FlashAddr=0x00000000
[12:03:03.682]    __FlashLen=0x00000000
[12:03:03.682]    __FlashArg=0x00000000
[12:03:03.682]    __FlashOp=0x00000000
[12:03:03.684]    __Result=0x00000000
[12:03:03.684]    
[12:03:03.684]    // User-defined
[12:03:03.684]    DbgMCU_CR=0x00000007
[12:03:03.684]    DbgMCU_APB1_Fz=0x00000000
[12:03:03.684]    DbgMCU_APB2_Fz=0x00000000
[12:03:03.684]    DoOptionByteLoading=0x00000000
[12:03:03.684]  </debugvars>
[12:03:03.685]  
[12:03:03.685]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[12:03:03.685]    <block atomic="false" info="">
[12:03:03.685]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[12:03:03.685]        // -> [connectionFlash <= 0x00000001]
[12:03:03.685]      __var FLASH_BASE = 0x40022000 ;
[12:03:03.686]        // -> [FLASH_BASE <= 0x40022000]
[12:03:03.686]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[12:03:03.686]        // -> [FLASH_CR <= 0x40022004]
[12:03:03.686]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[12:03:03.686]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[12:03:03.687]      __var LOCK_BIT = ( 1 << 0 ) ;
[12:03:03.687]        // -> [LOCK_BIT <= 0x00000001]
[12:03:03.687]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[12:03:03.687]        // -> [OPTLOCK_BIT <= 0x00000004]
[12:03:03.688]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[12:03:03.688]        // -> [FLASH_KEYR <= 0x4002200C]
[12:03:03.688]      __var FLASH_KEY1 = 0x89ABCDEF ;
[12:03:03.688]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[12:03:03.689]      __var FLASH_KEY2 = 0x02030405 ;
[12:03:03.690]        // -> [FLASH_KEY2 <= 0x02030405]
[12:03:03.690]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[12:03:03.690]        // -> [FLASH_OPTKEYR <= 0x40022014]
[12:03:03.690]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[12:03:03.690]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[12:03:03.691]      __var FLASH_OPTKEY2 = 0x24252627 ;
[12:03:03.691]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[12:03:03.691]      __var FLASH_CR_Value = 0 ;
[12:03:03.691]        // -> [FLASH_CR_Value <= 0x00000000]
[12:03:03.691]      __var DoDebugPortStop = 1 ;
[12:03:03.691]        // -> [DoDebugPortStop <= 0x00000001]
[12:03:03.691]      __var DP_CTRL_STAT = 0x4 ;
[12:03:03.691]        // -> [DP_CTRL_STAT <= 0x00000004]
[12:03:03.692]      __var DP_SELECT = 0x8 ;
[12:03:03.692]        // -> [DP_SELECT <= 0x00000008]
[12:03:03.692]    </block>
[12:03:03.693]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[12:03:03.694]      // if-block "connectionFlash && DoOptionByteLoading"
[12:03:03.694]        // =>  FALSE
[12:03:03.694]      // skip if-block "connectionFlash && DoOptionByteLoading"
[12:03:03.694]    </control>
[12:03:03.694]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[12:03:03.694]      // if-block "DoDebugPortStop"
[12:03:03.694]        // =>  TRUE
[12:03:03.694]      <block atomic="false" info="">
[12:03:03.694]        WriteDP(DP_SELECT, 0x00000000);
[12:03:03.695]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[12:03:03.695]        WriteDP(DP_CTRL_STAT, 0x00000000);
[12:03:03.696]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[12:03:03.696]      </block>
[12:03:03.696]      // end if-block "DoDebugPortStop"
[12:03:03.696]    </control>
[12:03:03.696]  </sequence>
[12:03:03.698]  
[13:37:37.502]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[13:37:37.502]  
[13:37:37.510]  <debugvars>
[13:37:37.511]    // Pre-defined
[13:37:37.511]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:37:37.511]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:37:37.511]    __dp=0x00000000
[13:37:37.511]    __ap=0x00000000
[13:37:37.511]    __traceout=0x00000000      (Trace Disabled)
[13:37:37.512]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:37:37.512]    __FlashAddr=0x00000000
[13:37:37.512]    __FlashLen=0x00000000
[13:37:37.512]    __FlashArg=0x00000000
[13:37:37.512]    __FlashOp=0x00000000
[13:37:37.513]    __Result=0x00000000
[13:37:37.513]    
[13:37:37.513]    // User-defined
[13:37:37.513]    DbgMCU_CR=0x00000007
[13:37:37.513]    DbgMCU_APB1_Fz=0x00000000
[13:37:37.513]    DbgMCU_APB2_Fz=0x00000000
[13:37:37.514]    DoOptionByteLoading=0x00000000
[13:37:37.514]  </debugvars>
[13:37:37.514]  
[13:37:37.514]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[13:37:37.514]    <block atomic="false" info="">
[13:37:37.515]      Sequence("CheckID");
[13:37:37.515]        <sequence name="CheckID" Pname="" disable="false" info="">
[13:37:37.515]          <block atomic="false" info="">
[13:37:37.515]            __var pidr1 = 0;
[13:37:37.515]              // -> [pidr1 <= 0x00000000]
[13:37:37.516]            __var pidr2 = 0;
[13:37:37.516]              // -> [pidr2 <= 0x00000000]
[13:37:37.516]            __var jep106id = 0;
[13:37:37.516]              // -> [jep106id <= 0x00000000]
[13:37:37.517]            __var ROMTableBase = 0;
[13:37:37.517]              // -> [ROMTableBase <= 0x00000000]
[13:37:37.517]            __ap = 0;      // AHB-AP
[13:37:37.517]              // -> [__ap <= 0x00000000]
[13:37:37.517]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[13:37:37.518]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[13:37:37.518]              // -> [ROMTableBase <= 0xF0000000]
[13:37:37.518]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[13:37:37.519]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[13:37:37.521]              // -> [pidr1 <= 0x00000004]
[13:37:37.521]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[13:37:37.522]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[13:37:37.522]              // -> [pidr2 <= 0x0000000A]
[13:37:37.522]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[13:37:37.522]              // -> [jep106id <= 0x00000020]
[13:37:37.522]          </block>
[13:37:37.523]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[13:37:37.523]            // if-block "jep106id != 0x20"
[13:37:37.523]              // =>  FALSE
[13:37:37.523]            // skip if-block "jep106id != 0x20"
[13:37:37.523]          </control>
[13:37:37.524]        </sequence>
[13:37:37.524]    </block>
[13:37:37.524]  </sequence>
[13:37:37.524]  
[13:37:37.538]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[13:37:37.538]  
[13:37:37.539]  <debugvars>
[13:37:37.539]    // Pre-defined
[13:37:37.539]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:37:37.539]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:37:37.539]    __dp=0x00000000
[13:37:37.540]    __ap=0x00000000
[13:37:37.540]    __traceout=0x00000000      (Trace Disabled)
[13:37:37.540]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:37:37.540]    __FlashAddr=0x00000000
[13:37:37.540]    __FlashLen=0x00000000
[13:37:37.541]    __FlashArg=0x00000000
[13:37:37.541]    __FlashOp=0x00000000
[13:37:37.541]    __Result=0x00000000
[13:37:37.541]    
[13:37:37.541]    // User-defined
[13:37:37.541]    DbgMCU_CR=0x00000007
[13:37:37.541]    DbgMCU_APB1_Fz=0x00000000
[13:37:37.541]    DbgMCU_APB2_Fz=0x00000000
[13:37:37.542]    DoOptionByteLoading=0x00000000
[13:37:37.542]  </debugvars>
[13:37:37.542]  
[13:37:37.543]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[13:37:37.543]    <block atomic="false" info="">
[13:37:37.543]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[13:37:37.544]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[13:37:37.544]    </block>
[13:37:37.545]    <block atomic="false" info="DbgMCU registers">
[13:37:37.545]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[13:37:37.546]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[13:37:37.547]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[13:37:37.547]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[13:37:37.548]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[13:37:37.548]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[13:37:37.549]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:37:37.549]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[13:37:37.550]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:37:37.550]    </block>
[13:37:37.550]  </sequence>
[13:37:37.551]  
[13:37:45.645]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[13:37:45.645]  
[13:37:45.646]  <debugvars>
[13:37:45.646]    // Pre-defined
[13:37:45.646]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:37:45.647]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:37:45.647]    __dp=0x00000000
[13:37:45.647]    __ap=0x00000000
[13:37:45.648]    __traceout=0x00000000      (Trace Disabled)
[13:37:45.648]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:37:45.648]    __FlashAddr=0x00000000
[13:37:45.649]    __FlashLen=0x00000000
[13:37:45.649]    __FlashArg=0x00000000
[13:37:45.649]    __FlashOp=0x00000000
[13:37:45.649]    __Result=0x00000000
[13:37:45.650]    
[13:37:45.650]    // User-defined
[13:37:45.650]    DbgMCU_CR=0x00000007
[13:37:45.650]    DbgMCU_APB1_Fz=0x00000000
[13:37:45.650]    DbgMCU_APB2_Fz=0x00000000
[13:37:45.650]    DoOptionByteLoading=0x00000000
[13:37:45.651]  </debugvars>
[13:37:45.651]  
[13:37:45.651]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[13:37:45.651]    <block atomic="false" info="">
[13:37:45.651]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[13:37:45.651]        // -> [connectionFlash <= 0x00000001]
[13:37:45.652]      __var FLASH_BASE = 0x40022000 ;
[13:37:45.652]        // -> [FLASH_BASE <= 0x40022000]
[13:37:45.652]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[13:37:45.652]        // -> [FLASH_CR <= 0x40022004]
[13:37:45.652]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[13:37:45.653]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[13:37:45.653]      __var LOCK_BIT = ( 1 << 0 ) ;
[13:37:45.653]        // -> [LOCK_BIT <= 0x00000001]
[13:37:45.653]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[13:37:45.653]        // -> [OPTLOCK_BIT <= 0x00000004]
[13:37:45.653]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[13:37:45.654]        // -> [FLASH_KEYR <= 0x4002200C]
[13:37:45.654]      __var FLASH_KEY1 = 0x89ABCDEF ;
[13:37:45.654]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[13:37:45.654]      __var FLASH_KEY2 = 0x02030405 ;
[13:37:45.654]        // -> [FLASH_KEY2 <= 0x02030405]
[13:37:45.655]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[13:37:45.655]        // -> [FLASH_OPTKEYR <= 0x40022014]
[13:37:45.655]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[13:37:45.655]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[13:37:45.655]      __var FLASH_OPTKEY2 = 0x24252627 ;
[13:37:45.655]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[13:37:45.656]      __var FLASH_CR_Value = 0 ;
[13:37:45.656]        // -> [FLASH_CR_Value <= 0x00000000]
[13:37:45.656]      __var DoDebugPortStop = 1 ;
[13:37:45.656]        // -> [DoDebugPortStop <= 0x00000001]
[13:37:45.656]      __var DP_CTRL_STAT = 0x4 ;
[13:37:45.656]        // -> [DP_CTRL_STAT <= 0x00000004]
[13:37:45.657]      __var DP_SELECT = 0x8 ;
[13:37:45.657]        // -> [DP_SELECT <= 0x00000008]
[13:37:45.658]    </block>
[13:37:45.658]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[13:37:45.658]      // if-block "connectionFlash && DoOptionByteLoading"
[13:37:45.658]        // =>  FALSE
[13:37:45.659]      // skip if-block "connectionFlash && DoOptionByteLoading"
[13:37:45.659]    </control>
[13:37:45.659]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[13:37:45.660]      // if-block "DoDebugPortStop"
[13:37:45.660]        // =>  TRUE
[13:37:45.660]      <block atomic="false" info="">
[13:37:45.660]        WriteDP(DP_SELECT, 0x00000000);
[13:37:45.661]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[13:37:45.661]        WriteDP(DP_CTRL_STAT, 0x00000000);
[13:37:45.661]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[13:37:45.662]      </block>
[13:37:45.663]      // end if-block "DoDebugPortStop"
[13:37:45.663]    </control>
[13:37:45.663]  </sequence>
[13:37:45.663]  
[13:44:33.761]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[13:44:33.761]  
[13:44:33.761]  <debugvars>
[13:44:33.761]    // Pre-defined
[13:44:33.762]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:44:33.763]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:44:33.763]    __dp=0x00000000
[13:44:33.763]    __ap=0x00000000
[13:44:33.764]    __traceout=0x00000000      (Trace Disabled)
[13:44:33.764]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:44:33.764]    __FlashAddr=0x00000000
[13:44:33.765]    __FlashLen=0x00000000
[13:44:33.765]    __FlashArg=0x00000000
[13:44:33.765]    __FlashOp=0x00000000
[13:44:33.766]    __Result=0x00000000
[13:44:33.766]    
[13:44:33.766]    // User-defined
[13:44:33.766]    DbgMCU_CR=0x00000007
[13:44:33.766]    DbgMCU_APB1_Fz=0x00000000
[13:44:33.766]    DbgMCU_APB2_Fz=0x00000000
[13:44:33.767]    DoOptionByteLoading=0x00000000
[13:44:33.767]  </debugvars>
[13:44:33.767]  
[13:44:33.767]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[13:44:33.767]    <block atomic="false" info="">
[13:44:33.768]      Sequence("CheckID");
[13:44:33.768]        <sequence name="CheckID" Pname="" disable="false" info="">
[13:44:33.768]          <block atomic="false" info="">
[13:44:33.768]            __var pidr1 = 0;
[13:44:33.768]              // -> [pidr1 <= 0x00000000]
[13:44:33.768]            __var pidr2 = 0;
[13:44:33.769]              // -> [pidr2 <= 0x00000000]
[13:44:33.769]            __var jep106id = 0;
[13:44:33.769]              // -> [jep106id <= 0x00000000]
[13:44:33.769]            __var ROMTableBase = 0;
[13:44:33.769]              // -> [ROMTableBase <= 0x00000000]
[13:44:33.770]            __ap = 0;      // AHB-AP
[13:44:33.770]              // -> [__ap <= 0x00000000]
[13:44:33.770]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[13:44:33.771]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[13:44:33.771]              // -> [ROMTableBase <= 0xF0000000]
[13:44:33.771]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[13:44:33.771]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[13:44:33.773]              // -> [pidr1 <= 0x00000004]
[13:44:33.773]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[13:44:33.774]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[13:44:33.775]              // -> [pidr2 <= 0x0000000A]
[13:44:33.775]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[13:44:33.775]              // -> [jep106id <= 0x00000020]
[13:44:33.776]          </block>
[13:44:33.776]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[13:44:33.776]            // if-block "jep106id != 0x20"
[13:44:33.776]              // =>  FALSE
[13:44:33.776]            // skip if-block "jep106id != 0x20"
[13:44:33.776]          </control>
[13:44:33.777]        </sequence>
[13:44:33.777]    </block>
[13:44:33.777]  </sequence>
[13:44:33.777]  
[13:44:33.791]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[13:44:33.791]  
[13:44:33.816]  <debugvars>
[13:44:33.866]    // Pre-defined
[13:44:33.867]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:44:33.867]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:44:33.867]    __dp=0x00000000
[13:44:33.867]    __ap=0x00000000
[13:44:33.870]    __traceout=0x00000000      (Trace Disabled)
[13:44:33.870]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:44:33.870]    __FlashAddr=0x00000000
[13:44:33.870]    __FlashLen=0x00000000
[13:44:33.870]    __FlashArg=0x00000000
[13:44:33.870]    __FlashOp=0x00000000
[13:44:33.870]    __Result=0x00000000
[13:44:33.870]    
[13:44:33.870]    // User-defined
[13:44:33.870]    DbgMCU_CR=0x00000007
[13:44:33.870]    DbgMCU_APB1_Fz=0x00000000
[13:44:33.870]    DbgMCU_APB2_Fz=0x00000000
[13:44:33.870]    DoOptionByteLoading=0x00000000
[13:44:33.870]  </debugvars>
[13:44:33.870]  
[13:44:33.870]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[13:44:33.870]    <block atomic="false" info="">
[13:44:33.870]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[13:44:33.870]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[13:44:33.870]    </block>
[13:44:33.870]    <block atomic="false" info="DbgMCU registers">
[13:44:33.870]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[13:44:33.882]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[13:44:33.884]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[13:44:33.884]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[13:44:33.886]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[13:44:33.886]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[13:44:33.888]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:44:33.888]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[13:44:33.890]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:44:33.890]    </block>
[13:44:33.892]  </sequence>
[13:44:33.892]  
[13:44:41.737]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[13:44:41.737]  
[13:44:41.737]  <debugvars>
[13:44:41.737]    // Pre-defined
[13:44:41.737]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:44:41.737]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:44:41.737]    __dp=0x00000000
[13:44:41.737]    __ap=0x00000000
[13:44:41.742]    __traceout=0x00000000      (Trace Disabled)
[13:44:41.742]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:44:41.742]    __FlashAddr=0x00000000
[13:44:41.742]    __FlashLen=0x00000000
[13:44:41.742]    __FlashArg=0x00000000
[13:44:41.742]    __FlashOp=0x00000000
[13:44:41.742]    __Result=0x00000000
[13:44:41.742]    
[13:44:41.742]    // User-defined
[13:44:41.742]    DbgMCU_CR=0x00000007
[13:44:41.742]    DbgMCU_APB1_Fz=0x00000000
[13:44:41.742]    DbgMCU_APB2_Fz=0x00000000
[13:44:41.742]    DoOptionByteLoading=0x00000000
[13:44:41.742]  </debugvars>
[13:44:41.742]  
[13:44:41.742]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[13:44:41.742]    <block atomic="false" info="">
[13:44:41.746]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[13:44:41.746]        // -> [connectionFlash <= 0x00000001]
[13:44:41.747]      __var FLASH_BASE = 0x40022000 ;
[13:44:41.747]        // -> [FLASH_BASE <= 0x40022000]
[13:44:41.747]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[13:44:41.747]        // -> [FLASH_CR <= 0x40022004]
[13:44:41.747]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[13:44:41.748]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[13:44:41.748]      __var LOCK_BIT = ( 1 << 0 ) ;
[13:44:41.748]        // -> [LOCK_BIT <= 0x00000001]
[13:44:41.748]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[13:44:41.748]        // -> [OPTLOCK_BIT <= 0x00000004]
[13:44:41.748]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[13:44:41.748]        // -> [FLASH_KEYR <= 0x4002200C]
[13:44:41.748]      __var FLASH_KEY1 = 0x89ABCDEF ;
[13:44:41.749]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[13:44:41.749]      __var FLASH_KEY2 = 0x02030405 ;
[13:44:41.749]        // -> [FLASH_KEY2 <= 0x02030405]
[13:44:41.750]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[13:44:41.750]        // -> [FLASH_OPTKEYR <= 0x40022014]
[13:44:41.750]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[13:44:41.750]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[13:44:41.750]      __var FLASH_OPTKEY2 = 0x24252627 ;
[13:44:41.751]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[13:44:41.751]      __var FLASH_CR_Value = 0 ;
[13:44:41.751]        // -> [FLASH_CR_Value <= 0x00000000]
[13:44:41.752]      __var DoDebugPortStop = 1 ;
[13:44:41.752]        // -> [DoDebugPortStop <= 0x00000001]
[13:44:41.752]      __var DP_CTRL_STAT = 0x4 ;
[13:44:41.752]        // -> [DP_CTRL_STAT <= 0x00000004]
[13:44:41.752]      __var DP_SELECT = 0x8 ;
[13:44:41.752]        // -> [DP_SELECT <= 0x00000008]
[13:44:41.753]    </block>
[13:44:41.753]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[13:44:41.753]      // if-block "connectionFlash && DoOptionByteLoading"
[13:44:41.753]        // =>  FALSE
[13:44:41.754]      // skip if-block "connectionFlash && DoOptionByteLoading"
[13:44:41.754]    </control>
[13:44:41.754]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[13:44:41.754]      // if-block "DoDebugPortStop"
[13:44:41.755]        // =>  TRUE
[13:44:41.755]      <block atomic="false" info="">
[13:44:41.755]        WriteDP(DP_SELECT, 0x00000000);
[13:44:41.755]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[13:44:41.756]        WriteDP(DP_CTRL_STAT, 0x00000000);
[13:44:41.757]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[13:44:41.757]      </block>
[13:44:41.757]      // end if-block "DoDebugPortStop"
[13:44:41.757]    </control>
[13:44:41.757]  </sequence>
[13:44:41.758]  
[13:48:05.135]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[13:48:05.135]  
[13:48:05.135]  <debugvars>
[13:48:05.135]    // Pre-defined
[13:48:05.139]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:48:05.139]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:48:05.139]    __dp=0x00000000
[13:48:05.139]    __ap=0x00000000
[13:48:05.139]    __traceout=0x00000000      (Trace Disabled)
[13:48:05.139]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:48:05.139]    __FlashAddr=0x00000000
[13:48:05.140]    __FlashLen=0x00000000
[13:48:05.140]    __FlashArg=0x00000000
[13:48:05.140]    __FlashOp=0x00000000
[13:48:05.141]    __Result=0x00000000
[13:48:05.141]    
[13:48:05.141]    // User-defined
[13:48:05.141]    DbgMCU_CR=0x00000007
[13:48:05.141]    DbgMCU_APB1_Fz=0x00000000
[13:48:05.141]    DbgMCU_APB2_Fz=0x00000000
[13:48:05.141]    DoOptionByteLoading=0x00000000
[13:48:05.141]  </debugvars>
[13:48:05.141]  
[13:48:05.141]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[13:48:05.141]    <block atomic="false" info="">
[13:48:05.141]      Sequence("CheckID");
[13:48:05.141]        <sequence name="CheckID" Pname="" disable="false" info="">
[13:48:05.141]          <block atomic="false" info="">
[13:48:05.141]            __var pidr1 = 0;
[13:48:05.141]              // -> [pidr1 <= 0x00000000]
[13:48:05.141]            __var pidr2 = 0;
[13:48:05.141]              // -> [pidr2 <= 0x00000000]
[13:48:05.141]            __var jep106id = 0;
[13:48:05.141]              // -> [jep106id <= 0x00000000]
[13:48:05.141]            __var ROMTableBase = 0;
[13:48:05.141]              // -> [ROMTableBase <= 0x00000000]
[13:48:05.141]            __ap = 0;      // AHB-AP
[13:48:05.141]              // -> [__ap <= 0x00000000]
[13:48:05.145]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[13:48:05.145]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[13:48:05.145]              // -> [ROMTableBase <= 0xF0000000]
[13:48:05.145]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[13:48:05.145]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[13:48:05.145]              // -> [pidr1 <= 0x00000004]
[13:48:05.145]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[13:48:05.145]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[13:48:05.145]              // -> [pidr2 <= 0x0000000A]
[13:48:05.145]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[13:48:05.145]              // -> [jep106id <= 0x00000020]
[13:48:05.145]          </block>
[13:48:05.145]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[13:48:05.145]            // if-block "jep106id != 0x20"
[13:48:05.145]              // =>  FALSE
[13:48:05.151]            // skip if-block "jep106id != 0x20"
[13:48:05.151]          </control>
[13:48:05.151]        </sequence>
[13:48:05.151]    </block>
[13:48:05.151]  </sequence>
[13:48:05.151]  
[13:48:05.163]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[13:48:05.163]  
[13:48:05.163]  <debugvars>
[13:48:05.163]    // Pre-defined
[13:48:05.164]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:48:05.164]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:48:05.164]    __dp=0x00000000
[13:48:05.165]    __ap=0x00000000
[13:48:05.165]    __traceout=0x00000000      (Trace Disabled)
[13:48:05.165]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:48:05.165]    __FlashAddr=0x00000000
[13:48:05.166]    __FlashLen=0x00000000
[13:48:05.166]    __FlashArg=0x00000000
[13:48:05.166]    __FlashOp=0x00000000
[13:48:05.167]    __Result=0x00000000
[13:48:05.167]    
[13:48:05.167]    // User-defined
[13:48:05.167]    DbgMCU_CR=0x00000007
[13:48:05.168]    DbgMCU_APB1_Fz=0x00000000
[13:48:05.168]    DbgMCU_APB2_Fz=0x00000000
[13:48:05.168]    DoOptionByteLoading=0x00000000
[13:48:05.168]  </debugvars>
[13:48:05.169]  
[13:48:05.169]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[13:48:05.169]    <block atomic="false" info="">
[13:48:05.169]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[13:48:05.170]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[13:48:05.170]    </block>
[13:48:05.171]    <block atomic="false" info="DbgMCU registers">
[13:48:05.171]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[13:48:05.172]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[13:48:05.173]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[13:48:05.173]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[13:48:05.174]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[13:48:05.174]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[13:48:05.175]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:48:05.175]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[13:48:05.176]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:48:05.176]    </block>
[13:48:05.177]  </sequence>
[13:48:05.177]  
[13:48:13.055]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[13:48:13.055]  
[13:48:13.056]  <debugvars>
[13:48:13.056]    // Pre-defined
[13:48:13.056]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:48:13.056]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:48:13.056]    __dp=0x00000000
[13:48:13.056]    __ap=0x00000000
[13:48:13.056]    __traceout=0x00000000      (Trace Disabled)
[13:48:13.058]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:48:13.058]    __FlashAddr=0x00000000
[13:48:13.058]    __FlashLen=0x00000000
[13:48:13.058]    __FlashArg=0x00000000
[13:48:13.058]    __FlashOp=0x00000000
[13:48:13.058]    __Result=0x00000000
[13:48:13.058]    
[13:48:13.058]    // User-defined
[13:48:13.058]    DbgMCU_CR=0x00000007
[13:48:13.060]    DbgMCU_APB1_Fz=0x00000000
[13:48:13.060]    DbgMCU_APB2_Fz=0x00000000
[13:48:13.060]    DoOptionByteLoading=0x00000000
[13:48:13.060]  </debugvars>
[13:48:13.061]  
[13:48:13.061]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[13:48:13.061]    <block atomic="false" info="">
[13:48:13.061]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[13:48:13.061]        // -> [connectionFlash <= 0x00000001]
[13:48:13.061]      __var FLASH_BASE = 0x40022000 ;
[13:48:13.061]        // -> [FLASH_BASE <= 0x40022000]
[13:48:13.063]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[13:48:13.063]        // -> [FLASH_CR <= 0x40022004]
[13:48:13.063]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[13:48:13.063]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[13:48:13.064]      __var LOCK_BIT = ( 1 << 0 ) ;
[13:48:13.064]        // -> [LOCK_BIT <= 0x00000001]
[13:48:13.065]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[13:48:13.065]        // -> [OPTLOCK_BIT <= 0x00000004]
[13:48:13.065]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[13:48:13.066]        // -> [FLASH_KEYR <= 0x4002200C]
[13:48:13.066]      __var FLASH_KEY1 = 0x89ABCDEF ;
[13:48:13.066]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[13:48:13.066]      __var FLASH_KEY2 = 0x02030405 ;
[13:48:13.066]        // -> [FLASH_KEY2 <= 0x02030405]
[13:48:13.066]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[13:48:13.068]        // -> [FLASH_OPTKEYR <= 0x40022014]
[13:48:13.068]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[13:48:13.068]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[13:48:13.068]      __var FLASH_OPTKEY2 = 0x24252627 ;
[13:48:13.068]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[13:48:13.068]      __var FLASH_CR_Value = 0 ;
[13:48:13.070]        // -> [FLASH_CR_Value <= 0x00000000]
[13:48:13.070]      __var DoDebugPortStop = 1 ;
[13:48:13.070]        // -> [DoDebugPortStop <= 0x00000001]
[13:48:13.070]      __var DP_CTRL_STAT = 0x4 ;
[13:48:13.071]        // -> [DP_CTRL_STAT <= 0x00000004]
[13:48:13.071]      __var DP_SELECT = 0x8 ;
[13:48:13.071]        // -> [DP_SELECT <= 0x00000008]
[13:48:13.071]    </block>
[13:48:13.071]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[13:48:13.071]      // if-block "connectionFlash && DoOptionByteLoading"
[13:48:13.071]        // =>  FALSE
[13:48:13.071]      // skip if-block "connectionFlash && DoOptionByteLoading"
[13:48:13.071]    </control>
[13:48:13.073]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[13:48:13.073]      // if-block "DoDebugPortStop"
[13:48:13.073]        // =>  TRUE
[13:48:13.073]      <block atomic="false" info="">
[13:48:13.073]        WriteDP(DP_SELECT, 0x00000000);
[13:48:13.073]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[13:48:13.073]        WriteDP(DP_CTRL_STAT, 0x00000000);
[13:48:13.076]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[13:48:13.076]      </block>
[13:48:13.076]      // end if-block "DoDebugPortStop"
[13:48:13.076]    </control>
[13:48:13.076]  </sequence>
[13:48:13.076]  
[14:03:10.316]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:03:10.316]  
[14:03:10.323]  <debugvars>
[14:03:10.323]    // Pre-defined
[14:03:10.323]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:03:10.324]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:03:10.324]    __dp=0x00000000
[14:03:10.324]    __ap=0x00000000
[14:03:10.324]    __traceout=0x00000000      (Trace Disabled)
[14:03:10.324]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:03:10.324]    __FlashAddr=0x00000000
[14:03:10.324]    __FlashLen=0x00000000
[14:03:10.324]    __FlashArg=0x00000000
[14:03:10.324]    __FlashOp=0x00000000
[14:03:10.324]    __Result=0x00000000
[14:03:10.324]    
[14:03:10.324]    // User-defined
[14:03:10.326]    DbgMCU_CR=0x00000007
[14:03:10.326]    DbgMCU_APB1_Fz=0x00000000
[14:03:10.326]    DbgMCU_APB2_Fz=0x00000000
[14:03:10.326]    DoOptionByteLoading=0x00000000
[14:03:10.326]  </debugvars>
[14:03:10.326]  
[14:03:10.326]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:03:10.326]    <block atomic="false" info="">
[14:03:10.326]      Sequence("CheckID");
[14:03:10.326]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:03:10.328]          <block atomic="false" info="">
[14:03:10.328]            __var pidr1 = 0;
[14:03:10.328]              // -> [pidr1 <= 0x00000000]
[14:03:10.328]            __var pidr2 = 0;
[14:03:10.328]              // -> [pidr2 <= 0x00000000]
[14:03:10.328]            __var jep106id = 0;
[14:03:10.329]              // -> [jep106id <= 0x00000000]
[14:03:10.329]            __var ROMTableBase = 0;
[14:03:10.329]              // -> [ROMTableBase <= 0x00000000]
[14:03:10.329]            __ap = 0;      // AHB-AP
[14:03:10.329]              // -> [__ap <= 0x00000000]
[14:03:10.329]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:03:10.329]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:03:10.329]              // -> [ROMTableBase <= 0xF0000000]
[14:03:10.331]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:03:10.331]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:03:10.331]              // -> [pidr1 <= 0x00000004]
[14:03:10.331]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:03:10.333]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:03:10.334]              // -> [pidr2 <= 0x0000000A]
[14:03:10.334]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:03:10.334]              // -> [jep106id <= 0x00000020]
[14:03:10.334]          </block>
[14:03:10.334]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:03:10.334]            // if-block "jep106id != 0x20"
[14:03:10.334]              // =>  FALSE
[14:03:10.334]            // skip if-block "jep106id != 0x20"
[14:03:10.334]          </control>
[14:03:10.334]        </sequence>
[14:03:10.334]    </block>
[14:03:10.334]  </sequence>
[14:03:10.334]  
[14:03:10.350]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:03:10.350]  
[14:03:10.375]  <debugvars>
[14:03:10.375]    // Pre-defined
[14:03:10.376]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:03:10.376]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:03:10.376]    __dp=0x00000000
[14:03:10.376]    __ap=0x00000000
[14:03:10.376]    __traceout=0x00000000      (Trace Disabled)
[14:03:10.376]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:03:10.376]    __FlashAddr=0x00000000
[14:03:10.376]    __FlashLen=0x00000000
[14:03:10.376]    __FlashArg=0x00000000
[14:03:10.376]    __FlashOp=0x00000000
[14:03:10.378]    __Result=0x00000000
[14:03:10.378]    
[14:03:10.378]    // User-defined
[14:03:10.378]    DbgMCU_CR=0x00000007
[14:03:10.378]    DbgMCU_APB1_Fz=0x00000000
[14:03:10.378]    DbgMCU_APB2_Fz=0x00000000
[14:03:10.378]    DoOptionByteLoading=0x00000000
[14:03:10.378]  </debugvars>
[14:03:10.378]  
[14:03:10.381]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:03:10.381]    <block atomic="false" info="">
[14:03:10.381]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:03:10.381]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:03:10.381]    </block>
[14:03:10.381]    <block atomic="false" info="DbgMCU registers">
[14:03:10.381]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:03:10.383]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[14:03:10.383]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[14:03:10.383]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:03:10.385]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:03:10.385]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:03:10.385]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:03:10.385]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:03:10.387]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:03:10.387]    </block>
[14:03:10.387]  </sequence>
[14:03:10.387]  
[14:03:18.319]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:03:18.319]  
[14:03:18.319]  <debugvars>
[14:03:18.320]    // Pre-defined
[14:03:18.321]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:03:18.321]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:03:18.322]    __dp=0x00000000
[14:03:18.322]    __ap=0x00000000
[14:03:18.323]    __traceout=0x00000000      (Trace Disabled)
[14:03:18.323]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:03:18.323]    __FlashAddr=0x00000000
[14:03:18.323]    __FlashLen=0x00000000
[14:03:18.324]    __FlashArg=0x00000000
[14:03:18.324]    __FlashOp=0x00000000
[14:03:18.325]    __Result=0x00000000
[14:03:18.325]    
[14:03:18.325]    // User-defined
[14:03:18.325]    DbgMCU_CR=0x00000007
[14:03:18.325]    DbgMCU_APB1_Fz=0x00000000
[14:03:18.325]    DbgMCU_APB2_Fz=0x00000000
[14:03:18.326]    DoOptionByteLoading=0x00000000
[14:03:18.326]  </debugvars>
[14:03:18.327]  
[14:03:18.327]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:03:18.327]    <block atomic="false" info="">
[14:03:18.328]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:03:18.328]        // -> [connectionFlash <= 0x00000001]
[14:03:18.328]      __var FLASH_BASE = 0x40022000 ;
[14:03:18.328]        // -> [FLASH_BASE <= 0x40022000]
[14:03:18.328]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:03:18.328]        // -> [FLASH_CR <= 0x40022004]
[14:03:18.328]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:03:18.329]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:03:18.329]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:03:18.329]        // -> [LOCK_BIT <= 0x00000001]
[14:03:18.329]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:03:18.329]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:03:18.329]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:03:18.330]        // -> [FLASH_KEYR <= 0x4002200C]
[14:03:18.330]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:03:18.330]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:03:18.330]      __var FLASH_KEY2 = 0x02030405 ;
[14:03:18.330]        // -> [FLASH_KEY2 <= 0x02030405]
[14:03:18.330]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:03:18.331]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:03:18.331]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:03:18.331]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:03:18.332]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:03:18.332]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:03:18.332]      __var FLASH_CR_Value = 0 ;
[14:03:18.332]        // -> [FLASH_CR_Value <= 0x00000000]
[14:03:18.332]      __var DoDebugPortStop = 1 ;
[14:03:18.332]        // -> [DoDebugPortStop <= 0x00000001]
[14:03:18.333]      __var DP_CTRL_STAT = 0x4 ;
[14:03:18.333]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:03:18.333]      __var DP_SELECT = 0x8 ;
[14:03:18.333]        // -> [DP_SELECT <= 0x00000008]
[14:03:18.333]    </block>
[14:03:18.333]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:03:18.333]      // if-block "connectionFlash && DoOptionByteLoading"
[14:03:18.333]        // =>  FALSE
[14:03:18.333]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:03:18.333]    </control>
[14:03:18.333]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:03:18.335]      // if-block "DoDebugPortStop"
[14:03:18.335]        // =>  TRUE
[14:03:18.335]      <block atomic="false" info="">
[14:03:18.335]        WriteDP(DP_SELECT, 0x00000000);
[14:03:18.335]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:03:18.335]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:03:18.335]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:03:18.335]      </block>
[14:03:18.335]      // end if-block "DoDebugPortStop"
[14:03:18.335]    </control>
[14:03:18.335]  </sequence>
[14:03:18.335]  
[14:06:30.978]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:06:30.978]  
[14:06:30.980]  <debugvars>
[14:06:30.980]    // Pre-defined
[14:06:30.980]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:06:30.981]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:06:30.981]    __dp=0x00000000
[14:06:30.982]    __ap=0x00000000
[14:06:30.982]    __traceout=0x00000000      (Trace Disabled)
[14:06:30.982]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:06:30.982]    __FlashAddr=0x00000000
[14:06:30.983]    __FlashLen=0x00000000
[14:06:30.983]    __FlashArg=0x00000000
[14:06:30.983]    __FlashOp=0x00000000
[14:06:30.983]    __Result=0x00000000
[14:06:30.984]    
[14:06:30.984]    // User-defined
[14:06:30.984]    DbgMCU_CR=0x00000007
[14:06:30.984]    DbgMCU_APB1_Fz=0x00000000
[14:06:30.984]    DbgMCU_APB2_Fz=0x00000000
[14:06:30.985]    DoOptionByteLoading=0x00000000
[14:06:30.985]  </debugvars>
[14:06:30.985]  
[14:06:30.985]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:06:30.985]    <block atomic="false" info="">
[14:06:30.986]      Sequence("CheckID");
[14:06:30.986]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:06:30.986]          <block atomic="false" info="">
[14:06:30.986]            __var pidr1 = 0;
[14:06:30.986]              // -> [pidr1 <= 0x00000000]
[14:06:30.986]            __var pidr2 = 0;
[14:06:30.987]              // -> [pidr2 <= 0x00000000]
[14:06:30.987]            __var jep106id = 0;
[14:06:30.987]              // -> [jep106id <= 0x00000000]
[14:06:30.987]            __var ROMTableBase = 0;
[14:06:30.987]              // -> [ROMTableBase <= 0x00000000]
[14:06:30.987]            __ap = 0;      // AHB-AP
[14:06:30.987]              // -> [__ap <= 0x00000000]
[14:06:30.987]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:06:30.989]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:06:30.989]              // -> [ROMTableBase <= 0xF0000000]
[14:06:30.989]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:06:30.990]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:06:30.990]              // -> [pidr1 <= 0x00000004]
[14:06:30.991]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:06:30.992]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:06:30.992]              // -> [pidr2 <= 0x0000000A]
[14:06:30.992]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:06:30.992]              // -> [jep106id <= 0x00000020]
[14:06:30.993]          </block>
[14:06:30.993]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:06:30.994]            // if-block "jep106id != 0x20"
[14:06:30.994]              // =>  FALSE
[14:06:30.994]            // skip if-block "jep106id != 0x20"
[14:06:30.994]          </control>
[14:06:30.994]        </sequence>
[14:06:30.994]    </block>
[14:06:30.994]  </sequence>
[14:06:30.994]  
[14:06:31.008]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:06:31.008]  
[14:06:31.008]  <debugvars>
[14:06:31.008]    // Pre-defined
[14:06:31.008]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:06:31.010]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:06:31.010]    __dp=0x00000000
[14:06:31.010]    __ap=0x00000000
[14:06:31.010]    __traceout=0x00000000      (Trace Disabled)
[14:06:31.010]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:06:31.010]    __FlashAddr=0x00000000
[14:06:31.010]    __FlashLen=0x00000000
[14:06:31.010]    __FlashArg=0x00000000
[14:06:31.010]    __FlashOp=0x00000000
[14:06:31.010]    __Result=0x00000000
[14:06:31.010]    
[14:06:31.010]    // User-defined
[14:06:31.010]    DbgMCU_CR=0x00000007
[14:06:31.010]    DbgMCU_APB1_Fz=0x00000000
[14:06:31.010]    DbgMCU_APB2_Fz=0x00000000
[14:06:31.013]    DoOptionByteLoading=0x00000000
[14:06:31.013]  </debugvars>
[14:06:31.013]  
[14:06:31.013]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:06:31.013]    <block atomic="false" info="">
[14:06:31.013]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:06:31.016]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:06:31.016]    </block>
[14:06:31.016]    <block atomic="false" info="DbgMCU registers">
[14:06:31.016]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:06:31.016]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[14:06:31.018]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[14:06:31.018]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:06:31.018]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:06:31.018]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:06:31.020]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:06:31.020]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:06:31.022]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:06:31.022]    </block>
[14:06:31.022]  </sequence>
[14:06:31.022]  
[14:06:39.023]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:06:39.023]  
[14:06:39.024]  <debugvars>
[14:06:39.024]    // Pre-defined
[14:06:39.024]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:06:39.024]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:06:39.024]    __dp=0x00000000
[14:06:39.024]    __ap=0x00000000
[14:06:39.024]    __traceout=0x00000000      (Trace Disabled)
[14:06:39.024]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:06:39.024]    __FlashAddr=0x00000000
[14:06:39.024]    __FlashLen=0x00000000
[14:06:39.024]    __FlashArg=0x00000000
[14:06:39.024]    __FlashOp=0x00000000
[14:06:39.027]    __Result=0x00000000
[14:06:39.027]    
[14:06:39.027]    // User-defined
[14:06:39.027]    DbgMCU_CR=0x00000007
[14:06:39.027]    DbgMCU_APB1_Fz=0x00000000
[14:06:39.027]    DbgMCU_APB2_Fz=0x00000000
[14:06:39.027]    DoOptionByteLoading=0x00000000
[14:06:39.027]  </debugvars>
[14:06:39.027]  
[14:06:39.028]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:06:39.028]    <block atomic="false" info="">
[14:06:39.028]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:06:39.028]        // -> [connectionFlash <= 0x00000001]
[14:06:39.028]      __var FLASH_BASE = 0x40022000 ;
[14:06:39.028]        // -> [FLASH_BASE <= 0x40022000]
[14:06:39.028]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:06:39.028]        // -> [FLASH_CR <= 0x40022004]
[14:06:39.028]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:06:39.028]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:06:39.030]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:06:39.030]        // -> [LOCK_BIT <= 0x00000001]
[14:06:39.031]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:06:39.031]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:06:39.031]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:06:39.031]        // -> [FLASH_KEYR <= 0x4002200C]
[14:06:39.031]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:06:39.032]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:06:39.032]      __var FLASH_KEY2 = 0x02030405 ;
[14:06:39.032]        // -> [FLASH_KEY2 <= 0x02030405]
[14:06:39.032]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:06:39.032]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:06:39.033]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:06:39.033]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:06:39.033]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:06:39.033]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:06:39.033]      __var FLASH_CR_Value = 0 ;
[14:06:39.034]        // -> [FLASH_CR_Value <= 0x00000000]
[14:06:39.034]      __var DoDebugPortStop = 1 ;
[14:06:39.034]        // -> [DoDebugPortStop <= 0x00000001]
[14:06:39.034]      __var DP_CTRL_STAT = 0x4 ;
[14:06:39.034]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:06:39.035]      __var DP_SELECT = 0x8 ;
[14:06:39.035]        // -> [DP_SELECT <= 0x00000008]
[14:06:39.035]    </block>
[14:06:39.035]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:06:39.036]      // if-block "connectionFlash && DoOptionByteLoading"
[14:06:39.036]        // =>  FALSE
[14:06:39.036]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:06:39.036]    </control>
[14:06:39.036]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:06:39.036]      // if-block "DoDebugPortStop"
[14:06:39.036]        // =>  TRUE
[14:06:39.037]      <block atomic="false" info="">
[14:06:39.037]        WriteDP(DP_SELECT, 0x00000000);
[14:06:39.037]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:06:39.038]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:06:39.038]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:06:39.038]      </block>
[14:06:39.038]      // end if-block "DoDebugPortStop"
[14:06:39.039]    </control>
[14:06:39.039]  </sequence>
[14:06:39.039]  
[14:09:43.083]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:09:43.083]  
[14:09:43.083]  <debugvars>
[14:09:43.083]    // Pre-defined
[14:09:43.083]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:09:43.084]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:09:43.084]    __dp=0x00000000
[14:09:43.084]    __ap=0x00000000
[14:09:43.085]    __traceout=0x00000000      (Trace Disabled)
[14:09:43.085]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:09:43.085]    __FlashAddr=0x00000000
[14:09:43.085]    __FlashLen=0x00000000
[14:09:43.085]    __FlashArg=0x00000000
[14:09:43.085]    __FlashOp=0x00000000
[14:09:43.086]    __Result=0x00000000
[14:09:43.086]    
[14:09:43.086]    // User-defined
[14:09:43.086]    DbgMCU_CR=0x00000007
[14:09:43.086]    DbgMCU_APB1_Fz=0x00000000
[14:09:43.087]    DbgMCU_APB2_Fz=0x00000000
[14:09:43.087]    DoOptionByteLoading=0x00000000
[14:09:43.087]  </debugvars>
[14:09:43.087]  
[14:09:43.088]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:09:43.088]    <block atomic="false" info="">
[14:09:43.088]      Sequence("CheckID");
[14:09:43.088]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:09:43.088]          <block atomic="false" info="">
[14:09:43.088]            __var pidr1 = 0;
[14:09:43.089]              // -> [pidr1 <= 0x00000000]
[14:09:43.089]            __var pidr2 = 0;
[14:09:43.089]              // -> [pidr2 <= 0x00000000]
[14:09:43.089]            __var jep106id = 0;
[14:09:43.089]              // -> [jep106id <= 0x00000000]
[14:09:43.090]            __var ROMTableBase = 0;
[14:09:43.090]              // -> [ROMTableBase <= 0x00000000]
[14:09:43.090]            __ap = 0;      // AHB-AP
[14:09:43.091]              // -> [__ap <= 0x00000000]
[14:09:43.091]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:09:43.092]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:09:43.092]              // -> [ROMTableBase <= 0xF0000000]
[14:09:43.092]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:09:43.093]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:09:43.093]              // -> [pidr1 <= 0x00000004]
[14:09:43.093]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:09:43.094]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:09:43.095]              // -> [pidr2 <= 0x0000000A]
[14:09:43.095]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:09:43.095]              // -> [jep106id <= 0x00000020]
[14:09:43.095]          </block>
[14:09:43.095]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:09:43.096]            // if-block "jep106id != 0x20"
[14:09:43.096]              // =>  FALSE
[14:09:43.096]            // skip if-block "jep106id != 0x20"
[14:09:43.096]          </control>
[14:09:43.097]        </sequence>
[14:09:43.097]    </block>
[14:09:43.097]  </sequence>
[14:09:43.097]  
[14:09:43.111]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:09:43.111]  
[14:09:43.111]  <debugvars>
[14:09:43.112]    // Pre-defined
[14:09:43.112]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:09:43.112]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:09:43.112]    __dp=0x00000000
[14:09:43.113]    __ap=0x00000000
[14:09:43.113]    __traceout=0x00000000      (Trace Disabled)
[14:09:43.113]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:09:43.113]    __FlashAddr=0x00000000
[14:09:43.113]    __FlashLen=0x00000000
[14:09:43.113]    __FlashArg=0x00000000
[14:09:43.113]    __FlashOp=0x00000000
[14:09:43.113]    __Result=0x00000000
[14:09:43.114]    
[14:09:43.114]    // User-defined
[14:09:43.114]    DbgMCU_CR=0x00000007
[14:09:43.115]    DbgMCU_APB1_Fz=0x00000000
[14:09:43.115]    DbgMCU_APB2_Fz=0x00000000
[14:09:43.115]    DoOptionByteLoading=0x00000000
[14:09:43.115]  </debugvars>
[14:09:43.115]  
[14:09:43.116]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:09:43.116]    <block atomic="false" info="">
[14:09:43.116]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:09:43.117]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:09:43.117]    </block>
[14:09:43.117]    <block atomic="false" info="DbgMCU registers">
[14:09:43.118]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:09:43.118]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[14:09:43.120]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[14:09:43.120]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:09:43.121]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:09:43.121]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:09:43.122]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:09:43.122]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:09:43.123]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:09:43.124]    </block>
[14:09:43.124]  </sequence>
[14:09:43.124]  
[14:09:50.806]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:09:50.806]  
[14:09:50.807]  <debugvars>
[14:09:50.807]    // Pre-defined
[14:09:50.808]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:09:50.808]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:09:50.808]    __dp=0x00000000
[14:09:50.808]    __ap=0x00000000
[14:09:50.808]    __traceout=0x00000000      (Trace Disabled)
[14:09:50.809]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:09:50.809]    __FlashAddr=0x00000000
[14:09:50.810]    __FlashLen=0x00000000
[14:09:50.810]    __FlashArg=0x00000000
[14:09:50.810]    __FlashOp=0x00000000
[14:09:50.810]    __Result=0x00000000
[14:09:50.811]    
[14:09:50.811]    // User-defined
[14:09:50.811]    DbgMCU_CR=0x00000007
[14:09:50.811]    DbgMCU_APB1_Fz=0x00000000
[14:09:50.811]    DbgMCU_APB2_Fz=0x00000000
[14:09:50.812]    DoOptionByteLoading=0x00000000
[14:09:50.812]  </debugvars>
[14:09:50.812]  
[14:09:50.813]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:09:50.813]    <block atomic="false" info="">
[14:09:50.813]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:09:50.813]        // -> [connectionFlash <= 0x00000001]
[14:09:50.814]      __var FLASH_BASE = 0x40022000 ;
[14:09:50.814]        // -> [FLASH_BASE <= 0x40022000]
[14:09:50.814]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:09:50.814]        // -> [FLASH_CR <= 0x40022004]
[14:09:50.814]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:09:50.814]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:09:50.814]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:09:50.815]        // -> [LOCK_BIT <= 0x00000001]
[14:09:50.815]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:09:50.815]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:09:50.815]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:09:50.815]        // -> [FLASH_KEYR <= 0x4002200C]
[14:09:50.816]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:09:50.816]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:09:50.816]      __var FLASH_KEY2 = 0x02030405 ;
[14:09:50.816]        // -> [FLASH_KEY2 <= 0x02030405]
[14:09:50.816]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:09:50.816]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:09:50.817]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:09:50.817]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:09:50.818]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:09:50.818]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:09:50.818]      __var FLASH_CR_Value = 0 ;
[14:09:50.818]        // -> [FLASH_CR_Value <= 0x00000000]
[14:09:50.819]      __var DoDebugPortStop = 1 ;
[14:09:50.819]        // -> [DoDebugPortStop <= 0x00000001]
[14:09:50.819]      __var DP_CTRL_STAT = 0x4 ;
[14:09:50.819]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:09:50.819]      __var DP_SELECT = 0x8 ;
[14:09:50.819]        // -> [DP_SELECT <= 0x00000008]
[14:09:50.819]    </block>
[14:09:50.819]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:09:50.820]      // if-block "connectionFlash && DoOptionByteLoading"
[14:09:50.820]        // =>  FALSE
[14:09:50.821]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:09:50.821]    </control>
[14:09:50.821]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:09:50.821]      // if-block "DoDebugPortStop"
[14:09:50.822]        // =>  TRUE
[14:09:50.822]      <block atomic="false" info="">
[14:09:50.822]        WriteDP(DP_SELECT, 0x00000000);
[14:09:50.823]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:09:50.823]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:09:50.824]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:09:50.824]      </block>
[14:09:50.824]      // end if-block "DoDebugPortStop"
[14:09:50.824]    </control>
[14:09:50.824]  </sequence>
[14:09:50.824]  
[14:12:31.357]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:12:31.357]  
[14:12:31.357]  <debugvars>
[14:12:31.357]    // Pre-defined
[14:12:31.357]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:12:31.358]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:12:31.358]    __dp=0x00000000
[14:12:31.358]    __ap=0x00000000
[14:12:31.358]    __traceout=0x00000000      (Trace Disabled)
[14:12:31.359]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:12:31.359]    __FlashAddr=0x00000000
[14:12:31.359]    __FlashLen=0x00000000
[14:12:31.359]    __FlashArg=0x00000000
[14:12:31.359]    __FlashOp=0x00000000
[14:12:31.360]    __Result=0x00000000
[14:12:31.360]    
[14:12:31.360]    // User-defined
[14:12:31.361]    DbgMCU_CR=0x00000007
[14:12:31.361]    DbgMCU_APB1_Fz=0x00000000
[14:12:31.361]    DbgMCU_APB2_Fz=0x00000000
[14:12:31.361]    DoOptionByteLoading=0x00000000
[14:12:31.361]  </debugvars>
[14:12:31.361]  
[14:12:31.362]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:12:31.362]    <block atomic="false" info="">
[14:12:31.362]      Sequence("CheckID");
[14:12:31.362]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:12:31.362]          <block atomic="false" info="">
[14:12:31.362]            __var pidr1 = 0;
[14:12:31.363]              // -> [pidr1 <= 0x00000000]
[14:12:31.363]            __var pidr2 = 0;
[14:12:31.363]              // -> [pidr2 <= 0x00000000]
[14:12:31.363]            __var jep106id = 0;
[14:12:31.363]              // -> [jep106id <= 0x00000000]
[14:12:31.363]            __var ROMTableBase = 0;
[14:12:31.364]              // -> [ROMTableBase <= 0x00000000]
[14:12:31.364]            __ap = 0;      // AHB-AP
[14:12:31.364]              // -> [__ap <= 0x00000000]
[14:12:31.364]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:12:31.365]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:12:31.365]              // -> [ROMTableBase <= 0xF0000000]
[14:12:31.365]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:12:31.366]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:12:31.366]              // -> [pidr1 <= 0x00000004]
[14:12:31.367]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:12:31.368]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:12:31.368]              // -> [pidr2 <= 0x0000000A]
[14:12:31.368]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:12:31.368]              // -> [jep106id <= 0x00000020]
[14:12:31.369]          </block>
[14:12:31.369]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:12:31.369]            // if-block "jep106id != 0x20"
[14:12:31.369]              // =>  FALSE
[14:12:31.369]            // skip if-block "jep106id != 0x20"
[14:12:31.370]          </control>
[14:12:31.370]        </sequence>
[14:12:31.370]    </block>
[14:12:31.370]  </sequence>
[14:12:31.370]  
[14:12:31.381]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:12:31.381]  
[14:12:31.392]  <debugvars>
[14:12:31.397]    // Pre-defined
[14:12:31.397]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:12:31.397]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:12:31.397]    __dp=0x00000000
[14:12:31.397]    __ap=0x00000000
[14:12:31.397]    __traceout=0x00000000      (Trace Disabled)
[14:12:31.397]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:12:31.397]    __FlashAddr=0x00000000
[14:12:31.397]    __FlashLen=0x00000000
[14:12:31.397]    __FlashArg=0x00000000
[14:12:31.397]    __FlashOp=0x00000000
[14:12:31.397]    __Result=0x00000000
[14:12:31.400]    
[14:12:31.400]    // User-defined
[14:12:31.400]    DbgMCU_CR=0x00000007
[14:12:31.400]    DbgMCU_APB1_Fz=0x00000000
[14:12:31.400]    DbgMCU_APB2_Fz=0x00000000
[14:12:31.400]    DoOptionByteLoading=0x00000000
[14:12:31.400]  </debugvars>
[14:12:31.402]  
[14:12:31.402]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:12:31.402]    <block atomic="false" info="">
[14:12:31.402]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:12:31.404]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:12:31.404]    </block>
[14:12:31.404]    <block atomic="false" info="DbgMCU registers">
[14:12:31.404]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:12:31.406]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[14:12:31.407]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[14:12:31.407]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:12:31.407]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:12:31.407]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:12:31.409]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:12:31.409]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:12:31.411]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:12:31.411]    </block>
[14:12:31.412]  </sequence>
[14:12:31.412]  
[14:12:39.384]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:12:39.384]  
[14:12:39.385]  <debugvars>
[14:12:39.385]    // Pre-defined
[14:12:39.385]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:12:39.386]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:12:39.386]    __dp=0x00000000
[14:12:39.386]    __ap=0x00000000
[14:12:39.386]    __traceout=0x00000000      (Trace Disabled)
[14:12:39.387]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:12:39.387]    __FlashAddr=0x00000000
[14:12:39.387]    __FlashLen=0x00000000
[14:12:39.388]    __FlashArg=0x00000000
[14:12:39.388]    __FlashOp=0x00000000
[14:12:39.388]    __Result=0x00000000
[14:12:39.389]    
[14:12:39.389]    // User-defined
[14:12:39.389]    DbgMCU_CR=0x00000007
[14:12:39.389]    DbgMCU_APB1_Fz=0x00000000
[14:12:39.389]    DbgMCU_APB2_Fz=0x00000000
[14:12:39.389]    DoOptionByteLoading=0x00000000
[14:12:39.389]  </debugvars>
[14:12:39.390]  
[14:12:39.390]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:12:39.390]    <block atomic="false" info="">
[14:12:39.390]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:12:39.390]        // -> [connectionFlash <= 0x00000001]
[14:12:39.390]      __var FLASH_BASE = 0x40022000 ;
[14:12:39.390]        // -> [FLASH_BASE <= 0x40022000]
[14:12:39.390]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:12:39.391]        // -> [FLASH_CR <= 0x40022004]
[14:12:39.391]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:12:39.391]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:12:39.392]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:12:39.392]        // -> [LOCK_BIT <= 0x00000001]
[14:12:39.392]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:12:39.392]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:12:39.393]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:12:39.393]        // -> [FLASH_KEYR <= 0x4002200C]
[14:12:39.393]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:12:39.393]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:12:39.393]      __var FLASH_KEY2 = 0x02030405 ;
[14:12:39.394]        // -> [FLASH_KEY2 <= 0x02030405]
[14:12:39.395]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:12:39.396]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:12:39.396]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:12:39.396]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:12:39.396]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:12:39.396]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:12:39.397]      __var FLASH_CR_Value = 0 ;
[14:12:39.397]        // -> [FLASH_CR_Value <= 0x00000000]
[14:12:39.397]      __var DoDebugPortStop = 1 ;
[14:12:39.398]        // -> [DoDebugPortStop <= 0x00000001]
[14:12:39.398]      __var DP_CTRL_STAT = 0x4 ;
[14:12:39.398]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:12:39.398]      __var DP_SELECT = 0x8 ;
[14:12:39.399]        // -> [DP_SELECT <= 0x00000008]
[14:12:39.399]    </block>
[14:12:39.400]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:12:39.400]      // if-block "connectionFlash && DoOptionByteLoading"
[14:12:39.400]        // =>  FALSE
[14:12:39.400]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:12:39.401]    </control>
[14:12:39.401]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:12:39.401]      // if-block "DoDebugPortStop"
[14:12:39.401]        // =>  TRUE
[14:12:39.402]      <block atomic="false" info="">
[14:12:39.402]        WriteDP(DP_SELECT, 0x00000000);
[14:12:39.403]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:12:39.403]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:12:39.403]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:12:39.403]      </block>
[14:12:39.404]      // end if-block "DoDebugPortStop"
[14:12:39.404]    </control>
[14:12:39.404]  </sequence>
[14:12:39.404]  
[14:17:50.225]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:17:50.225]  
[14:17:50.243]  <debugvars>
[14:17:50.243]    // Pre-defined
[14:17:50.244]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:17:50.244]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:17:50.245]    __dp=0x00000000
[14:17:50.245]    __ap=0x00000000
[14:17:50.245]    __traceout=0x00000000      (Trace Disabled)
[14:17:50.245]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:17:50.246]    __FlashAddr=0x00000000
[14:17:50.246]    __FlashLen=0x00000000
[14:17:50.246]    __FlashArg=0x00000000
[14:17:50.246]    __FlashOp=0x00000000
[14:17:50.247]    __Result=0x00000000
[14:17:50.247]    
[14:17:50.247]    // User-defined
[14:17:50.247]    DbgMCU_CR=0x00000007
[14:17:50.247]    DbgMCU_APB1_Fz=0x00000000
[14:17:50.248]    DbgMCU_APB2_Fz=0x00000000
[14:17:50.248]    DoOptionByteLoading=0x00000000
[14:17:50.248]  </debugvars>
[14:17:50.249]  
[14:17:50.249]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:17:50.249]    <block atomic="false" info="">
[14:17:50.249]      Sequence("CheckID");
[14:17:50.249]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:17:50.250]          <block atomic="false" info="">
[14:17:50.250]            __var pidr1 = 0;
[14:17:50.250]              // -> [pidr1 <= 0x00000000]
[14:17:50.250]            __var pidr2 = 0;
[14:17:50.250]              // -> [pidr2 <= 0x00000000]
[14:17:50.251]            __var jep106id = 0;
[14:17:50.251]              // -> [jep106id <= 0x00000000]
[14:17:50.251]            __var ROMTableBase = 0;
[14:17:50.251]              // -> [ROMTableBase <= 0x00000000]
[14:17:50.251]            __ap = 0;      // AHB-AP
[14:17:50.252]              // -> [__ap <= 0x00000000]
[14:17:50.252]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:17:50.252]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:17:50.253]              // -> [ROMTableBase <= 0xF0000000]
[14:17:50.254]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:17:50.255]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:17:50.255]              // -> [pidr1 <= 0x00000004]
[14:17:50.255]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:17:50.256]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:17:50.256]              // -> [pidr2 <= 0x0000000A]
[14:17:50.256]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:17:50.256]              // -> [jep106id <= 0x00000020]
[14:17:50.257]          </block>
[14:17:50.257]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:17:50.257]            // if-block "jep106id != 0x20"
[14:17:50.257]              // =>  FALSE
[14:17:50.257]            // skip if-block "jep106id != 0x20"
[14:17:50.257]          </control>
[14:17:50.258]        </sequence>
[14:17:50.258]    </block>
[14:17:50.258]  </sequence>
[14:17:50.258]  
[14:17:50.271]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:17:50.271]  
[14:17:50.277]  <debugvars>
[14:17:50.277]    // Pre-defined
[14:17:50.277]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:17:50.277]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:17:50.278]    __dp=0x00000000
[14:17:50.278]    __ap=0x00000000
[14:17:50.278]    __traceout=0x00000000      (Trace Disabled)
[14:17:50.279]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:17:50.279]    __FlashAddr=0x00000000
[14:17:50.279]    __FlashLen=0x00000000
[14:17:50.279]    __FlashArg=0x00000000
[14:17:50.280]    __FlashOp=0x00000000
[14:17:50.280]    __Result=0x00000000
[14:17:50.281]    
[14:17:50.281]    // User-defined
[14:17:50.281]    DbgMCU_CR=0x00000007
[14:17:50.281]    DbgMCU_APB1_Fz=0x00000000
[14:17:50.281]    DbgMCU_APB2_Fz=0x00000000
[14:17:50.282]    DoOptionByteLoading=0x00000000
[14:17:50.282]  </debugvars>
[14:17:50.282]  
[14:17:50.283]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:17:50.283]    <block atomic="false" info="">
[14:17:50.283]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:17:50.284]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:17:50.285]    </block>
[14:17:50.285]    <block atomic="false" info="DbgMCU registers">
[14:17:50.285]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:17:50.286]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[14:17:50.287]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[14:17:50.287]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:17:50.288]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:17:50.288]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:17:50.289]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:17:50.289]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:17:50.290]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:17:50.291]    </block>
[14:17:50.291]  </sequence>
[14:17:50.291]  
[14:17:57.974]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:17:57.974]  
[14:17:57.974]  <debugvars>
[14:17:57.974]    // Pre-defined
[14:17:57.974]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:17:57.975]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:17:57.975]    __dp=0x00000000
[14:17:57.975]    __ap=0x00000000
[14:17:57.975]    __traceout=0x00000000      (Trace Disabled)
[14:17:57.975]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:17:57.975]    __FlashAddr=0x00000000
[14:17:57.975]    __FlashLen=0x00000000
[14:17:57.975]    __FlashArg=0x00000000
[14:17:57.975]    __FlashOp=0x00000000
[14:17:57.975]    __Result=0x00000000
[14:17:57.977]    
[14:17:57.977]    // User-defined
[14:17:57.977]    DbgMCU_CR=0x00000007
[14:17:57.977]    DbgMCU_APB1_Fz=0x00000000
[14:17:57.977]    DbgMCU_APB2_Fz=0x00000000
[14:17:57.977]    DoOptionByteLoading=0x00000000
[14:17:57.977]  </debugvars>
[14:17:57.977]  
[14:17:57.977]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:17:57.977]    <block atomic="false" info="">
[14:17:57.977]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:17:57.979]        // -> [connectionFlash <= 0x00000001]
[14:17:57.979]      __var FLASH_BASE = 0x40022000 ;
[14:17:57.979]        // -> [FLASH_BASE <= 0x40022000]
[14:17:57.979]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:17:57.980]        // -> [FLASH_CR <= 0x40022004]
[14:17:57.980]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:17:57.980]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:17:57.980]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:17:57.980]        // -> [LOCK_BIT <= 0x00000001]
[14:17:57.980]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:17:57.980]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:17:57.980]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:17:57.980]        // -> [FLASH_KEYR <= 0x4002200C]
[14:17:57.980]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:17:57.980]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:17:57.980]      __var FLASH_KEY2 = 0x02030405 ;
[14:17:57.980]        // -> [FLASH_KEY2 <= 0x02030405]
[14:17:57.980]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:17:57.980]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:17:57.980]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:17:57.980]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:17:57.980]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:17:57.980]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:17:57.980]      __var FLASH_CR_Value = 0 ;
[14:17:57.980]        // -> [FLASH_CR_Value <= 0x00000000]
[14:17:57.980]      __var DoDebugPortStop = 1 ;
[14:17:57.980]        // -> [DoDebugPortStop <= 0x00000001]
[14:17:57.980]      __var DP_CTRL_STAT = 0x4 ;
[14:17:57.985]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:17:57.985]      __var DP_SELECT = 0x8 ;
[14:17:57.985]        // -> [DP_SELECT <= 0x00000008]
[14:17:57.985]    </block>
[14:17:57.985]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:17:57.985]      // if-block "connectionFlash && DoOptionByteLoading"
[14:17:57.985]        // =>  FALSE
[14:17:57.985]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:17:57.985]    </control>
[14:17:57.985]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:17:57.985]      // if-block "DoDebugPortStop"
[14:17:57.985]        // =>  TRUE
[14:17:57.985]      <block atomic="false" info="">
[14:17:57.985]        WriteDP(DP_SELECT, 0x00000000);
[14:17:57.985]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:17:57.985]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:17:57.985]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:17:57.985]      </block>
[14:17:57.985]      // end if-block "DoDebugPortStop"
[14:17:57.985]    </control>
[14:17:57.985]  </sequence>
[14:17:57.985]  
[14:39:43.866]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:39:43.866]  
[14:39:43.884]  <debugvars>
[14:39:43.884]    // Pre-defined
[14:39:43.884]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:39:43.884]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:39:43.887]    __dp=0x00000000
[14:39:43.887]    __ap=0x00000000
[14:39:43.887]    __traceout=0x00000000      (Trace Disabled)
[14:39:43.887]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:39:43.888]    __FlashAddr=0x00000000
[14:39:43.888]    __FlashLen=0x00000000
[14:39:43.888]    __FlashArg=0x00000000
[14:39:43.888]    __FlashOp=0x00000000
[14:39:43.888]    __Result=0x00000000
[14:39:43.888]    
[14:39:43.888]    // User-defined
[14:39:43.888]    DbgMCU_CR=0x00000007
[14:39:43.889]    DbgMCU_APB1_Fz=0x00000000
[14:39:43.890]    DbgMCU_APB2_Fz=0x00000000
[14:39:43.890]    DoOptionByteLoading=0x00000000
[14:39:43.891]  </debugvars>
[14:39:43.891]  
[14:39:43.891]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:39:43.891]    <block atomic="false" info="">
[14:39:43.891]      Sequence("CheckID");
[14:39:43.892]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:39:43.892]          <block atomic="false" info="">
[14:39:43.892]            __var pidr1 = 0;
[14:39:43.893]              // -> [pidr1 <= 0x00000000]
[14:39:43.893]            __var pidr2 = 0;
[14:39:43.893]              // -> [pidr2 <= 0x00000000]
[14:39:43.894]            __var jep106id = 0;
[14:39:43.894]              // -> [jep106id <= 0x00000000]
[14:39:43.894]            __var ROMTableBase = 0;
[14:39:43.894]              // -> [ROMTableBase <= 0x00000000]
[14:39:43.894]            __ap = 0;      // AHB-AP
[14:39:43.894]              // -> [__ap <= 0x00000000]
[14:39:43.895]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:39:43.896]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:39:43.896]              // -> [ROMTableBase <= 0xF0000000]
[14:39:43.896]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:39:43.898]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:39:43.898]              // -> [pidr1 <= 0x00000004]
[14:39:43.898]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:39:43.898]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:39:43.898]              // -> [pidr2 <= 0x0000000A]
[14:39:43.898]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:39:43.898]              // -> [jep106id <= 0x00000020]
[14:39:43.900]          </block>
[14:39:43.900]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:39:43.900]            // if-block "jep106id != 0x20"
[14:39:43.900]              // =>  FALSE
[14:39:43.900]            // skip if-block "jep106id != 0x20"
[14:39:43.900]          </control>
[14:39:43.902]        </sequence>
[14:39:43.902]    </block>
[14:39:43.902]  </sequence>
[14:39:43.902]  
[14:39:43.914]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:39:43.914]  
[14:39:43.915]  <debugvars>
[14:39:43.915]    // Pre-defined
[14:39:43.915]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:39:43.915]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:39:43.915]    __dp=0x00000000
[14:39:43.915]    __ap=0x00000000
[14:39:43.916]    __traceout=0x00000000      (Trace Disabled)
[14:39:43.916]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:39:43.916]    __FlashAddr=0x00000000
[14:39:43.916]    __FlashLen=0x00000000
[14:39:43.916]    __FlashArg=0x00000000
[14:39:43.916]    __FlashOp=0x00000000
[14:39:43.916]    __Result=0x00000000
[14:39:43.916]    
[14:39:43.916]    // User-defined
[14:39:43.916]    DbgMCU_CR=0x00000007
[14:39:43.916]    DbgMCU_APB1_Fz=0x00000000
[14:39:43.916]    DbgMCU_APB2_Fz=0x00000000
[14:39:43.918]    DoOptionByteLoading=0x00000000
[14:39:43.918]  </debugvars>
[14:39:43.919]  
[14:39:43.919]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:39:43.919]    <block atomic="false" info="">
[14:39:43.919]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:39:43.919]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:39:43.919]    </block>
[14:39:43.919]    <block atomic="false" info="DbgMCU registers">
[14:39:43.921]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:39:43.921]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[14:39:43.922]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[14:39:43.922]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:39:43.922]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:39:43.924]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:39:43.924]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:39:43.924]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:39:43.924]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:39:43.926]    </block>
[14:39:43.926]  </sequence>
[14:39:43.926]  
[14:39:51.578]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:39:51.578]  
[14:39:51.578]  <debugvars>
[14:39:51.578]    // Pre-defined
[14:39:51.578]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:39:51.578]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:39:51.578]    __dp=0x00000000
[14:39:51.578]    __ap=0x00000000
[14:39:51.578]    __traceout=0x00000000      (Trace Disabled)
[14:39:51.578]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:39:51.580]    __FlashAddr=0x00000000
[14:39:51.580]    __FlashLen=0x00000000
[14:39:51.580]    __FlashArg=0x00000000
[14:39:51.580]    __FlashOp=0x00000000
[14:39:51.580]    __Result=0x00000000
[14:39:51.580]    
[14:39:51.580]    // User-defined
[14:39:51.580]    DbgMCU_CR=0x00000007
[14:39:51.580]    DbgMCU_APB1_Fz=0x00000000
[14:39:51.580]    DbgMCU_APB2_Fz=0x00000000
[14:39:51.582]    DoOptionByteLoading=0x00000000
[14:39:51.582]  </debugvars>
[14:39:51.582]  
[14:39:51.582]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:39:51.582]    <block atomic="false" info="">
[14:39:51.582]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:39:51.582]        // -> [connectionFlash <= 0x00000001]
[14:39:51.582]      __var FLASH_BASE = 0x40022000 ;
[14:39:51.582]        // -> [FLASH_BASE <= 0x40022000]
[14:39:51.582]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:39:51.584]        // -> [FLASH_CR <= 0x40022004]
[14:39:51.584]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:39:51.584]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:39:51.584]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:39:51.584]        // -> [LOCK_BIT <= 0x00000001]
[14:39:51.584]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:39:51.584]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:39:51.584]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:39:51.584]        // -> [FLASH_KEYR <= 0x4002200C]
[14:39:51.584]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:39:51.586]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:39:51.586]      __var FLASH_KEY2 = 0x02030405 ;
[14:39:51.586]        // -> [FLASH_KEY2 <= 0x02030405]
[14:39:51.586]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:39:51.586]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:39:51.586]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:39:51.586]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:39:51.586]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:39:51.586]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:39:51.588]      __var FLASH_CR_Value = 0 ;
[14:39:51.588]        // -> [FLASH_CR_Value <= 0x00000000]
[14:39:51.588]      __var DoDebugPortStop = 1 ;
[14:39:51.588]        // -> [DoDebugPortStop <= 0x00000001]
[14:39:51.588]      __var DP_CTRL_STAT = 0x4 ;
[14:39:51.588]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:39:51.588]      __var DP_SELECT = 0x8 ;
[14:39:51.588]        // -> [DP_SELECT <= 0x00000008]
[14:39:51.588]    </block>
[14:39:51.588]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:39:51.590]      // if-block "connectionFlash && DoOptionByteLoading"
[14:39:51.590]        // =>  FALSE
[14:39:51.590]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:39:51.590]    </control>
[14:39:51.590]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:39:51.591]      // if-block "DoDebugPortStop"
[14:39:51.591]        // =>  TRUE
[14:39:51.591]      <block atomic="false" info="">
[14:39:51.591]        WriteDP(DP_SELECT, 0x00000000);
[14:39:51.591]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:39:51.591]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:39:51.591]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:39:51.591]      </block>
[14:39:51.593]      // end if-block "DoDebugPortStop"
[14:39:51.593]    </control>
[14:39:51.593]  </sequence>
[14:39:51.593]  
[14:49:14.651]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:49:14.651]  
[14:49:14.651]  <debugvars>
[14:49:14.651]    // Pre-defined
[14:49:14.651]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:49:14.653]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:49:14.653]    __dp=0x00000000
[14:49:14.653]    __ap=0x00000000
[14:49:14.653]    __traceout=0x00000000      (Trace Disabled)
[14:49:14.654]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:49:14.654]    __FlashAddr=0x00000000
[14:49:14.654]    __FlashLen=0x00000000
[14:49:14.655]    __FlashArg=0x00000000
[14:49:14.655]    __FlashOp=0x00000000
[14:49:14.655]    __Result=0x00000000
[14:49:14.655]    
[14:49:14.655]    // User-defined
[14:49:14.655]    DbgMCU_CR=0x00000007
[14:49:14.655]    DbgMCU_APB1_Fz=0x00000000
[14:49:14.656]    DbgMCU_APB2_Fz=0x00000000
[14:49:14.656]    DoOptionByteLoading=0x00000000
[14:49:14.656]  </debugvars>
[14:49:14.656]  
[14:49:14.657]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:49:14.657]    <block atomic="false" info="">
[14:49:14.657]      Sequence("CheckID");
[14:49:14.657]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:49:14.658]          <block atomic="false" info="">
[14:49:14.658]            __var pidr1 = 0;
[14:49:14.658]              // -> [pidr1 <= 0x00000000]
[14:49:14.658]            __var pidr2 = 0;
[14:49:14.658]              // -> [pidr2 <= 0x00000000]
[14:49:14.658]            __var jep106id = 0;
[14:49:14.658]              // -> [jep106id <= 0x00000000]
[14:49:14.659]            __var ROMTableBase = 0;
[14:49:14.659]              // -> [ROMTableBase <= 0x00000000]
[14:49:14.659]            __ap = 0;      // AHB-AP
[14:49:14.659]              // -> [__ap <= 0x00000000]
[14:49:14.659]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:49:14.660]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:49:14.660]              // -> [ROMTableBase <= 0xF0000000]
[14:49:14.660]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:49:14.662]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:49:14.662]              // -> [pidr1 <= 0x00000004]
[14:49:14.662]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:49:14.663]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:49:14.663]              // -> [pidr2 <= 0x0000000A]
[14:49:14.663]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:49:14.664]              // -> [jep106id <= 0x00000020]
[14:49:14.664]          </block>
[14:49:14.665]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:49:14.665]            // if-block "jep106id != 0x20"
[14:49:14.665]              // =>  FALSE
[14:49:14.665]            // skip if-block "jep106id != 0x20"
[14:49:14.665]          </control>
[14:49:14.665]        </sequence>
[14:49:14.666]    </block>
[14:49:14.666]  </sequence>
[14:49:14.666]  
[14:49:14.679]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:49:14.679]  
[14:49:14.679]  <debugvars>
[14:49:14.679]    // Pre-defined
[14:49:14.679]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:49:14.679]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:49:14.680]    __dp=0x00000000
[14:49:14.680]    __ap=0x00000000
[14:49:14.680]    __traceout=0x00000000      (Trace Disabled)
[14:49:14.680]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:49:14.681]    __FlashAddr=0x00000000
[14:49:14.681]    __FlashLen=0x00000000
[14:49:14.681]    __FlashArg=0x00000000
[14:49:14.681]    __FlashOp=0x00000000
[14:49:14.681]    __Result=0x00000000
[14:49:14.681]    
[14:49:14.681]    // User-defined
[14:49:14.681]    DbgMCU_CR=0x00000007
[14:49:14.682]    DbgMCU_APB1_Fz=0x00000000
[14:49:14.682]    DbgMCU_APB2_Fz=0x00000000
[14:49:14.682]    DoOptionByteLoading=0x00000000
[14:49:14.683]  </debugvars>
[14:49:14.683]  
[14:49:14.683]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:49:14.683]    <block atomic="false" info="">
[14:49:14.683]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:49:14.684]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:49:14.684]    </block>
[14:49:14.684]    <block atomic="false" info="DbgMCU registers">
[14:49:14.685]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:49:14.685]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[14:49:14.686]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[14:49:14.687]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:49:14.687]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:49:14.688]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:49:14.689]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:49:14.689]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:49:14.689]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:49:14.690]    </block>
[14:49:14.690]  </sequence>
[14:49:14.690]  
[14:49:22.486]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:49:22.486]  
[14:49:22.487]  <debugvars>
[14:49:22.487]    // Pre-defined
[14:49:22.487]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:49:22.488]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:49:22.488]    __dp=0x00000000
[14:49:22.489]    __ap=0x00000000
[14:49:22.489]    __traceout=0x00000000      (Trace Disabled)
[14:49:22.489]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:49:22.490]    __FlashAddr=0x00000000
[14:49:22.490]    __FlashLen=0x00000000
[14:49:22.490]    __FlashArg=0x00000000
[14:49:22.491]    __FlashOp=0x00000000
[14:49:22.491]    __Result=0x00000000
[14:49:22.491]    
[14:49:22.491]    // User-defined
[14:49:22.491]    DbgMCU_CR=0x00000007
[14:49:22.492]    DbgMCU_APB1_Fz=0x00000000
[14:49:22.492]    DbgMCU_APB2_Fz=0x00000000
[14:49:22.492]    DoOptionByteLoading=0x00000000
[14:49:22.492]  </debugvars>
[14:49:22.492]  
[14:49:22.493]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:49:22.493]    <block atomic="false" info="">
[14:49:22.493]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:49:22.493]        // -> [connectionFlash <= 0x00000001]
[14:49:22.493]      __var FLASH_BASE = 0x40022000 ;
[14:49:22.494]        // -> [FLASH_BASE <= 0x40022000]
[14:49:22.494]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:49:22.494]        // -> [FLASH_CR <= 0x40022004]
[14:49:22.494]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:49:22.494]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:49:22.495]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:49:22.495]        // -> [LOCK_BIT <= 0x00000001]
[14:49:22.495]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:49:22.495]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:49:22.495]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:49:22.495]        // -> [FLASH_KEYR <= 0x4002200C]
[14:49:22.496]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:49:22.496]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:49:22.496]      __var FLASH_KEY2 = 0x02030405 ;
[14:49:22.496]        // -> [FLASH_KEY2 <= 0x02030405]
[14:49:22.496]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:49:22.497]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:49:22.497]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:49:22.497]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:49:22.497]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:49:22.497]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:49:22.497]      __var FLASH_CR_Value = 0 ;
[14:49:22.497]        // -> [FLASH_CR_Value <= 0x00000000]
[14:49:22.497]      __var DoDebugPortStop = 1 ;
[14:49:22.497]        // -> [DoDebugPortStop <= 0x00000001]
[14:49:22.497]      __var DP_CTRL_STAT = 0x4 ;
[14:49:22.497]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:49:22.497]      __var DP_SELECT = 0x8 ;
[14:49:22.497]        // -> [DP_SELECT <= 0x00000008]
[14:49:22.497]    </block>
[14:49:22.497]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:49:22.497]      // if-block "connectionFlash && DoOptionByteLoading"
[14:49:22.497]        // =>  FALSE
[14:49:22.497]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:49:22.497]    </control>
[14:49:22.497]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:49:22.497]      // if-block "DoDebugPortStop"
[14:49:22.497]        // =>  TRUE
[14:49:22.497]      <block atomic="false" info="">
[14:49:22.497]        WriteDP(DP_SELECT, 0x00000000);
[14:49:22.503]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:49:22.503]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:49:22.504]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:49:22.504]      </block>
[14:49:22.504]      // end if-block "DoDebugPortStop"
[14:49:22.504]    </control>
[14:49:22.505]  </sequence>
[14:49:22.505]  
[14:51:50.179]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:51:50.179]  
[14:51:50.179]  <debugvars>
[14:51:50.179]    // Pre-defined
[14:51:50.179]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:51:50.179]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:51:50.179]    __dp=0x00000000
[14:51:50.179]    __ap=0x00000000
[14:51:50.179]    __traceout=0x00000000      (Trace Disabled)
[14:51:50.179]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:51:50.179]    __FlashAddr=0x00000000
[14:51:50.179]    __FlashLen=0x00000000
[14:51:50.179]    __FlashArg=0x00000000
[14:51:50.179]    __FlashOp=0x00000000
[14:51:50.179]    __Result=0x00000000
[14:51:50.179]    
[14:51:50.179]    // User-defined
[14:51:50.179]    DbgMCU_CR=0x00000007
[14:51:50.179]    DbgMCU_APB1_Fz=0x00000000
[14:51:50.179]    DbgMCU_APB2_Fz=0x00000000
[14:51:50.179]    DoOptionByteLoading=0x00000000
[14:51:50.179]  </debugvars>
[14:51:50.179]  
[14:51:50.179]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:51:50.179]    <block atomic="false" info="">
[14:51:50.179]      Sequence("CheckID");
[14:51:50.179]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:51:50.179]          <block atomic="false" info="">
[14:51:50.179]            __var pidr1 = 0;
[14:51:50.179]              // -> [pidr1 <= 0x00000000]
[14:51:50.179]            __var pidr2 = 0;
[14:51:50.179]              // -> [pidr2 <= 0x00000000]
[14:51:50.179]            __var jep106id = 0;
[14:51:50.179]              // -> [jep106id <= 0x00000000]
[14:51:50.179]            __var ROMTableBase = 0;
[14:51:50.179]              // -> [ROMTableBase <= 0x00000000]
[14:51:50.179]            __ap = 0;      // AHB-AP
[14:51:50.179]              // -> [__ap <= 0x00000000]
[14:51:50.179]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:51:50.179]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:51:50.193]              // -> [ROMTableBase <= 0xF0000000]
[14:51:50.193]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:51:50.193]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:51:50.193]              // -> [pidr1 <= 0x00000004]
[14:51:50.193]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:51:50.193]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:51:50.193]              // -> [pidr2 <= 0x0000000A]
[14:51:50.193]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:51:50.193]              // -> [jep106id <= 0x00000020]
[14:51:50.193]          </block>
[14:51:50.193]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:51:50.193]            // if-block "jep106id != 0x20"
[14:51:50.193]              // =>  FALSE
[14:51:50.193]            // skip if-block "jep106id != 0x20"
[14:51:50.193]          </control>
[14:51:50.193]        </sequence>
[14:51:50.193]    </block>
[14:51:50.193]  </sequence>
[14:51:50.193]  
[14:51:50.208]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:51:50.208]  
[14:51:50.208]  <debugvars>
[14:51:50.208]    // Pre-defined
[14:51:50.208]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:51:50.208]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:51:50.208]    __dp=0x00000000
[14:51:50.208]    __ap=0x00000000
[14:51:50.208]    __traceout=0x00000000      (Trace Disabled)
[14:51:50.208]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:51:50.208]    __FlashAddr=0x00000000
[14:51:50.208]    __FlashLen=0x00000000
[14:51:50.208]    __FlashArg=0x00000000
[14:51:50.208]    __FlashOp=0x00000000
[14:51:50.208]    __Result=0x00000000
[14:51:50.208]    
[14:51:50.208]    // User-defined
[14:51:50.218]    DbgMCU_CR=0x00000007
[14:51:50.218]    DbgMCU_APB1_Fz=0x00000000
[14:51:50.218]    DbgMCU_APB2_Fz=0x00000000
[14:51:50.218]    DoOptionByteLoading=0x00000000
[14:51:50.218]  </debugvars>
[14:51:50.218]  
[14:51:50.218]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:51:50.218]    <block atomic="false" info="">
[14:51:50.218]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:51:50.218]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:51:50.218]    </block>
[14:51:50.218]    <block atomic="false" info="DbgMCU registers">
[14:51:50.218]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:51:50.218]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[14:51:50.223]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[14:51:50.223]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:51:50.224]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:51:50.224]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:51:50.224]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:51:50.224]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:51:50.226]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:51:50.226]    </block>
[14:51:50.226]  </sequence>
[14:51:50.226]  
[14:51:58.072]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:51:58.072]  
[14:51:58.073]  <debugvars>
[14:51:58.074]    // Pre-defined
[14:51:58.074]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:51:58.074]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:51:58.074]    __dp=0x00000000
[14:51:58.074]    __ap=0x00000000
[14:51:58.074]    __traceout=0x00000000      (Trace Disabled)
[14:51:58.074]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:51:58.074]    __FlashAddr=0x00000000
[14:51:58.074]    __FlashLen=0x00000000
[14:51:58.074]    __FlashArg=0x00000000
[14:51:58.074]    __FlashOp=0x00000000
[14:51:58.074]    __Result=0x00000000
[14:51:58.074]    
[14:51:58.074]    // User-defined
[14:51:58.074]    DbgMCU_CR=0x00000007
[14:51:58.074]    DbgMCU_APB1_Fz=0x00000000
[14:51:58.074]    DbgMCU_APB2_Fz=0x00000000
[14:51:58.074]    DoOptionByteLoading=0x00000000
[14:51:58.074]  </debugvars>
[14:51:58.074]  
[14:51:58.074]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:51:58.074]    <block atomic="false" info="">
[14:51:58.074]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:51:58.074]        // -> [connectionFlash <= 0x00000001]
[14:51:58.074]      __var FLASH_BASE = 0x40022000 ;
[14:51:58.074]        // -> [FLASH_BASE <= 0x40022000]
[14:51:58.074]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:51:58.074]        // -> [FLASH_CR <= 0x40022004]
[14:51:58.074]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:51:58.074]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:51:58.074]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:51:58.074]        // -> [LOCK_BIT <= 0x00000001]
[14:51:58.074]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:51:58.074]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:51:58.074]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:51:58.074]        // -> [FLASH_KEYR <= 0x4002200C]
[14:51:58.074]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:51:58.074]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:51:58.074]      __var FLASH_KEY2 = 0x02030405 ;
[14:51:58.074]        // -> [FLASH_KEY2 <= 0x02030405]
[14:51:58.074]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:51:58.074]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:51:58.074]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:51:58.074]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:51:58.074]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:51:58.074]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:51:58.074]      __var FLASH_CR_Value = 0 ;
[14:51:58.074]        // -> [FLASH_CR_Value <= 0x00000000]
[14:51:58.074]      __var DoDebugPortStop = 1 ;
[14:51:58.074]        // -> [DoDebugPortStop <= 0x00000001]
[14:51:58.074]      __var DP_CTRL_STAT = 0x4 ;
[14:51:58.074]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:51:58.074]      __var DP_SELECT = 0x8 ;
[14:51:58.074]        // -> [DP_SELECT <= 0x00000008]
[14:51:58.074]    </block>
[14:51:58.074]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:51:58.074]      // if-block "connectionFlash && DoOptionByteLoading"
[14:51:58.074]        // =>  FALSE
[14:51:58.074]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:51:58.074]    </control>
[14:51:58.074]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:51:58.074]      // if-block "DoDebugPortStop"
[14:51:58.074]        // =>  TRUE
[14:51:58.074]      <block atomic="false" info="">
[14:51:58.074]        WriteDP(DP_SELECT, 0x00000000);
[14:51:58.089]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:51:58.089]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:51:58.089]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:51:58.089]      </block>
[14:51:58.089]      // end if-block "DoDebugPortStop"
[14:51:58.089]    </control>
[14:51:58.091]  </sequence>
[14:51:58.091]  
[14:54:14.970]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:54:14.970]  
[14:54:14.970]  <debugvars>
[14:54:14.970]    // Pre-defined
[14:54:14.970]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:54:14.971]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:54:14.971]    __dp=0x00000000
[14:54:14.971]    __ap=0x00000000
[14:54:14.971]    __traceout=0x00000000      (Trace Disabled)
[14:54:14.971]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:54:14.971]    __FlashAddr=0x00000000
[14:54:14.971]    __FlashLen=0x00000000
[14:54:14.972]    __FlashArg=0x00000000
[14:54:14.972]    __FlashOp=0x00000000
[14:54:14.973]    __Result=0x00000000
[14:54:14.973]    
[14:54:14.973]    // User-defined
[14:54:14.973]    DbgMCU_CR=0x00000007
[14:54:14.973]    DbgMCU_APB1_Fz=0x00000000
[14:54:14.973]    DbgMCU_APB2_Fz=0x00000000
[14:54:14.974]    DoOptionByteLoading=0x00000000
[14:54:14.974]  </debugvars>
[14:54:14.974]  
[14:54:14.974]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:54:14.974]    <block atomic="false" info="">
[14:54:14.974]      Sequence("CheckID");
[14:54:14.975]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:54:14.975]          <block atomic="false" info="">
[14:54:14.975]            __var pidr1 = 0;
[14:54:14.975]              // -> [pidr1 <= 0x00000000]
[14:54:14.975]            __var pidr2 = 0;
[14:54:14.976]              // -> [pidr2 <= 0x00000000]
[14:54:14.976]            __var jep106id = 0;
[14:54:14.976]              // -> [jep106id <= 0x00000000]
[14:54:14.976]            __var ROMTableBase = 0;
[14:54:14.976]              // -> [ROMTableBase <= 0x00000000]
[14:54:14.976]            __ap = 0;      // AHB-AP
[14:54:14.977]              // -> [__ap <= 0x00000000]
[14:54:14.977]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:54:14.977]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:54:14.978]              // -> [ROMTableBase <= 0xF0000000]
[14:54:14.978]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:54:14.979]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:54:14.979]              // -> [pidr1 <= 0x00000004]
[14:54:14.979]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:54:14.980]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:54:14.980]              // -> [pidr2 <= 0x0000000A]
[14:54:14.980]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:54:14.980]              // -> [jep106id <= 0x00000020]
[14:54:14.981]          </block>
[14:54:14.981]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:54:14.981]            // if-block "jep106id != 0x20"
[14:54:14.981]              // =>  FALSE
[14:54:14.982]            // skip if-block "jep106id != 0x20"
[14:54:14.982]          </control>
[14:54:14.982]        </sequence>
[14:54:14.982]    </block>
[14:54:14.982]  </sequence>
[14:54:14.982]  
[14:54:14.994]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:54:14.994]  
[14:54:14.999]  <debugvars>
[14:54:14.999]    // Pre-defined
[14:54:15.000]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:54:15.000]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:54:15.000]    __dp=0x00000000
[14:54:15.000]    __ap=0x00000000
[14:54:15.000]    __traceout=0x00000000      (Trace Disabled)
[14:54:15.001]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:54:15.001]    __FlashAddr=0x00000000
[14:54:15.001]    __FlashLen=0x00000000
[14:54:15.001]    __FlashArg=0x00000000
[14:54:15.002]    __FlashOp=0x00000000
[14:54:15.002]    __Result=0x00000000
[14:54:15.002]    
[14:54:15.002]    // User-defined
[14:54:15.002]    DbgMCU_CR=0x00000007
[14:54:15.002]    DbgMCU_APB1_Fz=0x00000000
[14:54:15.003]    DbgMCU_APB2_Fz=0x00000000
[14:54:15.003]    DoOptionByteLoading=0x00000000
[14:54:15.003]  </debugvars>
[14:54:15.003]  
[14:54:15.004]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:54:15.004]    <block atomic="false" info="">
[14:54:15.004]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:54:15.005]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:54:15.005]    </block>
[14:54:15.005]    <block atomic="false" info="DbgMCU registers">
[14:54:15.005]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:54:15.006]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[14:54:15.007]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[14:54:15.007]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:54:15.008]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:54:15.008]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:54:15.009]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:54:15.009]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:54:15.010]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:54:15.010]    </block>
[14:54:15.010]  </sequence>
[14:54:15.011]  
[14:54:22.612]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:54:22.612]  
[14:54:22.612]  <debugvars>
[14:54:22.612]    // Pre-defined
[14:54:22.614]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:54:22.614]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:54:22.614]    __dp=0x00000000
[14:54:22.614]    __ap=0x00000000
[14:54:22.614]    __traceout=0x00000000      (Trace Disabled)
[14:54:22.614]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:54:22.615]    __FlashAddr=0x00000000
[14:54:22.615]    __FlashLen=0x00000000
[14:54:22.615]    __FlashArg=0x00000000
[14:54:22.615]    __FlashOp=0x00000000
[14:54:22.615]    __Result=0x00000000
[14:54:22.615]    
[14:54:22.615]    // User-defined
[14:54:22.615]    DbgMCU_CR=0x00000007
[14:54:22.615]    DbgMCU_APB1_Fz=0x00000000
[14:54:22.615]    DbgMCU_APB2_Fz=0x00000000
[14:54:22.615]    DoOptionByteLoading=0x00000000
[14:54:22.615]  </debugvars>
[14:54:22.615]  
[14:54:22.615]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:54:22.615]    <block atomic="false" info="">
[14:54:22.615]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:54:22.615]        // -> [connectionFlash <= 0x00000001]
[14:54:22.615]      __var FLASH_BASE = 0x40022000 ;
[14:54:22.615]        // -> [FLASH_BASE <= 0x40022000]
[14:54:22.615]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:54:22.615]        // -> [FLASH_CR <= 0x40022004]
[14:54:22.615]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:54:22.615]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:54:22.615]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:54:22.615]        // -> [LOCK_BIT <= 0x00000001]
[14:54:22.615]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:54:22.615]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:54:22.615]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:54:22.615]        // -> [FLASH_KEYR <= 0x4002200C]
[14:54:22.615]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:54:22.615]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:54:22.615]      __var FLASH_KEY2 = 0x02030405 ;
[14:54:22.615]        // -> [FLASH_KEY2 <= 0x02030405]
[14:54:22.615]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:54:22.615]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:54:22.615]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:54:22.615]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:54:22.615]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:54:22.615]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:54:22.615]      __var FLASH_CR_Value = 0 ;
[14:54:22.615]        // -> [FLASH_CR_Value <= 0x00000000]
[14:54:22.615]      __var DoDebugPortStop = 1 ;
[14:54:22.615]        // -> [DoDebugPortStop <= 0x00000001]
[14:54:22.615]      __var DP_CTRL_STAT = 0x4 ;
[14:54:22.615]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:54:22.615]      __var DP_SELECT = 0x8 ;
[14:54:22.625]        // -> [DP_SELECT <= 0x00000008]
[14:54:22.625]    </block>
[14:54:22.626]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:54:22.626]      // if-block "connectionFlash && DoOptionByteLoading"
[14:54:22.626]        // =>  FALSE
[14:54:22.626]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:54:22.626]    </control>
[14:54:22.627]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:54:22.627]      // if-block "DoDebugPortStop"
[14:54:22.628]        // =>  TRUE
[14:54:22.628]      <block atomic="false" info="">
[14:54:22.628]        WriteDP(DP_SELECT, 0x00000000);
[14:54:22.628]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:54:22.628]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:54:22.629]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:54:22.629]      </block>
[14:54:22.629]      // end if-block "DoDebugPortStop"
[14:54:22.629]    </control>
[14:54:22.629]  </sequence>
[14:54:22.629]  
[15:14:41.859]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:14:41.859]  
[15:14:41.874]  <debugvars>
[15:14:41.874]    // Pre-defined
[15:14:41.875]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:14:41.875]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:14:41.875]    __dp=0x00000000
[15:14:41.876]    __ap=0x00000000
[15:14:41.876]    __traceout=0x00000000      (Trace Disabled)
[15:14:41.876]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:14:41.876]    __FlashAddr=0x00000000
[15:14:41.877]    __FlashLen=0x00000000
[15:14:41.877]    __FlashArg=0x00000000
[15:14:41.877]    __FlashOp=0x00000000
[15:14:41.877]    __Result=0x00000000
[15:14:41.878]    
[15:14:41.878]    // User-defined
[15:14:41.878]    DbgMCU_CR=0x00000007
[15:14:41.878]    DbgMCU_APB1_Fz=0x00000000
[15:14:41.879]    DbgMCU_APB2_Fz=0x00000000
[15:14:41.879]    DoOptionByteLoading=0x00000000
[15:14:41.879]  </debugvars>
[15:14:41.879]  
[15:14:41.880]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:14:41.880]    <block atomic="false" info="">
[15:14:41.880]      Sequence("CheckID");
[15:14:41.880]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:14:41.880]          <block atomic="false" info="">
[15:14:41.880]            __var pidr1 = 0;
[15:14:41.880]              // -> [pidr1 <= 0x00000000]
[15:14:41.881]            __var pidr2 = 0;
[15:14:41.881]              // -> [pidr2 <= 0x00000000]
[15:14:41.882]            __var jep106id = 0;
[15:14:41.882]              // -> [jep106id <= 0x00000000]
[15:14:41.882]            __var ROMTableBase = 0;
[15:14:41.882]              // -> [ROMTableBase <= 0x00000000]
[15:14:41.883]            __ap = 0;      // AHB-AP
[15:14:41.883]              // -> [__ap <= 0x00000000]
[15:14:41.883]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:14:41.885]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:14:41.885]              // -> [ROMTableBase <= 0xF0000000]
[15:14:41.886]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:14:41.886]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:14:41.887]              // -> [pidr1 <= 0x00000004]
[15:14:41.887]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:14:41.888]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:14:41.888]              // -> [pidr2 <= 0x0000000A]
[15:14:41.888]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:14:41.888]              // -> [jep106id <= 0x00000020]
[15:14:41.889]          </block>
[15:14:41.889]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:14:41.889]            // if-block "jep106id != 0x20"
[15:14:41.889]              // =>  FALSE
[15:14:41.890]            // skip if-block "jep106id != 0x20"
[15:14:41.890]          </control>
[15:14:41.890]        </sequence>
[15:14:41.890]    </block>
[15:14:41.890]  </sequence>
[15:14:41.890]  
[15:14:41.902]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:14:41.902]  
[15:14:41.903]  <debugvars>
[15:14:41.903]    // Pre-defined
[15:14:41.903]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:14:41.903]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:14:41.904]    __dp=0x00000000
[15:14:41.904]    __ap=0x00000000
[15:14:41.904]    __traceout=0x00000000      (Trace Disabled)
[15:14:41.904]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:14:41.904]    __FlashAddr=0x00000000
[15:14:41.905]    __FlashLen=0x00000000
[15:14:41.905]    __FlashArg=0x00000000
[15:14:41.905]    __FlashOp=0x00000000
[15:14:41.905]    __Result=0x00000000
[15:14:41.905]    
[15:14:41.905]    // User-defined
[15:14:41.905]    DbgMCU_CR=0x00000007
[15:14:41.906]    DbgMCU_APB1_Fz=0x00000000
[15:14:41.906]    DbgMCU_APB2_Fz=0x00000000
[15:14:41.906]    DoOptionByteLoading=0x00000000
[15:14:41.907]  </debugvars>
[15:14:41.907]  
[15:14:41.907]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:14:41.907]    <block atomic="false" info="">
[15:14:41.907]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:14:41.908]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:14:41.908]    </block>
[15:14:41.908]    <block atomic="false" info="DbgMCU registers">
[15:14:41.909]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:14:41.910]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[15:14:41.911]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[15:14:41.911]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:14:41.912]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:14:41.912]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:14:41.913]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:14:41.913]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:14:41.914]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:14:41.914]    </block>
[15:14:41.914]  </sequence>
[15:14:41.914]  
[15:14:49.773]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:14:49.773]  
[15:14:49.774]  <debugvars>
[15:14:49.775]    // Pre-defined
[15:14:49.775]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:14:49.775]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:14:49.775]    __dp=0x00000000
[15:14:49.775]    __ap=0x00000000
[15:14:49.776]    __traceout=0x00000000      (Trace Disabled)
[15:14:49.776]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:14:49.776]    __FlashAddr=0x00000000
[15:14:49.776]    __FlashLen=0x00000000
[15:14:49.777]    __FlashArg=0x00000000
[15:14:49.777]    __FlashOp=0x00000000
[15:14:49.778]    __Result=0x00000000
[15:14:49.778]    
[15:14:49.778]    // User-defined
[15:14:49.778]    DbgMCU_CR=0x00000007
[15:14:49.778]    DbgMCU_APB1_Fz=0x00000000
[15:14:49.778]    DbgMCU_APB2_Fz=0x00000000
[15:14:49.779]    DoOptionByteLoading=0x00000000
[15:14:49.779]  </debugvars>
[15:14:49.779]  
[15:14:49.779]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:14:49.780]    <block atomic="false" info="">
[15:14:49.780]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:14:49.780]        // -> [connectionFlash <= 0x00000001]
[15:14:49.781]      __var FLASH_BASE = 0x40022000 ;
[15:14:49.781]        // -> [FLASH_BASE <= 0x40022000]
[15:14:49.781]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:14:49.781]        // -> [FLASH_CR <= 0x40022004]
[15:14:49.781]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:14:49.781]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:14:49.782]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:14:49.782]        // -> [LOCK_BIT <= 0x00000001]
[15:14:49.783]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:14:49.783]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:14:49.783]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:14:49.783]        // -> [FLASH_KEYR <= 0x4002200C]
[15:14:49.783]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:14:49.783]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:14:49.783]      __var FLASH_KEY2 = 0x02030405 ;
[15:14:49.783]        // -> [FLASH_KEY2 <= 0x02030405]
[15:14:49.783]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:14:49.783]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:14:49.783]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:14:49.783]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:14:49.785]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:14:49.785]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:14:49.786]      __var FLASH_CR_Value = 0 ;
[15:14:49.786]        // -> [FLASH_CR_Value <= 0x00000000]
[15:14:49.786]      __var DoDebugPortStop = 1 ;
[15:14:49.786]        // -> [DoDebugPortStop <= 0x00000001]
[15:14:49.786]      __var DP_CTRL_STAT = 0x4 ;
[15:14:49.787]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:14:49.787]      __var DP_SELECT = 0x8 ;
[15:14:49.787]        // -> [DP_SELECT <= 0x00000008]
[15:14:49.787]    </block>
[15:14:49.787]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:14:49.787]      // if-block "connectionFlash && DoOptionByteLoading"
[15:14:49.788]        // =>  FALSE
[15:14:49.788]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:14:49.788]    </control>
[15:14:49.789]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:14:49.789]      // if-block "DoDebugPortStop"
[15:14:49.789]        // =>  TRUE
[15:14:49.789]      <block atomic="false" info="">
[15:14:49.789]        WriteDP(DP_SELECT, 0x00000000);
[15:14:49.790]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:14:49.790]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:14:49.790]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:14:49.791]      </block>
[15:14:49.791]      // end if-block "DoDebugPortStop"
[15:14:49.792]    </control>
[15:14:49.792]  </sequence>
[15:14:49.792]  
[15:24:05.713]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:24:05.713]  
[15:24:05.720]  <debugvars>
[15:24:05.721]    // Pre-defined
[15:24:05.721]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:24:05.721]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:24:05.721]    __dp=0x00000000
[15:24:05.722]    __ap=0x00000000
[15:24:05.722]    __traceout=0x00000000      (Trace Disabled)
[15:24:05.722]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:24:05.723]    __FlashAddr=0x00000000
[15:24:05.723]    __FlashLen=0x00000000
[15:24:05.723]    __FlashArg=0x00000000
[15:24:05.723]    __FlashOp=0x00000000
[15:24:05.723]    __Result=0x00000000
[15:24:05.723]    
[15:24:05.723]    // User-defined
[15:24:05.724]    DbgMCU_CR=0x00000007
[15:24:05.724]    DbgMCU_APB1_Fz=0x00000000
[15:24:05.724]    DbgMCU_APB2_Fz=0x00000000
[15:24:05.724]    DoOptionByteLoading=0x00000000
[15:24:05.724]  </debugvars>
[15:24:05.724]  
[15:24:05.725]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:24:05.725]    <block atomic="false" info="">
[15:24:05.725]      Sequence("CheckID");
[15:24:05.725]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:24:05.725]          <block atomic="false" info="">
[15:24:05.725]            __var pidr1 = 0;
[15:24:05.726]              // -> [pidr1 <= 0x00000000]
[15:24:05.726]            __var pidr2 = 0;
[15:24:05.726]              // -> [pidr2 <= 0x00000000]
[15:24:05.727]            __var jep106id = 0;
[15:24:05.727]              // -> [jep106id <= 0x00000000]
[15:24:05.727]            __var ROMTableBase = 0;
[15:24:05.727]              // -> [ROMTableBase <= 0x00000000]
[15:24:05.727]            __ap = 0;      // AHB-AP
[15:24:05.727]              // -> [__ap <= 0x00000000]
[15:24:05.728]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:24:05.728]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:24:05.728]              // -> [ROMTableBase <= 0xF0000000]
[15:24:05.729]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:24:05.730]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:24:05.730]              // -> [pidr1 <= 0x00000004]
[15:24:05.731]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:24:05.731]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:24:05.732]              // -> [pidr2 <= 0x0000000A]
[15:24:05.732]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:24:05.733]              // -> [jep106id <= 0x00000020]
[15:24:05.733]          </block>
[15:24:05.733]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:24:05.733]            // if-block "jep106id != 0x20"
[15:24:05.733]              // =>  FALSE
[15:24:05.734]            // skip if-block "jep106id != 0x20"
[15:24:05.734]          </control>
[15:24:05.734]        </sequence>
[15:24:05.734]    </block>
[15:24:05.734]  </sequence>
[15:24:05.735]  
[15:24:05.746]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:24:05.746]  
[15:24:05.746]  <debugvars>
[15:24:05.746]    // Pre-defined
[15:24:05.747]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:24:05.747]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:24:05.747]    __dp=0x00000000
[15:24:05.747]    __ap=0x00000000
[15:24:05.748]    __traceout=0x00000000      (Trace Disabled)
[15:24:05.748]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:24:05.748]    __FlashAddr=0x00000000
[15:24:05.748]    __FlashLen=0x00000000
[15:24:05.749]    __FlashArg=0x00000000
[15:24:05.749]    __FlashOp=0x00000000
[15:24:05.749]    __Result=0x00000000
[15:24:05.749]    
[15:24:05.749]    // User-defined
[15:24:05.749]    DbgMCU_CR=0x00000007
[15:24:05.750]    DbgMCU_APB1_Fz=0x00000000
[15:24:05.750]    DbgMCU_APB2_Fz=0x00000000
[15:24:05.750]    DoOptionByteLoading=0x00000000
[15:24:05.750]  </debugvars>
[15:24:05.750]  
[15:24:05.751]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:24:05.751]    <block atomic="false" info="">
[15:24:05.751]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:24:05.752]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:24:05.752]    </block>
[15:24:05.753]    <block atomic="false" info="DbgMCU registers">
[15:24:05.753]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:24:05.754]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[15:24:05.755]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[15:24:05.755]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:24:05.756]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:24:05.757]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:24:05.757]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:24:05.758]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:24:05.759]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:24:05.759]    </block>
[15:24:05.759]  </sequence>
[15:24:05.760]  
[15:24:13.546]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:24:13.546]  
[15:24:13.547]  <debugvars>
[15:24:13.548]    // Pre-defined
[15:24:13.548]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:24:13.548]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:24:13.548]    __dp=0x00000000
[15:24:13.548]    __ap=0x00000000
[15:24:13.549]    __traceout=0x00000000      (Trace Disabled)
[15:24:13.549]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:24:13.549]    __FlashAddr=0x00000000
[15:24:13.549]    __FlashLen=0x00000000
[15:24:13.549]    __FlashArg=0x00000000
[15:24:13.549]    __FlashOp=0x00000000
[15:24:13.549]    __Result=0x00000000
[15:24:13.551]    
[15:24:13.551]    // User-defined
[15:24:13.551]    DbgMCU_CR=0x00000007
[15:24:13.552]    DbgMCU_APB1_Fz=0x00000000
[15:24:13.552]    DbgMCU_APB2_Fz=0x00000000
[15:24:13.552]    DoOptionByteLoading=0x00000000
[15:24:13.552]  </debugvars>
[15:24:13.553]  
[15:24:13.553]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:24:13.554]    <block atomic="false" info="">
[15:24:13.554]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:24:13.554]        // -> [connectionFlash <= 0x00000001]
[15:24:13.554]      __var FLASH_BASE = 0x40022000 ;
[15:24:13.554]        // -> [FLASH_BASE <= 0x40022000]
[15:24:13.554]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:24:13.555]        // -> [FLASH_CR <= 0x40022004]
[15:24:13.555]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:24:13.555]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:24:13.555]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:24:13.556]        // -> [LOCK_BIT <= 0x00000001]
[15:24:13.556]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:24:13.556]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:24:13.556]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:24:13.556]        // -> [FLASH_KEYR <= 0x4002200C]
[15:24:13.556]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:24:13.556]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:24:13.556]      __var FLASH_KEY2 = 0x02030405 ;
[15:24:13.558]        // -> [FLASH_KEY2 <= 0x02030405]
[15:24:13.558]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:24:13.558]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:24:13.558]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:24:13.558]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:24:13.558]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:24:13.559]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:24:13.559]      __var FLASH_CR_Value = 0 ;
[15:24:13.559]        // -> [FLASH_CR_Value <= 0x00000000]
[15:24:13.559]      __var DoDebugPortStop = 1 ;
[15:24:13.559]        // -> [DoDebugPortStop <= 0x00000001]
[15:24:13.559]      __var DP_CTRL_STAT = 0x4 ;
[15:24:13.560]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:24:13.560]      __var DP_SELECT = 0x8 ;
[15:24:13.561]        // -> [DP_SELECT <= 0x00000008]
[15:24:13.561]    </block>
[15:24:13.561]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:24:13.561]      // if-block "connectionFlash && DoOptionByteLoading"
[15:24:13.562]        // =>  FALSE
[15:24:13.562]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:24:13.562]    </control>
[15:24:13.562]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:24:13.562]      // if-block "DoDebugPortStop"
[15:24:13.563]        // =>  TRUE
[15:24:13.563]      <block atomic="false" info="">
[15:24:13.563]        WriteDP(DP_SELECT, 0x00000000);
[15:24:13.563]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:24:13.564]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:24:13.564]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:24:13.564]      </block>
[15:24:13.565]      // end if-block "DoDebugPortStop"
[15:24:13.565]    </control>
[15:24:13.565]  </sequence>
[15:24:13.565]  
[15:36:48.524]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:36:48.524]  
[15:36:48.524]  <debugvars>
[15:36:48.524]    // Pre-defined
[15:36:48.524]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:36:48.524]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:36:48.524]    __dp=0x00000000
[15:36:48.524]    __ap=0x00000000
[15:36:48.524]    __traceout=0x00000000      (Trace Disabled)
[15:36:48.524]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:36:48.524]    __FlashAddr=0x00000000
[15:36:48.524]    __FlashLen=0x00000000
[15:36:48.524]    __FlashArg=0x00000000
[15:36:48.524]    __FlashOp=0x00000000
[15:36:48.528]    __Result=0x00000000
[15:36:48.528]    
[15:36:48.528]    // User-defined
[15:36:48.529]    DbgMCU_CR=0x00000007
[15:36:48.529]    DbgMCU_APB1_Fz=0x00000000
[15:36:48.530]    DbgMCU_APB2_Fz=0x00000000
[15:36:48.530]    DoOptionByteLoading=0x00000000
[15:36:48.530]  </debugvars>
[15:36:48.530]  
[15:36:48.530]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:36:48.531]    <block atomic="false" info="">
[15:36:48.531]      Sequence("CheckID");
[15:36:48.531]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:36:48.531]          <block atomic="false" info="">
[15:36:48.531]            __var pidr1 = 0;
[15:36:48.531]              // -> [pidr1 <= 0x00000000]
[15:36:48.531]            __var pidr2 = 0;
[15:36:48.531]              // -> [pidr2 <= 0x00000000]
[15:36:48.531]            __var jep106id = 0;
[15:36:48.531]              // -> [jep106id <= 0x00000000]
[15:36:48.531]            __var ROMTableBase = 0;
[15:36:48.531]              // -> [ROMTableBase <= 0x00000000]
[15:36:48.531]            __ap = 0;      // AHB-AP
[15:36:48.531]              // -> [__ap <= 0x00000000]
[15:36:48.531]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:36:48.531]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:36:48.531]              // -> [ROMTableBase <= 0xF0000000]
[15:36:48.531]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:36:48.531]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:36:48.531]              // -> [pidr1 <= 0x00000004]
[15:36:48.531]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:36:48.531]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:36:48.531]              // -> [pidr2 <= 0x0000000A]
[15:36:48.531]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:36:48.531]              // -> [jep106id <= 0x00000020]
[15:36:48.531]          </block>
[15:36:48.531]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:36:48.531]            // if-block "jep106id != 0x20"
[15:36:48.531]              // =>  FALSE
[15:36:48.531]            // skip if-block "jep106id != 0x20"
[15:36:48.531]          </control>
[15:36:48.531]        </sequence>
[15:36:48.531]    </block>
[15:36:48.531]  </sequence>
[15:36:48.531]  
[15:36:48.551]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:36:48.551]  
[15:36:48.559]  <debugvars>
[15:36:48.574]    // Pre-defined
[15:36:48.574]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:36:48.574]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:36:48.574]    __dp=0x00000000
[15:36:48.574]    __ap=0x00000000
[15:36:48.574]    __traceout=0x00000000      (Trace Disabled)
[15:36:48.574]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:36:48.574]    __FlashAddr=0x00000000
[15:36:48.574]    __FlashLen=0x00000000
[15:36:48.574]    __FlashArg=0x00000000
[15:36:48.574]    __FlashOp=0x00000000
[15:36:48.574]    __Result=0x00000000
[15:36:48.574]    
[15:36:48.574]    // User-defined
[15:36:48.574]    DbgMCU_CR=0x00000007
[15:36:48.574]    DbgMCU_APB1_Fz=0x00000000
[15:36:48.574]    DbgMCU_APB2_Fz=0x00000000
[15:36:48.574]    DoOptionByteLoading=0x00000000
[15:36:48.574]  </debugvars>
[15:36:48.574]  
[15:36:48.574]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:36:48.574]    <block atomic="false" info="">
[15:36:48.574]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:36:48.574]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:36:48.574]    </block>
[15:36:48.574]    <block atomic="false" info="DbgMCU registers">
[15:36:48.574]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:36:48.574]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[15:36:48.574]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[15:36:48.574]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:36:48.574]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:36:48.574]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:36:48.584]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:36:48.584]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:36:48.584]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:36:48.584]    </block>
[15:36:48.584]  </sequence>
[15:36:48.584]  
[15:36:56.253]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:36:56.253]  
[15:36:56.254]  <debugvars>
[15:36:56.254]    // Pre-defined
[15:36:56.254]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:36:56.254]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:36:56.255]    __dp=0x00000000
[15:36:56.255]    __ap=0x00000000
[15:36:56.256]    __traceout=0x00000000      (Trace Disabled)
[15:36:56.256]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:36:56.256]    __FlashAddr=0x00000000
[15:36:56.256]    __FlashLen=0x00000000
[15:36:56.256]    __FlashArg=0x00000000
[15:36:56.256]    __FlashOp=0x00000000
[15:36:56.257]    __Result=0x00000000
[15:36:56.257]    
[15:36:56.257]    // User-defined
[15:36:56.258]    DbgMCU_CR=0x00000007
[15:36:56.258]    DbgMCU_APB1_Fz=0x00000000
[15:36:56.258]    DbgMCU_APB2_Fz=0x00000000
[15:36:56.258]    DoOptionByteLoading=0x00000000
[15:36:56.258]  </debugvars>
[15:36:56.258]  
[15:36:56.260]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:36:56.260]    <block atomic="false" info="">
[15:36:56.260]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:36:56.260]        // -> [connectionFlash <= 0x00000001]
[15:36:56.261]      __var FLASH_BASE = 0x40022000 ;
[15:36:56.261]        // -> [FLASH_BASE <= 0x40022000]
[15:36:56.261]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:36:56.261]        // -> [FLASH_CR <= 0x40022004]
[15:36:56.261]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:36:56.262]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:36:56.262]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:36:56.262]        // -> [LOCK_BIT <= 0x00000001]
[15:36:56.262]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:36:56.262]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:36:56.263]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:36:56.263]        // -> [FLASH_KEYR <= 0x4002200C]
[15:36:56.263]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:36:56.263]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:36:56.263]      __var FLASH_KEY2 = 0x02030405 ;
[15:36:56.264]        // -> [FLASH_KEY2 <= 0x02030405]
[15:36:56.264]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:36:56.264]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:36:56.264]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:36:56.264]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:36:56.264]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:36:56.265]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:36:56.265]      __var FLASH_CR_Value = 0 ;
[15:36:56.265]        // -> [FLASH_CR_Value <= 0x00000000]
[15:36:56.266]      __var DoDebugPortStop = 1 ;
[15:36:56.266]        // -> [DoDebugPortStop <= 0x00000001]
[15:36:56.266]      __var DP_CTRL_STAT = 0x4 ;
[15:36:56.266]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:36:56.266]      __var DP_SELECT = 0x8 ;
[15:36:56.266]        // -> [DP_SELECT <= 0x00000008]
[15:36:56.267]    </block>
[15:36:56.267]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:36:56.267]      // if-block "connectionFlash && DoOptionByteLoading"
[15:36:56.267]        // =>  FALSE
[15:36:56.268]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:36:56.268]    </control>
[15:36:56.268]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:36:56.268]      // if-block "DoDebugPortStop"
[15:36:56.269]        // =>  TRUE
[15:36:56.269]      <block atomic="false" info="">
[15:36:56.269]        WriteDP(DP_SELECT, 0x00000000);
[15:36:56.269]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:36:56.270]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:36:56.270]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:36:56.271]      </block>
[15:36:56.271]      // end if-block "DoDebugPortStop"
[15:36:56.272]    </control>
[15:36:56.272]  </sequence>
[15:36:56.273]  
[15:47:44.908]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:47:44.908]  
[15:47:44.908]  <debugvars>
[15:47:44.909]    // Pre-defined
[15:47:44.909]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:47:44.910]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:47:44.910]    __dp=0x00000000
[15:47:44.910]    __ap=0x00000000
[15:47:44.910]    __traceout=0x00000000      (Trace Disabled)
[15:47:44.911]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:47:44.911]    __FlashAddr=0x00000000
[15:47:44.911]    __FlashLen=0x00000000
[15:47:44.911]    __FlashArg=0x00000000
[15:47:44.911]    __FlashOp=0x00000000
[15:47:44.911]    __Result=0x00000000
[15:47:44.911]    
[15:47:44.911]    // User-defined
[15:47:44.911]    DbgMCU_CR=0x00000007
[15:47:44.912]    DbgMCU_APB1_Fz=0x00000000
[15:47:44.912]    DbgMCU_APB2_Fz=0x00000000
[15:47:44.913]    DoOptionByteLoading=0x00000000
[15:47:44.913]  </debugvars>
[15:47:44.913]  
[15:47:44.913]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:47:44.913]    <block atomic="false" info="">
[15:47:44.914]      Sequence("CheckID");
[15:47:44.914]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:47:44.914]          <block atomic="false" info="">
[15:47:44.914]            __var pidr1 = 0;
[15:47:44.915]              // -> [pidr1 <= 0x00000000]
[15:47:44.915]            __var pidr2 = 0;
[15:47:44.915]              // -> [pidr2 <= 0x00000000]
[15:47:44.915]            __var jep106id = 0;
[15:47:44.915]              // -> [jep106id <= 0x00000000]
[15:47:44.916]            __var ROMTableBase = 0;
[15:47:44.916]              // -> [ROMTableBase <= 0x00000000]
[15:47:44.916]            __ap = 0;      // AHB-AP
[15:47:44.916]              // -> [__ap <= 0x00000000]
[15:47:44.916]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:47:44.917]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:47:44.917]              // -> [ROMTableBase <= 0xF0000000]
[15:47:44.917]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:47:44.918]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:47:44.918]              // -> [pidr1 <= 0x00000004]
[15:47:44.919]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:47:44.919]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:47:44.919]              // -> [pidr2 <= 0x0000000A]
[15:47:44.919]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:47:44.920]              // -> [jep106id <= 0x00000020]
[15:47:44.920]          </block>
[15:47:44.920]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:47:44.921]            // if-block "jep106id != 0x20"
[15:47:44.921]              // =>  FALSE
[15:47:44.921]            // skip if-block "jep106id != 0x20"
[15:47:44.921]          </control>
[15:47:44.921]        </sequence>
[15:47:44.922]    </block>
[15:47:44.922]  </sequence>
[15:47:44.923]  
[15:47:44.934]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:47:44.934]  
[15:47:44.945]  <debugvars>
[15:47:44.945]    // Pre-defined
[15:47:44.946]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:47:44.946]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:47:44.946]    __dp=0x00000000
[15:47:44.946]    __ap=0x00000000
[15:47:44.946]    __traceout=0x00000000      (Trace Disabled)
[15:47:44.946]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:47:44.946]    __FlashAddr=0x00000000
[15:47:44.946]    __FlashLen=0x00000000
[15:47:44.948]    __FlashArg=0x00000000
[15:47:44.948]    __FlashOp=0x00000000
[15:47:44.948]    __Result=0x00000000
[15:47:44.948]    
[15:47:44.948]    // User-defined
[15:47:44.948]    DbgMCU_CR=0x00000007
[15:47:44.949]    DbgMCU_APB1_Fz=0x00000000
[15:47:44.949]    DbgMCU_APB2_Fz=0x00000000
[15:47:44.949]    DoOptionByteLoading=0x00000000
[15:47:44.949]  </debugvars>
[15:47:44.949]  
[15:47:44.949]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:47:44.950]    <block atomic="false" info="">
[15:47:44.950]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:47:44.951]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:47:44.951]    </block>
[15:47:44.952]    <block atomic="false" info="DbgMCU registers">
[15:47:44.952]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:47:44.952]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[15:47:44.953]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[15:47:44.953]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:47:44.954]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:47:44.954]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:47:44.955]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:47:44.955]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:47:44.956]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:47:44.957]    </block>
[15:47:44.957]  </sequence>
[15:47:44.957]  
[15:47:52.807]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:47:52.807]  
[15:47:52.807]  <debugvars>
[15:47:52.808]    // Pre-defined
[15:47:52.809]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:47:52.809]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:47:52.809]    __dp=0x00000000
[15:47:52.809]    __ap=0x00000000
[15:47:52.810]    __traceout=0x00000000      (Trace Disabled)
[15:47:52.810]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:47:52.810]    __FlashAddr=0x00000000
[15:47:52.811]    __FlashLen=0x00000000
[15:47:52.811]    __FlashArg=0x00000000
[15:47:52.811]    __FlashOp=0x00000000
[15:47:52.812]    __Result=0x00000000
[15:47:52.812]    
[15:47:52.812]    // User-defined
[15:47:52.812]    DbgMCU_CR=0x00000007
[15:47:52.813]    DbgMCU_APB1_Fz=0x00000000
[15:47:52.813]    DbgMCU_APB2_Fz=0x00000000
[15:47:52.813]    DoOptionByteLoading=0x00000000
[15:47:52.813]  </debugvars>
[15:47:52.813]  
[15:47:52.813]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:47:52.814]    <block atomic="false" info="">
[15:47:52.814]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:47:52.814]        // -> [connectionFlash <= 0x00000001]
[15:47:52.814]      __var FLASH_BASE = 0x40022000 ;
[15:47:52.814]        // -> [FLASH_BASE <= 0x40022000]
[15:47:52.815]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:47:52.815]        // -> [FLASH_CR <= 0x40022004]
[15:47:52.815]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:47:52.815]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:47:52.815]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:47:52.815]        // -> [LOCK_BIT <= 0x00000001]
[15:47:52.816]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:47:52.816]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:47:52.816]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:47:52.817]        // -> [FLASH_KEYR <= 0x4002200C]
[15:47:52.817]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:47:52.817]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:47:52.817]      __var FLASH_KEY2 = 0x02030405 ;
[15:47:52.817]        // -> [FLASH_KEY2 <= 0x02030405]
[15:47:52.818]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:47:52.818]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:47:52.818]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:47:52.818]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:47:52.818]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:47:52.818]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:47:52.819]      __var FLASH_CR_Value = 0 ;
[15:47:52.819]        // -> [FLASH_CR_Value <= 0x00000000]
[15:47:52.819]      __var DoDebugPortStop = 1 ;
[15:47:52.819]        // -> [DoDebugPortStop <= 0x00000001]
[15:47:52.819]      __var DP_CTRL_STAT = 0x4 ;
[15:47:52.820]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:47:52.820]      __var DP_SELECT = 0x8 ;
[15:47:52.820]        // -> [DP_SELECT <= 0x00000008]
[15:47:52.820]    </block>
[15:47:52.820]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:47:52.821]      // if-block "connectionFlash && DoOptionByteLoading"
[15:47:52.821]        // =>  FALSE
[15:47:52.821]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:47:52.821]    </control>
[15:47:52.821]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:47:52.821]      // if-block "DoDebugPortStop"
[15:47:52.822]        // =>  TRUE
[15:47:52.822]      <block atomic="false" info="">
[15:47:52.822]        WriteDP(DP_SELECT, 0x00000000);
[15:47:52.822]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:47:52.823]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:47:52.823]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:47:52.824]      </block>
[15:47:52.824]      // end if-block "DoDebugPortStop"
[15:47:52.824]    </control>
[15:47:52.824]  </sequence>
[15:47:52.825]  
[16:12:49.382]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:12:49.382]  
[16:12:49.403]  <debugvars>
[16:12:49.403]    // Pre-defined
[16:12:49.403]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:12:49.403]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:12:49.403]    __dp=0x00000000
[16:12:49.405]    __ap=0x00000000
[16:12:49.405]    __traceout=0x00000000      (Trace Disabled)
[16:12:49.406]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:12:49.406]    __FlashAddr=0x00000000
[16:12:49.406]    __FlashLen=0x00000000
[16:12:49.406]    __FlashArg=0x00000000
[16:12:49.407]    __FlashOp=0x00000000
[16:12:49.407]    __Result=0x00000000
[16:12:49.407]    
[16:12:49.407]    // User-defined
[16:12:49.407]    DbgMCU_CR=0x00000007
[16:12:49.407]    DbgMCU_APB1_Fz=0x00000000
[16:12:49.408]    DbgMCU_APB2_Fz=0x00000000
[16:12:49.408]    DoOptionByteLoading=0x00000000
[16:12:49.408]  </debugvars>
[16:12:49.408]  
[16:12:49.409]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:12:49.409]    <block atomic="false" info="">
[16:12:49.409]      Sequence("CheckID");
[16:12:49.409]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:12:49.410]          <block atomic="false" info="">
[16:12:49.410]            __var pidr1 = 0;
[16:12:49.410]              // -> [pidr1 <= 0x00000000]
[16:12:49.411]            __var pidr2 = 0;
[16:12:49.411]              // -> [pidr2 <= 0x00000000]
[16:12:49.411]            __var jep106id = 0;
[16:12:49.412]              // -> [jep106id <= 0x00000000]
[16:12:49.412]            __var ROMTableBase = 0;
[16:12:49.412]              // -> [ROMTableBase <= 0x00000000]
[16:12:49.412]            __ap = 0;      // AHB-AP
[16:12:49.412]              // -> [__ap <= 0x00000000]
[16:12:49.412]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:12:49.413]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:12:49.414]              // -> [ROMTableBase <= 0xF0000000]
[16:12:49.414]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:12:49.414]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:12:49.414]              // -> [pidr1 <= 0x00000004]
[16:12:49.414]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:12:49.417]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:12:49.417]              // -> [pidr2 <= 0x0000000A]
[16:12:49.417]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:12:49.418]              // -> [jep106id <= 0x00000020]
[16:12:49.419]          </block>
[16:12:49.419]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:12:49.419]            // if-block "jep106id != 0x20"
[16:12:49.419]              // =>  FALSE
[16:12:49.419]            // skip if-block "jep106id != 0x20"
[16:12:49.420]          </control>
[16:12:49.420]        </sequence>
[16:12:49.420]    </block>
[16:12:49.420]  </sequence>
[16:12:49.420]  
[16:12:49.432]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:12:49.432]  
[16:12:49.451]  <debugvars>
[16:12:49.451]    // Pre-defined
[16:12:49.451]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:12:49.451]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:12:49.451]    __dp=0x00000000
[16:12:49.451]    __ap=0x00000000
[16:12:49.451]    __traceout=0x00000000      (Trace Disabled)
[16:12:49.451]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:12:49.451]    __FlashAddr=0x00000000
[16:12:49.451]    __FlashLen=0x00000000
[16:12:49.451]    __FlashArg=0x00000000
[16:12:49.451]    __FlashOp=0x00000000
[16:12:49.451]    __Result=0x00000000
[16:12:49.456]    
[16:12:49.456]    // User-defined
[16:12:49.456]    DbgMCU_CR=0x00000007
[16:12:49.456]    DbgMCU_APB1_Fz=0x00000000
[16:12:49.456]    DbgMCU_APB2_Fz=0x00000000
[16:12:49.456]    DoOptionByteLoading=0x00000000
[16:12:49.456]  </debugvars>
[16:12:49.456]  
[16:12:49.456]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:12:49.456]    <block atomic="false" info="">
[16:12:49.456]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:12:49.456]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:12:49.456]    </block>
[16:12:49.456]    <block atomic="false" info="DbgMCU registers">
[16:12:49.456]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:12:49.461]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:12:49.461]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:12:49.461]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:12:49.461]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:12:49.461]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:12:49.461]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:12:49.461]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:12:49.461]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:12:49.466]    </block>
[16:12:49.466]  </sequence>
[16:12:49.466]  
[16:12:57.788]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:12:57.788]  
[16:12:57.789]  <debugvars>
[16:12:57.789]    // Pre-defined
[16:12:57.789]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:12:57.790]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:12:57.790]    __dp=0x00000000
[16:12:57.790]    __ap=0x00000000
[16:12:57.791]    __traceout=0x00000000      (Trace Disabled)
[16:12:57.791]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:12:57.791]    __FlashAddr=0x00000000
[16:12:57.791]    __FlashLen=0x00000000
[16:12:57.792]    __FlashArg=0x00000000
[16:12:57.792]    __FlashOp=0x00000000
[16:12:57.792]    __Result=0x00000000
[16:12:57.792]    
[16:12:57.792]    // User-defined
[16:12:57.793]    DbgMCU_CR=0x00000007
[16:12:57.793]    DbgMCU_APB1_Fz=0x00000000
[16:12:57.793]    DbgMCU_APB2_Fz=0x00000000
[16:12:57.794]    DoOptionByteLoading=0x00000000
[16:12:57.794]  </debugvars>
[16:12:57.795]  
[16:12:57.795]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:12:57.795]    <block atomic="false" info="">
[16:12:57.796]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:12:57.796]        // -> [connectionFlash <= 0x00000001]
[16:12:57.796]      __var FLASH_BASE = 0x40022000 ;
[16:12:57.796]        // -> [FLASH_BASE <= 0x40022000]
[16:12:57.797]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:12:57.797]        // -> [FLASH_CR <= 0x40022004]
[16:12:57.797]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:12:57.798]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:12:57.798]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:12:57.798]        // -> [LOCK_BIT <= 0x00000001]
[16:12:57.798]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:12:57.798]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:12:57.798]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:12:57.799]        // -> [FLASH_KEYR <= 0x4002200C]
[16:12:57.799]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:12:57.799]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:12:57.799]      __var FLASH_KEY2 = 0x02030405 ;
[16:12:57.799]        // -> [FLASH_KEY2 <= 0x02030405]
[16:12:57.799]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:12:57.800]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:12:57.800]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:12:57.800]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:12:57.801]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:12:57.801]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:12:57.801]      __var FLASH_CR_Value = 0 ;
[16:12:57.801]        // -> [FLASH_CR_Value <= 0x00000000]
[16:12:57.801]      __var DoDebugPortStop = 1 ;
[16:12:57.802]        // -> [DoDebugPortStop <= 0x00000001]
[16:12:57.802]      __var DP_CTRL_STAT = 0x4 ;
[16:12:57.802]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:12:57.802]      __var DP_SELECT = 0x8 ;
[16:12:57.802]        // -> [DP_SELECT <= 0x00000008]
[16:12:57.803]    </block>
[16:12:57.803]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:12:57.803]      // if-block "connectionFlash && DoOptionByteLoading"
[16:12:57.803]        // =>  FALSE
[16:12:57.803]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:12:57.804]    </control>
[16:12:57.804]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:12:57.804]      // if-block "DoDebugPortStop"
[16:12:57.804]        // =>  TRUE
[16:12:57.805]      <block atomic="false" info="">
[16:12:57.805]        WriteDP(DP_SELECT, 0x00000000);
[16:12:57.805]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:12:57.806]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:12:57.806]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:12:57.806]      </block>
[16:12:57.807]      // end if-block "DoDebugPortStop"
[16:12:57.807]    </control>
[16:12:57.807]  </sequence>
[16:12:57.807]  
[16:18:30.890]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:18:30.890]  
[16:18:30.908]  <debugvars>
[16:18:30.908]    // Pre-defined
[16:18:30.908]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:18:30.908]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:18:30.909]    __dp=0x00000000
[16:18:30.909]    __ap=0x00000000
[16:18:30.909]    __traceout=0x00000000      (Trace Disabled)
[16:18:30.909]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:18:30.910]    __FlashAddr=0x00000000
[16:18:30.910]    __FlashLen=0x00000000
[16:18:30.910]    __FlashArg=0x00000000
[16:18:30.910]    __FlashOp=0x00000000
[16:18:30.910]    __Result=0x00000000
[16:18:30.911]    
[16:18:30.911]    // User-defined
[16:18:30.911]    DbgMCU_CR=0x00000007
[16:18:30.911]    DbgMCU_APB1_Fz=0x00000000
[16:18:30.911]    DbgMCU_APB2_Fz=0x00000000
[16:18:30.912]    DoOptionByteLoading=0x00000000
[16:18:30.912]  </debugvars>
[16:18:30.912]  
[16:18:30.912]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:18:30.913]    <block atomic="false" info="">
[16:18:30.913]      Sequence("CheckID");
[16:18:30.913]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:18:30.914]          <block atomic="false" info="">
[16:18:30.914]            __var pidr1 = 0;
[16:18:30.914]              // -> [pidr1 <= 0x00000000]
[16:18:30.914]            __var pidr2 = 0;
[16:18:30.914]              // -> [pidr2 <= 0x00000000]
[16:18:30.914]            __var jep106id = 0;
[16:18:30.914]              // -> [jep106id <= 0x00000000]
[16:18:30.914]            __var ROMTableBase = 0;
[16:18:30.914]              // -> [ROMTableBase <= 0x00000000]
[16:18:30.916]            __ap = 0;      // AHB-AP
[16:18:30.916]              // -> [__ap <= 0x00000000]
[16:18:30.916]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:18:30.917]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:18:30.917]              // -> [ROMTableBase <= 0xF0000000]
[16:18:30.917]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:18:30.918]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:18:30.919]              // -> [pidr1 <= 0x00000004]
[16:18:30.919]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:18:30.920]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:18:30.920]              // -> [pidr2 <= 0x0000000A]
[16:18:30.920]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:18:30.920]              // -> [jep106id <= 0x00000020]
[16:18:30.920]          </block>
[16:18:30.920]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:18:30.920]            // if-block "jep106id != 0x20"
[16:18:30.921]              // =>  FALSE
[16:18:30.921]            // skip if-block "jep106id != 0x20"
[16:18:30.921]          </control>
[16:18:30.922]        </sequence>
[16:18:30.922]    </block>
[16:18:30.933]  </sequence>
[16:18:30.933]  
[16:18:30.945]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:18:30.945]  
[16:18:30.945]  <debugvars>
[16:18:30.946]    // Pre-defined
[16:18:30.947]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:18:30.947]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:18:30.947]    __dp=0x00000000
[16:18:30.947]    __ap=0x00000000
[16:18:30.947]    __traceout=0x00000000      (Trace Disabled)
[16:18:30.947]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:18:30.947]    __FlashAddr=0x00000000
[16:18:30.947]    __FlashLen=0x00000000
[16:18:30.947]    __FlashArg=0x00000000
[16:18:30.947]    __FlashOp=0x00000000
[16:18:30.947]    __Result=0x00000000
[16:18:30.947]    
[16:18:30.947]    // User-defined
[16:18:30.947]    DbgMCU_CR=0x00000007
[16:18:30.947]    DbgMCU_APB1_Fz=0x00000000
[16:18:30.947]    DbgMCU_APB2_Fz=0x00000000
[16:18:30.947]    DoOptionByteLoading=0x00000000
[16:18:30.947]  </debugvars>
[16:18:30.947]  
[16:18:30.951]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:18:30.951]    <block atomic="false" info="">
[16:18:30.951]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:18:30.952]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:18:30.952]    </block>
[16:18:30.952]    <block atomic="false" info="DbgMCU registers">
[16:18:30.952]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:18:30.954]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:18:30.954]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:18:30.954]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:18:30.954]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:18:30.954]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:18:30.956]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:18:30.957]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:18:30.958]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:18:30.958]    </block>
[16:18:30.958]  </sequence>
[16:18:30.959]  
[16:18:39.257]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:18:39.257]  
[16:18:39.257]  <debugvars>
[16:18:39.257]    // Pre-defined
[16:18:39.257]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:18:39.257]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:18:39.257]    __dp=0x00000000
[16:18:39.257]    __ap=0x00000000
[16:18:39.259]    __traceout=0x00000000      (Trace Disabled)
[16:18:39.259]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:18:39.259]    __FlashAddr=0x00000000
[16:18:39.259]    __FlashLen=0x00000000
[16:18:39.259]    __FlashArg=0x00000000
[16:18:39.259]    __FlashOp=0x00000000
[16:18:39.259]    __Result=0x00000000
[16:18:39.259]    
[16:18:39.259]    // User-defined
[16:18:39.259]    DbgMCU_CR=0x00000007
[16:18:39.261]    DbgMCU_APB1_Fz=0x00000000
[16:18:39.261]    DbgMCU_APB2_Fz=0x00000000
[16:18:39.261]    DoOptionByteLoading=0x00000000
[16:18:39.261]  </debugvars>
[16:18:39.261]  
[16:18:39.261]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:18:39.261]    <block atomic="false" info="">
[16:18:39.261]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:18:39.261]        // -> [connectionFlash <= 0x00000001]
[16:18:39.261]      __var FLASH_BASE = 0x40022000 ;
[16:18:39.263]        // -> [FLASH_BASE <= 0x40022000]
[16:18:39.263]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:18:39.263]        // -> [FLASH_CR <= 0x40022004]
[16:18:39.263]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:18:39.263]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:18:39.263]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:18:39.263]        // -> [LOCK_BIT <= 0x00000001]
[16:18:39.263]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:18:39.265]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:18:39.265]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:18:39.265]        // -> [FLASH_KEYR <= 0x4002200C]
[16:18:39.265]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:18:39.265]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:18:39.265]      __var FLASH_KEY2 = 0x02030405 ;
[16:18:39.265]        // -> [FLASH_KEY2 <= 0x02030405]
[16:18:39.267]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:18:39.267]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:18:39.267]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:18:39.267]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:18:39.267]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:18:39.267]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:18:39.267]      __var FLASH_CR_Value = 0 ;
[16:18:39.267]        // -> [FLASH_CR_Value <= 0x00000000]
[16:18:39.267]      __var DoDebugPortStop = 1 ;
[16:18:39.267]        // -> [DoDebugPortStop <= 0x00000001]
[16:18:39.270]      __var DP_CTRL_STAT = 0x4 ;
[16:18:39.270]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:18:39.270]      __var DP_SELECT = 0x8 ;
[16:18:39.270]        // -> [DP_SELECT <= 0x00000008]
[16:18:39.270]    </block>
[16:18:39.270]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:18:39.270]      // if-block "connectionFlash && DoOptionByteLoading"
[16:18:39.270]        // =>  FALSE
[16:18:39.270]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:18:39.270]    </control>
[16:18:39.270]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:18:39.272]      // if-block "DoDebugPortStop"
[16:18:39.272]        // =>  TRUE
[16:18:39.272]      <block atomic="false" info="">
[16:18:39.272]        WriteDP(DP_SELECT, 0x00000000);
[16:18:39.272]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:18:39.272]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:18:39.272]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:18:39.272]      </block>
[16:18:39.272]      // end if-block "DoDebugPortStop"
[16:18:39.272]    </control>
[16:18:39.272]  </sequence>
[16:18:39.275]  
[16:21:12.188]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:21:12.188]  
[16:21:12.189]  <debugvars>
[16:21:12.189]    // Pre-defined
[16:21:12.189]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:21:12.190]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:21:12.190]    __dp=0x00000000
[16:21:12.190]    __ap=0x00000000
[16:21:12.191]    __traceout=0x00000000      (Trace Disabled)
[16:21:12.191]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:21:12.191]    __FlashAddr=0x00000000
[16:21:12.192]    __FlashLen=0x00000000
[16:21:12.192]    __FlashArg=0x00000000
[16:21:12.192]    __FlashOp=0x00000000
[16:21:12.192]    __Result=0x00000000
[16:21:12.193]    
[16:21:12.193]    // User-defined
[16:21:12.193]    DbgMCU_CR=0x00000007
[16:21:12.193]    DbgMCU_APB1_Fz=0x00000000
[16:21:12.194]    DbgMCU_APB2_Fz=0x00000000
[16:21:12.194]    DoOptionByteLoading=0x00000000
[16:21:12.194]  </debugvars>
[16:21:12.194]  
[16:21:12.194]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:21:12.195]    <block atomic="false" info="">
[16:21:12.195]      Sequence("CheckID");
[16:21:12.195]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:21:12.195]          <block atomic="false" info="">
[16:21:12.195]            __var pidr1 = 0;
[16:21:12.195]              // -> [pidr1 <= 0x00000000]
[16:21:12.196]            __var pidr2 = 0;
[16:21:12.196]              // -> [pidr2 <= 0x00000000]
[16:21:12.196]            __var jep106id = 0;
[16:21:12.196]              // -> [jep106id <= 0x00000000]
[16:21:12.196]            __var ROMTableBase = 0;
[16:21:12.197]              // -> [ROMTableBase <= 0x00000000]
[16:21:12.197]            __ap = 0;      // AHB-AP
[16:21:12.197]              // -> [__ap <= 0x00000000]
[16:21:12.197]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:21:12.198]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:21:12.199]              // -> [ROMTableBase <= 0xF0000000]
[16:21:12.199]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:21:12.200]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:21:12.200]              // -> [pidr1 <= 0x00000004]
[16:21:12.200]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:21:12.200]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:21:12.200]              // -> [pidr2 <= 0x0000000A]
[16:21:12.200]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:21:12.200]              // -> [jep106id <= 0x00000020]
[16:21:12.200]          </block>
[16:21:12.200]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:21:12.200]            // if-block "jep106id != 0x20"
[16:21:12.200]              // =>  FALSE
[16:21:12.200]            // skip if-block "jep106id != 0x20"
[16:21:12.200]          </control>
[16:21:12.200]        </sequence>
[16:21:12.200]    </block>
[16:21:12.200]  </sequence>
[16:21:12.200]  
[16:21:12.216]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:21:12.216]  
[16:21:12.235]  <debugvars>
[16:21:12.235]    // Pre-defined
[16:21:12.235]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:21:12.235]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:21:12.235]    __dp=0x00000000
[16:21:12.235]    __ap=0x00000000
[16:21:12.235]    __traceout=0x00000000      (Trace Disabled)
[16:21:12.235]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:21:12.235]    __FlashAddr=0x00000000
[16:21:12.235]    __FlashLen=0x00000000
[16:21:12.244]    __FlashArg=0x00000000
[16:21:12.244]    __FlashOp=0x00000000
[16:21:12.244]    __Result=0x00000000
[16:21:12.244]    
[16:21:12.244]    // User-defined
[16:21:12.244]    DbgMCU_CR=0x00000007
[16:21:12.244]    DbgMCU_APB1_Fz=0x00000000
[16:21:12.244]    DbgMCU_APB2_Fz=0x00000000
[16:21:12.246]    DoOptionByteLoading=0x00000000
[16:21:12.246]  </debugvars>
[16:21:12.246]  
[16:21:12.246]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:21:12.246]    <block atomic="false" info="">
[16:21:12.246]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:21:12.248]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:21:12.248]    </block>
[16:21:12.248]    <block atomic="false" info="DbgMCU registers">
[16:21:12.248]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:21:12.249]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:21:12.249]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:21:12.249]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:21:12.249]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:21:12.249]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:21:12.249]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:21:12.249]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:21:12.249]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:21:12.249]    </block>
[16:21:12.249]  </sequence>
[16:21:12.249]  
[16:21:20.156]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:21:20.156]  
[16:21:20.156]  <debugvars>
[16:21:20.156]    // Pre-defined
[16:21:20.156]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:21:20.156]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:21:20.156]    __dp=0x00000000
[16:21:20.156]    __ap=0x00000000
[16:21:20.156]    __traceout=0x00000000      (Trace Disabled)
[16:21:20.156]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:21:20.156]    __FlashAddr=0x00000000
[16:21:20.156]    __FlashLen=0x00000000
[16:21:20.156]    __FlashArg=0x00000000
[16:21:20.156]    __FlashOp=0x00000000
[16:21:20.156]    __Result=0x00000000
[16:21:20.156]    
[16:21:20.156]    // User-defined
[16:21:20.156]    DbgMCU_CR=0x00000007
[16:21:20.156]    DbgMCU_APB1_Fz=0x00000000
[16:21:20.156]    DbgMCU_APB2_Fz=0x00000000
[16:21:20.156]    DoOptionByteLoading=0x00000000
[16:21:20.156]  </debugvars>
[16:21:20.156]  
[16:21:20.156]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:21:20.156]    <block atomic="false" info="">
[16:21:20.156]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:21:20.156]        // -> [connectionFlash <= 0x00000001]
[16:21:20.156]      __var FLASH_BASE = 0x40022000 ;
[16:21:20.156]        // -> [FLASH_BASE <= 0x40022000]
[16:21:20.156]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:21:20.156]        // -> [FLASH_CR <= 0x40022004]
[16:21:20.156]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:21:20.156]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:21:20.156]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:21:20.156]        // -> [LOCK_BIT <= 0x00000001]
[16:21:20.165]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:21:20.165]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:21:20.165]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:21:20.165]        // -> [FLASH_KEYR <= 0x4002200C]
[16:21:20.165]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:21:20.165]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:21:20.165]      __var FLASH_KEY2 = 0x02030405 ;
[16:21:20.165]        // -> [FLASH_KEY2 <= 0x02030405]
[16:21:20.165]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:21:20.165]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:21:20.165]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:21:20.165]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:21:20.165]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:21:20.165]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:21:20.165]      __var FLASH_CR_Value = 0 ;
[16:21:20.165]        // -> [FLASH_CR_Value <= 0x00000000]
[16:21:20.165]      __var DoDebugPortStop = 1 ;
[16:21:20.165]        // -> [DoDebugPortStop <= 0x00000001]
[16:21:20.165]      __var DP_CTRL_STAT = 0x4 ;
[16:21:20.165]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:21:20.165]      __var DP_SELECT = 0x8 ;
[16:21:20.165]        // -> [DP_SELECT <= 0x00000008]
[16:21:20.165]    </block>
[16:21:20.165]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:21:20.165]      // if-block "connectionFlash && DoOptionByteLoading"
[16:21:20.165]        // =>  FALSE
[16:21:20.165]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:21:20.165]    </control>
[16:21:20.165]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:21:20.165]      // if-block "DoDebugPortStop"
[16:21:20.165]        // =>  TRUE
[16:21:20.165]      <block atomic="false" info="">
[16:21:20.165]        WriteDP(DP_SELECT, 0x00000000);
[16:21:20.165]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:21:20.171]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:21:20.171]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:21:20.171]      </block>
[16:21:20.171]      // end if-block "DoDebugPortStop"
[16:21:20.171]    </control>
[16:21:20.171]  </sequence>
[16:21:20.171]  
[16:23:46.184]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:23:46.184]  
[16:23:46.185]  <debugvars>
[16:23:46.185]    // Pre-defined
[16:23:46.185]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:23:46.185]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:23:46.186]    __dp=0x00000000
[16:23:46.187]    __ap=0x00000000
[16:23:46.187]    __traceout=0x00000000      (Trace Disabled)
[16:23:46.187]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:23:46.188]    __FlashAddr=0x00000000
[16:23:46.188]    __FlashLen=0x00000000
[16:23:46.188]    __FlashArg=0x00000000
[16:23:46.189]    __FlashOp=0x00000000
[16:23:46.189]    __Result=0x00000000
[16:23:46.189]    
[16:23:46.189]    // User-defined
[16:23:46.190]    DbgMCU_CR=0x00000007
[16:23:46.190]    DbgMCU_APB1_Fz=0x00000000
[16:23:46.190]    DbgMCU_APB2_Fz=0x00000000
[16:23:46.191]    DoOptionByteLoading=0x00000000
[16:23:46.191]  </debugvars>
[16:23:46.191]  
[16:23:46.191]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:23:46.192]    <block atomic="false" info="">
[16:23:46.192]      Sequence("CheckID");
[16:23:46.192]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:23:46.192]          <block atomic="false" info="">
[16:23:46.192]            __var pidr1 = 0;
[16:23:46.192]              // -> [pidr1 <= 0x00000000]
[16:23:46.193]            __var pidr2 = 0;
[16:23:46.193]              // -> [pidr2 <= 0x00000000]
[16:23:46.193]            __var jep106id = 0;
[16:23:46.194]              // -> [jep106id <= 0x00000000]
[16:23:46.194]            __var ROMTableBase = 0;
[16:23:46.194]              // -> [ROMTableBase <= 0x00000000]
[16:23:46.194]            __ap = 0;      // AHB-AP
[16:23:46.195]              // -> [__ap <= 0x00000000]
[16:23:46.195]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:23:46.196]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:23:46.196]              // -> [ROMTableBase <= 0xF0000000]
[16:23:46.196]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:23:46.197]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:23:46.198]              // -> [pidr1 <= 0x00000004]
[16:23:46.198]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:23:46.198]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:23:46.198]              // -> [pidr2 <= 0x0000000A]
[16:23:46.198]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:23:46.198]              // -> [jep106id <= 0x00000020]
[16:23:46.198]          </block>
[16:23:46.200]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:23:46.200]            // if-block "jep106id != 0x20"
[16:23:46.200]              // =>  FALSE
[16:23:46.200]            // skip if-block "jep106id != 0x20"
[16:23:46.200]          </control>
[16:23:46.200]        </sequence>
[16:23:46.200]    </block>
[16:23:46.200]  </sequence>
[16:23:46.200]  
[16:23:46.213]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:23:46.213]  
[16:23:46.239]  <debugvars>
[16:23:46.239]    // Pre-defined
[16:23:46.239]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:23:46.239]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:23:46.239]    __dp=0x00000000
[16:23:46.239]    __ap=0x00000000
[16:23:46.239]    __traceout=0x00000000      (Trace Disabled)
[16:23:46.239]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:23:46.239]    __FlashAddr=0x00000000
[16:23:46.239]    __FlashLen=0x00000000
[16:23:46.239]    __FlashArg=0x00000000
[16:23:46.247]    __FlashOp=0x00000000
[16:23:46.247]    __Result=0x00000000
[16:23:46.247]    
[16:23:46.247]    // User-defined
[16:23:46.249]    DbgMCU_CR=0x00000007
[16:23:46.249]    DbgMCU_APB1_Fz=0x00000000
[16:23:46.249]    DbgMCU_APB2_Fz=0x00000000
[16:23:46.251]    DoOptionByteLoading=0x00000000
[16:23:46.251]  </debugvars>
[16:23:46.251]  
[16:23:46.251]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:23:46.251]    <block atomic="false" info="">
[16:23:46.253]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:23:46.255]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:23:46.255]    </block>
[16:23:46.256]    <block atomic="false" info="DbgMCU registers">
[16:23:46.256]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:23:46.256]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:23:46.256]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:23:46.256]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:23:46.256]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:23:46.256]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:23:46.256]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:23:46.256]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:23:46.256]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:23:46.256]    </block>
[16:23:46.256]  </sequence>
[16:23:46.256]  
[16:23:54.042]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:23:54.042]  
[16:23:54.042]  <debugvars>
[16:23:54.042]    // Pre-defined
[16:23:54.043]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:23:54.044]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:23:54.044]    __dp=0x00000000
[16:23:54.045]    __ap=0x00000000
[16:23:54.045]    __traceout=0x00000000      (Trace Disabled)
[16:23:54.045]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:23:54.046]    __FlashAddr=0x00000000
[16:23:54.046]    __FlashLen=0x00000000
[16:23:54.046]    __FlashArg=0x00000000
[16:23:54.046]    __FlashOp=0x00000000
[16:23:54.047]    __Result=0x00000000
[16:23:54.047]    
[16:23:54.047]    // User-defined
[16:23:54.047]    DbgMCU_CR=0x00000007
[16:23:54.048]    DbgMCU_APB1_Fz=0x00000000
[16:23:54.048]    DbgMCU_APB2_Fz=0x00000000
[16:23:54.048]    DoOptionByteLoading=0x00000000
[16:23:54.048]  </debugvars>
[16:23:54.049]  
[16:23:54.049]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:23:54.049]    <block atomic="false" info="">
[16:23:54.049]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:23:54.049]        // -> [connectionFlash <= 0x00000001]
[16:23:54.050]      __var FLASH_BASE = 0x40022000 ;
[16:23:54.050]        // -> [FLASH_BASE <= 0x40022000]
[16:23:54.050]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:23:54.050]        // -> [FLASH_CR <= 0x40022004]
[16:23:54.050]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:23:54.051]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:23:54.051]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:23:54.051]        // -> [LOCK_BIT <= 0x00000001]
[16:23:54.051]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:23:54.051]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:23:54.052]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:23:54.052]        // -> [FLASH_KEYR <= 0x4002200C]
[16:23:54.052]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:23:54.052]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:23:54.052]      __var FLASH_KEY2 = 0x02030405 ;
[16:23:54.052]        // -> [FLASH_KEY2 <= 0x02030405]
[16:23:54.053]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:23:54.053]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:23:54.053]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:23:54.053]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:23:54.053]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:23:54.054]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:23:54.054]      __var FLASH_CR_Value = 0 ;
[16:23:54.054]        // -> [FLASH_CR_Value <= 0x00000000]
[16:23:54.054]      __var DoDebugPortStop = 1 ;
[16:23:54.054]        // -> [DoDebugPortStop <= 0x00000001]
[16:23:54.055]      __var DP_CTRL_STAT = 0x4 ;
[16:23:54.055]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:23:54.055]      __var DP_SELECT = 0x8 ;
[16:23:54.055]        // -> [DP_SELECT <= 0x00000008]
[16:23:54.055]    </block>
[16:23:54.056]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:23:54.056]      // if-block "connectionFlash && DoOptionByteLoading"
[16:23:54.056]        // =>  FALSE
[16:23:54.056]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:23:54.056]    </control>
[16:23:54.056]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:23:54.056]      // if-block "DoDebugPortStop"
[16:23:54.056]        // =>  TRUE
[16:23:54.056]      <block atomic="false" info="">
[16:23:54.056]        WriteDP(DP_SELECT, 0x00000000);
[16:23:54.056]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:23:54.056]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:23:54.056]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:23:54.056]      </block>
[16:23:54.056]      // end if-block "DoDebugPortStop"
[16:23:54.056]    </control>
[16:23:54.056]  </sequence>
[16:23:54.056]  
[16:29:47.487]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:29:47.487]  
[16:29:47.487]  <debugvars>
[16:29:47.487]    // Pre-defined
[16:29:47.488]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:29:47.488]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:29:47.488]    __dp=0x00000000
[16:29:47.488]    __ap=0x00000000
[16:29:47.489]    __traceout=0x00000000      (Trace Disabled)
[16:29:47.489]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:29:47.489]    __FlashAddr=0x00000000
[16:29:47.490]    __FlashLen=0x00000000
[16:29:47.490]    __FlashArg=0x00000000
[16:29:47.491]    __FlashOp=0x00000000
[16:29:47.491]    __Result=0x00000000
[16:29:47.491]    
[16:29:47.491]    // User-defined
[16:29:47.491]    DbgMCU_CR=0x00000007
[16:29:47.491]    DbgMCU_APB1_Fz=0x00000000
[16:29:47.491]    DbgMCU_APB2_Fz=0x00000000
[16:29:47.493]    DoOptionByteLoading=0x00000000
[16:29:47.493]  </debugvars>
[16:29:47.493]  
[16:29:47.493]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:29:47.493]    <block atomic="false" info="">
[16:29:47.493]      Sequence("CheckID");
[16:29:47.494]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:29:47.495]          <block atomic="false" info="">
[16:29:47.495]            __var pidr1 = 0;
[16:29:47.495]              // -> [pidr1 <= 0x00000000]
[16:29:47.496]            __var pidr2 = 0;
[16:29:47.496]              // -> [pidr2 <= 0x00000000]
[16:29:47.496]            __var jep106id = 0;
[16:29:47.496]              // -> [jep106id <= 0x00000000]
[16:29:47.497]            __var ROMTableBase = 0;
[16:29:47.497]              // -> [ROMTableBase <= 0x00000000]
[16:29:47.497]            __ap = 0;      // AHB-AP
[16:29:47.497]              // -> [__ap <= 0x00000000]
[16:29:47.497]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:29:47.498]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:29:47.498]              // -> [ROMTableBase <= 0xF0000000]
[16:29:47.498]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:29:47.500]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:29:47.500]              // -> [pidr1 <= 0x00000004]
[16:29:47.500]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:29:47.501]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:29:47.502]              // -> [pidr2 <= 0x0000000A]
[16:29:47.502]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:29:47.502]              // -> [jep106id <= 0x00000020]
[16:29:47.503]          </block>
[16:29:47.503]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:29:47.503]            // if-block "jep106id != 0x20"
[16:29:47.503]              // =>  FALSE
[16:29:47.504]            // skip if-block "jep106id != 0x20"
[16:29:47.504]          </control>
[16:29:47.504]        </sequence>
[16:29:47.504]    </block>
[16:29:47.505]  </sequence>
[16:29:47.505]  
[16:29:47.516]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:29:47.516]  
[16:29:47.537]  <debugvars>
[16:29:47.538]    // Pre-defined
[16:29:47.538]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:29:47.538]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:29:47.539]    __dp=0x00000000
[16:29:47.540]    __ap=0x00000000
[16:29:47.540]    __traceout=0x00000000      (Trace Disabled)
[16:29:47.542]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:29:47.542]    __FlashAddr=0x00000000
[16:29:47.543]    __FlashLen=0x00000000
[16:29:47.543]    __FlashArg=0x00000000
[16:29:47.544]    __FlashOp=0x00000000
[16:29:47.544]    __Result=0x00000000
[16:29:47.544]    
[16:29:47.544]    // User-defined
[16:29:47.545]    DbgMCU_CR=0x00000007
[16:29:47.545]    DbgMCU_APB1_Fz=0x00000000
[16:29:47.546]    DbgMCU_APB2_Fz=0x00000000
[16:29:47.546]    DoOptionByteLoading=0x00000000
[16:29:47.547]  </debugvars>
[16:29:47.547]  
[16:29:47.547]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:29:47.547]    <block atomic="false" info="">
[16:29:47.548]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:29:47.550]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:29:47.550]    </block>
[16:29:47.551]    <block atomic="false" info="DbgMCU registers">
[16:29:47.551]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:29:47.553]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:29:47.554]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:29:47.554]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:29:47.555]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:29:47.556]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:29:47.557]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:29:47.557]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:29:47.559]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:29:47.559]    </block>
[16:29:47.559]  </sequence>
[16:29:47.560]  
[16:29:55.234]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:29:55.234]  
[16:29:55.235]  <debugvars>
[16:29:55.235]    // Pre-defined
[16:29:55.236]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:29:55.236]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:29:55.236]    __dp=0x00000000
[16:29:55.236]    __ap=0x00000000
[16:29:55.237]    __traceout=0x00000000      (Trace Disabled)
[16:29:55.237]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:29:55.237]    __FlashAddr=0x00000000
[16:29:55.237]    __FlashLen=0x00000000
[16:29:55.237]    __FlashArg=0x00000000
[16:29:55.237]    __FlashOp=0x00000000
[16:29:55.238]    __Result=0x00000000
[16:29:55.238]    
[16:29:55.238]    // User-defined
[16:29:55.238]    DbgMCU_CR=0x00000007
[16:29:55.238]    DbgMCU_APB1_Fz=0x00000000
[16:29:55.239]    DbgMCU_APB2_Fz=0x00000000
[16:29:55.239]    DoOptionByteLoading=0x00000000
[16:29:55.239]  </debugvars>
[16:29:55.239]  
[16:29:55.239]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:29:55.239]    <block atomic="false" info="">
[16:29:55.240]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:29:55.240]        // -> [connectionFlash <= 0x00000001]
[16:29:55.240]      __var FLASH_BASE = 0x40022000 ;
[16:29:55.240]        // -> [FLASH_BASE <= 0x40022000]
[16:29:55.241]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:29:55.241]        // -> [FLASH_CR <= 0x40022004]
[16:29:55.241]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:29:55.241]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:29:55.241]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:29:55.241]        // -> [LOCK_BIT <= 0x00000001]
[16:29:55.242]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:29:55.242]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:29:55.242]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:29:55.242]        // -> [FLASH_KEYR <= 0x4002200C]
[16:29:55.242]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:29:55.242]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:29:55.243]      __var FLASH_KEY2 = 0x02030405 ;
[16:29:55.243]        // -> [FLASH_KEY2 <= 0x02030405]
[16:29:55.243]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:29:55.243]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:29:55.243]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:29:55.244]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:29:55.244]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:29:55.244]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:29:55.245]      __var FLASH_CR_Value = 0 ;
[16:29:55.245]        // -> [FLASH_CR_Value <= 0x00000000]
[16:29:55.245]      __var DoDebugPortStop = 1 ;
[16:29:55.245]        // -> [DoDebugPortStop <= 0x00000001]
[16:29:55.246]      __var DP_CTRL_STAT = 0x4 ;
[16:29:55.246]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:29:55.246]      __var DP_SELECT = 0x8 ;
[16:29:55.246]        // -> [DP_SELECT <= 0x00000008]
[16:29:55.246]    </block>
[16:29:55.247]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:29:55.247]      // if-block "connectionFlash && DoOptionByteLoading"
[16:29:55.247]        // =>  FALSE
[16:29:55.247]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:29:55.247]    </control>
[16:29:55.248]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:29:55.248]      // if-block "DoDebugPortStop"
[16:29:55.248]        // =>  TRUE
[16:29:55.248]      <block atomic="false" info="">
[16:29:55.248]        WriteDP(DP_SELECT, 0x00000000);
[16:29:55.249]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:29:55.249]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:29:55.249]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:29:55.250]      </block>
[16:29:55.250]      // end if-block "DoDebugPortStop"
[16:29:55.250]    </control>
[16:29:55.250]  </sequence>
[16:29:55.251]  
[16:36:06.403]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:36:06.403]  
[16:36:06.419]  <debugvars>
[16:36:06.419]    // Pre-defined
[16:36:06.420]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:36:06.420]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:36:06.421]    __dp=0x00000000
[16:36:06.422]    __ap=0x00000000
[16:36:06.422]    __traceout=0x00000000      (Trace Disabled)
[16:36:06.423]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:36:06.423]    __FlashAddr=0x00000000
[16:36:06.424]    __FlashLen=0x00000000
[16:36:06.424]    __FlashArg=0x00000000
[16:36:06.424]    __FlashOp=0x00000000
[16:36:06.424]    __Result=0x00000000
[16:36:06.424]    
[16:36:06.424]    // User-defined
[16:36:06.424]    DbgMCU_CR=0x00000007
[16:36:06.424]    DbgMCU_APB1_Fz=0x00000000
[16:36:06.424]    DbgMCU_APB2_Fz=0x00000000
[16:36:06.424]    DoOptionByteLoading=0x00000000
[16:36:06.424]  </debugvars>
[16:36:06.424]  
[16:36:06.424]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:36:06.424]    <block atomic="false" info="">
[16:36:06.424]      Sequence("CheckID");
[16:36:06.424]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:36:06.424]          <block atomic="false" info="">
[16:36:06.424]            __var pidr1 = 0;
[16:36:06.424]              // -> [pidr1 <= 0x00000000]
[16:36:06.424]            __var pidr2 = 0;
[16:36:06.424]              // -> [pidr2 <= 0x00000000]
[16:36:06.424]            __var jep106id = 0;
[16:36:06.424]              // -> [jep106id <= 0x00000000]
[16:36:06.424]            __var ROMTableBase = 0;
[16:36:06.424]              // -> [ROMTableBase <= 0x00000000]
[16:36:06.424]            __ap = 0;      // AHB-AP
[16:36:06.424]              // -> [__ap <= 0x00000000]
[16:36:06.424]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:36:06.424]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:36:06.424]              // -> [ROMTableBase <= 0xF0000000]
[16:36:06.424]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:36:06.424]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:36:06.424]              // -> [pidr1 <= 0x00000004]
[16:36:06.424]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:36:06.424]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:36:06.424]              // -> [pidr2 <= 0x0000000A]
[16:36:06.424]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:36:06.424]              // -> [jep106id <= 0x00000020]
[16:36:06.424]          </block>
[16:36:06.424]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:36:06.424]            // if-block "jep106id != 0x20"
[16:36:06.424]              // =>  FALSE
[16:36:06.424]            // skip if-block "jep106id != 0x20"
[16:36:06.424]          </control>
[16:36:06.424]        </sequence>
[16:36:06.424]    </block>
[16:36:06.439]  </sequence>
[16:36:06.439]  
[16:36:06.451]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:36:06.451]  
[16:36:06.451]  <debugvars>
[16:36:06.452]    // Pre-defined
[16:36:06.452]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:36:06.452]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:36:06.452]    __dp=0x00000000
[16:36:06.453]    __ap=0x00000000
[16:36:06.453]    __traceout=0x00000000      (Trace Disabled)
[16:36:06.453]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:36:06.454]    __FlashAddr=0x00000000
[16:36:06.454]    __FlashLen=0x00000000
[16:36:06.454]    __FlashArg=0x00000000
[16:36:06.454]    __FlashOp=0x00000000
[16:36:06.454]    __Result=0x00000000
[16:36:06.454]    
[16:36:06.454]    // User-defined
[16:36:06.454]    DbgMCU_CR=0x00000007
[16:36:06.454]    DbgMCU_APB1_Fz=0x00000000
[16:36:06.454]    DbgMCU_APB2_Fz=0x00000000
[16:36:06.454]    DoOptionByteLoading=0x00000000
[16:36:06.454]  </debugvars>
[16:36:06.454]  
[16:36:06.454]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:36:06.454]    <block atomic="false" info="">
[16:36:06.454]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:36:06.454]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:36:06.454]    </block>
[16:36:06.459]    <block atomic="false" info="DbgMCU registers">
[16:36:06.459]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:36:06.460]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:36:06.460]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:36:06.461]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:36:06.462]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:36:06.462]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:36:06.463]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:36:06.463]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:36:06.464]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:36:06.464]    </block>
[16:36:06.464]  </sequence>
[16:36:06.464]  
[16:36:14.096]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:36:14.096]  
[16:36:14.096]  <debugvars>
[16:36:14.097]    // Pre-defined
[16:36:14.097]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:36:14.097]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:36:14.098]    __dp=0x00000000
[16:36:14.098]    __ap=0x00000000
[16:36:14.098]    __traceout=0x00000000      (Trace Disabled)
[16:36:14.099]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:36:14.099]    __FlashAddr=0x00000000
[16:36:14.099]    __FlashLen=0x00000000
[16:36:14.099]    __FlashArg=0x00000000
[16:36:14.099]    __FlashOp=0x00000000
[16:36:14.099]    __Result=0x00000000
[16:36:14.100]    
[16:36:14.100]    // User-defined
[16:36:14.100]    DbgMCU_CR=0x00000007
[16:36:14.100]    DbgMCU_APB1_Fz=0x00000000
[16:36:14.100]    DbgMCU_APB2_Fz=0x00000000
[16:36:14.101]    DoOptionByteLoading=0x00000000
[16:36:14.101]  </debugvars>
[16:36:14.101]  
[16:36:14.101]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:36:14.102]    <block atomic="false" info="">
[16:36:14.102]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:36:14.102]        // -> [connectionFlash <= 0x00000001]
[16:36:14.102]      __var FLASH_BASE = 0x40022000 ;
[16:36:14.102]        // -> [FLASH_BASE <= 0x40022000]
[16:36:14.102]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:36:14.103]        // -> [FLASH_CR <= 0x40022004]
[16:36:14.103]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:36:14.103]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:36:14.104]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:36:14.104]        // -> [LOCK_BIT <= 0x00000001]
[16:36:14.104]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:36:14.104]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:36:14.104]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:36:14.105]        // -> [FLASH_KEYR <= 0x4002200C]
[16:36:14.105]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:36:14.105]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:36:14.105]      __var FLASH_KEY2 = 0x02030405 ;
[16:36:14.105]        // -> [FLASH_KEY2 <= 0x02030405]
[16:36:14.105]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:36:14.106]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:36:14.106]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:36:14.107]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:36:14.107]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:36:14.107]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:36:14.107]      __var FLASH_CR_Value = 0 ;
[16:36:14.107]        // -> [FLASH_CR_Value <= 0x00000000]
[16:36:14.108]      __var DoDebugPortStop = 1 ;
[16:36:14.108]        // -> [DoDebugPortStop <= 0x00000001]
[16:36:14.108]      __var DP_CTRL_STAT = 0x4 ;
[16:36:14.108]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:36:14.108]      __var DP_SELECT = 0x8 ;
[16:36:14.108]        // -> [DP_SELECT <= 0x00000008]
[16:36:14.109]    </block>
[16:36:14.109]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:36:14.109]      // if-block "connectionFlash && DoOptionByteLoading"
[16:36:14.110]        // =>  FALSE
[16:36:14.110]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:36:14.110]    </control>
[16:36:14.110]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:36:14.111]      // if-block "DoDebugPortStop"
[16:36:14.111]        // =>  TRUE
[16:36:14.111]      <block atomic="false" info="">
[16:36:14.111]        WriteDP(DP_SELECT, 0x00000000);
[16:36:14.112]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:36:14.112]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:36:14.112]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:36:14.112]      </block>
[16:36:14.112]      // end if-block "DoDebugPortStop"
[16:36:14.113]    </control>
[16:36:14.113]  </sequence>
[16:36:14.113]  
[16:38:27.626]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:38:27.626]  
[16:38:27.626]  <debugvars>
[16:38:27.626]    // Pre-defined
[16:38:27.626]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:38:27.629]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:38:27.629]    __dp=0x00000000
[16:38:27.629]    __ap=0x00000000
[16:38:27.629]    __traceout=0x00000000      (Trace Disabled)
[16:38:27.629]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:38:27.630]    __FlashAddr=0x00000000
[16:38:27.630]    __FlashLen=0x00000000
[16:38:27.630]    __FlashArg=0x00000000
[16:38:27.630]    __FlashOp=0x00000000
[16:38:27.630]    __Result=0x00000000
[16:38:27.631]    
[16:38:27.631]    // User-defined
[16:38:27.631]    DbgMCU_CR=0x00000007
[16:38:27.631]    DbgMCU_APB1_Fz=0x00000000
[16:38:27.631]    DbgMCU_APB2_Fz=0x00000000
[16:38:27.631]    DoOptionByteLoading=0x00000000
[16:38:27.631]  </debugvars>
[16:38:27.631]  
[16:38:27.631]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:38:27.631]    <block atomic="false" info="">
[16:38:27.633]      Sequence("CheckID");
[16:38:27.633]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:38:27.633]          <block atomic="false" info="">
[16:38:27.633]            __var pidr1 = 0;
[16:38:27.633]              // -> [pidr1 <= 0x00000000]
[16:38:27.633]            __var pidr2 = 0;
[16:38:27.633]              // -> [pidr2 <= 0x00000000]
[16:38:27.633]            __var jep106id = 0;
[16:38:27.633]              // -> [jep106id <= 0x00000000]
[16:38:27.633]            __var ROMTableBase = 0;
[16:38:27.635]              // -> [ROMTableBase <= 0x00000000]
[16:38:27.635]            __ap = 0;      // AHB-AP
[16:38:27.635]              // -> [__ap <= 0x00000000]
[16:38:27.635]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:38:27.635]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:38:27.635]              // -> [ROMTableBase <= 0xF0000000]
[16:38:27.635]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:38:27.637]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:38:27.637]              // -> [pidr1 <= 0x00000004]
[16:38:27.637]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:38:27.639]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:38:27.639]              // -> [pidr2 <= 0x0000000A]
[16:38:27.639]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:38:27.639]              // -> [jep106id <= 0x00000020]
[16:38:27.639]          </block>
[16:38:27.639]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:38:27.639]            // if-block "jep106id != 0x20"
[16:38:27.639]              // =>  FALSE
[16:38:27.639]            // skip if-block "jep106id != 0x20"
[16:38:27.641]          </control>
[16:38:27.641]        </sequence>
[16:38:27.641]    </block>
[16:38:27.641]  </sequence>
[16:38:27.642]  
[16:38:27.654]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:38:27.654]  
[16:38:27.654]  <debugvars>
[16:38:27.654]    // Pre-defined
[16:38:27.654]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:38:27.654]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:38:27.654]    __dp=0x00000000
[16:38:27.654]    __ap=0x00000000
[16:38:27.656]    __traceout=0x00000000      (Trace Disabled)
[16:38:27.656]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:38:27.656]    __FlashAddr=0x00000000
[16:38:27.656]    __FlashLen=0x00000000
[16:38:27.656]    __FlashArg=0x00000000
[16:38:27.656]    __FlashOp=0x00000000
[16:38:27.656]    __Result=0x00000000
[16:38:27.656]    
[16:38:27.656]    // User-defined
[16:38:27.656]    DbgMCU_CR=0x00000007
[16:38:27.656]    DbgMCU_APB1_Fz=0x00000000
[16:38:27.658]    DbgMCU_APB2_Fz=0x00000000
[16:38:27.658]    DoOptionByteLoading=0x00000000
[16:38:27.659]  </debugvars>
[16:38:27.659]  
[16:38:27.659]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:38:27.659]    <block atomic="false" info="">
[16:38:27.659]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:38:27.661]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:38:27.661]    </block>
[16:38:27.661]    <block atomic="false" info="DbgMCU registers">
[16:38:27.661]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:38:27.661]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:38:27.663]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:38:27.663]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:38:27.665]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:38:27.665]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:38:27.665]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:38:27.667]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:38:27.667]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:38:27.667]    </block>
[16:38:27.667]  </sequence>
[16:38:27.667]  
[16:38:35.728]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:38:35.728]  
[16:38:35.729]  <debugvars>
[16:38:35.729]    // Pre-defined
[16:38:35.729]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:38:35.730]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:38:35.730]    __dp=0x00000000
[16:38:35.730]    __ap=0x00000000
[16:38:35.730]    __traceout=0x00000000      (Trace Disabled)
[16:38:35.730]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:38:35.731]    __FlashAddr=0x00000000
[16:38:35.731]    __FlashLen=0x00000000
[16:38:35.731]    __FlashArg=0x00000000
[16:38:35.732]    __FlashOp=0x00000000
[16:38:35.732]    __Result=0x00000000
[16:38:35.732]    
[16:38:35.732]    // User-defined
[16:38:35.732]    DbgMCU_CR=0x00000007
[16:38:35.732]    DbgMCU_APB1_Fz=0x00000000
[16:38:35.733]    DbgMCU_APB2_Fz=0x00000000
[16:38:35.733]    DoOptionByteLoading=0x00000000
[16:38:35.733]  </debugvars>
[16:38:35.733]  
[16:38:35.734]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:38:35.734]    <block atomic="false" info="">
[16:38:35.735]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:38:35.735]        // -> [connectionFlash <= 0x00000001]
[16:38:35.735]      __var FLASH_BASE = 0x40022000 ;
[16:38:35.735]        // -> [FLASH_BASE <= 0x40022000]
[16:38:35.735]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:38:35.736]        // -> [FLASH_CR <= 0x40022004]
[16:38:35.736]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:38:35.736]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:38:35.736]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:38:35.737]        // -> [LOCK_BIT <= 0x00000001]
[16:38:35.737]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:38:35.737]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:38:35.737]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:38:35.738]        // -> [FLASH_KEYR <= 0x4002200C]
[16:38:35.738]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:38:35.738]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:38:35.738]      __var FLASH_KEY2 = 0x02030405 ;
[16:38:35.739]        // -> [FLASH_KEY2 <= 0x02030405]
[16:38:35.739]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:38:35.739]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:38:35.739]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:38:35.739]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:38:35.740]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:38:35.740]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:38:35.740]      __var FLASH_CR_Value = 0 ;
[16:38:35.740]        // -> [FLASH_CR_Value <= 0x00000000]
[16:38:35.740]      __var DoDebugPortStop = 1 ;
[16:38:35.741]        // -> [DoDebugPortStop <= 0x00000001]
[16:38:35.741]      __var DP_CTRL_STAT = 0x4 ;
[16:38:35.741]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:38:35.741]      __var DP_SELECT = 0x8 ;
[16:38:35.741]        // -> [DP_SELECT <= 0x00000008]
[16:38:35.741]    </block>
[16:38:35.742]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:38:35.742]      // if-block "connectionFlash && DoOptionByteLoading"
[16:38:35.742]        // =>  FALSE
[16:38:35.743]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:38:35.743]    </control>
[16:38:35.743]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:38:35.743]      // if-block "DoDebugPortStop"
[16:38:35.743]        // =>  TRUE
[16:38:35.744]      <block atomic="false" info="">
[16:38:35.744]        WriteDP(DP_SELECT, 0x00000000);
[16:38:35.744]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:38:35.744]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:38:35.745]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:38:35.745]      </block>
[16:38:35.745]      // end if-block "DoDebugPortStop"
[16:38:35.746]    </control>
[16:38:35.746]  </sequence>
[16:38:35.746]  
[16:40:34.168]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:40:34.168]  
[16:40:34.185]  <debugvars>
[16:40:34.185]    // Pre-defined
[16:40:34.185]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:40:34.186]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:40:34.186]    __dp=0x00000000
[16:40:34.186]    __ap=0x00000000
[16:40:34.187]    __traceout=0x00000000      (Trace Disabled)
[16:40:34.187]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:40:34.187]    __FlashAddr=0x00000000
[16:40:34.187]    __FlashLen=0x00000000
[16:40:34.188]    __FlashArg=0x00000000
[16:40:34.188]    __FlashOp=0x00000000
[16:40:34.188]    __Result=0x00000000
[16:40:34.188]    
[16:40:34.188]    // User-defined
[16:40:34.189]    DbgMCU_CR=0x00000007
[16:40:34.189]    DbgMCU_APB1_Fz=0x00000000
[16:40:34.189]    DbgMCU_APB2_Fz=0x00000000
[16:40:34.189]    DoOptionByteLoading=0x00000000
[16:40:34.189]  </debugvars>
[16:40:34.189]  
[16:40:34.190]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:40:34.190]    <block atomic="false" info="">
[16:40:34.191]      Sequence("CheckID");
[16:40:34.191]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:40:34.191]          <block atomic="false" info="">
[16:40:34.191]            __var pidr1 = 0;
[16:40:34.191]              // -> [pidr1 <= 0x00000000]
[16:40:34.191]            __var pidr2 = 0;
[16:40:34.191]              // -> [pidr2 <= 0x00000000]
[16:40:34.192]            __var jep106id = 0;
[16:40:34.192]              // -> [jep106id <= 0x00000000]
[16:40:34.192]            __var ROMTableBase = 0;
[16:40:34.192]              // -> [ROMTableBase <= 0x00000000]
[16:40:34.192]            __ap = 0;      // AHB-AP
[16:40:34.192]              // -> [__ap <= 0x00000000]
[16:40:34.193]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:40:34.195]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:40:34.195]              // -> [ROMTableBase <= 0xF0000000]
[16:40:34.195]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:40:34.196]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:40:34.196]              // -> [pidr1 <= 0x00000004]
[16:40:34.197]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:40:34.198]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:40:34.198]              // -> [pidr2 <= 0x0000000A]
[16:40:34.198]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:40:34.198]              // -> [jep106id <= 0x00000020]
[16:40:34.199]          </block>
[16:40:34.199]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:40:34.199]            // if-block "jep106id != 0x20"
[16:40:34.200]              // =>  FALSE
[16:40:34.200]            // skip if-block "jep106id != 0x20"
[16:40:34.200]          </control>
[16:40:34.200]        </sequence>
[16:40:34.200]    </block>
[16:40:34.200]  </sequence>
[16:40:34.201]  
[16:40:34.213]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:40:34.213]  
[16:40:34.240]  <debugvars>
[16:40:34.242]    // Pre-defined
[16:40:34.242]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:40:34.242]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:40:34.242]    __dp=0x00000000
[16:40:34.243]    __ap=0x00000000
[16:40:34.243]    __traceout=0x00000000      (Trace Disabled)
[16:40:34.243]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:40:34.244]    __FlashAddr=0x00000000
[16:40:34.244]    __FlashLen=0x00000000
[16:40:34.244]    __FlashArg=0x00000000
[16:40:34.245]    __FlashOp=0x00000000
[16:40:34.245]    __Result=0x00000000
[16:40:34.245]    
[16:40:34.245]    // User-defined
[16:40:34.245]    DbgMCU_CR=0x00000007
[16:40:34.245]    DbgMCU_APB1_Fz=0x00000000
[16:40:34.245]    DbgMCU_APB2_Fz=0x00000000
[16:40:34.245]    DoOptionByteLoading=0x00000000
[16:40:34.245]  </debugvars>
[16:40:34.245]  
[16:40:34.245]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:40:34.245]    <block atomic="false" info="">
[16:40:34.245]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:40:34.245]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:40:34.245]    </block>
[16:40:34.245]    <block atomic="false" info="DbgMCU registers">
[16:40:34.245]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:40:34.250]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:40:34.250]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:40:34.250]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:40:34.250]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:40:34.250]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:40:34.250]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:40:34.250]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:40:34.250]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:40:34.250]    </block>
[16:40:34.250]  </sequence>
[16:40:34.250]  
[16:40:42.252]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:40:42.252]  
[16:40:42.252]  <debugvars>
[16:40:42.252]    // Pre-defined
[16:40:42.254]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:40:42.254]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:40:42.255]    __dp=0x00000000
[16:40:42.255]    __ap=0x00000000
[16:40:42.255]    __traceout=0x00000000      (Trace Disabled)
[16:40:42.255]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:40:42.255]    __FlashAddr=0x00000000
[16:40:42.256]    __FlashLen=0x00000000
[16:40:42.257]    __FlashArg=0x00000000
[16:40:42.257]    __FlashOp=0x00000000
[16:40:42.257]    __Result=0x00000000
[16:40:42.257]    
[16:40:42.257]    // User-defined
[16:40:42.258]    DbgMCU_CR=0x00000007
[16:40:42.258]    DbgMCU_APB1_Fz=0x00000000
[16:40:42.258]    DbgMCU_APB2_Fz=0x00000000
[16:40:42.258]    DoOptionByteLoading=0x00000000
[16:40:42.259]  </debugvars>
[16:40:42.259]  
[16:40:42.259]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:40:42.260]    <block atomic="false" info="">
[16:40:42.260]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:40:42.260]        // -> [connectionFlash <= 0x00000001]
[16:40:42.261]      __var FLASH_BASE = 0x40022000 ;
[16:40:42.261]        // -> [FLASH_BASE <= 0x40022000]
[16:40:42.261]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:40:42.261]        // -> [FLASH_CR <= 0x40022004]
[16:40:42.261]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:40:42.262]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:40:42.262]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:40:42.262]        // -> [LOCK_BIT <= 0x00000001]
[16:40:42.263]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:40:42.263]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:40:42.264]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:40:42.264]        // -> [FLASH_KEYR <= 0x4002200C]
[16:40:42.264]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:40:42.264]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:40:42.265]      __var FLASH_KEY2 = 0x02030405 ;
[16:40:42.265]        // -> [FLASH_KEY2 <= 0x02030405]
[16:40:42.265]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:40:42.265]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:40:42.266]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:40:42.266]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:40:42.266]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:40:42.266]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:40:42.266]      __var FLASH_CR_Value = 0 ;
[16:40:42.267]        // -> [FLASH_CR_Value <= 0x00000000]
[16:40:42.267]      __var DoDebugPortStop = 1 ;
[16:40:42.267]        // -> [DoDebugPortStop <= 0x00000001]
[16:40:42.267]      __var DP_CTRL_STAT = 0x4 ;
[16:40:42.268]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:40:42.268]      __var DP_SELECT = 0x8 ;
[16:40:42.268]        // -> [DP_SELECT <= 0x00000008]
[16:40:42.268]    </block>
[16:40:42.269]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:40:42.269]      // if-block "connectionFlash && DoOptionByteLoading"
[16:40:42.269]        // =>  FALSE
[16:40:42.269]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:40:42.270]    </control>
[16:40:42.270]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:40:42.270]      // if-block "DoDebugPortStop"
[16:40:42.270]        // =>  TRUE
[16:40:42.271]      <block atomic="false" info="">
[16:40:42.271]        WriteDP(DP_SELECT, 0x00000000);
[16:40:42.271]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:40:42.272]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:40:42.272]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:40:42.272]      </block>
[16:40:42.273]      // end if-block "DoDebugPortStop"
[16:40:42.273]    </control>
[16:40:42.273]  </sequence>
[16:40:42.273]  
[16:45:30.106]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:45:30.106]  
[16:45:30.106]  <debugvars>
[16:45:30.106]    // Pre-defined
[16:45:30.106]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:45:30.106]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:45:30.106]    __dp=0x00000000
[16:45:30.106]    __ap=0x00000000
[16:45:30.106]    __traceout=0x00000000      (Trace Disabled)
[16:45:30.106]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:45:30.106]    __FlashAddr=0x00000000
[16:45:30.106]    __FlashLen=0x00000000
[16:45:30.106]    __FlashArg=0x00000000
[16:45:30.106]    __FlashOp=0x00000000
[16:45:30.106]    __Result=0x00000000
[16:45:30.106]    
[16:45:30.106]    // User-defined
[16:45:30.106]    DbgMCU_CR=0x00000007
[16:45:30.106]    DbgMCU_APB1_Fz=0x00000000
[16:45:30.106]    DbgMCU_APB2_Fz=0x00000000
[16:45:30.106]    DoOptionByteLoading=0x00000000
[16:45:30.106]  </debugvars>
[16:45:30.106]  
[16:45:30.106]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:45:30.106]    <block atomic="false" info="">
[16:45:30.106]      Sequence("CheckID");
[16:45:30.106]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:45:30.106]          <block atomic="false" info="">
[16:45:30.106]            __var pidr1 = 0;
[16:45:30.106]              // -> [pidr1 <= 0x00000000]
[16:45:30.116]            __var pidr2 = 0;
[16:45:30.116]              // -> [pidr2 <= 0x00000000]
[16:45:30.116]            __var jep106id = 0;
[16:45:30.116]              // -> [jep106id <= 0x00000000]
[16:45:30.116]            __var ROMTableBase = 0;
[16:45:30.116]              // -> [ROMTableBase <= 0x00000000]
[16:45:30.116]            __ap = 0;      // AHB-AP
[16:45:30.116]              // -> [__ap <= 0x00000000]
[16:45:30.116]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:45:30.118]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:45:30.118]              // -> [ROMTableBase <= 0xF0000000]
[16:45:30.118]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:45:30.118]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:45:30.118]              // -> [pidr1 <= 0x00000004]
[16:45:30.118]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:45:30.118]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:45:30.118]              // -> [pidr2 <= 0x0000000A]
[16:45:30.118]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:45:30.118]              // -> [jep106id <= 0x00000020]
[16:45:30.118]          </block>
[16:45:30.118]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:45:30.118]            // if-block "jep106id != 0x20"
[16:45:30.118]              // =>  FALSE
[16:45:30.118]            // skip if-block "jep106id != 0x20"
[16:45:30.118]          </control>
[16:45:30.118]        </sequence>
[16:45:30.118]    </block>
[16:45:30.118]  </sequence>
[16:45:30.118]  
[16:45:30.135]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:45:30.135]  
[16:45:30.137]  <debugvars>
[16:45:30.137]    // Pre-defined
[16:45:30.137]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:45:30.137]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:45:30.137]    __dp=0x00000000
[16:45:30.139]    __ap=0x00000000
[16:45:30.139]    __traceout=0x00000000      (Trace Disabled)
[16:45:30.139]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:45:30.139]    __FlashAddr=0x00000000
[16:45:30.139]    __FlashLen=0x00000000
[16:45:30.139]    __FlashArg=0x00000000
[16:45:30.139]    __FlashOp=0x00000000
[16:45:30.139]    __Result=0x00000000
[16:45:30.139]    
[16:45:30.139]    // User-defined
[16:45:30.139]    DbgMCU_CR=0x00000007
[16:45:30.141]    DbgMCU_APB1_Fz=0x00000000
[16:45:30.141]    DbgMCU_APB2_Fz=0x00000000
[16:45:30.141]    DoOptionByteLoading=0x00000000
[16:45:30.141]  </debugvars>
[16:45:30.141]  
[16:45:30.141]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:45:30.141]    <block atomic="false" info="">
[16:45:30.141]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:45:30.143]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:45:30.143]    </block>
[16:45:30.143]    <block atomic="false" info="DbgMCU registers">
[16:45:30.143]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:45:30.145]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:45:30.145]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:45:30.145]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:45:30.147]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:45:30.147]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:45:30.147]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:45:30.149]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:45:30.149]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:45:30.149]    </block>
[16:45:30.149]  </sequence>
[16:45:30.149]  
[16:45:37.985]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:45:37.985]  
[16:45:37.985]  <debugvars>
[16:45:37.986]    // Pre-defined
[16:45:37.986]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:45:37.986]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:45:37.986]    __dp=0x00000000
[16:45:37.986]    __ap=0x00000000
[16:45:37.987]    __traceout=0x00000000      (Trace Disabled)
[16:45:37.987]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:45:37.987]    __FlashAddr=0x00000000
[16:45:37.988]    __FlashLen=0x00000000
[16:45:37.988]    __FlashArg=0x00000000
[16:45:37.988]    __FlashOp=0x00000000
[16:45:37.988]    __Result=0x00000000
[16:45:37.988]    
[16:45:37.988]    // User-defined
[16:45:37.989]    DbgMCU_CR=0x00000007
[16:45:37.989]    DbgMCU_APB1_Fz=0x00000000
[16:45:37.989]    DbgMCU_APB2_Fz=0x00000000
[16:45:37.990]    DoOptionByteLoading=0x00000000
[16:45:37.990]  </debugvars>
[16:45:37.990]  
[16:45:37.990]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:45:37.990]    <block atomic="false" info="">
[16:45:37.990]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:45:37.990]        // -> [connectionFlash <= 0x00000001]
[16:45:37.991]      __var FLASH_BASE = 0x40022000 ;
[16:45:37.991]        // -> [FLASH_BASE <= 0x40022000]
[16:45:37.991]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:45:37.992]        // -> [FLASH_CR <= 0x40022004]
[16:45:37.992]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:45:37.992]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:45:37.992]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:45:37.992]        // -> [LOCK_BIT <= 0x00000001]
[16:45:37.992]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:45:37.993]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:45:37.993]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:45:37.993]        // -> [FLASH_KEYR <= 0x4002200C]
[16:45:37.993]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:45:37.994]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:45:37.994]      __var FLASH_KEY2 = 0x02030405 ;
[16:45:37.994]        // -> [FLASH_KEY2 <= 0x02030405]
[16:45:37.994]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:45:37.995]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:45:37.995]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:45:37.995]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:45:37.995]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:45:37.995]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:45:37.996]      __var FLASH_CR_Value = 0 ;
[16:45:37.996]        // -> [FLASH_CR_Value <= 0x00000000]
[16:45:37.996]      __var DoDebugPortStop = 1 ;
[16:45:37.997]        // -> [DoDebugPortStop <= 0x00000001]
[16:45:37.997]      __var DP_CTRL_STAT = 0x4 ;
[16:45:37.997]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:45:37.997]      __var DP_SELECT = 0x8 ;
[16:45:37.997]        // -> [DP_SELECT <= 0x00000008]
[16:45:37.998]    </block>
[16:45:37.998]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:45:37.998]      // if-block "connectionFlash && DoOptionByteLoading"
[16:45:37.998]        // =>  FALSE
[16:45:37.998]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:45:37.999]    </control>
[16:45:37.999]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:45:37.999]      // if-block "DoDebugPortStop"
[16:45:37.999]        // =>  TRUE
[16:45:37.999]      <block atomic="false" info="">
[16:45:37.999]        WriteDP(DP_SELECT, 0x00000000);
[16:45:38.000]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:45:38.000]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:45:38.001]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:45:38.001]      </block>
[16:45:38.001]      // end if-block "DoDebugPortStop"
[16:45:38.001]    </control>
[16:45:38.001]  </sequence>
[16:45:38.001]  
[16:49:24.473]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:49:24.473]  
[16:49:24.473]  <debugvars>
[16:49:24.473]    // Pre-defined
[16:49:24.473]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:49:24.473]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:49:24.473]    __dp=0x00000000
[16:49:24.473]    __ap=0x00000000
[16:49:24.473]    __traceout=0x00000000      (Trace Disabled)
[16:49:24.473]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:49:24.473]    __FlashAddr=0x00000000
[16:49:24.473]    __FlashLen=0x00000000
[16:49:24.473]    __FlashArg=0x00000000
[16:49:24.473]    __FlashOp=0x00000000
[16:49:24.473]    __Result=0x00000000
[16:49:24.473]    
[16:49:24.473]    // User-defined
[16:49:24.473]    DbgMCU_CR=0x00000007
[16:49:24.473]    DbgMCU_APB1_Fz=0x00000000
[16:49:24.473]    DbgMCU_APB2_Fz=0x00000000
[16:49:24.473]    DoOptionByteLoading=0x00000000
[16:49:24.478]  </debugvars>
[16:49:24.478]  
[16:49:24.478]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:49:24.478]    <block atomic="false" info="">
[16:49:24.478]      Sequence("CheckID");
[16:49:24.478]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:49:24.478]          <block atomic="false" info="">
[16:49:24.478]            __var pidr1 = 0;
[16:49:24.478]              // -> [pidr1 <= 0x00000000]
[16:49:24.478]            __var pidr2 = 0;
[16:49:24.480]              // -> [pidr2 <= 0x00000000]
[16:49:24.480]            __var jep106id = 0;
[16:49:24.480]              // -> [jep106id <= 0x00000000]
[16:49:24.480]            __var ROMTableBase = 0;
[16:49:24.480]              // -> [ROMTableBase <= 0x00000000]
[16:49:24.480]            __ap = 0;      // AHB-AP
[16:49:24.480]              // -> [__ap <= 0x00000000]
[16:49:24.480]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:49:24.483]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:49:24.483]              // -> [ROMTableBase <= 0xF0000000]
[16:49:24.483]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:49:24.483]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:49:24.483]              // -> [pidr1 <= 0x00000004]
[16:49:24.483]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:49:24.485]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:49:24.485]              // -> [pidr2 <= 0x0000000A]
[16:49:24.485]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:49:24.485]              // -> [jep106id <= 0x00000020]
[16:49:24.485]          </block>
[16:49:24.485]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:49:24.485]            // if-block "jep106id != 0x20"
[16:49:24.487]              // =>  FALSE
[16:49:24.487]            // skip if-block "jep106id != 0x20"
[16:49:24.487]          </control>
[16:49:24.487]        </sequence>
[16:49:24.487]    </block>
[16:49:24.487]  </sequence>
[16:49:24.487]  
[16:49:24.500]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:49:24.500]  
[16:49:24.501]  <debugvars>
[16:49:24.501]    // Pre-defined
[16:49:24.501]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:49:24.501]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:49:24.501]    __dp=0x00000000
[16:49:24.501]    __ap=0x00000000
[16:49:24.502]    __traceout=0x00000000      (Trace Disabled)
[16:49:24.502]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:49:24.502]    __FlashAddr=0x00000000
[16:49:24.502]    __FlashLen=0x00000000
[16:49:24.502]    __FlashArg=0x00000000
[16:49:24.502]    __FlashOp=0x00000000
[16:49:24.502]    __Result=0x00000000
[16:49:24.502]    
[16:49:24.502]    // User-defined
[16:49:24.502]    DbgMCU_CR=0x00000007
[16:49:24.504]    DbgMCU_APB1_Fz=0x00000000
[16:49:24.504]    DbgMCU_APB2_Fz=0x00000000
[16:49:24.504]    DoOptionByteLoading=0x00000000
[16:49:24.504]  </debugvars>
[16:49:24.504]  
[16:49:24.504]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:49:24.504]    <block atomic="false" info="">
[16:49:24.504]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:49:24.506]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:49:24.506]    </block>
[16:49:24.506]    <block atomic="false" info="DbgMCU registers">
[16:49:24.506]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:49:24.506]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:49:24.508]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:49:24.508]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:49:24.508]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:49:24.508]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:49:24.510]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:49:24.510]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:49:24.512]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:49:24.512]    </block>
[16:49:24.512]  </sequence>
[16:49:24.512]  
[16:49:32.410]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:49:32.410]  
[16:49:32.411]  <debugvars>
[16:49:32.412]    // Pre-defined
[16:49:32.412]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:49:32.412]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:49:32.413]    __dp=0x00000000
[16:49:32.414]    __ap=0x00000000
[16:49:32.414]    __traceout=0x00000000      (Trace Disabled)
[16:49:32.414]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:49:32.415]    __FlashAddr=0x00000000
[16:49:32.415]    __FlashLen=0x00000000
[16:49:32.415]    __FlashArg=0x00000000
[16:49:32.416]    __FlashOp=0x00000000
[16:49:32.416]    __Result=0x00000000
[16:49:32.416]    
[16:49:32.416]    // User-defined
[16:49:32.417]    DbgMCU_CR=0x00000007
[16:49:32.417]    DbgMCU_APB1_Fz=0x00000000
[16:49:32.417]    DbgMCU_APB2_Fz=0x00000000
[16:49:32.417]    DoOptionByteLoading=0x00000000
[16:49:32.418]  </debugvars>
[16:49:32.418]  
[16:49:32.418]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:49:32.418]    <block atomic="false" info="">
[16:49:32.418]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:49:32.419]        // -> [connectionFlash <= 0x00000001]
[16:49:32.419]      __var FLASH_BASE = 0x40022000 ;
[16:49:32.419]        // -> [FLASH_BASE <= 0x40022000]
[16:49:32.419]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:49:32.419]        // -> [FLASH_CR <= 0x40022004]
[16:49:32.419]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:49:32.419]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:49:32.419]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:49:32.419]        // -> [LOCK_BIT <= 0x00000001]
[16:49:32.419]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:49:32.419]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:49:32.419]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:49:32.419]        // -> [FLASH_KEYR <= 0x4002200C]
[16:49:32.419]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:49:32.419]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:49:32.419]      __var FLASH_KEY2 = 0x02030405 ;
[16:49:32.419]        // -> [FLASH_KEY2 <= 0x02030405]
[16:49:32.419]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:49:32.419]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:49:32.419]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:49:32.419]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:49:32.419]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:49:32.419]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:49:32.419]      __var FLASH_CR_Value = 0 ;
[16:49:32.419]        // -> [FLASH_CR_Value <= 0x00000000]
[16:49:32.419]      __var DoDebugPortStop = 1 ;
[16:49:32.419]        // -> [DoDebugPortStop <= 0x00000001]
[16:49:32.419]      __var DP_CTRL_STAT = 0x4 ;
[16:49:32.419]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:49:32.419]      __var DP_SELECT = 0x8 ;
[16:49:32.419]        // -> [DP_SELECT <= 0x00000008]
[16:49:32.419]    </block>
[16:49:32.426]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:49:32.426]      // if-block "connectionFlash && DoOptionByteLoading"
[16:49:32.426]        // =>  FALSE
[16:49:32.426]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:49:32.426]    </control>
[16:49:32.426]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:49:32.427]      // if-block "DoDebugPortStop"
[16:49:32.427]        // =>  TRUE
[16:49:32.427]      <block atomic="false" info="">
[16:49:32.427]        WriteDP(DP_SELECT, 0x00000000);
[16:49:32.428]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:49:32.428]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:49:32.429]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:49:32.429]      </block>
[16:49:32.429]      // end if-block "DoDebugPortStop"
[16:49:32.429]    </control>
[16:49:32.429]  </sequence>
[16:49:32.430]  
[16:52:43.409]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:52:43.409]  
[16:52:43.409]  <debugvars>
[16:52:43.409]    // Pre-defined
[16:52:43.410]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:52:43.410]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:52:43.410]    __dp=0x00000000
[16:52:43.411]    __ap=0x00000000
[16:52:43.411]    __traceout=0x00000000      (Trace Disabled)
[16:52:43.411]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:52:43.412]    __FlashAddr=0x00000000
[16:52:43.412]    __FlashLen=0x00000000
[16:52:43.412]    __FlashArg=0x00000000
[16:52:43.412]    __FlashOp=0x00000000
[16:52:43.413]    __Result=0x00000000
[16:52:43.413]    
[16:52:43.413]    // User-defined
[16:52:43.413]    DbgMCU_CR=0x00000007
[16:52:43.414]    DbgMCU_APB1_Fz=0x00000000
[16:52:43.414]    DbgMCU_APB2_Fz=0x00000000
[16:52:43.414]    DoOptionByteLoading=0x00000000
[16:52:43.415]  </debugvars>
[16:52:43.415]  
[16:52:43.415]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:52:43.415]    <block atomic="false" info="">
[16:52:43.416]      Sequence("CheckID");
[16:52:43.416]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:52:43.416]          <block atomic="false" info="">
[16:52:43.416]            __var pidr1 = 0;
[16:52:43.416]              // -> [pidr1 <= 0x00000000]
[16:52:43.416]            __var pidr2 = 0;
[16:52:43.417]              // -> [pidr2 <= 0x00000000]
[16:52:43.417]            __var jep106id = 0;
[16:52:43.417]              // -> [jep106id <= 0x00000000]
[16:52:43.417]            __var ROMTableBase = 0;
[16:52:43.417]              // -> [ROMTableBase <= 0x00000000]
[16:52:43.419]            __ap = 0;      // AHB-AP
[16:52:43.420]              // -> [__ap <= 0x00000000]
[16:52:43.420]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:52:43.421]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:52:43.421]              // -> [ROMTableBase <= 0xF0000000]
[16:52:43.421]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:52:43.423]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:52:43.423]              // -> [pidr1 <= 0x00000004]
[16:52:43.423]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:52:43.424]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:52:43.424]              // -> [pidr2 <= 0x0000000A]
[16:52:43.424]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:52:43.425]              // -> [jep106id <= 0x00000020]
[16:52:43.425]          </block>
[16:52:43.425]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:52:43.425]            // if-block "jep106id != 0x20"
[16:52:43.425]              // =>  FALSE
[16:52:43.426]            // skip if-block "jep106id != 0x20"
[16:52:43.426]          </control>
[16:52:43.426]        </sequence>
[16:52:43.426]    </block>
[16:52:43.426]  </sequence>
[16:52:43.427]  
[16:52:43.438]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:52:43.438]  
[16:52:43.461]  <debugvars>
[16:52:43.462]    // Pre-defined
[16:52:43.463]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:52:43.464]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:52:43.464]    __dp=0x00000000
[16:52:43.465]    __ap=0x00000000
[16:52:43.466]    __traceout=0x00000000      (Trace Disabled)
[16:52:43.467]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:52:43.467]    __FlashAddr=0x00000000
[16:52:43.468]    __FlashLen=0x00000000
[16:52:43.469]    __FlashArg=0x00000000
[16:52:43.469]    __FlashOp=0x00000000
[16:52:43.470]    __Result=0x00000000
[16:52:43.471]    
[16:52:43.471]    // User-defined
[16:52:43.471]    DbgMCU_CR=0x00000007
[16:52:43.471]    DbgMCU_APB1_Fz=0x00000000
[16:52:43.472]    DbgMCU_APB2_Fz=0x00000000
[16:52:43.472]    DoOptionByteLoading=0x00000000
[16:52:43.472]  </debugvars>
[16:52:43.472]  
[16:52:43.474]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:52:43.474]    <block atomic="false" info="">
[16:52:43.474]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:52:43.474]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:52:43.474]    </block>
[16:52:43.474]    <block atomic="false" info="DbgMCU registers">
[16:52:43.474]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:52:43.482]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:52:43.484]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:52:43.484]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:52:43.486]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:52:43.486]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:52:43.487]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:52:43.488]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:52:43.488]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:52:43.489]    </block>
[16:52:43.489]  </sequence>
[16:52:43.490]  
[16:52:51.439]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:52:51.439]  
[16:52:51.439]  <debugvars>
[16:52:51.439]    // Pre-defined
[16:52:51.439]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:52:51.439]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:52:51.439]    __dp=0x00000000
[16:52:51.439]    __ap=0x00000000
[16:52:51.439]    __traceout=0x00000000      (Trace Disabled)
[16:52:51.439]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:52:51.439]    __FlashAddr=0x00000000
[16:52:51.439]    __FlashLen=0x00000000
[16:52:51.439]    __FlashArg=0x00000000
[16:52:51.439]    __FlashOp=0x00000000
[16:52:51.439]    __Result=0x00000000
[16:52:51.439]    
[16:52:51.439]    // User-defined
[16:52:51.439]    DbgMCU_CR=0x00000007
[16:52:51.439]    DbgMCU_APB1_Fz=0x00000000
[16:52:51.439]    DbgMCU_APB2_Fz=0x00000000
[16:52:51.439]    DoOptionByteLoading=0x00000000
[16:52:51.439]  </debugvars>
[16:52:51.439]  
[16:52:51.439]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:52:51.439]    <block atomic="false" info="">
[16:52:51.450]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:52:51.450]        // -> [connectionFlash <= 0x00000001]
[16:52:51.450]      __var FLASH_BASE = 0x40022000 ;
[16:52:51.450]        // -> [FLASH_BASE <= 0x40022000]
[16:52:51.450]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:52:51.450]        // -> [FLASH_CR <= 0x40022004]
[16:52:51.450]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:52:51.450]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:52:51.452]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:52:51.452]        // -> [LOCK_BIT <= 0x00000001]
[16:52:51.452]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:52:51.452]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:52:51.452]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:52:51.452]        // -> [FLASH_KEYR <= 0x4002200C]
[16:52:51.452]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:52:51.452]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:52:51.452]      __var FLASH_KEY2 = 0x02030405 ;
[16:52:51.452]        // -> [FLASH_KEY2 <= 0x02030405]
[16:52:51.452]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:52:51.452]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:52:51.452]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:52:51.452]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:52:51.452]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:52:51.452]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:52:51.452]      __var FLASH_CR_Value = 0 ;
[16:52:51.452]        // -> [FLASH_CR_Value <= 0x00000000]
[16:52:51.452]      __var DoDebugPortStop = 1 ;
[16:52:51.452]        // -> [DoDebugPortStop <= 0x00000001]
[16:52:51.452]      __var DP_CTRL_STAT = 0x4 ;
[16:52:51.452]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:52:51.452]      __var DP_SELECT = 0x8 ;
[16:52:51.452]        // -> [DP_SELECT <= 0x00000008]
[16:52:51.452]    </block>
[16:52:51.452]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:52:51.452]      // if-block "connectionFlash && DoOptionByteLoading"
[16:52:51.452]        // =>  FALSE
[16:52:51.452]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:52:51.460]    </control>
[16:52:51.460]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:52:51.460]      // if-block "DoDebugPortStop"
[16:52:51.460]        // =>  TRUE
[16:52:51.460]      <block atomic="false" info="">
[16:52:51.460]        WriteDP(DP_SELECT, 0x00000000);
[16:52:51.460]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:52:51.460]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:52:51.460]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:52:51.460]      </block>
[16:52:51.460]      // end if-block "DoDebugPortStop"
[16:52:51.460]    </control>
[16:52:51.460]  </sequence>
[16:52:51.460]  
[17:16:07.208]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:16:07.208]  
[17:16:07.217]  <debugvars>
[17:16:07.217]    // Pre-defined
[17:16:07.218]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:16:07.218]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:16:07.218]    __dp=0x00000000
[17:16:07.218]    __ap=0x00000000
[17:16:07.219]    __traceout=0x00000000      (Trace Disabled)
[17:16:07.219]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:16:07.219]    __FlashAddr=0x00000000
[17:16:07.219]    __FlashLen=0x00000000
[17:16:07.220]    __FlashArg=0x00000000
[17:16:07.220]    __FlashOp=0x00000000
[17:16:07.220]    __Result=0x00000000
[17:16:07.221]    
[17:16:07.221]    // User-defined
[17:16:07.221]    DbgMCU_CR=0x00000007
[17:16:07.221]    DbgMCU_APB1_Fz=0x00000000
[17:16:07.221]    DbgMCU_APB2_Fz=0x00000000
[17:16:07.221]    DoOptionByteLoading=0x00000000
[17:16:07.222]  </debugvars>
[17:16:07.222]  
[17:16:07.222]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:16:07.222]    <block atomic="false" info="">
[17:16:07.222]      Sequence("CheckID");
[17:16:07.223]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:16:07.223]          <block atomic="false" info="">
[17:16:07.223]            __var pidr1 = 0;
[17:16:07.223]              // -> [pidr1 <= 0x00000000]
[17:16:07.224]            __var pidr2 = 0;
[17:16:07.224]              // -> [pidr2 <= 0x00000000]
[17:16:07.224]            __var jep106id = 0;
[17:16:07.224]              // -> [jep106id <= 0x00000000]
[17:16:07.225]            __var ROMTableBase = 0;
[17:16:07.225]              // -> [ROMTableBase <= 0x00000000]
[17:16:07.225]            __ap = 0;      // AHB-AP
[17:16:07.225]              // -> [__ap <= 0x00000000]
[17:16:07.225]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:16:07.226]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:16:07.226]              // -> [ROMTableBase <= 0xF0000000]
[17:16:07.226]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:16:07.226]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:16:07.226]              // -> [pidr1 <= 0x00000004]
[17:16:07.228]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:16:07.229]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:16:07.229]              // -> [pidr2 <= 0x0000000A]
[17:16:07.229]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:16:07.229]              // -> [jep106id <= 0x00000020]
[17:16:07.230]          </block>
[17:16:07.230]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:16:07.230]            // if-block "jep106id != 0x20"
[17:16:07.231]              // =>  FALSE
[17:16:07.231]            // skip if-block "jep106id != 0x20"
[17:16:07.231]          </control>
[17:16:07.231]        </sequence>
[17:16:07.232]    </block>
[17:16:07.232]  </sequence>
[17:16:07.233]  
[17:16:07.245]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:16:07.245]  
[17:16:07.256]  <debugvars>
[17:16:07.257]    // Pre-defined
[17:16:07.257]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:16:07.257]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:16:07.257]    __dp=0x00000000
[17:16:07.257]    __ap=0x00000000
[17:16:07.258]    __traceout=0x00000000      (Trace Disabled)
[17:16:07.258]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:16:07.258]    __FlashAddr=0x00000000
[17:16:07.258]    __FlashLen=0x00000000
[17:16:07.259]    __FlashArg=0x00000000
[17:16:07.259]    __FlashOp=0x00000000
[17:16:07.259]    __Result=0x00000000
[17:16:07.259]    
[17:16:07.259]    // User-defined
[17:16:07.259]    DbgMCU_CR=0x00000007
[17:16:07.260]    DbgMCU_APB1_Fz=0x00000000
[17:16:07.260]    DbgMCU_APB2_Fz=0x00000000
[17:16:07.260]    DoOptionByteLoading=0x00000000
[17:16:07.261]  </debugvars>
[17:16:07.261]  
[17:16:07.261]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:16:07.261]    <block atomic="false" info="">
[17:16:07.261]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:16:07.262]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:16:07.263]    </block>
[17:16:07.263]    <block atomic="false" info="DbgMCU registers">
[17:16:07.263]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:16:07.263]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[17:16:07.265]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[17:16:07.265]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:16:07.266]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:16:07.266]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:16:07.268]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:16:07.268]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:16:07.269]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:16:07.269]    </block>
[17:16:07.269]  </sequence>
[17:16:07.270]  
[17:16:15.359]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:16:15.359]  
[17:16:15.360]  <debugvars>
[17:16:15.361]    // Pre-defined
[17:16:15.361]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:16:15.361]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:16:15.362]    __dp=0x00000000
[17:16:15.362]    __ap=0x00000000
[17:16:15.363]    __traceout=0x00000000      (Trace Disabled)
[17:16:15.363]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:16:15.363]    __FlashAddr=0x00000000
[17:16:15.364]    __FlashLen=0x00000000
[17:16:15.364]    __FlashArg=0x00000000
[17:16:15.364]    __FlashOp=0x00000000
[17:16:15.364]    __Result=0x00000000
[17:16:15.365]    
[17:16:15.365]    // User-defined
[17:16:15.365]    DbgMCU_CR=0x00000007
[17:16:15.365]    DbgMCU_APB1_Fz=0x00000000
[17:16:15.365]    DbgMCU_APB2_Fz=0x00000000
[17:16:15.366]    DoOptionByteLoading=0x00000000
[17:16:15.366]  </debugvars>
[17:16:15.366]  
[17:16:15.366]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:16:15.367]    <block atomic="false" info="">
[17:16:15.367]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:16:15.367]        // -> [connectionFlash <= 0x00000001]
[17:16:15.367]      __var FLASH_BASE = 0x40022000 ;
[17:16:15.367]        // -> [FLASH_BASE <= 0x40022000]
[17:16:15.368]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:16:15.368]        // -> [FLASH_CR <= 0x40022004]
[17:16:15.368]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:16:15.368]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:16:15.368]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:16:15.368]        // -> [LOCK_BIT <= 0x00000001]
[17:16:15.369]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:16:15.369]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:16:15.369]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:16:15.369]        // -> [FLASH_KEYR <= 0x4002200C]
[17:16:15.369]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:16:15.369]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:16:15.370]      __var FLASH_KEY2 = 0x02030405 ;
[17:16:15.370]        // -> [FLASH_KEY2 <= 0x02030405]
[17:16:15.370]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:16:15.370]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:16:15.370]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:16:15.371]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:16:15.371]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:16:15.371]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:16:15.371]      __var FLASH_CR_Value = 0 ;
[17:16:15.371]        // -> [FLASH_CR_Value <= 0x00000000]
[17:16:15.372]      __var DoDebugPortStop = 1 ;
[17:16:15.372]        // -> [DoDebugPortStop <= 0x00000001]
[17:16:15.372]      __var DP_CTRL_STAT = 0x4 ;
[17:16:15.372]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:16:15.372]      __var DP_SELECT = 0x8 ;
[17:16:15.372]        // -> [DP_SELECT <= 0x00000008]
[17:16:15.373]    </block>
[17:16:15.373]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:16:15.373]      // if-block "connectionFlash && DoOptionByteLoading"
[17:16:15.373]        // =>  FALSE
[17:16:15.373]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:16:15.374]    </control>
[17:16:15.374]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:16:15.374]      // if-block "DoDebugPortStop"
[17:16:15.374]        // =>  TRUE
[17:16:15.374]      <block atomic="false" info="">
[17:16:15.374]        WriteDP(DP_SELECT, 0x00000000);
[17:16:15.375]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:16:15.375]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:16:15.376]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:16:15.376]      </block>
[17:16:15.376]      // end if-block "DoDebugPortStop"
[17:16:15.377]    </control>
[17:16:15.377]  </sequence>
[17:16:15.377]  
[17:23:25.725]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:23:25.725]  
[17:23:25.725]  <debugvars>
[17:23:25.725]    // Pre-defined
[17:23:25.725]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:23:25.725]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:23:25.725]    __dp=0x00000000
[17:23:25.735]    __ap=0x00000000
[17:23:25.735]    __traceout=0x00000000      (Trace Disabled)
[17:23:25.735]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:23:25.735]    __FlashAddr=0x00000000
[17:23:25.735]    __FlashLen=0x00000000
[17:23:25.735]    __FlashArg=0x00000000
[17:23:25.735]    __FlashOp=0x00000000
[17:23:25.735]    __Result=0x00000000
[17:23:25.735]    
[17:23:25.735]    // User-defined
[17:23:25.735]    DbgMCU_CR=0x00000007
[17:23:25.735]    DbgMCU_APB1_Fz=0x00000000
[17:23:25.735]    DbgMCU_APB2_Fz=0x00000000
[17:23:25.735]    DoOptionByteLoading=0x00000000
[17:23:25.735]  </debugvars>
[17:23:25.735]  
[17:23:25.735]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:23:25.738]    <block atomic="false" info="">
[17:23:25.738]      Sequence("CheckID");
[17:23:25.738]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:23:25.738]          <block atomic="false" info="">
[17:23:25.738]            __var pidr1 = 0;
[17:23:25.738]              // -> [pidr1 <= 0x00000000]
[17:23:25.738]            __var pidr2 = 0;
[17:23:25.738]              // -> [pidr2 <= 0x00000000]
[17:23:25.738]            __var jep106id = 0;
[17:23:25.740]              // -> [jep106id <= 0x00000000]
[17:23:25.740]            __var ROMTableBase = 0;
[17:23:25.740]              // -> [ROMTableBase <= 0x00000000]
[17:23:25.740]            __ap = 0;      // AHB-AP
[17:23:25.740]              // -> [__ap <= 0x00000000]
[17:23:25.740]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:23:25.740]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:23:25.740]              // -> [ROMTableBase <= 0xF0000000]
[17:23:25.740]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:23:25.740]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:23:25.740]              // -> [pidr1 <= 0x00000004]
[17:23:25.740]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:23:25.740]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:23:25.740]              // -> [pidr2 <= 0x0000000A]
[17:23:25.740]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:23:25.740]              // -> [jep106id <= 0x00000020]
[17:23:25.740]          </block>
[17:23:25.740]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:23:25.745]            // if-block "jep106id != 0x20"
[17:23:25.745]              // =>  FALSE
[17:23:25.745]            // skip if-block "jep106id != 0x20"
[17:23:25.745]          </control>
[17:23:25.745]        </sequence>
[17:23:25.745]    </block>
[17:23:25.745]  </sequence>
[17:23:25.745]  
[17:23:25.755]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:23:25.755]  
[17:23:25.755]  <debugvars>
[17:23:25.755]    // Pre-defined
[17:23:25.755]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:23:25.759]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:23:25.759]    __dp=0x00000000
[17:23:25.759]    __ap=0x00000000
[17:23:25.759]    __traceout=0x00000000      (Trace Disabled)
[17:23:25.759]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:23:25.759]    __FlashAddr=0x00000000
[17:23:25.759]    __FlashLen=0x00000000
[17:23:25.759]    __FlashArg=0x00000000
[17:23:25.759]    __FlashOp=0x00000000
[17:23:25.759]    __Result=0x00000000
[17:23:25.759]    
[17:23:25.759]    // User-defined
[17:23:25.759]    DbgMCU_CR=0x00000007
[17:23:25.759]    DbgMCU_APB1_Fz=0x00000000
[17:23:25.759]    DbgMCU_APB2_Fz=0x00000000
[17:23:25.759]    DoOptionByteLoading=0x00000000
[17:23:25.759]  </debugvars>
[17:23:25.759]  
[17:23:25.759]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:23:25.759]    <block atomic="false" info="">
[17:23:25.759]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:23:25.759]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:23:25.759]    </block>
[17:23:25.759]    <block atomic="false" info="DbgMCU registers">
[17:23:25.759]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:23:25.759]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[17:23:25.759]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[17:23:25.759]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:23:25.759]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:23:25.759]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:23:25.759]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:23:25.768]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:23:25.768]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:23:25.768]    </block>
[17:23:25.768]  </sequence>
[17:23:25.768]  
[17:23:33.588]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:23:33.588]  
[17:23:33.588]  <debugvars>
[17:23:33.589]    // Pre-defined
[17:23:33.589]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:23:33.589]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:23:33.589]    __dp=0x00000000
[17:23:33.590]    __ap=0x00000000
[17:23:33.590]    __traceout=0x00000000      (Trace Disabled)
[17:23:33.591]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:23:33.591]    __FlashAddr=0x00000000
[17:23:33.591]    __FlashLen=0x00000000
[17:23:33.591]    __FlashArg=0x00000000
[17:23:33.591]    __FlashOp=0x00000000
[17:23:33.592]    __Result=0x00000000
[17:23:33.592]    
[17:23:33.592]    // User-defined
[17:23:33.592]    DbgMCU_CR=0x00000007
[17:23:33.593]    DbgMCU_APB1_Fz=0x00000000
[17:23:33.593]    DbgMCU_APB2_Fz=0x00000000
[17:23:33.593]    DoOptionByteLoading=0x00000000
[17:23:33.594]  </debugvars>
[17:23:33.594]  
[17:23:33.595]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:23:33.595]    <block atomic="false" info="">
[17:23:33.596]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:23:33.596]        // -> [connectionFlash <= 0x00000001]
[17:23:33.597]      __var FLASH_BASE = 0x40022000 ;
[17:23:33.597]        // -> [FLASH_BASE <= 0x40022000]
[17:23:33.597]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:23:33.598]        // -> [FLASH_CR <= 0x40022004]
[17:23:33.598]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:23:33.598]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:23:33.598]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:23:33.599]        // -> [LOCK_BIT <= 0x00000001]
[17:23:33.599]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:23:33.599]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:23:33.599]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:23:33.600]        // -> [FLASH_KEYR <= 0x4002200C]
[17:23:33.601]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:23:33.601]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:23:33.601]      __var FLASH_KEY2 = 0x02030405 ;
[17:23:33.602]        // -> [FLASH_KEY2 <= 0x02030405]
[17:23:33.602]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:23:33.602]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:23:33.602]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:23:33.603]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:23:33.603]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:23:33.603]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:23:33.603]      __var FLASH_CR_Value = 0 ;
[17:23:33.603]        // -> [FLASH_CR_Value <= 0x00000000]
[17:23:33.604]      __var DoDebugPortStop = 1 ;
[17:23:33.604]        // -> [DoDebugPortStop <= 0x00000001]
[17:23:33.604]      __var DP_CTRL_STAT = 0x4 ;
[17:23:33.604]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:23:33.604]      __var DP_SELECT = 0x8 ;
[17:23:33.604]        // -> [DP_SELECT <= 0x00000008]
[17:23:33.605]    </block>
[17:23:33.605]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:23:33.605]      // if-block "connectionFlash && DoOptionByteLoading"
[17:23:33.606]        // =>  FALSE
[17:23:33.606]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:23:33.606]    </control>
[17:23:33.606]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:23:33.607]      // if-block "DoDebugPortStop"
[17:23:33.607]        // =>  TRUE
[17:23:33.607]      <block atomic="false" info="">
[17:23:33.607]        WriteDP(DP_SELECT, 0x00000000);
[17:23:33.608]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:23:33.608]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:23:33.608]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:23:33.609]      </block>
[17:23:33.609]      // end if-block "DoDebugPortStop"
[17:23:33.610]    </control>
[17:23:33.610]  </sequence>
[17:23:33.610]  
[17:34:24.483]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:34:24.483]  
[17:34:24.483]  <debugvars>
[17:34:24.483]    // Pre-defined
[17:34:24.484]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:34:24.484]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:34:24.484]    __dp=0x00000000
[17:34:24.485]    __ap=0x00000000
[17:34:24.485]    __traceout=0x00000000      (Trace Disabled)
[17:34:24.485]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:34:24.485]    __FlashAddr=0x00000000
[17:34:24.486]    __FlashLen=0x00000000
[17:34:24.486]    __FlashArg=0x00000000
[17:34:24.486]    __FlashOp=0x00000000
[17:34:24.487]    __Result=0x00000000
[17:34:24.487]    
[17:34:24.487]    // User-defined
[17:34:24.487]    DbgMCU_CR=0x00000007
[17:34:24.488]    DbgMCU_APB1_Fz=0x00000000
[17:34:24.488]    DbgMCU_APB2_Fz=0x00000000
[17:34:24.488]    DoOptionByteLoading=0x00000000
[17:34:24.488]  </debugvars>
[17:34:24.488]  
[17:34:24.489]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:34:24.489]    <block atomic="false" info="">
[17:34:24.489]      Sequence("CheckID");
[17:34:24.489]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:34:24.489]          <block atomic="false" info="">
[17:34:24.490]            __var pidr1 = 0;
[17:34:24.490]              // -> [pidr1 <= 0x00000000]
[17:34:24.490]            __var pidr2 = 0;
[17:34:24.490]              // -> [pidr2 <= 0x00000000]
[17:34:24.491]            __var jep106id = 0;
[17:34:24.491]              // -> [jep106id <= 0x00000000]
[17:34:24.491]            __var ROMTableBase = 0;
[17:34:24.492]              // -> [ROMTableBase <= 0x00000000]
[17:34:24.492]            __ap = 0;      // AHB-AP
[17:34:24.492]              // -> [__ap <= 0x00000000]
[17:34:24.493]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:34:24.493]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:34:24.494]              // -> [ROMTableBase <= 0xF0000000]
[17:34:24.494]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:34:24.495]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:34:24.496]              // -> [pidr1 <= 0x00000004]
[17:34:24.496]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:34:24.497]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:34:24.497]              // -> [pidr2 <= 0x0000000A]
[17:34:24.497]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:34:24.498]              // -> [jep106id <= 0x00000020]
[17:34:24.498]          </block>
[17:34:24.498]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:34:24.499]            // if-block "jep106id != 0x20"
[17:34:24.499]              // =>  FALSE
[17:34:24.499]            // skip if-block "jep106id != 0x20"
[17:34:24.499]          </control>
[17:34:24.499]        </sequence>
[17:34:24.500]    </block>
[17:34:24.500]  </sequence>
[17:34:24.500]  
[17:34:24.512]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:34:24.512]  
[17:34:24.513]  <debugvars>
[17:34:24.513]    // Pre-defined
[17:34:24.513]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:34:24.514]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:34:24.514]    __dp=0x00000000
[17:34:24.514]    __ap=0x00000000
[17:34:24.515]    __traceout=0x00000000      (Trace Disabled)
[17:34:24.515]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:34:24.515]    __FlashAddr=0x00000000
[17:34:24.515]    __FlashLen=0x00000000
[17:34:24.516]    __FlashArg=0x00000000
[17:34:24.516]    __FlashOp=0x00000000
[17:34:24.516]    __Result=0x00000000
[17:34:24.517]    
[17:34:24.517]    // User-defined
[17:34:24.517]    DbgMCU_CR=0x00000007
[17:34:24.517]    DbgMCU_APB1_Fz=0x00000000
[17:34:24.517]    DbgMCU_APB2_Fz=0x00000000
[17:34:24.518]    DoOptionByteLoading=0x00000000
[17:34:24.518]  </debugvars>
[17:34:24.518]  
[17:34:24.518]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:34:24.518]    <block atomic="false" info="">
[17:34:24.519]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:34:24.519]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:34:24.520]    </block>
[17:34:24.520]    <block atomic="false" info="DbgMCU registers">
[17:34:24.520]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:34:24.521]        // -> [Read32(0x40021034) => 0x00000200]   (__dp=0x00000000, __ap=0x00000000)
[17:34:24.522]        // -> [Write32(0x40021034, 0x00400200)]   (__dp=0x00000000, __ap=0x00000000)
[17:34:24.522]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:34:24.523]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:34:24.523]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:34:24.524]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:34:24.524]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:34:24.525]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:34:24.525]    </block>
[17:34:24.525]  </sequence>
[17:34:24.526]  
[17:34:42.262]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:34:42.262]  
[17:34:42.262]  <debugvars>
[17:34:42.262]    // Pre-defined
[17:34:42.262]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:34:42.262]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:34:42.262]    __dp=0x00000000
[17:34:42.262]    __ap=0x00000000
[17:34:42.262]    __traceout=0x00000000      (Trace Disabled)
[17:34:42.262]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:34:42.265]    __FlashAddr=0x00000000
[17:34:42.265]    __FlashLen=0x00000000
[17:34:42.265]    __FlashArg=0x00000000
[17:34:42.265]    __FlashOp=0x00000000
[17:34:42.265]    __Result=0x00000000
[17:34:42.265]    
[17:34:42.265]    // User-defined
[17:34:42.265]    DbgMCU_CR=0x00000007
[17:34:42.265]    DbgMCU_APB1_Fz=0x00000000
[17:34:42.265]    DbgMCU_APB2_Fz=0x00000000
[17:34:42.265]    DoOptionByteLoading=0x00000000
[17:34:42.267]  </debugvars>
[17:34:42.267]  
[17:34:42.267]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:34:42.267]    <block atomic="false" info="">
[17:34:42.267]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:34:42.267]        // -> [connectionFlash <= 0x00000001]
[17:34:42.267]      __var FLASH_BASE = 0x40022000 ;
[17:34:42.267]        // -> [FLASH_BASE <= 0x40022000]
[17:34:42.269]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:34:42.269]        // -> [FLASH_CR <= 0x40022004]
[17:34:42.269]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:34:42.269]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:34:42.269]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:34:42.270]        // -> [LOCK_BIT <= 0x00000001]
[17:34:42.270]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:34:42.270]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:34:42.270]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:34:42.270]        // -> [FLASH_KEYR <= 0x4002200C]
[17:34:42.270]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:34:42.270]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:34:42.270]      __var FLASH_KEY2 = 0x02030405 ;
[17:34:42.270]        // -> [FLASH_KEY2 <= 0x02030405]
[17:34:42.270]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:34:42.272]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:34:42.272]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:34:42.272]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:34:42.272]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:34:42.272]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:34:42.272]      __var FLASH_CR_Value = 0 ;
[17:34:42.272]        // -> [FLASH_CR_Value <= 0x00000000]
[17:34:42.272]      __var DoDebugPortStop = 1 ;
[17:34:42.272]        // -> [DoDebugPortStop <= 0x00000001]
[17:34:42.274]      __var DP_CTRL_STAT = 0x4 ;
[17:34:42.274]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:34:42.274]      __var DP_SELECT = 0x8 ;
[17:34:42.274]        // -> [DP_SELECT <= 0x00000008]
[17:34:42.274]    </block>
[17:34:42.274]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:34:42.274]      // if-block "connectionFlash && DoOptionByteLoading"
[17:34:42.274]        // =>  FALSE
[17:34:42.274]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:34:42.276]    </control>
[17:34:42.276]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:34:42.276]      // if-block "DoDebugPortStop"
[17:34:42.276]        // =>  TRUE
[17:34:42.276]      <block atomic="false" info="">
[17:34:42.276]        WriteDP(DP_SELECT, 0x00000000);
[17:34:42.276]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:34:42.276]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:34:42.278]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:34:42.278]      </block>
[17:34:42.278]      // end if-block "DoDebugPortStop"
[17:34:42.278]    </control>
[17:34:42.278]  </sequence>
[17:34:42.278]  
[17:34:44.602]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:34:44.602]  
[17:34:44.602]  <debugvars>
[17:34:44.602]    // Pre-defined
[17:34:44.602]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:34:44.602]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:34:44.602]    __dp=0x00000000
[17:34:44.602]    __ap=0x00000000
[17:34:44.602]    __traceout=0x00000000      (Trace Disabled)
[17:34:44.602]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:34:44.602]    __FlashAddr=0x00000000
[17:34:44.602]    __FlashLen=0x00000000
[17:34:44.602]    __FlashArg=0x00000000
[17:34:44.602]    __FlashOp=0x00000000
[17:34:44.602]    __Result=0x00000000
[17:34:44.602]    
[17:34:44.602]    // User-defined
[17:34:44.602]    DbgMCU_CR=0x00000007
[17:34:44.617]    DbgMCU_APB1_Fz=0x00000000
[17:34:44.617]    DbgMCU_APB2_Fz=0x00000000
[17:34:44.617]    DoOptionByteLoading=0x00000000
[17:34:44.617]  </debugvars>
[17:34:44.617]  
[17:34:44.617]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:34:44.617]    <block atomic="false" info="">
[17:34:44.617]      Sequence("CheckID");
[17:34:44.617]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:34:44.617]          <block atomic="false" info="">
[17:34:44.617]            __var pidr1 = 0;
[17:34:44.617]              // -> [pidr1 <= 0x00000000]
[17:34:44.617]            __var pidr2 = 0;
[17:34:44.617]              // -> [pidr2 <= 0x00000000]
[17:34:44.617]            __var jep106id = 0;
[17:34:44.617]              // -> [jep106id <= 0x00000000]
[17:34:44.617]            __var ROMTableBase = 0;
[17:34:44.617]              // -> [ROMTableBase <= 0x00000000]
[17:34:44.617]            __ap = 0;      // AHB-AP
[17:34:44.617]              // -> [__ap <= 0x00000000]
[17:34:44.617]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:34:44.617]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:34:44.617]              // -> [ROMTableBase <= 0xF0000000]
[17:34:44.617]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:34:44.617]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:34:44.617]              // -> [pidr1 <= 0x00000004]
[17:34:44.617]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:34:44.617]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:34:44.617]              // -> [pidr2 <= 0x0000000A]
[17:34:44.617]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:34:44.617]              // -> [jep106id <= 0x00000020]
[17:34:44.617]          </block>
[17:34:44.617]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:34:44.617]            // if-block "jep106id != 0x20"
[17:34:44.617]              // =>  FALSE
[17:34:44.617]            // skip if-block "jep106id != 0x20"
[17:34:44.617]          </control>
[17:34:44.617]        </sequence>
[17:34:44.617]    </block>
[17:34:44.617]  </sequence>
[17:34:44.617]  
[17:34:44.639]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:34:44.639]  
[17:34:44.639]  <debugvars>
[17:34:44.641]    // Pre-defined
[17:34:44.641]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:34:44.641]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:34:44.641]    __dp=0x00000000
[17:34:44.641]    __ap=0x00000000
[17:34:44.641]    __traceout=0x00000000      (Trace Disabled)
[17:34:44.643]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:34:44.643]    __FlashAddr=0x00000000
[17:34:44.643]    __FlashLen=0x00000000
[17:34:44.643]    __FlashArg=0x00000000
[17:34:44.643]    __FlashOp=0x00000000
[17:34:44.643]    __Result=0x00000000
[17:34:44.643]    
[17:34:44.643]    // User-defined
[17:34:44.643]    DbgMCU_CR=0x00000007
[17:34:44.645]    DbgMCU_APB1_Fz=0x00000000
[17:34:44.645]    DbgMCU_APB2_Fz=0x00000000
[17:34:44.645]    DoOptionByteLoading=0x00000000
[17:34:44.645]  </debugvars>
[17:34:44.645]  
[17:34:44.645]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:34:44.645]    <block atomic="false" info="">
[17:34:44.645]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:34:44.645]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:34:44.648]    </block>
[17:34:44.648]    <block atomic="false" info="DbgMCU registers">
[17:34:44.649]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:34:44.649]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[17:34:44.649]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[17:34:44.649]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:34:44.651]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:34:44.652]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:34:44.652]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:34:44.652]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:34:44.652]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:34:44.652]    </block>
[17:34:44.652]  </sequence>
[17:34:44.652]  
[17:34:52.405]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:34:52.405]  
[17:34:52.405]  <debugvars>
[17:34:52.405]    // Pre-defined
[17:34:52.405]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:34:52.405]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:34:52.405]    __dp=0x00000000
[17:34:52.406]    __ap=0x00000000
[17:34:52.406]    __traceout=0x00000000      (Trace Disabled)
[17:34:52.406]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:34:52.406]    __FlashAddr=0x00000000
[17:34:52.406]    __FlashLen=0x00000000
[17:34:52.406]    __FlashArg=0x00000000
[17:34:52.406]    __FlashOp=0x00000000
[17:34:52.406]    __Result=0x00000000
[17:34:52.406]    
[17:34:52.406]    // User-defined
[17:34:52.406]    DbgMCU_CR=0x00000007
[17:34:52.408]    DbgMCU_APB1_Fz=0x00000000
[17:34:52.408]    DbgMCU_APB2_Fz=0x00000000
[17:34:52.408]    DoOptionByteLoading=0x00000000
[17:34:52.408]  </debugvars>
[17:34:52.408]  
[17:34:52.408]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:34:52.408]    <block atomic="false" info="">
[17:34:52.408]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:34:52.408]        // -> [connectionFlash <= 0x00000001]
[17:34:52.408]      __var FLASH_BASE = 0x40022000 ;
[17:34:52.408]        // -> [FLASH_BASE <= 0x40022000]
[17:34:52.408]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:34:52.408]        // -> [FLASH_CR <= 0x40022004]
[17:34:52.408]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:34:52.408]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:34:52.408]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:34:52.408]        // -> [LOCK_BIT <= 0x00000001]
[17:34:52.412]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:34:52.412]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:34:52.412]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:34:52.413]        // -> [FLASH_KEYR <= 0x4002200C]
[17:34:52.413]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:34:52.413]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:34:52.413]      __var FLASH_KEY2 = 0x02030405 ;
[17:34:52.413]        // -> [FLASH_KEY2 <= 0x02030405]
[17:34:52.413]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:34:52.413]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:34:52.413]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:34:52.413]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:34:52.413]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:34:52.415]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:34:52.415]      __var FLASH_CR_Value = 0 ;
[17:34:52.415]        // -> [FLASH_CR_Value <= 0x00000000]
[17:34:52.415]      __var DoDebugPortStop = 1 ;
[17:34:52.415]        // -> [DoDebugPortStop <= 0x00000001]
[17:34:52.415]      __var DP_CTRL_STAT = 0x4 ;
[17:34:52.415]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:34:52.415]      __var DP_SELECT = 0x8 ;
[17:34:52.415]        // -> [DP_SELECT <= 0x00000008]
[17:34:52.417]    </block>
[17:34:52.417]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:34:52.417]      // if-block "connectionFlash && DoOptionByteLoading"
[17:34:52.417]        // =>  FALSE
[17:34:52.417]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:34:52.417]    </control>
[17:34:52.417]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:34:52.417]      // if-block "DoDebugPortStop"
[17:34:52.417]        // =>  TRUE
[17:34:52.417]      <block atomic="false" info="">
[17:34:52.417]        WriteDP(DP_SELECT, 0x00000000);
[17:34:52.417]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:34:52.419]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:34:52.419]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:34:52.419]      </block>
[17:34:52.419]      // end if-block "DoDebugPortStop"
[17:34:52.419]    </control>
[17:34:52.419]  </sequence>
[17:34:52.419]  
[17:36:45.414]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:36:45.414]  
[17:36:45.415]  <debugvars>
[17:36:45.415]    // Pre-defined
[17:36:45.415]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:36:45.415]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:36:45.415]    __dp=0x00000000
[17:36:45.415]    __ap=0x00000000
[17:36:45.415]    __traceout=0x00000000      (Trace Disabled)
[17:36:45.415]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:36:45.415]    __FlashAddr=0x00000000
[17:36:45.415]    __FlashLen=0x00000000
[17:36:45.415]    __FlashArg=0x00000000
[17:36:45.415]    __FlashOp=0x00000000
[17:36:45.415]    __Result=0x00000000
[17:36:45.415]    
[17:36:45.415]    // User-defined
[17:36:45.415]    DbgMCU_CR=0x00000007
[17:36:45.415]    DbgMCU_APB1_Fz=0x00000000
[17:36:45.415]    DbgMCU_APB2_Fz=0x00000000
[17:36:45.420]    DoOptionByteLoading=0x00000000
[17:36:45.421]  </debugvars>
[17:36:45.421]  
[17:36:45.421]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:36:45.421]    <block atomic="false" info="">
[17:36:45.421]      Sequence("CheckID");
[17:36:45.423]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:36:45.423]          <block atomic="false" info="">
[17:36:45.423]            __var pidr1 = 0;
[17:36:45.423]              // -> [pidr1 <= 0x00000000]
[17:36:45.423]            __var pidr2 = 0;
[17:36:45.423]              // -> [pidr2 <= 0x00000000]
[17:36:45.425]            __var jep106id = 0;
[17:36:45.425]              // -> [jep106id <= 0x00000000]
[17:36:45.425]            __var ROMTableBase = 0;
[17:36:45.426]              // -> [ROMTableBase <= 0x00000000]
[17:36:45.426]            __ap = 0;      // AHB-AP
[17:36:45.426]              // -> [__ap <= 0x00000000]
[17:36:45.426]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:36:45.426]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:36:45.426]              // -> [ROMTableBase <= 0xF0000000]
[17:36:45.426]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:36:45.426]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:36:45.426]              // -> [pidr1 <= 0x00000004]
[17:36:45.426]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:36:45.426]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:36:45.426]              // -> [pidr2 <= 0x0000000A]
[17:36:45.426]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:36:45.426]              // -> [jep106id <= 0x00000020]
[17:36:45.426]          </block>
[17:36:45.426]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:36:45.426]            // if-block "jep106id != 0x20"
[17:36:45.426]              // =>  FALSE
[17:36:45.426]            // skip if-block "jep106id != 0x20"
[17:36:45.426]          </control>
[17:36:45.426]        </sequence>
[17:36:45.426]    </block>
[17:36:45.426]  </sequence>
[17:36:45.426]  
[17:36:45.441]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:36:45.441]  
[17:36:45.478]  <debugvars>
[17:36:45.478]    // Pre-defined
[17:36:45.478]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:36:45.478]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:36:45.478]    __dp=0x00000000
[17:36:45.478]    __ap=0x00000000
[17:36:45.478]    __traceout=0x00000000      (Trace Disabled)
[17:36:45.478]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:36:45.478]    __FlashAddr=0x00000000
[17:36:45.478]    __FlashLen=0x00000000
[17:36:45.478]    __FlashArg=0x00000000
[17:36:45.478]    __FlashOp=0x00000000
[17:36:45.478]    __Result=0x00000000
[17:36:45.478]    
[17:36:45.478]    // User-defined
[17:36:45.478]    DbgMCU_CR=0x00000007
[17:36:45.478]    DbgMCU_APB1_Fz=0x00000000
[17:36:45.478]    DbgMCU_APB2_Fz=0x00000000
[17:36:45.478]    DoOptionByteLoading=0x00000000
[17:36:45.478]  </debugvars>
[17:36:45.478]  
[17:36:45.484]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:36:45.484]    <block atomic="false" info="">
[17:36:45.484]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:36:45.484]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:36:45.484]    </block>
[17:36:45.486]    <block atomic="false" info="DbgMCU registers">
[17:36:45.486]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:36:45.486]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[17:36:45.486]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[17:36:45.486]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:36:45.486]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:36:45.486]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:36:45.486]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:36:45.486]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:36:45.486]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:36:45.486]    </block>
[17:36:45.486]  </sequence>
[17:36:45.486]  
[17:36:53.334]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:36:53.334]  
[17:36:53.334]  <debugvars>
[17:36:53.335]    // Pre-defined
[17:36:53.335]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:36:53.335]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:36:53.335]    __dp=0x00000000
[17:36:53.336]    __ap=0x00000000
[17:36:53.336]    __traceout=0x00000000      (Trace Disabled)
[17:36:53.336]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:36:53.336]    __FlashAddr=0x00000000
[17:36:53.336]    __FlashLen=0x00000000
[17:36:53.337]    __FlashArg=0x00000000
[17:36:53.337]    __FlashOp=0x00000000
[17:36:53.337]    __Result=0x00000000
[17:36:53.337]    
[17:36:53.337]    // User-defined
[17:36:53.337]    DbgMCU_CR=0x00000007
[17:36:53.337]    DbgMCU_APB1_Fz=0x00000000
[17:36:53.338]    DbgMCU_APB2_Fz=0x00000000
[17:36:53.338]    DoOptionByteLoading=0x00000000
[17:36:53.338]  </debugvars>
[17:36:53.338]  
[17:36:53.338]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:36:53.339]    <block atomic="false" info="">
[17:36:53.339]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:36:53.339]        // -> [connectionFlash <= 0x00000001]
[17:36:53.339]      __var FLASH_BASE = 0x40022000 ;
[17:36:53.339]        // -> [FLASH_BASE <= 0x40022000]
[17:36:53.340]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:36:53.340]        // -> [FLASH_CR <= 0x40022004]
[17:36:53.340]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:36:53.340]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:36:53.340]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:36:53.341]        // -> [LOCK_BIT <= 0x00000001]
[17:36:53.341]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:36:53.342]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:36:53.342]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:36:53.342]        // -> [FLASH_KEYR <= 0x4002200C]
[17:36:53.342]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:36:53.342]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:36:53.343]      __var FLASH_KEY2 = 0x02030405 ;
[17:36:53.343]        // -> [FLASH_KEY2 <= 0x02030405]
[17:36:53.343]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:36:53.343]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:36:53.343]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:36:53.344]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:36:53.344]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:36:53.344]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:36:53.344]      __var FLASH_CR_Value = 0 ;
[17:36:53.344]        // -> [FLASH_CR_Value <= 0x00000000]
[17:36:53.344]      __var DoDebugPortStop = 1 ;
[17:36:53.345]        // -> [DoDebugPortStop <= 0x00000001]
[17:36:53.345]      __var DP_CTRL_STAT = 0x4 ;
[17:36:53.345]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:36:53.345]      __var DP_SELECT = 0x8 ;
[17:36:53.345]        // -> [DP_SELECT <= 0x00000008]
[17:36:53.346]    </block>
[17:36:53.346]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:36:53.346]      // if-block "connectionFlash && DoOptionByteLoading"
[17:36:53.346]        // =>  FALSE
[17:36:53.346]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:36:53.346]    </control>
[17:36:53.347]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:36:53.347]      // if-block "DoDebugPortStop"
[17:36:53.347]        // =>  TRUE
[17:36:53.347]      <block atomic="false" info="">
[17:36:53.347]        WriteDP(DP_SELECT, 0x00000000);
[17:36:53.348]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:36:53.348]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:36:53.348]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:36:53.349]      </block>
[17:36:53.349]      // end if-block "DoDebugPortStop"
[17:36:53.349]    </control>
[17:36:53.349]  </sequence>
[17:36:53.363]  
[17:39:11.003]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:39:11.003]  
[17:39:11.003]  <debugvars>
[17:39:11.004]    // Pre-defined
[17:39:11.004]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:39:11.004]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:39:11.005]    __dp=0x00000000
[17:39:11.005]    __ap=0x00000000
[17:39:11.005]    __traceout=0x00000000      (Trace Disabled)
[17:39:11.005]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:39:11.005]    __FlashAddr=0x00000000
[17:39:11.005]    __FlashLen=0x00000000
[17:39:11.005]    __FlashArg=0x00000000
[17:39:11.005]    __FlashOp=0x00000000
[17:39:11.005]    __Result=0x00000000
[17:39:11.005]    
[17:39:11.005]    // User-defined
[17:39:11.007]    DbgMCU_CR=0x00000007
[17:39:11.007]    DbgMCU_APB1_Fz=0x00000000
[17:39:11.008]    DbgMCU_APB2_Fz=0x00000000
[17:39:11.008]    DoOptionByteLoading=0x00000000
[17:39:11.008]  </debugvars>
[17:39:11.008]  
[17:39:11.008]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:39:11.009]    <block atomic="false" info="">
[17:39:11.009]      Sequence("CheckID");
[17:39:11.009]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:39:11.010]          <block atomic="false" info="">
[17:39:11.010]            __var pidr1 = 0;
[17:39:11.010]              // -> [pidr1 <= 0x00000000]
[17:39:11.010]            __var pidr2 = 0;
[17:39:11.010]              // -> [pidr2 <= 0x00000000]
[17:39:11.010]            __var jep106id = 0;
[17:39:11.011]              // -> [jep106id <= 0x00000000]
[17:39:11.011]            __var ROMTableBase = 0;
[17:39:11.011]              // -> [ROMTableBase <= 0x00000000]
[17:39:11.011]            __ap = 0;      // AHB-AP
[17:39:11.012]              // -> [__ap <= 0x00000000]
[17:39:11.012]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:39:11.013]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:39:11.014]              // -> [ROMTableBase <= 0xF0000000]
[17:39:11.014]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:39:11.015]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:39:11.015]              // -> [pidr1 <= 0x00000004]
[17:39:11.015]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:39:11.016]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:39:11.017]              // -> [pidr2 <= 0x0000000A]
[17:39:11.017]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:39:11.017]              // -> [jep106id <= 0x00000020]
[17:39:11.018]          </block>
[17:39:11.018]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:39:11.018]            // if-block "jep106id != 0x20"
[17:39:11.018]              // =>  FALSE
[17:39:11.019]            // skip if-block "jep106id != 0x20"
[17:39:11.019]          </control>
[17:39:11.020]        </sequence>
[17:39:11.020]    </block>
[17:39:11.020]  </sequence>
[17:39:11.020]  
[17:39:11.032]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:39:11.032]  
[17:39:11.042]  <debugvars>
[17:39:11.042]    // Pre-defined
[17:39:11.043]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:39:11.043]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:39:11.044]    __dp=0x00000000
[17:39:11.044]    __ap=0x00000000
[17:39:11.045]    __traceout=0x00000000      (Trace Disabled)
[17:39:11.045]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:39:11.046]    __FlashAddr=0x00000000
[17:39:11.046]    __FlashLen=0x00000000
[17:39:11.046]    __FlashArg=0x00000000
[17:39:11.047]    __FlashOp=0x00000000
[17:39:11.047]    __Result=0x00000000
[17:39:11.047]    
[17:39:11.047]    // User-defined
[17:39:11.047]    DbgMCU_CR=0x00000007
[17:39:11.048]    DbgMCU_APB1_Fz=0x00000000
[17:39:11.048]    DbgMCU_APB2_Fz=0x00000000
[17:39:11.048]    DoOptionByteLoading=0x00000000
[17:39:11.049]  </debugvars>
[17:39:11.049]  
[17:39:11.049]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:39:11.049]    <block atomic="false" info="">
[17:39:11.050]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:39:11.050]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:39:11.051]    </block>
[17:39:11.051]    <block atomic="false" info="DbgMCU registers">
[17:39:11.051]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:39:11.052]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[17:39:11.053]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[17:39:11.054]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:39:11.055]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:39:11.055]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:39:11.056]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:39:11.056]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:39:11.057]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:39:11.057]    </block>
[17:39:11.058]  </sequence>
[17:39:11.058]  
[17:39:18.897]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:39:18.897]  
[17:39:18.898]  <debugvars>
[17:39:18.899]    // Pre-defined
[17:39:18.899]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:39:18.899]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:39:18.899]    __dp=0x00000000
[17:39:18.899]    __ap=0x00000000
[17:39:18.899]    __traceout=0x00000000      (Trace Disabled)
[17:39:18.899]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:39:18.899]    __FlashAddr=0x00000000
[17:39:18.899]    __FlashLen=0x00000000
[17:39:18.899]    __FlashArg=0x00000000
[17:39:18.899]    __FlashOp=0x00000000
[17:39:18.899]    __Result=0x00000000
[17:39:18.899]    
[17:39:18.899]    // User-defined
[17:39:18.899]    DbgMCU_CR=0x00000007
[17:39:18.899]    DbgMCU_APB1_Fz=0x00000000
[17:39:18.899]    DbgMCU_APB2_Fz=0x00000000
[17:39:18.899]    DoOptionByteLoading=0x00000000
[17:39:18.899]  </debugvars>
[17:39:18.903]  
[17:39:18.903]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:39:18.903]    <block atomic="false" info="">
[17:39:18.903]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:39:18.904]        // -> [connectionFlash <= 0x00000001]
[17:39:18.904]      __var FLASH_BASE = 0x40022000 ;
[17:39:18.904]        // -> [FLASH_BASE <= 0x40022000]
[17:39:18.905]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:39:18.905]        // -> [FLASH_CR <= 0x40022004]
[17:39:18.905]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:39:18.905]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:39:18.905]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:39:18.906]        // -> [LOCK_BIT <= 0x00000001]
[17:39:18.906]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:39:18.906]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:39:18.906]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:39:18.906]        // -> [FLASH_KEYR <= 0x4002200C]
[17:39:18.906]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:39:18.906]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:39:18.907]      __var FLASH_KEY2 = 0x02030405 ;
[17:39:18.907]        // -> [FLASH_KEY2 <= 0x02030405]
[17:39:18.907]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:39:18.907]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:39:18.907]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:39:18.907]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:39:18.908]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:39:18.908]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:39:18.908]      __var FLASH_CR_Value = 0 ;
[17:39:18.909]        // -> [FLASH_CR_Value <= 0x00000000]
[17:39:18.909]      __var DoDebugPortStop = 1 ;
[17:39:18.909]        // -> [DoDebugPortStop <= 0x00000001]
[17:39:18.909]      __var DP_CTRL_STAT = 0x4 ;
[17:39:18.909]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:39:18.910]      __var DP_SELECT = 0x8 ;
[17:39:18.910]        // -> [DP_SELECT <= 0x00000008]
[17:39:18.910]    </block>
[17:39:18.910]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:39:18.910]      // if-block "connectionFlash && DoOptionByteLoading"
[17:39:18.911]        // =>  FALSE
[17:39:18.911]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:39:18.911]    </control>
[17:39:18.911]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:39:18.911]      // if-block "DoDebugPortStop"
[17:39:18.911]        // =>  TRUE
[17:39:18.912]      <block atomic="false" info="">
[17:39:18.912]        WriteDP(DP_SELECT, 0x00000000);
[17:39:18.913]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:39:18.913]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:39:18.913]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:39:18.913]      </block>
[17:39:18.914]      // end if-block "DoDebugPortStop"
[17:39:18.914]    </control>
[17:39:18.914]  </sequence>
[17:39:18.914]  
[17:49:52.140]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:49:52.140]  
[17:49:52.140]  <debugvars>
[17:49:52.156]    // Pre-defined
[17:49:52.156]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:49:52.156]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:49:52.156]    __dp=0x00000000
[17:49:52.156]    __ap=0x00000000
[17:49:52.156]    __traceout=0x00000000      (Trace Disabled)
[17:49:52.156]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:49:52.156]    __FlashAddr=0x00000000
[17:49:52.156]    __FlashLen=0x00000000
[17:49:52.156]    __FlashArg=0x00000000
[17:49:52.156]    __FlashOp=0x00000000
[17:49:52.160]    __Result=0x00000000
[17:49:52.160]    
[17:49:52.160]    // User-defined
[17:49:52.160]    DbgMCU_CR=0x00000007
[17:49:52.160]    DbgMCU_APB1_Fz=0x00000000
[17:49:52.160]    DbgMCU_APB2_Fz=0x00000000
[17:49:52.160]    DoOptionByteLoading=0x00000000
[17:49:52.160]  </debugvars>
[17:49:52.161]  
[17:49:52.161]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:49:52.161]    <block atomic="false" info="">
[17:49:52.162]      Sequence("CheckID");
[17:49:52.163]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:49:52.163]          <block atomic="false" info="">
[17:49:52.163]            __var pidr1 = 0;
[17:49:52.163]              // -> [pidr1 <= 0x00000000]
[17:49:52.163]            __var pidr2 = 0;
[17:49:52.163]              // -> [pidr2 <= 0x00000000]
[17:49:52.163]            __var jep106id = 0;
[17:49:52.163]              // -> [jep106id <= 0x00000000]
[17:49:52.163]            __var ROMTableBase = 0;
[17:49:52.163]              // -> [ROMTableBase <= 0x00000000]
[17:49:52.163]            __ap = 0;      // AHB-AP
[17:49:52.163]              // -> [__ap <= 0x00000000]
[17:49:52.163]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:49:52.163]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:49:52.163]              // -> [ROMTableBase <= 0xF0000000]
[17:49:52.163]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:49:52.163]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:49:52.163]              // -> [pidr1 <= 0x00000004]
[17:49:52.163]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:49:52.163]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:49:52.163]              // -> [pidr2 <= 0x0000000A]
[17:49:52.163]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:49:52.163]              // -> [jep106id <= 0x00000020]
[17:49:52.163]          </block>
[17:49:52.163]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:49:52.163]            // if-block "jep106id != 0x20"
[17:49:52.163]              // =>  FALSE
[17:49:52.163]            // skip if-block "jep106id != 0x20"
[17:49:52.163]          </control>
[17:49:52.171]        </sequence>
[17:49:52.171]    </block>
[17:49:52.171]  </sequence>
[17:49:52.171]  
[17:49:52.181]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:49:52.181]  
[17:49:52.186]  <debugvars>
[17:49:52.189]    // Pre-defined
[17:49:52.189]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:49:52.189]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:49:52.190]    __dp=0x00000000
[17:49:52.190]    __ap=0x00000000
[17:49:52.191]    __traceout=0x00000000      (Trace Disabled)
[17:49:52.191]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:49:52.191]    __FlashAddr=0x00000000
[17:49:52.191]    __FlashLen=0x00000000
[17:49:52.191]    __FlashArg=0x00000000
[17:49:52.191]    __FlashOp=0x00000000
[17:49:52.191]    __Result=0x00000000
[17:49:52.191]    
[17:49:52.191]    // User-defined
[17:49:52.191]    DbgMCU_CR=0x00000007
[17:49:52.191]    DbgMCU_APB1_Fz=0x00000000
[17:49:52.191]    DbgMCU_APB2_Fz=0x00000000
[17:49:52.191]    DoOptionByteLoading=0x00000000
[17:49:52.191]  </debugvars>
[17:49:52.191]  
[17:49:52.191]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:49:52.191]    <block atomic="false" info="">
[17:49:52.195]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:49:52.196]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:49:52.196]    </block>
[17:49:52.196]    <block atomic="false" info="DbgMCU registers">
[17:49:52.197]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:49:52.198]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[17:49:52.198]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[17:49:52.199]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:49:52.200]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:49:52.200]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:49:52.201]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:49:52.201]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:49:52.202]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:49:52.202]    </block>
[17:49:52.203]  </sequence>
[17:49:52.203]  
[17:50:00.532]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:50:00.532]  
[17:50:00.532]  <debugvars>
[17:50:00.532]    // Pre-defined
[17:50:00.532]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:50:00.532]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:50:00.533]    __dp=0x00000000
[17:50:00.533]    __ap=0x00000000
[17:50:00.534]    __traceout=0x00000000      (Trace Disabled)
[17:50:00.534]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:50:00.534]    __FlashAddr=0x00000000
[17:50:00.534]    __FlashLen=0x00000000
[17:50:00.534]    __FlashArg=0x00000000
[17:50:00.534]    __FlashOp=0x00000000
[17:50:00.535]    __Result=0x00000000
[17:50:00.535]    
[17:50:00.535]    // User-defined
[17:50:00.535]    DbgMCU_CR=0x00000007
[17:50:00.535]    DbgMCU_APB1_Fz=0x00000000
[17:50:00.535]    DbgMCU_APB2_Fz=0x00000000
[17:50:00.536]    DoOptionByteLoading=0x00000000
[17:50:00.536]  </debugvars>
[17:50:00.536]  
[17:50:00.536]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:50:00.536]    <block atomic="false" info="">
[17:50:00.536]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:50:00.536]        // -> [connectionFlash <= 0x00000001]
[17:50:00.537]      __var FLASH_BASE = 0x40022000 ;
[17:50:00.537]        // -> [FLASH_BASE <= 0x40022000]
[17:50:00.537]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:50:00.538]        // -> [FLASH_CR <= 0x40022004]
[17:50:00.538]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:50:00.538]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:50:00.538]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:50:00.538]        // -> [LOCK_BIT <= 0x00000001]
[17:50:00.539]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:50:00.539]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:50:00.539]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:50:00.539]        // -> [FLASH_KEYR <= 0x4002200C]
[17:50:00.539]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:50:00.539]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:50:00.540]      __var FLASH_KEY2 = 0x02030405 ;
[17:50:00.540]        // -> [FLASH_KEY2 <= 0x02030405]
[17:50:00.540]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:50:00.541]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:50:00.541]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:50:00.541]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:50:00.541]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:50:00.541]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:50:00.541]      __var FLASH_CR_Value = 0 ;
[17:50:00.541]        // -> [FLASH_CR_Value <= 0x00000000]
[17:50:00.541]      __var DoDebugPortStop = 1 ;
[17:50:00.542]        // -> [DoDebugPortStop <= 0x00000001]
[17:50:00.542]      __var DP_CTRL_STAT = 0x4 ;
[17:50:00.542]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:50:00.543]      __var DP_SELECT = 0x8 ;
[17:50:00.543]        // -> [DP_SELECT <= 0x00000008]
[17:50:00.543]    </block>
[17:50:00.543]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:50:00.543]      // if-block "connectionFlash && DoOptionByteLoading"
[17:50:00.544]        // =>  FALSE
[17:50:00.544]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:50:00.544]    </control>
[17:50:00.545]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:50:00.545]      // if-block "DoDebugPortStop"
[17:50:00.545]        // =>  TRUE
[17:50:00.545]      <block atomic="false" info="">
[17:50:00.545]        WriteDP(DP_SELECT, 0x00000000);
[17:50:00.546]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:50:00.546]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:50:00.546]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:50:00.547]      </block>
[17:50:00.547]      // end if-block "DoDebugPortStop"
[17:50:00.547]    </control>
[17:50:00.547]  </sequence>
[17:50:00.547]  
[17:53:22.508]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:53:22.508]  
[17:53:22.508]  <debugvars>
[17:53:22.508]    // Pre-defined
[17:53:22.509]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:53:22.509]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:53:22.509]    __dp=0x00000000
[17:53:22.509]    __ap=0x00000000
[17:53:22.510]    __traceout=0x00000000      (Trace Disabled)
[17:53:22.510]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:53:22.510]    __FlashAddr=0x00000000
[17:53:22.510]    __FlashLen=0x00000000
[17:53:22.510]    __FlashArg=0x00000000
[17:53:22.511]    __FlashOp=0x00000000
[17:53:22.511]    __Result=0x00000000
[17:53:22.511]    
[17:53:22.511]    // User-defined
[17:53:22.511]    DbgMCU_CR=0x00000007
[17:53:22.511]    DbgMCU_APB1_Fz=0x00000000
[17:53:22.511]    DbgMCU_APB2_Fz=0x00000000
[17:53:22.511]    DoOptionByteLoading=0x00000000
[17:53:22.511]  </debugvars>
[17:53:22.511]  
[17:53:22.511]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:53:22.513]    <block atomic="false" info="">
[17:53:22.513]      Sequence("CheckID");
[17:53:22.513]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:53:22.513]          <block atomic="false" info="">
[17:53:22.514]            __var pidr1 = 0;
[17:53:22.514]              // -> [pidr1 <= 0x00000000]
[17:53:22.514]            __var pidr2 = 0;
[17:53:22.514]              // -> [pidr2 <= 0x00000000]
[17:53:22.515]            __var jep106id = 0;
[17:53:22.515]              // -> [jep106id <= 0x00000000]
[17:53:22.515]            __var ROMTableBase = 0;
[17:53:22.515]              // -> [ROMTableBase <= 0x00000000]
[17:53:22.516]            __ap = 0;      // AHB-AP
[17:53:22.516]              // -> [__ap <= 0x00000000]
[17:53:22.516]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:53:22.517]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:53:22.517]              // -> [ROMTableBase <= 0xF0000000]
[17:53:22.517]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:53:22.518]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:53:22.518]              // -> [pidr1 <= 0x00000004]
[17:53:22.519]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:53:22.519]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:53:22.519]              // -> [pidr2 <= 0x0000000A]
[17:53:22.520]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:53:22.520]              // -> [jep106id <= 0x00000020]
[17:53:22.521]          </block>
[17:53:22.521]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:53:22.521]            // if-block "jep106id != 0x20"
[17:53:22.521]              // =>  FALSE
[17:53:22.522]            // skip if-block "jep106id != 0x20"
[17:53:22.522]          </control>
[17:53:22.522]        </sequence>
[17:53:22.523]    </block>
[17:53:22.523]  </sequence>
[17:53:22.523]  
[17:53:22.535]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:53:22.535]  
[17:53:22.535]  <debugvars>
[17:53:22.535]    // Pre-defined
[17:53:22.535]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:53:22.535]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:53:22.535]    __dp=0x00000000
[17:53:22.535]    __ap=0x00000000
[17:53:22.536]    __traceout=0x00000000      (Trace Disabled)
[17:53:22.536]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:53:22.536]    __FlashAddr=0x00000000
[17:53:22.537]    __FlashLen=0x00000000
[17:53:22.537]    __FlashArg=0x00000000
[17:53:22.537]    __FlashOp=0x00000000
[17:53:22.537]    __Result=0x00000000
[17:53:22.537]    
[17:53:22.537]    // User-defined
[17:53:22.538]    DbgMCU_CR=0x00000007
[17:53:22.538]    DbgMCU_APB1_Fz=0x00000000
[17:53:22.538]    DbgMCU_APB2_Fz=0x00000000
[17:53:22.538]    DoOptionByteLoading=0x00000000
[17:53:22.538]  </debugvars>
[17:53:22.539]  
[17:53:22.539]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:53:22.539]    <block atomic="false" info="">
[17:53:22.539]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:53:22.541]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:53:22.541]    </block>
[17:53:22.541]    <block atomic="false" info="DbgMCU registers">
[17:53:22.541]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:53:22.542]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[17:53:22.543]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[17:53:22.543]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:53:22.544]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:53:22.544]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:53:22.544]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:53:22.545]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:53:22.545]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:53:22.546]    </block>
[17:53:22.546]  </sequence>
[17:53:22.546]  
[17:53:30.555]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:53:30.555]  
[17:53:30.555]  <debugvars>
[17:53:30.555]    // Pre-defined
[17:53:30.555]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:53:30.555]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:53:30.557]    __dp=0x00000000
[17:53:30.557]    __ap=0x00000000
[17:53:30.559]    __traceout=0x00000000      (Trace Disabled)
[17:53:30.559]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:53:30.559]    __FlashAddr=0x00000000
[17:53:30.561]    __FlashLen=0x00000000
[17:53:30.563]    __FlashArg=0x00000000
[17:53:30.563]    __FlashOp=0x00000000
[17:53:30.565]    __Result=0x00000000
[17:53:30.565]    
[17:53:30.565]    // User-defined
[17:53:30.565]    DbgMCU_CR=0x00000007
[17:53:30.568]    DbgMCU_APB1_Fz=0x00000000
[17:53:30.570]    DbgMCU_APB2_Fz=0x00000000
[17:53:30.570]    DoOptionByteLoading=0x00000000
[17:53:30.570]  </debugvars>
[17:53:30.572]  
[17:53:30.572]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:53:30.572]    <block atomic="false" info="">
[17:53:30.572]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:53:30.574]        // -> [connectionFlash <= 0x00000001]
[17:53:30.574]      __var FLASH_BASE = 0x40022000 ;
[17:53:30.574]        // -> [FLASH_BASE <= 0x40022000]
[17:53:30.574]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:53:30.574]        // -> [FLASH_CR <= 0x40022004]
[17:53:30.574]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:53:30.574]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:53:30.576]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:53:30.576]        // -> [LOCK_BIT <= 0x00000001]
[17:53:30.576]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:53:30.576]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:53:30.576]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:53:30.576]        // -> [FLASH_KEYR <= 0x4002200C]
[17:53:30.578]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:53:30.578]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:53:30.578]      __var FLASH_KEY2 = 0x02030405 ;
[17:53:30.578]        // -> [FLASH_KEY2 <= 0x02030405]
[17:53:30.578]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:53:30.578]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:53:30.578]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:53:30.580]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:53:30.580]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:53:30.580]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:53:30.580]      __var FLASH_CR_Value = 0 ;
[17:53:30.580]        // -> [FLASH_CR_Value <= 0x00000000]
[17:53:30.580]      __var DoDebugPortStop = 1 ;
[17:53:30.580]        // -> [DoDebugPortStop <= 0x00000001]
[17:53:30.582]      __var DP_CTRL_STAT = 0x4 ;
[17:53:30.582]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:53:30.582]      __var DP_SELECT = 0x8 ;
[17:53:30.582]        // -> [DP_SELECT <= 0x00000008]
[17:53:30.583]    </block>
[17:53:30.583]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:53:30.583]      // if-block "connectionFlash && DoOptionByteLoading"
[17:53:30.591]        // =>  FALSE
[17:53:30.591]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:53:30.594]    </control>
[17:53:30.594]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:53:30.594]      // if-block "DoDebugPortStop"
[17:53:30.594]        // =>  TRUE
[17:53:30.594]      <block atomic="false" info="">
[17:53:30.594]        WriteDP(DP_SELECT, 0x00000000);
[17:53:30.594]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:53:30.596]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:53:30.596]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:53:30.596]      </block>
[17:53:30.596]      // end if-block "DoDebugPortStop"
[17:53:30.596]    </control>
[17:53:30.596]  </sequence>
[17:53:30.596]  
