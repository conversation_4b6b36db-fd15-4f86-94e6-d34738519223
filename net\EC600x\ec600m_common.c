/*
 * @file    ec600m_common.c
 * @brief   ec600m common function
 *
 * @note 移远公共部分函数接口定义
 *
 */
#include "ec600m_common.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

static NET_ERR send_cmd(char *cmd,char *ack,uint32_t timeout)
{
  serial_port.clean();
  serial_port.send_cmd(cmd,strlen(cmd));
  timeout = timeout / 100;
  while(timeout)
  {
    if(timeout)timeout--;
    rt_thread_mdelay(100);
    if(serial_port.count && !serial_port.byte_timeout)
    {
      if(strstr((char *)serial_port.recv,ack) != NULL)
      {
        break;
      }
    }
  }
  if(timeout == 0)rt_thread_mdelay(200);//指令间隔时长，或者加互斥量
  return timeout ? NET_ERROR_NONE : NET_ERROR_TIMEOUT;
}

static NET_ERR watting(char *ack,uint32_t timeout)
{
  timeout /= 100;
  while(timeout)
  { 
    if(timeout)timeout--;
    rt_thread_mdelay(100);
    if(serial_port.count && !serial_port.byte_timeout)
    {
      if(strstr((char *)serial_port.recv,ack) != NULL)
      {
        break;
      }
    }
  }
  if(timeout == 0)rt_thread_mdelay(200);//指令间隔时长，或者加互斥量
  return timeout ? NET_ERROR_NONE : NET_ERROR_TIMEOUT;
}

static NET_ERR send_data(uint8_t *data,uint16_t len)
{
  serial_port.send_data(data,len);
	return NET_ERROR_NONE;
}

static NET_ERR sync_time(uint16_t *year,uint8_t *month,uint8_t *day,uint8_t *hour,uint8_t *minute,uint8_t *second )
{
  if(send_cmd("AT+CREG?","OK",500) != NET_ERROR_NONE)
    return NET_ERROR_TIMEOUT;
  char *temp = strchr((char *)serial_port.recv,',');
  if(temp ==RT_NULL)
    return NET_ERROR_FAIL;
  uint8_t status = atoi(temp+1);
  if((status != 1) && (status != 5))//1he 5标识已经注册到网络，同步时间只有注册网络后才能进行
    return NET_ERROR_FAIL;
  /*+QLTS: "2019/01/13,11:41:23+32,0"*/
  if(send_cmd("AT+QLTS=2","OK",500) != NET_ERROR_NONE)
    return NET_ERROR_TIMEOUT;
  temp = strchr((char *)serial_port.recv,'"');
  if(temp == RT_NULL)
    return NET_ERROR_FAIL;
  temp++;
  *year = atoi(temp);

  temp = strchr(temp, '/');
  if(temp == RT_NULL)
    return NET_ERROR_FAIL;
  temp++;
  *month = atoi(temp);

  temp = strchr(temp, '/');
  if(temp == RT_NULL)
    return NET_ERROR_FAIL;
  temp++;
  *day = atoi(temp);

  temp = strchr(temp, ',');
  if(temp == RT_NULL)
    return NET_ERROR_FAIL;
  temp++;
  *hour = atoi(temp);

  temp = strchr(temp, ':');
  if(temp == RT_NULL)
    return NET_ERROR_FAIL;
  temp++;
  *minute = atoi(temp);

  temp = strchr(temp, ':');
  if(temp == RT_NULL)
    return NET_ERROR_FAIL;
  temp++;
  *second = atoi(temp);
  
  return NET_ERROR_NONE;
}

static NET_ERR get_info(char *iccid,char *imei)
{
  if(send_cmd("AT+GSN","OK",500) != NET_ERROR_NONE)
    return NET_ERROR_FAIL;
  char *temp1 = strstr((char *)serial_port.recv,"\r\n");
  char *temp2 = strstr(temp1 + 2,"\r\n");
  strncpy(imei,temp1 + 2,temp2 - temp1 - 2);

  if(send_cmd("AT+QCCID","OK",500) != NET_ERROR_NONE)
    return NET_ERROR_FAIL;
  temp1 = strchr((char *)serial_port.recv,':');
  temp2 = strstr(temp1 + 1,"\r\n");
  strncpy(iccid,temp1 + 1,temp2 - temp1 - 1);
  return NET_ERROR_NONE;
}

static NET_ERR get_net_sig(uint8_t *sig)
{
	if(send_cmd("AT+CSQ","OK",500) != NET_ERROR_NONE)
		return NET_ERROR_FAIL;
	char *temp = NULL;
	temp = rt_strstr((char *)serial_port.recv,"+CSQ");
	if(temp == RT_NULL)
		return NET_ERROR_FAIL;
	temp = strchr((char *)serial_port.recv,':');
	if(temp == RT_NULL)
		return NET_ERROR_FAIL;
	*sig = atoi(temp + 1);
	return NET_ERROR_NONE;
}

NET_BASE_FUNCTIONS ec600x_base_functions = {
  .send_cmd = send_cmd,
  .send_data = send_data,
  .waitting = watting,
  .sync_time = sync_time,
  .get_info = get_info,
	.get_net_sig = get_net_sig,
};

NET_ERR ec600m_base_function_register(NET_BASE_FUNCTIONS **info)
{
  *info = &ec600x_base_functions;
  return NET_ERROR_NONE;
}
