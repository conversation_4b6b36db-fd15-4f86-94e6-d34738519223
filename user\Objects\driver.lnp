--cpu Cortex-M0+
".\objects\main.o"
".\objects\auto_ctrl_task.o"
".\objects\user_config.o"
".\objects\protocol_common.o"
".\objects\daosheng_protocol.o"
".\objects\flow_meter_protocol.o"
".\objects\water_meter_protocol.o"
".\objects\modbus_rtu.o"
".\objects\pc_protocol.o"
".\objects\net_protocol.o"
".\objects\bsp_board.o"
".\objects\lcd12864.o"
".\objects\lcd_font.o"
".\objects\net.o"
".\objects\ec600m_common.o"
".\objects\ec600m_tcpip.o"
".\objects\uart.o"
".\objects\debug.o"
".\objects\stm32_flash.o"
".\objects\adc.o"
".\objects\rtc.o"
".\objects\system_stm32l0xx.o"
".\objects\startup_stm32l072xx.o"
".\objects\system.o"
".\objects\stm32l0xx_hal.o"
".\objects\stm32l0xx_hal_adc.o"
".\objects\stm32l0xx_hal_adc_ex.o"
".\objects\stm32l0xx_hal_cortex.o"
".\objects\stm32l0xx_hal_dma.o"
".\objects\stm32l0xx_hal_exti.o"
".\objects\stm32l0xx_hal_gpio.o"
".\objects\stm32l0xx_hal_pwr.o"
".\objects\stm32l0xx_hal_pwr_ex.o"
".\objects\stm32l0xx_hal_rcc.o"
".\objects\stm32l0xx_hal_rcc_ex.o"
".\objects\stm32l0xx_hal_rtc.o"
".\objects\stm32l0xx_hal_rtc_ex.o"
".\objects\stm32l0xx_hal_tim_ex.o"
".\objects\stm32l0xx_hal_uart.o"
".\objects\stm32l0xx_hal_uart_ex.o"
".\objects\stm32l0xx_hal_usart.o"
".\objects\stm32l0xx_hal_flash.o"
".\objects\stm32l0xx_hal_flash_ex.o"
".\objects\board.o"
".\objects\clock.o"
".\objects\components.o"
".\objects\cpu.o"
".\objects\device.o"
".\objects\idle.o"
".\objects\ipc.o"
".\objects\irq.o"
".\objects\kservice.o"
".\objects\mem.o"
".\objects\memheap.o"
".\objects\mempool.o"
".\objects\object.o"
".\objects\scheduler.o"
".\objects\signal.o"
".\objects\slab.o"
".\objects\thread.o"
".\objects\timer.o"
".\objects\cpuport.o"
".\objects\context_rvds.o"
--library_type=microlib --strict --scatter ".\Objects\driver.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\Listings\driver.map" -o .\Objects\driver.axf