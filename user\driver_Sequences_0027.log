/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : D:\2025work\STM32L072PRO\source-application\user\driver_Sequences_0027.log
 *  Created     : 16:49:34 (22/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : D:\2025work\STM32L072PRO\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[16:49:34.377]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:49:34.377]  
[16:49:34.401]  <debugvars>
[16:49:34.428]    // Pre-defined
[16:49:34.451]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:49:34.478]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:49:34.478]    __dp=0x00000000
[16:49:34.479]    __ap=0x00000000
[16:49:34.479]    __traceout=0x00000000      (Trace Disabled)
[16:49:34.479]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:49:34.479]    __FlashAddr=0x00000000
[16:49:34.481]    __FlashLen=0x00000000
[16:49:34.481]    __FlashArg=0x00000000
[16:49:34.481]    __FlashOp=0x00000000
[16:49:34.482]    __Result=0x00000000
[16:49:34.482]    
[16:49:34.482]    // User-defined
[16:49:34.482]    DbgMCU_CR=0x00000007
[16:49:34.482]    DbgMCU_APB1_Fz=0x00000000
[16:49:34.483]    DbgMCU_APB2_Fz=0x00000000
[16:49:34.483]    DoOptionByteLoading=0x00000000
[16:49:34.483]  </debugvars>
[16:49:34.484]  
[16:49:34.484]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:49:34.485]    <block atomic="false" info="">
[16:49:34.485]      Sequence("CheckID");
[16:49:34.485]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:49:34.486]          <block atomic="false" info="">
[16:49:34.486]            __var pidr1 = 0;
[16:49:34.486]              // -> [pidr1 <= 0x00000000]
[16:49:34.487]            __var pidr2 = 0;
[16:49:34.487]              // -> [pidr2 <= 0x00000000]
[16:49:34.488]            __var jep106id = 0;
[16:49:34.488]              // -> [jep106id <= 0x00000000]
[16:49:34.489]            __var ROMTableBase = 0;
[16:49:34.489]              // -> [ROMTableBase <= 0x00000000]
[16:49:34.489]            __ap = 0;      // AHB-AP
[16:49:34.489]              // -> [__ap <= 0x00000000]
[16:49:34.490]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:49:34.497]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:49:34.497]              // -> [ROMTableBase <= 0xF0000000]
[16:49:34.498]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:49:34.507]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:49:34.519]              // -> [pidr1 <= 0x00000004]
[16:49:34.519]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:49:34.526]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:49:34.527]              // -> [pidr2 <= 0x0000000A]
[16:49:34.528]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:49:34.528]              // -> [jep106id <= 0x00000020]
[16:49:34.529]          </block>
[16:49:34.529]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:49:34.530]            // if-block "jep106id != 0x20"
[16:49:34.530]              // =>  FALSE
[16:49:34.531]            // skip if-block "jep106id != 0x20"
[16:49:34.531]          </control>
[16:49:34.532]        </sequence>
[16:49:34.532]    </block>
[16:49:34.533]  </sequence>
[16:49:34.533]  
[16:49:34.640]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:49:34.640]  
[16:49:34.641]  <debugvars>
[16:49:34.642]    // Pre-defined
[16:49:34.643]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:49:34.643]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:49:34.644]    __dp=0x00000000
[16:49:34.644]    __ap=0x00000000
[16:49:34.645]    __traceout=0x00000000      (Trace Disabled)
[16:49:34.645]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:49:34.646]    __FlashAddr=0x00000000
[16:49:34.646]    __FlashLen=0x00000000
[16:49:34.647]    __FlashArg=0x00000000
[16:49:34.647]    __FlashOp=0x00000000
[16:49:34.647]    __Result=0x00000000
[16:49:34.647]    
[16:49:34.647]    // User-defined
[16:49:34.648]    DbgMCU_CR=0x00000007
[16:49:34.648]    DbgMCU_APB1_Fz=0x00000000
[16:49:34.648]    DbgMCU_APB2_Fz=0x00000000
[16:49:34.649]    DoOptionByteLoading=0x00000000
[16:49:34.649]  </debugvars>
[16:49:34.649]  
[16:49:34.650]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:49:34.650]    <block atomic="false" info="">
[16:49:34.651]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:49:34.657]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:49:34.657]    </block>
[16:49:34.658]    <block atomic="false" info="DbgMCU registers">
[16:49:34.658]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:49:34.665]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:49:34.672]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:49:34.677]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:49:34.684]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:49:34.685]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:49:34.691]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:49:34.692]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:49:34.699]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:49:34.700]    </block>
[16:49:34.701]  </sequence>
[16:49:34.701]  
[16:49:54.518]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:49:54.518]  
[16:49:54.519]  <debugvars>
[16:49:54.519]    // Pre-defined
[16:49:54.520]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:49:54.520]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:49:54.521]    __dp=0x00000000
[16:49:54.522]    __ap=0x00000000
[16:49:54.522]    __traceout=0x00000000      (Trace Disabled)
[16:49:54.523]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:49:54.523]    __FlashAddr=0x00000000
[16:49:54.524]    __FlashLen=0x00000000
[16:49:54.524]    __FlashArg=0x00000000
[16:49:54.524]    __FlashOp=0x00000000
[16:49:54.524]    __Result=0x00000000
[16:49:54.525]    
[16:49:54.525]    // User-defined
[16:49:54.526]    DbgMCU_CR=0x00000007
[16:49:54.526]    DbgMCU_APB1_Fz=0x00000000
[16:49:54.526]    DbgMCU_APB2_Fz=0x00000000
[16:49:54.527]    DoOptionByteLoading=0x00000000
[16:49:54.527]  </debugvars>
[16:49:54.527]  
[16:49:54.528]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:49:54.528]    <block atomic="false" info="">
[16:49:54.528]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:49:54.529]        // -> [connectionFlash <= 0x00000001]
[16:49:54.529]      __var FLASH_BASE = 0x40022000 ;
[16:49:54.529]        // -> [FLASH_BASE <= 0x40022000]
[16:49:54.529]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:49:54.530]        // -> [FLASH_CR <= 0x40022004]
[16:49:54.530]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:49:54.530]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:49:54.530]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:49:54.530]        // -> [LOCK_BIT <= 0x00000001]
[16:49:54.530]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:49:54.531]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:49:54.531]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:49:54.531]        // -> [FLASH_KEYR <= 0x4002200C]
[16:49:54.531]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:49:54.531]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:49:54.532]      __var FLASH_KEY2 = 0x02030405 ;
[16:49:54.532]        // -> [FLASH_KEY2 <= 0x02030405]
[16:49:54.532]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:49:54.533]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:49:54.533]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:49:54.533]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:49:54.533]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:49:54.533]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:49:54.533]      __var FLASH_CR_Value = 0 ;
[16:49:54.534]        // -> [FLASH_CR_Value <= 0x00000000]
[16:49:54.534]      __var DoDebugPortStop = 1 ;
[16:49:54.534]        // -> [DoDebugPortStop <= 0x00000001]
[16:49:54.534]      __var DP_CTRL_STAT = 0x4 ;
[16:49:54.534]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:49:54.534]      __var DP_SELECT = 0x8 ;
[16:49:54.536]        // -> [DP_SELECT <= 0x00000008]
[16:49:54.536]    </block>
[16:49:54.536]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:49:54.536]      // if-block "connectionFlash && DoOptionByteLoading"
[16:49:54.537]        // =>  FALSE
[16:49:54.537]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:49:54.537]    </control>
[16:49:54.537]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:49:54.537]      // if-block "DoDebugPortStop"
[16:49:54.538]        // =>  TRUE
[16:49:54.538]      <block atomic="false" info="">
[16:49:54.538]        WriteDP(DP_SELECT, 0x00000000);
[16:49:54.540]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:49:54.541]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:49:54.543]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:49:54.544]      </block>
[16:49:54.544]      // end if-block "DoDebugPortStop"
[16:49:54.545]    </control>
[16:49:54.545]  </sequence>
[16:49:54.546]  
[16:53:44.231]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:53:44.231]  
[16:53:44.232]  <debugvars>
[16:53:44.232]    // Pre-defined
[16:53:44.232]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:53:44.232]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:53:44.233]    __dp=0x00000000
[16:53:44.233]    __ap=0x00000000
[16:53:44.233]    __traceout=0x00000000      (Trace Disabled)
[16:53:44.233]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:53:44.233]    __FlashAddr=0x00000000
[16:53:44.234]    __FlashLen=0x00000000
[16:53:44.234]    __FlashArg=0x00000000
[16:53:44.234]    __FlashOp=0x00000000
[16:53:44.234]    __Result=0x00000000
[16:53:44.235]    
[16:53:44.235]    // User-defined
[16:53:44.235]    DbgMCU_CR=0x00000007
[16:53:44.235]    DbgMCU_APB1_Fz=0x00000000
[16:53:44.235]    DbgMCU_APB2_Fz=0x00000000
[16:53:44.235]    DoOptionByteLoading=0x00000000
[16:53:44.236]  </debugvars>
[16:53:44.236]  
[16:53:44.236]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:53:44.236]    <block atomic="false" info="">
[16:53:44.237]      Sequence("CheckID");
[16:53:44.237]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:53:44.238]          <block atomic="false" info="">
[16:53:44.239]            __var pidr1 = 0;
[16:53:44.239]              // -> [pidr1 <= 0x00000000]
[16:53:44.239]            __var pidr2 = 0;
[16:53:44.239]              // -> [pidr2 <= 0x00000000]
[16:53:44.239]            __var jep106id = 0;
[16:53:44.240]              // -> [jep106id <= 0x00000000]
[16:53:44.240]            __var ROMTableBase = 0;
[16:53:44.240]              // -> [ROMTableBase <= 0x00000000]
[16:53:44.240]            __ap = 0;      // AHB-AP
[16:53:44.240]              // -> [__ap <= 0x00000000]
[16:53:44.240]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:53:44.245]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:53:44.245]              // -> [ROMTableBase <= 0xF0000000]
[16:53:44.245]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:53:44.254]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:53:44.254]              // -> [pidr1 <= 0x00000004]
[16:53:44.255]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:53:44.261]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:53:44.283]              // -> [pidr2 <= 0x0000000A]
[16:53:44.284]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:53:44.284]              // -> [jep106id <= 0x00000020]
[16:53:44.285]          </block>
[16:53:44.285]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:53:44.285]            // if-block "jep106id != 0x20"
[16:53:44.286]              // =>  FALSE
[16:53:44.286]            // skip if-block "jep106id != 0x20"
[16:53:44.286]          </control>
[16:53:44.286]        </sequence>
[16:53:44.287]    </block>
[16:53:44.287]  </sequence>
[16:53:44.288]  
[16:53:44.394]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:53:44.394]  
[16:53:44.394]  <debugvars>
[16:53:44.395]    // Pre-defined
[16:53:44.395]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:53:44.395]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:53:44.395]    __dp=0x00000000
[16:53:44.395]    __ap=0x00000000
[16:53:44.396]    __traceout=0x00000000      (Trace Disabled)
[16:53:44.396]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:53:44.396]    __FlashAddr=0x00000000
[16:53:44.397]    __FlashLen=0x00000000
[16:53:44.397]    __FlashArg=0x00000000
[16:53:44.397]    __FlashOp=0x00000000
[16:53:44.397]    __Result=0x00000000
[16:53:44.398]    
[16:53:44.398]    // User-defined
[16:53:44.398]    DbgMCU_CR=0x00000007
[16:53:44.398]    DbgMCU_APB1_Fz=0x00000000
[16:53:44.398]    DbgMCU_APB2_Fz=0x00000000
[16:53:44.398]    DoOptionByteLoading=0x00000000
[16:53:44.398]  </debugvars>
[16:53:44.398]  
[16:53:44.399]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:53:44.399]    <block atomic="false" info="">
[16:53:44.399]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:53:44.406]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:53:44.406]    </block>
[16:53:44.406]    <block atomic="false" info="DbgMCU registers">
[16:53:44.406]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:53:44.413]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:53:44.419]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:53:44.425]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:53:44.432]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:53:44.433]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:53:44.440]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:53:44.442]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:53:44.449]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:53:44.451]    </block>
[16:53:44.451]  </sequence>
[16:53:44.451]  
[16:54:03.806]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:54:03.806]  
[16:54:03.806]  <debugvars>
[16:54:03.807]    // Pre-defined
[16:54:03.808]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:54:03.808]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:54:03.808]    __dp=0x00000000
[16:54:03.808]    __ap=0x00000000
[16:54:03.809]    __traceout=0x00000000      (Trace Disabled)
[16:54:03.809]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:54:03.809]    __FlashAddr=0x00000000
[16:54:03.810]    __FlashLen=0x00000000
[16:54:03.810]    __FlashArg=0x00000000
[16:54:03.811]    __FlashOp=0x00000000
[16:54:03.811]    __Result=0x00000000
[16:54:03.811]    
[16:54:03.811]    // User-defined
[16:54:03.811]    DbgMCU_CR=0x00000007
[16:54:03.812]    DbgMCU_APB1_Fz=0x00000000
[16:54:03.812]    DbgMCU_APB2_Fz=0x00000000
[16:54:03.812]    DoOptionByteLoading=0x00000000
[16:54:03.812]  </debugvars>
[16:54:03.812]  
[16:54:03.812]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:54:03.813]    <block atomic="false" info="">
[16:54:03.813]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:54:03.814]        // -> [connectionFlash <= 0x00000001]
[16:54:03.814]      __var FLASH_BASE = 0x40022000 ;
[16:54:03.814]        // -> [FLASH_BASE <= 0x40022000]
[16:54:03.814]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:54:03.814]        // -> [FLASH_CR <= 0x40022004]
[16:54:03.815]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:54:03.815]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:54:03.815]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:54:03.815]        // -> [LOCK_BIT <= 0x00000001]
[16:54:03.816]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:54:03.816]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:54:03.816]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:54:03.816]        // -> [FLASH_KEYR <= 0x4002200C]
[16:54:03.817]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:54:03.817]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:54:03.817]      __var FLASH_KEY2 = 0x02030405 ;
[16:54:03.817]        // -> [FLASH_KEY2 <= 0x02030405]
[16:54:03.818]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:54:03.818]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:54:03.818]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:54:03.818]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:54:03.819]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:54:03.819]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:54:03.819]      __var FLASH_CR_Value = 0 ;
[16:54:03.819]        // -> [FLASH_CR_Value <= 0x00000000]
[16:54:03.820]      __var DoDebugPortStop = 1 ;
[16:54:03.820]        // -> [DoDebugPortStop <= 0x00000001]
[16:54:03.820]      __var DP_CTRL_STAT = 0x4 ;
[16:54:03.821]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:54:03.821]      __var DP_SELECT = 0x8 ;
[16:54:03.821]        // -> [DP_SELECT <= 0x00000008]
[16:54:03.821]    </block>
[16:54:03.822]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:54:03.822]      // if-block "connectionFlash && DoOptionByteLoading"
[16:54:03.822]        // =>  FALSE
[16:54:03.822]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:54:03.822]    </control>
[16:54:03.823]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:54:03.824]      // if-block "DoDebugPortStop"
[16:54:03.824]        // =>  TRUE
[16:54:03.824]      <block atomic="false" info="">
[16:54:03.824]        WriteDP(DP_SELECT, 0x00000000);
[16:54:03.826]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:54:03.826]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:54:03.829]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:54:03.829]      </block>
[16:54:03.830]      // end if-block "DoDebugPortStop"
[16:54:03.831]    </control>
[16:54:03.831]  </sequence>
[16:54:03.831]  
[16:58:30.687]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:58:30.687]  
[16:58:30.688]  <debugvars>
[16:58:30.688]    // Pre-defined
[16:58:30.689]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:58:30.690]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[16:58:30.690]    __dp=0x00000000
[16:58:30.691]    __ap=0x00000000
[16:58:30.691]    __traceout=0x00000000      (Trace Disabled)
[16:58:30.691]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:58:30.693]    __FlashAddr=0x00000000
[16:58:30.693]    __FlashLen=0x00000000
[16:58:30.694]    __FlashArg=0x00000000
[16:58:30.694]    __FlashOp=0x00000000
[16:58:30.695]    __Result=0x00000000
[16:58:30.695]    
[16:58:30.695]    // User-defined
[16:58:30.696]    DbgMCU_CR=0x00000007
[16:58:30.696]    DbgMCU_APB1_Fz=0x00000000
[16:58:30.696]    DbgMCU_APB2_Fz=0x00000000
[16:58:30.698]    DoOptionByteLoading=0x00000000
[16:58:30.698]  </debugvars>
[16:58:30.699]  
[16:58:30.699]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:58:30.700]    <block atomic="false" info="">
[16:58:30.700]      Sequence("CheckID");
[16:58:30.701]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:58:30.701]          <block atomic="false" info="">
[16:58:30.701]            __var pidr1 = 0;
[16:58:30.702]              // -> [pidr1 <= 0x00000000]
[16:58:30.702]            __var pidr2 = 0;
[16:58:30.703]              // -> [pidr2 <= 0x00000000]
[16:58:30.703]            __var jep106id = 0;
[16:58:30.704]              // -> [jep106id <= 0x00000000]
[16:58:30.704]            __var ROMTableBase = 0;
[16:58:30.705]              // -> [ROMTableBase <= 0x00000000]
[16:58:30.705]            __ap = 0;      // AHB-AP
[16:58:30.705]              // -> [__ap <= 0x00000000]
[16:58:30.706]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:58:30.710]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:58:30.710]              // -> [ROMTableBase <= 0xF0000000]
[16:58:30.711]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:58:30.719]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:58:30.734]              // -> [pidr1 <= 0x00000004]
[16:58:30.734]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:58:30.741]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:58:30.741]              // -> [pidr2 <= 0x0000000A]
[16:58:30.742]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:58:30.742]              // -> [jep106id <= 0x00000020]
[16:58:30.743]          </block>
[16:58:30.743]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:58:30.743]            // if-block "jep106id != 0x20"
[16:58:30.743]              // =>  FALSE
[16:58:30.743]            // skip if-block "jep106id != 0x20"
[16:58:30.743]          </control>
[16:58:30.744]        </sequence>
[16:58:30.744]    </block>
[16:58:30.744]  </sequence>
[16:58:30.744]  
[16:58:30.851]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:58:30.851]  
[16:58:30.851]  <debugvars>
[16:58:30.851]    // Pre-defined
[16:58:30.851]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:58:30.853]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[16:58:30.853]    __dp=0x00000000
[16:58:30.853]    __ap=0x00000000
[16:58:30.854]    __traceout=0x00000000      (Trace Disabled)
[16:58:30.854]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:58:30.854]    __FlashAddr=0x00000000
[16:58:30.854]    __FlashLen=0x00000000
[16:58:30.855]    __FlashArg=0x00000000
[16:58:30.855]    __FlashOp=0x00000000
[16:58:30.855]    __Result=0x00000000
[16:58:30.855]    
[16:58:30.855]    // User-defined
[16:58:30.855]    DbgMCU_CR=0x00000007
[16:58:30.855]    DbgMCU_APB1_Fz=0x00000000
[16:58:30.856]    DbgMCU_APB2_Fz=0x00000000
[16:58:30.856]    DoOptionByteLoading=0x00000000
[16:58:30.856]  </debugvars>
[16:58:30.856]  
[16:58:30.857]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:58:30.857]    <block atomic="false" info="">
[16:58:30.857]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:58:30.862]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:58:30.865]    </block>
[16:58:30.865]    <block atomic="false" info="DbgMCU registers">
[16:58:30.865]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:58:30.872]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:58:30.877]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:58:30.882]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:58:30.888]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:58:30.889]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:58:30.897]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:58:30.909]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:58:30.916]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:58:30.916]    </block>
[16:58:30.916]  </sequence>
[16:58:30.917]  
[17:00:17.016]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:00:17.016]  
[17:00:17.017]  <debugvars>
[17:00:17.017]    // Pre-defined
[17:00:17.017]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:00:17.017]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[17:00:17.018]    __dp=0x00000000
[17:00:17.018]    __ap=0x00000000
[17:00:17.019]    __traceout=0x00000000      (Trace Disabled)
[17:00:17.019]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:00:17.019]    __FlashAddr=0x00000000
[17:00:17.019]    __FlashLen=0x00000000
[17:00:17.019]    __FlashArg=0x00000000
[17:00:17.020]    __FlashOp=0x00000000
[17:00:17.020]    __Result=0x00000000
[17:00:17.020]    
[17:00:17.020]    // User-defined
[17:00:17.020]    DbgMCU_CR=0x00000007
[17:00:17.021]    DbgMCU_APB1_Fz=0x00000000
[17:00:17.021]    DbgMCU_APB2_Fz=0x00000000
[17:00:17.021]    DoOptionByteLoading=0x00000000
[17:00:17.022]  </debugvars>
[17:00:17.022]  
[17:00:17.022]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:00:17.022]    <block atomic="false" info="">
[17:00:17.022]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:00:17.022]        // -> [connectionFlash <= 0x00000000]
[17:00:17.022]      __var FLASH_BASE = 0x40022000 ;
[17:00:17.022]        // -> [FLASH_BASE <= 0x40022000]
[17:00:17.023]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:00:17.023]        // -> [FLASH_CR <= 0x40022004]
[17:00:17.024]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:00:17.024]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:00:17.024]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:00:17.024]        // -> [LOCK_BIT <= 0x00000001]
[17:00:17.025]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:00:17.025]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:00:17.025]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:00:17.026]        // -> [FLASH_KEYR <= 0x4002200C]
[17:00:17.026]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:00:17.026]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:00:17.026]      __var FLASH_KEY2 = 0x02030405 ;
[17:00:17.026]        // -> [FLASH_KEY2 <= 0x02030405]
[17:00:17.026]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:00:17.026]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:00:17.026]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:00:17.026]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:00:17.027]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:00:17.027]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:00:17.027]      __var FLASH_CR_Value = 0 ;
[17:00:17.027]        // -> [FLASH_CR_Value <= 0x00000000]
[17:00:17.027]      __var DoDebugPortStop = 1 ;
[17:00:17.028]        // -> [DoDebugPortStop <= 0x00000001]
[17:00:17.028]      __var DP_CTRL_STAT = 0x4 ;
[17:00:17.028]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:00:17.028]      __var DP_SELECT = 0x8 ;
[17:00:17.028]        // -> [DP_SELECT <= 0x00000008]
[17:00:17.030]    </block>
[17:00:17.030]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:00:17.030]      // if-block "connectionFlash && DoOptionByteLoading"
[17:00:17.031]        // =>  FALSE
[17:00:17.031]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:00:17.031]    </control>
[17:00:17.031]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:00:17.032]      // if-block "DoDebugPortStop"
[17:00:17.032]        // =>  TRUE
[17:00:17.032]      <block atomic="false" info="">
[17:00:17.033]        WriteDP(DP_SELECT, 0x00000000);
[17:00:17.035]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:00:17.036]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:00:17.038]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:00:17.039]      </block>
[17:00:17.039]      // end if-block "DoDebugPortStop"
[17:00:17.040]    </control>
[17:00:17.040]  </sequence>
[17:00:17.041]  
[17:03:48.952]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:03:48.952]  
[17:03:48.952]  <debugvars>
[17:03:48.952]    // Pre-defined
[17:03:48.953]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:03:48.953]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:03:48.953]    __dp=0x00000000
[17:03:48.954]    __ap=0x00000000
[17:03:48.954]    __traceout=0x00000000      (Trace Disabled)
[17:03:48.955]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:03:48.955]    __FlashAddr=0x00000000
[17:03:48.955]    __FlashLen=0x00000000
[17:03:48.955]    __FlashArg=0x00000000
[17:03:48.955]    __FlashOp=0x00000000
[17:03:48.955]    __Result=0x00000000
[17:03:48.956]    
[17:03:48.956]    // User-defined
[17:03:48.956]    DbgMCU_CR=0x00000007
[17:03:48.957]    DbgMCU_APB1_Fz=0x00000000
[17:03:48.957]    DbgMCU_APB2_Fz=0x00000000
[17:03:48.957]    DoOptionByteLoading=0x00000000
[17:03:48.957]  </debugvars>
[17:03:48.957]  
[17:03:48.958]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:03:48.959]    <block atomic="false" info="">
[17:03:48.959]      Sequence("CheckID");
[17:03:48.959]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:03:48.959]          <block atomic="false" info="">
[17:03:48.959]            __var pidr1 = 0;
[17:03:48.959]              // -> [pidr1 <= 0x00000000]
[17:03:48.960]            __var pidr2 = 0;
[17:03:48.960]              // -> [pidr2 <= 0x00000000]
[17:03:48.960]            __var jep106id = 0;
[17:03:48.960]              // -> [jep106id <= 0x00000000]
[17:03:48.960]            __var ROMTableBase = 0;
[17:03:48.961]              // -> [ROMTableBase <= 0x00000000]
[17:03:48.961]            __ap = 0;      // AHB-AP
[17:03:48.961]              // -> [__ap <= 0x00000000]
[17:03:48.962]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:03:48.965]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:03:48.967]              // -> [ROMTableBase <= 0xF0000000]
[17:03:48.967]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:03:48.975]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:03:48.988]              // -> [pidr1 <= 0x00000004]
[17:03:48.988]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:03:48.995]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:03:48.997]              // -> [pidr2 <= 0x0000000A]
[17:03:48.997]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:03:48.998]              // -> [jep106id <= 0x00000020]
[17:03:48.998]          </block>
[17:03:48.998]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:03:48.999]            // if-block "jep106id != 0x20"
[17:03:48.999]              // =>  FALSE
[17:03:49.000]            // skip if-block "jep106id != 0x20"
[17:03:49.000]          </control>
[17:03:49.000]        </sequence>
[17:03:49.001]    </block>
[17:03:49.001]  </sequence>
[17:03:49.001]  
[17:03:49.106]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:03:49.106]  
[17:03:49.107]  <debugvars>
[17:03:49.107]    // Pre-defined
[17:03:49.109]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:03:49.109]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:03:49.109]    __dp=0x00000000
[17:03:49.109]    __ap=0x00000000
[17:03:49.110]    __traceout=0x00000000      (Trace Disabled)
[17:03:49.111]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:03:49.111]    __FlashAddr=0x00000000
[17:03:49.111]    __FlashLen=0x00000000
[17:03:49.111]    __FlashArg=0x00000000
[17:03:49.112]    __FlashOp=0x00000000
[17:03:49.112]    __Result=0x00000000
[17:03:49.112]    
[17:03:49.112]    // User-defined
[17:03:49.113]    DbgMCU_CR=0x00000007
[17:03:49.113]    DbgMCU_APB1_Fz=0x00000000
[17:03:49.113]    DbgMCU_APB2_Fz=0x00000000
[17:03:49.115]    DoOptionByteLoading=0x00000000
[17:03:49.115]  </debugvars>
[17:03:49.115]  
[17:03:49.115]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:03:49.115]    <block atomic="false" info="">
[17:03:49.115]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:03:49.121]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:03:49.122]    </block>
[17:03:49.122]    <block atomic="false" info="DbgMCU registers">
[17:03:49.122]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:03:49.129]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[17:03:49.141]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[17:03:49.155]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:03:49.162]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:03:49.163]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:03:49.169]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:03:49.170]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:03:49.177]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:03:49.177]    </block>
[17:03:49.178]  </sequence>
[17:03:49.178]  
[17:04:08.497]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:04:08.497]  
[17:04:08.497]  <debugvars>
[17:04:08.498]    // Pre-defined
[17:04:08.498]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:04:08.498]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:04:08.498]    __dp=0x00000000
[17:04:08.498]    __ap=0x00000000
[17:04:08.499]    __traceout=0x00000000      (Trace Disabled)
[17:04:08.499]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:04:08.499]    __FlashAddr=0x00000000
[17:04:08.499]    __FlashLen=0x00000000
[17:04:08.499]    __FlashArg=0x00000000
[17:04:08.499]    __FlashOp=0x00000000
[17:04:08.500]    __Result=0x00000000
[17:04:08.500]    
[17:04:08.500]    // User-defined
[17:04:08.500]    DbgMCU_CR=0x00000007
[17:04:08.501]    DbgMCU_APB1_Fz=0x00000000
[17:04:08.501]    DbgMCU_APB2_Fz=0x00000000
[17:04:08.501]    DoOptionByteLoading=0x00000000
[17:04:08.501]  </debugvars>
[17:04:08.501]  
[17:04:08.501]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:04:08.502]    <block atomic="false" info="">
[17:04:08.502]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:04:08.503]        // -> [connectionFlash <= 0x00000001]
[17:04:08.503]      __var FLASH_BASE = 0x40022000 ;
[17:04:08.503]        // -> [FLASH_BASE <= 0x40022000]
[17:04:08.503]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:04:08.503]        // -> [FLASH_CR <= 0x40022004]
[17:04:08.504]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:04:08.504]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:04:08.504]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:04:08.505]        // -> [LOCK_BIT <= 0x00000001]
[17:04:08.505]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:04:08.505]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:04:08.505]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:04:08.505]        // -> [FLASH_KEYR <= 0x4002200C]
[17:04:08.506]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:04:08.506]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:04:08.506]      __var FLASH_KEY2 = 0x02030405 ;
[17:04:08.506]        // -> [FLASH_KEY2 <= 0x02030405]
[17:04:08.506]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:04:08.506]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:04:08.506]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:04:08.507]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:04:08.507]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:04:08.508]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:04:08.508]      __var FLASH_CR_Value = 0 ;
[17:04:08.508]        // -> [FLASH_CR_Value <= 0x00000000]
[17:04:08.508]      __var DoDebugPortStop = 1 ;
[17:04:08.508]        // -> [DoDebugPortStop <= 0x00000001]
[17:04:08.508]      __var DP_CTRL_STAT = 0x4 ;
[17:04:08.508]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:04:08.509]      __var DP_SELECT = 0x8 ;
[17:04:08.509]        // -> [DP_SELECT <= 0x00000008]
[17:04:08.509]    </block>
[17:04:08.509]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:04:08.509]      // if-block "connectionFlash && DoOptionByteLoading"
[17:04:08.510]        // =>  FALSE
[17:04:08.510]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:04:08.510]    </control>
[17:04:08.510]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:04:08.510]      // if-block "DoDebugPortStop"
[17:04:08.511]        // =>  TRUE
[17:04:08.511]      <block atomic="false" info="">
[17:04:08.512]        WriteDP(DP_SELECT, 0x00000000);
[17:04:08.514]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:04:08.514]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:04:08.517]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:04:08.517]      </block>
[17:04:08.517]      // end if-block "DoDebugPortStop"
[17:04:08.517]    </control>
[17:04:08.518]  </sequence>
[17:04:08.518]  
[17:05:37.945]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:05:37.945]  
[17:05:37.945]  <debugvars>
[17:05:37.946]    // Pre-defined
[17:05:37.946]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:05:37.946]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:05:37.947]    __dp=0x00000000
[17:05:37.947]    __ap=0x00000000
[17:05:37.947]    __traceout=0x00000000      (Trace Disabled)
[17:05:37.948]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:05:37.948]    __FlashAddr=0x00000000
[17:05:37.948]    __FlashLen=0x00000000
[17:05:37.949]    __FlashArg=0x00000000
[17:05:37.949]    __FlashOp=0x00000000
[17:05:37.949]    __Result=0x00000000
[17:05:37.949]    
[17:05:37.949]    // User-defined
[17:05:37.949]    DbgMCU_CR=0x00000007
[17:05:37.950]    DbgMCU_APB1_Fz=0x00000000
[17:05:37.950]    DbgMCU_APB2_Fz=0x00000000
[17:05:37.950]    DoOptionByteLoading=0x00000000
[17:05:37.950]  </debugvars>
[17:05:37.950]  
[17:05:37.950]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:05:37.951]    <block atomic="false" info="">
[17:05:37.951]      Sequence("CheckID");
[17:05:37.951]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:05:37.951]          <block atomic="false" info="">
[17:05:37.951]            __var pidr1 = 0;
[17:05:37.951]              // -> [pidr1 <= 0x00000000]
[17:05:37.952]            __var pidr2 = 0;
[17:05:37.952]              // -> [pidr2 <= 0x00000000]
[17:05:37.952]            __var jep106id = 0;
[17:05:37.952]              // -> [jep106id <= 0x00000000]
[17:05:37.952]            __var ROMTableBase = 0;
[17:05:37.952]              // -> [ROMTableBase <= 0x00000000]
[17:05:37.954]            __ap = 0;      // AHB-AP
[17:05:37.954]              // -> [__ap <= 0x00000000]
[17:05:37.954]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:05:37.958]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:05:37.958]              // -> [ROMTableBase <= 0xF0000000]
[17:05:37.958]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:05:37.967]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:05:37.985]              // -> [pidr1 <= 0x00000004]
[17:05:37.985]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:05:37.991]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:05:37.992]              // -> [pidr2 <= 0x0000000A]
[17:05:37.992]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:05:37.993]              // -> [jep106id <= 0x00000020]
[17:05:37.993]          </block>
[17:05:37.993]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:05:37.994]            // if-block "jep106id != 0x20"
[17:05:37.994]              // =>  FALSE
[17:05:37.994]            // skip if-block "jep106id != 0x20"
[17:05:37.995]          </control>
[17:05:37.995]        </sequence>
[17:05:37.995]    </block>
[17:05:37.995]  </sequence>
[17:05:37.996]  
[17:05:38.103]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:05:38.103]  
[17:05:38.103]  <debugvars>
[17:05:38.103]    // Pre-defined
[17:05:38.104]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:05:38.104]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:05:38.104]    __dp=0x00000000
[17:05:38.105]    __ap=0x00000000
[17:05:38.105]    __traceout=0x00000000      (Trace Disabled)
[17:05:38.105]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:05:38.106]    __FlashAddr=0x00000000
[17:05:38.106]    __FlashLen=0x00000000
[17:05:38.106]    __FlashArg=0x00000000
[17:05:38.107]    __FlashOp=0x00000000
[17:05:38.107]    __Result=0x00000000
[17:05:38.107]    
[17:05:38.107]    // User-defined
[17:05:38.107]    DbgMCU_CR=0x00000007
[17:05:38.107]    DbgMCU_APB1_Fz=0x00000000
[17:05:38.107]    DbgMCU_APB2_Fz=0x00000000
[17:05:38.107]    DoOptionByteLoading=0x00000000
[17:05:38.109]  </debugvars>
[17:05:38.109]  
[17:05:38.109]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:05:38.109]    <block atomic="false" info="">
[17:05:38.109]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:05:38.116]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:05:38.121]    </block>
[17:05:38.121]    <block atomic="false" info="DbgMCU registers">
[17:05:38.121]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:05:38.128]        // -> [Read32(0x40021034) => 0x00000200]   (__dp=0x00000000, __ap=0x00000000)
[17:05:38.134]        // -> [Write32(0x40021034, 0x00400200)]   (__dp=0x00000000, __ap=0x00000000)
[17:05:38.138]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:05:38.145]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:05:38.146]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:05:38.151]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:05:38.154]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:05:38.161]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:05:38.163]    </block>
[17:05:38.163]  </sequence>
[17:05:38.163]  
[17:05:57.512]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:05:57.512]  
[17:05:57.512]  <debugvars>
[17:05:57.512]    // Pre-defined
[17:05:57.513]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:05:57.513]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:05:57.513]    __dp=0x00000000
[17:05:57.514]    __ap=0x00000000
[17:05:57.514]    __traceout=0x00000000      (Trace Disabled)
[17:05:57.514]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:05:57.514]    __FlashAddr=0x00000000
[17:05:57.515]    __FlashLen=0x00000000
[17:05:57.516]    __FlashArg=0x00000000
[17:05:57.516]    __FlashOp=0x00000000
[17:05:57.516]    __Result=0x00000000
[17:05:57.516]    
[17:05:57.516]    // User-defined
[17:05:57.516]    DbgMCU_CR=0x00000007
[17:05:57.517]    DbgMCU_APB1_Fz=0x00000000
[17:05:57.517]    DbgMCU_APB2_Fz=0x00000000
[17:05:57.517]    DoOptionByteLoading=0x00000000
[17:05:57.517]  </debugvars>
[17:05:57.518]  
[17:05:57.518]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:05:57.518]    <block atomic="false" info="">
[17:05:57.518]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:05:57.519]        // -> [connectionFlash <= 0x00000001]
[17:05:57.519]      __var FLASH_BASE = 0x40022000 ;
[17:05:57.519]        // -> [FLASH_BASE <= 0x40022000]
[17:05:57.520]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:05:57.520]        // -> [FLASH_CR <= 0x40022004]
[17:05:57.521]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:05:57.521]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:05:57.521]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:05:57.521]        // -> [LOCK_BIT <= 0x00000001]
[17:05:57.522]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:05:57.522]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:05:57.522]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:05:57.522]        // -> [FLASH_KEYR <= 0x4002200C]
[17:05:57.523]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:05:57.523]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:05:57.523]      __var FLASH_KEY2 = 0x02030405 ;
[17:05:57.524]        // -> [FLASH_KEY2 <= 0x02030405]
[17:05:57.524]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:05:57.524]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:05:57.524]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:05:57.524]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:05:57.524]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:05:57.524]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:05:57.525]      __var FLASH_CR_Value = 0 ;
[17:05:57.525]        // -> [FLASH_CR_Value <= 0x00000000]
[17:05:57.526]      __var DoDebugPortStop = 1 ;
[17:05:57.526]        // -> [DoDebugPortStop <= 0x00000001]
[17:05:57.526]      __var DP_CTRL_STAT = 0x4 ;
[17:05:57.526]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:05:57.526]      __var DP_SELECT = 0x8 ;
[17:05:57.526]        // -> [DP_SELECT <= 0x00000008]
[17:05:57.526]    </block>
[17:05:57.527]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:05:57.527]      // if-block "connectionFlash && DoOptionByteLoading"
[17:05:57.527]        // =>  FALSE
[17:05:57.527]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:05:57.527]    </control>
[17:05:57.527]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:05:57.528]      // if-block "DoDebugPortStop"
[17:05:57.528]        // =>  TRUE
[17:05:57.528]      <block atomic="false" info="">
[17:05:57.529]        WriteDP(DP_SELECT, 0x00000000);
[17:05:57.531]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:05:57.532]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:05:57.534]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:05:57.534]      </block>
[17:05:57.534]      // end if-block "DoDebugPortStop"
[17:05:57.535]    </control>
[17:05:57.535]  </sequence>
[17:05:57.536]  
[17:08:05.625]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:08:05.625]  
[17:08:05.625]  <debugvars>
[17:08:05.625]    // Pre-defined
[17:08:05.626]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:08:05.626]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:08:05.626]    __dp=0x00000000
[17:08:05.626]    __ap=0x00000000
[17:08:05.626]    __traceout=0x00000000      (Trace Disabled)
[17:08:05.627]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:08:05.627]    __FlashAddr=0x00000000
[17:08:05.627]    __FlashLen=0x00000000
[17:08:05.628]    __FlashArg=0x00000000
[17:08:05.628]    __FlashOp=0x00000000
[17:08:05.628]    __Result=0x00000000
[17:08:05.628]    
[17:08:05.628]    // User-defined
[17:08:05.628]    DbgMCU_CR=0x00000007
[17:08:05.628]    DbgMCU_APB1_Fz=0x00000000
[17:08:05.628]    DbgMCU_APB2_Fz=0x00000000
[17:08:05.628]    DoOptionByteLoading=0x00000000
[17:08:05.628]  </debugvars>
[17:08:05.628]  
[17:08:05.629]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:08:05.629]    <block atomic="false" info="">
[17:08:05.629]      Sequence("CheckID");
[17:08:05.630]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:08:05.631]          <block atomic="false" info="">
[17:08:05.631]            __var pidr1 = 0;
[17:08:05.631]              // -> [pidr1 <= 0x00000000]
[17:08:05.631]            __var pidr2 = 0;
[17:08:05.631]              // -> [pidr2 <= 0x00000000]
[17:08:05.631]            __var jep106id = 0;
[17:08:05.632]              // -> [jep106id <= 0x00000000]
[17:08:05.632]            __var ROMTableBase = 0;
[17:08:05.632]              // -> [ROMTableBase <= 0x00000000]
[17:08:05.632]            __ap = 0;      // AHB-AP
[17:08:05.632]              // -> [__ap <= 0x00000000]
[17:08:05.632]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:08:05.633]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:08:05.634]              // -> [ROMTableBase <= 0xF0000000]
[17:08:05.634]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:08:05.635]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:08:05.636]              // -> [pidr1 <= 0x00000004]
[17:08:05.636]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:08:05.637]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:08:05.638]              // -> [pidr2 <= 0x0000000A]
[17:08:05.638]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:08:05.638]              // -> [jep106id <= 0x00000020]
[17:08:05.638]          </block>
[17:08:05.639]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:08:05.639]            // if-block "jep106id != 0x20"
[17:08:05.639]              // =>  FALSE
[17:08:05.639]            // skip if-block "jep106id != 0x20"
[17:08:05.639]          </control>
[17:08:05.640]        </sequence>
[17:08:05.640]    </block>
[17:08:05.640]  </sequence>
[17:08:05.640]  
[17:08:05.651]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:08:05.651]  
[17:08:05.671]  <debugvars>
[17:08:05.671]    // Pre-defined
[17:08:05.672]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:08:05.672]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:08:05.673]    __dp=0x00000000
[17:08:05.673]    __ap=0x00000000
[17:08:05.673]    __traceout=0x00000000      (Trace Disabled)
[17:08:05.674]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:08:05.674]    __FlashAddr=0x00000000
[17:08:05.675]    __FlashLen=0x00000000
[17:08:05.675]    __FlashArg=0x00000000
[17:08:05.676]    __FlashOp=0x00000000
[17:08:05.676]    __Result=0x00000000
[17:08:05.676]    
[17:08:05.676]    // User-defined
[17:08:05.677]    DbgMCU_CR=0x00000007
[17:08:05.677]    DbgMCU_APB1_Fz=0x00000000
[17:08:05.677]    DbgMCU_APB2_Fz=0x00000000
[17:08:05.678]    DoOptionByteLoading=0x00000000
[17:08:05.678]  </debugvars>
[17:08:05.678]  
[17:08:05.678]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:08:05.679]    <block atomic="false" info="">
[17:08:05.679]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:08:05.680]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:08:05.680]    </block>
[17:08:05.680]    <block atomic="false" info="DbgMCU registers">
[17:08:05.680]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:08:05.681]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[17:08:05.682]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[17:08:05.682]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:08:05.683]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:08:05.683]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:08:05.684]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:08:05.684]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:08:05.685]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:08:05.685]    </block>
[17:08:05.685]  </sequence>
[17:08:05.686]  
[17:08:13.884]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:08:13.884]  
[17:08:13.884]  <debugvars>
[17:08:13.885]    // Pre-defined
[17:08:13.885]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:08:13.885]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:08:13.886]    __dp=0x00000000
[17:08:13.886]    __ap=0x00000000
[17:08:13.886]    __traceout=0x00000000      (Trace Disabled)
[17:08:13.886]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:08:13.886]    __FlashAddr=0x00000000
[17:08:13.886]    __FlashLen=0x00000000
[17:08:13.886]    __FlashArg=0x00000000
[17:08:13.887]    __FlashOp=0x00000000
[17:08:13.887]    __Result=0x00000000
[17:08:13.887]    
[17:08:13.887]    // User-defined
[17:08:13.887]    DbgMCU_CR=0x00000007
[17:08:13.887]    DbgMCU_APB1_Fz=0x00000000
[17:08:13.887]    DbgMCU_APB2_Fz=0x00000000
[17:08:13.888]    DoOptionByteLoading=0x00000000
[17:08:13.888]  </debugvars>
[17:08:13.888]  
[17:08:13.888]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:08:13.888]    <block atomic="false" info="">
[17:08:13.889]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:08:13.889]        // -> [connectionFlash <= 0x00000001]
[17:08:13.889]      __var FLASH_BASE = 0x40022000 ;
[17:08:13.889]        // -> [FLASH_BASE <= 0x40022000]
[17:08:13.890]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:08:13.890]        // -> [FLASH_CR <= 0x40022004]
[17:08:13.890]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:08:13.890]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:08:13.890]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:08:13.891]        // -> [LOCK_BIT <= 0x00000001]
[17:08:13.891]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:08:13.891]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:08:13.891]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:08:13.891]        // -> [FLASH_KEYR <= 0x4002200C]
[17:08:13.893]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:08:13.893]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:08:13.893]      __var FLASH_KEY2 = 0x02030405 ;
[17:08:13.893]        // -> [FLASH_KEY2 <= 0x02030405]
[17:08:13.893]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:08:13.894]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:08:13.894]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:08:13.894]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:08:13.894]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:08:13.894]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:08:13.894]      __var FLASH_CR_Value = 0 ;
[17:08:13.895]        // -> [FLASH_CR_Value <= 0x00000000]
[17:08:13.895]      __var DoDebugPortStop = 1 ;
[17:08:13.895]        // -> [DoDebugPortStop <= 0x00000001]
[17:08:13.895]      __var DP_CTRL_STAT = 0x4 ;
[17:08:13.895]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:08:13.896]      __var DP_SELECT = 0x8 ;
[17:08:13.896]        // -> [DP_SELECT <= 0x00000008]
[17:08:13.896]    </block>
[17:08:13.896]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:08:13.896]      // if-block "connectionFlash && DoOptionByteLoading"
[17:08:13.896]        // =>  FALSE
[17:08:13.896]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:08:13.898]    </control>
[17:08:13.898]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:08:13.898]      // if-block "DoDebugPortStop"
[17:08:13.898]        // =>  TRUE
[17:08:13.899]      <block atomic="false" info="">
[17:08:13.899]        WriteDP(DP_SELECT, 0x00000000);
[17:08:13.899]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:08:13.899]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:08:13.900]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:08:13.900]      </block>
[17:08:13.900]      // end if-block "DoDebugPortStop"
[17:08:13.900]    </control>
[17:08:13.901]  </sequence>
[17:08:13.901]  
[17:11:40.273]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:11:40.273]  
[17:11:40.273]  <debugvars>
[17:11:40.273]    // Pre-defined
[17:11:40.275]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:11:40.275]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:11:40.275]    __dp=0x00000000
[17:11:40.275]    __ap=0x00000000
[17:11:40.275]    __traceout=0x00000000      (Trace Disabled)
[17:11:40.275]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:11:40.275]    __FlashAddr=0x00000000
[17:11:40.276]    __FlashLen=0x00000000
[17:11:40.276]    __FlashArg=0x00000000
[17:11:40.276]    __FlashOp=0x00000000
[17:11:40.276]    __Result=0x00000000
[17:11:40.276]    
[17:11:40.276]    // User-defined
[17:11:40.278]    DbgMCU_CR=0x00000007
[17:11:40.278]    DbgMCU_APB1_Fz=0x00000000
[17:11:40.278]    DbgMCU_APB2_Fz=0x00000000
[17:11:40.278]    DoOptionByteLoading=0x00000000
[17:11:40.278]  </debugvars>
[17:11:40.278]  
[17:11:40.278]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:11:40.279]    <block atomic="false" info="">
[17:11:40.279]      Sequence("CheckID");
[17:11:40.279]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:11:40.279]          <block atomic="false" info="">
[17:11:40.279]            __var pidr1 = 0;
[17:11:40.280]              // -> [pidr1 <= 0x00000000]
[17:11:40.281]            __var pidr2 = 0;
[17:11:40.281]              // -> [pidr2 <= 0x00000000]
[17:11:40.281]            __var jep106id = 0;
[17:11:40.281]              // -> [jep106id <= 0x00000000]
[17:11:40.281]            __var ROMTableBase = 0;
[17:11:40.281]              // -> [ROMTableBase <= 0x00000000]
[17:11:40.283]            __ap = 0;      // AHB-AP
[17:11:40.283]              // -> [__ap <= 0x00000000]
[17:11:40.283]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:11:40.284]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:11:40.284]              // -> [ROMTableBase <= 0xF0000000]
[17:11:40.284]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:11:40.286]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:11:40.286]              // -> [pidr1 <= 0x00000004]
[17:11:40.286]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:11:40.288]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:11:40.288]              // -> [pidr2 <= 0x0000000A]
[17:11:40.288]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:11:40.289]              // -> [jep106id <= 0x00000020]
[17:11:40.289]          </block>
[17:11:40.289]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:11:40.290]            // if-block "jep106id != 0x20"
[17:11:40.290]              // =>  FALSE
[17:11:40.290]            // skip if-block "jep106id != 0x20"
[17:11:40.290]          </control>
[17:11:40.291]        </sequence>
[17:11:40.291]    </block>
[17:11:40.291]  </sequence>
[17:11:40.291]  
[17:11:40.304]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:11:40.304]  
[17:11:40.311]  <debugvars>
[17:11:40.312]    // Pre-defined
[17:11:40.312]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:11:40.312]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:11:40.313]    __dp=0x00000000
[17:11:40.313]    __ap=0x00000000
[17:11:40.313]    __traceout=0x00000000      (Trace Disabled)
[17:11:40.313]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:11:40.314]    __FlashAddr=0x00000000
[17:11:40.314]    __FlashLen=0x00000000
[17:11:40.314]    __FlashArg=0x00000000
[17:11:40.315]    __FlashOp=0x00000000
[17:11:40.315]    __Result=0x00000000
[17:11:40.315]    
[17:11:40.315]    // User-defined
[17:11:40.316]    DbgMCU_CR=0x00000007
[17:11:40.316]    DbgMCU_APB1_Fz=0x00000000
[17:11:40.316]    DbgMCU_APB2_Fz=0x00000000
[17:11:40.316]    DoOptionByteLoading=0x00000000
[17:11:40.316]  </debugvars>
[17:11:40.317]  
[17:11:40.317]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:11:40.317]    <block atomic="false" info="">
[17:11:40.318]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:11:40.319]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:11:40.319]    </block>
[17:11:40.319]    <block atomic="false" info="DbgMCU registers">
[17:11:40.320]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:11:40.320]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[17:11:40.321]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[17:11:40.322]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:11:40.322]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:11:40.323]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:11:40.324]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:11:40.324]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:11:40.325]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:11:40.325]    </block>
[17:11:40.325]  </sequence>
[17:11:40.325]  
[17:11:48.495]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:11:48.495]  
[17:11:48.495]  <debugvars>
[17:11:48.496]    // Pre-defined
[17:11:48.496]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:11:48.496]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:11:48.497]    __dp=0x00000000
[17:11:48.497]    __ap=0x00000000
[17:11:48.497]    __traceout=0x00000000      (Trace Disabled)
[17:11:48.497]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:11:48.498]    __FlashAddr=0x00000000
[17:11:48.499]    __FlashLen=0x00000000
[17:11:48.499]    __FlashArg=0x00000000
[17:11:48.499]    __FlashOp=0x00000000
[17:11:48.499]    __Result=0x00000000
[17:11:48.499]    
[17:11:48.499]    // User-defined
[17:11:48.500]    DbgMCU_CR=0x00000007
[17:11:48.500]    DbgMCU_APB1_Fz=0x00000000
[17:11:48.500]    DbgMCU_APB2_Fz=0x00000000
[17:11:48.500]    DoOptionByteLoading=0x00000000
[17:11:48.500]  </debugvars>
[17:11:48.501]  
[17:11:48.501]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:11:48.501]    <block atomic="false" info="">
[17:11:48.501]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:11:48.501]        // -> [connectionFlash <= 0x00000001]
[17:11:48.501]      __var FLASH_BASE = 0x40022000 ;
[17:11:48.502]        // -> [FLASH_BASE <= 0x40022000]
[17:11:48.502]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:11:48.502]        // -> [FLASH_CR <= 0x40022004]
[17:11:48.502]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:11:48.503]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:11:48.503]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:11:48.503]        // -> [LOCK_BIT <= 0x00000001]
[17:11:48.503]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:11:48.504]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:11:48.504]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:11:48.505]        // -> [FLASH_KEYR <= 0x4002200C]
[17:11:48.505]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:11:48.506]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:11:48.506]      __var FLASH_KEY2 = 0x02030405 ;
[17:11:48.506]        // -> [FLASH_KEY2 <= 0x02030405]
[17:11:48.506]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:11:48.506]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:11:48.506]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:11:48.507]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:11:48.507]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:11:48.507]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:11:48.507]      __var FLASH_CR_Value = 0 ;
[17:11:48.507]        // -> [FLASH_CR_Value <= 0x00000000]
[17:11:48.507]      __var DoDebugPortStop = 1 ;
[17:11:48.507]        // -> [DoDebugPortStop <= 0x00000001]
[17:11:48.508]      __var DP_CTRL_STAT = 0x4 ;
[17:11:48.508]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:11:48.508]      __var DP_SELECT = 0x8 ;
[17:11:48.508]        // -> [DP_SELECT <= 0x00000008]
[17:11:48.508]    </block>
[17:11:48.509]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:11:48.509]      // if-block "connectionFlash && DoOptionByteLoading"
[17:11:48.509]        // =>  FALSE
[17:11:48.509]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:11:48.509]    </control>
[17:11:48.509]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:11:48.510]      // if-block "DoDebugPortStop"
[17:11:48.510]        // =>  TRUE
[17:11:48.510]      <block atomic="false" info="">
[17:11:48.510]        WriteDP(DP_SELECT, 0x00000000);
[17:11:48.511]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:11:48.511]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:11:48.512]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:11:48.512]      </block>
[17:11:48.512]      // end if-block "DoDebugPortStop"
[17:11:48.512]    </control>
[17:11:48.513]  </sequence>
[17:11:48.513]  
[17:12:40.351]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:12:40.351]  
[17:12:40.352]  <debugvars>
[17:12:40.352]    // Pre-defined
[17:12:40.352]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:12:40.353]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:12:40.353]    __dp=0x00000000
[17:12:40.353]    __ap=0x00000000
[17:12:40.353]    __traceout=0x00000000      (Trace Disabled)
[17:12:40.354]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:12:40.354]    __FlashAddr=0x00000000
[17:12:40.354]    __FlashLen=0x00000000
[17:12:40.354]    __FlashArg=0x00000000
[17:12:40.355]    __FlashOp=0x00000000
[17:12:40.355]    __Result=0x00000000
[17:12:40.355]    
[17:12:40.355]    // User-defined
[17:12:40.355]    DbgMCU_CR=0x00000007
[17:12:40.355]    DbgMCU_APB1_Fz=0x00000000
[17:12:40.355]    DbgMCU_APB2_Fz=0x00000000
[17:12:40.355]    DoOptionByteLoading=0x00000000
[17:12:40.357]  </debugvars>
[17:12:40.357]  
[17:12:40.357]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:12:40.357]    <block atomic="false" info="">
[17:12:40.357]      Sequence("CheckID");
[17:12:40.358]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:12:40.358]          <block atomic="false" info="">
[17:12:40.358]            __var pidr1 = 0;
[17:12:40.358]              // -> [pidr1 <= 0x00000000]
[17:12:40.359]            __var pidr2 = 0;
[17:12:40.359]              // -> [pidr2 <= 0x00000000]
[17:12:40.359]            __var jep106id = 0;
[17:12:40.359]              // -> [jep106id <= 0x00000000]
[17:12:40.359]            __var ROMTableBase = 0;
[17:12:40.360]              // -> [ROMTableBase <= 0x00000000]
[17:12:40.360]            __ap = 0;      // AHB-AP
[17:12:40.360]              // -> [__ap <= 0x00000000]
[17:12:40.360]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:12:40.361]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:12:40.361]              // -> [ROMTableBase <= 0xF0000000]
[17:12:40.361]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:12:40.362]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:12:40.362]              // -> [pidr1 <= 0x00000004]
[17:12:40.362]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:12:40.364]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:12:40.364]              // -> [pidr2 <= 0x0000000A]
[17:12:40.364]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:12:40.364]              // -> [jep106id <= 0x00000020]
[17:12:40.364]          </block>
[17:12:40.364]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:12:40.364]            // if-block "jep106id != 0x20"
[17:12:40.365]              // =>  FALSE
[17:12:40.365]            // skip if-block "jep106id != 0x20"
[17:12:40.365]          </control>
[17:12:40.365]        </sequence>
[17:12:40.366]    </block>
[17:12:40.367]  </sequence>
[17:12:40.367]  
[17:12:40.379]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:12:40.379]  
[17:12:40.380]  <debugvars>
[17:12:40.380]    // Pre-defined
[17:12:40.380]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:12:40.380]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:12:40.381]    __dp=0x00000000
[17:12:40.381]    __ap=0x00000000
[17:12:40.381]    __traceout=0x00000000      (Trace Disabled)
[17:12:40.381]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:12:40.381]    __FlashAddr=0x00000000
[17:12:40.381]    __FlashLen=0x00000000
[17:12:40.381]    __FlashArg=0x00000000
[17:12:40.382]    __FlashOp=0x00000000
[17:12:40.382]    __Result=0x00000000
[17:12:40.383]    
[17:12:40.383]    // User-defined
[17:12:40.383]    DbgMCU_CR=0x00000007
[17:12:40.383]    DbgMCU_APB1_Fz=0x00000000
[17:12:40.383]    DbgMCU_APB2_Fz=0x00000000
[17:12:40.384]    DoOptionByteLoading=0x00000000
[17:12:40.384]  </debugvars>
[17:12:40.384]  
[17:12:40.384]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:12:40.385]    <block atomic="false" info="">
[17:12:40.385]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:12:40.386]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:12:40.386]    </block>
[17:12:40.386]    <block atomic="false" info="DbgMCU registers">
[17:12:40.386]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:12:40.387]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[17:12:40.388]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[17:12:40.388]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:12:40.389]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:12:40.389]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:12:40.390]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:12:40.390]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:12:40.391]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:12:40.391]    </block>
[17:12:40.392]  </sequence>
[17:12:40.392]  
[17:12:48.297]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:12:48.297]  
[17:12:48.298]  <debugvars>
[17:12:48.298]    // Pre-defined
[17:12:48.298]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:12:48.298]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:12:48.299]    __dp=0x00000000
[17:12:48.299]    __ap=0x00000000
[17:12:48.300]    __traceout=0x00000000      (Trace Disabled)
[17:12:48.300]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:12:48.300]    __FlashAddr=0x00000000
[17:12:48.300]    __FlashLen=0x00000000
[17:12:48.301]    __FlashArg=0x00000000
[17:12:48.301]    __FlashOp=0x00000000
[17:12:48.301]    __Result=0x00000000
[17:12:48.301]    
[17:12:48.301]    // User-defined
[17:12:48.301]    DbgMCU_CR=0x00000007
[17:12:48.301]    DbgMCU_APB1_Fz=0x00000000
[17:12:48.301]    DbgMCU_APB2_Fz=0x00000000
[17:12:48.302]    DoOptionByteLoading=0x00000000
[17:12:48.302]  </debugvars>
[17:12:48.303]  
[17:12:48.303]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:12:48.303]    <block atomic="false" info="">
[17:12:48.304]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:12:48.304]        // -> [connectionFlash <= 0x00000001]
[17:12:48.304]      __var FLASH_BASE = 0x40022000 ;
[17:12:48.304]        // -> [FLASH_BASE <= 0x40022000]
[17:12:48.304]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:12:48.304]        // -> [FLASH_CR <= 0x40022004]
[17:12:48.305]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:12:48.305]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:12:48.305]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:12:48.306]        // -> [LOCK_BIT <= 0x00000001]
[17:12:48.306]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:12:48.306]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:12:48.307]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:12:48.307]        // -> [FLASH_KEYR <= 0x4002200C]
[17:12:48.307]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:12:48.307]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:12:48.307]      __var FLASH_KEY2 = 0x02030405 ;
[17:12:48.307]        // -> [FLASH_KEY2 <= 0x02030405]
[17:12:48.308]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:12:48.308]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:12:48.308]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:12:48.308]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:12:48.308]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:12:48.308]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:12:48.308]      __var FLASH_CR_Value = 0 ;
[17:12:48.309]        // -> [FLASH_CR_Value <= 0x00000000]
[17:12:48.309]      __var DoDebugPortStop = 1 ;
[17:12:48.309]        // -> [DoDebugPortStop <= 0x00000001]
[17:12:48.309]      __var DP_CTRL_STAT = 0x4 ;
[17:12:48.309]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:12:48.310]      __var DP_SELECT = 0x8 ;
[17:12:48.310]        // -> [DP_SELECT <= 0x00000008]
[17:12:48.310]    </block>
[17:12:48.310]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:12:48.310]      // if-block "connectionFlash && DoOptionByteLoading"
[17:12:48.311]        // =>  FALSE
[17:12:48.311]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:12:48.311]    </control>
[17:12:48.311]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:12:48.313]      // if-block "DoDebugPortStop"
[17:12:48.313]        // =>  TRUE
[17:12:48.313]      <block atomic="false" info="">
[17:12:48.313]        WriteDP(DP_SELECT, 0x00000000);
[17:12:48.314]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:12:48.314]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:12:48.314]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:12:48.315]      </block>
[17:12:48.315]      // end if-block "DoDebugPortStop"
[17:12:48.315]    </control>
[17:12:48.315]  </sequence>
[17:12:48.315]  
[17:15:11.999]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:15:11.999]  
[17:15:12.025]  <debugvars>
[17:15:12.025]    // Pre-defined
[17:15:12.025]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:15:12.026]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:15:12.026]    __dp=0x00000000
[17:15:12.027]    __ap=0x00000000
[17:15:12.027]    __traceout=0x00000000      (Trace Disabled)
[17:15:12.027]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:15:12.027]    __FlashAddr=0x00000000
[17:15:12.027]    __FlashLen=0x00000000
[17:15:12.028]    __FlashArg=0x00000000
[17:15:12.028]    __FlashOp=0x00000000
[17:15:12.028]    __Result=0x00000000
[17:15:12.029]    
[17:15:12.029]    // User-defined
[17:15:12.029]    DbgMCU_CR=0x00000007
[17:15:12.029]    DbgMCU_APB1_Fz=0x00000000
[17:15:12.030]    DbgMCU_APB2_Fz=0x00000000
[17:15:12.030]    DoOptionByteLoading=0x00000000
[17:15:12.030]  </debugvars>
[17:15:12.030]  
[17:15:12.030]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:15:12.031]    <block atomic="false" info="">
[17:15:12.031]      Sequence("CheckID");
[17:15:12.031]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:15:12.032]          <block atomic="false" info="">
[17:15:12.032]            __var pidr1 = 0;
[17:15:12.032]              // -> [pidr1 <= 0x00000000]
[17:15:12.032]            __var pidr2 = 0;
[17:15:12.032]              // -> [pidr2 <= 0x00000000]
[17:15:12.032]            __var jep106id = 0;
[17:15:12.033]              // -> [jep106id <= 0x00000000]
[17:15:12.033]            __var ROMTableBase = 0;
[17:15:12.033]              // -> [ROMTableBase <= 0x00000000]
[17:15:12.033]            __ap = 0;      // AHB-AP
[17:15:12.033]              // -> [__ap <= 0x00000000]
[17:15:12.034]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:15:12.034]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:15:12.034]              // -> [ROMTableBase <= 0xF0000000]
[17:15:12.035]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:15:12.036]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:15:12.036]              // -> [pidr1 <= 0x00000004]
[17:15:12.037]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:15:12.037]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:15:12.038]              // -> [pidr2 <= 0x0000000A]
[17:15:12.038]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:15:12.038]              // -> [jep106id <= 0x00000020]
[17:15:12.038]          </block>
[17:15:12.038]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:15:12.038]            // if-block "jep106id != 0x20"
[17:15:12.039]              // =>  FALSE
[17:15:12.039]            // skip if-block "jep106id != 0x20"
[17:15:12.039]          </control>
[17:15:12.039]        </sequence>
[17:15:12.040]    </block>
[17:15:12.040]  </sequence>
[17:15:12.040]  
[17:15:12.052]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:15:12.052]  
[17:15:12.052]  <debugvars>
[17:15:12.053]    // Pre-defined
[17:15:12.053]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:15:12.053]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:15:12.053]    __dp=0x00000000
[17:15:12.054]    __ap=0x00000000
[17:15:12.054]    __traceout=0x00000000      (Trace Disabled)
[17:15:12.054]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:15:12.055]    __FlashAddr=0x00000000
[17:15:12.055]    __FlashLen=0x00000000
[17:15:12.055]    __FlashArg=0x00000000
[17:15:12.055]    __FlashOp=0x00000000
[17:15:12.056]    __Result=0x00000000
[17:15:12.056]    
[17:15:12.056]    // User-defined
[17:15:12.056]    DbgMCU_CR=0x00000007
[17:15:12.056]    DbgMCU_APB1_Fz=0x00000000
[17:15:12.056]    DbgMCU_APB2_Fz=0x00000000
[17:15:12.056]    DoOptionByteLoading=0x00000000
[17:15:12.056]  </debugvars>
[17:15:12.057]  
[17:15:12.057]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:15:12.058]    <block atomic="false" info="">
[17:15:12.058]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:15:12.059]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:15:12.059]    </block>
[17:15:12.059]    <block atomic="false" info="DbgMCU registers">
[17:15:12.059]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:15:12.061]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[17:15:12.061]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[17:15:12.061]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:15:12.062]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:15:12.063]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:15:12.063]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:15:12.064]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:15:12.064]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:15:12.064]    </block>
[17:15:12.064]  </sequence>
[17:15:12.064]  
[17:15:20.240]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:15:20.240]  
[17:15:20.240]  <debugvars>
[17:15:20.241]    // Pre-defined
[17:15:20.241]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:15:20.241]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:15:20.241]    __dp=0x00000000
[17:15:20.241]    __ap=0x00000000
[17:15:20.242]    __traceout=0x00000000      (Trace Disabled)
[17:15:20.242]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:15:20.242]    __FlashAddr=0x00000000
[17:15:20.242]    __FlashLen=0x00000000
[17:15:20.243]    __FlashArg=0x00000000
[17:15:20.243]    __FlashOp=0x00000000
[17:15:20.243]    __Result=0x00000000
[17:15:20.243]    
[17:15:20.243]    // User-defined
[17:15:20.243]    DbgMCU_CR=0x00000007
[17:15:20.243]    DbgMCU_APB1_Fz=0x00000000
[17:15:20.243]    DbgMCU_APB2_Fz=0x00000000
[17:15:20.243]    DoOptionByteLoading=0x00000000
[17:15:20.243]  </debugvars>
[17:15:20.245]  
[17:15:20.245]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:15:20.245]    <block atomic="false" info="">
[17:15:20.245]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:15:20.245]        // -> [connectionFlash <= 0x00000001]
[17:15:20.246]      __var FLASH_BASE = 0x40022000 ;
[17:15:20.246]        // -> [FLASH_BASE <= 0x40022000]
[17:15:20.246]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:15:20.246]        // -> [FLASH_CR <= 0x40022004]
[17:15:20.246]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:15:20.247]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:15:20.247]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:15:20.247]        // -> [LOCK_BIT <= 0x00000001]
[17:15:20.247]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:15:20.247]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:15:20.248]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:15:20.248]        // -> [FLASH_KEYR <= 0x4002200C]
[17:15:20.248]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:15:20.248]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:15:20.248]      __var FLASH_KEY2 = 0x02030405 ;
[17:15:20.249]        // -> [FLASH_KEY2 <= 0x02030405]
[17:15:20.249]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:15:20.249]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:15:20.249]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:15:20.249]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:15:20.249]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:15:20.250]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:15:20.250]      __var FLASH_CR_Value = 0 ;
[17:15:20.250]        // -> [FLASH_CR_Value <= 0x00000000]
[17:15:20.250]      __var DoDebugPortStop = 1 ;
[17:15:20.250]        // -> [DoDebugPortStop <= 0x00000001]
[17:15:20.250]      __var DP_CTRL_STAT = 0x4 ;
[17:15:20.251]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:15:20.251]      __var DP_SELECT = 0x8 ;
[17:15:20.251]        // -> [DP_SELECT <= 0x00000008]
[17:15:20.251]    </block>
[17:15:20.251]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:15:20.252]      // if-block "connectionFlash && DoOptionByteLoading"
[17:15:20.252]        // =>  FALSE
[17:15:20.252]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:15:20.252]    </control>
[17:15:20.253]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:15:20.253]      // if-block "DoDebugPortStop"
[17:15:20.253]        // =>  TRUE
[17:15:20.253]      <block atomic="false" info="">
[17:15:20.253]        WriteDP(DP_SELECT, 0x00000000);
[17:15:20.254]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:15:20.254]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:15:20.255]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:15:20.255]      </block>
[17:15:20.255]      // end if-block "DoDebugPortStop"
[17:15:20.255]    </control>
[17:15:20.255]  </sequence>
[17:15:20.255]  
[17:27:15.858]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:27:15.858]  
[17:27:15.858]  <debugvars>
[17:27:15.858]    // Pre-defined
[17:27:15.858]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:27:15.858]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[17:27:15.858]    __dp=0x00000000
[17:27:15.860]    __ap=0x00000000
[17:27:15.860]    __traceout=0x00000000      (Trace Disabled)
[17:27:15.860]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:27:15.860]    __FlashAddr=0x00000000
[17:27:15.860]    __FlashLen=0x00000000
[17:27:15.860]    __FlashArg=0x00000000
[17:27:15.860]    __FlashOp=0x00000000
[17:27:15.860]    __Result=0x00000000
[17:27:15.860]    
[17:27:15.860]    // User-defined
[17:27:15.860]    DbgMCU_CR=0x00000007
[17:27:15.860]    DbgMCU_APB1_Fz=0x00000000
[17:27:15.860]    DbgMCU_APB2_Fz=0x00000000
[17:27:15.860]    DoOptionByteLoading=0x00000000
[17:27:15.860]  </debugvars>
[17:27:15.860]  
[17:27:15.860]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:27:15.860]    <block atomic="false" info="">
[17:27:15.860]      Sequence("CheckID");
[17:27:15.864]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:27:15.864]          <block atomic="false" info="">
[17:27:15.864]            __var pidr1 = 0;
[17:27:15.864]              // -> [pidr1 <= 0x00000000]
[17:27:15.864]            __var pidr2 = 0;
[17:27:15.865]              // -> [pidr2 <= 0x00000000]
[17:27:15.865]            __var jep106id = 0;
[17:27:15.865]              // -> [jep106id <= 0x00000000]
[17:27:15.865]            __var ROMTableBase = 0;
[17:27:15.865]              // -> [ROMTableBase <= 0x00000000]
[17:27:15.866]            __ap = 0;      // AHB-AP
[17:27:15.866]              // -> [__ap <= 0x00000000]
[17:27:15.866]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:27:15.867]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:27:15.867]              // -> [ROMTableBase <= 0xF0000000]
[17:27:15.867]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:27:15.868]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:27:15.868]              // -> [pidr1 <= 0x00000004]
[17:27:15.868]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:27:15.869]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:27:15.869]              // -> [pidr2 <= 0x0000000A]
[17:27:15.870]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:27:15.870]              // -> [jep106id <= 0x00000020]
[17:27:15.870]          </block>
[17:27:15.870]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:27:15.871]            // if-block "jep106id != 0x20"
[17:27:15.871]              // =>  FALSE
[17:27:15.871]            // skip if-block "jep106id != 0x20"
[17:27:15.871]          </control>
[17:27:15.872]        </sequence>
[17:27:15.872]    </block>
[17:27:15.872]  </sequence>
[17:27:15.872]  
[17:27:15.884]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:27:15.884]  
[17:27:15.884]  <debugvars>
[17:27:15.884]    // Pre-defined
[17:27:15.885]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:27:15.885]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[17:27:15.885]    __dp=0x00000000
[17:27:15.886]    __ap=0x00000000
[17:27:15.886]    __traceout=0x00000000      (Trace Disabled)
[17:27:15.886]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:27:15.886]    __FlashAddr=0x00000000
[17:27:15.886]    __FlashLen=0x00000000
[17:27:15.886]    __FlashArg=0x00000000
[17:27:15.887]    __FlashOp=0x00000000
[17:27:15.887]    __Result=0x00000000
[17:27:15.887]    
[17:27:15.887]    // User-defined
[17:27:15.888]    DbgMCU_CR=0x00000007
[17:27:15.888]    DbgMCU_APB1_Fz=0x00000000
[17:27:15.888]    DbgMCU_APB2_Fz=0x00000000
[17:27:15.888]    DoOptionByteLoading=0x00000000
[17:27:15.889]  </debugvars>
[17:27:15.889]  
[17:27:15.889]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:27:15.889]    <block atomic="false" info="">
[17:27:15.889]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:27:15.890]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:27:15.891]    </block>
[17:27:15.891]    <block atomic="false" info="DbgMCU registers">
[17:27:15.891]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:27:15.892]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[17:27:15.893]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[17:27:15.893]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:27:15.894]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:27:15.894]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:27:15.895]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:27:15.895]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:27:15.896]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:27:15.897]    </block>
[17:27:15.897]  </sequence>
[17:27:15.897]  
[17:31:55.884]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:31:55.884]  
[17:31:55.884]  <debugvars>
[17:31:55.894]    // Pre-defined
[17:31:55.894]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:31:55.894]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[17:31:55.894]    __dp=0x00000000
[17:31:55.894]    __ap=0x00000000
[17:31:55.894]    __traceout=0x00000000      (Trace Disabled)
[17:31:55.894]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:31:55.894]    __FlashAddr=0x00000000
[17:31:55.894]    __FlashLen=0x00000000
[17:31:55.894]    __FlashArg=0x00000000
[17:31:55.894]    __FlashOp=0x00000000
[17:31:55.894]    __Result=0x00000000
[17:31:55.894]    
[17:31:55.894]    // User-defined
[17:31:55.894]    DbgMCU_CR=0x00000007
[17:31:55.894]    DbgMCU_APB1_Fz=0x00000000
[17:31:55.894]    DbgMCU_APB2_Fz=0x00000000
[17:31:55.894]    DoOptionByteLoading=0x00000000
[17:31:55.899]  </debugvars>
[17:31:55.899]  
[17:31:55.899]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:31:55.899]    <block atomic="false" info="">
[17:31:55.899]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:31:55.899]        // -> [connectionFlash <= 0x00000000]
[17:31:55.899]      __var FLASH_BASE = 0x40022000 ;
[17:31:55.899]        // -> [FLASH_BASE <= 0x40022000]
[17:31:55.899]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:31:55.899]        // -> [FLASH_CR <= 0x40022004]
[17:31:55.899]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:31:55.899]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:31:55.899]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:31:55.899]        // -> [LOCK_BIT <= 0x00000001]
[17:31:55.899]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:31:55.899]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:31:55.899]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:31:55.899]        // -> [FLASH_KEYR <= 0x4002200C]
[17:31:55.899]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:31:55.899]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:31:55.899]      __var FLASH_KEY2 = 0x02030405 ;
[17:31:55.899]        // -> [FLASH_KEY2 <= 0x02030405]
[17:31:55.899]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:31:55.899]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:31:55.899]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:31:55.899]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:31:55.899]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:31:55.899]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:31:55.899]      __var FLASH_CR_Value = 0 ;
[17:31:55.899]        // -> [FLASH_CR_Value <= 0x00000000]
[17:31:55.899]      __var DoDebugPortStop = 1 ;
[17:31:55.899]        // -> [DoDebugPortStop <= 0x00000001]
[17:31:55.899]      __var DP_CTRL_STAT = 0x4 ;
[17:31:55.899]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:31:55.899]      __var DP_SELECT = 0x8 ;
[17:31:55.899]        // -> [DP_SELECT <= 0x00000008]
[17:31:55.899]    </block>
[17:31:55.899]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:31:55.899]      // if-block "connectionFlash && DoOptionByteLoading"
[17:31:55.899]        // =>  FALSE
[17:31:55.899]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:31:55.899]    </control>
[17:31:55.899]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:31:55.899]      // if-block "DoDebugPortStop"
[17:31:55.899]        // =>  TRUE
[17:31:55.899]      <block atomic="false" info="">
[17:31:55.899]        WriteDP(DP_SELECT, 0x00000000);
[17:31:55.910]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:31:55.910]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:31:55.910]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:31:55.910]      </block>
[17:31:55.910]      // end if-block "DoDebugPortStop"
[17:31:55.910]    </control>
[17:31:55.910]  </sequence>
[17:31:55.910]  
