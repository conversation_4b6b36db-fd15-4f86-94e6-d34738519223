
T675C 000:002 SEGGER J-Link V6.32f Log File (0000ms, 0002ms total)
T675C 000:002 DLL Compiled: Jun 12 2018 14:46:40 (0000ms, 0002ms total)
T675C 000:002 Logging started @ 2025-08-14 20:03 (0000ms, 0002ms total)
T675C 000:002 JLINK_SetWarnOutHandler(...) (0000ms, 0002ms total)
T675C 000:002 JLINK_OpenEx(...)
Firmware: J-Link V11 compiled Apr 27 2041 16:36:21
Hardware: V11.00
S/N: 941000024
Feature(s): GDB, JFlash, FlashDL, RDI, FlashBP
TELNET listener socket opened on port 19021WEBSRV 
Starting webserver (0014ms, 0016ms total)
T675C 000:002 WEBSRV Webserver running on local port 19080 (0014ms, 0016ms total)
T675C 000:002   returns O.K. (0014ms, 0016ms total)
T675C 000:016 JLINK_GetEmuCaps()  returns 0xB9FF7BBF (0000ms, 0016ms total)
T675C 000:016 JLINK_TIF_GetAvailable(...) (0000ms, 0016ms total)
T675C 000:016 JLINK_SetErrorOutHandler(...) (0000ms, 0016ms total)
T675C 000:016 JLINK_ExecCommand("ProjectFile = "D:\2025work\STM32L072PRO\source-application\user\JLinkSettings.ini"", ...). Ref file found at: C:\Keil_v5\ARM\Segger\JLinkDevices.ref (0001ms, 0017ms total)
T675C 000:016 REF file references invalid XML file: C:\Program Files\SEGGER\JLink\JLinkDevices.xml (0001ms, 0017ms total)
T675C 000:016   returns 0x00 (0002ms, 0018ms total)
T675C 000:018 JLINK_ExecCommand("Device = STM32L072CBTx", ...). Device "STM32L072CB" selected.  returns 0x00 (0001ms, 0019ms total)
T675C 000:019 JLINK_ExecCommand("DisableConnectionTimeout", ...).   returns 0x01 (0000ms, 0019ms total)
T675C 000:019 JLINK_GetHardwareVersion()  returns 0x1ADB0 (0000ms, 0019ms total)
T675C 000:019 JLINK_GetDLLVersion()  returns 63206 (0000ms, 0019ms total)
T675C 000:019 JLINK_GetFirmwareString(...) (0000ms, 0019ms total)
T675C 000:019 JLINK_GetDLLVersion()  returns 63206 (0000ms, 0019ms total)
T675C 000:019 JLINK_GetCompileDateTime() (0000ms, 0019ms total)
T675C 000:019 JLINK_GetFirmwareString(...) (0000ms, 0019ms total)
T675C 000:019 JLINK_GetHardwareVersion()  returns 0x1ADB0 (0000ms, 0019ms total)
T675C 000:019 JLINK_TIF_Select(JLINKARM_TIF_SWD)  returns 0x00 (0001ms, 0020ms total)
T675C 000:020 JLINK_SetSpeed(1000) (0000ms, 0020ms total)
T675C 000:020 JLINK_GetId() >0x10B TIF>Found SW-DP with ID 0x0BC11477 >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF>
 >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF>
 >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF> >0x10B TIF>Found SW-DP with ID 0x0BC11477 >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF>Scanning AP map to find all available APs >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF>AP[1]: Stopped AP scan as end of AP map has been reachedAP[0]: AHB-AP (IDR: 0x04770031)
Iterating through AP map to find AHB-AP to use >0x42 TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF> >0x42 TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF>AP[0]: Core foundAP[0]: AHB-AP ROM base: 0xF0000000 >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x21 TIF>CPUID register: 0x410CC601. Implementer code: 0x41 (ARM)Found Cortex-M0 r0p1, Little endian.
 -- CPU_ReadMem(4 bytes @ 0xE000EDF0) -- CPU_WriteMem(4 bytes @ 0xE000EDF0) -- CPU_ReadMem(4 bytes @ 0xE0002000)FPUnit: 4 code (BP) slots and 0 literal slots -- CPU_ReadMem(4 bytes @ 0xE000EDFC) -- CPU_WriteMem(4 bytes @ 0xE000EDFC) -- CPU_ReadMem(4 bytes @ 0xE0001000) -- CPU_WriteMem(4 bytes @ 0xE0001000)CoreSight components:ROMTbl[0] @ F0000000 -- CPU_ReadMem(16 bytes @ 0xF0000000) -- CPU_ReadMem(16 bytes @ 0xE00FFFF0) -- CPU_ReadMem(16 bytes @ 0xE00FFFE0)
ROMTbl[0][0]: E00FF000, CID: B105100D, PID: 000BB4C0 ROM TableROMTbl[1] @ E00FF000 -- CPU_ReadMem(16 bytes @ 0xE00FF000) -- CPU_ReadMem(16 bytes @ 0xE000EFF0) -- CPU_ReadMem(16 bytes @ 0xE000EFE0)ROMTbl[1][0]: E000E000, CID: B105E00D, PID: 000BB008 SCS -- CPU_ReadMem(16 bytes @ 0xE0001FF0) -- CPU_ReadMem(16 bytes @ 0xE0001FE0)ROMTbl[1][1]: E0001000, CID: B105E00D, PID: 000BB00A DWT -- CPU_ReadMem(16 bytes @ 0xE0002FF0) -- CPU_ReadMem(16 bytes @ 0xE0002FE0)
ROMTbl[1][2]: E0002000, CID: B105E00D, PID: 000BB00B FPB -- CPU is running -- CPU_WriteMem(4 bytes @ 0xE000EDF0) -- CPU is running -- CPU_WriteMem(4 bytes @ 0xE000EDFC)Reset: Halt core after reset via DEMCR.VC_CORERESET. >0x35 TIF>Reset: Reset device via AIRCR.SYSRESETREQ. -- CPU is running -- CPU_WriteMem(4 bytes @ 0xE000ED0C) >0x0D TIF> >0x28 TIF> -- CPU_ReadMem(4 bytes @ 0xE000EDF0) -- CPU_ReadMem(4 bytes @ 0xE000EDF0) -- CPU is running -- CPU_WriteMem(4 bytes @ 0xE000EDF0) -- CPU is running
 -- CPU_WriteMem(4 bytes @ 0xE000EDFC) -- CPU_ReadMem(4 bytes @ 0xE000EDF0) -- CPU_WriteMem(4 bytes @ 0xE0002000) -- CPU_ReadMem(4 bytes @ 0xE000EDFC) -- CPU_ReadMem(4 bytes @ 0xE0001000) -- CPU_WriteMem(4 bytes @ 0xE0001000) -- CPU_WriteMem(20480 bytes @ 0x20000000) >0x0D TIF> >0x21 TIF>  returns 0x0BC11477 (0510ms, 0530ms total)
T675C 000:530 JLINK_GetDLLVersion()  returns 63206 (0000ms, 0530ms total)
T675C 000:530 JLINK_CORE_GetFound()  returns 0x60000FF (0000ms, 0530ms total)
T675C 000:530 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX) -- Value=0xF0000000  returns 0x00 (0000ms, 0530ms total)
T675C 000:530 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX) -- Value=0xF0000000  returns 0x00 (0000ms, 0530ms total)
T675C 000:530 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX) -- Value=0x00000000  returns 0x00 (0000ms, 0530ms total)
T675C 000:530 JLINK_ReadMemEx(0xE0041FF0, 0x0010 Bytes, ..., Flags = 0x02000004) -- CPU_ReadMem(16 bytes @ 0xE0041FF0) - Data: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  returns 0x10 (0002ms, 0532ms total)
T675C 000:532 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX) -- Value=0x00000000  returns 0x00 (0000ms, 0532ms total)
T675C 000:532 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX) -- Value=0x00000000  returns 0x00 (0000ms, 0532ms total)
T675C 000:532 JLINK_ReadMemEx(0xE0040FF0, 0x0010 Bytes, ..., Flags = 0x02000004) -- CPU_ReadMem(16 bytes @ 0xE0040FF0) - Data: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  returns 0x10 (0001ms, 0533ms total)
T675C 000:533 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX) -- Value=0xE0000000  returns 0x00 (0000ms, 0533ms total)
T675C 000:533 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX) -- Value=0xE0001000  returns 0x00 (0000ms, 0533ms total)
T675C 000:533 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX) -- Value=0xE0002000  returns 0x00 (0000ms, 0533ms total)
T675C 000:533 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX) -- Value=0xE000E000  returns 0x00 (0000ms, 0533ms total)
T675C 000:533 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX) -- Value=0xE000EDF0  returns 0x00 (0000ms, 0533ms total)
T675C 000:533 JLINK_GetDebugInfo(0x01 = Unknown) -- Value=0x00000000  returns 0x00 (0000ms, 0533ms total)
T675C 000:533 JLINK_ReadMemU32(0xE000ED00, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000ED00) - Data: 01 C6 0C 41  returns 0x01 (0001ms, 0534ms total)
T675C 000:534 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX) -- Value=0x00000000  returns 0x00 (0000ms, 0534ms total)
T675C 000:534 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)  returns JLINKARM_CM3_RESET_TYPE_NORMAL (0000ms, 0534ms total)
T675C 000:534 JLINK_Reset() -- CPU_ReadMem(4 bytes @ 0x20000000) -- CPU_WriteMem(4 bytes @ 0x20000000) -- CPU_WriteMem(4 bytes @ 0xE000EDF0) -- CPU_WriteMem(4 bytes @ 0xE000EDFC)Reset: Halt core after reset via DEMCR.VC_CORERESET. >0x35 TIF>Reset: Reset device via AIRCR.SYSRESETREQ. -- CPU_WriteMem(4 bytes @ 0xE000ED0C) >0x0D TIF> >0x28 TIF> -- CPU_ReadMem(4 bytes @ 0xE000EDF0) -- CPU_ReadMem(4 bytes @ 0xE000EDF0) -- CPU_WriteMem(4 bytes @ 0xE000EDF0) -- CPU_WriteMem(4 bytes @ 0xE000EDFC)
 -- CPU_ReadMem(4 bytes @ 0xE000EDF0) -- CPU_WriteMem(4 bytes @ 0xE0002000) -- CPU_ReadMem(4 bytes @ 0xE000EDFC) -- CPU_ReadMem(4 bytes @ 0xE0001000) -- CPU_WriteMem(4 bytes @ 0xE0001000) (0080ms, 0614ms total)
T675C 000:614 JLINK_ReadReg(R15 (PC))  returns 0x080000D8 (0000ms, 0614ms total)
T675C 000:614 JLINK_ReadReg(XPSR)  returns 0xF1000000 (0000ms, 0614ms total)
T675C 000:614 JLINK_Halt()  returns 0x00 (0000ms, 0614ms total)
T675C 000:614 JLINK_ReadMemU32(0xE000EDF0, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000EDF0) - Data: 03 00 03 00  returns 0x01 (0002ms, 0616ms total)
T675C 000:616 JLINK_WriteU32(0xE000EDF0, 0xA05F0003) -- CPU_WriteMem(4 bytes @ 0xE000EDF0)  returns 0x00 (0000ms, 0616ms total)
T675C 000:616 JLINK_WriteU32(0xE000EDFC, 0x01000000) -- CPU_WriteMem(4 bytes @ 0xE000EDFC)  returns 0x00 (0002ms, 0618ms total)
T675C 000:618 JLINK_GetHWStatus(...)  returns 0x00 (0000ms, 0618ms total)
T675C 000:618 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)  returns 0x04 (0000ms, 0618ms total)
T675C 000:618 JLINK_GetNumBPUnits(Type = 0xF0)  returns 0x2000 (0000ms, 0618ms total)
T675C 000:618 JLINK_GetNumWPUnits()  returns 0x02 (0000ms, 0618ms total)
T675C 000:618 JLINK_GetSpeed()  returns 0x3E8 (0000ms, 0618ms total)
T675C 000:618 JLINK_ReadMemU32(0xE000E004, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000E004) - Data: 00 00 00 00  returns 0x01 (0001ms, 0619ms total)
T675C 000:619 JLINK_ReadReg(R15 (PC))  returns 0x080000D8 (0000ms, 0619ms total)
T675C 000:619 JLINK_ReadReg(XPSR)  returns 0xF1000000 (0000ms, 0619ms total)
T675C 000:622 JLINK_SWO_Control(JLINKARM_SWO_CMD_GET_SPEED_INFO, ...)  returns 0x00 (0001ms, 0620ms total)
T675C 000:623 JLINK_SWO_Control(JLINKARM_SWO_CMD_STOP, ...)  returns 0x00 (0000ms, 0620ms total)
T675C 000:623 JLINK_SWO_Control(JLINKARM_SWO_CMD_START, ...) -- UART -- 1428571bps  returns 0x00 (0001ms, 0621ms total)
T675C 000:624 JLINK_WriteU32(0x00000010, 0x00000006) -- CPU_ReadMem(4 bytes @ 0x00000004)  returns 0x00 (0001ms, 0622ms total)
T675C 000:626 JLINK_WriteU32(0x000000F0, 0x00000002)  returns 0x00 (0000ms, 0622ms total)
T675C 000:626 JLINK_WriteU32(0x00000304, 0x00000000)  returns 0x00 (0000ms, 0622ms total)
T675C 000:626 JLINK_WriteU32(0xE0000FB0, 0xC5ACCE55) -- CPU_ReadMem(4 bytes @ 0xE000ED90) -- CPU_ReadMem(4 bytes @ 0xE000ED94) -- -------------------------------------- -- Start of determining dirty areas in flash cache -- CPU_ReadMem(128 bytes @ 0x08000000) -- Updating C cache (128 bytes @ 0x08000000) -- Read from C cache (128 bytes @ 0x08000000) -- Updating flash cache (128 bytes @ 0x08000000) -- CPU_ReadMem(128 bytes @ 0x08000080) -- Updating C cache (128 bytes @ 0x08000080)
 -- Read from C cache (128 bytes @ 0x08000080) -- Updating flash cache (128 bytes @ 0x08000080) -- CPU_ReadMem(128 bytes @ 0x08000300) -- Updating C cache (128 bytes @ 0x08000300) -- Read from C cache (128 bytes @ 0x08000300) -- Updating flash cache (128 bytes @ 0x08000300) -- End of determining dirty areas -- Start of preparing flash programming -- Calculating RAM usage -- RAM usage = 2588 Bytes -- Preserving CPU registers -- Preparing memory -- Preparing target -- Downloading RAMCode
 -- Checking target RAM -- Preparing RAMCode -- End of preparing flash programming -- CPU speed could not be measured. -- Start of comparing flash -- CRC check was estimated as fastest method -- Comparing range 0x8000000 - 0x80000FF (2 Sectors, 256 Bytes), using multi-block CRC calculation -- All CRCs match -- Comparing range 0x8000300 - 0x800037F (1 Sector, 128 Bytes), using multi-block CRC calculation -- All CRCs match
 -- Comparing range 0x8000000 - 0x80000FF (2 Sectors, 256 Bytes), using alternative multi-block CRC calculation -- All CRCs match -- Comparing range 0x8000300 - 0x800037F (1 Sector, 128 Bytes), using alternative multi-block CRC calculation -- All CRCs match -- End of comparing flash -- Start of erasing sectors -- End of erasing sectors -- Start of flash programming -- End of flash programming -- Start of verifying flash -- End of verifying flash -- Start of restoring -- Restoring RAMCode
 -- Restore target -- Restore memory -- Restoring CPU registers -- End of restoring -- Bank 0 @ 0x08000000: Skipped. Contents already match -- Bank 0 @ 0x08000000: Skipped. Contents already match -- CPU_WriteMem(4 bytes @ 0xE0000FB0)  returns 0x00 (0106ms, 0728ms total)
T675C 000:732 JLINK_WriteU32(0xE0000E80, 0x0001000D)  returns 0x00 (0002ms, 0730ms total)
T675C 000:734 JLINK_WriteU32(0xE0000E00, 0xFFFFFFFF)  returns 0x00 (0000ms, 0730ms total)
T675C 000:734 JLINK_WriteU32(0xE0000E40, 0x00000008)  returns 0x00 (0000ms, 0730ms total)
T675C 000:734 JLINK_WriteU32(0xE0001000, 0x0001061F)  returns 0x00 (0000ms, 0730ms total)
T675C 000:850 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)  returns JLINKARM_CM3_RESET_TYPE_NORMAL (0000ms, 0730ms total)
T675C 000:850 JLINK_Reset() -- CPU_ReadMem(4 bytes @ 0x2000073C) -- CPU_WriteMem(4 bytes @ 0x2000073C) -- CPU_WriteMem(4 bytes @ 0xE0000E00) -- CPU_WriteMem(4 bytes @ 0xE0000E40) -- CPU_WriteMem(4 bytes @ 0xE0000E80) -- CPU_WriteMem(4 bytes @ 0xE0001000) -- CPU_WriteMem(4 bytes @ 0xE000EDF0) -- CPU_WriteMem(4 bytes @ 0xE000EDFC)Reset: Halt core after reset via DEMCR.VC_CORERESET. >0x35 TIF>Reset: Reset device via AIRCR.SYSRESETREQ. -- CPU_WriteMem(4 bytes @ 0xE000ED0C) >0x0D TIF> >0x28 TIF>
 -- CPU_ReadMem(4 bytes @ 0xE000EDF0) -- CPU_ReadMem(4 bytes @ 0xE000EDF0) -- CPU_WriteMem(4 bytes @ 0xE000EDF0) -- CPU_WriteMem(4 bytes @ 0xE000EDFC) -- CPU_ReadMem(4 bytes @ 0xE000EDF0) -- CPU_WriteMem(4 bytes @ 0xE0002000) -- CPU_ReadMem(4 bytes @ 0xE000EDFC) -- CPU_ReadMem(4 bytes @ 0xE0001000) -- CPU_WriteMem(4 bytes @ 0xE0001000) (0079ms, 0809ms total)
T675C 000:930 JLINK_WriteU32(0xE000EDFC, 0x01000000) -- CPU_WriteMem(4 bytes @ 0xE000EDFC)  returns 0x00 (0001ms, 0810ms total)
T675C 000:931 JLINK_ReadReg(R15 (PC))  returns 0x080000D8 (0000ms, 0810ms total)
T675C 000:931 JLINK_ReadReg(XPSR)  returns 0xF1000000 (0000ms, 0810ms total)
T675C 000:937 JLINK_ReadMemEx(0x080000D8, 0x003C Bytes, ..., Flags = 0x02000000) -- CPU_ReadMem(128 bytes @ 0x080000C0) -- Updating C cache (128 bytes @ 0x080000C0) -- Read from C cache (60 bytes @ 0x080000D8) - Data: 04 48 80 47 04 48 00 47 FE E7 FE E7 FE E7 FE E7 ...  returns 0x3C (0002ms, 0812ms total)
T675C 000:939 JLINK_ReadMemEx(0x080000D8, 0x0002 Bytes, ..., Flags = 0x02000000) -- Read from flash cache (2 bytes @ 0x080000D8) - Data: 04 48  returns 0x02 (0000ms, 0812ms total)
T675C 000:939 JLINK_ReadMemEx(0x080000DA, 0x0002 Bytes, ..., Flags = 0x02000000) -- Read from flash cache (2 bytes @ 0x080000DA) - Data: 80 47  returns 0x02 (0000ms, 0812ms total)
T675C 000:939 JLINK_ReadMemEx(0x080000DA, 0x0002 Bytes, ..., Flags = 0x02000000) -- Read from flash cache (2 bytes @ 0x080000DA) - Data: 80 47  returns 0x02 (0000ms, 0812ms total)
T675C 000:939 JLINK_ReadMemEx(0x080000DC, 0x003C Bytes, ..., Flags = 0x02000000) -- Read from C cache (60 bytes @ 0x080000DC) - Data: 04 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...  returns 0x3C (0000ms, 0812ms total)
T675C 000:939 JLINK_ReadMemEx(0x080000DC, 0x0002 Bytes, ..., Flags = 0x02000000) -- Read from flash cache (2 bytes @ 0x080000DC) - Data: 04 48  returns 0x02 (0002ms, 0814ms total)
T675C 001:283 JLINK_WriteU32(0xE0001008, 0x00000000)  returns 0x00 (0000ms, 0814ms total)
T675C 001:283 JLINK_WriteU32(0xE000100C, 0x00000000)  returns 0x00 (0001ms, 0815ms total)
T675C 001:284 JLINK_WriteU32(0xE0001010, 0x00000000)  returns 0x00 (0000ms, 0815ms total)
T675C 001:285 JLINK_WriteU32(0xE0001014, 0x00000000)  returns 0x00 (0000ms, 0815ms total)
T675C 001:285 JLINK_WriteU32(0xE0001018, 0x00000000)  returns 0x00 (0000ms, 0815ms total)
T675C 004:049 JLINK_ReadMemEx(0x080000DC, 0x003C Bytes, ..., Flags = 0x02000000) -- Read from C cache (60 bytes @ 0x080000DC) - Data: 04 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...  returns 0x3C (0001ms, 0816ms total)
T675C 004:050 JLINK_ReadMemEx(0x080000DC, 0x0002 Bytes, ..., Flags = 0x02000000) -- Read from flash cache (2 bytes @ 0x080000DC) - Data: 04 48  returns 0x02 (0000ms, 0816ms total)
T675C 004:050 JLINK_ReadMemEx(0x080000DE, 0x0002 Bytes, ..., Flags = 0x02000000) -- Read from flash cache (2 bytes @ 0x080000DE) - Data: 00 47  returns 0x02 (0000ms, 0816ms total)
T4D3C 004:834 JLINK_ReadMemEx(0x080000D8, 0x0002 Bytes, ..., Flags = 0x02000000) -- Read from flash cache (2 bytes @ 0x080000D8) - Data: 04 48  returns 0x02 (0000ms, 0816ms total)
T4D3C 004:834 JLINK_SetBPEx(Addr = 0x08000C08, Type = 0xFFFFFFF2)  returns 0x00000001 (0000ms, 0816ms total)
T4D3C 004:834 JLINK_WriteU32(0xE0000E80, 0x0001000F)  returns 0x00 (0000ms, 0816ms total)
T4D3C 004:834 JLINK_SWO_Control(JLINKARM_SWO_CMD_GET_NUM_BYTES, ...)  returns 0x00 (0000ms, 0816ms total)
T4D3C 004:834 JLINK_Go() -- CPU_WriteMem(4 bytes @ 0xE0002000) -- CPU_ReadMem(4 bytes @ 0xE0001000) -- CPU_WriteMem(4 bytes @ 0xE0000E80) -- CPU_WriteMem(4 bytes @ 0xE0001000) -- CPU_WriteMem(4 bytes @ 0xE0001008) -- CPU_WriteMem(4 bytes @ 0xE000100C) -- CPU_WriteMem(4 bytes @ 0xE0001010) -- CPU_WriteMem(4 bytes @ 0xE0001014) -- CPU_WriteMem(4 bytes @ 0xE0001018) -- CPU_WriteMem(4 bytes @ 0xE0002008) -- CPU_WriteMem(4 bytes @ 0xE000200C) -- CPU_WriteMem(4 bytes @ 0xE0002010)
 -- CPU_WriteMem(4 bytes @ 0xE0002014) -- CPU_WriteMem(4 bytes @ 0xE0001004) (0007ms, 0823ms total)
T4D3C 004:942 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 004:944 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 004:944 JLINK_IsHalted()  returns FALSE (0001ms, 0824ms total)
T4D3C 005:045 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 005:046 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 005:046 JLINK_IsHalted()  returns FALSE (0000ms, 0823ms total)
T4D3C 005:148 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 005:150 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 005:150 JLINK_IsHalted()  returns FALSE (0001ms, 0824ms total)
T4D3C 005:251 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 005:254 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 005:254 JLINK_IsHalted()  returns FALSE (0001ms, 0824ms total)
T4D3C 005:355 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 005:357 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 005:369 JLINK_IsHalted()  returns FALSE (0001ms, 0824ms total)
T4D3C 005:471 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 005:473 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 005:473 JLINK_IsHalted()  returns FALSE (0001ms, 0824ms total)
T4D3C 005:575 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 005:578 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 005:578 JLINK_IsHalted()  returns FALSE (0001ms, 0824ms total)
T4D3C 005:680 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 005:682 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 005:682 JLINK_IsHalted()  returns FALSE (0001ms, 0824ms total)
T4D3C 005:785 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 005:786 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 005:786 JLINK_IsHalted()  returns FALSE (0002ms, 0825ms total)
T4D3C 005:889 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 005:892 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 005:896 JLINK_IsHalted()  returns FALSE (0001ms, 0824ms total)
T4D3C 005:997 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 005:999 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 005:999 JLINK_IsHalted()  returns FALSE (0000ms, 0823ms total)
T4D3C 006:100 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 006:102 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 006:102 JLINK_IsHalted()  returns FALSE (0000ms, 0823ms total)
T4D3C 006:203 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 006:205 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 006:205 JLINK_IsHalted()  returns FALSE (0001ms, 0824ms total)
T4D3C 006:307 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 006:308 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 006:308 JLINK_IsHalted()  returns FALSE (0000ms, 0823ms total)
T4D3C 006:409 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 006:411 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 006:419 JLINK_IsHalted()  returns FALSE (0001ms, 0824ms total)
T4D3C 006:522 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 006:524 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 006:524 JLINK_IsHalted()  returns FALSE (0000ms, 0823ms total)
T4D3C 006:626 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 006:628 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 006:628 JLINK_IsHalted()  returns FALSE (0001ms, 0824ms total)
T4D3C 006:731 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 006:733 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 006:733 JLINK_IsHalted()  returns FALSE (0001ms, 0824ms total)
T4D3C 006:835 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 006:837 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 006:837 JLINK_IsHalted()  returns FALSE (0001ms, 0824ms total)
T4D3C 006:938 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 006:940 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 006:943 JLINK_IsHalted()  returns FALSE (0001ms, 0824ms total)
T4D3C 007:045 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 007:047 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 007:047 JLINK_IsHalted()  returns FALSE (0000ms, 0823ms total)
T4D3C 007:148 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 007:151 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 007:151 JLINK_IsHalted()  returns FALSE (0000ms, 0823ms total)
T4D3C 007:252 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 007:254 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 007:254 JLINK_IsHalted()  returns FALSE (0000ms, 0823ms total)
T4D3C 007:355 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 007:357 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 007:357 JLINK_IsHalted()  returns FALSE (0000ms, 0823ms total)
T4D3C 007:458 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 007:460 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 007:465 JLINK_IsHalted()  returns FALSE (0000ms, 0823ms total)
T4D3C 007:567 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 007:568 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 007:568 JLINK_IsHalted()  returns FALSE (0000ms, 0823ms total)
T4D3C 007:670 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 007:671 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 007:671 JLINK_IsHalted()  returns FALSE (0000ms, 0823ms total)
T4D3C 007:772 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 007:774 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 007:774 JLINK_IsHalted()  returns FALSE (0000ms, 0823ms total)
T4D3C 007:876 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0823ms total)
T4D3C 007:878 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0823ms total)
T4D3C 007:878 JLINK_IsHalted()  returns FALSE (0000ms, 0823ms total)
T4D3C 007:978 JLINK_Halt()  returns 0x00 (0007ms, 0830ms total)
T4D3C 007:985 JLINK_IsHalted()  returns TRUE (0000ms, 0830ms total)
T4D3C 007:985 JLINK_IsHalted()  returns TRUE (0000ms, 0830ms total)
T4D3C 007:985 JLINK_IsHalted()  returns TRUE (0000ms, 0830ms total)
T4D3C 007:985 JLINK_ReadReg(R15 (PC))  returns 0x08005AF0 (0000ms, 0830ms total)
T4D3C 007:985 JLINK_ReadReg(XPSR)  returns 0x11000003 (0000ms, 0830ms total)
T4D3C 007:985 JLINK_SWO_Read(..., Offset = 0x00, NumBytes = 0x400000) - Data:  NumBytesRead = 0x00 (0000ms, 0830ms total)
T4D3C 007:987 JLINK_SWO_Control(JLINKARM_SWO_CMD_FLUSH, ...)  returns 0x00 (0000ms, 0830ms total)
T4D3C 007:987 JLINK_ClrBPEx(BPHandle = 0x00000001)  returns 0x00 (0000ms, 0830ms total)
T4D3C 007:987 JLINK_ReadMemU32(0xE000ED30, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000ED30) - Data: 01 00 00 00  returns 0x01 (0001ms, 0831ms total)
T4D3C 007:988 JLINK_ReadMemU32(0xE0001028, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001028) - Data: 00 00 00 00  returns 0x01 (0000ms, 0831ms total)
T4D3C 007:988 JLINK_ReadMemU32(0xE0001038, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001038) - Data: 00 00 00 00  returns 0x01 (0001ms, 0832ms total)
T675C 008:651 JLINK_Close() -- CPU_WriteMem(4 bytes @ 0xE0002008) -- CPU_ReadMem(4 bytes @ 0xE0001000) -- CPU_WriteMem(4 bytes @ 0xE0001000) >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x28 TIF> >0x0D TIF> >0x21 TIF> >0x0D TIF> >0x28 TIF> (0023ms, 0855ms total)
T675C 008:651  (0023ms, 0855ms total)
T675C 008:651 Closed (0023ms, 0855ms total)
