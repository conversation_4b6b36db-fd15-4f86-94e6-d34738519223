#ifndef __LCD12864_H__
#define __LCD12864_H__

#include "system.h"

#define LCD_WIDTH  128
#define LCD_HEIGHT 64

typedef enum
{
  FONT_0808B= 0,
  FONT_1616B ,
  FONT_2424B,
  FONT_DEFAULT = FONT_1616B,
}FONT_TYPE;
void lcd_init(void);
void lcd_refresh(void);
void lcd_clear(uint8_t mode);
void lcd_draw_line(uint8_t s_x, uint8_t e_x, uint8_t s_y, uint8_t e_y, uint8_t mode);//画线
void lcd_draw_point(uint8_t x, uint8_t y, uint8_t mode);//画点
void lcd_draw_pixel(uint8_t x, uint8_t y, uint8_t mode);//画像素        
void lcd_draw_char(uint8_t x, uint8_t y, char c, uint8_t mode, FONT_TYPE font_type); //画字符 
void lcd_draw_image(uint8_t *image, uint8_t *width, uint8_t *height, uint8_t x, uint8_t y); //画图片
void lcd_write_image(uint8_t *image, uint8_t *width, uint8_t *height, uint16_t x, uint16_t y) ;//写图片
void lcd_show_string(uint8_t x, uint8_t y, char *str, uint8_t mode, FONT_TYPE font_type); //显示字符串  
void	disp_row(void);     //竖条
void	disp_col(void);    //横条
 void dispstr(uint8_t d);
void lcd_backlight_ctrl(uint8_t opt);
void display_power_voltage(void);
void lcd_show_char(uint8_t x, uint8_t y, uint8_t *dat, uint8_t count, uint8_t mode, FONT_TYPE font_type);
void display_flow_rate(void);
void display_fourG_volage(void);
void display_rotate(void);
void display_battery_voltage(void);
#endif
