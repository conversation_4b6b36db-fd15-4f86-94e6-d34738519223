#ifndef __MODBUS_RTU_H__
#define __MODBUS_RTU_H__

#include "system.h"
#include <stdint.h>

typedef enum
{
	MODBUS_ERR_NONE = 0,
	MODBUS_ERR_FAIL,
	MODBUS_ERR_CRC,
	MODBUS_ERR_TIMEOUT,
}MODBUS_ERR;


uint16_t modbus_crc16(uint8_t *buf,uint8_t len);
MODBUS_ERR modbus_rtu_read(uint8_t addr,uint16_t reg_addr,uint16_t reg_count,uint8_t *out_buf,uint8_t *out_count,uint16_t timeout);
MODBUS_ERR modbus_rtu_write(uint8_t addr,uint16_t reg_addr,uint16_t dat,uint16_t timeout);

#endif
