#include <stdio.h>
#include <stdint.h>
#include <string.h>

// 全局变量定义
int g_posFlow = 0;          // 正累计流量（全局变量）
int g_negFlow = 0;          // 负累计流量（全局变量）
int g_netFlow = 0;          // 净累计流量（全局变量）
float g_instFlow = 0.0f;    // 瞬时流量（全局变量，浮点型）
float g_velocity = 0.0f;    // 流速（全局变量，浮点型）
char g_esnStr[6] = {0};     // ESN字符串（全局变量）
uint8_t g_water_meter_read_flag = 0;  // 水表读取成功标志：0-失败，1-成功

// 外部函数声明
// void serial_485_send_dat(uint8_t *dat, uint8_t len);  // 485串口发送函数（外部实现）

/**
 * @brief 发送读取水表数据的固定报文
 * @note 发送Modbus RTU命令：01 03 05 A1 00 1C 15 2D
 *       地址：01，功能码：03（读保持寄存器），起始地址：05A1，寄存器数量：001C
 */
void sendWaterMeterReadCommand(void)
{
    uint8_t readCommand[] = {0x01, 0x03, 0x05, 0xA1, 0x00, 0x1C, 0x15, 0x2D};

    // 打印发送的命令（调试用）
    printf("发送读取命令: ");
    for (int i = 0; i < sizeof(readCommand); i++) {
        printf("%02X ", readCommand[i]);
    }
    printf("\n");

    // 实际发送命令（需要实现serial_485_send_dat函数）
    // serial_485_send_dat(readCommand, sizeof(readCommand));
}

/**
 * @brief 解析水表数据
 * @param buf 接收到的数据缓冲区
 * @param len 数据长度
 * @return 1-解析成功，0-解析失败
 * @note 解析Modbus RTU响应数据，提取水表各项参数并存储到全局变量中
 */
int parseWaterMeterData(const uint8_t *buf, int len)
{
    // 检查数据长度是否足够（至少包含地址+功能码+字节数+CRC）
    if (len < 5) {
        printf("错误：数据长度不足，需要至少5字节，实际收到%d字节\n", len);
        return 0;
    }

    // 跳过 Modbus RTU 帧头 (地址1字节 + 功能码1字节 + 字节数1字节)
    const uint8_t *data = buf + 3;
    int dataLen = len - 5; // 去掉帧头3字节和CRC校验2字节

    // 宏定义：从数据中提取16位和32位寄存器值
    // 注意：Modbus使用大端字节序（高字节在前）
    #define REG16(i)   ((data[(i)*2] << 8) | data[(i)*2+1])        // 提取16位寄存器
    #define REG32(i)   ((REG16(i+1) << 16) | REG16(i))             // 提取32位寄存器（低字在后）

    // 提取正累计流量 (寄存器REG1464–1465 对应数据偏移22,23)
    int posRaw = REG32(22);

    // 提取净累计流量 (寄存器REG1443–1444 对应数据偏移1,2)
    int netRaw = REG32(1);

    // 计算负累计流量 = 正累计流量 - 净累计流量
    int negRaw = posRaw - netRaw;

    // 提取瞬时流量 (IEEE754浮点格式, 寄存器REG1447–1448 对应数据偏移5,6)
    // 注意：IEEE754遵从低位低字节在前排放原则，所以REG16(5)是低字，REG16(6)是高字
    uint32_t instRaw = ((uint32_t)REG16(6) << 16) | REG16(5);
    float instVal;
    memcpy(&instVal, &instRaw, sizeof(float));  // 将32位整数转换为IEEE754浮点数

    // 提取流速 (IEEE754浮点格式, 寄存器REG1449–1450 对应数据偏移7,8)
    // 注意：IEEE754遵从低位低字节在前排放原则，所以REG16(7)是低字，REG16(8)是高字
    uint32_t velRaw = ((uint32_t)REG16(8) << 16) | REG16(7);
    float velVal;
    memcpy(&velVal, &velRaw, sizeof(float));    // 将32位整数转换为IEEE754浮点数

    // 提取ESN设备序列号 (BCD编码格式, 寄存器REG1466–1467 对应数据偏移24,25)
    uint32_t esnRaw = ((uint32_t)REG16(25) << 16) | REG16(24);

    // 将BCD编码的ESN转换为字符串
    char tmp[11] = {0};  // 临时缓冲区，最多8位BCD数字+结束符
    int idx = 0;

    // 从高位到低位提取每个BCD数字（每4位表示一个十进制数字）
    for (int i = 7; i >= 0; i--) {
        int digit = (esnRaw >> (i*4)) & 0xF;  // 提取第i个4位BCD数字
        tmp[idx++] = '0' + digit;             // 转换为ASCII字符
    }
    tmp[idx] = '\0';  // 字符串结束符

    // 去掉前导零，保留至少一位数字
    int start = 0;
    while (tmp[start] == '0' && start < idx-1) start++;
    strcpy(g_esnStr, tmp + start);  // 复制到全局ESN字符串变量

    // 将解析结果存储到全局变量中
    g_posFlow = posRaw;           // 正累计流量（整数）
    g_negFlow = negRaw;           // 负累计流量（整数）
    g_netFlow = netRaw;           // 净累计流量（整数）
    g_instFlow = instVal;         // 瞬时流量（浮点型，保持原精度）
    g_velocity = velVal;          // 流速（浮点型，保持原精度）

    // 解析成功，设置成功标志
    g_water_meter_read_flag = 1;
    printf("数据解析成功，成功标志已设置为1\n");

    return 1;  // 返回成功
}

/**
 * @brief 主函数 - 演示水表数据读取和解析流程
 */
int main() {
    int retry_count = 0;  // 重试计数器
    const int MAX_RETRY = 2;  // 最大重试次数

    // 模拟接收到的水表数据（示例数据）
    uint8_t recvData[] = {
        0x01,0x03,0x38,
        0x00,0x01,
        0x03,0x34,0x00,0x00,
        0x00,0x03,
        0x00,0x00,
        0x00,0x01,0x44,0x4D,
        0x00,0x00,0x44,0x4D,
        0x00,0x00,0x3F,0x80,
        0x00,0x00,0x44,0x4D,
        0x62,0x40,0x40,0x64,
        0x00,0x34,0x00,0x34,0x00,0x00,0x01,0x7E,0x00,0x30,0x08,0x59,0x00,0x00,0x03,
        0x34,0x00,0x00,
        0x22,0x11,0x90,0x11,
        0x00,0x13,0x00,0x00,0x7F,0xB7
    };

    int len = sizeof(recvData);

    // 初始化成功标志为0
    g_water_meter_read_flag = 0;

    // 读取和解析水表数据，支持重试机制
    do {
        retry_count++;
        printf("\n=== 第%d次尝试读取水表数据 ===\n", retry_count);

        // 发送读取水表数据命令
        sendWaterMeterReadCommand();

        // 模拟延时等待响应
        // rt_thread_mdelay(1000);
        printf("等待水表响应（模拟延时1s）\n");

        // 解析接收到的数据，结果存储在全局变量中
        if(parseWaterMeterData(recvData, len)) {
            printf("第%d次尝试：数据解析成功！\n", retry_count);
            break;  // 解析成功，跳出循环
        } else {
            printf("第%d次尝试：数据解析失败！\n", retry_count);

            // 如果达到最大重试次数，设置失败标志并退出
            if(retry_count >= MAX_RETRY) {
                g_water_meter_read_flag = 0;
                printf("错误：已达到最大重试次数(%d次)，水表读取失败！\n", MAX_RETRY);
                printf("调试信息：成功标志已设置为0\n");
                break;
            } else {
                printf("准备进行第%d次重试...\n", retry_count + 1);
            }
        }
    } while(retry_count < MAX_RETRY);

    // 打印最终结果
    printf("\n=== 水表数据读取结果 ===\n");
    printf("读取状态: %s\n", g_water_meter_read_flag ? "成功" : "失败");

    if(g_water_meter_read_flag) {
        // 从全局变量读取解析结果
        printf("正累计流量: %d\n", g_posFlow);
        printf("负累计流量: %d\n", g_negFlow);
        printf("净累计流量: %d\n", g_netFlow);
        printf("瞬时流量: %.2f\n", g_instFlow);      // 浮点型，保留两位小数
        printf("流速: %.2f\n", g_velocity);          // 浮点型，保留两位小数
        printf("ESN: %s\n", g_esnStr);
    } else {
        printf("由于读取失败，无法获取有效的水表数据\n");
    }

    printf("程序执行完毕\n");
    return 0;
}

// int new_daoSheng_protocol_parsing(SeRIAL serial) {
//     int retry_count = 0;  // 重试计数器
//     const int MAX_RETRY = 2;  // 最大重试次数

//     // 模拟接收到的水表数据（示例数据）
//     uint8_t recvData[] = serial.rx_buffer;
//     int len = serial.rx_count;

//     // 初始化成功标志为0
//     g_water_meter_read_flag = 0;

//     // 读取和解析水表数据，支持重试机制
//     do {
//         retry_count++;
//         printf("\n=== 第%d次尝试读取水表数据 ===\n", retry_count);

//         // 发送读取水表数据命令
//         sendWaterMeterReadCommand();

//         // 模拟延时等待响应
//         // rt_thread_mdelay(1000);
//         printf("等待水表响应（模拟延时1s）\n");

//         // 解析接收到的数据，结果存储在全局变量中
//         if(parseWaterMeterData(recvData, len)) {
//             printf("第%d次尝试：数据解析成功！\n", retry_count);
//             break;  // 解析成功，跳出循环
//         } else {
//             printf("第%d次尝试：数据解析失败！\n", retry_count);

//             // 如果达到最大重试次数，设置失败标志并退出
//             if(retry_count >= MAX_RETRY) {
//                 g_water_meter_read_flag = 0;
//                 printf("错误：已达到最大重试次数(%d次)，水表读取失败！\n", MAX_RETRY);
//                 printf("调试信息：成功标志已设置为0\n");
//                 break;
//             } else {
//                 printf("准备进行第%d次重试...\n", retry_count + 1);
//             }
//         }
//     } while(retry_count < MAX_RETRY);

//     // 打印最终结果
//     printf("\n=== 水表数据读取结果 ===\n");
//     printf("读取状态: %s\n", g_water_meter_read_flag ? "成功" : "失败");

//     if(g_water_meter_read_flag) {
//         // 从全局变量读取解析结果
//         printf("正累计流量: %d\n", g_posFlow);
//         printf("负累计流量: %d\n", g_negFlow);
//         printf("净累计流量: %d\n", g_netFlow);
//         printf("瞬时流量: %d\n", g_instFlow);
//         printf("流速: %d\n", g_velocity);
//         printf("ESN: %s\n", g_esnStr);
//     } else {
//         printf("由于读取失败，无法获取有效的水表数据\n");
//     }

//     printf("程序执行完毕\n");
//     return 0;
// }
