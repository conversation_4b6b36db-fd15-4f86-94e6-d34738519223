#include <stdio.h>
#include <stdint.h>
#include <string.h>

// 全局变量定义
int32_t g_posFlow = 0;
int32_t g_negFlow = 0;
int32_t g_netFlow = 0;
int32_t g_instFlow = 0;
int32_t g_velocity = 0;
char g_esnStr[6] = {0};
//定义一个字节变量，水表是否读取成功
uint8_t g_water_meter_read_flag = 0;

// 外部函数声明


// 发送读取水表数据的固定报文
void sendWaterMeterReadCommand(void)
{
    uint8_t readCommand[] = {0x01, 0x03, 0x05, 0xA1, 0x00, 0x1C, 0x15, 0x2D};
    //打印readCommand
    for (int i = 0; i < sizeof(readCommand); i++) {
        printf("%02X ", readCommand[i]);
    }
    printf("\n");
    // serial_485_send_dat(readCommand, sizeof(readCommand));
}

// 解析函数
int  parseWaterMeterData(const uint8_t *buf, int len)
{
    if (len < 5) {
        printf("数据长度不足！\n");
        return 0;
    }

    // 跳过 Modbus RTU 帧头 (地址1 + 功能码1 + 字节数1)
    const uint8_t *data = buf + 3;
    int dataLen = len - 5; // 去掉头3和CRC2

    // 宏：取寄存器 (大端，高字节在前；32位组合时低字在后)
    #define REG16(i)   ((data[(i)*2] << 8) | data[(i)*2+1])
    #define REG32(i)   ((REG16(i+1) << 16) | REG16(i))

    // 正累计流量 (REG1464–1465 → offset=22,23)
    int posRaw = REG32(22);

    // 净累计流量 (REG1443–1444 → offset=1,2) 只用于计算负累计流量
    int netRaw = REG32(1);

    // 负累计流量 = 正 - 净
    int negRaw = posRaw - netRaw;

    // 瞬时流量 (IEEE754 float, REG1447–1448 → offset=5,6)
    uint32_t instRaw = ((uint32_t)REG16(5) << 16) | REG16(6);
    float instVal;
    memcpy(&instVal, &instRaw, sizeof(float));

    // 流速 (IEEE754 float, REG1449–1450 → offset=7,8)
    uint32_t velRaw = ((uint32_t)REG16(7) << 16) | REG16(8);
    float velVal;
    memcpy(&velVal, &velRaw, sizeof(float));

    // ESN (BCD, REG1466–1467 → offset=24,25)
    uint32_t esnRaw = ((uint32_t)REG16(25) << 16) | REG16(24);

    // 把 BCD 转成字符串
    char tmp[11] = {0};
    int idx = 0;
    for (int i = 7; i >= 0; i--) {
        int digit = (esnRaw >> (i*4)) & 0xF;
        tmp[idx++] = '0' + digit;
    }
    tmp[idx] = '\0';

    // 去掉前导 0
    int start = 0;
    while (tmp[start] == '0' && start < idx-1) start++;
    strcpy(g_esnStr, tmp + start);

    // 输出整数（取整）到全局变量
    g_posFlow = posRaw;
    g_negFlow = negRaw;
    g_netFlow = netRaw;
    g_instFlow = (int)instVal;
    g_velocity = (int)velVal;
    return 1;
}

int main() {
    // 发送读取水表数据命令
    sendWaterMeterReadCommand();
    // rt_thread_mdelay(1000); 
     printf("延时1s\n");
    // 你接收到的字节数据 (示例)
    uint8_t recvData[] = {
        0x01,0x03,0x38,0x00,0x01,0x03,0x34,0x00,0x00,0x00,0x03,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0x80,0x00,0x00,0x44,0x4D,0x62,0x40,0x40,
        0x64,0x00,0x34,0x00,0x34,0x00,0x00,0x01,0x7E,0x00,0x30,0x08,0x59,0x00,0x00,0x03,
        0x34,0x00,0x00,0x00,0x00,0x90,0x11,0x00,0x13,0x00,0x00,0x7F,0xB7
    };
    int len = sizeof(recvData);


    
    // 解析接收到的数据，结果存储在全局变量中
    if(parseWaterMeterData(recvData, len)){
        //打印解析成功
        printf("解析成功\n");
    }else{
        //解析失败
        //重新读取
    }

    // 从全局变量读取解析结果
    printf("正累计流量: %d\n", g_posFlow);
    printf("负累计流量: %d\n", g_negFlow);
    printf("净累计流量: %d\n", g_netFlow);
    printf("瞬时流量: %d\n", g_instFlow);
    printf("流速: %d\n", g_velocity);
    printf("ESN: %s\n", g_esnStr);

    return 0;
}
