/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : D:\2025work\STM32L072PRO\source-application\user\driver_Sequences_0020.log
 *  Created     : 08:28:10 (19/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : D:\2025work\STM32L072PRO\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[08:28:10.305]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:28:10.305]  
[08:28:10.325]  <debugvars>
[08:28:10.345]    // Pre-defined
[08:28:10.360]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:28:10.388]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:28:10.389]    __dp=0x00000000
[08:28:10.389]    __ap=0x00000000
[08:28:10.389]    __traceout=0x00000000      (Trace Disabled)
[08:28:10.389]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:28:10.390]    __FlashAddr=0x00000000
[08:28:10.390]    __FlashLen=0x00000000
[08:28:10.390]    __FlashArg=0x00000000
[08:28:10.390]    __FlashOp=0x00000000
[08:28:10.390]    __Result=0x00000000
[08:28:10.391]    
[08:28:10.391]    // User-defined
[08:28:10.391]    DbgMCU_CR=0x00000007
[08:28:10.391]    DbgMCU_APB1_Fz=0x00000000
[08:28:10.391]    DbgMCU_APB2_Fz=0x00000000
[08:28:10.392]    DoOptionByteLoading=0x00000000
[08:28:10.392]  </debugvars>
[08:28:10.392]  
[08:28:10.392]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:28:10.393]    <block atomic="false" info="">
[08:28:10.393]      Sequence("CheckID");
[08:28:10.393]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:28:10.393]          <block atomic="false" info="">
[08:28:10.393]            __var pidr1 = 0;
[08:28:10.393]              // -> [pidr1 <= 0x00000000]
[08:28:10.394]            __var pidr2 = 0;
[08:28:10.394]              // -> [pidr2 <= 0x00000000]
[08:28:10.394]            __var jep106id = 0;
[08:28:10.395]              // -> [jep106id <= 0x00000000]
[08:28:10.395]            __var ROMTableBase = 0;
[08:28:10.395]              // -> [ROMTableBase <= 0x00000000]
[08:28:10.395]            __ap = 0;      // AHB-AP
[08:28:10.395]              // -> [__ap <= 0x00000000]
[08:28:10.395]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:28:10.395]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:28:10.395]              // -> [ROMTableBase <= 0xF0000000]
[08:28:10.395]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:28:10.395]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:28:10.398]              // -> [pidr1 <= 0x00000004]
[08:28:10.398]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:28:10.399]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:28:10.399]              // -> [pidr2 <= 0x0000000A]
[08:28:10.399]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:28:10.399]              // -> [jep106id <= 0x00000020]
[08:28:10.400]          </block>
[08:28:10.400]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:28:10.400]            // if-block "jep106id != 0x20"
[08:28:10.400]              // =>  FALSE
[08:28:10.400]            // skip if-block "jep106id != 0x20"
[08:28:10.400]          </control>
[08:28:10.400]        </sequence>
[08:28:10.400]    </block>
[08:28:10.400]  </sequence>
[08:28:10.400]  
[08:28:10.413]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:28:10.413]  
[08:28:10.413]  <debugvars>
[08:28:10.413]    // Pre-defined
[08:28:10.414]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:28:10.414]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:28:10.414]    __dp=0x00000000
[08:28:10.414]    __ap=0x00000000
[08:28:10.414]    __traceout=0x00000000      (Trace Disabled)
[08:28:10.414]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:28:10.414]    __FlashAddr=0x00000000
[08:28:10.414]    __FlashLen=0x00000000
[08:28:10.416]    __FlashArg=0x00000000
[08:28:10.416]    __FlashOp=0x00000000
[08:28:10.416]    __Result=0x00000000
[08:28:10.416]    
[08:28:10.416]    // User-defined
[08:28:10.416]    DbgMCU_CR=0x00000007
[08:28:10.416]    DbgMCU_APB1_Fz=0x00000000
[08:28:10.416]    DbgMCU_APB2_Fz=0x00000000
[08:28:10.417]    DoOptionByteLoading=0x00000000
[08:28:10.417]  </debugvars>
[08:28:10.417]  
[08:28:10.418]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:28:10.418]    <block atomic="false" info="">
[08:28:10.418]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:28:10.418]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:28:10.418]    </block>
[08:28:10.418]    <block atomic="false" info="DbgMCU registers">
[08:28:10.420]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:28:10.420]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[08:28:10.421]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[08:28:10.421]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:28:10.421]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:28:10.423]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:28:10.423]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:28:10.423]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:28:10.423]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:28:10.425]    </block>
[08:28:10.425]  </sequence>
[08:28:10.426]  
[08:28:18.302]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[08:28:18.302]  
[08:28:18.303]  <debugvars>
[08:28:18.303]    // Pre-defined
[08:28:18.303]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:28:18.303]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:28:18.304]    __dp=0x00000000
[08:28:18.304]    __ap=0x00000000
[08:28:18.304]    __traceout=0x00000000      (Trace Disabled)
[08:28:18.304]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:28:18.304]    __FlashAddr=0x00000000
[08:28:18.304]    __FlashLen=0x00000000
[08:28:18.304]    __FlashArg=0x00000000
[08:28:18.306]    __FlashOp=0x00000000
[08:28:18.306]    __Result=0x00000000
[08:28:18.306]    
[08:28:18.306]    // User-defined
[08:28:18.306]    DbgMCU_CR=0x00000007
[08:28:18.306]    DbgMCU_APB1_Fz=0x00000000
[08:28:18.307]    DbgMCU_APB2_Fz=0x00000000
[08:28:18.307]    DoOptionByteLoading=0x00000000
[08:28:18.307]  </debugvars>
[08:28:18.307]  
[08:28:18.307]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[08:28:18.308]    <block atomic="false" info="">
[08:28:18.308]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[08:28:18.308]        // -> [connectionFlash <= 0x00000001]
[08:28:18.308]      __var FLASH_BASE = 0x40022000 ;
[08:28:18.309]        // -> [FLASH_BASE <= 0x40022000]
[08:28:18.309]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[08:28:18.310]        // -> [FLASH_CR <= 0x40022004]
[08:28:18.310]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[08:28:18.310]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[08:28:18.310]      __var LOCK_BIT = ( 1 << 0 ) ;
[08:28:18.310]        // -> [LOCK_BIT <= 0x00000001]
[08:28:18.310]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[08:28:18.311]        // -> [OPTLOCK_BIT <= 0x00000004]
[08:28:18.311]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[08:28:18.311]        // -> [FLASH_KEYR <= 0x4002200C]
[08:28:18.311]      __var FLASH_KEY1 = 0x89ABCDEF ;
[08:28:18.312]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[08:28:18.312]      __var FLASH_KEY2 = 0x02030405 ;
[08:28:18.312]        // -> [FLASH_KEY2 <= 0x02030405]
[08:28:18.312]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[08:28:18.313]        // -> [FLASH_OPTKEYR <= 0x40022014]
[08:28:18.313]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[08:28:18.313]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[08:28:18.313]      __var FLASH_OPTKEY2 = 0x24252627 ;
[08:28:18.313]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[08:28:18.313]      __var FLASH_CR_Value = 0 ;
[08:28:18.314]        // -> [FLASH_CR_Value <= 0x00000000]
[08:28:18.314]      __var DoDebugPortStop = 1 ;
[08:28:18.314]        // -> [DoDebugPortStop <= 0x00000001]
[08:28:18.314]      __var DP_CTRL_STAT = 0x4 ;
[08:28:18.314]        // -> [DP_CTRL_STAT <= 0x00000004]
[08:28:18.315]      __var DP_SELECT = 0x8 ;
[08:28:18.315]        // -> [DP_SELECT <= 0x00000008]
[08:28:18.315]    </block>
[08:28:18.316]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[08:28:18.316]      // if-block "connectionFlash && DoOptionByteLoading"
[08:28:18.316]        // =>  FALSE
[08:28:18.316]      // skip if-block "connectionFlash && DoOptionByteLoading"
[08:28:18.316]    </control>
[08:28:18.316]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[08:28:18.317]      // if-block "DoDebugPortStop"
[08:28:18.317]        // =>  TRUE
[08:28:18.317]      <block atomic="false" info="">
[08:28:18.317]        WriteDP(DP_SELECT, 0x00000000);
[08:28:18.318]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[08:28:18.318]        WriteDP(DP_CTRL_STAT, 0x00000000);
[08:28:18.318]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[08:28:18.318]      </block>
[08:28:18.319]      // end if-block "DoDebugPortStop"
[08:28:18.319]    </control>
[08:28:18.319]  </sequence>
[08:28:18.319]  
