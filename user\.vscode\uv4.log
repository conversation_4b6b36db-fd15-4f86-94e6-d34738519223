*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'C:\Keil_v5\ARM\ARMCC\Bin'
Build target 'driver'
compiling timer.c...
compiling daoSheng_protocol.c...
..\protocol\daoSheng_protocol.c(277): warning:  #1182-D: a declaration cannot have a label
              int32_t streamflow_value = parse_long(serial.rx_buffer + 4);
..\protocol\daoSheng_protocol.c(289): warning:  #1182-D: a declaration cannot have a label
              int32_t total_flow_value = parse_long(serial.rx_buffer + 4);
..\protocol\daoSheng_protocol.c(296): warning:  #1182-D: a declaration cannot have a label
              int32_t time_start_value = parse_long(serial.rx_buffer + 4);
..\protocol\daoSheng_protocol.c(305): warning:  #167-D: argument of type "uint8_t *" is incompatible with parameter of type "MeterData *"
               parse_modbus_data(serial.rx_buffer, serial.rx_count-2, &serial.rx_buffer[0]);
..\protocol\daoSheng_protocol.c(306): warning:  #223-D: function "serial_rx_reset" declared implicitly
               serial_rx_reset();   //??????????????????
..\protocol\daoSheng_protocol.c(274): warning:  #546-D: transfer of control bypasses initialization of:
            variable "streamflow_value"  (declared at line 277)
            variable "streamflow"  (declared at line 278)
            variable "total_flow_value"  (declared at line 289)
            variable "total_flow"  (declared at line 290)
            variable "time_start_value"  (declared at line 296)
       switch (cmd_state) {
       ^
..\protocol\daoSheng_protocol.c: 6 warnings, 0 errors
linking...
.\Objects\driver.axf: Error: L6218E: Undefined symbol serial_rx_reset (referred from daosheng_protocol.o).
Not enough information to list image symbols.
Not enough information to list load addresses in the image map.
Finished: 2 information, 0 warning and 1 error messages.
".\Objects\driver.axf" - 1 Error(s), 6 Warning(s).
Target not created.
Build Time Elapsed:  00:00:06
