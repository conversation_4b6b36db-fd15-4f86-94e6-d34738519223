#include "adc.h"
#include "stm32l0xx_hal.h"
#include <stdio.h>


ADC_HandleTypeDef hadc;
const float VREFINT_CAL = 1.224f;  // 参考电压典型值(V)
const uint32_t VREFINT_CAL_ADDR = 0x1FF80078; // 校准值地址

PowerMonitor_t pwr_mon = {0};
////////////////////////////////////////////////

////////////////////////////////////////////////////



// ADC低功耗初始化
static void ADC_Init_LowPower(void) {
 
  __HAL_RCC_SYSCFG_CLK_ENABLE();
  ADC_ChannelConfTypeDef sConfig = {0};

  /* USER CODE BEGIN ADC_Init 1 */

  /* USER CODE END ADC_Init 1 */

  /** Configure the global features of the ADC (Clock, Resolution, Data Alignment and number of conversion)
  */
  hadc.Instance = ADC1;
  hadc.Init.OversamplingMode = DISABLE;
  hadc.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV1;
  hadc.Init.Resolution = ADC_RESOLUTION_12B;
  hadc.Init.SamplingTime = ADC_SAMPLETIME_12CYCLES_5;
  hadc.Init.ScanConvMode = ADC_SCAN_DIRECTION_FORWARD;
  hadc.Init.DataAlign = ADC_DATAALIGN_RIGHT;
  hadc.Init.ContinuousConvMode = ENABLE;
  hadc.Init.DiscontinuousConvMode = DISABLE;
  hadc.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_NONE;
  hadc.Init.ExternalTrigConv = ADC_SOFTWARE_START;
  hadc.Init.DMAContinuousRequests = DISABLE;
  hadc.Init.EOCSelection = ADC_EOC_SINGLE_CONV;
  hadc.Init.Overrun = ADC_OVR_DATA_PRESERVED;
  hadc.Init.LowPowerAutoWait = DISABLE;
  hadc.Init.LowPowerFrequencyMode = ENABLE;
  hadc.Init.LowPowerAutoPowerOff = DISABLE;
  if (HAL_ADC_Init(&hadc) != HAL_OK)
  {
     printf("Error_Handler();\r\n");
  }

  /** Configure for the selected ADC regular channel to be converted.
  */
  sConfig.Channel = ADC_CHANNEL_VREFINT;;
  sConfig.Rank = ADC_RANK_CHANNEL_NUMBER;
  if (HAL_ADC_ConfigChannel(&hadc, &sConfig) != HAL_OK)
  {
    printf("Error_Handler();\r\n");
  }
  
  
}

// 读取VREFINT原始值
static uint16_t Read_VREFINT_Raw(void) {
  uint32_t sum = 0;
  const uint8_t samples = 8;
  uint8_t valid_samples = 0;
  
  for (uint8_t i = 0; i < samples; i++) {
    printf("ADC Sample %d: Starting...\r\n", i+1);
    
    if (HAL_ADC_Start(&hadc) != HAL_OK) {
      printf("ADC Start failed!\r\n");
      continue;
    }
    
    printf("Waiting for conversion...\r\n");
    HAL_StatusTypeDef status = HAL_ADC_PollForConversion(&hadc, 100); // 增加超时时间
    if (status != HAL_OK) {
      printf("ADC Conversion timeout/error! Status: %d\r\n", status);
      HAL_ADC_Stop(&hadc);
      continue;
    }
    
    uint32_t value = HAL_ADC_GetValue(&hadc);
    printf("ADC Value: %lu\r\n", value);
    sum += value;
    valid_samples++;
    HAL_ADC_Stop(&hadc);
  }
  
  if (valid_samples == 0) {
    printf("No valid ADC samples!\r\n");
    return 0;
    }
    
  printf("Average ADC value: %lu\r\n", sum / valid_samples);
  return (uint16_t)(sum / valid_samples);
}

// 获取VDDA电压值(mV)
uint32_t vdda_get_voltage(void) 
{
  // 获取校准值
  pwr_mon.vrefint_cal = *((uint16_t*)VREFINT_CAL_ADDR);
  
  if (pwr_mon.vrefint_cal == 0) 
	{ 
		
    return 0;
  }
// 强制启用 VREFINT
SYSCFG->CFGR3 |= SYSCFG_CFGR3_ENBUF_VREFINT_ADC;
while ((SYSCFG->CFGR3 & SYSCFG_CFGR3_VREFINT_RDYF) == 0) {
    // 等待 VREFINT 就绪
}
if (HAL_ADC_Start(&hadc) != HAL_OK) {
    HAL_ADC_DeInit(&hadc);
    HAL_ADC_Init(&hadc); // 重新初始化 ADC
    if (HAL_ADC_Start(&hadc) != HAL_OK) {
        printf("ADC Start failed after retry!\\r\\n");
        return 0;
    }
}
SYSCFG->CFGR3 |= SYSCFG_CFGR3_ENBUF_VREFINT_ADC;
uint32_t timeout = 1000000; // 超时计数器
while ((SYSCFG->CFGR3 & SYSCFG_CFGR3_VREFINT_RDYF) == 0) {
    if (--timeout == 0) {
        printf("Error: VREFINT not ready! Check VDDA voltage or hardware.\r\n");
        break; // 超时退出
    }
}

  // 初始化ADC
  ADC_Init_LowPower();
  
  printf("Before enabling VREFINT - SYSCFG->CFGR3: 0x%08lX\r\n", SYSCFG->CFGR3);
  printf("Before enabling VREFINT - RCC->APB2ENR: 0x%08lX\r\n", RCC->APB2ENR);
  
  printf("Enabling VREFINT...\r\n");
    HAL_StatusTypeDef vref_status = HAL_ADCEx_EnableVREFINT();
    printf("After enabling VREFINT - SYSCFG->CFGR3: 0x%08lX\r\n", SYSCFG->CFGR3);
    printf("VREFINT enable status: %d\r\n", vref_status);
    
    if (vref_status != HAL_OK) {
      printf("VREFINT enable failed! Last error: 0x%08lX\r\n", HAL_ADC_GetError(&hadc));
      return 0;
    }
    
//  // 读取ADC值
  pwr_mon.vrefint_raw = Read_VREFINT_Raw();

  // 关闭ADC
  HAL_ADCEx_DisableVREFINT();
  HAL_ADC_DeInit(&hadc);
  __HAL_RCC_ADC1_CLK_DISABLE();

  // 计算VDDA
  if (pwr_mon.vrefint_raw > 0)
	{
    return (uint32_t)((VREFINT_CAL * pwr_mon.vrefint_cal * 1000) / pwr_mon.vrefint_raw);
  }
  return 0;
	
}

//static ADC_HandleTypeDef hadc;

void ADC_Voltage_Init(void) {
    // 1. 初始化GPIO
    __HAL_RCC_GPIOA_CLK_ENABLE();
   
  __HAL_RCC_ADC1_CLK_ENABLE();
      ADC_ChannelConfTypeDef sConfig = {0};

  /* USER CODE BEGIN ADC_Init 1 */

  /* USER CODE END ADC_Init 1 */

  /** Configure the global features of the ADC (Clock, Resolution, Data Alignment and number of conversion)
  */
  hadc.Instance = ADC1;
  hadc.Init.OversamplingMode = DISABLE;
  hadc.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV1;
  hadc.Init.Resolution = ADC_RESOLUTION_12B;
  hadc.Init.SamplingTime = ADC_SAMPLETIME_12CYCLES_5;
  hadc.Init.ScanConvMode = ADC_SCAN_DIRECTION_FORWARD;
  hadc.Init.DataAlign = ADC_DATAALIGN_RIGHT;
  hadc.Init.ContinuousConvMode = ENABLE;
  hadc.Init.DiscontinuousConvMode = DISABLE;
  hadc.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_NONE;
  hadc.Init.ExternalTrigConv = ADC_SOFTWARE_START;
  hadc.Init.DMAContinuousRequests = DISABLE;
  hadc.Init.EOCSelection = ADC_EOC_SINGLE_CONV;
  hadc.Init.Overrun = ADC_OVR_DATA_PRESERVED;
  hadc.Init.LowPowerAutoWait = DISABLE;
  hadc.Init.LowPowerFrequencyMode = ENABLE;
  hadc.Init.LowPowerAutoPowerOff = DISABLE;
  if (HAL_ADC_Init(&hadc) != HAL_OK)
  {
   // Error_Handler();
  }

  /** Configure for the selected ADC regular channel to be converted.
  */
  sConfig.Channel = ADC_CHANNEL_4;
  sConfig.Rank = ADC_RANK_CHANNEL_NUMBER;
  if (HAL_ADC_ConfigChannel(&hadc, &sConfig) != HAL_OK)
  {
    //Error_Handler();
  }
}

float Get_Power_Voltage(void) {
   // 1. 启动ADC转换
    if(HAL_ADC_Start(&hadc) != HAL_OK) {
        printf("[ERROR] ADC start failed!\r\n");
        return 0;
    }
    
    HAL_StatusTypeDef poll_status = HAL_ADC_PollForConversion(&hadc, 10);
    if(poll_status != HAL_OK) {
        printf("[ERROR] ADC timeout! Status: %d\r\n", poll_status);
        return 0;
    }
    
    // 2. 读取ADC值（0-4095）
    uint32_t adc_value = HAL_ADC_GetValue(&hadc);
    HAL_ADC_Stop(&hadc);
    
    // 调试输出原始ADC值
   // printf("[DEBUG] ADC RAW: %lu\r\n", adc_value);
    
    // 3. 计算实际电压（假设VDDA=3.0V，分压比2倍）
   // uint32_t voltage_mv = (adc_value * 6000) / 4095; // 3.0V*2*1000=6000
    float voltage = (adc_value * 6.0f) / 4095.0f; // 3.0V*2=6.0V
    return  voltage;
}

