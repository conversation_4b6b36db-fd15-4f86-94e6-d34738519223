#ifndef __USER_CONFIG_H__
#define __USER_CONFIG_H__

#include "system.h"

#define EEPROM_BANK1_ADDR 0x08080000
#define EEPROM_BANK2_ADDR 0x080C0000

#define APPLICATION_ADDR 0x08004000 //16Kb

#define SERVER_IP "**************"
#define SERVER_PORT 16000

typedef struct
{
  uint8_t ip[3][4];//IP
  uint16_t port[3];//端口
  uint16_t upload_cycle[3];//上报周期
  uint8_t sim[8];
  uint8_t central_station_address;//中心站地址0x01
  uint8_t telemetry_address[5];//遥测地址0x00
  uint8_t password[2];//密码0x0000
}NET_PARAM;

typedef enum
{
  PPRODUCER_ID_DAOSHENG = 1,
  PPRODUCER_ID_TAIAN = 2,
  PPRODUCER_ID_TANSHAN = 3,
  PPRODUCER_ID_HENAN = 4
}PPRODUCER_ID;
/*
4800:   C0 12 00 00 (小端序)
9600:   80 25 00 00 (小端序)
19200:  00 4B 00 00 (小端序)
38400:  00 96 00 00 (小端序)
57600:  00 E1 00 00 (小端序)
115200: 00 C2 01 00 (小端序)
128000: 00 F4 01 00 (小端序)
230400: 00 84 03 00 (小端序)

*/
typedef enum
{
  BAUD_RATE_ID_1200 = 1,
  BAUD_RATE_ID_2400 = 2,
  BAUD_RATE_ID_4800 = 3,
  BAUD_RATE_ID_9600 = 4,
  BAUD_RATE_ID_19200 = 5,
  BAUD_RATE_ID_38400 = 6,
	BAUD_RATE_ID_57600 = 7,
  BAUD_RATE_ID_115200 = 8,
  BAUD_RATE_ID_128000 = 9,
	BAUD_RATE_ID_230400 = 10
}BAUD_RATE_ID;

typedef enum
{
  DATA_BIT_ID_7 = 7,
  DATA_BIT_ID_8 = 8,
  DATA_BIT_ID_9 = 9
}DATA_BIT_ID;

typedef enum
{
  PARITY_ID_NONE = 0,
  PARITY_ID_ODD = 1,
  PARITY_ID_EVEN = 2
}PARITY_ID;

typedef enum
{
  STOP_BIT_ID_1 = 1,
  STOP_BIT_ID_2 = 2
}STOP_BIT_ID;

typedef enum 
{
  PROTOCOL_TYPE_DAOSHENG_WATER_METER = 0,
  PROTOCOL_TYPE_FLOW_METER,
  PROTOCOL_TYPE_WATER_METER,
}PROTOCOL_TYPE;

typedef struct
{
  PROTOCOL_TYPE protocol_type;//厂商ID,01-大连道盛 02-泰安  03-唐山 04-河南
  BAUD_RATE_ID baud_rate_id;//1-1200,2-2400,3-4800,4-9600,5-19200,6-38400
  DATA_BIT_ID data_bit_id;//07-7 位， 08-8位， 09-9 位， 一般为 8 位
  PARITY_ID parity_id;//0-无校验，1-奇校验， 2-偶校验， 一般为无校验
  STOP_BIT_ID stop_bit_id;//1-1 位， 2-2位， 一般为 1 位
  uint32_t cycle;//南向采集周期S
}WATER_METER_INFO;

typedef struct
{
  NET_PARAM net_param;//网络参数
  WATER_METER_INFO water_meter_info;//水表参数
	uint8_t Terminal_ID[5];      //终端ID配置Terminal ID
  uint16_t flag;
}CONFIG;

extern CONFIG config;

void read_config(u8 type);
/**
 * 更新配置数据，写到falsh中
*/
void config_refresh(void);

#endif
