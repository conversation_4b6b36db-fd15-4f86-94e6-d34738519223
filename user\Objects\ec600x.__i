--c99 --gnu -c --cpu Cortex-M0+ -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\app\include -I ..\drivers\include -I ..\peripheral\include -I ..\rtthread\bsp -I ..\rtthread\include -I ..\stm32L0xx_hal\Inc -I ..\system\include -I ..\lcd12864 -I ..\protocol\include -I ..\ec600x\include --locale=english
-D__UVISION_VERSION="536" -DSTM32L072xx -DSTM32L072xx -DUSE_HAL_DRIVER
-o .\objects\ec600x.o --omf_browse .\objects\ec600x.crf --depend .\objects\ec600x.d "..\ec600x\ec600x.c"