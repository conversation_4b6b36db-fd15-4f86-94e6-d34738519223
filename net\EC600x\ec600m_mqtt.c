#include "ec600m_mqtt.h"

static NET_ERR connect(char *id,char *domain_name,u16 port,char *username,char *password)
{
	return NET_ERR_NONE;
}
static NET_ERR send(char *topic,u8 qos,char *data,u16 data_len)
{
	return NET_ERR_NONE;
}
static NET_ERR recv(char *topic,char *out_data,u16 *data_len)
{
	return NET_ERR_NONE;
}
static NET_ERR topic(char *topic)
{
	return NET_ERR_NONE;
}
static NET_ERR close(void)
{
	return NET_ERR_NONE;
}

static NET_MQTT ec600m_mqtt = {
	.connect = connect,
	.send = send,
	.recv = recv,
	.topic = topic,
	.close = close,
};

NET_ERR ec600m_mqtt_register(NET_INFO *info)
{
	if(info == RT_NULL)
		return NET_ERR_FAIL;
	info->mqtt = &ec600m_mqtt;
	return NET_ERR_NONE;
}
