/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR> for STM32L072
  * @version V1.0.0
  * @date    2024/12/26
  * @brief   Bootloader main program for STM32L072 water meter collector
  *          固件升级引导程序主文件，适配STM32L072水表采集器
  ******************************************************************************
  * @attention
  *
  * 本文件是基于STM32F103的bootloader适配到STM32L072的版本
  * 保持原有的业务逻辑和数据解析逻辑不变，仅修改硬件相关部分
  *
  * 主要功能：
  * 1. 检查升级标志，决定是否进入升级模式
  * 2. 如果有有效应用程序且无升级标志，则跳转到应用程序
  * 3. 如果需要升级，则进入IAP模式接收固件数据
  * 4. 支持加密固件数据的接收和解密
  * 5. 升级完成后重启进入新应用程序
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include <stdio.h>                    /* 标准I/O库 */
#include <ctype.h>                    /* 字符处理函数 */
#include <string.h>                   /* 字符串和内存操作函数 */
#include "stm32l0xx_hal.h"           /* STM32L0 HAL库 */
#include "stm32l0xx_hal_flash.h"     /* Flash操作库 */
#include "stm32l0xx_hal_iwdg.h"      /* 独立看门狗库 */
#include "stm32l0xx_hal_uart.h"      /* UART库 */
#include "stm32_flash.h"             /* 自定义Flash操作接口 */

/* 数据类型定义 ------------------------------------------------------------------*/
typedef uint8_t u8;                     /* 8位无符号整型 */
typedef uint16_t u16;                   /* 16位无符号整型 */
typedef uint32_t u32;                   /* 32位无符号整型 */
typedef int8_t int8;                    /* 8位有符号整型 */
typedef void (*pFunction)(void);        /* 函数指针类型定义 */

/* 宏定义和常量 ------------------------------------------------------------------*/
#define ALARM_CONFIG_KEY_LEN 8          /* 加密密钥长度 */
#define BUFF_LEN 1032                   /* 数据缓冲区长度 */
#define UPDATA_FLAG_ADDR 128            /* 升级标志存储地址(EEPROM偏移地址) */
#define UPDATA_FLAG 0xFF                /* 升级标志值 */

/* STM32L072应用程序相关定义 */
#define STM32L072_APP_SIZE 96           /* 应用程序最大占用空间(KB) - STM32L072有128KB Flash */
#define USER_FLASH_FIRST_PAGE_ADDRESS 0x08004000  /* 应用程序起始地址(16KB bootloader) */

/* 全局变量定义 ------------------------------------------------------------------*/
pFunction Jump_To_Application;          /* 应用程序跳转函数指针 */
uint32_t JumpAddress;                   /* 跳转地址 */
static volatile uint32_t FlashWriteAddress; /* Flash写入地址 */
uint8_t Parity = 0;                     /* 校验位 */
u8 configKey[ALARM_CONFIG_KEY_LEN];     /* 加密密钥数组 */


u8 reCmd[6] = {0x7E, 0x00, 0x04,0x00,0x00,0xCE};     //����֡

u8 aucCRCHi []={
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
    0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
    0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
    0x00, 0xC1, 0x81, 0x40
};
 u8 aucCRCLo[] =  {
    0x00, 0xC0, 0xC1, 0x01, 0xC3, 0x03, 0x02, 0xC2, 0xC6, 0x06, 0x07, 0xC7,
    0x05, 0xC5, 0xC4, 0x04, 0xCC, 0x0C, 0x0D, 0xCD, 0x0F, 0xCF, 0xCE, 0x0E,
    0x0A, 0xCA, 0xCB, 0x0B, 0xC9, 0x09, 0x08, 0xC8, 0xD8, 0x18, 0x19, 0xD9,
    0x1B, 0xDB, 0xDA, 0x1A, 0x1E, 0xDE, 0xDF, 0x1F, 0xDD, 0x1D, 0x1C, 0xDC,
    0x14, 0xD4, 0xD5, 0x15, 0xD7, 0x17, 0x16, 0xD6, 0xD2, 0x12, 0x13, 0xD3,
    0x11, 0xD1, 0xD0, 0x10, 0xF0, 0x30, 0x31, 0xF1, 0x33, 0xF3, 0xF2, 0x32,
    0x36, 0xF6, 0xF7, 0x37, 0xF5, 0x35, 0x34, 0xF4, 0x3C, 0xFC, 0xFD, 0x3D,
    0xFF, 0x3F, 0x3E, 0xFE, 0xFA, 0x3A, 0x3B, 0xFB, 0x39, 0xF9, 0xF8, 0x38,
    0x28, 0xE8, 0xE9, 0x29, 0xEB, 0x2B, 0x2A, 0xEA, 0xEE, 0x2E, 0x2F, 0xEF,
    0x2D, 0xED, 0xEC, 0x2C, 0xE4, 0x24, 0x25, 0xE5, 0x27, 0xE7, 0xE6, 0x26,
    0x22, 0xE2, 0xE3, 0x23, 0xE1, 0x21, 0x20, 0xE0, 0xA0, 0x60, 0x61, 0xA1,
    0x63, 0xA3, 0xA2, 0x62, 0x66, 0xA6, 0xA7, 0x67, 0xA5, 0x65, 0x64, 0xA4,
    0x6C, 0xAC, 0xAD, 0x6D, 0xAF, 0x6F, 0x6E, 0xAE, 0xAA, 0x6A, 0x6B, 0xAB,
    0x69, 0xA9, 0xA8, 0x68, 0x78, 0xB8, 0xB9, 0x79, 0xBB, 0x7B, 0x7A, 0xBA,
    0xBE, 0x7E, 0x7F, 0xBF, 0x7D, 0xBD, 0xBC, 0x7C, 0xB4, 0x74, 0x75, 0xB5,
    0x77, 0xB7, 0xB6, 0x76, 0x72, 0xB2, 0xB3, 0x73, 0xB1, 0x71, 0x70, 0xB0,
    0x50, 0x90, 0x91, 0x51, 0x93, 0x53, 0x52, 0x92, 0x96, 0x56, 0x57, 0x97,
    0x55, 0x95, 0x94, 0x54, 0x9C, 0x5C, 0x5D, 0x9D, 0x5F, 0x9F, 0x9E, 0x5E,
    0x5A, 0x9A, 0x9B, 0x5B, 0x99, 0x59, 0x58, 0x98, 0x88, 0x48, 0x49, 0x89,
    0x4B, 0x8B, 0x8A, 0x4A, 0x4E, 0x8E, 0x8F, 0x4F, 0x8D, 0x4D, 0x4C, 0x8C,
    0x44, 0x84, 0x85, 0x45, 0x87, 0x47, 0x46, 0x86, 0x82, 0x42, 0x43, 0x83,
    0x41, 0x81, 0x80, 0x40
};
 


void NVIC_Configuration(void);
void GPIO_App_Configuration(void);
uint8 DecryptConfigure(u8 frameNo, uint16	cmdLen,uint8* configUsartRcvDataBuffer);
void memCpy(void* psrc, void* pdest, uint32 size);
void DecryptKeyConfigure(uint8 a, uint8 b);
u16  usMBCRC16( u8* pucFrame, uint16 usLen );
void returnFrame(u8 type,u8  status);
void recvData(u8 *pframe , u16 dataLen);


void InterruptConfig(void)
{   
  /* Set the Vector Table base address at 0x08000000 */
  NVIC_SetVectorTable(NVIC_VectTab_FLASH, 0x00000);
}


/**
  * @brief  Main program.
  * @param  None
  * @retval None
  */
int main(void)
{
	uint8       updataFlag = 0xFF;
	uint8	buff[BUFF_LEN];	
       u16         validLen  =0;
	uint16	len = 0;

	InterruptConfig();

#if IWDG_Debug
	IWDG_Init(); 
#endif		

	Usart_GPIO_Configuration();
	Usart1_bps_Init(115200);
	u1_get_clear();
	I2C_Configuration();
	NVIC_Configuration();	

	I2C_ReadS_24C(UPDATA_FLAG_ADDR,&updataFlag,1);
	
	if(UPDATA_FLAG == updataFlag)
	{
		/* Check if valid stack address (RAM address) then jump to user application */
	    if (((*(__IO uint32_t*)USER_FLASH_FIRST_PAGE_ADDRESS) & 0x2FFE0000 ) == 0x20000000)
	    {
	      	      
	      /* Jump to user application */
	      JumpAddress = *(__IO uint32_t*) (USER_FLASH_FIRST_PAGE_ADDRESS + 4); 
	      Jump_To_Application = (pFunction) JumpAddress;
	      /* Initialize user application's Stack Pointer */
	      __set_MSP(*(__IO uint32_t*) USER_FLASH_FIRST_PAGE_ADDRESS);
	      Jump_To_Application();
	    }
	    else
	    {
	   	  updataFlag = 0x00;
		  I2C_WriteS_24C(UPDATA_FLAG_ADDR,&updataFlag,1);   

		  os_dly_wait(10);
		IWDG->KR=0X5555;//ʹ�ܶ�IWDG->PR��IWDG->RLR��д
		IWDG->PR=6;  		//0-6   //LSI/32=40Khz/4*2^pre     
		IWDG->RLR=5;  //0-4095//�ؼ��ؼĴ���IWDG->RLR   
		IWDG->KR=0XAAAA;//reload 
		IWDG->KR=0XCCCC;//ʹ�ܿ��Ź�
		while(1);
	    }

	}
  /* enter in IAP mode */
	else
	{   
		FLASH_If_Init();
		/* erase user flash area */
		FLASH_If_Erase(USER_FLASH_FIRST_PAGE_ADDRESS);

		while (1)
		{	
			//memset(buff ,0 , BUFF_LEN);
			len = u1_get_str((uint8*)buff,BUFF_LEN);
			validLen = buff[1]*256 + buff[2] ;              //��Ч����
			
			if(( len == validLen + 2) && (buff[0] == 0x7E) && (buff[len-1] == 0xCE))
			{
				switch(buff[3])
				{
					case 0x01:     //��ʼ���
						//��ȡ��Կ
						memCpy(buff+5,configKey,ALARM_CONFIG_KEY_LEN);

						//��Կ����0-2 1-6 3-7
						DecryptKeyConfigure(0,2);	
						DecryptKeyConfigure(1,6);	
						DecryptKeyConfigure(3,7);

					       returnFrame(0x04 , 0xff);

							
						break;
						
					case 0x03:    //��������
						recvData((&buff[1]),validLen);
						break;
						
					case 0x05:    //����
						returnFrame(0x06,0x00);
						updataFlag =0xff;
						I2C_WriteS_24C(UPDATA_FLAG_ADDR,&updataFlag,1);
						os_dly_wait(10);

						IWDG->KR=0X5555;//ʹ�ܶ�IWDG->PR��IWDG->RLR��д
						IWDG->PR=6;  		//0-6   //LSI/32=40Khz/4*2^pre     
						IWDG->RLR=5;  //0-4095//�ؼ��ؼĴ���IWDG->RLR   
						IWDG->KR=0XAAAA;//reload 
						IWDG->KR=0XCCCC;//ʹ�ܿ��Ź�
						while(1);
						break;
						
					default:
						returnFrame(0x0A,0xff);   //ָ�����
						break;
						
				}
			
			}
			else if(len > 0)
			{
				returnFrame(0x0A,0xff);   //ָ�����
		
			}		  
		  }

	}
	
       return 0;
}


	
/****************************************************************************
* ��	�ƣ�void NVIC_Configuration(void)
* ��	�ܣ��ж�����
* ��ڲ�������
* ���ڲ�������
* ˵	���������ж����ȼ�����������жϣ��紮���ж�
* ���÷������� 
****************************************************************************/ 
void NVIC_Configuration(void)
{
	NVIC_InitTypeDef NVIC_InitStructure;

	/* Configure one bit for preemption priority */
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);

		
	/* Enable the USART1 Interrupt */
	NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Init(&NVIC_InitStructure); 

}

//��Կ����0-2 1-6 3-7
void DecryptKeyConfigure(uint8 a, uint8 b)
{
	uint8 	configKey_temp = 0x00;
	
	configKey_temp=configKey[a];
	configKey[a] = configKey[b];
	configKey[b] = configKey_temp;
	
}

void memCpy(void* psrc, void* pdest, uint32 size)
{
	while(size>0){
		*((int8*)pdest+size-1)=*((int8*)psrc+size-1);
		size--;
	}
}


//�Խ��յļ������ݽ��н��ܺ��ֶν���
//dataLen���� ��Կ+����
uint8 DecryptConfigure(u8 frameNo,uint16	cmdLen,uint8* configUsartRcvDataBuffer)
{
	u8 decryptRound = 0; //�����ִ�
	u8* pCmdData = NULL;
	u8 countRound = 0;
	u8 countNum = 0;
	
	//���ݳ��ȱ�����ALARM_CONFIG_KEY_LEN��Կ���ȵ�������
	if((cmdLen % ALARM_CONFIG_KEY_LEN) != 0)
	{
		return 0;
	}

	decryptRound = cmdLen / 8 ;

	pCmdData = configUsartRcvDataBuffer; //������Կ
	
	//���ݽ��ܣ����ܷ���Ϊ:�����ĸ���Կ�����������,ÿһ��ALARM_CONFIG_KEY_LEN�ֽ�
	for(countRound = 0; countRound < decryptRound; countRound++)
	{
		for(countNum = 0; countNum < ALARM_CONFIG_KEY_LEN; countNum++)
		{
			pCmdData[countRound * ALARM_CONFIG_KEY_LEN + countNum] ^= configKey[countNum];
		}
	}

	FlashWriteAddress =  USER_FLASH_FIRST_PAGE_ADDRESS  + (frameNo)*(BUFF_LEN-8);						

	FLASH_If_Write(&FlashWriteAddress, (u32*)(pCmdData),cmdLen/4);

	return 1;

}


u16  usMBCRC16( u8* pucFrame, uint16 usLen )      //CRC У��
{
    u8 ucCRCHi = 0xFF;
    u8 ucCRCLo = 0xFF;
    int iIndex;
    int i = 0;
	
    while( usLen-- >0 )
    {
        iIndex = ucCRCLo ^  pucFrame[i++];
        ucCRCLo = ( u8 )( ucCRCHi ^ aucCRCHi[iIndex] );
        ucCRCHi = aucCRCLo[iIndex];
    }
    return ( ((u16)ucCRCHi << 8) | ucCRCLo );
}


void returnFrame(u8 type,u8  status)
{
	reCmd[3] = type;
	reCmd[4] =status;

	u1_get_clear();
	u1_put_str((uint8 *)reCmd, 6);
	os_dly_wait(10);

	
}

void recvData(u8 *pframe , u16 dataLen)
{
	u16 recvCRC16 = (pframe[dataLen-1]<<8) | pframe[dataLen-2];
	
	if(recvCRC16 == usMBCRC16(pframe,dataLen-2))    //У��ͨ��
	{
		//����
		if (DecryptConfigure(pframe[3],dataLen-6 ,pframe+4 ) )
		{
			//����
			os_dly_wait(10);
              	returnFrame(0x04,pframe[3]);
		}
		else
			
		{
			os_dly_wait(10);
			returnFrame(0x04,pframe[3]-1);    //���ܳ����ش�
		}
		
	}
	else
	{
		os_dly_wait(10);
		returnFrame(0x04,pframe[3]-1);      //У��������ش�
	}
	
}	

/**
  * @}
  */

/**
  * @}
  */

/******************* (C) COPYRIGHT 2010 STMicroelectronics *****END OF FILE****/
