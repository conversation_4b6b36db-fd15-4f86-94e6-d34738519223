.\objects\idle.o: ..\rtthread\src\idle.c
.\objects\idle.o: ..\rtthread\include\rthw.h
.\objects\idle.o: ..\rtthread\include\rtthread.h
.\objects\idle.o: ..\rtthread\bsp\rtconfig.h
.\objects\idle.o: ..\rtthread\include\rtdebug.h
.\objects\idle.o: ..\rtthread\include\rtdef.h
.\objects\idle.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdarg.h
.\objects\idle.o: ..\rtthread\include\rtlibc.h
.\objects\idle.o: ..\rtthread\include\libc/libc_stat.h
.\objects\idle.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\idle.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\time.h
.\objects\idle.o: ..\rtthread\include\libc/libc_errno.h
.\objects\idle.o: ..\rtthread\include\libc/libc_fcntl.h
.\objects\idle.o: ..\rtthread\include\libc/libc_ioctl.h
.\objects\idle.o: ..\rtthread\include\libc/libc_dirent.h
.\objects\idle.o: ..\rtthread\include\libc/libc_signal.h
.\objects\idle.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\signal.h
.\objects\idle.o: ..\rtthread\include\libc/libc_fdset.h
.\objects\idle.o: ..\rtthread\include\rtservice.h
.\objects\idle.o: ..\rtthread\include\rtm.h
.\objects\idle.o: ..\rtthread\include\rtthread.h
