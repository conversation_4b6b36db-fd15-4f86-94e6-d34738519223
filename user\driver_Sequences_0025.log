/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : D:\2025work\STM32L072PRO\source-application\user\driver_Sequences_0025.log
 *  Created     : 10:08:25 (21/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : D:\2025work\STM32L072PRO\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[10:08:25.673]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:08:25.673]  
[10:08:25.694]  <debugvars>
[10:08:25.717]    // Pre-defined
[10:08:25.737]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:08:25.758]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:08:25.760]    __dp=0x00000000
[10:08:25.761]    __ap=0x00000000
[10:08:25.762]    __traceout=0x00000000      (Trace Disabled)
[10:08:25.762]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:08:25.764]    __FlashAddr=0x00000000
[10:08:25.765]    __FlashLen=0x00000000
[10:08:25.765]    __FlashArg=0x00000000
[10:08:25.766]    __FlashOp=0x00000000
[10:08:25.767]    __Result=0x00000000
[10:08:25.767]    
[10:08:25.767]    // User-defined
[10:08:25.768]    DbgMCU_CR=0x00000007
[10:08:25.769]    DbgMCU_APB1_Fz=0x00000000
[10:08:25.769]    DbgMCU_APB2_Fz=0x00000000
[10:08:25.770]    DoOptionByteLoading=0x00000000
[10:08:25.771]  </debugvars>
[10:08:25.771]  
[10:08:25.772]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:08:25.773]    <block atomic="false" info="">
[10:08:25.773]      Sequence("CheckID");
[10:08:25.774]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:08:25.775]          <block atomic="false" info="">
[10:08:25.775]            __var pidr1 = 0;
[10:08:25.776]              // -> [pidr1 <= 0x00000000]
[10:08:25.777]            __var pidr2 = 0;
[10:08:25.777]              // -> [pidr2 <= 0x00000000]
[10:08:25.778]            __var jep106id = 0;
[10:08:25.779]              // -> [jep106id <= 0x00000000]
[10:08:25.779]            __var ROMTableBase = 0;
[10:08:25.780]              // -> [ROMTableBase <= 0x00000000]
[10:08:25.780]            __ap = 0;      // AHB-AP
[10:08:25.781]              // -> [__ap <= 0x00000000]
[10:08:25.781]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:08:25.783]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:08:25.784]              // -> [ROMTableBase <= 0xF0000000]
[10:08:25.784]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:08:25.786]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:08:25.787]              // -> [pidr1 <= 0x00000004]
[10:08:25.787]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:08:25.789]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:08:25.789]              // -> [pidr2 <= 0x0000000A]
[10:08:25.789]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:08:25.790]              // -> [jep106id <= 0x00000020]
[10:08:25.790]          </block>
[10:08:25.791]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:08:25.791]            // if-block "jep106id != 0x20"
[10:08:25.791]              // =>  FALSE
[10:08:25.791]            // skip if-block "jep106id != 0x20"
[10:08:25.792]          </control>
[10:08:25.792]        </sequence>
[10:08:25.792]    </block>
[10:08:25.793]  </sequence>
[10:08:25.793]  
[10:08:25.804]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:08:25.804]  
[10:08:25.812]  <debugvars>
[10:08:25.813]    // Pre-defined
[10:08:25.813]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:08:25.814]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:08:25.815]    __dp=0x00000000
[10:08:25.815]    __ap=0x00000000
[10:08:25.816]    __traceout=0x00000000      (Trace Disabled)
[10:08:25.817]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:08:25.818]    __FlashAddr=0x00000000
[10:08:25.819]    __FlashLen=0x00000000
[10:08:25.820]    __FlashArg=0x00000000
[10:08:25.820]    __FlashOp=0x00000000
[10:08:25.821]    __Result=0x00000000
[10:08:25.822]    
[10:08:25.822]    // User-defined
[10:08:25.822]    DbgMCU_CR=0x00000007
[10:08:25.823]    DbgMCU_APB1_Fz=0x00000000
[10:08:25.823]    DbgMCU_APB2_Fz=0x00000000
[10:08:25.824]    DoOptionByteLoading=0x00000000
[10:08:25.825]  </debugvars>
[10:08:25.825]  
[10:08:25.826]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:08:25.827]    <block atomic="false" info="">
[10:08:25.827]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:08:25.829]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:08:25.830]    </block>
[10:08:25.830]    <block atomic="false" info="DbgMCU registers">
[10:08:25.831]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:08:25.832]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[10:08:25.835]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[10:08:25.836]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:08:25.837]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:08:25.838]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:08:25.839]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:08:25.840]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:08:25.841]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:08:25.841]    </block>
[10:08:25.841]  </sequence>
[10:08:25.842]  
[10:08:33.744]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:08:33.744]  
[10:08:33.745]  <debugvars>
[10:08:33.746]    // Pre-defined
[10:08:33.746]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:08:33.747]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:08:33.748]    __dp=0x00000000
[10:08:33.748]    __ap=0x00000000
[10:08:33.750]    __traceout=0x00000000      (Trace Disabled)
[10:08:33.751]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:08:33.752]    __FlashAddr=0x00000000
[10:08:33.753]    __FlashLen=0x00000000
[10:08:33.754]    __FlashArg=0x00000000
[10:08:33.754]    __FlashOp=0x00000000
[10:08:33.755]    __Result=0x00000000
[10:08:33.756]    
[10:08:33.756]    // User-defined
[10:08:33.756]    DbgMCU_CR=0x00000007
[10:08:33.757]    DbgMCU_APB1_Fz=0x00000000
[10:08:33.758]    DbgMCU_APB2_Fz=0x00000000
[10:08:33.758]    DoOptionByteLoading=0x00000000
[10:08:33.759]  </debugvars>
[10:08:33.760]  
[10:08:33.760]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:08:33.761]    <block atomic="false" info="">
[10:08:33.761]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:08:33.761]        // -> [connectionFlash <= 0x00000001]
[10:08:33.762]      __var FLASH_BASE = 0x40022000 ;
[10:08:33.762]        // -> [FLASH_BASE <= 0x40022000]
[10:08:33.762]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:08:33.764]        // -> [FLASH_CR <= 0x40022004]
[10:08:33.764]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:08:33.764]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:08:33.765]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:08:33.765]        // -> [LOCK_BIT <= 0x00000001]
[10:08:33.765]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:08:33.766]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:08:33.766]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:08:33.767]        // -> [FLASH_KEYR <= 0x4002200C]
[10:08:33.767]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:08:33.767]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:08:33.768]      __var FLASH_KEY2 = 0x02030405 ;
[10:08:33.769]        // -> [FLASH_KEY2 <= 0x02030405]
[10:08:33.769]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:08:33.770]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:08:33.770]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:08:33.770]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:08:33.771]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:08:33.771]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:08:33.771]      __var FLASH_CR_Value = 0 ;
[10:08:33.771]        // -> [FLASH_CR_Value <= 0x00000000]
[10:08:33.772]      __var DoDebugPortStop = 1 ;
[10:08:33.772]        // -> [DoDebugPortStop <= 0x00000001]
[10:08:33.772]      __var DP_CTRL_STAT = 0x4 ;
[10:08:33.773]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:08:33.773]      __var DP_SELECT = 0x8 ;
[10:08:33.773]        // -> [DP_SELECT <= 0x00000008]
[10:08:33.773]    </block>
[10:08:33.774]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:08:33.774]      // if-block "connectionFlash && DoOptionByteLoading"
[10:08:33.774]        // =>  FALSE
[10:08:33.774]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:08:33.774]    </control>
[10:08:33.775]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:08:33.775]      // if-block "DoDebugPortStop"
[10:08:33.775]        // =>  TRUE
[10:08:33.775]      <block atomic="false" info="">
[10:08:33.775]        WriteDP(DP_SELECT, 0x00000000);
[10:08:33.776]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:08:33.776]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:08:33.776]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:08:33.776]      </block>
[10:08:33.777]      // end if-block "DoDebugPortStop"
[10:08:33.777]    </control>
[10:08:33.777]  </sequence>
[10:08:33.777]  
[10:08:34.081]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:08:34.081]  
[10:08:34.081]  <debugvars>
[10:08:34.082]    // Pre-defined
[10:08:34.082]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:08:34.083]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:08:34.083]    __dp=0x00000000
[10:08:34.083]    __ap=0x00000000
[10:08:34.084]    __traceout=0x00000000      (Trace Disabled)
[10:08:34.084]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:08:34.084]    __FlashAddr=0x00000000
[10:08:34.084]    __FlashLen=0x00000000
[10:08:34.085]    __FlashArg=0x00000000
[10:08:34.085]    __FlashOp=0x00000000
[10:08:34.085]    __Result=0x00000000
[10:08:34.085]    
[10:08:34.085]    // User-defined
[10:08:34.085]    DbgMCU_CR=0x00000007
[10:08:34.086]    DbgMCU_APB1_Fz=0x00000000
[10:08:34.086]    DbgMCU_APB2_Fz=0x00000000
[10:08:34.086]    DoOptionByteLoading=0x00000000
[10:08:34.086]  </debugvars>
[10:08:34.086]  
[10:08:34.086]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:08:34.087]    <block atomic="false" info="">
[10:08:34.087]      Sequence("CheckID");
[10:08:34.087]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:08:34.087]          <block atomic="false" info="">
[10:08:34.087]            __var pidr1 = 0;
[10:08:34.088]              // -> [pidr1 <= 0x00000000]
[10:08:34.088]            __var pidr2 = 0;
[10:08:34.088]              // -> [pidr2 <= 0x00000000]
[10:08:34.088]            __var jep106id = 0;
[10:08:34.088]              // -> [jep106id <= 0x00000000]
[10:08:34.088]            __var ROMTableBase = 0;
[10:08:34.088]              // -> [ROMTableBase <= 0x00000000]
[10:08:34.088]            __ap = 0;      // AHB-AP
[10:08:34.088]              // -> [__ap <= 0x00000000]
[10:08:34.088]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:08:34.090]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:08:34.090]              // -> [ROMTableBase <= 0xF0000000]
[10:08:34.090]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:08:34.090]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:08:34.091]              // -> [pidr1 <= 0x00000004]
[10:08:34.092]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:08:34.092]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:08:34.093]              // -> [pidr2 <= 0x0000000A]
[10:08:34.093]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:08:34.093]              // -> [jep106id <= 0x00000020]
[10:08:34.094]          </block>
[10:08:34.094]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:08:34.094]            // if-block "jep106id != 0x20"
[10:08:34.094]              // =>  FALSE
[10:08:34.094]            // skip if-block "jep106id != 0x20"
[10:08:34.095]          </control>
[10:08:34.095]        </sequence>
[10:08:34.095]    </block>
[10:08:34.095]  </sequence>
[10:08:34.095]  
[10:08:34.106]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:08:34.106]  
[10:08:34.107]  <debugvars>
[10:08:34.107]    // Pre-defined
[10:08:34.107]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:08:34.108]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:08:34.108]    __dp=0x00000000
[10:08:34.108]    __ap=0x00000000
[10:08:34.108]    __traceout=0x00000000      (Trace Disabled)
[10:08:34.109]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:08:34.109]    __FlashAddr=0x00000000
[10:08:34.110]    __FlashLen=0x00000000
[10:08:34.110]    __FlashArg=0x00000000
[10:08:34.111]    __FlashOp=0x00000000
[10:08:34.111]    __Result=0x00000000
[10:08:34.111]    
[10:08:34.111]    // User-defined
[10:08:34.112]    DbgMCU_CR=0x00000007
[10:08:34.112]    DbgMCU_APB1_Fz=0x00000000
[10:08:34.112]    DbgMCU_APB2_Fz=0x00000000
[10:08:34.113]    DoOptionByteLoading=0x00000000
[10:08:34.113]  </debugvars>
[10:08:34.113]  
[10:08:34.113]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:08:34.114]    <block atomic="false" info="">
[10:08:34.114]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:08:34.115]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:08:34.115]    </block>
[10:08:34.116]    <block atomic="false" info="DbgMCU registers">
[10:08:34.116]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:08:34.117]        // -> [Read32(0x40021034) => 0x00000200]   (__dp=0x00000000, __ap=0x00000000)
[10:08:34.118]        // -> [Write32(0x40021034, 0x00400200)]   (__dp=0x00000000, __ap=0x00000000)
[10:08:34.118]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:08:34.119]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:08:34.119]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:08:34.120]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:08:34.120]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:08:34.121]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:08:34.122]    </block>
[10:08:34.122]  </sequence>
[10:08:34.122]  
[10:10:15.625]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:10:15.625]  
[10:10:15.625]  <debugvars>
[10:10:15.626]    // Pre-defined
[10:10:15.626]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:10:15.627]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:10:15.627]    __dp=0x00000000
[10:10:15.628]    __ap=0x00000000
[10:10:15.628]    __traceout=0x00000000      (Trace Disabled)
[10:10:15.629]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:10:15.629]    __FlashAddr=0x00000000
[10:10:15.629]    __FlashLen=0x00000000
[10:10:15.630]    __FlashArg=0x00000000
[10:10:15.630]    __FlashOp=0x00000000
[10:10:15.631]    __Result=0x00000000
[10:10:15.632]    
[10:10:15.632]    // User-defined
[10:10:15.632]    DbgMCU_CR=0x00000007
[10:10:15.633]    DbgMCU_APB1_Fz=0x00000000
[10:10:15.633]    DbgMCU_APB2_Fz=0x00000000
[10:10:15.633]    DoOptionByteLoading=0x00000000
[10:10:15.634]  </debugvars>
[10:10:15.634]  
[10:10:15.634]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:10:15.635]    <block atomic="false" info="">
[10:10:15.635]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:10:15.635]        // -> [connectionFlash <= 0x00000000]
[10:10:15.636]      __var FLASH_BASE = 0x40022000 ;
[10:10:15.636]        // -> [FLASH_BASE <= 0x40022000]
[10:10:15.636]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:10:15.636]        // -> [FLASH_CR <= 0x40022004]
[10:10:15.637]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:10:15.637]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:10:15.637]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:10:15.637]        // -> [LOCK_BIT <= 0x00000001]
[10:10:15.637]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:10:15.637]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:10:15.638]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:10:15.638]        // -> [FLASH_KEYR <= 0x4002200C]
[10:10:15.638]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:10:15.638]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:10:15.639]      __var FLASH_KEY2 = 0x02030405 ;
[10:10:15.639]        // -> [FLASH_KEY2 <= 0x02030405]
[10:10:15.639]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:10:15.639]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:10:15.639]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:10:15.639]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:10:15.640]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:10:15.640]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:10:15.640]      __var FLASH_CR_Value = 0 ;
[10:10:15.640]        // -> [FLASH_CR_Value <= 0x00000000]
[10:10:15.640]      __var DoDebugPortStop = 1 ;
[10:10:15.641]        // -> [DoDebugPortStop <= 0x00000001]
[10:10:15.641]      __var DP_CTRL_STAT = 0x4 ;
[10:10:15.641]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:10:15.641]      __var DP_SELECT = 0x8 ;
[10:10:15.641]        // -> [DP_SELECT <= 0x00000008]
[10:10:15.641]    </block>
[10:10:15.642]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:10:15.642]      // if-block "connectionFlash && DoOptionByteLoading"
[10:10:15.642]        // =>  FALSE
[10:10:15.642]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:10:15.642]    </control>
[10:10:15.642]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:10:15.643]      // if-block "DoDebugPortStop"
[10:10:15.643]        // =>  TRUE
[10:10:15.643]      <block atomic="false" info="">
[10:10:15.643]        WriteDP(DP_SELECT, 0x00000000);
[10:10:15.644]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:10:15.644]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:10:15.644]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:10:15.644]      </block>
[10:10:15.645]      // end if-block "DoDebugPortStop"
[10:10:15.645]    </control>
[10:10:15.645]  </sequence>
[10:10:15.645]  
[10:15:11.886]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:15:11.886]  
[10:15:11.886]  <debugvars>
[10:15:11.887]    // Pre-defined
[10:15:11.887]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:15:11.887]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:15:11.887]    __dp=0x00000000
[10:15:11.888]    __ap=0x00000000
[10:15:11.888]    __traceout=0x00000000      (Trace Disabled)
[10:15:11.888]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:15:11.889]    __FlashAddr=0x00000000
[10:15:11.889]    __FlashLen=0x00000000
[10:15:11.889]    __FlashArg=0x00000000
[10:15:11.889]    __FlashOp=0x00000000
[10:15:11.890]    __Result=0x00000000
[10:15:11.890]    
[10:15:11.890]    // User-defined
[10:15:11.890]    DbgMCU_CR=0x00000007
[10:15:11.891]    DbgMCU_APB1_Fz=0x00000000
[10:15:11.891]    DbgMCU_APB2_Fz=0x00000000
[10:15:11.891]    DoOptionByteLoading=0x00000000
[10:15:11.891]  </debugvars>
[10:15:11.891]  
[10:15:11.892]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:15:11.892]    <block atomic="false" info="">
[10:15:11.892]      Sequence("CheckID");
[10:15:11.892]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:15:11.892]          <block atomic="false" info="">
[10:15:11.892]            __var pidr1 = 0;
[10:15:11.893]              // -> [pidr1 <= 0x00000000]
[10:15:11.893]            __var pidr2 = 0;
[10:15:11.893]              // -> [pidr2 <= 0x00000000]
[10:15:11.893]            __var jep106id = 0;
[10:15:11.893]              // -> [jep106id <= 0x00000000]
[10:15:11.894]            __var ROMTableBase = 0;
[10:15:11.894]              // -> [ROMTableBase <= 0x00000000]
[10:15:11.894]            __ap = 0;      // AHB-AP
[10:15:11.894]              // -> [__ap <= 0x00000000]
[10:15:11.894]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:15:11.895]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:15:11.895]              // -> [ROMTableBase <= 0xF0000000]
[10:15:11.896]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:15:11.897]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:15:11.897]              // -> [pidr1 <= 0x00000004]
[10:15:11.897]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:15:11.898]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:15:11.898]              // -> [pidr2 <= 0x0000000A]
[10:15:11.898]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:15:11.899]              // -> [jep106id <= 0x00000020]
[10:15:11.899]          </block>
[10:15:11.899]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:15:11.899]            // if-block "jep106id != 0x20"
[10:15:11.900]              // =>  FALSE
[10:15:11.900]            // skip if-block "jep106id != 0x20"
[10:15:11.900]          </control>
[10:15:11.900]        </sequence>
[10:15:11.900]    </block>
[10:15:11.900]  </sequence>
[10:15:11.901]  
[10:15:11.911]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:15:11.911]  
[10:15:11.911]  <debugvars>
[10:15:11.911]    // Pre-defined
[10:15:11.912]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:15:11.912]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:15:11.912]    __dp=0x00000000
[10:15:11.912]    __ap=0x00000000
[10:15:11.913]    __traceout=0x00000000      (Trace Disabled)
[10:15:11.913]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:15:11.913]    __FlashAddr=0x00000000
[10:15:11.913]    __FlashLen=0x00000000
[10:15:11.913]    __FlashArg=0x00000000
[10:15:11.913]    __FlashOp=0x00000000
[10:15:11.915]    __Result=0x00000000
[10:15:11.915]    
[10:15:11.915]    // User-defined
[10:15:11.915]    DbgMCU_CR=0x00000007
[10:15:11.915]    DbgMCU_APB1_Fz=0x00000000
[10:15:11.915]    DbgMCU_APB2_Fz=0x00000000
[10:15:11.915]    DoOptionByteLoading=0x00000000
[10:15:11.916]  </debugvars>
[10:15:11.916]  
[10:15:11.916]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:15:11.916]    <block atomic="false" info="">
[10:15:11.916]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:15:11.917]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:15:11.917]    </block>
[10:15:11.918]    <block atomic="false" info="DbgMCU registers">
[10:15:11.918]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:15:11.919]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[10:15:11.920]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[10:15:11.920]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:15:11.921]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:15:11.921]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:15:11.922]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:15:11.922]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:15:11.923]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:15:11.924]    </block>
[10:15:11.924]  </sequence>
[10:15:11.924]  
[10:15:19.878]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:15:19.878]  
[10:15:19.879]  <debugvars>
[10:15:19.879]    // Pre-defined
[10:15:19.880]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:15:19.880]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:15:19.881]    __dp=0x00000000
[10:15:19.882]    __ap=0x00000000
[10:15:19.882]    __traceout=0x00000000      (Trace Disabled)
[10:15:19.883]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:15:19.883]    __FlashAddr=0x00000000
[10:15:19.883]    __FlashLen=0x00000000
[10:15:19.884]    __FlashArg=0x00000000
[10:15:19.884]    __FlashOp=0x00000000
[10:15:19.885]    __Result=0x00000000
[10:15:19.885]    
[10:15:19.885]    // User-defined
[10:15:19.885]    DbgMCU_CR=0x00000007
[10:15:19.885]    DbgMCU_APB1_Fz=0x00000000
[10:15:19.886]    DbgMCU_APB2_Fz=0x00000000
[10:15:19.887]    DoOptionByteLoading=0x00000000
[10:15:19.887]  </debugvars>
[10:15:19.887]  
[10:15:19.888]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:15:19.888]    <block atomic="false" info="">
[10:15:19.888]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:15:19.889]        // -> [connectionFlash <= 0x00000001]
[10:15:19.889]      __var FLASH_BASE = 0x40022000 ;
[10:15:19.889]        // -> [FLASH_BASE <= 0x40022000]
[10:15:19.889]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:15:19.889]        // -> [FLASH_CR <= 0x40022004]
[10:15:19.890]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:15:19.890]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:15:19.890]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:15:19.890]        // -> [LOCK_BIT <= 0x00000001]
[10:15:19.890]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:15:19.891]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:15:19.891]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:15:19.891]        // -> [FLASH_KEYR <= 0x4002200C]
[10:15:19.891]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:15:19.891]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:15:19.891]      __var FLASH_KEY2 = 0x02030405 ;
[10:15:19.892]        // -> [FLASH_KEY2 <= 0x02030405]
[10:15:19.892]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:15:19.892]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:15:19.892]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:15:19.892]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:15:19.893]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:15:19.893]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:15:19.893]      __var FLASH_CR_Value = 0 ;
[10:15:19.893]        // -> [FLASH_CR_Value <= 0x00000000]
[10:15:19.893]      __var DoDebugPortStop = 1 ;
[10:15:19.894]        // -> [DoDebugPortStop <= 0x00000001]
[10:15:19.894]      __var DP_CTRL_STAT = 0x4 ;
[10:15:19.894]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:15:19.894]      __var DP_SELECT = 0x8 ;
[10:15:19.894]        // -> [DP_SELECT <= 0x00000008]
[10:15:19.894]    </block>
[10:15:19.895]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:15:19.895]      // if-block "connectionFlash && DoOptionByteLoading"
[10:15:19.895]        // =>  FALSE
[10:15:19.895]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:15:19.895]    </control>
[10:15:19.896]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:15:19.896]      // if-block "DoDebugPortStop"
[10:15:19.896]        // =>  TRUE
[10:15:19.896]      <block atomic="false" info="">
[10:15:19.896]        WriteDP(DP_SELECT, 0x00000000);
[10:15:19.897]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:15:19.898]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:15:19.899]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:15:19.899]      </block>
[10:15:19.899]      // end if-block "DoDebugPortStop"
[10:15:19.899]    </control>
[10:15:19.900]  </sequence>
[10:15:19.900]  
[10:22:08.569]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:22:08.569]  
[10:22:08.570]  <debugvars>
[10:22:08.570]    // Pre-defined
[10:22:08.570]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:22:08.570]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:22:08.570]    __dp=0x00000000
[10:22:08.571]    __ap=0x00000000
[10:22:08.572]    __traceout=0x00000000      (Trace Disabled)
[10:22:08.572]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:22:08.572]    __FlashAddr=0x00000000
[10:22:08.573]    __FlashLen=0x00000000
[10:22:08.573]    __FlashArg=0x00000000
[10:22:08.573]    __FlashOp=0x00000000
[10:22:08.574]    __Result=0x00000000
[10:22:08.574]    
[10:22:08.574]    // User-defined
[10:22:08.574]    DbgMCU_CR=0x00000007
[10:22:08.574]    DbgMCU_APB1_Fz=0x00000000
[10:22:08.574]    DbgMCU_APB2_Fz=0x00000000
[10:22:08.575]    DoOptionByteLoading=0x00000000
[10:22:08.575]  </debugvars>
[10:22:08.575]  
[10:22:08.575]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:22:08.575]    <block atomic="false" info="">
[10:22:08.576]      Sequence("CheckID");
[10:22:08.576]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:22:08.576]          <block atomic="false" info="">
[10:22:08.576]            __var pidr1 = 0;
[10:22:08.576]              // -> [pidr1 <= 0x00000000]
[10:22:08.577]            __var pidr2 = 0;
[10:22:08.577]              // -> [pidr2 <= 0x00000000]
[10:22:08.577]            __var jep106id = 0;
[10:22:08.577]              // -> [jep106id <= 0x00000000]
[10:22:08.577]            __var ROMTableBase = 0;
[10:22:08.577]              // -> [ROMTableBase <= 0x00000000]
[10:22:08.578]            __ap = 0;      // AHB-AP
[10:22:08.578]              // -> [__ap <= 0x00000000]
[10:22:08.578]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:22:08.579]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:22:08.579]              // -> [ROMTableBase <= 0xF0000000]
[10:22:08.580]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:22:08.582]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:22:08.582]              // -> [pidr1 <= 0x00000004]
[10:22:08.582]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:22:08.583]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:22:08.584]              // -> [pidr2 <= 0x0000000A]
[10:22:08.584]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:22:08.584]              // -> [jep106id <= 0x00000020]
[10:22:08.584]          </block>
[10:22:08.585]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:22:08.585]            // if-block "jep106id != 0x20"
[10:22:08.585]              // =>  FALSE
[10:22:08.585]            // skip if-block "jep106id != 0x20"
[10:22:08.585]          </control>
[10:22:08.586]        </sequence>
[10:22:08.586]    </block>
[10:22:08.586]  </sequence>
[10:22:08.586]  
[10:22:08.600]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:22:08.600]  
[10:22:08.614]  <debugvars>
[10:22:08.615]    // Pre-defined
[10:22:08.615]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:22:08.616]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:22:08.616]    __dp=0x00000000
[10:22:08.616]    __ap=0x00000000
[10:22:08.617]    __traceout=0x00000000      (Trace Disabled)
[10:22:08.617]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:22:08.618]    __FlashAddr=0x00000000
[10:22:08.618]    __FlashLen=0x00000000
[10:22:08.618]    __FlashArg=0x00000000
[10:22:08.619]    __FlashOp=0x00000000
[10:22:08.619]    __Result=0x00000000
[10:22:08.619]    
[10:22:08.619]    // User-defined
[10:22:08.620]    DbgMCU_CR=0x00000007
[10:22:08.620]    DbgMCU_APB1_Fz=0x00000000
[10:22:08.620]    DbgMCU_APB2_Fz=0x00000000
[10:22:08.620]    DoOptionByteLoading=0x00000000
[10:22:08.620]  </debugvars>
[10:22:08.621]  
[10:22:08.621]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:22:08.621]    <block atomic="false" info="">
[10:22:08.621]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:22:08.622]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:08.622]    </block>
[10:22:08.623]    <block atomic="false" info="DbgMCU registers">
[10:22:08.623]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:22:08.624]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[10:22:08.626]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:08.627]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:22:08.628]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:08.628]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:22:08.629]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:08.629]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:22:08.631]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:08.631]    </block>
[10:22:08.631]  </sequence>
[10:22:08.631]  
[10:22:16.716]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:22:16.716]  
[10:22:16.717]  <debugvars>
[10:22:16.717]    // Pre-defined
[10:22:16.717]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:22:16.718]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:22:16.718]    __dp=0x00000000
[10:22:16.718]    __ap=0x00000000
[10:22:16.718]    __traceout=0x00000000      (Trace Disabled)
[10:22:16.718]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:22:16.719]    __FlashAddr=0x00000000
[10:22:16.719]    __FlashLen=0x00000000
[10:22:16.720]    __FlashArg=0x00000000
[10:22:16.720]    __FlashOp=0x00000000
[10:22:16.721]    __Result=0x00000000
[10:22:16.721]    
[10:22:16.721]    // User-defined
[10:22:16.721]    DbgMCU_CR=0x00000007
[10:22:16.721]    DbgMCU_APB1_Fz=0x00000000
[10:22:16.721]    DbgMCU_APB2_Fz=0x00000000
[10:22:16.721]    DoOptionByteLoading=0x00000000
[10:22:16.722]  </debugvars>
[10:22:16.722]  
[10:22:16.722]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:22:16.722]    <block atomic="false" info="">
[10:22:16.722]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:22:16.723]        // -> [connectionFlash <= 0x00000001]
[10:22:16.723]      __var FLASH_BASE = 0x40022000 ;
[10:22:16.723]        // -> [FLASH_BASE <= 0x40022000]
[10:22:16.723]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:22:16.724]        // -> [FLASH_CR <= 0x40022004]
[10:22:16.724]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:22:16.724]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:22:16.724]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:22:16.725]        // -> [LOCK_BIT <= 0x00000001]
[10:22:16.725]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:22:16.725]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:22:16.725]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:22:16.725]        // -> [FLASH_KEYR <= 0x4002200C]
[10:22:16.725]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:22:16.726]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:22:16.726]      __var FLASH_KEY2 = 0x02030405 ;
[10:22:16.726]        // -> [FLASH_KEY2 <= 0x02030405]
[10:22:16.726]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:22:16.726]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:22:16.727]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:22:16.727]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:22:16.727]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:22:16.727]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:22:16.727]      __var FLASH_CR_Value = 0 ;
[10:22:16.728]        // -> [FLASH_CR_Value <= 0x00000000]
[10:22:16.728]      __var DoDebugPortStop = 1 ;
[10:22:16.728]        // -> [DoDebugPortStop <= 0x00000001]
[10:22:16.728]      __var DP_CTRL_STAT = 0x4 ;
[10:22:16.728]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:22:16.728]      __var DP_SELECT = 0x8 ;
[10:22:16.729]        // -> [DP_SELECT <= 0x00000008]
[10:22:16.729]    </block>
[10:22:16.729]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:22:16.729]      // if-block "connectionFlash && DoOptionByteLoading"
[10:22:16.729]        // =>  FALSE
[10:22:16.730]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:22:16.730]    </control>
[10:22:16.730]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:22:16.730]      // if-block "DoDebugPortStop"
[10:22:16.730]        // =>  TRUE
[10:22:16.730]      <block atomic="false" info="">
[10:22:16.731]        WriteDP(DP_SELECT, 0x00000000);
[10:22:16.731]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:22:16.732]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:22:16.732]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:22:16.732]      </block>
[10:22:16.733]      // end if-block "DoDebugPortStop"
[10:22:16.733]    </control>
[10:22:16.733]  </sequence>
[10:22:16.734]  
[10:22:17.048]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:22:17.048]  
[10:22:17.048]  <debugvars>
[10:22:17.049]    // Pre-defined
[10:22:17.049]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:22:17.049]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:22:17.050]    __dp=0x00000000
[10:22:17.050]    __ap=0x00000000
[10:22:17.050]    __traceout=0x00000000      (Trace Disabled)
[10:22:17.051]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:22:17.051]    __FlashAddr=0x00000000
[10:22:17.051]    __FlashLen=0x00000000
[10:22:17.051]    __FlashArg=0x00000000
[10:22:17.052]    __FlashOp=0x00000000
[10:22:17.052]    __Result=0x00000000
[10:22:17.052]    
[10:22:17.052]    // User-defined
[10:22:17.052]    DbgMCU_CR=0x00000007
[10:22:17.052]    DbgMCU_APB1_Fz=0x00000000
[10:22:17.053]    DbgMCU_APB2_Fz=0x00000000
[10:22:17.053]    DoOptionByteLoading=0x00000000
[10:22:17.053]  </debugvars>
[10:22:17.053]  
[10:22:17.053]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:22:17.054]    <block atomic="false" info="">
[10:22:17.054]      Sequence("CheckID");
[10:22:17.054]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:22:17.054]          <block atomic="false" info="">
[10:22:17.054]            __var pidr1 = 0;
[10:22:17.054]              // -> [pidr1 <= 0x00000000]
[10:22:17.055]            __var pidr2 = 0;
[10:22:17.055]              // -> [pidr2 <= 0x00000000]
[10:22:17.055]            __var jep106id = 0;
[10:22:17.055]              // -> [jep106id <= 0x00000000]
[10:22:17.055]            __var ROMTableBase = 0;
[10:22:17.056]              // -> [ROMTableBase <= 0x00000000]
[10:22:17.056]            __ap = 0;      // AHB-AP
[10:22:17.056]              // -> [__ap <= 0x00000000]
[10:22:17.056]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:22:17.057]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:22:17.057]              // -> [ROMTableBase <= 0xF0000000]
[10:22:17.057]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:22:17.058]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:22:17.058]              // -> [pidr1 <= 0x00000004]
[10:22:17.059]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:22:17.059]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:22:17.060]              // -> [pidr2 <= 0x0000000A]
[10:22:17.060]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:22:17.060]              // -> [jep106id <= 0x00000020]
[10:22:17.060]          </block>
[10:22:17.060]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:22:17.060]            // if-block "jep106id != 0x20"
[10:22:17.061]              // =>  FALSE
[10:22:17.061]            // skip if-block "jep106id != 0x20"
[10:22:17.061]          </control>
[10:22:17.061]        </sequence>
[10:22:17.061]    </block>
[10:22:17.061]  </sequence>
[10:22:17.061]  
[10:22:17.074]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:22:17.074]  
[10:22:17.074]  <debugvars>
[10:22:17.075]    // Pre-defined
[10:22:17.075]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:22:17.075]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:22:17.075]    __dp=0x00000000
[10:22:17.076]    __ap=0x00000000
[10:22:17.076]    __traceout=0x00000000      (Trace Disabled)
[10:22:17.076]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:22:17.076]    __FlashAddr=0x00000000
[10:22:17.076]    __FlashLen=0x00000000
[10:22:17.077]    __FlashArg=0x00000000
[10:22:17.077]    __FlashOp=0x00000000
[10:22:17.077]    __Result=0x00000000
[10:22:17.077]    
[10:22:17.077]    // User-defined
[10:22:17.077]    DbgMCU_CR=0x00000007
[10:22:17.077]    DbgMCU_APB1_Fz=0x00000000
[10:22:17.078]    DbgMCU_APB2_Fz=0x00000000
[10:22:17.078]    DoOptionByteLoading=0x00000000
[10:22:17.078]  </debugvars>
[10:22:17.078]  
[10:22:17.079]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:22:17.079]    <block atomic="false" info="">
[10:22:17.079]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:22:17.080]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:17.080]    </block>
[10:22:17.080]    <block atomic="false" info="DbgMCU registers">
[10:22:17.081]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:22:17.082]        // -> [Read32(0x40021034) => 0x00000200]   (__dp=0x00000000, __ap=0x00000000)
[10:22:17.083]        // -> [Write32(0x40021034, 0x00400200)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:17.084]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:22:17.085]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:17.085]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:22:17.086]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:17.087]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:22:17.088]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:22:17.088]    </block>
[10:22:17.088]  </sequence>
[10:22:17.089]  
[10:22:56.557]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:22:56.557]  
[10:22:56.557]  <debugvars>
[10:22:56.557]    // Pre-defined
[10:22:56.557]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:22:56.558]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:22:56.558]    __dp=0x00000000
[10:22:56.558]    __ap=0x00000000
[10:22:56.558]    __traceout=0x00000000      (Trace Disabled)
[10:22:56.559]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:22:56.559]    __FlashAddr=0x00000000
[10:22:56.559]    __FlashLen=0x00000000
[10:22:56.559]    __FlashArg=0x00000000
[10:22:56.560]    __FlashOp=0x00000000
[10:22:56.560]    __Result=0x00000000
[10:22:56.560]    
[10:22:56.560]    // User-defined
[10:22:56.560]    DbgMCU_CR=0x00000007
[10:22:56.560]    DbgMCU_APB1_Fz=0x00000000
[10:22:56.560]    DbgMCU_APB2_Fz=0x00000000
[10:22:56.562]    DoOptionByteLoading=0x00000000
[10:22:56.562]  </debugvars>
[10:22:56.562]  
[10:22:56.562]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:22:56.563]    <block atomic="false" info="">
[10:22:56.563]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:22:56.563]        // -> [connectionFlash <= 0x00000000]
[10:22:56.563]      __var FLASH_BASE = 0x40022000 ;
[10:22:56.563]        // -> [FLASH_BASE <= 0x40022000]
[10:22:56.564]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:22:56.564]        // -> [FLASH_CR <= 0x40022004]
[10:22:56.564]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:22:56.564]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:22:56.564]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:22:56.565]        // -> [LOCK_BIT <= 0x00000001]
[10:22:56.565]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:22:56.565]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:22:56.565]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:22:56.565]        // -> [FLASH_KEYR <= 0x4002200C]
[10:22:56.565]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:22:56.565]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:22:56.565]      __var FLASH_KEY2 = 0x02030405 ;
[10:22:56.566]        // -> [FLASH_KEY2 <= 0x02030405]
[10:22:56.566]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:22:56.566]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:22:56.566]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:22:56.567]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:22:56.567]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:22:56.567]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:22:56.567]      __var FLASH_CR_Value = 0 ;
[10:22:56.567]        // -> [FLASH_CR_Value <= 0x00000000]
[10:22:56.568]      __var DoDebugPortStop = 1 ;
[10:22:56.568]        // -> [DoDebugPortStop <= 0x00000001]
[10:22:56.569]      __var DP_CTRL_STAT = 0x4 ;
[10:22:56.569]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:22:56.569]      __var DP_SELECT = 0x8 ;
[10:22:56.569]        // -> [DP_SELECT <= 0x00000008]
[10:22:56.569]    </block>
[10:22:56.569]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:22:56.569]      // if-block "connectionFlash && DoOptionByteLoading"
[10:22:56.569]        // =>  FALSE
[10:22:56.569]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:22:56.570]    </control>
[10:22:56.571]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:22:56.571]      // if-block "DoDebugPortStop"
[10:22:56.571]        // =>  TRUE
[10:22:56.571]      <block atomic="false" info="">
[10:22:56.572]        WriteDP(DP_SELECT, 0x00000000);
[10:22:56.572]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:22:56.572]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:22:56.572]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:22:56.572]      </block>
[10:22:56.573]      // end if-block "DoDebugPortStop"
[10:22:56.573]    </control>
[10:22:56.573]  </sequence>
[10:22:56.573]  
[10:24:31.672]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:24:31.672]  
[10:24:31.673]  <debugvars>
[10:24:31.673]    // Pre-defined
[10:24:31.673]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:24:31.674]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:24:31.674]    __dp=0x00000000
[10:24:31.674]    __ap=0x00000000
[10:24:31.675]    __traceout=0x00000000      (Trace Disabled)
[10:24:31.675]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:24:31.676]    __FlashAddr=0x00000000
[10:24:31.676]    __FlashLen=0x00000000
[10:24:31.676]    __FlashArg=0x00000000
[10:24:31.677]    __FlashOp=0x00000000
[10:24:31.677]    __Result=0x00000000
[10:24:31.677]    
[10:24:31.677]    // User-defined
[10:24:31.678]    DbgMCU_CR=0x00000007
[10:24:31.678]    DbgMCU_APB1_Fz=0x00000000
[10:24:31.678]    DbgMCU_APB2_Fz=0x00000000
[10:24:31.678]    DoOptionByteLoading=0x00000000
[10:24:31.678]  </debugvars>
[10:24:31.678]  
[10:24:31.678]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:24:31.678]    <block atomic="false" info="">
[10:24:31.679]      Sequence("CheckID");
[10:24:31.679]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:24:31.679]          <block atomic="false" info="">
[10:24:31.680]            __var pidr1 = 0;
[10:24:31.680]              // -> [pidr1 <= 0x00000000]
[10:24:31.680]            __var pidr2 = 0;
[10:24:31.680]              // -> [pidr2 <= 0x00000000]
[10:24:31.680]            __var jep106id = 0;
[10:24:31.680]              // -> [jep106id <= 0x00000000]
[10:24:31.680]            __var ROMTableBase = 0;
[10:24:31.680]              // -> [ROMTableBase <= 0x00000000]
[10:24:31.680]            __ap = 0;      // AHB-AP
[10:24:31.681]              // -> [__ap <= 0x00000000]
[10:24:31.681]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:24:31.682]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:24:31.682]              // -> [ROMTableBase <= 0xF0000000]
[10:24:31.682]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:24:31.683]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:24:31.683]              // -> [pidr1 <= 0x00000004]
[10:24:31.684]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:24:31.685]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:24:31.685]              // -> [pidr2 <= 0x0000000A]
[10:24:31.685]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:24:31.686]              // -> [jep106id <= 0x00000020]
[10:24:31.686]          </block>
[10:24:31.686]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:24:31.686]            // if-block "jep106id != 0x20"
[10:24:31.686]              // =>  FALSE
[10:24:31.686]            // skip if-block "jep106id != 0x20"
[10:24:31.687]          </control>
[10:24:31.687]        </sequence>
[10:24:31.687]    </block>
[10:24:31.687]  </sequence>
[10:24:31.687]  
[10:24:31.700]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:24:31.700]  
[10:24:31.719]  <debugvars>
[10:24:31.719]    // Pre-defined
[10:24:31.720]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:24:31.720]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:24:31.720]    __dp=0x00000000
[10:24:31.721]    __ap=0x00000000
[10:24:31.721]    __traceout=0x00000000      (Trace Disabled)
[10:24:31.722]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:24:31.722]    __FlashAddr=0x00000000
[10:24:31.723]    __FlashLen=0x00000000
[10:24:31.723]    __FlashArg=0x00000000
[10:24:31.724]    __FlashOp=0x00000000
[10:24:31.724]    __Result=0x00000000
[10:24:31.724]    
[10:24:31.724]    // User-defined
[10:24:31.726]    DbgMCU_CR=0x00000007
[10:24:31.726]    DbgMCU_APB1_Fz=0x00000000
[10:24:31.726]    DbgMCU_APB2_Fz=0x00000000
[10:24:31.727]    DoOptionByteLoading=0x00000000
[10:24:31.727]  </debugvars>
[10:24:31.728]  
[10:24:31.728]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:24:31.729]    <block atomic="false" info="">
[10:24:31.729]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:24:31.731]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:24:31.731]    </block>
[10:24:31.731]    <block atomic="false" info="DbgMCU registers">
[10:24:31.732]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:24:31.732]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[10:24:31.733]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[10:24:31.733]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:24:31.734]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:24:31.734]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:24:31.734]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:24:31.736]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:24:31.736]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:24:31.737]    </block>
[10:24:31.737]  </sequence>
[10:24:31.737]  
[10:24:39.661]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:24:39.661]  
[10:24:39.661]  <debugvars>
[10:24:39.663]    // Pre-defined
[10:24:39.663]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:24:39.664]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:24:39.664]    __dp=0x00000000
[10:24:39.664]    __ap=0x00000000
[10:24:39.664]    __traceout=0x00000000      (Trace Disabled)
[10:24:39.665]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:24:39.665]    __FlashAddr=0x00000000
[10:24:39.665]    __FlashLen=0x00000000
[10:24:39.666]    __FlashArg=0x00000000
[10:24:39.666]    __FlashOp=0x00000000
[10:24:39.666]    __Result=0x00000000
[10:24:39.666]    
[10:24:39.666]    // User-defined
[10:24:39.666]    DbgMCU_CR=0x00000007
[10:24:39.666]    DbgMCU_APB1_Fz=0x00000000
[10:24:39.667]    DbgMCU_APB2_Fz=0x00000000
[10:24:39.667]    DoOptionByteLoading=0x00000000
[10:24:39.667]  </debugvars>
[10:24:39.667]  
[10:24:39.667]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:24:39.668]    <block atomic="false" info="">
[10:24:39.668]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:24:39.668]        // -> [connectionFlash <= 0x00000001]
[10:24:39.668]      __var FLASH_BASE = 0x40022000 ;
[10:24:39.668]        // -> [FLASH_BASE <= 0x40022000]
[10:24:39.668]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:24:39.669]        // -> [FLASH_CR <= 0x40022004]
[10:24:39.669]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:24:39.669]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:24:39.669]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:24:39.669]        // -> [LOCK_BIT <= 0x00000001]
[10:24:39.669]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:24:39.670]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:24:39.670]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:24:39.670]        // -> [FLASH_KEYR <= 0x4002200C]
[10:24:39.670]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:24:39.670]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:24:39.670]      __var FLASH_KEY2 = 0x02030405 ;
[10:24:39.672]        // -> [FLASH_KEY2 <= 0x02030405]
[10:24:39.672]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:24:39.672]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:24:39.672]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:24:39.672]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:24:39.672]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:24:39.673]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:24:39.673]      __var FLASH_CR_Value = 0 ;
[10:24:39.673]        // -> [FLASH_CR_Value <= 0x00000000]
[10:24:39.673]      __var DoDebugPortStop = 1 ;
[10:24:39.674]        // -> [DoDebugPortStop <= 0x00000001]
[10:24:39.674]      __var DP_CTRL_STAT = 0x4 ;
[10:24:39.674]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:24:39.674]      __var DP_SELECT = 0x8 ;
[10:24:39.674]        // -> [DP_SELECT <= 0x00000008]
[10:24:39.675]    </block>
[10:24:39.675]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:24:39.675]      // if-block "connectionFlash && DoOptionByteLoading"
[10:24:39.675]        // =>  FALSE
[10:24:39.675]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:24:39.676]    </control>
[10:24:39.676]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:24:39.676]      // if-block "DoDebugPortStop"
[10:24:39.676]        // =>  TRUE
[10:24:39.676]      <block atomic="false" info="">
[10:24:39.676]        WriteDP(DP_SELECT, 0x00000000);
[10:24:39.678]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:24:39.678]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:24:39.678]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:24:39.678]      </block>
[10:24:39.679]      // end if-block "DoDebugPortStop"
[10:24:39.679]    </control>
[10:24:39.679]  </sequence>
[10:24:39.679]  
[10:24:39.995]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:24:39.995]  
[10:24:39.995]  <debugvars>
[10:24:39.996]    // Pre-defined
[10:24:39.996]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:24:39.996]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:24:39.997]    __dp=0x00000000
[10:24:39.997]    __ap=0x00000000
[10:24:39.997]    __traceout=0x00000000      (Trace Disabled)
[10:24:39.997]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:24:39.998]    __FlashAddr=0x00000000
[10:24:39.998]    __FlashLen=0x00000000
[10:24:39.998]    __FlashArg=0x00000000
[10:24:39.999]    __FlashOp=0x00000000
[10:24:39.999]    __Result=0x00000000
[10:24:39.999]    
[10:24:39.999]    // User-defined
[10:24:39.999]    DbgMCU_CR=0x00000007
[10:24:40.000]    DbgMCU_APB1_Fz=0x00000000
[10:24:40.000]    DbgMCU_APB2_Fz=0x00000000
[10:24:40.000]    DoOptionByteLoading=0x00000000
[10:24:40.001]  </debugvars>
[10:24:40.001]  
[10:24:40.001]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:24:40.002]    <block atomic="false" info="">
[10:24:40.002]      Sequence("CheckID");
[10:24:40.003]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:24:40.003]          <block atomic="false" info="">
[10:24:40.003]            __var pidr1 = 0;
[10:24:40.003]              // -> [pidr1 <= 0x00000000]
[10:24:40.003]            __var pidr2 = 0;
[10:24:40.004]              // -> [pidr2 <= 0x00000000]
[10:24:40.004]            __var jep106id = 0;
[10:24:40.004]              // -> [jep106id <= 0x00000000]
[10:24:40.004]            __var ROMTableBase = 0;
[10:24:40.004]              // -> [ROMTableBase <= 0x00000000]
[10:24:40.004]            __ap = 0;      // AHB-AP
[10:24:40.005]              // -> [__ap <= 0x00000000]
[10:24:40.005]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:24:40.005]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:24:40.006]              // -> [ROMTableBase <= 0xF0000000]
[10:24:40.006]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:24:40.007]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:24:40.007]              // -> [pidr1 <= 0x00000004]
[10:24:40.007]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:24:40.008]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:24:40.008]              // -> [pidr2 <= 0x0000000A]
[10:24:40.009]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:24:40.009]              // -> [jep106id <= 0x00000020]
[10:24:40.009]          </block>
[10:24:40.009]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:24:40.009]            // if-block "jep106id != 0x20"
[10:24:40.010]              // =>  FALSE
[10:24:40.010]            // skip if-block "jep106id != 0x20"
[10:24:40.010]          </control>
[10:24:40.010]        </sequence>
[10:24:40.010]    </block>
[10:24:40.010]  </sequence>
[10:24:40.011]  
[10:24:40.022]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:24:40.022]  
[10:24:40.023]  <debugvars>
[10:24:40.023]    // Pre-defined
[10:24:40.023]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:24:40.024]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:24:40.024]    __dp=0x00000000
[10:24:40.024]    __ap=0x00000000
[10:24:40.024]    __traceout=0x00000000      (Trace Disabled)
[10:24:40.025]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:24:40.025]    __FlashAddr=0x00000000
[10:24:40.025]    __FlashLen=0x00000000
[10:24:40.026]    __FlashArg=0x00000000
[10:24:40.026]    __FlashOp=0x00000000
[10:24:40.026]    __Result=0x00000000
[10:24:40.026]    
[10:24:40.026]    // User-defined
[10:24:40.026]    DbgMCU_CR=0x00000007
[10:24:40.027]    DbgMCU_APB1_Fz=0x00000000
[10:24:40.027]    DbgMCU_APB2_Fz=0x00000000
[10:24:40.027]    DoOptionByteLoading=0x00000000
[10:24:40.027]  </debugvars>
[10:24:40.027]  
[10:24:40.027]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:24:40.027]    <block atomic="false" info="">
[10:24:40.027]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:24:40.029]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:24:40.029]    </block>
[10:24:40.029]    <block atomic="false" info="DbgMCU registers">
[10:24:40.030]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:24:40.030]        // -> [Read32(0x40021034) => 0x00000200]   (__dp=0x00000000, __ap=0x00000000)
[10:24:40.031]        // -> [Write32(0x40021034, 0x00400200)]   (__dp=0x00000000, __ap=0x00000000)
[10:24:40.032]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:24:40.033]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:24:40.033]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:24:40.034]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:24:40.034]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:24:40.035]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:24:40.035]    </block>
[10:24:40.036]  </sequence>
[10:24:40.036]  
[10:26:12.453]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:26:12.453]  
[10:26:12.454]  <debugvars>
[10:26:12.454]    // Pre-defined
[10:26:12.454]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:26:12.455]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:26:12.455]    __dp=0x00000000
[10:26:12.455]    __ap=0x00000000
[10:26:12.455]    __traceout=0x00000000      (Trace Disabled)
[10:26:12.456]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:26:12.456]    __FlashAddr=0x00000000
[10:26:12.458]    __FlashLen=0x00000000
[10:26:12.458]    __FlashArg=0x00000000
[10:26:12.458]    __FlashOp=0x00000000
[10:26:12.458]    __Result=0x00000000
[10:26:12.458]    
[10:26:12.458]    // User-defined
[10:26:12.458]    DbgMCU_CR=0x00000007
[10:26:12.459]    DbgMCU_APB1_Fz=0x00000000
[10:26:12.460]    DbgMCU_APB2_Fz=0x00000000
[10:26:12.460]    DoOptionByteLoading=0x00000000
[10:26:12.460]  </debugvars>
[10:26:12.460]  
[10:26:12.460]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:26:12.461]    <block atomic="false" info="">
[10:26:12.462]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:26:12.462]        // -> [connectionFlash <= 0x00000000]
[10:26:12.462]      __var FLASH_BASE = 0x40022000 ;
[10:26:12.462]        // -> [FLASH_BASE <= 0x40022000]
[10:26:12.462]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:26:12.462]        // -> [FLASH_CR <= 0x40022004]
[10:26:12.462]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:26:12.463]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:26:12.463]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:26:12.463]        // -> [LOCK_BIT <= 0x00000001]
[10:26:12.463]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:26:12.463]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:26:12.464]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:26:12.464]        // -> [FLASH_KEYR <= 0x4002200C]
[10:26:12.464]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:26:12.465]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:26:12.465]      __var FLASH_KEY2 = 0x02030405 ;
[10:26:12.466]        // -> [FLASH_KEY2 <= 0x02030405]
[10:26:12.466]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:26:12.466]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:26:12.466]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:26:12.466]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:26:12.466]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:26:12.467]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:26:12.467]      __var FLASH_CR_Value = 0 ;
[10:26:12.468]        // -> [FLASH_CR_Value <= 0x00000000]
[10:26:12.468]      __var DoDebugPortStop = 1 ;
[10:26:12.468]        // -> [DoDebugPortStop <= 0x00000001]
[10:26:12.469]      __var DP_CTRL_STAT = 0x4 ;
[10:26:12.469]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:26:12.469]      __var DP_SELECT = 0x8 ;
[10:26:12.469]        // -> [DP_SELECT <= 0x00000008]
[10:26:12.469]    </block>
[10:26:12.469]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:26:12.470]      // if-block "connectionFlash && DoOptionByteLoading"
[10:26:12.470]        // =>  FALSE
[10:26:12.470]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:26:12.471]    </control>
[10:26:12.471]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:26:12.472]      // if-block "DoDebugPortStop"
[10:26:12.472]        // =>  TRUE
[10:26:12.472]      <block atomic="false" info="">
[10:26:12.472]        WriteDP(DP_SELECT, 0x00000000);
[10:26:12.473]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:26:12.473]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:26:12.473]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:26:12.473]      </block>
[10:26:12.474]      // end if-block "DoDebugPortStop"
[10:26:12.474]    </control>
[10:26:12.474]  </sequence>
[10:26:12.474]  
[10:27:28.678]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:27:28.678]  
[10:27:28.678]  <debugvars>
[10:27:28.678]    // Pre-defined
[10:27:28.679]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:27:28.679]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:27:28.679]    __dp=0x00000000
[10:27:28.680]    __ap=0x00000000
[10:27:28.681]    __traceout=0x00000000      (Trace Disabled)
[10:27:28.681]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:27:28.681]    __FlashAddr=0x00000000
[10:27:28.681]    __FlashLen=0x00000000
[10:27:28.682]    __FlashArg=0x00000000
[10:27:28.682]    __FlashOp=0x00000000
[10:27:28.682]    __Result=0x00000000
[10:27:28.682]    
[10:27:28.682]    // User-defined
[10:27:28.682]    DbgMCU_CR=0x00000007
[10:27:28.682]    DbgMCU_APB1_Fz=0x00000000
[10:27:28.682]    DbgMCU_APB2_Fz=0x00000000
[10:27:28.683]    DoOptionByteLoading=0x00000000
[10:27:28.683]  </debugvars>
[10:27:28.683]  
[10:27:28.683]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:27:28.685]    <block atomic="false" info="">
[10:27:28.685]      Sequence("CheckID");
[10:27:28.686]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:27:28.687]          <block atomic="false" info="">
[10:27:28.687]            __var pidr1 = 0;
[10:27:28.687]              // -> [pidr1 <= 0x00000000]
[10:27:28.688]            __var pidr2 = 0;
[10:27:28.688]              // -> [pidr2 <= 0x00000000]
[10:27:28.688]            __var jep106id = 0;
[10:27:28.688]              // -> [jep106id <= 0x00000000]
[10:27:28.689]            __var ROMTableBase = 0;
[10:27:28.689]              // -> [ROMTableBase <= 0x00000000]
[10:27:28.689]            __ap = 0;      // AHB-AP
[10:27:28.689]              // -> [__ap <= 0x00000000]
[10:27:28.689]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:27:28.690]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:27:28.690]              // -> [ROMTableBase <= 0xF0000000]
[10:27:28.691]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:27:28.692]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:27:28.692]              // -> [pidr1 <= 0x00000004]
[10:27:28.692]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:27:28.693]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:27:28.694]              // -> [pidr2 <= 0x0000000A]
[10:27:28.694]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:27:28.694]              // -> [jep106id <= 0x00000020]
[10:27:28.694]          </block>
[10:27:28.695]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:27:28.695]            // if-block "jep106id != 0x20"
[10:27:28.695]              // =>  FALSE
[10:27:28.695]            // skip if-block "jep106id != 0x20"
[10:27:28.695]          </control>
[10:27:28.696]        </sequence>
[10:27:28.696]    </block>
[10:27:28.696]  </sequence>
[10:27:28.696]  
[10:27:28.708]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:27:28.708]  
[10:27:28.722]  <debugvars>
[10:27:28.723]    // Pre-defined
[10:27:28.723]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:27:28.724]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:27:28.724]    __dp=0x00000000
[10:27:28.725]    __ap=0x00000000
[10:27:28.725]    __traceout=0x00000000      (Trace Disabled)
[10:27:28.726]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:27:28.727]    __FlashAddr=0x00000000
[10:27:28.727]    __FlashLen=0x00000000
[10:27:28.728]    __FlashArg=0x00000000
[10:27:28.728]    __FlashOp=0x00000000
[10:27:28.728]    __Result=0x00000000
[10:27:28.728]    
[10:27:28.728]    // User-defined
[10:27:28.729]    DbgMCU_CR=0x00000007
[10:27:28.730]    DbgMCU_APB1_Fz=0x00000000
[10:27:28.730]    DbgMCU_APB2_Fz=0x00000000
[10:27:28.731]    DoOptionByteLoading=0x00000000
[10:27:28.731]  </debugvars>
[10:27:28.732]  
[10:27:28.732]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:27:28.733]    <block atomic="false" info="">
[10:27:28.733]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:27:28.734]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:27:28.735]    </block>
[10:27:28.735]    <block atomic="false" info="DbgMCU registers">
[10:27:28.735]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:27:28.736]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[10:27:28.737]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[10:27:28.737]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:27:28.738]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:27:28.739]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:27:28.740]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:27:28.740]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:27:28.741]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:27:28.741]    </block>
[10:27:28.741]  </sequence>
[10:27:28.742]  
[10:27:37.070]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:27:37.070]  
[10:27:37.070]  <debugvars>
[10:27:37.071]    // Pre-defined
[10:27:37.071]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:27:37.071]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:27:37.072]    __dp=0x00000000
[10:27:37.072]    __ap=0x00000000
[10:27:37.072]    __traceout=0x00000000      (Trace Disabled)
[10:27:37.073]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:27:37.073]    __FlashAddr=0x00000000
[10:27:37.073]    __FlashLen=0x00000000
[10:27:37.073]    __FlashArg=0x00000000
[10:27:37.074]    __FlashOp=0x00000000
[10:27:37.074]    __Result=0x00000000
[10:27:37.074]    
[10:27:37.074]    // User-defined
[10:27:37.074]    DbgMCU_CR=0x00000007
[10:27:37.075]    DbgMCU_APB1_Fz=0x00000000
[10:27:37.075]    DbgMCU_APB2_Fz=0x00000000
[10:27:37.075]    DoOptionByteLoading=0x00000000
[10:27:37.075]  </debugvars>
[10:27:37.075]  
[10:27:37.076]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:27:37.076]    <block atomic="false" info="">
[10:27:37.076]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:27:37.076]        // -> [connectionFlash <= 0x00000001]
[10:27:37.076]      __var FLASH_BASE = 0x40022000 ;
[10:27:37.076]        // -> [FLASH_BASE <= 0x40022000]
[10:27:37.077]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:27:37.077]        // -> [FLASH_CR <= 0x40022004]
[10:27:37.077]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:27:37.077]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:27:37.077]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:27:37.077]        // -> [LOCK_BIT <= 0x00000001]
[10:27:37.078]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:27:37.078]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:27:37.078]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:27:37.078]        // -> [FLASH_KEYR <= 0x4002200C]
[10:27:37.078]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:27:37.079]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:27:37.079]      __var FLASH_KEY2 = 0x02030405 ;
[10:27:37.080]        // -> [FLASH_KEY2 <= 0x02030405]
[10:27:37.081]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:27:37.081]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:27:37.081]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:27:37.082]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:27:37.082]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:27:37.082]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:27:37.082]      __var FLASH_CR_Value = 0 ;
[10:27:37.082]        // -> [FLASH_CR_Value <= 0x00000000]
[10:27:37.083]      __var DoDebugPortStop = 1 ;
[10:27:37.083]        // -> [DoDebugPortStop <= 0x00000001]
[10:27:37.083]      __var DP_CTRL_STAT = 0x4 ;
[10:27:37.083]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:27:37.083]      __var DP_SELECT = 0x8 ;
[10:27:37.083]        // -> [DP_SELECT <= 0x00000008]
[10:27:37.084]    </block>
[10:27:37.084]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:27:37.084]      // if-block "connectionFlash && DoOptionByteLoading"
[10:27:37.084]        // =>  FALSE
[10:27:37.084]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:27:37.085]    </control>
[10:27:37.085]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:27:37.085]      // if-block "DoDebugPortStop"
[10:27:37.085]        // =>  TRUE
[10:27:37.086]      <block atomic="false" info="">
[10:27:37.086]        WriteDP(DP_SELECT, 0x00000000);
[10:27:37.087]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:27:37.087]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:27:37.088]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:27:37.089]      </block>
[10:27:37.089]      // end if-block "DoDebugPortStop"
[10:27:37.089]    </control>
[10:27:37.089]  </sequence>
[10:27:37.090]  
[10:27:37.472]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:27:37.472]  
[10:27:37.472]  <debugvars>
[10:27:37.473]    // Pre-defined
[10:27:37.473]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:27:37.473]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:27:37.473]    __dp=0x00000000
[10:27:37.474]    __ap=0x00000000
[10:27:37.474]    __traceout=0x00000000      (Trace Disabled)
[10:27:37.474]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:27:37.474]    __FlashAddr=0x00000000
[10:27:37.474]    __FlashLen=0x00000000
[10:27:37.475]    __FlashArg=0x00000000
[10:27:37.475]    __FlashOp=0x00000000
[10:27:37.475]    __Result=0x00000000
[10:27:37.475]    
[10:27:37.475]    // User-defined
[10:27:37.475]    DbgMCU_CR=0x00000007
[10:27:37.475]    DbgMCU_APB1_Fz=0x00000000
[10:27:37.476]    DbgMCU_APB2_Fz=0x00000000
[10:27:37.476]    DoOptionByteLoading=0x00000000
[10:27:37.476]  </debugvars>
[10:27:37.476]  
[10:27:37.477]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:27:37.477]    <block atomic="false" info="">
[10:27:37.477]      Sequence("CheckID");
[10:27:37.477]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:27:37.477]          <block atomic="false" info="">
[10:27:37.477]            __var pidr1 = 0;
[10:27:37.479]              // -> [pidr1 <= 0x00000000]
[10:27:37.479]            __var pidr2 = 0;
[10:27:37.479]              // -> [pidr2 <= 0x00000000]
[10:27:37.479]            __var jep106id = 0;
[10:27:37.479]              // -> [jep106id <= 0x00000000]
[10:27:37.480]            __var ROMTableBase = 0;
[10:27:37.480]              // -> [ROMTableBase <= 0x00000000]
[10:27:37.480]            __ap = 0;      // AHB-AP
[10:27:37.480]              // -> [__ap <= 0x00000000]
[10:27:37.480]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:27:37.481]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:27:37.481]              // -> [ROMTableBase <= 0xF0000000]
[10:27:37.482]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:27:37.482]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:27:37.483]              // -> [pidr1 <= 0x00000004]
[10:27:37.483]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:27:37.484]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:27:37.484]              // -> [pidr2 <= 0x0000000A]
[10:27:37.484]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:27:37.485]              // -> [jep106id <= 0x00000020]
[10:27:37.485]          </block>
[10:27:37.485]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:27:37.485]            // if-block "jep106id != 0x20"
[10:27:37.485]              // =>  FALSE
[10:27:37.485]            // skip if-block "jep106id != 0x20"
[10:27:37.486]          </control>
[10:27:37.486]        </sequence>
[10:27:37.486]    </block>
[10:27:37.486]  </sequence>
[10:27:37.486]  
[10:27:37.499]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:27:37.499]  
[10:27:37.500]  <debugvars>
[10:27:37.500]    // Pre-defined
[10:27:37.500]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:27:37.500]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:27:37.501]    __dp=0x00000000
[10:27:37.501]    __ap=0x00000000
[10:27:37.501]    __traceout=0x00000000      (Trace Disabled)
[10:27:37.501]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:27:37.501]    __FlashAddr=0x00000000
[10:27:37.503]    __FlashLen=0x00000000
[10:27:37.503]    __FlashArg=0x00000000
[10:27:37.503]    __FlashOp=0x00000000
[10:27:37.503]    __Result=0x00000000
[10:27:37.503]    
[10:27:37.503]    // User-defined
[10:27:37.504]    DbgMCU_CR=0x00000007
[10:27:37.504]    DbgMCU_APB1_Fz=0x00000000
[10:27:37.504]    DbgMCU_APB2_Fz=0x00000000
[10:27:37.505]    DoOptionByteLoading=0x00000000
[10:27:37.505]  </debugvars>
[10:27:37.505]  
[10:27:37.505]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:27:37.505]    <block atomic="false" info="">
[10:27:37.506]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:27:37.506]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:27:37.506]    </block>
[10:27:37.507]    <block atomic="false" info="DbgMCU registers">
[10:27:37.507]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:27:37.508]        // -> [Read32(0x40021034) => 0x00000200]   (__dp=0x00000000, __ap=0x00000000)
[10:27:37.508]        // -> [Write32(0x40021034, 0x00400200)]   (__dp=0x00000000, __ap=0x00000000)
[10:27:37.509]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:27:37.510]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:27:37.510]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:27:37.511]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:27:37.511]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:27:37.512]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:27:37.512]    </block>
[10:27:37.512]  </sequence>
[10:27:37.512]  
[10:28:08.389]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:28:08.389]  
[10:28:08.390]  <debugvars>
[10:28:08.391]    // Pre-defined
[10:28:08.391]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:28:08.392]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[10:28:08.393]    __dp=0x00000000
[10:28:08.393]    __ap=0x00000000
[10:28:08.394]    __traceout=0x00000000      (Trace Disabled)
[10:28:08.394]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:28:08.394]    __FlashAddr=0x00000000
[10:28:08.395]    __FlashLen=0x00000000
[10:28:08.395]    __FlashArg=0x00000000
[10:28:08.395]    __FlashOp=0x00000000
[10:28:08.396]    __Result=0x00000000
[10:28:08.396]    
[10:28:08.396]    // User-defined
[10:28:08.397]    DbgMCU_CR=0x00000007
[10:28:08.397]    DbgMCU_APB1_Fz=0x00000000
[10:28:08.398]    DbgMCU_APB2_Fz=0x00000000
[10:28:08.398]    DoOptionByteLoading=0x00000000
[10:28:08.398]  </debugvars>
[10:28:08.399]  
[10:28:08.399]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:28:08.399]    <block atomic="false" info="">
[10:28:08.399]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:28:08.400]        // -> [connectionFlash <= 0x00000000]
[10:28:08.400]      __var FLASH_BASE = 0x40022000 ;
[10:28:08.401]        // -> [FLASH_BASE <= 0x40022000]
[10:28:08.401]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:28:08.401]        // -> [FLASH_CR <= 0x40022004]
[10:28:08.401]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:28:08.401]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:28:08.401]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:28:08.402]        // -> [LOCK_BIT <= 0x00000001]
[10:28:08.402]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:28:08.402]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:28:08.402]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:28:08.402]        // -> [FLASH_KEYR <= 0x4002200C]
[10:28:08.403]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:28:08.403]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:28:08.403]      __var FLASH_KEY2 = 0x02030405 ;
[10:28:08.403]        // -> [FLASH_KEY2 <= 0x02030405]
[10:28:08.403]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:28:08.403]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:28:08.404]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:28:08.404]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:28:08.404]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:28:08.404]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:28:08.404]      __var FLASH_CR_Value = 0 ;
[10:28:08.405]        // -> [FLASH_CR_Value <= 0x00000000]
[10:28:08.405]      __var DoDebugPortStop = 1 ;
[10:28:08.405]        // -> [DoDebugPortStop <= 0x00000001]
[10:28:08.405]      __var DP_CTRL_STAT = 0x4 ;
[10:28:08.405]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:28:08.405]      __var DP_SELECT = 0x8 ;
[10:28:08.406]        // -> [DP_SELECT <= 0x00000008]
[10:28:08.406]    </block>
[10:28:08.406]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:28:08.406]      // if-block "connectionFlash && DoOptionByteLoading"
[10:28:08.406]        // =>  FALSE
[10:28:08.407]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:28:08.407]    </control>
[10:28:08.407]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:28:08.407]      // if-block "DoDebugPortStop"
[10:28:08.407]        // =>  TRUE
[10:28:08.407]      <block atomic="false" info="">
[10:28:08.408]        WriteDP(DP_SELECT, 0x00000000);
[10:28:08.408]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:28:08.408]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:28:08.409]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:28:08.409]      </block>
[10:28:08.409]      // end if-block "DoDebugPortStop"
[10:28:08.410]    </control>
[10:28:08.410]  </sequence>
[10:28:08.410]  
[10:28:12.106]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:28:12.106]  
[10:28:12.107]  <debugvars>
[10:28:12.107]    // Pre-defined
[10:28:12.107]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:28:12.108]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:28:12.108]    __dp=0x00000000
[10:28:12.108]    __ap=0x00000000
[10:28:12.109]    __traceout=0x00000000      (Trace Disabled)
[10:28:12.109]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:28:12.109]    __FlashAddr=0x00000000
[10:28:12.109]    __FlashLen=0x00000000
[10:28:12.110]    __FlashArg=0x00000000
[10:28:12.110]    __FlashOp=0x00000000
[10:28:12.110]    __Result=0x00000000
[10:28:12.110]    
[10:28:12.110]    // User-defined
[10:28:12.110]    DbgMCU_CR=0x00000007
[10:28:12.110]    DbgMCU_APB1_Fz=0x00000000
[10:28:12.111]    DbgMCU_APB2_Fz=0x00000000
[10:28:12.111]    DoOptionByteLoading=0x00000000
[10:28:12.111]  </debugvars>
[10:28:12.111]  
[10:28:12.111]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:28:12.111]    <block atomic="false" info="">
[10:28:12.112]      Sequence("CheckID");
[10:28:12.112]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:28:12.112]          <block atomic="false" info="">
[10:28:12.112]            __var pidr1 = 0;
[10:28:12.113]              // -> [pidr1 <= 0x00000000]
[10:28:12.113]            __var pidr2 = 0;
[10:28:12.113]              // -> [pidr2 <= 0x00000000]
[10:28:12.113]            __var jep106id = 0;
[10:28:12.114]              // -> [jep106id <= 0x00000000]
[10:28:12.114]            __var ROMTableBase = 0;
[10:28:12.114]              // -> [ROMTableBase <= 0x00000000]
[10:28:12.114]            __ap = 0;      // AHB-AP
[10:28:12.114]              // -> [__ap <= 0x00000000]
[10:28:12.115]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:28:12.115]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:28:12.116]              // -> [ROMTableBase <= 0xF0000000]
[10:28:12.116]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:28:12.117]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:28:12.117]              // -> [pidr1 <= 0x00000004]
[10:28:12.117]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:28:12.118]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:28:12.119]              // -> [pidr2 <= 0x0000000A]
[10:28:12.119]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:28:12.119]              // -> [jep106id <= 0x00000020]
[10:28:12.119]          </block>
[10:28:12.120]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:28:12.120]            // if-block "jep106id != 0x20"
[10:28:12.120]              // =>  FALSE
[10:28:12.120]            // skip if-block "jep106id != 0x20"
[10:28:12.120]          </control>
[10:28:12.120]        </sequence>
[10:28:12.121]    </block>
[10:28:12.121]  </sequence>
[10:28:12.121]  
[10:28:12.133]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:28:12.133]  
[10:28:12.133]  <debugvars>
[10:28:12.133]    // Pre-defined
[10:28:12.134]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:28:12.134]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:28:12.134]    __dp=0x00000000
[10:28:12.135]    __ap=0x00000000
[10:28:12.135]    __traceout=0x00000000      (Trace Disabled)
[10:28:12.135]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:28:12.135]    __FlashAddr=0x00000000
[10:28:12.136]    __FlashLen=0x00000000
[10:28:12.136]    __FlashArg=0x00000000
[10:28:12.136]    __FlashOp=0x00000000
[10:28:12.136]    __Result=0x00000000
[10:28:12.136]    
[10:28:12.136]    // User-defined
[10:28:12.137]    DbgMCU_CR=0x00000007
[10:28:12.137]    DbgMCU_APB1_Fz=0x00000000
[10:28:12.137]    DbgMCU_APB2_Fz=0x00000000
[10:28:12.137]    DoOptionByteLoading=0x00000000
[10:28:12.137]  </debugvars>
[10:28:12.137]  
[10:28:12.138]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:28:12.138]    <block atomic="false" info="">
[10:28:12.138]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:28:12.139]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:28:12.139]    </block>
[10:28:12.139]    <block atomic="false" info="DbgMCU registers">
[10:28:12.139]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:28:12.140]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[10:28:12.141]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[10:28:12.141]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:28:12.142]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:28:12.143]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:28:12.144]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:28:12.144]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:28:12.145]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:28:12.145]    </block>
[10:28:12.146]  </sequence>
[10:28:12.146]  
[10:28:19.977]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:28:19.977]  
[10:28:19.977]  <debugvars>
[10:28:19.979]    // Pre-defined
[10:28:19.979]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:28:19.980]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:28:19.981]    __dp=0x00000000
[10:28:19.981]    __ap=0x00000000
[10:28:19.981]    __traceout=0x00000000      (Trace Disabled)
[10:28:19.982]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:28:19.982]    __FlashAddr=0x00000000
[10:28:19.982]    __FlashLen=0x00000000
[10:28:19.983]    __FlashArg=0x00000000
[10:28:19.983]    __FlashOp=0x00000000
[10:28:19.983]    __Result=0x00000000
[10:28:19.984]    
[10:28:19.984]    // User-defined
[10:28:19.984]    DbgMCU_CR=0x00000007
[10:28:19.984]    DbgMCU_APB1_Fz=0x00000000
[10:28:19.984]    DbgMCU_APB2_Fz=0x00000000
[10:28:19.985]    DoOptionByteLoading=0x00000000
[10:28:19.986]  </debugvars>
[10:28:19.986]  
[10:28:19.986]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:28:19.986]    <block atomic="false" info="">
[10:28:19.986]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:28:19.986]        // -> [connectionFlash <= 0x00000001]
[10:28:19.987]      __var FLASH_BASE = 0x40022000 ;
[10:28:19.987]        // -> [FLASH_BASE <= 0x40022000]
[10:28:19.987]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:28:19.987]        // -> [FLASH_CR <= 0x40022004]
[10:28:19.987]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:28:19.988]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:28:19.988]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:28:19.988]        // -> [LOCK_BIT <= 0x00000001]
[10:28:19.988]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:28:19.988]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:28:19.989]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:28:19.989]        // -> [FLASH_KEYR <= 0x4002200C]
[10:28:19.989]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:28:19.989]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:28:19.989]      __var FLASH_KEY2 = 0x02030405 ;
[10:28:19.989]        // -> [FLASH_KEY2 <= 0x02030405]
[10:28:19.989]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:28:19.989]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:28:19.989]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:28:19.990]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:28:19.990]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:28:19.991]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:28:19.991]      __var FLASH_CR_Value = 0 ;
[10:28:19.992]        // -> [FLASH_CR_Value <= 0x00000000]
[10:28:19.992]      __var DoDebugPortStop = 1 ;
[10:28:19.992]        // -> [DoDebugPortStop <= 0x00000001]
[10:28:19.992]      __var DP_CTRL_STAT = 0x4 ;
[10:28:19.992]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:28:19.993]      __var DP_SELECT = 0x8 ;
[10:28:19.993]        // -> [DP_SELECT <= 0x00000008]
[10:28:19.993]    </block>
[10:28:19.993]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:28:19.993]      // if-block "connectionFlash && DoOptionByteLoading"
[10:28:19.993]        // =>  FALSE
[10:28:19.994]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:28:19.994]    </control>
[10:28:19.994]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:28:19.994]      // if-block "DoDebugPortStop"
[10:28:19.994]        // =>  TRUE
[10:28:19.995]      <block atomic="false" info="">
[10:28:19.995]        WriteDP(DP_SELECT, 0x00000000);
[10:28:19.995]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:28:19.996]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:28:19.996]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:28:19.996]      </block>
[10:28:19.996]      // end if-block "DoDebugPortStop"
[10:28:19.996]    </control>
[10:28:19.997]  </sequence>
[10:28:19.997]  
[14:41:04.169]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:41:04.169]  
[14:41:04.180]  <debugvars>
[14:41:04.180]    // Pre-defined
[14:41:04.181]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:41:04.181]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:41:04.181]    __dp=0x00000000
[14:41:04.181]    __ap=0x00000000
[14:41:04.182]    __traceout=0x00000000      (Trace Disabled)
[14:41:04.182]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:41:04.182]    __FlashAddr=0x00000000
[14:41:04.183]    __FlashLen=0x00000000
[14:41:04.183]    __FlashArg=0x00000000
[14:41:04.183]    __FlashOp=0x00000000
[14:41:04.184]    __Result=0x00000000
[14:41:04.184]    
[14:41:04.184]    // User-defined
[14:41:04.184]    DbgMCU_CR=0x00000007
[14:41:04.186]    DbgMCU_APB1_Fz=0x00000000
[14:41:04.186]    DbgMCU_APB2_Fz=0x00000000
[14:41:04.187]    DoOptionByteLoading=0x00000000
[14:41:04.187]  </debugvars>
[14:41:04.187]  
[14:41:04.188]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:41:04.188]    <block atomic="false" info="">
[14:41:04.188]      Sequence("CheckID");
[14:41:04.189]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:41:04.189]          <block atomic="false" info="">
[14:41:04.189]            __var pidr1 = 0;
[14:41:04.190]              // -> [pidr1 <= 0x00000000]
[14:41:04.190]            __var pidr2 = 0;
[14:41:04.191]              // -> [pidr2 <= 0x00000000]
[14:41:04.191]            __var jep106id = 0;
[14:41:04.191]              // -> [jep106id <= 0x00000000]
[14:41:04.192]            __var ROMTableBase = 0;
[14:41:04.192]              // -> [ROMTableBase <= 0x00000000]
[14:41:04.192]            __ap = 0;      // AHB-AP
[14:41:04.193]              // -> [__ap <= 0x00000000]
[14:41:04.193]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:41:04.193]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:41:04.193]              // -> [ROMTableBase <= 0xF0000000]
[14:41:04.194]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:41:04.196]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:41:04.196]              // -> [pidr1 <= 0x00000004]
[14:41:04.196]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:41:04.197]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:41:04.197]              // -> [pidr2 <= 0x0000000A]
[14:41:04.197]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:41:04.197]              // -> [jep106id <= 0x00000020]
[14:41:04.198]          </block>
[14:41:04.198]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:41:04.198]            // if-block "jep106id != 0x20"
[14:41:04.198]              // =>  FALSE
[14:41:04.200]            // skip if-block "jep106id != 0x20"
[14:41:04.200]          </control>
[14:41:04.200]        </sequence>
[14:41:04.201]    </block>
[14:41:04.201]  </sequence>
[14:41:04.201]  
[14:41:04.214]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:41:04.214]  
[14:41:04.231]  <debugvars>
[14:41:04.231]    // Pre-defined
[14:41:04.231]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:41:04.232]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:41:04.232]    __dp=0x00000000
[14:41:04.233]    __ap=0x00000000
[14:41:04.233]    __traceout=0x00000000      (Trace Disabled)
[14:41:04.234]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:41:04.235]    __FlashAddr=0x00000000
[14:41:04.235]    __FlashLen=0x00000000
[14:41:04.236]    __FlashArg=0x00000000
[14:41:04.236]    __FlashOp=0x00000000
[14:41:04.236]    __Result=0x00000000
[14:41:04.236]    
[14:41:04.236]    // User-defined
[14:41:04.237]    DbgMCU_CR=0x00000007
[14:41:04.237]    DbgMCU_APB1_Fz=0x00000000
[14:41:04.237]    DbgMCU_APB2_Fz=0x00000000
[14:41:04.238]    DoOptionByteLoading=0x00000000
[14:41:04.238]  </debugvars>
[14:41:04.238]  
[14:41:04.239]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:41:04.239]    <block atomic="false" info="">
[14:41:04.239]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:41:04.240]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:41:04.240]    </block>
[14:41:04.240]    <block atomic="false" info="DbgMCU registers">
[14:41:04.240]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:41:04.241]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[14:41:04.243]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[14:41:04.243]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:41:04.244]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:41:04.244]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:41:04.245]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:41:04.246]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:41:04.246]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:41:04.246]    </block>
[14:41:04.247]  </sequence>
[14:41:04.247]  
[14:41:12.190]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:41:12.190]  
[14:41:12.190]  <debugvars>
[14:41:12.192]    // Pre-defined
[14:41:12.192]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:41:12.192]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:41:12.193]    __dp=0x00000000
[14:41:12.194]    __ap=0x00000000
[14:41:12.194]    __traceout=0x00000000      (Trace Disabled)
[14:41:12.195]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:41:12.196]    __FlashAddr=0x00000000
[14:41:12.196]    __FlashLen=0x00000000
[14:41:12.196]    __FlashArg=0x00000000
[14:41:12.197]    __FlashOp=0x00000000
[14:41:12.197]    __Result=0x00000000
[14:41:12.197]    
[14:41:12.197]    // User-defined
[14:41:12.198]    DbgMCU_CR=0x00000007
[14:41:12.198]    DbgMCU_APB1_Fz=0x00000000
[14:41:12.198]    DbgMCU_APB2_Fz=0x00000000
[14:41:12.198]    DoOptionByteLoading=0x00000000
[14:41:12.198]  </debugvars>
[14:41:12.200]  
[14:41:12.200]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:41:12.200]    <block atomic="false" info="">
[14:41:12.201]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:41:12.201]        // -> [connectionFlash <= 0x00000001]
[14:41:12.201]      __var FLASH_BASE = 0x40022000 ;
[14:41:12.202]        // -> [FLASH_BASE <= 0x40022000]
[14:41:12.202]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:41:12.202]        // -> [FLASH_CR <= 0x40022004]
[14:41:12.202]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:41:12.203]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:41:12.203]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:41:12.203]        // -> [LOCK_BIT <= 0x00000001]
[14:41:12.203]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:41:12.203]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:41:12.204]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:41:12.204]        // -> [FLASH_KEYR <= 0x4002200C]
[14:41:12.204]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:41:12.204]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:41:12.204]      __var FLASH_KEY2 = 0x02030405 ;
[14:41:12.205]        // -> [FLASH_KEY2 <= 0x02030405]
[14:41:12.205]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:41:12.205]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:41:12.205]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:41:12.205]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:41:12.206]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:41:12.206]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:41:12.206]      __var FLASH_CR_Value = 0 ;
[14:41:12.206]        // -> [FLASH_CR_Value <= 0x00000000]
[14:41:12.206]      __var DoDebugPortStop = 1 ;
[14:41:12.206]        // -> [DoDebugPortStop <= 0x00000001]
[14:41:12.207]      __var DP_CTRL_STAT = 0x4 ;
[14:41:12.207]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:41:12.207]      __var DP_SELECT = 0x8 ;
[14:41:12.208]        // -> [DP_SELECT <= 0x00000008]
[14:41:12.208]    </block>
[14:41:12.208]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:41:12.208]      // if-block "connectionFlash && DoOptionByteLoading"
[14:41:12.208]        // =>  FALSE
[14:41:12.209]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:41:12.209]    </control>
[14:41:12.209]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:41:12.209]      // if-block "DoDebugPortStop"
[14:41:12.209]        // =>  TRUE
[14:41:12.210]      <block atomic="false" info="">
[14:41:12.210]        WriteDP(DP_SELECT, 0x00000000);
[14:41:12.210]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:41:12.210]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:41:12.211]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:41:12.211]      </block>
[14:41:12.211]      // end if-block "DoDebugPortStop"
[14:41:12.211]    </control>
[14:41:12.211]  </sequence>
[14:41:12.211]  
[14:45:35.589]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:45:35.589]  
[14:45:35.589]  <debugvars>
[14:45:35.589]    // Pre-defined
[14:45:35.589]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:45:35.589]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:45:35.589]    __dp=0x00000000
[14:45:35.589]    __ap=0x00000000
[14:45:35.590]    __traceout=0x00000000      (Trace Disabled)
[14:45:35.590]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:45:35.591]    __FlashAddr=0x00000000
[14:45:35.591]    __FlashLen=0x00000000
[14:45:35.591]    __FlashArg=0x00000000
[14:45:35.591]    __FlashOp=0x00000000
[14:45:35.591]    __Result=0x00000000
[14:45:35.592]    
[14:45:35.592]    // User-defined
[14:45:35.592]    DbgMCU_CR=0x00000007
[14:45:35.592]    DbgMCU_APB1_Fz=0x00000000
[14:45:35.592]    DbgMCU_APB2_Fz=0x00000000
[14:45:35.593]    DoOptionByteLoading=0x00000000
[14:45:35.593]  </debugvars>
[14:45:35.593]  
[14:45:35.593]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:45:35.593]    <block atomic="false" info="">
[14:45:35.594]      Sequence("CheckID");
[14:45:35.594]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:45:35.594]          <block atomic="false" info="">
[14:45:35.594]            __var pidr1 = 0;
[14:45:35.594]              // -> [pidr1 <= 0x00000000]
[14:45:35.595]            __var pidr2 = 0;
[14:45:35.595]              // -> [pidr2 <= 0x00000000]
[14:45:35.595]            __var jep106id = 0;
[14:45:35.595]              // -> [jep106id <= 0x00000000]
[14:45:35.596]            __var ROMTableBase = 0;
[14:45:35.596]              // -> [ROMTableBase <= 0x00000000]
[14:45:35.596]            __ap = 0;      // AHB-AP
[14:45:35.596]              // -> [__ap <= 0x00000000]
[14:45:35.596]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:45:35.597]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:45:35.597]              // -> [ROMTableBase <= 0xF0000000]
[14:45:35.597]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:45:35.599]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:45:35.599]              // -> [pidr1 <= 0x00000004]
[14:45:35.599]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:45:35.599]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:45:35.600]              // -> [pidr2 <= 0x0000000A]
[14:45:35.600]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:45:35.600]              // -> [jep106id <= 0x00000020]
[14:45:35.601]          </block>
[14:45:35.601]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:45:35.601]            // if-block "jep106id != 0x20"
[14:45:35.601]              // =>  FALSE
[14:45:35.601]            // skip if-block "jep106id != 0x20"
[14:45:35.601]          </control>
[14:45:35.601]        </sequence>
[14:45:35.601]    </block>
[14:45:35.602]  </sequence>
[14:45:35.602]  
[14:45:35.615]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:45:35.615]  
[14:45:35.615]  <debugvars>
[14:45:35.615]    // Pre-defined
[14:45:35.615]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:45:35.615]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:45:35.616]    __dp=0x00000000
[14:45:35.616]    __ap=0x00000000
[14:45:35.616]    __traceout=0x00000000      (Trace Disabled)
[14:45:35.616]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:45:35.617]    __FlashAddr=0x00000000
[14:45:35.617]    __FlashLen=0x00000000
[14:45:35.617]    __FlashArg=0x00000000
[14:45:35.617]    __FlashOp=0x00000000
[14:45:35.617]    __Result=0x00000000
[14:45:35.617]    
[14:45:35.617]    // User-defined
[14:45:35.618]    DbgMCU_CR=0x00000007
[14:45:35.618]    DbgMCU_APB1_Fz=0x00000000
[14:45:35.618]    DbgMCU_APB2_Fz=0x00000000
[14:45:35.618]    DoOptionByteLoading=0x00000000
[14:45:35.618]  </debugvars>
[14:45:35.618]  
[14:45:35.619]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:45:35.619]    <block atomic="false" info="">
[14:45:35.619]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:45:35.619]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:45:35.620]    </block>
[14:45:35.620]    <block atomic="false" info="DbgMCU registers">
[14:45:35.621]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:45:35.621]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[14:45:35.622]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[14:45:35.623]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:45:35.624]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:45:35.624]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:45:35.625]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:45:35.625]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:45:35.626]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:45:35.626]    </block>
[14:45:35.626]  </sequence>
[14:45:35.626]  
[14:45:43.501]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:45:43.501]  
[14:45:43.501]  <debugvars>
[14:45:43.502]    // Pre-defined
[14:45:43.503]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:45:43.503]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:45:43.503]    __dp=0x00000000
[14:45:43.503]    __ap=0x00000000
[14:45:43.504]    __traceout=0x00000000      (Trace Disabled)
[14:45:43.504]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:45:43.504]    __FlashAddr=0x00000000
[14:45:43.505]    __FlashLen=0x00000000
[14:45:43.505]    __FlashArg=0x00000000
[14:45:43.505]    __FlashOp=0x00000000
[14:45:43.505]    __Result=0x00000000
[14:45:43.505]    
[14:45:43.505]    // User-defined
[14:45:43.505]    DbgMCU_CR=0x00000007
[14:45:43.506]    DbgMCU_APB1_Fz=0x00000000
[14:45:43.506]    DbgMCU_APB2_Fz=0x00000000
[14:45:43.506]    DoOptionByteLoading=0x00000000
[14:45:43.506]  </debugvars>
[14:45:43.506]  
[14:45:43.506]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:45:43.507]    <block atomic="false" info="">
[14:45:43.507]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:45:43.507]        // -> [connectionFlash <= 0x00000001]
[14:45:43.508]      __var FLASH_BASE = 0x40022000 ;
[14:45:43.508]        // -> [FLASH_BASE <= 0x40022000]
[14:45:43.508]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:45:43.508]        // -> [FLASH_CR <= 0x40022004]
[14:45:43.508]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:45:43.508]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:45:43.508]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:45:43.509]        // -> [LOCK_BIT <= 0x00000001]
[14:45:43.509]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:45:43.509]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:45:43.509]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:45:43.509]        // -> [FLASH_KEYR <= 0x4002200C]
[14:45:43.510]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:45:43.510]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:45:43.511]      __var FLASH_KEY2 = 0x02030405 ;
[14:45:43.511]        // -> [FLASH_KEY2 <= 0x02030405]
[14:45:43.511]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:45:43.511]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:45:43.511]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:45:43.511]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:45:43.512]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:45:43.512]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:45:43.512]      __var FLASH_CR_Value = 0 ;
[14:45:43.512]        // -> [FLASH_CR_Value <= 0x00000000]
[14:45:43.512]      __var DoDebugPortStop = 1 ;
[14:45:43.512]        // -> [DoDebugPortStop <= 0x00000001]
[14:45:43.513]      __var DP_CTRL_STAT = 0x4 ;
[14:45:43.513]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:45:43.513]      __var DP_SELECT = 0x8 ;
[14:45:43.513]        // -> [DP_SELECT <= 0x00000008]
[14:45:43.513]    </block>
[14:45:43.513]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:45:43.514]      // if-block "connectionFlash && DoOptionByteLoading"
[14:45:43.514]        // =>  FALSE
[14:45:43.515]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:45:43.515]    </control>
[14:45:43.515]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:45:43.515]      // if-block "DoDebugPortStop"
[14:45:43.516]        // =>  TRUE
[14:45:43.516]      <block atomic="false" info="">
[14:45:43.516]        WriteDP(DP_SELECT, 0x00000000);
[14:45:43.517]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:45:43.517]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:45:43.518]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:45:43.518]      </block>
[14:45:43.518]      // end if-block "DoDebugPortStop"
[14:45:43.518]    </control>
[14:45:43.519]  </sequence>
[14:45:43.519]  
[14:50:41.445]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:50:41.445]  
[14:50:41.445]  <debugvars>
[14:50:41.446]    // Pre-defined
[14:50:41.446]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:50:41.446]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:50:41.447]    __dp=0x00000000
[14:50:41.447]    __ap=0x00000000
[14:50:41.447]    __traceout=0x00000000      (Trace Disabled)
[14:50:41.447]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:50:41.447]    __FlashAddr=0x00000000
[14:50:41.448]    __FlashLen=0x00000000
[14:50:41.448]    __FlashArg=0x00000000
[14:50:41.448]    __FlashOp=0x00000000
[14:50:41.448]    __Result=0x00000000
[14:50:41.448]    
[14:50:41.448]    // User-defined
[14:50:41.448]    DbgMCU_CR=0x00000007
[14:50:41.448]    DbgMCU_APB1_Fz=0x00000000
[14:50:41.449]    DbgMCU_APB2_Fz=0x00000000
[14:50:41.449]    DoOptionByteLoading=0x00000000
[14:50:41.449]  </debugvars>
[14:50:41.449]  
[14:50:41.449]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:50:41.450]    <block atomic="false" info="">
[14:50:41.450]      Sequence("CheckID");
[14:50:41.450]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:50:41.450]          <block atomic="false" info="">
[14:50:41.450]            __var pidr1 = 0;
[14:50:41.451]              // -> [pidr1 <= 0x00000000]
[14:50:41.451]            __var pidr2 = 0;
[14:50:41.451]              // -> [pidr2 <= 0x00000000]
[14:50:41.451]            __var jep106id = 0;
[14:50:41.451]              // -> [jep106id <= 0x00000000]
[14:50:41.452]            __var ROMTableBase = 0;
[14:50:41.452]              // -> [ROMTableBase <= 0x00000000]
[14:50:41.452]            __ap = 0;      // AHB-AP
[14:50:41.452]              // -> [__ap <= 0x00000000]
[14:50:41.453]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:50:41.453]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:50:41.453]              // -> [ROMTableBase <= 0xF0000000]
[14:50:41.454]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:50:41.455]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:50:41.456]              // -> [pidr1 <= 0x00000004]
[14:50:41.456]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:50:41.457]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:50:41.457]              // -> [pidr2 <= 0x0000000A]
[14:50:41.458]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:50:41.458]              // -> [jep106id <= 0x00000020]
[14:50:41.458]          </block>
[14:50:41.458]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:50:41.458]            // if-block "jep106id != 0x20"
[14:50:41.458]              // =>  FALSE
[14:50:41.459]            // skip if-block "jep106id != 0x20"
[14:50:41.459]          </control>
[14:50:41.459]        </sequence>
[14:50:41.459]    </block>
[14:50:41.459]  </sequence>
[14:50:41.460]  
[14:50:41.471]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:50:41.471]  
[14:50:41.472]  <debugvars>
[14:50:41.472]    // Pre-defined
[14:50:41.472]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:50:41.472]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:50:41.473]    __dp=0x00000000
[14:50:41.473]    __ap=0x00000000
[14:50:41.473]    __traceout=0x00000000      (Trace Disabled)
[14:50:41.473]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:50:41.473]    __FlashAddr=0x00000000
[14:50:41.474]    __FlashLen=0x00000000
[14:50:41.474]    __FlashArg=0x00000000
[14:50:41.474]    __FlashOp=0x00000000
[14:50:41.474]    __Result=0x00000000
[14:50:41.475]    
[14:50:41.475]    // User-defined
[14:50:41.475]    DbgMCU_CR=0x00000007
[14:50:41.475]    DbgMCU_APB1_Fz=0x00000000
[14:50:41.475]    DbgMCU_APB2_Fz=0x00000000
[14:50:41.475]    DoOptionByteLoading=0x00000000
[14:50:41.475]  </debugvars>
[14:50:41.475]  
[14:50:41.475]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:50:41.476]    <block atomic="false" info="">
[14:50:41.476]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:50:41.477]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:50:41.478]    </block>
[14:50:41.478]    <block atomic="false" info="DbgMCU registers">
[14:50:41.478]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:50:41.479]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[14:50:41.480]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[14:50:41.480]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:50:41.481]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:50:41.481]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:50:41.482]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:50:41.482]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:50:41.483]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:50:41.483]    </block>
[14:50:41.483]  </sequence>
[14:50:41.483]  
[14:50:49.372]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:50:49.372]  
[14:50:49.373]  <debugvars>
[14:50:49.373]    // Pre-defined
[14:50:49.373]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:50:49.374]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:50:49.374]    __dp=0x00000000
[14:50:49.374]    __ap=0x00000000
[14:50:49.374]    __traceout=0x00000000      (Trace Disabled)
[14:50:49.375]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:50:49.375]    __FlashAddr=0x00000000
[14:50:49.375]    __FlashLen=0x00000000
[14:50:49.375]    __FlashArg=0x00000000
[14:50:49.376]    __FlashOp=0x00000000
[14:50:49.376]    __Result=0x00000000
[14:50:49.377]    
[14:50:49.377]    // User-defined
[14:50:49.377]    DbgMCU_CR=0x00000007
[14:50:49.377]    DbgMCU_APB1_Fz=0x00000000
[14:50:49.377]    DbgMCU_APB2_Fz=0x00000000
[14:50:49.377]    DoOptionByteLoading=0x00000000
[14:50:49.378]  </debugvars>
[14:50:49.378]  
[14:50:49.378]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:50:49.378]    <block atomic="false" info="">
[14:50:49.378]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:50:49.378]        // -> [connectionFlash <= 0x00000001]
[14:50:49.379]      __var FLASH_BASE = 0x40022000 ;
[14:50:49.379]        // -> [FLASH_BASE <= 0x40022000]
[14:50:49.379]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:50:49.379]        // -> [FLASH_CR <= 0x40022004]
[14:50:49.379]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:50:49.379]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:50:49.380]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:50:49.380]        // -> [LOCK_BIT <= 0x00000001]
[14:50:49.380]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:50:49.380]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:50:49.380]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:50:49.380]        // -> [FLASH_KEYR <= 0x4002200C]
[14:50:49.381]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:50:49.381]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:50:49.381]      __var FLASH_KEY2 = 0x02030405 ;
[14:50:49.382]        // -> [FLASH_KEY2 <= 0x02030405]
[14:50:49.382]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:50:49.382]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:50:49.383]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:50:49.383]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:50:49.383]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:50:49.383]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:50:49.384]      __var FLASH_CR_Value = 0 ;
[14:50:49.384]        // -> [FLASH_CR_Value <= 0x00000000]
[14:50:49.384]      __var DoDebugPortStop = 1 ;
[14:50:49.384]        // -> [DoDebugPortStop <= 0x00000001]
[14:50:49.384]      __var DP_CTRL_STAT = 0x4 ;
[14:50:49.385]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:50:49.385]      __var DP_SELECT = 0x8 ;
[14:50:49.385]        // -> [DP_SELECT <= 0x00000008]
[14:50:49.385]    </block>
[14:50:49.385]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:50:49.385]      // if-block "connectionFlash && DoOptionByteLoading"
[14:50:49.386]        // =>  FALSE
[14:50:49.386]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:50:49.386]    </control>
[14:50:49.386]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:50:49.387]      // if-block "DoDebugPortStop"
[14:50:49.387]        // =>  TRUE
[14:50:49.387]      <block atomic="false" info="">
[14:50:49.387]        WriteDP(DP_SELECT, 0x00000000);
[14:50:49.387]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:50:49.388]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:50:49.388]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:50:49.388]      </block>
[14:50:49.389]      // end if-block "DoDebugPortStop"
[14:50:49.389]    </control>
[14:50:49.389]  </sequence>
[14:50:49.389]  
[15:05:41.856]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:05:41.856]  
[15:05:41.857]  <debugvars>
[15:05:41.857]    // Pre-defined
[15:05:41.857]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:05:41.858]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:05:41.858]    __dp=0x00000000
[15:05:41.858]    __ap=0x00000000
[15:05:41.859]    __traceout=0x00000000      (Trace Disabled)
[15:05:41.859]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:05:41.860]    __FlashAddr=0x00000000
[15:05:41.860]    __FlashLen=0x00000000
[15:05:41.861]    __FlashArg=0x00000000
[15:05:41.861]    __FlashOp=0x00000000
[15:05:41.861]    __Result=0x00000000
[15:05:41.862]    
[15:05:41.862]    // User-defined
[15:05:41.862]    DbgMCU_CR=0x00000007
[15:05:41.862]    DbgMCU_APB1_Fz=0x00000000
[15:05:41.862]    DbgMCU_APB2_Fz=0x00000000
[15:05:41.863]    DoOptionByteLoading=0x00000000
[15:05:41.863]  </debugvars>
[15:05:41.863]  
[15:05:41.863]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:05:41.864]    <block atomic="false" info="">
[15:05:41.864]      Sequence("CheckID");
[15:05:41.864]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:05:41.864]          <block atomic="false" info="">
[15:05:41.864]            __var pidr1 = 0;
[15:05:41.865]              // -> [pidr1 <= 0x00000000]
[15:05:41.865]            __var pidr2 = 0;
[15:05:41.865]              // -> [pidr2 <= 0x00000000]
[15:05:41.865]            __var jep106id = 0;
[15:05:41.865]              // -> [jep106id <= 0x00000000]
[15:05:41.865]            __var ROMTableBase = 0;
[15:05:41.865]              // -> [ROMTableBase <= 0x00000000]
[15:05:41.865]            __ap = 0;      // AHB-AP
[15:05:41.865]              // -> [__ap <= 0x00000000]
[15:05:41.866]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:05:41.867]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:05:41.868]              // -> [ROMTableBase <= 0xF0000000]
[15:05:41.868]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:05:41.869]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:05:41.869]              // -> [pidr1 <= 0x00000004]
[15:05:41.869]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:05:41.870]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:05:41.870]              // -> [pidr2 <= 0x0000000A]
[15:05:41.870]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:05:41.871]              // -> [jep106id <= 0x00000020]
[15:05:41.871]          </block>
[15:05:41.871]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:05:41.872]            // if-block "jep106id != 0x20"
[15:05:41.873]              // =>  FALSE
[15:05:41.873]            // skip if-block "jep106id != 0x20"
[15:05:41.873]          </control>
[15:05:41.874]        </sequence>
[15:05:41.874]    </block>
[15:05:41.874]  </sequence>
[15:05:41.874]  
[15:05:41.886]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:05:41.886]  
[15:05:41.905]  <debugvars>
[15:05:41.906]    // Pre-defined
[15:05:41.907]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:05:41.907]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:05:41.908]    __dp=0x00000000
[15:05:41.909]    __ap=0x00000000
[15:05:41.909]    __traceout=0x00000000      (Trace Disabled)
[15:05:41.910]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:05:41.911]    __FlashAddr=0x00000000
[15:05:41.911]    __FlashLen=0x00000000
[15:05:41.912]    __FlashArg=0x00000000
[15:05:41.913]    __FlashOp=0x00000000
[15:05:41.914]    __Result=0x00000000
[15:05:41.914]    
[15:05:41.914]    // User-defined
[15:05:41.914]    DbgMCU_CR=0x00000007
[15:05:41.915]    DbgMCU_APB1_Fz=0x00000000
[15:05:41.916]    DbgMCU_APB2_Fz=0x00000000
[15:05:41.917]    DoOptionByteLoading=0x00000000
[15:05:41.917]  </debugvars>
[15:05:41.918]  
[15:05:41.918]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:05:41.919]    <block atomic="false" info="">
[15:05:41.919]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:05:41.921]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:05:41.922]    </block>
[15:05:41.922]    <block atomic="false" info="DbgMCU registers">
[15:05:41.923]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:05:41.924]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[15:05:41.925]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[15:05:41.926]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:05:41.927]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:05:41.927]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:05:41.928]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:05:41.929]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:05:41.930]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:05:41.930]    </block>
[15:05:41.930]  </sequence>
[15:05:41.931]  
[15:05:49.803]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:05:49.803]  
[15:05:49.804]  <debugvars>
[15:05:49.804]    // Pre-defined
[15:05:49.805]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:05:49.805]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:05:49.805]    __dp=0x00000000
[15:05:49.805]    __ap=0x00000000
[15:05:49.806]    __traceout=0x00000000      (Trace Disabled)
[15:05:49.806]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:05:49.806]    __FlashAddr=0x00000000
[15:05:49.807]    __FlashLen=0x00000000
[15:05:49.807]    __FlashArg=0x00000000
[15:05:49.807]    __FlashOp=0x00000000
[15:05:49.807]    __Result=0x00000000
[15:05:49.808]    
[15:05:49.808]    // User-defined
[15:05:49.808]    DbgMCU_CR=0x00000007
[15:05:49.808]    DbgMCU_APB1_Fz=0x00000000
[15:05:49.809]    DbgMCU_APB2_Fz=0x00000000
[15:05:49.809]    DoOptionByteLoading=0x00000000
[15:05:49.809]  </debugvars>
[15:05:49.809]  
[15:05:49.809]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:05:49.810]    <block atomic="false" info="">
[15:05:49.810]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:05:49.810]        // -> [connectionFlash <= 0x00000001]
[15:05:49.810]      __var FLASH_BASE = 0x40022000 ;
[15:05:49.810]        // -> [FLASH_BASE <= 0x40022000]
[15:05:49.811]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:05:49.811]        // -> [FLASH_CR <= 0x40022004]
[15:05:49.811]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:05:49.811]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:05:49.811]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:05:49.811]        // -> [LOCK_BIT <= 0x00000001]
[15:05:49.812]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:05:49.812]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:05:49.812]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:05:49.812]        // -> [FLASH_KEYR <= 0x4002200C]
[15:05:49.812]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:05:49.812]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:05:49.813]      __var FLASH_KEY2 = 0x02030405 ;
[15:05:49.813]        // -> [FLASH_KEY2 <= 0x02030405]
[15:05:49.813]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:05:49.813]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:05:49.813]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:05:49.813]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:05:49.814]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:05:49.814]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:05:49.814]      __var FLASH_CR_Value = 0 ;
[15:05:49.814]        // -> [FLASH_CR_Value <= 0x00000000]
[15:05:49.814]      __var DoDebugPortStop = 1 ;
[15:05:49.815]        // -> [DoDebugPortStop <= 0x00000001]
[15:05:49.815]      __var DP_CTRL_STAT = 0x4 ;
[15:05:49.815]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:05:49.815]      __var DP_SELECT = 0x8 ;
[15:05:49.815]        // -> [DP_SELECT <= 0x00000008]
[15:05:49.816]    </block>
[15:05:49.816]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:05:49.816]      // if-block "connectionFlash && DoOptionByteLoading"
[15:05:49.816]        // =>  FALSE
[15:05:49.817]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:05:49.817]    </control>
[15:05:49.817]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:05:49.817]      // if-block "DoDebugPortStop"
[15:05:49.817]        // =>  TRUE
[15:05:49.817]      <block atomic="false" info="">
[15:05:49.817]        WriteDP(DP_SELECT, 0x00000000);
[15:05:49.818]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:05:49.818]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:05:49.819]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:05:49.820]      </block>
[15:05:49.820]      // end if-block "DoDebugPortStop"
[15:05:49.820]    </control>
[15:05:49.820]  </sequence>
[15:05:49.820]  
[15:07:45.908]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:07:45.908]  
[15:07:45.909]  <debugvars>
[15:07:45.909]    // Pre-defined
[15:07:45.909]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:07:45.910]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:07:45.910]    __dp=0x00000000
[15:07:45.910]    __ap=0x00000000
[15:07:45.911]    __traceout=0x00000000      (Trace Disabled)
[15:07:45.911]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:07:45.911]    __FlashAddr=0x00000000
[15:07:45.911]    __FlashLen=0x00000000
[15:07:45.912]    __FlashArg=0x00000000
[15:07:45.912]    __FlashOp=0x00000000
[15:07:45.912]    __Result=0x00000000
[15:07:45.912]    
[15:07:45.912]    // User-defined
[15:07:45.912]    DbgMCU_CR=0x00000007
[15:07:45.913]    DbgMCU_APB1_Fz=0x00000000
[15:07:45.913]    DbgMCU_APB2_Fz=0x00000000
[15:07:45.913]    DoOptionByteLoading=0x00000000
[15:07:45.913]  </debugvars>
[15:07:45.913]  
[15:07:45.913]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:07:45.914]    <block atomic="false" info="">
[15:07:45.914]      Sequence("CheckID");
[15:07:45.914]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:07:45.914]          <block atomic="false" info="">
[15:07:45.914]            __var pidr1 = 0;
[15:07:45.915]              // -> [pidr1 <= 0x00000000]
[15:07:45.915]            __var pidr2 = 0;
[15:07:45.915]              // -> [pidr2 <= 0x00000000]
[15:07:45.915]            __var jep106id = 0;
[15:07:45.915]              // -> [jep106id <= 0x00000000]
[15:07:45.915]            __var ROMTableBase = 0;
[15:07:45.916]              // -> [ROMTableBase <= 0x00000000]
[15:07:45.916]            __ap = 0;      // AHB-AP
[15:07:45.916]              // -> [__ap <= 0x00000000]
[15:07:45.916]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:07:45.916]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:07:45.918]              // -> [ROMTableBase <= 0xF0000000]
[15:07:45.918]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:07:45.919]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:07:45.920]              // -> [pidr1 <= 0x00000004]
[15:07:45.920]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:07:45.921]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:07:45.921]              // -> [pidr2 <= 0x0000000A]
[15:07:45.921]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:07:45.922]              // -> [jep106id <= 0x00000020]
[15:07:45.922]          </block>
[15:07:45.922]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:07:45.922]            // if-block "jep106id != 0x20"
[15:07:45.922]              // =>  FALSE
[15:07:45.922]            // skip if-block "jep106id != 0x20"
[15:07:45.922]          </control>
[15:07:45.922]        </sequence>
[15:07:45.923]    </block>
[15:07:45.923]  </sequence>
[15:07:45.924]  
[15:07:45.936]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:07:45.936]  
[15:07:45.955]  <debugvars>
[15:07:45.956]    // Pre-defined
[15:07:45.957]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:07:45.957]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:07:45.958]    __dp=0x00000000
[15:07:45.959]    __ap=0x00000000
[15:07:45.959]    __traceout=0x00000000      (Trace Disabled)
[15:07:45.960]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:07:45.961]    __FlashAddr=0x00000000
[15:07:45.961]    __FlashLen=0x00000000
[15:07:45.962]    __FlashArg=0x00000000
[15:07:45.963]    __FlashOp=0x00000000
[15:07:45.963]    __Result=0x00000000
[15:07:45.964]    
[15:07:45.964]    // User-defined
[15:07:45.964]    DbgMCU_CR=0x00000007
[15:07:45.965]    DbgMCU_APB1_Fz=0x00000000
[15:07:45.966]    DbgMCU_APB2_Fz=0x00000000
[15:07:45.966]    DoOptionByteLoading=0x00000000
[15:07:45.967]  </debugvars>
[15:07:45.968]  
[15:07:45.968]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:07:45.969]    <block atomic="false" info="">
[15:07:45.969]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:07:45.971]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:07:45.972]    </block>
[15:07:45.973]    <block atomic="false" info="DbgMCU registers">
[15:07:45.974]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:07:45.975]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[15:07:45.977]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[15:07:45.977]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:07:45.979]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:07:45.979]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:07:45.981]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:07:45.981]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:07:45.982]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:07:45.983]    </block>
[15:07:45.983]  </sequence>
[15:07:45.983]  
[15:07:53.859]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:07:53.859]  
[15:07:53.860]  <debugvars>
[15:07:53.860]    // Pre-defined
[15:07:53.860]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:07:53.861]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:07:53.861]    __dp=0x00000000
[15:07:53.861]    __ap=0x00000000
[15:07:53.861]    __traceout=0x00000000      (Trace Disabled)
[15:07:53.861]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:07:53.861]    __FlashAddr=0x00000000
[15:07:53.861]    __FlashLen=0x00000000
[15:07:53.862]    __FlashArg=0x00000000
[15:07:53.862]    __FlashOp=0x00000000
[15:07:53.863]    __Result=0x00000000
[15:07:53.863]    
[15:07:53.863]    // User-defined
[15:07:53.863]    DbgMCU_CR=0x00000007
[15:07:53.863]    DbgMCU_APB1_Fz=0x00000000
[15:07:53.863]    DbgMCU_APB2_Fz=0x00000000
[15:07:53.864]    DoOptionByteLoading=0x00000000
[15:07:53.864]  </debugvars>
[15:07:53.864]  
[15:07:53.864]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:07:53.864]    <block atomic="false" info="">
[15:07:53.864]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:07:53.865]        // -> [connectionFlash <= 0x00000001]
[15:07:53.865]      __var FLASH_BASE = 0x40022000 ;
[15:07:53.866]        // -> [FLASH_BASE <= 0x40022000]
[15:07:53.866]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:07:53.866]        // -> [FLASH_CR <= 0x40022004]
[15:07:53.866]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:07:53.866]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:07:53.866]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:07:53.867]        // -> [LOCK_BIT <= 0x00000001]
[15:07:53.867]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:07:53.867]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:07:53.867]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:07:53.868]        // -> [FLASH_KEYR <= 0x4002200C]
[15:07:53.868]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:07:53.868]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:07:53.868]      __var FLASH_KEY2 = 0x02030405 ;
[15:07:53.868]        // -> [FLASH_KEY2 <= 0x02030405]
[15:07:53.869]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:07:53.869]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:07:53.869]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:07:53.869]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:07:53.870]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:07:53.870]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:07:53.870]      __var FLASH_CR_Value = 0 ;
[15:07:53.870]        // -> [FLASH_CR_Value <= 0x00000000]
[15:07:53.870]      __var DoDebugPortStop = 1 ;
[15:07:53.871]        // -> [DoDebugPortStop <= 0x00000001]
[15:07:53.871]      __var DP_CTRL_STAT = 0x4 ;
[15:07:53.871]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:07:53.871]      __var DP_SELECT = 0x8 ;
[15:07:53.871]        // -> [DP_SELECT <= 0x00000008]
[15:07:53.872]    </block>
[15:07:53.872]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:07:53.872]      // if-block "connectionFlash && DoOptionByteLoading"
[15:07:53.872]        // =>  FALSE
[15:07:53.872]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:07:53.872]    </control>
[15:07:53.873]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:07:53.873]      // if-block "DoDebugPortStop"
[15:07:53.873]        // =>  TRUE
[15:07:53.874]      <block atomic="false" info="">
[15:07:53.874]        WriteDP(DP_SELECT, 0x00000000);
[15:07:53.874]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:07:53.874]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:07:53.875]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:07:53.875]      </block>
[15:07:53.875]      // end if-block "DoDebugPortStop"
[15:07:53.875]    </control>
[15:07:53.876]  </sequence>
[15:07:53.876]  
[15:18:47.232]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:18:47.232]  
[15:18:47.233]  <debugvars>
[15:18:47.233]    // Pre-defined
[15:18:47.233]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:18:47.233]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:18:47.234]    __dp=0x00000000
[15:18:47.234]    __ap=0x00000000
[15:18:47.235]    __traceout=0x00000000      (Trace Disabled)
[15:18:47.235]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:18:47.235]    __FlashAddr=0x00000000
[15:18:47.235]    __FlashLen=0x00000000
[15:18:47.236]    __FlashArg=0x00000000
[15:18:47.236]    __FlashOp=0x00000000
[15:18:47.236]    __Result=0x00000000
[15:18:47.237]    
[15:18:47.237]    // User-defined
[15:18:47.237]    DbgMCU_CR=0x00000007
[15:18:47.237]    DbgMCU_APB1_Fz=0x00000000
[15:18:47.237]    DbgMCU_APB2_Fz=0x00000000
[15:18:47.237]    DoOptionByteLoading=0x00000000
[15:18:47.237]  </debugvars>
[15:18:47.238]  
[15:18:47.238]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:18:47.238]    <block atomic="false" info="">
[15:18:47.238]      Sequence("CheckID");
[15:18:47.238]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:18:47.239]          <block atomic="false" info="">
[15:18:47.239]            __var pidr1 = 0;
[15:18:47.239]              // -> [pidr1 <= 0x00000000]
[15:18:47.239]            __var pidr2 = 0;
[15:18:47.239]              // -> [pidr2 <= 0x00000000]
[15:18:47.239]            __var jep106id = 0;
[15:18:47.239]              // -> [jep106id <= 0x00000000]
[15:18:47.239]            __var ROMTableBase = 0;
[15:18:47.239]              // -> [ROMTableBase <= 0x00000000]
[15:18:47.240]            __ap = 0;      // AHB-AP
[15:18:47.240]              // -> [__ap <= 0x00000000]
[15:18:47.241]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:18:47.242]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:18:47.242]              // -> [ROMTableBase <= 0xF0000000]
[15:18:47.242]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:18:47.243]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:18:47.244]              // -> [pidr1 <= 0x00000004]
[15:18:47.244]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:18:47.245]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:18:47.246]              // -> [pidr2 <= 0x0000000A]
[15:18:47.246]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:18:47.246]              // -> [jep106id <= 0x00000020]
[15:18:47.247]          </block>
[15:18:47.247]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:18:47.247]            // if-block "jep106id != 0x20"
[15:18:47.248]              // =>  FALSE
[15:18:47.248]            // skip if-block "jep106id != 0x20"
[15:18:47.248]          </control>
[15:18:47.249]        </sequence>
[15:18:47.249]    </block>
[15:18:47.249]  </sequence>
[15:18:47.249]  
[15:18:47.261]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:18:47.261]  
[15:18:47.269]  <debugvars>
[15:18:47.270]    // Pre-defined
[15:18:47.270]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:18:47.271]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:18:47.272]    __dp=0x00000000
[15:18:47.272]    __ap=0x00000000
[15:18:47.273]    __traceout=0x00000000      (Trace Disabled)
[15:18:47.274]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:18:47.275]    __FlashAddr=0x00000000
[15:18:47.275]    __FlashLen=0x00000000
[15:18:47.276]    __FlashArg=0x00000000
[15:18:47.277]    __FlashOp=0x00000000
[15:18:47.278]    __Result=0x00000000
[15:18:47.278]    
[15:18:47.278]    // User-defined
[15:18:47.279]    DbgMCU_CR=0x00000007
[15:18:47.280]    DbgMCU_APB1_Fz=0x00000000
[15:18:47.280]    DbgMCU_APB2_Fz=0x00000000
[15:18:47.281]    DoOptionByteLoading=0x00000000
[15:18:47.282]  </debugvars>
[15:18:47.282]  
[15:18:47.283]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:18:47.283]    <block atomic="false" info="">
[15:18:47.284]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:18:47.287]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:18:47.287]    </block>
[15:18:47.288]    <block atomic="false" info="DbgMCU registers">
[15:18:47.288]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:18:47.290]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[15:18:47.292]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[15:18:47.292]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:18:47.294]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:18:47.294]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:18:47.296]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:18:47.296]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:18:47.297]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:18:47.298]    </block>
[15:18:47.298]  </sequence>
[15:18:47.299]  
[15:18:55.247]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:18:55.247]  
[15:18:55.247]  <debugvars>
[15:18:55.248]    // Pre-defined
[15:18:55.248]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:18:55.248]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:18:55.248]    __dp=0x00000000
[15:18:55.249]    __ap=0x00000000
[15:18:55.249]    __traceout=0x00000000      (Trace Disabled)
[15:18:55.249]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:18:55.249]    __FlashAddr=0x00000000
[15:18:55.249]    __FlashLen=0x00000000
[15:18:55.249]    __FlashArg=0x00000000
[15:18:55.250]    __FlashOp=0x00000000
[15:18:55.250]    __Result=0x00000000
[15:18:55.250]    
[15:18:55.250]    // User-defined
[15:18:55.250]    DbgMCU_CR=0x00000007
[15:18:55.250]    DbgMCU_APB1_Fz=0x00000000
[15:18:55.250]    DbgMCU_APB2_Fz=0x00000000
[15:18:55.251]    DoOptionByteLoading=0x00000000
[15:18:55.251]  </debugvars>
[15:18:55.251]  
[15:18:55.251]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:18:55.251]    <block atomic="false" info="">
[15:18:55.252]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:18:55.252]        // -> [connectionFlash <= 0x00000001]
[15:18:55.252]      __var FLASH_BASE = 0x40022000 ;
[15:18:55.253]        // -> [FLASH_BASE <= 0x40022000]
[15:18:55.253]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:18:55.253]        // -> [FLASH_CR <= 0x40022004]
[15:18:55.253]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:18:55.254]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:18:55.254]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:18:55.254]        // -> [LOCK_BIT <= 0x00000001]
[15:18:55.254]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:18:55.254]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:18:55.255]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:18:55.255]        // -> [FLASH_KEYR <= 0x4002200C]
[15:18:55.255]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:18:55.255]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:18:55.255]      __var FLASH_KEY2 = 0x02030405 ;
[15:18:55.255]        // -> [FLASH_KEY2 <= 0x02030405]
[15:18:55.256]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:18:55.256]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:18:55.256]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:18:55.256]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:18:55.256]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:18:55.256]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:18:55.257]      __var FLASH_CR_Value = 0 ;
[15:18:55.257]        // -> [FLASH_CR_Value <= 0x00000000]
[15:18:55.257]      __var DoDebugPortStop = 1 ;
[15:18:55.257]        // -> [DoDebugPortStop <= 0x00000001]
[15:18:55.257]      __var DP_CTRL_STAT = 0x4 ;
[15:18:55.258]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:18:55.258]      __var DP_SELECT = 0x8 ;
[15:18:55.258]        // -> [DP_SELECT <= 0x00000008]
[15:18:55.258]    </block>
[15:18:55.258]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:18:55.259]      // if-block "connectionFlash && DoOptionByteLoading"
[15:18:55.259]        // =>  FALSE
[15:18:55.259]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:18:55.259]    </control>
[15:18:55.260]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:18:55.260]      // if-block "DoDebugPortStop"
[15:18:55.260]        // =>  TRUE
[15:18:55.261]      <block atomic="false" info="">
[15:18:55.261]        WriteDP(DP_SELECT, 0x00000000);
[15:18:55.261]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:18:55.261]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:18:55.262]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:18:55.262]      </block>
[15:18:55.262]      // end if-block "DoDebugPortStop"
[15:18:55.262]    </control>
[15:18:55.263]  </sequence>
[15:18:55.263]  
[15:23:39.602]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:23:39.602]  
[15:23:39.602]  <debugvars>
[15:23:39.602]    // Pre-defined
[15:23:39.602]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:23:39.603]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:23:39.603]    __dp=0x00000000
[15:23:39.603]    __ap=0x00000000
[15:23:39.603]    __traceout=0x00000000      (Trace Disabled)
[15:23:39.603]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:23:39.604]    __FlashAddr=0x00000000
[15:23:39.604]    __FlashLen=0x00000000
[15:23:39.604]    __FlashArg=0x00000000
[15:23:39.604]    __FlashOp=0x00000000
[15:23:39.604]    __Result=0x00000000
[15:23:39.605]    
[15:23:39.605]    // User-defined
[15:23:39.605]    DbgMCU_CR=0x00000007
[15:23:39.605]    DbgMCU_APB1_Fz=0x00000000
[15:23:39.605]    DbgMCU_APB2_Fz=0x00000000
[15:23:39.606]    DoOptionByteLoading=0x00000000
[15:23:39.606]  </debugvars>
[15:23:39.606]  
[15:23:39.606]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:23:39.606]    <block atomic="false" info="">
[15:23:39.607]      Sequence("CheckID");
[15:23:39.607]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:23:39.607]          <block atomic="false" info="">
[15:23:39.607]            __var pidr1 = 0;
[15:23:39.607]              // -> [pidr1 <= 0x00000000]
[15:23:39.608]            __var pidr2 = 0;
[15:23:39.608]              // -> [pidr2 <= 0x00000000]
[15:23:39.608]            __var jep106id = 0;
[15:23:39.608]              // -> [jep106id <= 0x00000000]
[15:23:39.609]            __var ROMTableBase = 0;
[15:23:39.609]              // -> [ROMTableBase <= 0x00000000]
[15:23:39.610]            __ap = 0;      // AHB-AP
[15:23:39.610]              // -> [__ap <= 0x00000000]
[15:23:39.610]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:23:39.610]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:23:39.611]              // -> [ROMTableBase <= 0xF0000000]
[15:23:39.611]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:23:39.612]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:23:39.613]              // -> [pidr1 <= 0x00000004]
[15:23:39.613]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:23:39.614]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:23:39.614]              // -> [pidr2 <= 0x0000000A]
[15:23:39.615]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:23:39.615]              // -> [jep106id <= 0x00000020]
[15:23:39.615]          </block>
[15:23:39.615]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:23:39.615]            // if-block "jep106id != 0x20"
[15:23:39.616]              // =>  FALSE
[15:23:39.616]            // skip if-block "jep106id != 0x20"
[15:23:39.616]          </control>
[15:23:39.616]        </sequence>
[15:23:39.616]    </block>
[15:23:39.617]  </sequence>
[15:23:39.617]  
[15:23:39.629]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:23:39.629]  
[15:23:39.629]  <debugvars>
[15:23:39.629]    // Pre-defined
[15:23:39.629]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:23:39.630]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:23:39.630]    __dp=0x00000000
[15:23:39.630]    __ap=0x00000000
[15:23:39.631]    __traceout=0x00000000      (Trace Disabled)
[15:23:39.631]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:23:39.631]    __FlashAddr=0x00000000
[15:23:39.632]    __FlashLen=0x00000000
[15:23:39.632]    __FlashArg=0x00000000
[15:23:39.632]    __FlashOp=0x00000000
[15:23:39.632]    __Result=0x00000000
[15:23:39.632]    
[15:23:39.632]    // User-defined
[15:23:39.632]    DbgMCU_CR=0x00000007
[15:23:39.632]    DbgMCU_APB1_Fz=0x00000000
[15:23:39.632]    DbgMCU_APB2_Fz=0x00000000
[15:23:39.633]    DoOptionByteLoading=0x00000000
[15:23:39.633]  </debugvars>
[15:23:39.633]  
[15:23:39.634]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:23:39.634]    <block atomic="false" info="">
[15:23:39.634]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:23:39.635]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:23:39.636]    </block>
[15:23:39.636]    <block atomic="false" info="DbgMCU registers">
[15:23:39.636]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:23:39.636]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[15:23:39.638]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[15:23:39.638]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:23:39.639]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:23:39.639]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:23:39.640]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:23:39.640]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:23:39.641]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:23:39.641]    </block>
[15:23:39.642]  </sequence>
[15:23:39.642]  
[15:23:47.596]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:23:47.596]  
[15:23:47.596]  <debugvars>
[15:23:47.596]    // Pre-defined
[15:23:47.596]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:23:47.596]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:23:47.596]    __dp=0x00000000
[15:23:47.597]    __ap=0x00000000
[15:23:47.597]    __traceout=0x00000000      (Trace Disabled)
[15:23:47.597]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:23:47.598]    __FlashAddr=0x00000000
[15:23:47.598]    __FlashLen=0x00000000
[15:23:47.598]    __FlashArg=0x00000000
[15:23:47.599]    __FlashOp=0x00000000
[15:23:47.599]    __Result=0x00000000
[15:23:47.599]    
[15:23:47.599]    // User-defined
[15:23:47.599]    DbgMCU_CR=0x00000007
[15:23:47.599]    DbgMCU_APB1_Fz=0x00000000
[15:23:47.600]    DbgMCU_APB2_Fz=0x00000000
[15:23:47.600]    DoOptionByteLoading=0x00000000
[15:23:47.600]  </debugvars>
[15:23:47.600]  
[15:23:47.600]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:23:47.600]    <block atomic="false" info="">
[15:23:47.601]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:23:47.601]        // -> [connectionFlash <= 0x00000001]
[15:23:47.601]      __var FLASH_BASE = 0x40022000 ;
[15:23:47.602]        // -> [FLASH_BASE <= 0x40022000]
[15:23:47.602]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:23:47.602]        // -> [FLASH_CR <= 0x40022004]
[15:23:47.602]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:23:47.603]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:23:47.603]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:23:47.603]        // -> [LOCK_BIT <= 0x00000001]
[15:23:47.603]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:23:47.603]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:23:47.604]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:23:47.604]        // -> [FLASH_KEYR <= 0x4002200C]
[15:23:47.604]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:23:47.604]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:23:47.604]      __var FLASH_KEY2 = 0x02030405 ;
[15:23:47.604]        // -> [FLASH_KEY2 <= 0x02030405]
[15:23:47.604]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:23:47.605]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:23:47.605]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:23:47.605]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:23:47.605]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:23:47.606]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:23:47.606]      __var FLASH_CR_Value = 0 ;
[15:23:47.606]        // -> [FLASH_CR_Value <= 0x00000000]
[15:23:47.606]      __var DoDebugPortStop = 1 ;
[15:23:47.606]        // -> [DoDebugPortStop <= 0x00000001]
[15:23:47.606]      __var DP_CTRL_STAT = 0x4 ;
[15:23:47.606]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:23:47.606]      __var DP_SELECT = 0x8 ;
[15:23:47.607]        // -> [DP_SELECT <= 0x00000008]
[15:23:47.607]    </block>
[15:23:47.607]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:23:47.607]      // if-block "connectionFlash && DoOptionByteLoading"
[15:23:47.608]        // =>  FALSE
[15:23:47.608]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:23:47.609]    </control>
[15:23:47.609]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:23:47.609]      // if-block "DoDebugPortStop"
[15:23:47.609]        // =>  TRUE
[15:23:47.609]      <block atomic="false" info="">
[15:23:47.609]        WriteDP(DP_SELECT, 0x00000000);
[15:23:47.610]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:23:47.610]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:23:47.610]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:23:47.610]      </block>
[15:23:47.610]      // end if-block "DoDebugPortStop"
[15:23:47.611]    </control>
[15:23:47.611]  </sequence>
[15:23:47.611]  
[15:26:34.306]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:26:34.306]  
[15:26:34.306]  <debugvars>
[15:26:34.306]    // Pre-defined
[15:26:34.306]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:26:34.306]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:26:34.307]    __dp=0x00000000
[15:26:34.307]    __ap=0x00000000
[15:26:34.307]    __traceout=0x00000000      (Trace Disabled)
[15:26:34.307]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:26:34.307]    __FlashAddr=0x00000000
[15:26:34.308]    __FlashLen=0x00000000
[15:26:34.308]    __FlashArg=0x00000000
[15:26:34.308]    __FlashOp=0x00000000
[15:26:34.308]    __Result=0x00000000
[15:26:34.309]    
[15:26:34.309]    // User-defined
[15:26:34.309]    DbgMCU_CR=0x00000007
[15:26:34.309]    DbgMCU_APB1_Fz=0x00000000
[15:26:34.309]    DbgMCU_APB2_Fz=0x00000000
[15:26:34.309]    DoOptionByteLoading=0x00000000
[15:26:34.309]  </debugvars>
[15:26:34.309]  
[15:26:34.311]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:26:34.311]    <block atomic="false" info="">
[15:26:34.311]      Sequence("CheckID");
[15:26:34.311]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:26:34.311]          <block atomic="false" info="">
[15:26:34.311]            __var pidr1 = 0;
[15:26:34.312]              // -> [pidr1 <= 0x00000000]
[15:26:34.312]            __var pidr2 = 0;
[15:26:34.312]              // -> [pidr2 <= 0x00000000]
[15:26:34.312]            __var jep106id = 0;
[15:26:34.312]              // -> [jep106id <= 0x00000000]
[15:26:34.313]            __var ROMTableBase = 0;
[15:26:34.313]              // -> [ROMTableBase <= 0x00000000]
[15:26:34.313]            __ap = 0;      // AHB-AP
[15:26:34.313]              // -> [__ap <= 0x00000000]
[15:26:34.313]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:26:34.314]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:26:34.314]              // -> [ROMTableBase <= 0xF0000000]
[15:26:34.314]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:26:34.315]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:26:34.316]              // -> [pidr1 <= 0x00000004]
[15:26:34.316]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:26:34.318]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:26:34.318]              // -> [pidr2 <= 0x0000000A]
[15:26:34.318]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:26:34.318]              // -> [jep106id <= 0x00000020]
[15:26:34.319]          </block>
[15:26:34.319]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:26:34.319]            // if-block "jep106id != 0x20"
[15:26:34.319]              // =>  FALSE
[15:26:34.319]            // skip if-block "jep106id != 0x20"
[15:26:34.320]          </control>
[15:26:34.320]        </sequence>
[15:26:34.320]    </block>
[15:26:34.320]  </sequence>
[15:26:34.320]  
[15:26:34.332]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:26:34.332]  
[15:26:34.332]  <debugvars>
[15:26:34.332]    // Pre-defined
[15:26:34.332]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:26:34.333]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:26:34.333]    __dp=0x00000000
[15:26:34.333]    __ap=0x00000000
[15:26:34.334]    __traceout=0x00000000      (Trace Disabled)
[15:26:34.334]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:26:34.334]    __FlashAddr=0x00000000
[15:26:34.334]    __FlashLen=0x00000000
[15:26:34.334]    __FlashArg=0x00000000
[15:26:34.334]    __FlashOp=0x00000000
[15:26:34.336]    __Result=0x00000000
[15:26:34.336]    
[15:26:34.336]    // User-defined
[15:26:34.336]    DbgMCU_CR=0x00000007
[15:26:34.336]    DbgMCU_APB1_Fz=0x00000000
[15:26:34.336]    DbgMCU_APB2_Fz=0x00000000
[15:26:34.336]    DoOptionByteLoading=0x00000000
[15:26:34.336]  </debugvars>
[15:26:34.336]  
[15:26:34.337]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:26:34.337]    <block atomic="false" info="">
[15:26:34.337]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:26:34.338]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:26:34.339]    </block>
[15:26:34.339]    <block atomic="false" info="DbgMCU registers">
[15:26:34.340]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:26:34.340]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[15:26:34.341]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[15:26:34.341]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:26:34.342]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:26:34.342]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:26:34.343]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:26:34.343]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:26:34.344]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:26:34.344]    </block>
[15:26:34.344]  </sequence>
[15:26:34.345]  
[15:26:42.415]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:26:42.415]  
[15:26:42.415]  <debugvars>
[15:26:42.415]    // Pre-defined
[15:26:42.416]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:26:42.416]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:26:42.416]    __dp=0x00000000
[15:26:42.416]    __ap=0x00000000
[15:26:42.417]    __traceout=0x00000000      (Trace Disabled)
[15:26:42.417]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:26:42.417]    __FlashAddr=0x00000000
[15:26:42.417]    __FlashLen=0x00000000
[15:26:42.418]    __FlashArg=0x00000000
[15:26:42.418]    __FlashOp=0x00000000
[15:26:42.418]    __Result=0x00000000
[15:26:42.418]    
[15:26:42.418]    // User-defined
[15:26:42.418]    DbgMCU_CR=0x00000007
[15:26:42.418]    DbgMCU_APB1_Fz=0x00000000
[15:26:42.418]    DbgMCU_APB2_Fz=0x00000000
[15:26:42.418]    DoOptionByteLoading=0x00000000
[15:26:42.419]  </debugvars>
[15:26:42.419]  
[15:26:42.419]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:26:42.419]    <block atomic="false" info="">
[15:26:42.420]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:26:42.420]        // -> [connectionFlash <= 0x00000001]
[15:26:42.420]      __var FLASH_BASE = 0x40022000 ;
[15:26:42.421]        // -> [FLASH_BASE <= 0x40022000]
[15:26:42.421]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:26:42.421]        // -> [FLASH_CR <= 0x40022004]
[15:26:42.421]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:26:42.421]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:26:42.421]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:26:42.421]        // -> [LOCK_BIT <= 0x00000001]
[15:26:42.422]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:26:42.422]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:26:42.422]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:26:42.423]        // -> [FLASH_KEYR <= 0x4002200C]
[15:26:42.423]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:26:42.423]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:26:42.423]      __var FLASH_KEY2 = 0x02030405 ;
[15:26:42.423]        // -> [FLASH_KEY2 <= 0x02030405]
[15:26:42.424]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:26:42.424]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:26:42.424]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:26:42.425]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:26:42.425]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:26:42.425]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:26:42.425]      __var FLASH_CR_Value = 0 ;
[15:26:42.425]        // -> [FLASH_CR_Value <= 0x00000000]
[15:26:42.426]      __var DoDebugPortStop = 1 ;
[15:26:42.426]        // -> [DoDebugPortStop <= 0x00000001]
[15:26:42.426]      __var DP_CTRL_STAT = 0x4 ;
[15:26:42.426]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:26:42.426]      __var DP_SELECT = 0x8 ;
[15:26:42.427]        // -> [DP_SELECT <= 0x00000008]
[15:26:42.427]    </block>
[15:26:42.427]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:26:42.427]      // if-block "connectionFlash && DoOptionByteLoading"
[15:26:42.427]        // =>  FALSE
[15:26:42.428]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:26:42.428]    </control>
[15:26:42.428]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:26:42.428]      // if-block "DoDebugPortStop"
[15:26:42.428]        // =>  TRUE
[15:26:42.429]      <block atomic="false" info="">
[15:26:42.429]        WriteDP(DP_SELECT, 0x00000000);
[15:26:42.429]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:26:42.430]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:26:42.430]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:26:42.430]      </block>
[15:26:42.430]      // end if-block "DoDebugPortStop"
[15:26:42.431]    </control>
[15:26:42.431]  </sequence>
[15:26:42.431]  
[15:29:26.780]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:29:26.780]  
[15:29:26.780]  <debugvars>
[15:29:26.780]    // Pre-defined
[15:29:26.781]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:29:26.781]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:29:26.781]    __dp=0x00000000
[15:29:26.781]    __ap=0x00000000
[15:29:26.782]    __traceout=0x00000000      (Trace Disabled)
[15:29:26.782]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:29:26.782]    __FlashAddr=0x00000000
[15:29:26.782]    __FlashLen=0x00000000
[15:29:26.782]    __FlashArg=0x00000000
[15:29:26.782]    __FlashOp=0x00000000
[15:29:26.783]    __Result=0x00000000
[15:29:26.783]    
[15:29:26.783]    // User-defined
[15:29:26.783]    DbgMCU_CR=0x00000007
[15:29:26.783]    DbgMCU_APB1_Fz=0x00000000
[15:29:26.784]    DbgMCU_APB2_Fz=0x00000000
[15:29:26.784]    DoOptionByteLoading=0x00000000
[15:29:26.784]  </debugvars>
[15:29:26.785]  
[15:29:26.786]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:29:26.786]    <block atomic="false" info="">
[15:29:26.786]      Sequence("CheckID");
[15:29:26.787]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:29:26.787]          <block atomic="false" info="">
[15:29:26.787]            __var pidr1 = 0;
[15:29:26.787]              // -> [pidr1 <= 0x00000000]
[15:29:26.788]            __var pidr2 = 0;
[15:29:26.788]              // -> [pidr2 <= 0x00000000]
[15:29:26.788]            __var jep106id = 0;
[15:29:26.788]              // -> [jep106id <= 0x00000000]
[15:29:26.788]            __var ROMTableBase = 0;
[15:29:26.788]              // -> [ROMTableBase <= 0x00000000]
[15:29:26.788]            __ap = 0;      // AHB-AP
[15:29:26.789]              // -> [__ap <= 0x00000000]
[15:29:26.789]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:29:26.790]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:29:26.790]              // -> [ROMTableBase <= 0xF0000000]
[15:29:26.790]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:29:26.791]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:29:26.791]              // -> [pidr1 <= 0x00000004]
[15:29:26.792]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:29:26.792]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:29:26.793]              // -> [pidr2 <= 0x0000000A]
[15:29:26.793]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:29:26.793]              // -> [jep106id <= 0x00000020]
[15:29:26.793]          </block>
[15:29:26.793]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:29:26.794]            // if-block "jep106id != 0x20"
[15:29:26.794]              // =>  FALSE
[15:29:26.794]            // skip if-block "jep106id != 0x20"
[15:29:26.794]          </control>
[15:29:26.794]        </sequence>
[15:29:26.794]    </block>
[15:29:26.795]  </sequence>
[15:29:26.795]  
[15:29:26.807]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:29:26.807]  
[15:29:26.826]  <debugvars>
[15:29:26.826]    // Pre-defined
[15:29:26.826]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:29:26.827]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:29:26.827]    __dp=0x00000000
[15:29:26.827]    __ap=0x00000000
[15:29:26.828]    __traceout=0x00000000      (Trace Disabled)
[15:29:26.828]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:29:26.829]    __FlashAddr=0x00000000
[15:29:26.829]    __FlashLen=0x00000000
[15:29:26.829]    __FlashArg=0x00000000
[15:29:26.829]    __FlashOp=0x00000000
[15:29:26.831]    __Result=0x00000000
[15:29:26.831]    
[15:29:26.831]    // User-defined
[15:29:26.831]    DbgMCU_CR=0x00000007
[15:29:26.831]    DbgMCU_APB1_Fz=0x00000000
[15:29:26.831]    DbgMCU_APB2_Fz=0x00000000
[15:29:26.832]    DoOptionByteLoading=0x00000000
[15:29:26.832]  </debugvars>
[15:29:26.832]  
[15:29:26.832]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:29:26.833]    <block atomic="false" info="">
[15:29:26.833]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:29:26.834]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:29:26.834]    </block>
[15:29:26.835]    <block atomic="false" info="DbgMCU registers">
[15:29:26.835]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:29:26.836]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[15:29:26.837]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[15:29:26.837]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:29:26.838]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:29:26.838]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:29:26.839]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:29:26.839]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:29:26.840]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:29:26.840]    </block>
[15:29:26.841]  </sequence>
[15:29:26.841]  
[15:29:34.652]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:29:34.652]  
[15:29:34.653]  <debugvars>
[15:29:34.653]    // Pre-defined
[15:29:34.653]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:29:34.653]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:29:34.654]    __dp=0x00000000
[15:29:34.654]    __ap=0x00000000
[15:29:34.654]    __traceout=0x00000000      (Trace Disabled)
[15:29:34.654]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:29:34.654]    __FlashAddr=0x00000000
[15:29:34.655]    __FlashLen=0x00000000
[15:29:34.655]    __FlashArg=0x00000000
[15:29:34.655]    __FlashOp=0x00000000
[15:29:34.655]    __Result=0x00000000
[15:29:34.655]    
[15:29:34.655]    // User-defined
[15:29:34.655]    DbgMCU_CR=0x00000007
[15:29:34.655]    DbgMCU_APB1_Fz=0x00000000
[15:29:34.657]    DbgMCU_APB2_Fz=0x00000000
[15:29:34.657]    DoOptionByteLoading=0x00000000
[15:29:34.657]  </debugvars>
[15:29:34.657]  
[15:29:34.658]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:29:34.658]    <block atomic="false" info="">
[15:29:34.658]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:29:34.658]        // -> [connectionFlash <= 0x00000001]
[15:29:34.658]      __var FLASH_BASE = 0x40022000 ;
[15:29:34.659]        // -> [FLASH_BASE <= 0x40022000]
[15:29:34.659]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:29:34.659]        // -> [FLASH_CR <= 0x40022004]
[15:29:34.659]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:29:34.659]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:29:34.660]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:29:34.660]        // -> [LOCK_BIT <= 0x00000001]
[15:29:34.660]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:29:34.660]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:29:34.660]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:29:34.661]        // -> [FLASH_KEYR <= 0x4002200C]
[15:29:34.661]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:29:34.661]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:29:34.661]      __var FLASH_KEY2 = 0x02030405 ;
[15:29:34.661]        // -> [FLASH_KEY2 <= 0x02030405]
[15:29:34.662]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:29:34.662]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:29:34.662]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:29:34.663]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:29:34.663]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:29:34.663]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:29:34.663]      __var FLASH_CR_Value = 0 ;
[15:29:34.664]        // -> [FLASH_CR_Value <= 0x00000000]
[15:29:34.664]      __var DoDebugPortStop = 1 ;
[15:29:34.664]        // -> [DoDebugPortStop <= 0x00000001]
[15:29:34.664]      __var DP_CTRL_STAT = 0x4 ;
[15:29:34.664]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:29:34.665]      __var DP_SELECT = 0x8 ;
[15:29:34.665]        // -> [DP_SELECT <= 0x00000008]
[15:29:34.665]    </block>
[15:29:34.665]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:29:34.665]      // if-block "connectionFlash && DoOptionByteLoading"
[15:29:34.666]        // =>  FALSE
[15:29:34.666]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:29:34.666]    </control>
[15:29:34.666]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:29:34.666]      // if-block "DoDebugPortStop"
[15:29:34.668]        // =>  TRUE
[15:29:34.668]      <block atomic="false" info="">
[15:29:34.668]        WriteDP(DP_SELECT, 0x00000000);
[15:29:34.669]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:29:34.669]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:29:34.670]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:29:34.670]      </block>
[15:29:34.670]      // end if-block "DoDebugPortStop"
[15:29:34.670]    </control>
[15:29:34.671]  </sequence>
[15:29:34.671]  
[15:32:24.472]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:32:24.472]  
[15:32:24.472]  <debugvars>
[15:32:24.473]    // Pre-defined
[15:32:24.473]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:32:24.473]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:32:24.473]    __dp=0x00000000
[15:32:24.474]    __ap=0x00000000
[15:32:24.474]    __traceout=0x00000000      (Trace Disabled)
[15:32:24.475]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:32:24.475]    __FlashAddr=0x00000000
[15:32:24.475]    __FlashLen=0x00000000
[15:32:24.475]    __FlashArg=0x00000000
[15:32:24.476]    __FlashOp=0x00000000
[15:32:24.476]    __Result=0x00000000
[15:32:24.476]    
[15:32:24.476]    // User-defined
[15:32:24.476]    DbgMCU_CR=0x00000007
[15:32:24.476]    DbgMCU_APB1_Fz=0x00000000
[15:32:24.477]    DbgMCU_APB2_Fz=0x00000000
[15:32:24.477]    DoOptionByteLoading=0x00000000
[15:32:24.477]  </debugvars>
[15:32:24.477]  
[15:32:24.478]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:32:24.479]    <block atomic="false" info="">
[15:32:24.479]      Sequence("CheckID");
[15:32:24.479]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:32:24.480]          <block atomic="false" info="">
[15:32:24.481]            __var pidr1 = 0;
[15:32:24.481]              // -> [pidr1 <= 0x00000000]
[15:32:24.481]            __var pidr2 = 0;
[15:32:24.481]              // -> [pidr2 <= 0x00000000]
[15:32:24.482]            __var jep106id = 0;
[15:32:24.482]              // -> [jep106id <= 0x00000000]
[15:32:24.483]            __var ROMTableBase = 0;
[15:32:24.483]              // -> [ROMTableBase <= 0x00000000]
[15:32:24.483]            __ap = 0;      // AHB-AP
[15:32:24.483]              // -> [__ap <= 0x00000000]
[15:32:24.483]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:32:24.484]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:32:24.484]              // -> [ROMTableBase <= 0xF0000000]
[15:32:24.485]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:32:24.486]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:32:24.486]              // -> [pidr1 <= 0x00000004]
[15:32:24.487]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:32:24.487]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:32:24.488]              // -> [pidr2 <= 0x0000000A]
[15:32:24.488]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:32:24.488]              // -> [jep106id <= 0x00000020]
[15:32:24.488]          </block>
[15:32:24.489]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:32:24.489]            // if-block "jep106id != 0x20"
[15:32:24.489]              // =>  FALSE
[15:32:24.489]            // skip if-block "jep106id != 0x20"
[15:32:24.489]          </control>
[15:32:24.489]        </sequence>
[15:32:24.490]    </block>
[15:32:24.490]  </sequence>
[15:32:24.490]  
[15:32:24.503]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:32:24.503]  
[15:32:24.521]  <debugvars>
[15:32:24.521]    // Pre-defined
[15:32:24.522]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:32:24.522]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:32:24.522]    __dp=0x00000000
[15:32:24.522]    __ap=0x00000000
[15:32:24.523]    __traceout=0x00000000      (Trace Disabled)
[15:32:24.523]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:32:24.524]    __FlashAddr=0x00000000
[15:32:24.524]    __FlashLen=0x00000000
[15:32:24.524]    __FlashArg=0x00000000
[15:32:24.525]    __FlashOp=0x00000000
[15:32:24.525]    __Result=0x00000000
[15:32:24.525]    
[15:32:24.525]    // User-defined
[15:32:24.525]    DbgMCU_CR=0x00000007
[15:32:24.526]    DbgMCU_APB1_Fz=0x00000000
[15:32:24.526]    DbgMCU_APB2_Fz=0x00000000
[15:32:24.526]    DoOptionByteLoading=0x00000000
[15:32:24.526]  </debugvars>
[15:32:24.527]  
[15:32:24.527]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:32:24.528]    <block atomic="false" info="">
[15:32:24.528]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:32:24.529]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:32:24.529]    </block>
[15:32:24.529]    <block atomic="false" info="DbgMCU registers">
[15:32:24.529]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:32:24.530]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[15:32:24.531]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[15:32:24.531]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:32:24.532]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:32:24.532]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:32:24.533]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:32:24.533]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:32:24.535]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:32:24.535]    </block>
[15:32:24.535]  </sequence>
[15:32:24.535]  
[15:32:32.327]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:32:32.327]  
[15:32:32.328]  <debugvars>
[15:32:32.328]    // Pre-defined
[15:32:32.329]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:32:32.329]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:32:32.329]    __dp=0x00000000
[15:32:32.330]    __ap=0x00000000
[15:32:32.330]    __traceout=0x00000000      (Trace Disabled)
[15:32:32.331]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:32:32.331]    __FlashAddr=0x00000000
[15:32:32.331]    __FlashLen=0x00000000
[15:32:32.331]    __FlashArg=0x00000000
[15:32:32.332]    __FlashOp=0x00000000
[15:32:32.332]    __Result=0x00000000
[15:32:32.333]    
[15:32:32.333]    // User-defined
[15:32:32.333]    DbgMCU_CR=0x00000007
[15:32:32.333]    DbgMCU_APB1_Fz=0x00000000
[15:32:32.333]    DbgMCU_APB2_Fz=0x00000000
[15:32:32.333]    DoOptionByteLoading=0x00000000
[15:32:32.333]  </debugvars>
[15:32:32.333]  
[15:32:32.333]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:32:32.333]    <block atomic="false" info="">
[15:32:32.333]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:32:32.333]        // -> [connectionFlash <= 0x00000001]
[15:32:32.335]      __var FLASH_BASE = 0x40022000 ;
[15:32:32.335]        // -> [FLASH_BASE <= 0x40022000]
[15:32:32.335]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:32:32.336]        // -> [FLASH_CR <= 0x40022004]
[15:32:32.336]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:32:32.336]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:32:32.336]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:32:32.337]        // -> [LOCK_BIT <= 0x00000001]
[15:32:32.337]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:32:32.337]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:32:32.338]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:32:32.338]        // -> [FLASH_KEYR <= 0x4002200C]
[15:32:32.338]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:32:32.339]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:32:32.339]      __var FLASH_KEY2 = 0x02030405 ;
[15:32:32.339]        // -> [FLASH_KEY2 <= 0x02030405]
[15:32:32.339]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:32:32.339]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:32:32.339]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:32:32.340]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:32:32.340]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:32:32.340]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:32:32.340]      __var FLASH_CR_Value = 0 ;
[15:32:32.340]        // -> [FLASH_CR_Value <= 0x00000000]
[15:32:32.340]      __var DoDebugPortStop = 1 ;
[15:32:32.340]        // -> [DoDebugPortStop <= 0x00000001]
[15:32:32.340]      __var DP_CTRL_STAT = 0x4 ;
[15:32:32.341]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:32:32.342]      __var DP_SELECT = 0x8 ;
[15:32:32.342]        // -> [DP_SELECT <= 0x00000008]
[15:32:32.342]    </block>
[15:32:32.342]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:32:32.343]      // if-block "connectionFlash && DoOptionByteLoading"
[15:32:32.343]        // =>  FALSE
[15:32:32.343]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:32:32.343]    </control>
[15:32:32.343]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:32:32.344]      // if-block "DoDebugPortStop"
[15:32:32.344]        // =>  TRUE
[15:32:32.344]      <block atomic="false" info="">
[15:32:32.344]        WriteDP(DP_SELECT, 0x00000000);
[15:32:32.345]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:32:32.345]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:32:32.345]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:32:32.346]      </block>
[15:32:32.346]      // end if-block "DoDebugPortStop"
[15:32:32.346]    </control>
[15:32:32.346]  </sequence>
[15:32:32.346]  
[16:00:40.094]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:00:40.094]  
[16:00:40.106]  <debugvars>
[16:00:40.107]    // Pre-defined
[16:00:40.107]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:00:40.107]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:00:40.107]    __dp=0x00000000
[16:00:40.108]    __ap=0x00000000
[16:00:40.108]    __traceout=0x00000000      (Trace Disabled)
[16:00:40.108]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:00:40.109]    __FlashAddr=0x00000000
[16:00:40.109]    __FlashLen=0x00000000
[16:00:40.109]    __FlashArg=0x00000000
[16:00:40.109]    __FlashOp=0x00000000
[16:00:40.109]    __Result=0x00000000
[16:00:40.110]    
[16:00:40.110]    // User-defined
[16:00:40.110]    DbgMCU_CR=0x00000007
[16:00:40.110]    DbgMCU_APB1_Fz=0x00000000
[16:00:40.110]    DbgMCU_APB2_Fz=0x00000000
[16:00:40.110]    DoOptionByteLoading=0x00000000
[16:00:40.111]  </debugvars>
[16:00:40.111]  
[16:00:40.111]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:00:40.111]    <block atomic="false" info="">
[16:00:40.112]      Sequence("CheckID");
[16:00:40.112]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:00:40.112]          <block atomic="false" info="">
[16:00:40.113]            __var pidr1 = 0;
[16:00:40.113]              // -> [pidr1 <= 0x00000000]
[16:00:40.113]            __var pidr2 = 0;
[16:00:40.113]              // -> [pidr2 <= 0x00000000]
[16:00:40.113]            __var jep106id = 0;
[16:00:40.114]              // -> [jep106id <= 0x00000000]
[16:00:40.114]            __var ROMTableBase = 0;
[16:00:40.114]              // -> [ROMTableBase <= 0x00000000]
[16:00:40.114]            __ap = 0;      // AHB-AP
[16:00:40.115]              // -> [__ap <= 0x00000000]
[16:00:40.115]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:00:40.116]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:00:40.117]              // -> [ROMTableBase <= 0xF0000000]
[16:00:40.117]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:00:40.118]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:00:40.118]              // -> [pidr1 <= 0x00000004]
[16:00:40.118]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:00:40.120]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:00:40.120]              // -> [pidr2 <= 0x0000000A]
[16:00:40.120]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:00:40.120]              // -> [jep106id <= 0x00000020]
[16:00:40.120]          </block>
[16:00:40.121]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:00:40.121]            // if-block "jep106id != 0x20"
[16:00:40.121]              // =>  FALSE
[16:00:40.121]            // skip if-block "jep106id != 0x20"
[16:00:40.122]          </control>
[16:00:40.122]        </sequence>
[16:00:40.122]    </block>
[16:00:40.122]  </sequence>
[16:00:40.122]  
[16:00:40.136]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:00:40.136]  
[16:00:40.136]  <debugvars>
[16:00:40.136]    // Pre-defined
[16:00:40.137]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:00:40.137]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:00:40.137]    __dp=0x00000000
[16:00:40.137]    __ap=0x00000000
[16:00:40.138]    __traceout=0x00000000      (Trace Disabled)
[16:00:40.138]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:00:40.138]    __FlashAddr=0x00000000
[16:00:40.138]    __FlashLen=0x00000000
[16:00:40.139]    __FlashArg=0x00000000
[16:00:40.139]    __FlashOp=0x00000000
[16:00:40.139]    __Result=0x00000000
[16:00:40.139]    
[16:00:40.139]    // User-defined
[16:00:40.140]    DbgMCU_CR=0x00000007
[16:00:40.140]    DbgMCU_APB1_Fz=0x00000000
[16:00:40.140]    DbgMCU_APB2_Fz=0x00000000
[16:00:40.140]    DoOptionByteLoading=0x00000000
[16:00:40.141]  </debugvars>
[16:00:40.141]  
[16:00:40.141]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:00:40.142]    <block atomic="false" info="">
[16:00:40.142]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:00:40.143]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:00:40.143]    </block>
[16:00:40.143]    <block atomic="false" info="DbgMCU registers">
[16:00:40.143]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:00:40.144]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:00:40.146]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:00:40.146]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:00:40.147]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:00:40.147]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:00:40.148]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:00:40.148]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:00:40.149]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:00:40.150]    </block>
[16:00:40.150]  </sequence>
[16:00:40.151]  
[16:00:48.189]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:00:48.189]  
[16:00:48.189]  <debugvars>
[16:00:48.189]    // Pre-defined
[16:00:48.190]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:00:48.190]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:00:48.190]    __dp=0x00000000
[16:00:48.191]    __ap=0x00000000
[16:00:48.191]    __traceout=0x00000000      (Trace Disabled)
[16:00:48.191]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:00:48.191]    __FlashAddr=0x00000000
[16:00:48.192]    __FlashLen=0x00000000
[16:00:48.192]    __FlashArg=0x00000000
[16:00:48.193]    __FlashOp=0x00000000
[16:00:48.193]    __Result=0x00000000
[16:00:48.193]    
[16:00:48.193]    // User-defined
[16:00:48.194]    DbgMCU_CR=0x00000007
[16:00:48.194]    DbgMCU_APB1_Fz=0x00000000
[16:00:48.194]    DbgMCU_APB2_Fz=0x00000000
[16:00:48.195]    DoOptionByteLoading=0x00000000
[16:00:48.195]  </debugvars>
[16:00:48.195]  
[16:00:48.195]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:00:48.195]    <block atomic="false" info="">
[16:00:48.195]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:00:48.196]        // -> [connectionFlash <= 0x00000001]
[16:00:48.196]      __var FLASH_BASE = 0x40022000 ;
[16:00:48.196]        // -> [FLASH_BASE <= 0x40022000]
[16:00:48.196]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:00:48.196]        // -> [FLASH_CR <= 0x40022004]
[16:00:48.197]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:00:48.197]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:00:48.197]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:00:48.197]        // -> [LOCK_BIT <= 0x00000001]
[16:00:48.197]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:00:48.197]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:00:48.198]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:00:48.198]        // -> [FLASH_KEYR <= 0x4002200C]
[16:00:48.198]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:00:48.198]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:00:48.198]      __var FLASH_KEY2 = 0x02030405 ;
[16:00:48.198]        // -> [FLASH_KEY2 <= 0x02030405]
[16:00:48.198]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:00:48.198]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:00:48.198]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:00:48.199]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:00:48.199]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:00:48.200]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:00:48.200]      __var FLASH_CR_Value = 0 ;
[16:00:48.201]        // -> [FLASH_CR_Value <= 0x00000000]
[16:00:48.201]      __var DoDebugPortStop = 1 ;
[16:00:48.201]        // -> [DoDebugPortStop <= 0x00000001]
[16:00:48.201]      __var DP_CTRL_STAT = 0x4 ;
[16:00:48.202]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:00:48.202]      __var DP_SELECT = 0x8 ;
[16:00:48.202]        // -> [DP_SELECT <= 0x00000008]
[16:00:48.202]    </block>
[16:00:48.202]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:00:48.203]      // if-block "connectionFlash && DoOptionByteLoading"
[16:00:48.203]        // =>  FALSE
[16:00:48.203]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:00:48.203]    </control>
[16:00:48.204]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:00:48.204]      // if-block "DoDebugPortStop"
[16:00:48.204]        // =>  TRUE
[16:00:48.204]      <block atomic="false" info="">
[16:00:48.205]        WriteDP(DP_SELECT, 0x00000000);
[16:00:48.206]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:00:48.206]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:00:48.206]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:00:48.207]      </block>
[16:00:48.207]      // end if-block "DoDebugPortStop"
[16:00:48.207]    </control>
[16:00:48.207]  </sequence>
[16:00:48.207]  
[16:02:43.515]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:02:43.515]  
[16:02:43.516]  <debugvars>
[16:02:43.516]    // Pre-defined
[16:02:43.516]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:02:43.517]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:02:43.517]    __dp=0x00000000
[16:02:43.517]    __ap=0x00000000
[16:02:43.517]    __traceout=0x00000000      (Trace Disabled)
[16:02:43.517]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:02:43.519]    __FlashAddr=0x00000000
[16:02:43.520]    __FlashLen=0x00000000
[16:02:43.520]    __FlashArg=0x00000000
[16:02:43.520]    __FlashOp=0x00000000
[16:02:43.520]    __Result=0x00000000
[16:02:43.521]    
[16:02:43.521]    // User-defined
[16:02:43.521]    DbgMCU_CR=0x00000007
[16:02:43.521]    DbgMCU_APB1_Fz=0x00000000
[16:02:43.521]    DbgMCU_APB2_Fz=0x00000000
[16:02:43.522]    DoOptionByteLoading=0x00000000
[16:02:43.522]  </debugvars>
[16:02:43.522]  
[16:02:43.523]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:02:43.523]    <block atomic="false" info="">
[16:02:43.523]      Sequence("CheckID");
[16:02:43.524]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:02:43.524]          <block atomic="false" info="">
[16:02:43.524]            __var pidr1 = 0;
[16:02:43.524]              // -> [pidr1 <= 0x00000000]
[16:02:43.524]            __var pidr2 = 0;
[16:02:43.525]              // -> [pidr2 <= 0x00000000]
[16:02:43.525]            __var jep106id = 0;
[16:02:43.525]              // -> [jep106id <= 0x00000000]
[16:02:43.525]            __var ROMTableBase = 0;
[16:02:43.526]              // -> [ROMTableBase <= 0x00000000]
[16:02:43.526]            __ap = 0;      // AHB-AP
[16:02:43.526]              // -> [__ap <= 0x00000000]
[16:02:43.526]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:02:43.528]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:02:43.528]              // -> [ROMTableBase <= 0xF0000000]
[16:02:43.528]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:02:43.529]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:02:43.530]              // -> [pidr1 <= 0x00000004]
[16:02:43.530]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:02:43.531]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:02:43.531]              // -> [pidr2 <= 0x0000000A]
[16:02:43.531]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:02:43.532]              // -> [jep106id <= 0x00000020]
[16:02:43.532]          </block>
[16:02:43.532]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:02:43.532]            // if-block "jep106id != 0x20"
[16:02:43.532]              // =>  FALSE
[16:02:43.532]            // skip if-block "jep106id != 0x20"
[16:02:43.532]          </control>
[16:02:43.533]        </sequence>
[16:02:43.533]    </block>
[16:02:43.534]  </sequence>
[16:02:43.534]  
[16:02:43.546]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:02:43.546]  
[16:02:43.559]  <debugvars>
[16:02:43.559]    // Pre-defined
[16:02:43.560]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:02:43.560]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:02:43.560]    __dp=0x00000000
[16:02:43.562]    __ap=0x00000000
[16:02:43.562]    __traceout=0x00000000      (Trace Disabled)
[16:02:43.562]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:02:43.562]    __FlashAddr=0x00000000
[16:02:43.563]    __FlashLen=0x00000000
[16:02:43.563]    __FlashArg=0x00000000
[16:02:43.563]    __FlashOp=0x00000000
[16:02:43.564]    __Result=0x00000000
[16:02:43.564]    
[16:02:43.564]    // User-defined
[16:02:43.564]    DbgMCU_CR=0x00000007
[16:02:43.564]    DbgMCU_APB1_Fz=0x00000000
[16:02:43.564]    DbgMCU_APB2_Fz=0x00000000
[16:02:43.565]    DoOptionByteLoading=0x00000000
[16:02:43.565]  </debugvars>
[16:02:43.565]  
[16:02:43.565]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:02:43.565]    <block atomic="false" info="">
[16:02:43.567]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:02:43.568]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:02:43.568]    </block>
[16:02:43.568]    <block atomic="false" info="DbgMCU registers">
[16:02:43.569]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:02:43.570]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:02:43.570]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:02:43.571]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:02:43.573]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:02:43.573]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:02:43.574]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:02:43.574]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:02:43.575]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:02:43.575]    </block>
[16:02:43.576]  </sequence>
[16:02:43.576]  
[16:02:51.650]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:02:51.650]  
[16:02:51.650]  <debugvars>
[16:02:51.651]    // Pre-defined
[16:02:51.651]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:02:51.651]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:02:51.651]    __dp=0x00000000
[16:02:51.652]    __ap=0x00000000
[16:02:51.652]    __traceout=0x00000000      (Trace Disabled)
[16:02:51.652]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:02:51.653]    __FlashAddr=0x00000000
[16:02:51.653]    __FlashLen=0x00000000
[16:02:51.653]    __FlashArg=0x00000000
[16:02:51.653]    __FlashOp=0x00000000
[16:02:51.653]    __Result=0x00000000
[16:02:51.653]    
[16:02:51.653]    // User-defined
[16:02:51.654]    DbgMCU_CR=0x00000007
[16:02:51.654]    DbgMCU_APB1_Fz=0x00000000
[16:02:51.654]    DbgMCU_APB2_Fz=0x00000000
[16:02:51.654]    DoOptionByteLoading=0x00000000
[16:02:51.655]  </debugvars>
[16:02:51.655]  
[16:02:51.655]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:02:51.655]    <block atomic="false" info="">
[16:02:51.655]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:02:51.655]        // -> [connectionFlash <= 0x00000001]
[16:02:51.656]      __var FLASH_BASE = 0x40022000 ;
[16:02:51.656]        // -> [FLASH_BASE <= 0x40022000]
[16:02:51.657]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:02:51.657]        // -> [FLASH_CR <= 0x40022004]
[16:02:51.657]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:02:51.657]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:02:51.657]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:02:51.657]        // -> [LOCK_BIT <= 0x00000001]
[16:02:51.658]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:02:51.658]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:02:51.658]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:02:51.658]        // -> [FLASH_KEYR <= 0x4002200C]
[16:02:51.658]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:02:51.659]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:02:51.659]      __var FLASH_KEY2 = 0x02030405 ;
[16:02:51.659]        // -> [FLASH_KEY2 <= 0x02030405]
[16:02:51.659]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:02:51.660]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:02:51.660]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:02:51.660]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:02:51.660]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:02:51.660]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:02:51.661]      __var FLASH_CR_Value = 0 ;
[16:02:51.661]        // -> [FLASH_CR_Value <= 0x00000000]
[16:02:51.661]      __var DoDebugPortStop = 1 ;
[16:02:51.661]        // -> [DoDebugPortStop <= 0x00000001]
[16:02:51.661]      __var DP_CTRL_STAT = 0x4 ;
[16:02:51.661]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:02:51.662]      __var DP_SELECT = 0x8 ;
[16:02:51.662]        // -> [DP_SELECT <= 0x00000008]
[16:02:51.662]    </block>
[16:02:51.662]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:02:51.662]      // if-block "connectionFlash && DoOptionByteLoading"
[16:02:51.663]        // =>  FALSE
[16:02:51.663]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:02:51.663]    </control>
[16:02:51.663]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:02:51.663]      // if-block "DoDebugPortStop"
[16:02:51.664]        // =>  TRUE
[16:02:51.664]      <block atomic="false" info="">
[16:02:51.664]        WriteDP(DP_SELECT, 0x00000000);
[16:02:51.664]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:02:51.665]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:02:51.666]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:02:51.666]      </block>
[16:02:51.666]      // end if-block "DoDebugPortStop"
[16:02:51.666]    </control>
[16:02:51.667]  </sequence>
[16:02:51.667]  
[16:05:57.773]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:05:57.773]  
[16:05:57.773]  <debugvars>
[16:05:57.774]    // Pre-defined
[16:05:57.774]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:05:57.774]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[16:05:57.774]    __dp=0x00000000
[16:05:57.774]    __ap=0x00000000
[16:05:57.774]    __traceout=0x00000000      (Trace Disabled)
[16:05:57.775]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:05:57.775]    __FlashAddr=0x00000000
[16:05:57.775]    __FlashLen=0x00000000
[16:05:57.776]    __FlashArg=0x00000000
[16:05:57.776]    __FlashOp=0x00000000
[16:05:57.776]    __Result=0x00000000
[16:05:57.776]    
[16:05:57.776]    // User-defined
[16:05:57.776]    DbgMCU_CR=0x00000007
[16:05:57.776]    DbgMCU_APB1_Fz=0x00000000
[16:05:57.777]    DbgMCU_APB2_Fz=0x00000000
[16:05:57.777]    DoOptionByteLoading=0x00000000
[16:05:57.777]  </debugvars>
[16:05:57.777]  
[16:05:57.777]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:05:57.777]    <block atomic="false" info="">
[16:05:57.778]      Sequence("CheckID");
[16:05:57.778]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:05:57.778]          <block atomic="false" info="">
[16:05:57.779]            __var pidr1 = 0;
[16:05:57.779]              // -> [pidr1 <= 0x00000000]
[16:05:57.779]            __var pidr2 = 0;
[16:05:57.779]              // -> [pidr2 <= 0x00000000]
[16:05:57.779]            __var jep106id = 0;
[16:05:57.780]              // -> [jep106id <= 0x00000000]
[16:05:57.780]            __var ROMTableBase = 0;
[16:05:57.780]              // -> [ROMTableBase <= 0x00000000]
[16:05:57.780]            __ap = 0;      // AHB-AP
[16:05:57.780]              // -> [__ap <= 0x00000000]
[16:05:57.781]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:05:57.781]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:05:57.781]              // -> [ROMTableBase <= 0xF0000000]
[16:05:57.782]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:05:57.783]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:05:57.783]              // -> [pidr1 <= 0x00000004]
[16:05:57.783]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:05:57.783]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:05:57.783]              // -> [pidr2 <= 0x0000000A]
[16:05:57.784]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:05:57.784]              // -> [jep106id <= 0x00000020]
[16:05:57.784]          </block>
[16:05:57.785]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:05:57.785]            // if-block "jep106id != 0x20"
[16:05:57.785]              // =>  FALSE
[16:05:57.785]            // skip if-block "jep106id != 0x20"
[16:05:57.785]          </control>
[16:05:57.786]        </sequence>
[16:05:57.786]    </block>
[16:05:57.786]  </sequence>
[16:05:57.786]  
[16:05:57.797]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:05:57.797]  
[16:05:57.809]  <debugvars>
[16:05:57.809]    // Pre-defined
[16:05:57.810]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:05:57.810]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[16:05:57.810]    __dp=0x00000000
[16:05:57.811]    __ap=0x00000000
[16:05:57.811]    __traceout=0x00000000      (Trace Disabled)
[16:05:57.811]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:05:57.811]    __FlashAddr=0x00000000
[16:05:57.812]    __FlashLen=0x00000000
[16:05:57.812]    __FlashArg=0x00000000
[16:05:57.812]    __FlashOp=0x00000000
[16:05:57.813]    __Result=0x00000000
[16:05:57.813]    
[16:05:57.813]    // User-defined
[16:05:57.813]    DbgMCU_CR=0x00000007
[16:05:57.813]    DbgMCU_APB1_Fz=0x00000000
[16:05:57.813]    DbgMCU_APB2_Fz=0x00000000
[16:05:57.814]    DoOptionByteLoading=0x00000000
[16:05:57.814]  </debugvars>
[16:05:57.814]  
[16:05:57.814]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:05:57.814]    <block atomic="false" info="">
[16:05:57.816]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:05:57.816]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:05:57.817]    </block>
[16:05:57.817]    <block atomic="false" info="DbgMCU registers">
[16:05:57.817]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:05:57.818]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:05:57.818]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:05:57.818]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:05:57.820]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:05:57.820]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:05:57.821]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:05:57.821]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:05:57.822]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:05:57.823]    </block>
[16:05:57.823]  </sequence>
[16:05:57.823]  
[16:10:02.259]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:10:02.259]  
[16:10:02.260]  <debugvars>
[16:10:02.260]    // Pre-defined
[16:10:02.261]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:10:02.261]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[16:10:02.261]    __dp=0x00000000
[16:10:02.261]    __ap=0x00000000
[16:10:02.262]    __traceout=0x00000000      (Trace Disabled)
[16:10:02.262]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:10:02.262]    __FlashAddr=0x00000000
[16:10:02.262]    __FlashLen=0x00000000
[16:10:02.262]    __FlashArg=0x00000000
[16:10:02.263]    __FlashOp=0x00000000
[16:10:02.263]    __Result=0x00000000
[16:10:02.263]    
[16:10:02.263]    // User-defined
[16:10:02.263]    DbgMCU_CR=0x00000007
[16:10:02.263]    DbgMCU_APB1_Fz=0x00000000
[16:10:02.263]    DbgMCU_APB2_Fz=0x00000000
[16:10:02.264]    DoOptionByteLoading=0x00000000
[16:10:02.264]  </debugvars>
[16:10:02.264]  
[16:10:02.264]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:10:02.265]    <block atomic="false" info="">
[16:10:02.265]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:10:02.266]        // -> [connectionFlash <= 0x00000000]
[16:10:02.266]      __var FLASH_BASE = 0x40022000 ;
[16:10:02.266]        // -> [FLASH_BASE <= 0x40022000]
[16:10:02.266]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:10:02.266]        // -> [FLASH_CR <= 0x40022004]
[16:10:02.266]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:10:02.267]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:10:02.267]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:10:02.267]        // -> [LOCK_BIT <= 0x00000001]
[16:10:02.267]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:10:02.267]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:10:02.268]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:10:02.268]        // -> [FLASH_KEYR <= 0x4002200C]
[16:10:02.268]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:10:02.268]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:10:02.268]      __var FLASH_KEY2 = 0x02030405 ;
[16:10:02.268]        // -> [FLASH_KEY2 <= 0x02030405]
[16:10:02.269]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:10:02.269]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:10:02.269]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:10:02.269]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:10:02.269]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:10:02.270]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:10:02.270]      __var FLASH_CR_Value = 0 ;
[16:10:02.270]        // -> [FLASH_CR_Value <= 0x00000000]
[16:10:02.270]      __var DoDebugPortStop = 1 ;
[16:10:02.270]        // -> [DoDebugPortStop <= 0x00000001]
[16:10:02.270]      __var DP_CTRL_STAT = 0x4 ;
[16:10:02.270]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:10:02.271]      __var DP_SELECT = 0x8 ;
[16:10:02.271]        // -> [DP_SELECT <= 0x00000008]
[16:10:02.271]    </block>
[16:10:02.271]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:10:02.271]      // if-block "connectionFlash && DoOptionByteLoading"
[16:10:02.272]        // =>  FALSE
[16:10:02.272]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:10:02.272]    </control>
[16:10:02.272]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:10:02.272]      // if-block "DoDebugPortStop"
[16:10:02.273]        // =>  TRUE
[16:10:02.273]      <block atomic="false" info="">
[16:10:02.273]        WriteDP(DP_SELECT, 0x00000000);
[16:10:02.273]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:10:02.273]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:10:02.274]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:10:02.274]      </block>
[16:10:02.274]      // end if-block "DoDebugPortStop"
[16:10:02.275]    </control>
[16:10:02.275]  </sequence>
[16:10:02.275]  
[16:11:11.913]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:11:11.913]  
[16:11:11.914]  <debugvars>
[16:11:11.914]    // Pre-defined
[16:11:11.914]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:11:11.915]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:11:11.915]    __dp=0x00000000
[16:11:11.915]    __ap=0x00000000
[16:11:11.915]    __traceout=0x00000000      (Trace Disabled)
[16:11:11.916]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:11:11.916]    __FlashAddr=0x00000000
[16:11:11.916]    __FlashLen=0x00000000
[16:11:11.917]    __FlashArg=0x00000000
[16:11:11.917]    __FlashOp=0x00000000
[16:11:11.917]    __Result=0x00000000
[16:11:11.917]    
[16:11:11.917]    // User-defined
[16:11:11.917]    DbgMCU_CR=0x00000007
[16:11:11.918]    DbgMCU_APB1_Fz=0x00000000
[16:11:11.918]    DbgMCU_APB2_Fz=0x00000000
[16:11:11.918]    DoOptionByteLoading=0x00000000
[16:11:11.918]  </debugvars>
[16:11:11.918]  
[16:11:11.918]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:11:11.919]    <block atomic="false" info="">
[16:11:11.919]      Sequence("CheckID");
[16:11:11.919]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:11:11.919]          <block atomic="false" info="">
[16:11:11.919]            __var pidr1 = 0;
[16:11:11.919]              // -> [pidr1 <= 0x00000000]
[16:11:11.919]            __var pidr2 = 0;
[16:11:11.919]              // -> [pidr2 <= 0x00000000]
[16:11:11.919]            __var jep106id = 0;
[16:11:11.920]              // -> [jep106id <= 0x00000000]
[16:11:11.920]            __var ROMTableBase = 0;
[16:11:11.920]              // -> [ROMTableBase <= 0x00000000]
[16:11:11.920]            __ap = 0;      // AHB-AP
[16:11:11.920]              // -> [__ap <= 0x00000000]
[16:11:11.920]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:11:11.922]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:11:11.923]              // -> [ROMTableBase <= 0xF0000000]
[16:11:11.923]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:11:11.923]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:11:11.924]              // -> [pidr1 <= 0x00000004]
[16:11:11.924]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:11:11.925]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:11:11.925]              // -> [pidr2 <= 0x0000000A]
[16:11:11.926]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:11:11.926]              // -> [jep106id <= 0x00000020]
[16:11:11.926]          </block>
[16:11:11.927]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:11:11.927]            // if-block "jep106id != 0x20"
[16:11:11.927]              // =>  FALSE
[16:11:11.927]            // skip if-block "jep106id != 0x20"
[16:11:11.927]          </control>
[16:11:11.927]        </sequence>
[16:11:11.928]    </block>
[16:11:11.928]  </sequence>
[16:11:11.928]  
[16:11:11.940]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:11:11.940]  
[16:11:11.940]  <debugvars>
[16:11:11.941]    // Pre-defined
[16:11:11.941]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:11:11.941]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:11:11.942]    __dp=0x00000000
[16:11:11.942]    __ap=0x00000000
[16:11:11.942]    __traceout=0x00000000      (Trace Disabled)
[16:11:11.942]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:11:11.943]    __FlashAddr=0x00000000
[16:11:11.943]    __FlashLen=0x00000000
[16:11:11.943]    __FlashArg=0x00000000
[16:11:11.943]    __FlashOp=0x00000000
[16:11:11.943]    __Result=0x00000000
[16:11:11.943]    
[16:11:11.943]    // User-defined
[16:11:11.943]    DbgMCU_CR=0x00000007
[16:11:11.943]    DbgMCU_APB1_Fz=0x00000000
[16:11:11.944]    DbgMCU_APB2_Fz=0x00000000
[16:11:11.944]    DoOptionByteLoading=0x00000000
[16:11:11.944]  </debugvars>
[16:11:11.944]  
[16:11:11.945]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:11:11.945]    <block atomic="false" info="">
[16:11:11.946]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:11:11.947]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:11:11.947]    </block>
[16:11:11.947]    <block atomic="false" info="DbgMCU registers">
[16:11:11.947]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:11:11.949]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:11:11.950]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:11:11.950]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:11:11.951]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:11:11.951]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:11:11.953]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:11:11.953]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:11:11.953]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:11:11.954]    </block>
[16:11:11.954]  </sequence>
[16:11:11.954]  
[16:11:19.991]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:11:19.991]  
[16:11:19.991]  <debugvars>
[16:11:19.991]    // Pre-defined
[16:11:19.991]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:11:19.992]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:11:19.992]    __dp=0x00000000
[16:11:19.992]    __ap=0x00000000
[16:11:19.992]    __traceout=0x00000000      (Trace Disabled)
[16:11:19.993]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:11:19.993]    __FlashAddr=0x00000000
[16:11:19.994]    __FlashLen=0x00000000
[16:11:19.994]    __FlashArg=0x00000000
[16:11:19.994]    __FlashOp=0x00000000
[16:11:19.994]    __Result=0x00000000
[16:11:19.995]    
[16:11:19.995]    // User-defined
[16:11:19.995]    DbgMCU_CR=0x00000007
[16:11:19.995]    DbgMCU_APB1_Fz=0x00000000
[16:11:19.995]    DbgMCU_APB2_Fz=0x00000000
[16:11:19.995]    DoOptionByteLoading=0x00000000
[16:11:19.996]  </debugvars>
[16:11:19.996]  
[16:11:19.996]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:11:19.996]    <block atomic="false" info="">
[16:11:19.997]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:11:19.997]        // -> [connectionFlash <= 0x00000001]
[16:11:19.997]      __var FLASH_BASE = 0x40022000 ;
[16:11:19.997]        // -> [FLASH_BASE <= 0x40022000]
[16:11:19.997]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:11:19.997]        // -> [FLASH_CR <= 0x40022004]
[16:11:19.997]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:11:19.998]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:11:19.998]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:11:19.998]        // -> [LOCK_BIT <= 0x00000001]
[16:11:19.998]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:11:19.998]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:11:19.999]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:11:19.999]        // -> [FLASH_KEYR <= 0x4002200C]
[16:11:19.999]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:11:19.999]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:11:19.999]      __var FLASH_KEY2 = 0x02030405 ;
[16:11:19.999]        // -> [FLASH_KEY2 <= 0x02030405]
[16:11:19.999]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:11:20.001]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:11:20.001]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:11:20.001]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:11:20.001]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:11:20.002]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:11:20.002]      __var FLASH_CR_Value = 0 ;
[16:11:20.002]        // -> [FLASH_CR_Value <= 0x00000000]
[16:11:20.002]      __var DoDebugPortStop = 1 ;
[16:11:20.002]        // -> [DoDebugPortStop <= 0x00000001]
[16:11:20.002]      __var DP_CTRL_STAT = 0x4 ;
[16:11:20.002]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:11:20.002]      __var DP_SELECT = 0x8 ;
[16:11:20.003]        // -> [DP_SELECT <= 0x00000008]
[16:11:20.003]    </block>
[16:11:20.003]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:11:20.004]      // if-block "connectionFlash && DoOptionByteLoading"
[16:11:20.004]        // =>  FALSE
[16:11:20.004]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:11:20.004]    </control>
[16:11:20.005]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:11:20.005]      // if-block "DoDebugPortStop"
[16:11:20.005]        // =>  TRUE
[16:11:20.005]      <block atomic="false" info="">
[16:11:20.006]        WriteDP(DP_SELECT, 0x00000000);
[16:11:20.006]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:11:20.006]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:11:20.007]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:11:20.007]      </block>
[16:11:20.007]      // end if-block "DoDebugPortStop"
[16:11:20.007]    </control>
[16:11:20.008]  </sequence>
[16:11:20.008]  
[16:16:31.401]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:16:31.401]  
[16:16:31.401]  <debugvars>
[16:16:31.401]    // Pre-defined
[16:16:31.401]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:16:31.402]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:16:31.402]    __dp=0x00000000
[16:16:31.402]    __ap=0x00000000
[16:16:31.402]    __traceout=0x00000000      (Trace Disabled)
[16:16:31.402]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:16:31.403]    __FlashAddr=0x00000000
[16:16:31.403]    __FlashLen=0x00000000
[16:16:31.403]    __FlashArg=0x00000000
[16:16:31.404]    __FlashOp=0x00000000
[16:16:31.404]    __Result=0x00000000
[16:16:31.404]    
[16:16:31.404]    // User-defined
[16:16:31.404]    DbgMCU_CR=0x00000007
[16:16:31.404]    DbgMCU_APB1_Fz=0x00000000
[16:16:31.404]    DbgMCU_APB2_Fz=0x00000000
[16:16:31.404]    DoOptionByteLoading=0x00000000
[16:16:31.405]  </debugvars>
[16:16:31.405]  
[16:16:31.405]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:16:31.405]    <block atomic="false" info="">
[16:16:31.405]      Sequence("CheckID");
[16:16:31.406]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:16:31.406]          <block atomic="false" info="">
[16:16:31.406]            __var pidr1 = 0;
[16:16:31.406]              // -> [pidr1 <= 0x00000000]
[16:16:31.406]            __var pidr2 = 0;
[16:16:31.406]              // -> [pidr2 <= 0x00000000]
[16:16:31.407]            __var jep106id = 0;
[16:16:31.407]              // -> [jep106id <= 0x00000000]
[16:16:31.407]            __var ROMTableBase = 0;
[16:16:31.407]              // -> [ROMTableBase <= 0x00000000]
[16:16:31.407]            __ap = 0;      // AHB-AP
[16:16:31.408]              // -> [__ap <= 0x00000000]
[16:16:31.408]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:16:31.408]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:16:31.408]              // -> [ROMTableBase <= 0xF0000000]
[16:16:31.409]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:16:31.411]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:16:31.411]              // -> [pidr1 <= 0x00000004]
[16:16:31.411]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:16:31.412]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:16:31.412]              // -> [pidr2 <= 0x0000000A]
[16:16:31.413]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:16:31.413]              // -> [jep106id <= 0x00000020]
[16:16:31.413]          </block>
[16:16:31.413]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:16:31.413]            // if-block "jep106id != 0x20"
[16:16:31.414]              // =>  FALSE
[16:16:31.414]            // skip if-block "jep106id != 0x20"
[16:16:31.414]          </control>
[16:16:31.414]        </sequence>
[16:16:31.414]    </block>
[16:16:31.414]  </sequence>
[16:16:31.415]  
[16:16:31.427]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:16:31.427]  
[16:16:31.427]  <debugvars>
[16:16:31.427]    // Pre-defined
[16:16:31.427]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:16:31.427]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:16:31.428]    __dp=0x00000000
[16:16:31.428]    __ap=0x00000000
[16:16:31.428]    __traceout=0x00000000      (Trace Disabled)
[16:16:31.428]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:16:31.428]    __FlashAddr=0x00000000
[16:16:31.428]    __FlashLen=0x00000000
[16:16:31.429]    __FlashArg=0x00000000
[16:16:31.429]    __FlashOp=0x00000000
[16:16:31.429]    __Result=0x00000000
[16:16:31.429]    
[16:16:31.429]    // User-defined
[16:16:31.429]    DbgMCU_CR=0x00000007
[16:16:31.430]    DbgMCU_APB1_Fz=0x00000000
[16:16:31.430]    DbgMCU_APB2_Fz=0x00000000
[16:16:31.430]    DoOptionByteLoading=0x00000000
[16:16:31.430]  </debugvars>
[16:16:31.430]  
[16:16:31.430]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:16:31.431]    <block atomic="false" info="">
[16:16:31.431]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:16:31.432]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:16:31.432]    </block>
[16:16:31.432]    <block atomic="false" info="DbgMCU registers">
[16:16:31.432]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:16:31.433]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:16:31.434]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:16:31.434]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:16:31.435]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:16:31.435]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:16:31.436]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:16:31.436]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:16:31.437]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:16:31.437]    </block>
[16:16:31.437]  </sequence>
[16:16:31.438]  
[16:16:39.615]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:16:39.615]  
[16:16:39.617]  <debugvars>
[16:16:39.618]    // Pre-defined
[16:16:39.618]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:16:39.619]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:16:39.620]    __dp=0x00000000
[16:16:39.620]    __ap=0x00000000
[16:16:39.620]    __traceout=0x00000000      (Trace Disabled)
[16:16:39.621]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:16:39.621]    __FlashAddr=0x00000000
[16:16:39.622]    __FlashLen=0x00000000
[16:16:39.623]    __FlashArg=0x00000000
[16:16:39.624]    __FlashOp=0x00000000
[16:16:39.624]    __Result=0x00000000
[16:16:39.625]    
[16:16:39.625]    // User-defined
[16:16:39.625]    DbgMCU_CR=0x00000007
[16:16:39.626]    DbgMCU_APB1_Fz=0x00000000
[16:16:39.626]    DbgMCU_APB2_Fz=0x00000000
[16:16:39.626]    DoOptionByteLoading=0x00000000
[16:16:39.627]  </debugvars>
[16:16:39.627]  
[16:16:39.628]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:16:39.628]    <block atomic="false" info="">
[16:16:39.629]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:16:39.630]        // -> [connectionFlash <= 0x00000001]
[16:16:39.630]      __var FLASH_BASE = 0x40022000 ;
[16:16:39.631]        // -> [FLASH_BASE <= 0x40022000]
[16:16:39.632]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:16:39.633]        // -> [FLASH_CR <= 0x40022004]
[16:16:39.633]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:16:39.633]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:16:39.634]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:16:39.634]        // -> [LOCK_BIT <= 0x00000001]
[16:16:39.635]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:16:39.635]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:16:39.636]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:16:39.636]        // -> [FLASH_KEYR <= 0x4002200C]
[16:16:39.637]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:16:39.637]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:16:39.638]      __var FLASH_KEY2 = 0x02030405 ;
[16:16:39.638]        // -> [FLASH_KEY2 <= 0x02030405]
[16:16:39.638]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:16:39.639]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:16:39.639]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:16:39.639]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:16:39.639]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:16:39.640]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:16:39.640]      __var FLASH_CR_Value = 0 ;
[16:16:39.640]        // -> [FLASH_CR_Value <= 0x00000000]
[16:16:39.641]      __var DoDebugPortStop = 1 ;
[16:16:39.641]        // -> [DoDebugPortStop <= 0x00000001]
[16:16:39.641]      __var DP_CTRL_STAT = 0x4 ;
[16:16:39.642]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:16:39.642]      __var DP_SELECT = 0x8 ;
[16:16:39.642]        // -> [DP_SELECT <= 0x00000008]
[16:16:39.642]    </block>
[16:16:39.643]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:16:39.643]      // if-block "connectionFlash && DoOptionByteLoading"
[16:16:39.643]        // =>  FALSE
[16:16:39.643]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:16:39.643]    </control>
[16:16:39.643]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:16:39.644]      // if-block "DoDebugPortStop"
[16:16:39.644]        // =>  TRUE
[16:16:39.644]      <block atomic="false" info="">
[16:16:39.644]        WriteDP(DP_SELECT, 0x00000000);
[16:16:39.645]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:16:39.645]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:16:39.646]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:16:39.646]      </block>
[16:16:39.647]      // end if-block "DoDebugPortStop"
[16:16:39.647]    </control>
[16:16:39.647]  </sequence>
[16:16:39.648]  
[17:08:59.236]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:08:59.236]  
[17:08:59.249]  <debugvars>
[17:08:59.249]    // Pre-defined
[17:08:59.249]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:08:59.250]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[17:08:59.250]    __dp=0x00000000
[17:08:59.250]    __ap=0x00000000
[17:08:59.250]    __traceout=0x00000000      (Trace Disabled)
[17:08:59.251]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:08:59.251]    __FlashAddr=0x00000000
[17:08:59.251]    __FlashLen=0x00000000
[17:08:59.251]    __FlashArg=0x00000000
[17:08:59.252]    __FlashOp=0x00000000
[17:08:59.252]    __Result=0x00000000
[17:08:59.253]    
[17:08:59.253]    // User-defined
[17:08:59.253]    DbgMCU_CR=0x00000007
[17:08:59.253]    DbgMCU_APB1_Fz=0x00000000
[17:08:59.254]    DbgMCU_APB2_Fz=0x00000000
[17:08:59.254]    DoOptionByteLoading=0x00000000
[17:08:59.254]  </debugvars>
[17:08:59.254]  
[17:08:59.255]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:08:59.255]    <block atomic="false" info="">
[17:08:59.255]      Sequence("CheckID");
[17:08:59.255]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:08:59.255]          <block atomic="false" info="">
[17:08:59.255]            __var pidr1 = 0;
[17:08:59.255]              // -> [pidr1 <= 0x00000000]
[17:08:59.257]            __var pidr2 = 0;
[17:08:59.257]              // -> [pidr2 <= 0x00000000]
[17:08:59.257]            __var jep106id = 0;
[17:08:59.257]              // -> [jep106id <= 0x00000000]
[17:08:59.257]            __var ROMTableBase = 0;
[17:08:59.258]              // -> [ROMTableBase <= 0x00000000]
[17:08:59.258]            __ap = 0;      // AHB-AP
[17:08:59.258]              // -> [__ap <= 0x00000000]
[17:08:59.258]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:08:59.258]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:08:59.259]              // -> [ROMTableBase <= 0xF0000000]
[17:08:59.260]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:08:59.261]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:08:59.261]              // -> [pidr1 <= 0x00000004]
[17:08:59.261]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:08:59.262]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:08:59.263]              // -> [pidr2 <= 0x0000000A]
[17:08:59.263]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:08:59.264]              // -> [jep106id <= 0x00000020]
[17:08:59.264]          </block>
[17:08:59.264]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:08:59.264]            // if-block "jep106id != 0x20"
[17:08:59.265]              // =>  FALSE
[17:08:59.265]            // skip if-block "jep106id != 0x20"
[17:08:59.265]          </control>
[17:08:59.265]        </sequence>
[17:08:59.265]    </block>
[17:08:59.266]  </sequence>
[17:08:59.266]  
[17:08:59.278]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:08:59.278]  
[17:08:59.281]  <debugvars>
[17:08:59.282]    // Pre-defined
[17:08:59.283]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:08:59.283]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[17:08:59.283]    __dp=0x00000000
[17:08:59.283]    __ap=0x00000000
[17:08:59.284]    __traceout=0x00000000      (Trace Disabled)
[17:08:59.284]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:08:59.284]    __FlashAddr=0x00000000
[17:08:59.284]    __FlashLen=0x00000000
[17:08:59.285]    __FlashArg=0x00000000
[17:08:59.285]    __FlashOp=0x00000000
[17:08:59.285]    __Result=0x00000000
[17:08:59.287]    
[17:08:59.287]    // User-defined
[17:08:59.287]    DbgMCU_CR=0x00000007
[17:08:59.287]    DbgMCU_APB1_Fz=0x00000000
[17:08:59.287]    DbgMCU_APB2_Fz=0x00000000
[17:08:59.288]    DoOptionByteLoading=0x00000000
[17:08:59.288]  </debugvars>
[17:08:59.288]  
[17:08:59.288]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:08:59.288]    <block atomic="false" info="">
[17:08:59.289]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:08:59.290]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:08:59.290]    </block>
[17:08:59.290]    <block atomic="false" info="DbgMCU registers">
[17:08:59.291]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:08:59.292]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[17:08:59.293]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[17:08:59.294]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:08:59.295]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:08:59.295]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:08:59.296]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:08:59.296]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:08:59.297]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:08:59.298]    </block>
[17:08:59.298]  </sequence>
[17:08:59.298]  
[17:13:48.679]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:13:48.679]  
[17:13:48.679]  <debugvars>
[17:13:48.679]    // Pre-defined
[17:13:48.680]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:13:48.680]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[17:13:48.680]    __dp=0x00000000
[17:13:48.680]    __ap=0x00000000
[17:13:48.680]    __traceout=0x00000000      (Trace Disabled)
[17:13:48.681]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:13:48.681]    __FlashAddr=0x00000000
[17:13:48.681]    __FlashLen=0x00000000
[17:13:48.681]    __FlashArg=0x00000000
[17:13:48.682]    __FlashOp=0x00000000
[17:13:48.682]    __Result=0x00000000
[17:13:48.682]    
[17:13:48.682]    // User-defined
[17:13:48.682]    DbgMCU_CR=0x00000007
[17:13:48.682]    DbgMCU_APB1_Fz=0x00000000
[17:13:48.683]    DbgMCU_APB2_Fz=0x00000000
[17:13:48.683]    DoOptionByteLoading=0x00000000
[17:13:48.683]  </debugvars>
[17:13:48.684]  
[17:13:48.684]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:13:48.684]    <block atomic="false" info="">
[17:13:48.684]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:13:48.684]        // -> [connectionFlash <= 0x00000000]
[17:13:48.684]      __var FLASH_BASE = 0x40022000 ;
[17:13:48.685]        // -> [FLASH_BASE <= 0x40022000]
[17:13:48.685]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:13:48.685]        // -> [FLASH_CR <= 0x40022004]
[17:13:48.685]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:13:48.685]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:13:48.685]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:13:48.685]        // -> [LOCK_BIT <= 0x00000001]
[17:13:48.686]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:13:48.686]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:13:48.686]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:13:48.686]        // -> [FLASH_KEYR <= 0x4002200C]
[17:13:48.686]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:13:48.687]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:13:48.687]      __var FLASH_KEY2 = 0x02030405 ;
[17:13:48.687]        // -> [FLASH_KEY2 <= 0x02030405]
[17:13:48.687]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:13:48.687]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:13:48.688]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:13:48.688]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:13:48.688]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:13:48.688]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:13:48.688]      __var FLASH_CR_Value = 0 ;
[17:13:48.689]        // -> [FLASH_CR_Value <= 0x00000000]
[17:13:48.689]      __var DoDebugPortStop = 1 ;
[17:13:48.689]        // -> [DoDebugPortStop <= 0x00000001]
[17:13:48.689]      __var DP_CTRL_STAT = 0x4 ;
[17:13:48.690]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:13:48.690]      __var DP_SELECT = 0x8 ;
[17:13:48.690]        // -> [DP_SELECT <= 0x00000008]
[17:13:48.690]    </block>
[17:13:48.690]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:13:48.691]      // if-block "connectionFlash && DoOptionByteLoading"
[17:13:48.691]        // =>  FALSE
[17:13:48.691]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:13:48.691]    </control>
[17:13:48.691]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:13:48.691]      // if-block "DoDebugPortStop"
[17:13:48.693]        // =>  TRUE
[17:13:48.693]      <block atomic="false" info="">
[17:13:48.693]        WriteDP(DP_SELECT, 0x00000000);
[17:13:48.694]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:13:48.694]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:13:48.695]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:13:48.695]      </block>
[17:13:48.695]      // end if-block "DoDebugPortStop"
[17:13:48.695]    </control>
[17:13:48.697]  </sequence>
[17:13:48.697]  
[17:15:20.206]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:15:20.206]  
[17:15:20.207]  <debugvars>
[17:15:20.207]    // Pre-defined
[17:15:20.207]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:15:20.207]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:15:20.208]    __dp=0x00000000
[17:15:20.208]    __ap=0x00000000
[17:15:20.208]    __traceout=0x00000000      (Trace Disabled)
[17:15:20.209]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:15:20.209]    __FlashAddr=0x00000000
[17:15:20.209]    __FlashLen=0x00000000
[17:15:20.209]    __FlashArg=0x00000000
[17:15:20.210]    __FlashOp=0x00000000
[17:15:20.210]    __Result=0x00000000
[17:15:20.210]    
[17:15:20.210]    // User-defined
[17:15:20.210]    DbgMCU_CR=0x00000007
[17:15:20.210]    DbgMCU_APB1_Fz=0x00000000
[17:15:20.210]    DbgMCU_APB2_Fz=0x00000000
[17:15:20.211]    DoOptionByteLoading=0x00000000
[17:15:20.211]  </debugvars>
[17:15:20.211]  
[17:15:20.211]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:15:20.212]    <block atomic="false" info="">
[17:15:20.212]      Sequence("CheckID");
[17:15:20.213]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:15:20.213]          <block atomic="false" info="">
[17:15:20.213]            __var pidr1 = 0;
[17:15:20.213]              // -> [pidr1 <= 0x00000000]
[17:15:20.213]            __var pidr2 = 0;
[17:15:20.213]              // -> [pidr2 <= 0x00000000]
[17:15:20.213]            __var jep106id = 0;
[17:15:20.214]              // -> [jep106id <= 0x00000000]
[17:15:20.214]            __var ROMTableBase = 0;
[17:15:20.214]              // -> [ROMTableBase <= 0x00000000]
[17:15:20.215]            __ap = 0;      // AHB-AP
[17:15:20.215]              // -> [__ap <= 0x00000000]
[17:15:20.215]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:15:20.216]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:15:20.216]              // -> [ROMTableBase <= 0xF0000000]
[17:15:20.217]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:15:20.218]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:15:20.218]              // -> [pidr1 <= 0x00000004]
[17:15:20.218]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:15:20.219]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:15:20.220]              // -> [pidr2 <= 0x0000000A]
[17:15:20.220]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:15:20.220]              // -> [jep106id <= 0x00000020]
[17:15:20.220]          </block>
[17:15:20.221]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:15:20.221]            // if-block "jep106id != 0x20"
[17:15:20.221]              // =>  FALSE
[17:15:20.221]            // skip if-block "jep106id != 0x20"
[17:15:20.221]          </control>
[17:15:20.221]        </sequence>
[17:15:20.223]    </block>
[17:15:20.223]  </sequence>
[17:15:20.223]  
[17:15:20.236]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:15:20.236]  
[17:15:20.242]  <debugvars>
[17:15:20.281]    // Pre-defined
[17:15:20.281]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:15:20.282]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:15:20.282]    __dp=0x00000000
[17:15:20.282]    __ap=0x00000000
[17:15:20.283]    __traceout=0x00000000      (Trace Disabled)
[17:15:20.283]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:15:20.283]    __FlashAddr=0x00000000
[17:15:20.283]    __FlashLen=0x00000000
[17:15:20.284]    __FlashArg=0x00000000
[17:15:20.284]    __FlashOp=0x00000000
[17:15:20.284]    __Result=0x00000000
[17:15:20.285]    
[17:15:20.285]    // User-defined
[17:15:20.286]    DbgMCU_CR=0x00000007
[17:15:20.286]    DbgMCU_APB1_Fz=0x00000000
[17:15:20.286]    DbgMCU_APB2_Fz=0x00000000
[17:15:20.286]    DoOptionByteLoading=0x00000000
[17:15:20.286]  </debugvars>
[17:15:20.287]  
[17:15:20.287]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:15:20.287]    <block atomic="false" info="">
[17:15:20.287]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:15:20.289]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:15:20.290]    </block>
[17:15:20.290]    <block atomic="false" info="DbgMCU registers">
[17:15:20.290]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:15:20.291]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[17:15:20.292]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[17:15:20.293]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:15:20.293]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:15:20.294]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:15:20.295]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:15:20.295]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:15:20.296]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:15:20.296]    </block>
[17:15:20.297]  </sequence>
[17:15:20.297]  
[17:15:28.393]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:15:28.393]  
[17:15:28.393]  <debugvars>
[17:15:28.394]    // Pre-defined
[17:15:28.394]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:15:28.394]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:15:28.395]    __dp=0x00000000
[17:15:28.395]    __ap=0x00000000
[17:15:28.395]    __traceout=0x00000000      (Trace Disabled)
[17:15:28.395]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:15:28.396]    __FlashAddr=0x00000000
[17:15:28.396]    __FlashLen=0x00000000
[17:15:28.396]    __FlashArg=0x00000000
[17:15:28.396]    __FlashOp=0x00000000
[17:15:28.397]    __Result=0x00000000
[17:15:28.397]    
[17:15:28.397]    // User-defined
[17:15:28.397]    DbgMCU_CR=0x00000007
[17:15:28.397]    DbgMCU_APB1_Fz=0x00000000
[17:15:28.397]    DbgMCU_APB2_Fz=0x00000000
[17:15:28.397]    DoOptionByteLoading=0x00000000
[17:15:28.398]  </debugvars>
[17:15:28.398]  
[17:15:28.398]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:15:28.398]    <block atomic="false" info="">
[17:15:28.399]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:15:28.399]        // -> [connectionFlash <= 0x00000001]
[17:15:28.399]      __var FLASH_BASE = 0x40022000 ;
[17:15:28.399]        // -> [FLASH_BASE <= 0x40022000]
[17:15:28.399]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:15:28.400]        // -> [FLASH_CR <= 0x40022004]
[17:15:28.400]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:15:28.400]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:15:28.400]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:15:28.400]        // -> [LOCK_BIT <= 0x00000001]
[17:15:28.400]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:15:28.400]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:15:28.400]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:15:28.400]        // -> [FLASH_KEYR <= 0x4002200C]
[17:15:28.402]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:15:28.402]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:15:28.402]      __var FLASH_KEY2 = 0x02030405 ;
[17:15:28.402]        // -> [FLASH_KEY2 <= 0x02030405]
[17:15:28.402]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:15:28.403]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:15:28.403]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:15:28.404]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:15:28.404]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:15:28.404]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:15:28.404]      __var FLASH_CR_Value = 0 ;
[17:15:28.405]        // -> [FLASH_CR_Value <= 0x00000000]
[17:15:28.405]      __var DoDebugPortStop = 1 ;
[17:15:28.405]        // -> [DoDebugPortStop <= 0x00000001]
[17:15:28.405]      __var DP_CTRL_STAT = 0x4 ;
[17:15:28.405]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:15:28.405]      __var DP_SELECT = 0x8 ;
[17:15:28.405]        // -> [DP_SELECT <= 0x00000008]
[17:15:28.406]    </block>
[17:15:28.406]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:15:28.406]      // if-block "connectionFlash && DoOptionByteLoading"
[17:15:28.406]        // =>  FALSE
[17:15:28.406]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:15:28.407]    </control>
[17:15:28.407]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:15:28.407]      // if-block "DoDebugPortStop"
[17:15:28.407]        // =>  TRUE
[17:15:28.408]      <block atomic="false" info="">
[17:15:28.408]        WriteDP(DP_SELECT, 0x00000000);
[17:15:28.408]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:15:28.408]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:15:28.408]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:15:28.408]      </block>
[17:15:28.410]      // end if-block "DoDebugPortStop"
[17:15:28.410]    </control>
[17:15:28.410]  </sequence>
[17:15:28.410]  
