#include "user_config.h"
#include "stm32_flash.h"

CONFIG config;

/// @brief 
/// @param type 0读取数据，1读取默认值数据（恢复出产设置）
void read_config(u8 type)
{
	EEPROM_ReadBuffer(EEPROM_BANK2_ADDR,(uint8_t *)&config,sizeof(CONFIG));
	
  if((config.flag != 0x55AA) || (type == 1))
  {
    rt_memset(&config,0,sizeof(CONFIG));
    config.net_param.ip[0][0] = 47;
    config.net_param.ip[0][1] = 108;
    config.net_param.ip[0][2] = 206;
    config.net_param.ip[0][3] = 215;
    config.net_param.port[0] = 16000;
    config.net_param.central_station_address = 1;
    config.net_param.upload_cycle[0] = 4 * 60;//4小时，用于RTC计算唤醒

    config.water_meter_info.baud_rate_id = BAUD_RATE_ID_9600;
    config.water_meter_info.data_bit_id = DATA_BIT_ID_8;
    config.water_meter_info.parity_id = PARITY_ID_NONE;
    config.water_meter_info.protocol_type = PROTOCOL_TYPE_DAOSHENG_WATER_METER;
    config.water_meter_info.stop_bit_id = STOP_BIT_ID_1;

    config.flag = 0x55AA;
    //stm32_flash_write(EEPROM_BANK2_ADDR,(uint8_t *)&config,sizeof(CONFIG));
		EEPROM_WriteBuffer(EEPROM_BANK2_ADDR,(uint8_t *)&config,sizeof(CONFIG));
  }
}
/**
 * 更新配置数据，写到falsh中
*/
void config_refresh(void)
{
	stm32_flash_write(EEPROM_BANK2_ADDR,(uint8_t *)&config,sizeof(CONFIG));
	stm32_flash_read(EEPROM_BANK2_ADDR,(uint8_t *)&config,sizeof(CONFIG));
}
