#include "pc_protocol.h"
#include "stm32_flash.h"

const uint8_t VersionArray[] = {0x30, 0x31, 0x30, 0x30}; // 版本号V1.0.0

extern CONFIG config; // 定义配置结构体变量

#define TXDATALEN 30
#define CMD_SET_SHUOGUANG 0x11              // 设置烁光平台
#define CMD_GET_SHUOGUANG2 0x12          
#define CMD_GET_SHUOGUANG3 0x13                      
#define CMD_SET_MANUFACTURER_AGREEMENT 0x15 // 设置厂家协议
#define CMD_START_UPGRADING 0X01            // 开始升级
#define CMD_DATA_TRANSFER  0x03              // 数据传输
#define CMD_UPGRADING_END  0x05              // 升级结束
#define CMD_ANSWER         0x06              // 期待回复
#define CMD_SET_ID         0X17              // 终端ID配置
                                             // 定义结构体用于存储常用的缓冲区和计数
typedef struct
{
    uint8_t cmd1;
    uint8_t cmd2;
} CommandPair;

// 定义命令对数组，便于管理和扩展
const CommandPair query_commands[] = {
    {0x02, 0x05}, // 查询系统版本
    {0x02, 0x02}, // 建立连接
    {0x02, 0x11}, // 读取烁光平台读取中心1IP
    {0x02, 0x12}, // 读取中心2ip
    {0x02, 0x13}, // 读取中心3ip
    {0x02, 0x15}, // 读取厂家协议
    {0x02, 0x16}, // 读取其他协议

};

uint8_t Txbuf[TXDATALEN];
extern uint16_t mbus_crc16(const uint8_t *data, uint16_t length);
// 帧头  长度高位   长度低位  命令码 数据域  crc低位 crc高位  帧尾

// OX7E   0X00     LEN       CMD    DATA    CRC_L   CRC_H  0XCE
/*===============================================================
函数名：pc_protocol_parsing(SERIAL serial);
功   能：上位机配置工具协议解析
参数:     SERIAL serial
输   出:  无
修   改:
 ===============================================================*/
extern CONFIG config;

///**
// * @brief 处理命令对
// * 
// * @param cmd1 命令码1
// * @param cmd2 命令码2
// * @param serial 串口句柄
// */
//static void handle_command_pair(uint8_t cmd1, uint8_t cmd2, SERIAL serial)
//{
//    // 根据命令对执行相应操作
//    switch (cmd2)
//    {
//    case 0x05: // 查询系统版本
//        // 填充版本号到Txbuf
//        memcpy(Txbuf + 4, VersionArray, sizeof(VersionArray));
//        break;
//    case 0x02: // 建立连接
//        // 填充连接响应数据
//        Txbuf[4] = 0x01;
//        break;
//    default:
//        break;
//    }
//}

/**
 * @brief 上位机配置工具协议解析
 * 
 * @param serial 串口句柄
 */
void pc_protocol_parsing(SERIAL serial)
{

    unsigned short CRC_Data;
    if (serial.rx_count >= 4)
    {
        for (size_t i = 0; i < sizeof(query_commands) / sizeof(query_commands[0]); i++)
        {
            if ((serial.rx_buffer[1] == query_commands[i].cmd1) && (serial.rx_buffer[2] == query_commands[i].cmd2))
            {
                switch (i)
                {
                case 0: // 查询系统版本
                    Txbuf[0] = 0x7E;
                    Txbuf[1] = 0x06;
                    Txbuf[2] = 0x85;            //
                    Txbuf[3] = 0x00;            // 正常00，不正常01
                    Txbuf[4] = VersionArray[0]; // 版本号高位
                    Txbuf[5] = VersionArray[1]; // 版本号低位
                    Txbuf[6] = VersionArray[2]; // 版本号低位
                    CRC_Data = mbus_crc16(Txbuf + 1, 6);
                    Txbuf[7] = CRC_Data & 0x00ff; // crc低位
                    Txbuf[8] = CRC_Data >> 8;     // crc高位
                    Txbuf[9] = 0xCE;
                    serial_485_send_dat(Txbuf, 10);
                    break;
                case 1: // 建立连接回7E 03 82 00 CE
                    Txbuf[0] = 0x7E;
                    Txbuf[1] = 0x03;
                    Txbuf[2] = 0x82; //
                    Txbuf[3] = 0x00; //
                                     
                    Txbuf[4] = 0xCE;
                    serial_485_send_dat(Txbuf, 5);
                    break;
                    /*
返回IP: *************, 端口: 8080, 上报周期: 1分钟

十六进制：7E 0F 11 00 00 0C A8 01 64 00 50 1F 00 01 00 XX XX CE
字节分解：
7E        - 帧头
0F        - 长度(15字节)
11        - 响应码(中心1配置)
00 00 0C A8 01 64 00 - IP地址(7字节格式)
50 1F 00  - 端口(8080, 3字节格式)
01 00     - 上报周期(1分钟)
XX XX     - CRC校验
CE        - 帧尾*/
                case 2:  // 读取烁光平台读取中心1IP
                    Txbuf[0] = 0x7E;                         // 帧起始标志
                    Txbuf[1] = 0x0F;                         // 数据长度：15字节
                    Txbuf[2] = 0x91;                         // 命令码

                    Txbuf[3] = 00; 							
                    Txbuf[4] = 00;   
                    Txbuf[5] = config.net_param.ip[0][0];     // IP地址第1字节
                    Txbuf[6] = config.net_param.ip[0][1];    // IP地址第2字节
                    Txbuf[7] = config.net_param.ip[0][2];     // IP地址第3字节            
                    Txbuf[8] = config.net_param.ip[0][3];     // IP地址第4字节
                    Txbuf[9] = 00;
								
                    Txbuf[10] = config.net_param.port[0]& 0x00ff;
                    Txbuf[11] = config.net_param.port[0]>> 8;
                    Txbuf[12] = 0;								
                    Txbuf[13] = config.net_param.upload_cycle[0]& 0x00ff;
                    Txbuf[14] = config.net_param.upload_cycle[0]>> 8;
                    
                                       // 保留字段8
                    CRC_Data = mbus_crc16(Txbuf + 1, 14); // 计算CRC校验（从长度字段开始）
                    Txbuf[15] = CRC_Data >> 8;                // CRC校验高字节
                    Txbuf[16] = CRC_Data & 0x00ff;                // CRC校验低字节
					Txbuf[17] = 0xCE; // 帧结束标志
                    serial_485_send_dat(Txbuf, 18); // 发送完整数据帧（22字节）
                    break;
                case 3: // 读取中心2ip
                    Txbuf[0] = 0x7E;                         // 帧起始标志
                    Txbuf[1] = 0x0F;                         // 数据长度：15字节
                    Txbuf[2] = 0x92;                         // 命令码

                    Txbuf[3] = 00; 							
                    Txbuf[4] = 00;   
                    Txbuf[5] = config.net_param.ip[1][0];     // IP地址第1字节
                    Txbuf[6] = config.net_param.ip[1][1];    // IP地址第2字节
                    Txbuf[7] = config.net_param.ip[1][2];     // IP地址第3字节            
                    Txbuf[8] = config.net_param.ip[1][3];     // IP地址第4字节
                    Txbuf[9] = 00;
								
                    Txbuf[10] = config.net_param.port[1]& 0x00ff;
                    Txbuf[11] = config.net_param.port[1]>> 8;
                    Txbuf[12] = 0;								
                    Txbuf[13] = config.net_param.upload_cycle[0]& 0x00ff;
                    Txbuf[14] = config.net_param.upload_cycle[0]>> 8;
                    
                                       // 保留字段8
                    CRC_Data = mbus_crc16(Txbuf + 1, 14); // 计算CRC校验（从长度字段开始）
                    Txbuf[15] = CRC_Data >> 8;                // CRC校验高字节
                    Txbuf[16] = CRC_Data & 0x00ff;                // CRC校验低字节
					Txbuf[17] = 0xCE; // 帧结束标志
                    serial_485_send_dat(Txbuf, 18); // 发送完整数据帧（22字节）
                    break;
                case 4: // 读取中心3ip
                     Txbuf[0] = 0x7E;                         // 帧起始标志
                    Txbuf[1] = 0x0F;                             // 数据长度：15字节
                    Txbuf[2] = 0x93;                             // 命令码                  
                    Txbuf[3] = 00; 							
                    Txbuf[4] = 00;   
                    Txbuf[5] = config.net_param.ip[2][0];     // IP地址第1字节
                    Txbuf[6] = config.net_param.ip[2][1];    // IP地址第2字节
                    Txbuf[7] = config.net_param.ip[2][2];     // IP地址第3字节            
                    Txbuf[8] = config.net_param.ip[2][3];     // IP地址第4字节
                    Txbuf[9] = 00;
								
                    Txbuf[10] = config.net_param.port[2]& 0x00ff;
                    Txbuf[11] = config.net_param.port[2]>> 8;
                    Txbuf[12] = 0;								
                    Txbuf[13] = config.net_param.upload_cycle[2]& 0x00ff;
                    Txbuf[14] = config.net_param.upload_cycle[2]>> 8;
                    CRC_Data = mbus_crc16(Txbuf + 1, 14); // 计算CRC校验（从长度字段开始）
                    Txbuf[15] = CRC_Data >> 8;                  // CRC校验高字节            
                    Txbuf[16] = CRC_Data & 0x00ff;                  // CRC校验低字节
                    Txbuf[17] = 0xCE; // 帧结束标志
                    serial_485_send_dat(Txbuf, 18); // 发送完整数据帧（22字节）     
                   break;
                case 5: // 读取厂家协议
                    Txbuf[0] = 0x7E;
                    Txbuf[1] = 0x0f;
                    Txbuf[2] = 0x95; 
                    Txbuf[3] = config.water_meter_info.cycle& 0x000000ff;
                    Txbuf[4] = config.water_meter_info.cycle& 0x0000ff00>>8;
                    Txbuf[5] = config.water_meter_info.cycle& 0x0000ff0000>>16; 
                    Txbuf[6] = config.water_meter_info.protocol_type;
                    Txbuf[7] = config.water_meter_info.baud_rate_id;
                    Txbuf[8] = config.water_meter_info.data_bit_id;    
                    Txbuf[9] = config.water_meter_info.stop_bit_id; 
                    Txbuf[10] = config.water_meter_info.parity_id; 
                    Txbuf[11] = 0x00;   
                    Txbuf[12] = 0x00;
                    Txbuf[13] = 0x00;
                    Txbuf[14] = 0x00;
                    CRC_Data = mbus_crc16(Txbuf + 1, 14);
                    Txbuf[15] = CRC_Data >> 8;                    
                    Txbuf[16] = CRC_Data & 0x00ff;
                    Txbuf[17] = 0xCE;
                    serial_485_send_dat(Txbuf, 18); // 发送完整数据帧（22字节）
                    break;
                case 6: // 读取终端ID
                     Txbuf[0] = 0x7E;                         
                     Txbuf[1] = 0x09;   
                     Txbuf[2] = 0x97;            
                     Txbuf[3] = config.Terminal_ID[0]; 
                     Txbuf[4] = config.Terminal_ID[1];   
                     Txbuf[5] = config.Terminal_ID[2];      
                     Txbuf[6] = config.Terminal_ID[3];   
                     Txbuf[7] = config.Terminal_ID[4];   
                     CRC_Data= mbus_crc16(Txbuf + 1, 7); // 计算CRC校验（从长度字段开始）                                   
                     Txbuf[8] = CRC_Data >> 8;                

                     Txbuf[9] = CRC_Data & 0x00ff;                

                     Txbuf[10] = 0xCE; 

                     serial_485_send_dat(Txbuf, 11); // 发送完整数据帧（11字节）
                    break;
                }
               
            }
        }
    }
    else
    {
        if (serial.rx_count > 4) // 确保缓冲区有足够数据
        {
             if (serial.rx_count ==6)//升级结束
            {
                if(serial.rx_buffer[2]==0x04&&serial.rx_buffer[3]==0x05)
                {
                    Txbuf[0] = 0x7E;
                    Txbuf[1] = 0x05;                       
                    Txbuf[2] = 0x06;                        
                    Txbuf[3] = 0x00;                    
                    CRC_Data = mbus_crc16(Txbuf + 1, 3); 
                    Txbuf[4] = CRC_Data & 0x00ff;
                    Txbuf[5] = CRC_Data >> 8;
                    Txbuf[6] = 0xCE;                       
                    serial_485_send_dat(Txbuf, 7); // 发送完整数据帧（7字节）
                }
                
            }
            else 
            {
            switch (serial.rx_buffer[3])
            {                               

            case CMD_SET_SHUOGUANG: // 设置烁光平台
                config.net_param.ip[0][0] = serial.rx_buffer[6]; // IP地址第1字节
                config.net_param.ip[0][1] = serial.rx_buffer[7];    // IP地址第2字节
                config.net_param.ip[0][2] = serial.rx_buffer[8];     // IP地址第4字节   
                config.net_param.ip[0][3] = serial.rx_buffer[9];     // IP地址第4字节
                config.net_param.port[0] = serial.rx_buffer[11] << 8 | serial.rx_buffer[12]; // 端口号    
                config.net_param.upload_cycle[0] = serial.rx_buffer[14] << 8 | serial.rx_buffer[15]; // 上传周期
                EEPROM_WriteBuffer(EEPROM_BANK1_ADDR,(uint8_t *)&config,sizeof(CONFIG));
                 
                Txbuf[0] = 0x7E;
                Txbuf[1] = 0x05;    
                Txbuf[2] = 0x83;    
                Txbuf[3] = 0x00;     
                CRC_Data = mbus_crc16(Txbuf + 1, 4);
                Txbuf[4] = CRC_Data & 0x00ff;   
                Txbuf[5] = CRC_Data >> 8;
                Txbuf[6] = 0xCE;        
                serial_485_send_dat(Txbuf, 7); // 发送完整数据帧（7字节）
                break;
            case CMD_GET_SHUOGUANG2: // 设置网络参数
                config.net_param.ip[1][0] = serial.rx_buffer[6]; 
                config.net_param.ip[1][1] = serial.rx_buffer[7];
                config.net_param.ip[1][2] = serial.rx_buffer[8];
                config.net_param.ip[1][3] = serial.rx_buffer[9];
                config.net_param.port[1] = serial.rx_buffer[11] << 8 | serial.rx_buffer[12];
                config.net_param.upload_cycle[0] = serial.rx_buffer[14] << 8 | serial.rx_buffer[15];    
                EEPROM_WriteBuffer(EEPROM_BANK1_ADDR,(uint8_t *)&config,sizeof(CONFIG));
                
                Txbuf[0] = 0x7E;
                Txbuf[1] = 0x05;                        
                Txbuf[2] = 0x92;                        
                Txbuf[3] = 0x00;  
                CRC_Data = mbus_crc16(Txbuf + 1, 4);
                Txbuf[4] = CRC_Data & 0x00ff;   
                Txbuf[5] = CRC_Data >> 8;
                Txbuf[6] = 0xCE;        
                break;


              case CMD_GET_SHUOGUANG3: // 设置网络参数
                config.net_param.ip[1][0] = serial.rx_buffer[6]; 
                config.net_param.ip[1][1] = serial.rx_buffer[7];
                config.net_param.ip[1][2] = serial.rx_buffer[8];
                config.net_param.ip[1][3] = serial.rx_buffer[9];
                config.net_param.port[1] = serial.rx_buffer[11] << 8 | serial.rx_buffer[12];
                config.net_param.upload_cycle[0] = serial.rx_buffer[14] << 8 | serial.rx_buffer[15];    
                EEPROM_WriteBuffer(EEPROM_BANK1_ADDR,(uint8_t *)&config,sizeof(CONFIG));
                
                Txbuf[0] = 0x7E;
                Txbuf[1] = 0x05;                        
                Txbuf[2] = 0x93;                        
                Txbuf[3] = 0x00;    
                CRC_Data = mbus_crc16(Txbuf + 1, 4);
                Txbuf[4] = CRC_Data & 0x00ff;   
                Txbuf[5] = CRC_Data >> 8;
                Txbuf[6] = 0xCE;        
                break;

            
            case CMD_SET_MANUFACTURER_AGREEMENT: // 设置厂家协议
            	    
            config.water_meter_info.cycle = serial.rx_buffer[4] << 16 | serial.rx_buffer[5] << 8 | serial.rx_buffer[6];
//            config.water_meter_info.protocol_type = serial.rx_buffer[7];
//            config.water_meter_info.baud_rate_id = serial.rx_buffer[8];
//            config.water_meter_info.data_bit_id = serial.rx_buffer[9];
//            config.water_meter_info.stop_bit_id = serial.rx_buffer[10];
//            config.water_meter_info.parity_id = serial.rx_buffer[11];
            EEPROM_WriteBuffer(EEPROM_BANK2_ADDR,(uint8_t *)&config,sizeof(CONFIG));

                Txbuf[0] = 0x7E;
                Txbuf[1] = 0x05;                        
                Txbuf[2] = 0x83;  
                Txbuf[3] = 0x00;                        
                CRC_Data = mbus_crc16(Txbuf + 1, 4);
                Txbuf[4] = CRC_Data & 0x00ff;                   
                Txbuf[5] = CRC_Data >> 8;
                Txbuf[6] = 0xCE;        
                serial_485_send_dat(Txbuf, 7); // 发送完整数据帧（7字节）   
                break;
            case CMD_SET_ID: // 设置ID
                config.Terminal_ID[0] = serial.rx_buffer[4];
                config.Terminal_ID[1] = serial.rx_buffer[5];
                config.Terminal_ID[2] = serial.rx_buffer[6];
                config.Terminal_ID[3] = serial.rx_buffer[7];
                config.Terminal_ID[4] = serial.rx_buffer[8];

                Txbuf[0] = 0x7E;
                Txbuf[1] = 0x05;
                Txbuf[2] = 0x83;
                Txbuf[3] = 0x00;
                CRC_Data = mbus_crc16(Txbuf + 1, 4);
                Txbuf[4] = CRC_Data & 0x00ff;
                Txbuf[5] = CRC_Data >> 8;
                Txbuf[6] = 0xCE;
                serial_485_send_dat(Txbuf, 7); // 发送完整数据帧（7字节）

                break;
            case CMD_START_UPGRADING: // 开始升级

                break;
            case CMD_DATA_TRANSFER: // 数据传输
                break;
            case CMD_UPGRADING_END: // 升级结束
                break;
            
            }
            serial.rx_count = 0;
        }
			}
    }
}
