#include "system.h"

void system_clock_init(void)
{
	RCC_ClkInitTypeDef clk_init;
	RCC_OscInitTypeDef osc_init;

	/* Enable MSI Oscillator */
	osc_init.OscillatorType = RCC_OSCILLATORTYPE_MSI;
	osc_init.MSIState = RCC_MSI_ON;
	osc_init.MSIClockRange = RCC_MSIRANGE_5;
	osc_init.MSICalibrationValue = 0;
	osc_init.PLL.PLLState = RCC_PLL_NONE;
	if(HAL_RCC_OscConfig(&osc_init) != HAL_OK)
	{
		while(1);
	}

	/* Enable ADC Clock */
	__HAL_RCC_ADC1_CLK_ENABLE();
	/* Set ADC clock to HSI16 (no prescaler) */
	RCC->CFGR &= ~(0x3 << 28);  // Clear ADC prescaler bits
	RCC->CFGR |= (0x0 << 28);   // Set ADC prescaler to DIV1
	/* Select MSI as system clock source and configure the HCLK, PCLK1 and PCLK2 clocks dividers */
	clk_init.ClockType = (RCC_CLOCKTYPE_SYSCLK | RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_PCLK1 | RCC_CLOCKTYPE_PCLK2);
	clk_init.SYSCLKSource = RCC_SYSCLKSOURCE_MSI;
	clk_init.AHBCLKDivider = RCC_SYSCLK_DIV1;
	clk_init.APB1CLKDivider = RCC_HCLK_DIV1;
	clk_init.APB2CLKDivider = RCC_HCLK_DIV1;
	if(HAL_RCC_ClockConfig(&clk_init, FLASH_LATENCY_0) != HAL_OK)
	{
		while(1);
	}
	/* The voltage scaling allows optimizing the power consumption when the device is 
     clocked below the maximum system frequency, to update the voltage scaling value 
     regarding system frequency refer to product datasheet.  */
	__HAL_RCC_PWR_CLK_ENABLE();
	__HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);
}

