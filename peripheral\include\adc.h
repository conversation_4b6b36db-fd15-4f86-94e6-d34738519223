#ifndef __ADC_H__
#define __ADC_H__

#include "stm32l0xx_hal.h"

// 电源监控结构体
typedef struct {
  uint16_t vrefint_cal;      // VREFINT校准值
  uint32_t vdda_mv;          // 计算后的电压值(mV)
  uint16_t vrefint_raw;      // 原始ADC读数
  uint8_t measure_flag;      // 测量标志
} PowerMonitor_t;

extern PowerMonitor_t pwr_mon;

// 获取VDDA电压值
uint32_t vdda_get_voltage(void);

// 初始化PA4 ADC检测
void ADC_Voltage_Init(void);

// 获取电源电压(mV)
float Get_Power_Voltage(void);


#endif /* __ADC_H__ */
