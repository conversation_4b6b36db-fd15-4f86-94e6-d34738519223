/**
 * @file protocol.h
 * @brief 水表采集器配置工具通信协议 - STM32设备端头文件
 * <AUTHOR> Assistant
 * @date 2025-08-26
 */

#ifndef __PROTOCOL_H__
#define __PROTOCOL_H__

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif



// 主要协议处理函数
/**
 * @brief 协议数据处理主函数
 * @param length 报文长度
 * @param data 报文数据指针
 * @return 0-成功，-1-失败
 */
int protocol_process(int length, char* data);

// 配置数据访问接口函数
/**
 * @brief 获取中心配置
 * @param center_num 中心编号（1-3）
 * @param config 配置结构指针
 * @return 0-成功，-1-失败
 */
int protocol_get_center_config(int center_num, center_config_t *config);

/**
 * @brief 设置中心配置
 * @param center_num 中心编号（1-3）
 * @param config 配置结构指针
 * @return 0-成功，-1-失败
 */
int protocol_set_center_config(int center_num, const center_config_t *config);

/**
 * @brief 获取厂家协议配置
 * @param config 配置结构指针
 * @return 0-成功，-1-失败
 */
int protocol_get_protocol_config(protocol_config_t *config);

/**
 * @brief 设置厂家协议配置
 * @param config 配置结构指针
 * @return 0-成功，-1-失败
 */
int protocol_set_protocol_config(const protocol_config_t *config);

/**
 * @brief 获取其他配置
 * @param config 配置结构指针
 * @return 0-成功，-1-失败
 */
int protocol_get_other_config(other_config_t *config);

/**
 * @brief 设置其他配置
 * @param config 配置结构指针
 * @return 0-成功，-1-失败
 */
int protocol_set_other_config(const other_config_t *config);

/**
 * @brief 获取终端ID
 * @param id 终端ID指针（5字节）
 * @return 0-成功，-1-失败
 */
int protocol_get_terminal_id(uint8_t *id);

/**
 * @brief 设置终端ID
 * @param id 终端ID指针（5字节）
 * @return 0-成功，-1-失败
 */
int protocol_set_terminal_id(const uint8_t *id);

/**
 * @brief 获取系统版本
 * @param version 版本字符串指针（3字节）
 * @return 0-成功，-1-失败
 */
int protocol_get_system_version(uint8_t *version);

/**
 * @brief 设置系统版本
 * @param version 版本字符串指针（3字节）
 * @return 0-成功，-1-失败
 */
int protocol_set_system_version(const uint8_t *version);

// 外部函数声明（需要用户实现）
/**
 * @brief UART发送数据函数（用户需要实现）
 * @param data 数据指针
 * @param length 数据长度
 */
extern void uart_send_data(uint8_t *data, int length);

#ifdef __cplusplus
}
#endif

#endif /* __PROTOCOL_H__ */
