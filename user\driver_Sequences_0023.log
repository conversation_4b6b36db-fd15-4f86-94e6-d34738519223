/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : D:\2025work\STM32L072PRO\source-application\user\driver_Sequences_0023.log
 *  Created     : 19:18:13 (20/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : D:\2025work\STM32L072PRO\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[19:18:13.990]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[19:18:13.990]  
[19:18:14.011]  <debugvars>
[19:18:14.035]    // Pre-defined
[19:18:14.053]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:18:14.076]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:18:14.076]    __dp=0x00000000
[19:18:14.078]    __ap=0x00000000
[19:18:14.078]    __traceout=0x00000000      (Trace Disabled)
[19:18:14.078]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:18:14.078]    __FlashAddr=0x00000000
[19:18:14.078]    __FlashLen=0x00000000
[19:18:14.078]    __FlashArg=0x00000000
[19:18:14.079]    __FlashOp=0x00000000
[19:18:14.080]    __Result=0x00000000
[19:18:14.080]    
[19:18:14.080]    // User-defined
[19:18:14.080]    DbgMCU_CR=0x00000007
[19:18:14.081]    DbgMCU_APB1_Fz=0x00000000
[19:18:14.081]    DbgMCU_APB2_Fz=0x00000000
[19:18:14.081]    DoOptionByteLoading=0x00000000
[19:18:14.081]  </debugvars>
[19:18:14.081]  
[19:18:14.082]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[19:18:14.082]    <block atomic="false" info="">
[19:18:14.083]      Sequence("CheckID");
[19:18:14.083]        <sequence name="CheckID" Pname="" disable="false" info="">
[19:18:14.083]          <block atomic="false" info="">
[19:18:14.083]            __var pidr1 = 0;
[19:18:14.084]              // -> [pidr1 <= 0x00000000]
[19:18:14.084]            __var pidr2 = 0;
[19:18:14.084]              // -> [pidr2 <= 0x00000000]
[19:18:14.084]            __var jep106id = 0;
[19:18:14.084]              // -> [jep106id <= 0x00000000]
[19:18:14.084]            __var ROMTableBase = 0;
[19:18:14.085]              // -> [ROMTableBase <= 0x00000000]
[19:18:14.085]            __ap = 0;      // AHB-AP
[19:18:14.085]              // -> [__ap <= 0x00000000]
[19:18:14.085]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[19:18:14.086]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[19:18:14.086]              // -> [ROMTableBase <= 0xF0000000]
[19:18:14.086]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[19:18:14.087]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[19:18:14.088]              // -> [pidr1 <= 0x00000004]
[19:18:14.088]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[19:18:14.089]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[19:18:14.089]              // -> [pidr2 <= 0x0000000A]
[19:18:14.089]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[19:18:14.089]              // -> [jep106id <= 0x00000020]
[19:18:14.090]          </block>
[19:18:14.090]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[19:18:14.091]            // if-block "jep106id != 0x20"
[19:18:14.091]              // =>  FALSE
[19:18:14.091]            // skip if-block "jep106id != 0x20"
[19:18:14.091]          </control>
[19:18:14.091]        </sequence>
[19:18:14.092]    </block>
[19:18:14.092]  </sequence>
[19:18:14.092]  
[19:18:14.104]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[19:18:14.104]  
[19:18:14.104]  <debugvars>
[19:18:14.104]    // Pre-defined
[19:18:14.104]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:18:14.105]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:18:14.105]    __dp=0x00000000
[19:18:14.105]    __ap=0x00000000
[19:18:14.106]    __traceout=0x00000000      (Trace Disabled)
[19:18:14.106]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:18:14.106]    __FlashAddr=0x00000000
[19:18:14.106]    __FlashLen=0x00000000
[19:18:14.106]    __FlashArg=0x00000000
[19:18:14.106]    __FlashOp=0x00000000
[19:18:14.106]    __Result=0x00000000
[19:18:14.106]    
[19:18:14.106]    // User-defined
[19:18:14.108]    DbgMCU_CR=0x00000007
[19:18:14.108]    DbgMCU_APB1_Fz=0x00000000
[19:18:14.108]    DbgMCU_APB2_Fz=0x00000000
[19:18:14.108]    DoOptionByteLoading=0x00000000
[19:18:14.108]  </debugvars>
[19:18:14.109]  
[19:18:14.109]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[19:18:14.109]    <block atomic="false" info="">
[19:18:14.109]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[19:18:14.110]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[19:18:14.110]    </block>
[19:18:14.111]    <block atomic="false" info="DbgMCU registers">
[19:18:14.111]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[19:18:14.112]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[19:18:14.113]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[19:18:14.113]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[19:18:14.114]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[19:18:14.114]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[19:18:14.116]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[19:18:14.116]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[19:18:14.117]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[19:18:14.117]    </block>
[19:18:14.117]  </sequence>
[19:18:14.118]  
[19:18:22.248]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[19:18:22.248]  
[19:18:22.248]  <debugvars>
[19:18:22.249]    // Pre-defined
[19:18:22.249]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:18:22.249]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:18:22.249]    __dp=0x00000000
[19:18:22.249]    __ap=0x00000000
[19:18:22.249]    __traceout=0x00000000      (Trace Disabled)
[19:18:22.251]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:18:22.251]    __FlashAddr=0x00000000
[19:18:22.251]    __FlashLen=0x00000000
[19:18:22.251]    __FlashArg=0x00000000
[19:18:22.251]    __FlashOp=0x00000000
[19:18:22.252]    __Result=0x00000000
[19:18:22.252]    
[19:18:22.252]    // User-defined
[19:18:22.252]    DbgMCU_CR=0x00000007
[19:18:22.252]    DbgMCU_APB1_Fz=0x00000000
[19:18:22.252]    DbgMCU_APB2_Fz=0x00000000
[19:18:22.253]    DoOptionByteLoading=0x00000000
[19:18:22.253]  </debugvars>
[19:18:22.253]  
[19:18:22.253]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[19:18:22.253]    <block atomic="false" info="">
[19:18:22.254]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[19:18:22.254]        // -> [connectionFlash <= 0x00000001]
[19:18:22.254]      __var FLASH_BASE = 0x40022000 ;
[19:18:22.254]        // -> [FLASH_BASE <= 0x40022000]
[19:18:22.254]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[19:18:22.255]        // -> [FLASH_CR <= 0x40022004]
[19:18:22.255]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[19:18:22.255]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[19:18:22.255]      __var LOCK_BIT = ( 1 << 0 ) ;
[19:18:22.255]        // -> [LOCK_BIT <= 0x00000001]
[19:18:22.256]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[19:18:22.256]        // -> [OPTLOCK_BIT <= 0x00000004]
[19:18:22.256]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[19:18:22.256]        // -> [FLASH_KEYR <= 0x4002200C]
[19:18:22.256]      __var FLASH_KEY1 = 0x89ABCDEF ;
[19:18:22.257]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[19:18:22.257]      __var FLASH_KEY2 = 0x02030405 ;
[19:18:22.257]        // -> [FLASH_KEY2 <= 0x02030405]
[19:18:22.257]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[19:18:22.257]        // -> [FLASH_OPTKEYR <= 0x40022014]
[19:18:22.258]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[19:18:22.258]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[19:18:22.258]      __var FLASH_OPTKEY2 = 0x24252627 ;
[19:18:22.258]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[19:18:22.258]      __var FLASH_CR_Value = 0 ;
[19:18:22.259]        // -> [FLASH_CR_Value <= 0x00000000]
[19:18:22.259]      __var DoDebugPortStop = 1 ;
[19:18:22.259]        // -> [DoDebugPortStop <= 0x00000001]
[19:18:22.259]      __var DP_CTRL_STAT = 0x4 ;
[19:18:22.259]        // -> [DP_CTRL_STAT <= 0x00000004]
[19:18:22.260]      __var DP_SELECT = 0x8 ;
[19:18:22.260]        // -> [DP_SELECT <= 0x00000008]
[19:18:22.260]    </block>
[19:18:22.260]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[19:18:22.260]      // if-block "connectionFlash && DoOptionByteLoading"
[19:18:22.260]        // =>  FALSE
[19:18:22.260]      // skip if-block "connectionFlash && DoOptionByteLoading"
[19:18:22.260]    </control>
[19:18:22.261]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[19:18:22.261]      // if-block "DoDebugPortStop"
[19:18:22.262]        // =>  TRUE
[19:18:22.262]      <block atomic="false" info="">
[19:18:22.262]        WriteDP(DP_SELECT, 0x00000000);
[19:18:22.263]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[19:18:22.263]        WriteDP(DP_CTRL_STAT, 0x00000000);
[19:18:22.263]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[19:18:22.263]      </block>
[19:18:22.264]      // end if-block "DoDebugPortStop"
[19:18:22.264]    </control>
[19:18:22.264]  </sequence>
[19:18:22.264]  
[20:15:56.735]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[20:15:56.735]  
[20:15:56.753]  <debugvars>
[20:15:56.754]    // Pre-defined
[20:15:56.754]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:15:56.755]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:15:56.756]    __dp=0x00000000
[20:15:56.756]    __ap=0x00000000
[20:15:56.757]    __traceout=0x00000000      (Trace Disabled)
[20:15:56.757]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:15:56.757]    __FlashAddr=0x00000000
[20:15:56.758]    __FlashLen=0x00000000
[20:15:56.758]    __FlashArg=0x00000000
[20:15:56.759]    __FlashOp=0x00000000
[20:15:56.759]    __Result=0x00000000
[20:15:56.759]    
[20:15:56.759]    // User-defined
[20:15:56.760]    DbgMCU_CR=0x00000007
[20:15:56.760]    DbgMCU_APB1_Fz=0x00000000
[20:15:56.760]    DbgMCU_APB2_Fz=0x00000000
[20:15:56.760]    DoOptionByteLoading=0x00000000
[20:15:56.761]  </debugvars>
[20:15:56.762]  
[20:15:56.762]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[20:15:56.762]    <block atomic="false" info="">
[20:15:56.763]      Sequence("CheckID");
[20:15:56.763]        <sequence name="CheckID" Pname="" disable="false" info="">
[20:15:56.763]          <block atomic="false" info="">
[20:15:56.764]            __var pidr1 = 0;
[20:15:56.764]              // -> [pidr1 <= 0x00000000]
[20:15:56.764]            __var pidr2 = 0;
[20:15:56.765]              // -> [pidr2 <= 0x00000000]
[20:15:56.765]            __var jep106id = 0;
[20:15:56.765]              // -> [jep106id <= 0x00000000]
[20:15:56.765]            __var ROMTableBase = 0;
[20:15:56.765]              // -> [ROMTableBase <= 0x00000000]
[20:15:56.765]            __ap = 0;      // AHB-AP
[20:15:56.766]              // -> [__ap <= 0x00000000]
[20:15:56.767]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[20:15:56.767]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[20:15:56.767]              // -> [ROMTableBase <= 0xF0000000]
[20:15:56.768]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[20:15:56.770]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[20:15:56.770]              // -> [pidr1 <= 0x00000004]
[20:15:56.770]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[20:15:56.771]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[20:15:56.772]              // -> [pidr2 <= 0x0000000A]
[20:15:56.772]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[20:15:56.772]              // -> [jep106id <= 0x00000020]
[20:15:56.772]          </block>
[20:15:56.773]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[20:15:56.773]            // if-block "jep106id != 0x20"
[20:15:56.773]              // =>  FALSE
[20:15:56.774]            // skip if-block "jep106id != 0x20"
[20:15:56.774]          </control>
[20:15:56.774]        </sequence>
[20:15:56.774]    </block>
[20:15:56.775]  </sequence>
[20:15:56.775]  
[20:15:56.786]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[20:15:56.786]  
[20:15:56.799]  <debugvars>
[20:15:56.799]    // Pre-defined
[20:15:56.799]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:15:56.800]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:15:56.800]    __dp=0x00000000
[20:15:56.801]    __ap=0x00000000
[20:15:56.801]    __traceout=0x00000000      (Trace Disabled)
[20:15:56.802]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:15:56.802]    __FlashAddr=0x00000000
[20:15:56.802]    __FlashLen=0x00000000
[20:15:56.803]    __FlashArg=0x00000000
[20:15:56.804]    __FlashOp=0x00000000
[20:15:56.804]    __Result=0x00000000
[20:15:56.804]    
[20:15:56.804]    // User-defined
[20:15:56.805]    DbgMCU_CR=0x00000007
[20:15:56.805]    DbgMCU_APB1_Fz=0x00000000
[20:15:56.805]    DbgMCU_APB2_Fz=0x00000000
[20:15:56.805]    DoOptionByteLoading=0x00000000
[20:15:56.805]  </debugvars>
[20:15:56.807]  
[20:15:56.807]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[20:15:56.807]    <block atomic="false" info="">
[20:15:56.807]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[20:15:56.808]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[20:15:56.808]    </block>
[20:15:56.809]    <block atomic="false" info="DbgMCU registers">
[20:15:56.809]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[20:15:56.810]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[20:15:56.811]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[20:15:56.812]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[20:15:56.813]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[20:15:56.813]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[20:15:56.814]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[20:15:56.814]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[20:15:56.815]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[20:15:56.815]    </block>
[20:15:56.816]  </sequence>
[20:15:56.816]  
[20:16:05.291]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[20:16:05.291]  
[20:16:05.291]  <debugvars>
[20:16:05.292]    // Pre-defined
[20:16:05.293]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:16:05.293]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:16:05.293]    __dp=0x00000000
[20:16:05.294]    __ap=0x00000000
[20:16:05.294]    __traceout=0x00000000      (Trace Disabled)
[20:16:05.294]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:16:05.295]    __FlashAddr=0x00000000
[20:16:05.295]    __FlashLen=0x00000000
[20:16:05.295]    __FlashArg=0x00000000
[20:16:05.295]    __FlashOp=0x00000000
[20:16:05.296]    __Result=0x00000000
[20:16:05.296]    
[20:16:05.296]    // User-defined
[20:16:05.296]    DbgMCU_CR=0x00000007
[20:16:05.297]    DbgMCU_APB1_Fz=0x00000000
[20:16:05.297]    DbgMCU_APB2_Fz=0x00000000
[20:16:05.297]    DoOptionByteLoading=0x00000000
[20:16:05.298]  </debugvars>
[20:16:05.298]  
[20:16:05.298]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[20:16:05.298]    <block atomic="false" info="">
[20:16:05.298]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[20:16:05.299]        // -> [connectionFlash <= 0x00000001]
[20:16:05.299]      __var FLASH_BASE = 0x40022000 ;
[20:16:05.299]        // -> [FLASH_BASE <= 0x40022000]
[20:16:05.299]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[20:16:05.299]        // -> [FLASH_CR <= 0x40022004]
[20:16:05.300]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[20:16:05.300]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[20:16:05.300]      __var LOCK_BIT = ( 1 << 0 ) ;
[20:16:05.301]        // -> [LOCK_BIT <= 0x00000001]
[20:16:05.301]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[20:16:05.301]        // -> [OPTLOCK_BIT <= 0x00000004]
[20:16:05.301]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[20:16:05.301]        // -> [FLASH_KEYR <= 0x4002200C]
[20:16:05.302]      __var FLASH_KEY1 = 0x89ABCDEF ;
[20:16:05.302]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[20:16:05.302]      __var FLASH_KEY2 = 0x02030405 ;
[20:16:05.302]        // -> [FLASH_KEY2 <= 0x02030405]
[20:16:05.302]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[20:16:05.303]        // -> [FLASH_OPTKEYR <= 0x40022014]
[20:16:05.303]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[20:16:05.303]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[20:16:05.303]      __var FLASH_OPTKEY2 = 0x24252627 ;
[20:16:05.304]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[20:16:05.304]      __var FLASH_CR_Value = 0 ;
[20:16:05.304]        // -> [FLASH_CR_Value <= 0x00000000]
[20:16:05.304]      __var DoDebugPortStop = 1 ;
[20:16:05.305]        // -> [DoDebugPortStop <= 0x00000001]
[20:16:05.305]      __var DP_CTRL_STAT = 0x4 ;
[20:16:05.305]        // -> [DP_CTRL_STAT <= 0x00000004]
[20:16:05.305]      __var DP_SELECT = 0x8 ;
[20:16:05.305]        // -> [DP_SELECT <= 0x00000008]
[20:16:05.305]    </block>
[20:16:05.306]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[20:16:05.306]      // if-block "connectionFlash && DoOptionByteLoading"
[20:16:05.307]        // =>  FALSE
[20:16:05.307]      // skip if-block "connectionFlash && DoOptionByteLoading"
[20:16:05.308]    </control>
[20:16:05.308]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[20:16:05.308]      // if-block "DoDebugPortStop"
[20:16:05.308]        // =>  TRUE
[20:16:05.309]      <block atomic="false" info="">
[20:16:05.309]        WriteDP(DP_SELECT, 0x00000000);
[20:16:05.309]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[20:16:05.310]        WriteDP(DP_CTRL_STAT, 0x00000000);
[20:16:05.310]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[20:16:05.310]      </block>
[20:16:05.311]      // end if-block "DoDebugPortStop"
[20:16:05.311]    </control>
[20:16:05.311]  </sequence>
[20:16:05.311]  
[20:18:05.655]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[20:18:05.655]  
[20:18:05.655]  <debugvars>
[20:18:05.656]    // Pre-defined
[20:18:05.656]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:18:05.656]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:18:05.657]    __dp=0x00000000
[20:18:05.657]    __ap=0x00000000
[20:18:05.657]    __traceout=0x00000000      (Trace Disabled)
[20:18:05.657]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:18:05.657]    __FlashAddr=0x00000000
[20:18:05.658]    __FlashLen=0x00000000
[20:18:05.658]    __FlashArg=0x00000000
[20:18:05.658]    __FlashOp=0x00000000
[20:18:05.659]    __Result=0x00000000
[20:18:05.659]    
[20:18:05.659]    // User-defined
[20:18:05.659]    DbgMCU_CR=0x00000007
[20:18:05.659]    DbgMCU_APB1_Fz=0x00000000
[20:18:05.659]    DbgMCU_APB2_Fz=0x00000000
[20:18:05.659]    DoOptionByteLoading=0x00000000
[20:18:05.659]  </debugvars>
[20:18:05.660]  
[20:18:05.660]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[20:18:05.660]    <block atomic="false" info="">
[20:18:05.660]      Sequence("CheckID");
[20:18:05.661]        <sequence name="CheckID" Pname="" disable="false" info="">
[20:18:05.661]          <block atomic="false" info="">
[20:18:05.661]            __var pidr1 = 0;
[20:18:05.661]              // -> [pidr1 <= 0x00000000]
[20:18:05.661]            __var pidr2 = 0;
[20:18:05.662]              // -> [pidr2 <= 0x00000000]
[20:18:05.662]            __var jep106id = 0;
[20:18:05.662]              // -> [jep106id <= 0x00000000]
[20:18:05.662]            __var ROMTableBase = 0;
[20:18:05.662]              // -> [ROMTableBase <= 0x00000000]
[20:18:05.662]            __ap = 0;      // AHB-AP
[20:18:05.663]              // -> [__ap <= 0x00000000]
[20:18:05.663]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[20:18:05.663]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[20:18:05.664]              // -> [ROMTableBase <= 0xF0000000]
[20:18:05.664]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[20:18:05.665]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[20:18:05.665]              // -> [pidr1 <= 0x00000004]
[20:18:05.666]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[20:18:05.666]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[20:18:05.667]              // -> [pidr2 <= 0x0000000A]
[20:18:05.667]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[20:18:05.667]              // -> [jep106id <= 0x00000020]
[20:18:05.667]          </block>
[20:18:05.667]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[20:18:05.667]            // if-block "jep106id != 0x20"
[20:18:05.668]              // =>  FALSE
[20:18:05.668]            // skip if-block "jep106id != 0x20"
[20:18:05.668]          </control>
[20:18:05.668]        </sequence>
[20:18:05.668]    </block>
[20:18:05.668]  </sequence>
[20:18:05.669]  
[20:18:05.680]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[20:18:05.680]  
[20:18:05.680]  <debugvars>
[20:18:05.680]    // Pre-defined
[20:18:05.680]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:18:05.680]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:18:05.680]    __dp=0x00000000
[20:18:05.682]    __ap=0x00000000
[20:18:05.682]    __traceout=0x00000000      (Trace Disabled)
[20:18:05.682]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:18:05.683]    __FlashAddr=0x00000000
[20:18:05.683]    __FlashLen=0x00000000
[20:18:05.683]    __FlashArg=0x00000000
[20:18:05.683]    __FlashOp=0x00000000
[20:18:05.683]    __Result=0x00000000
[20:18:05.683]    
[20:18:05.683]    // User-defined
[20:18:05.683]    DbgMCU_CR=0x00000007
[20:18:05.683]    DbgMCU_APB1_Fz=0x00000000
[20:18:05.684]    DbgMCU_APB2_Fz=0x00000000
[20:18:05.684]    DoOptionByteLoading=0x00000000
[20:18:05.685]  </debugvars>
[20:18:05.685]  
[20:18:05.685]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[20:18:05.685]    <block atomic="false" info="">
[20:18:05.685]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[20:18:05.686]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[20:18:05.686]    </block>
[20:18:05.687]    <block atomic="false" info="DbgMCU registers">
[20:18:05.687]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[20:18:05.687]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[20:18:05.688]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[20:18:05.688]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[20:18:05.690]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[20:18:05.690]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[20:18:05.691]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[20:18:05.691]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[20:18:05.692]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[20:18:05.693]    </block>
[20:18:05.693]  </sequence>
[20:18:05.693]  
[20:18:13.674]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[20:18:13.674]  
[20:18:13.674]  <debugvars>
[20:18:13.674]    // Pre-defined
[20:18:13.674]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:18:13.674]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:18:13.675]    __dp=0x00000000
[20:18:13.675]    __ap=0x00000000
[20:18:13.675]    __traceout=0x00000000      (Trace Disabled)
[20:18:13.675]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:18:13.675]    __FlashAddr=0x00000000
[20:18:13.676]    __FlashLen=0x00000000
[20:18:13.676]    __FlashArg=0x00000000
[20:18:13.676]    __FlashOp=0x00000000
[20:18:13.676]    __Result=0x00000000
[20:18:13.676]    
[20:18:13.676]    // User-defined
[20:18:13.677]    DbgMCU_CR=0x00000007
[20:18:13.677]    DbgMCU_APB1_Fz=0x00000000
[20:18:13.677]    DbgMCU_APB2_Fz=0x00000000
[20:18:13.677]    DoOptionByteLoading=0x00000000
[20:18:13.677]  </debugvars>
[20:18:13.678]  
[20:18:13.678]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[20:18:13.678]    <block atomic="false" info="">
[20:18:13.678]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[20:18:13.678]        // -> [connectionFlash <= 0x00000001]
[20:18:13.678]      __var FLASH_BASE = 0x40022000 ;
[20:18:13.679]        // -> [FLASH_BASE <= 0x40022000]
[20:18:13.679]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[20:18:13.679]        // -> [FLASH_CR <= 0x40022004]
[20:18:13.679]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[20:18:13.679]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[20:18:13.679]      __var LOCK_BIT = ( 1 << 0 ) ;
[20:18:13.680]        // -> [LOCK_BIT <= 0x00000001]
[20:18:13.680]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[20:18:13.680]        // -> [OPTLOCK_BIT <= 0x00000004]
[20:18:13.680]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[20:18:13.680]        // -> [FLASH_KEYR <= 0x4002200C]
[20:18:13.680]      __var FLASH_KEY1 = 0x89ABCDEF ;
[20:18:13.681]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[20:18:13.681]      __var FLASH_KEY2 = 0x02030405 ;
[20:18:13.681]        // -> [FLASH_KEY2 <= 0x02030405]
[20:18:13.681]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[20:18:13.681]        // -> [FLASH_OPTKEYR <= 0x40022014]
[20:18:13.681]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[20:18:13.681]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[20:18:13.683]      __var FLASH_OPTKEY2 = 0x24252627 ;
[20:18:13.683]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[20:18:13.683]      __var FLASH_CR_Value = 0 ;
[20:18:13.683]        // -> [FLASH_CR_Value <= 0x00000000]
[20:18:13.684]      __var DoDebugPortStop = 1 ;
[20:18:13.684]        // -> [DoDebugPortStop <= 0x00000001]
[20:18:13.684]      __var DP_CTRL_STAT = 0x4 ;
[20:18:13.684]        // -> [DP_CTRL_STAT <= 0x00000004]
[20:18:13.684]      __var DP_SELECT = 0x8 ;
[20:18:13.685]        // -> [DP_SELECT <= 0x00000008]
[20:18:13.685]    </block>
[20:18:13.685]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[20:18:13.685]      // if-block "connectionFlash && DoOptionByteLoading"
[20:18:13.685]        // =>  FALSE
[20:18:13.686]      // skip if-block "connectionFlash && DoOptionByteLoading"
[20:18:13.686]    </control>
[20:18:13.686]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[20:18:13.686]      // if-block "DoDebugPortStop"
[20:18:13.686]        // =>  TRUE
[20:18:13.687]      <block atomic="false" info="">
[20:18:13.687]        WriteDP(DP_SELECT, 0x00000000);
[20:18:13.687]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[20:18:13.687]        WriteDP(DP_CTRL_STAT, 0x00000000);
[20:18:13.688]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[20:18:13.688]      </block>
[20:18:13.688]      // end if-block "DoDebugPortStop"
[20:18:13.688]    </control>
[20:18:13.689]  </sequence>
[20:18:13.689]  
[20:24:27.166]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[20:24:27.166]  
[20:24:27.166]  <debugvars>
[20:24:27.167]    // Pre-defined
[20:24:27.167]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:24:27.167]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:24:27.167]    __dp=0x00000000
[20:24:27.167]    __ap=0x00000000
[20:24:27.168]    __traceout=0x00000000      (Trace Disabled)
[20:24:27.169]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:24:27.169]    __FlashAddr=0x00000000
[20:24:27.169]    __FlashLen=0x00000000
[20:24:27.169]    __FlashArg=0x00000000
[20:24:27.169]    __FlashOp=0x00000000
[20:24:27.169]    __Result=0x00000000
[20:24:27.169]    
[20:24:27.169]    // User-defined
[20:24:27.170]    DbgMCU_CR=0x00000007
[20:24:27.170]    DbgMCU_APB1_Fz=0x00000000
[20:24:27.170]    DbgMCU_APB2_Fz=0x00000000
[20:24:27.170]    DoOptionByteLoading=0x00000000
[20:24:27.170]  </debugvars>
[20:24:27.170]  
[20:24:27.171]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[20:24:27.171]    <block atomic="false" info="">
[20:24:27.172]      Sequence("CheckID");
[20:24:27.172]        <sequence name="CheckID" Pname="" disable="false" info="">
[20:24:27.172]          <block atomic="false" info="">
[20:24:27.172]            __var pidr1 = 0;
[20:24:27.172]              // -> [pidr1 <= 0x00000000]
[20:24:27.173]            __var pidr2 = 0;
[20:24:27.173]              // -> [pidr2 <= 0x00000000]
[20:24:27.173]            __var jep106id = 0;
[20:24:27.173]              // -> [jep106id <= 0x00000000]
[20:24:27.173]            __var ROMTableBase = 0;
[20:24:27.173]              // -> [ROMTableBase <= 0x00000000]
[20:24:27.174]            __ap = 0;      // AHB-AP
[20:24:27.174]              // -> [__ap <= 0x00000000]
[20:24:27.174]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[20:24:27.175]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[20:24:27.175]              // -> [ROMTableBase <= 0xF0000000]
[20:24:27.175]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[20:24:27.176]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[20:24:27.177]              // -> [pidr1 <= 0x00000004]
[20:24:27.177]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[20:24:27.177]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[20:24:27.179]              // -> [pidr2 <= 0x0000000A]
[20:24:27.179]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[20:24:27.179]              // -> [jep106id <= 0x00000020]
[20:24:27.179]          </block>
[20:24:27.179]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[20:24:27.179]            // if-block "jep106id != 0x20"
[20:24:27.180]              // =>  FALSE
[20:24:27.181]            // skip if-block "jep106id != 0x20"
[20:24:27.181]          </control>
[20:24:27.181]        </sequence>
[20:24:27.181]    </block>
[20:24:27.181]  </sequence>
[20:24:27.181]  
[20:24:27.193]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[20:24:27.193]  
[20:24:27.217]  <debugvars>
[20:24:27.219]    // Pre-defined
[20:24:27.219]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:24:27.220]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:24:27.220]    __dp=0x00000000
[20:24:27.220]    __ap=0x00000000
[20:24:27.221]    __traceout=0x00000000      (Trace Disabled)
[20:24:27.221]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:24:27.222]    __FlashAddr=0x00000000
[20:24:27.222]    __FlashLen=0x00000000
[20:24:27.222]    __FlashArg=0x00000000
[20:24:27.222]    __FlashOp=0x00000000
[20:24:27.223]    __Result=0x00000000
[20:24:27.223]    
[20:24:27.223]    // User-defined
[20:24:27.224]    DbgMCU_CR=0x00000007
[20:24:27.224]    DbgMCU_APB1_Fz=0x00000000
[20:24:27.224]    DbgMCU_APB2_Fz=0x00000000
[20:24:27.224]    DoOptionByteLoading=0x00000000
[20:24:27.225]  </debugvars>
[20:24:27.225]  
[20:24:27.225]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[20:24:27.225]    <block atomic="false" info="">
[20:24:27.226]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[20:24:27.227]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[20:24:27.227]    </block>
[20:24:27.227]    <block atomic="false" info="DbgMCU registers">
[20:24:27.227]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[20:24:27.228]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[20:24:27.229]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[20:24:27.230]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[20:24:27.231]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[20:24:27.231]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[20:24:27.231]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[20:24:27.231]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[20:24:27.232]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[20:24:27.232]    </block>
[20:24:27.233]  </sequence>
[20:24:27.234]  
[20:24:35.334]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[20:24:35.334]  
[20:24:35.334]  <debugvars>
[20:24:35.335]    // Pre-defined
[20:24:35.335]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:24:35.335]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:24:35.335]    __dp=0x00000000
[20:24:35.336]    __ap=0x00000000
[20:24:35.336]    __traceout=0x00000000      (Trace Disabled)
[20:24:35.336]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:24:35.337]    __FlashAddr=0x00000000
[20:24:35.337]    __FlashLen=0x00000000
[20:24:35.337]    __FlashArg=0x00000000
[20:24:35.337]    __FlashOp=0x00000000
[20:24:35.338]    __Result=0x00000000
[20:24:35.338]    
[20:24:35.338]    // User-defined
[20:24:35.338]    DbgMCU_CR=0x00000007
[20:24:35.338]    DbgMCU_APB1_Fz=0x00000000
[20:24:35.338]    DbgMCU_APB2_Fz=0x00000000
[20:24:35.339]    DoOptionByteLoading=0x00000000
[20:24:35.339]  </debugvars>
[20:24:35.339]  
[20:24:35.339]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[20:24:35.339]    <block atomic="false" info="">
[20:24:35.339]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[20:24:35.339]        // -> [connectionFlash <= 0x00000001]
[20:24:35.340]      __var FLASH_BASE = 0x40022000 ;
[20:24:35.340]        // -> [FLASH_BASE <= 0x40022000]
[20:24:35.340]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[20:24:35.340]        // -> [FLASH_CR <= 0x40022004]
[20:24:35.340]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[20:24:35.341]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[20:24:35.341]      __var LOCK_BIT = ( 1 << 0 ) ;
[20:24:35.341]        // -> [LOCK_BIT <= 0x00000001]
[20:24:35.341]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[20:24:35.341]        // -> [OPTLOCK_BIT <= 0x00000004]
[20:24:35.341]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[20:24:35.342]        // -> [FLASH_KEYR <= 0x4002200C]
[20:24:35.342]      __var FLASH_KEY1 = 0x89ABCDEF ;
[20:24:35.342]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[20:24:35.342]      __var FLASH_KEY2 = 0x02030405 ;
[20:24:35.342]        // -> [FLASH_KEY2 <= 0x02030405]
[20:24:35.343]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[20:24:35.343]        // -> [FLASH_OPTKEYR <= 0x40022014]
[20:24:35.343]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[20:24:35.343]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[20:24:35.343]      __var FLASH_OPTKEY2 = 0x24252627 ;
[20:24:35.344]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[20:24:35.344]      __var FLASH_CR_Value = 0 ;
[20:24:35.344]        // -> [FLASH_CR_Value <= 0x00000000]
[20:24:35.344]      __var DoDebugPortStop = 1 ;
[20:24:35.344]        // -> [DoDebugPortStop <= 0x00000001]
[20:24:35.345]      __var DP_CTRL_STAT = 0x4 ;
[20:24:35.345]        // -> [DP_CTRL_STAT <= 0x00000004]
[20:24:35.345]      __var DP_SELECT = 0x8 ;
[20:24:35.345]        // -> [DP_SELECT <= 0x00000008]
[20:24:35.345]    </block>
[20:24:35.347]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[20:24:35.347]      // if-block "connectionFlash && DoOptionByteLoading"
[20:24:35.347]        // =>  FALSE
[20:24:35.347]      // skip if-block "connectionFlash && DoOptionByteLoading"
[20:24:35.347]    </control>
[20:24:35.347]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[20:24:35.347]      // if-block "DoDebugPortStop"
[20:24:35.347]        // =>  TRUE
[20:24:35.348]      <block atomic="false" info="">
[20:24:35.348]        WriteDP(DP_SELECT, 0x00000000);
[20:24:35.348]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[20:24:35.348]        WriteDP(DP_CTRL_STAT, 0x00000000);
[20:24:35.349]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[20:24:35.349]      </block>
[20:24:35.349]      // end if-block "DoDebugPortStop"
[20:24:35.349]    </control>
[20:24:35.349]  </sequence>
[20:24:35.350]  
[20:26:40.160]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[20:26:40.160]  
[20:26:40.160]  <debugvars>
[20:26:40.160]    // Pre-defined
[20:26:40.160]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:26:40.162]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:26:40.162]    __dp=0x00000000
[20:26:40.162]    __ap=0x00000000
[20:26:40.162]    __traceout=0x00000000      (Trace Disabled)
[20:26:40.162]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:26:40.162]    __FlashAddr=0x00000000
[20:26:40.162]    __FlashLen=0x00000000
[20:26:40.162]    __FlashArg=0x00000000
[20:26:40.162]    __FlashOp=0x00000000
[20:26:40.162]    __Result=0x00000000
[20:26:40.164]    
[20:26:40.164]    // User-defined
[20:26:40.164]    DbgMCU_CR=0x00000007
[20:26:40.164]    DbgMCU_APB1_Fz=0x00000000
[20:26:40.164]    DbgMCU_APB2_Fz=0x00000000
[20:26:40.164]    DoOptionByteLoading=0x00000000
[20:26:40.164]  </debugvars>
[20:26:40.164]  
[20:26:40.164]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[20:26:40.164]    <block atomic="false" info="">
[20:26:40.166]      Sequence("CheckID");
[20:26:40.166]        <sequence name="CheckID" Pname="" disable="false" info="">
[20:26:40.166]          <block atomic="false" info="">
[20:26:40.166]            __var pidr1 = 0;
[20:26:40.166]              // -> [pidr1 <= 0x00000000]
[20:26:40.166]            __var pidr2 = 0;
[20:26:40.166]              // -> [pidr2 <= 0x00000000]
[20:26:40.166]            __var jep106id = 0;
[20:26:40.166]              // -> [jep106id <= 0x00000000]
[20:26:40.166]            __var ROMTableBase = 0;
[20:26:40.168]              // -> [ROMTableBase <= 0x00000000]
[20:26:40.168]            __ap = 0;      // AHB-AP
[20:26:40.168]              // -> [__ap <= 0x00000000]
[20:26:40.168]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[20:26:40.168]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[20:26:40.168]              // -> [ROMTableBase <= 0xF0000000]
[20:26:40.168]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[20:26:40.171]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[20:26:40.172]              // -> [pidr1 <= 0x00000004]
[20:26:40.172]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[20:26:40.173]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[20:26:40.173]              // -> [pidr2 <= 0x0000000A]
[20:26:40.174]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[20:26:40.174]              // -> [jep106id <= 0x00000020]
[20:26:40.174]          </block>
[20:26:40.174]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[20:26:40.174]            // if-block "jep106id != 0x20"
[20:26:40.175]              // =>  FALSE
[20:26:40.175]            // skip if-block "jep106id != 0x20"
[20:26:40.175]          </control>
[20:26:40.175]        </sequence>
[20:26:40.175]    </block>
[20:26:40.176]  </sequence>
[20:26:40.176]  
[20:26:40.188]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[20:26:40.188]  
[20:26:40.207]  <debugvars>
[20:26:40.207]    // Pre-defined
[20:26:40.207]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:26:40.207]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:26:40.208]    __dp=0x00000000
[20:26:40.208]    __ap=0x00000000
[20:26:40.208]    __traceout=0x00000000      (Trace Disabled)
[20:26:40.209]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:26:40.209]    __FlashAddr=0x00000000
[20:26:40.209]    __FlashLen=0x00000000
[20:26:40.209]    __FlashArg=0x00000000
[20:26:40.209]    __FlashOp=0x00000000
[20:26:40.209]    __Result=0x00000000
[20:26:40.209]    
[20:26:40.209]    // User-defined
[20:26:40.210]    DbgMCU_CR=0x00000007
[20:26:40.210]    DbgMCU_APB1_Fz=0x00000000
[20:26:40.210]    DbgMCU_APB2_Fz=0x00000000
[20:26:40.210]    DoOptionByteLoading=0x00000000
[20:26:40.211]  </debugvars>
[20:26:40.211]  
[20:26:40.211]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[20:26:40.211]    <block atomic="false" info="">
[20:26:40.211]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[20:26:40.212]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[20:26:40.212]    </block>
[20:26:40.213]    <block atomic="false" info="DbgMCU registers">
[20:26:40.213]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[20:26:40.214]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[20:26:40.214]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[20:26:40.215]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[20:26:40.216]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[20:26:40.216]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[20:26:40.217]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[20:26:40.217]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[20:26:40.218]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[20:26:40.218]    </block>
[20:26:40.218]  </sequence>
[20:26:40.218]  
[20:26:48.216]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[20:26:48.216]  
[20:26:48.217]  <debugvars>
[20:26:48.217]    // Pre-defined
[20:26:48.217]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:26:48.218]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:26:48.218]    __dp=0x00000000
[20:26:48.218]    __ap=0x00000000
[20:26:48.218]    __traceout=0x00000000      (Trace Disabled)
[20:26:48.219]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:26:48.220]    __FlashAddr=0x00000000
[20:26:48.220]    __FlashLen=0x00000000
[20:26:48.220]    __FlashArg=0x00000000
[20:26:48.221]    __FlashOp=0x00000000
[20:26:48.221]    __Result=0x00000000
[20:26:48.221]    
[20:26:48.221]    // User-defined
[20:26:48.221]    DbgMCU_CR=0x00000007
[20:26:48.221]    DbgMCU_APB1_Fz=0x00000000
[20:26:48.222]    DbgMCU_APB2_Fz=0x00000000
[20:26:48.222]    DoOptionByteLoading=0x00000000
[20:26:48.223]  </debugvars>
[20:26:48.223]  
[20:26:48.223]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[20:26:48.224]    <block atomic="false" info="">
[20:26:48.224]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[20:26:48.224]        // -> [connectionFlash <= 0x00000001]
[20:26:48.224]      __var FLASH_BASE = 0x40022000 ;
[20:26:48.224]        // -> [FLASH_BASE <= 0x40022000]
[20:26:48.225]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[20:26:48.225]        // -> [FLASH_CR <= 0x40022004]
[20:26:48.225]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[20:26:48.225]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[20:26:48.225]      __var LOCK_BIT = ( 1 << 0 ) ;
[20:26:48.225]        // -> [LOCK_BIT <= 0x00000001]
[20:26:48.226]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[20:26:48.226]        // -> [OPTLOCK_BIT <= 0x00000004]
[20:26:48.226]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[20:26:48.226]        // -> [FLASH_KEYR <= 0x4002200C]
[20:26:48.226]      __var FLASH_KEY1 = 0x89ABCDEF ;
[20:26:48.226]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[20:26:48.226]      __var FLASH_KEY2 = 0x02030405 ;
[20:26:48.226]        // -> [FLASH_KEY2 <= 0x02030405]
[20:26:48.226]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[20:26:48.226]        // -> [FLASH_OPTKEYR <= 0x40022014]
[20:26:48.226]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[20:26:48.228]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[20:26:48.228]      __var FLASH_OPTKEY2 = 0x24252627 ;
[20:26:48.228]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[20:26:48.228]      __var FLASH_CR_Value = 0 ;
[20:26:48.228]        // -> [FLASH_CR_Value <= 0x00000000]
[20:26:48.229]      __var DoDebugPortStop = 1 ;
[20:26:48.229]        // -> [DoDebugPortStop <= 0x00000001]
[20:26:48.229]      __var DP_CTRL_STAT = 0x4 ;
[20:26:48.229]        // -> [DP_CTRL_STAT <= 0x00000004]
[20:26:48.230]      __var DP_SELECT = 0x8 ;
[20:26:48.230]        // -> [DP_SELECT <= 0x00000008]
[20:26:48.230]    </block>
[20:26:48.231]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[20:26:48.231]      // if-block "connectionFlash && DoOptionByteLoading"
[20:26:48.231]        // =>  FALSE
[20:26:48.232]      // skip if-block "connectionFlash && DoOptionByteLoading"
[20:26:48.232]    </control>
[20:26:48.232]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[20:26:48.232]      // if-block "DoDebugPortStop"
[20:26:48.232]        // =>  TRUE
[20:26:48.232]      <block atomic="false" info="">
[20:26:48.233]        WriteDP(DP_SELECT, 0x00000000);
[20:26:48.233]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[20:26:48.233]        WriteDP(DP_CTRL_STAT, 0x00000000);
[20:26:48.234]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[20:26:48.234]      </block>
[20:26:48.235]      // end if-block "DoDebugPortStop"
[20:26:48.235]    </control>
[20:26:48.235]  </sequence>
[20:26:48.235]  
[20:29:37.499]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[20:29:37.499]  
[20:29:37.500]  <debugvars>
[20:29:37.500]    // Pre-defined
[20:29:37.500]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:29:37.501]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:29:37.501]    __dp=0x00000000
[20:29:37.501]    __ap=0x00000000
[20:29:37.502]    __traceout=0x00000000      (Trace Disabled)
[20:29:37.502]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:29:37.502]    __FlashAddr=0x00000000
[20:29:37.503]    __FlashLen=0x00000000
[20:29:37.503]    __FlashArg=0x00000000
[20:29:37.503]    __FlashOp=0x00000000
[20:29:37.503]    __Result=0x00000000
[20:29:37.503]    
[20:29:37.503]    // User-defined
[20:29:37.504]    DbgMCU_CR=0x00000007
[20:29:37.504]    DbgMCU_APB1_Fz=0x00000000
[20:29:37.504]    DbgMCU_APB2_Fz=0x00000000
[20:29:37.504]    DoOptionByteLoading=0x00000000
[20:29:37.504]  </debugvars>
[20:29:37.504]  
[20:29:37.505]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[20:29:37.505]    <block atomic="false" info="">
[20:29:37.505]      Sequence("CheckID");
[20:29:37.505]        <sequence name="CheckID" Pname="" disable="false" info="">
[20:29:37.505]          <block atomic="false" info="">
[20:29:37.506]            __var pidr1 = 0;
[20:29:37.506]              // -> [pidr1 <= 0x00000000]
[20:29:37.506]            __var pidr2 = 0;
[20:29:37.506]              // -> [pidr2 <= 0x00000000]
[20:29:37.506]            __var jep106id = 0;
[20:29:37.507]              // -> [jep106id <= 0x00000000]
[20:29:37.507]            __var ROMTableBase = 0;
[20:29:37.507]              // -> [ROMTableBase <= 0x00000000]
[20:29:37.507]            __ap = 0;      // AHB-AP
[20:29:37.507]              // -> [__ap <= 0x00000000]
[20:29:37.507]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[20:29:37.507]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[20:29:37.507]              // -> [ROMTableBase <= 0xF0000000]
[20:29:37.508]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[20:29:37.510]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[20:29:37.510]              // -> [pidr1 <= 0x00000004]
[20:29:37.510]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[20:29:37.510]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[20:29:37.510]              // -> [pidr2 <= 0x0000000A]
[20:29:37.511]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[20:29:37.512]              // -> [jep106id <= 0x00000020]
[20:29:37.512]          </block>
[20:29:37.513]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[20:29:37.513]            // if-block "jep106id != 0x20"
[20:29:37.513]              // =>  FALSE
[20:29:37.513]            // skip if-block "jep106id != 0x20"
[20:29:37.513]          </control>
[20:29:37.514]        </sequence>
[20:29:37.514]    </block>
[20:29:37.514]  </sequence>
[20:29:37.514]  
[20:29:37.526]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[20:29:37.526]  
[20:29:37.526]  <debugvars>
[20:29:37.526]    // Pre-defined
[20:29:37.527]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:29:37.527]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:29:37.528]    __dp=0x00000000
[20:29:37.528]    __ap=0x00000000
[20:29:37.528]    __traceout=0x00000000      (Trace Disabled)
[20:29:37.528]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:29:37.529]    __FlashAddr=0x00000000
[20:29:37.529]    __FlashLen=0x00000000
[20:29:37.529]    __FlashArg=0x00000000
[20:29:37.530]    __FlashOp=0x00000000
[20:29:37.530]    __Result=0x00000000
[20:29:37.530]    
[20:29:37.530]    // User-defined
[20:29:37.531]    DbgMCU_CR=0x00000007
[20:29:37.531]    DbgMCU_APB1_Fz=0x00000000
[20:29:37.531]    DbgMCU_APB2_Fz=0x00000000
[20:29:37.531]    DoOptionByteLoading=0x00000000
[20:29:37.531]  </debugvars>
[20:29:37.532]  
[20:29:37.532]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[20:29:37.532]    <block atomic="false" info="">
[20:29:37.532]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[20:29:37.533]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[20:29:37.533]    </block>
[20:29:37.534]    <block atomic="false" info="DbgMCU registers">
[20:29:37.534]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[20:29:37.535]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[20:29:37.536]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[20:29:37.536]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[20:29:37.537]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[20:29:37.537]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[20:29:37.538]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[20:29:37.538]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[20:29:37.539]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[20:29:37.539]    </block>
[20:29:37.539]  </sequence>
[20:29:37.539]  
[20:29:45.626]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[20:29:45.626]  
[20:29:45.626]  <debugvars>
[20:29:45.627]    // Pre-defined
[20:29:45.627]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:29:45.628]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:29:45.628]    __dp=0x00000000
[20:29:45.628]    __ap=0x00000000
[20:29:45.629]    __traceout=0x00000000      (Trace Disabled)
[20:29:45.629]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:29:45.631]    __FlashAddr=0x00000000
[20:29:45.631]    __FlashLen=0x00000000
[20:29:45.631]    __FlashArg=0x00000000
[20:29:45.632]    __FlashOp=0x00000000
[20:29:45.632]    __Result=0x00000000
[20:29:45.633]    
[20:29:45.633]    // User-defined
[20:29:45.633]    DbgMCU_CR=0x00000007
[20:29:45.633]    DbgMCU_APB1_Fz=0x00000000
[20:29:45.633]    DbgMCU_APB2_Fz=0x00000000
[20:29:45.634]    DoOptionByteLoading=0x00000000
[20:29:45.634]  </debugvars>
[20:29:45.634]  
[20:29:45.634]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[20:29:45.635]    <block atomic="false" info="">
[20:29:45.635]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[20:29:45.635]        // -> [connectionFlash <= 0x00000001]
[20:29:45.635]      __var FLASH_BASE = 0x40022000 ;
[20:29:45.635]        // -> [FLASH_BASE <= 0x40022000]
[20:29:45.636]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[20:29:45.636]        // -> [FLASH_CR <= 0x40022004]
[20:29:45.636]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[20:29:45.636]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[20:29:45.636]      __var LOCK_BIT = ( 1 << 0 ) ;
[20:29:45.637]        // -> [LOCK_BIT <= 0x00000001]
[20:29:45.637]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[20:29:45.637]        // -> [OPTLOCK_BIT <= 0x00000004]
[20:29:45.637]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[20:29:45.637]        // -> [FLASH_KEYR <= 0x4002200C]
[20:29:45.637]      __var FLASH_KEY1 = 0x89ABCDEF ;
[20:29:45.638]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[20:29:45.638]      __var FLASH_KEY2 = 0x02030405 ;
[20:29:45.638]        // -> [FLASH_KEY2 <= 0x02030405]
[20:29:45.638]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[20:29:45.638]        // -> [FLASH_OPTKEYR <= 0x40022014]
[20:29:45.639]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[20:29:45.639]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[20:29:45.639]      __var FLASH_OPTKEY2 = 0x24252627 ;
[20:29:45.639]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[20:29:45.639]      __var FLASH_CR_Value = 0 ;
[20:29:45.639]        // -> [FLASH_CR_Value <= 0x00000000]
[20:29:45.639]      __var DoDebugPortStop = 1 ;
[20:29:45.640]        // -> [DoDebugPortStop <= 0x00000001]
[20:29:45.640]      __var DP_CTRL_STAT = 0x4 ;
[20:29:45.640]        // -> [DP_CTRL_STAT <= 0x00000004]
[20:29:45.640]      __var DP_SELECT = 0x8 ;
[20:29:45.641]        // -> [DP_SELECT <= 0x00000008]
[20:29:45.641]    </block>
[20:29:45.641]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[20:29:45.641]      // if-block "connectionFlash && DoOptionByteLoading"
[20:29:45.641]        // =>  FALSE
[20:29:45.641]      // skip if-block "connectionFlash && DoOptionByteLoading"
[20:29:45.642]    </control>
[20:29:45.642]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[20:29:45.642]      // if-block "DoDebugPortStop"
[20:29:45.642]        // =>  TRUE
[20:29:45.642]      <block atomic="false" info="">
[20:29:45.643]        WriteDP(DP_SELECT, 0x00000000);
[20:29:45.643]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[20:29:45.643]        WriteDP(DP_CTRL_STAT, 0x00000000);
[20:29:45.643]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[20:29:45.643]      </block>
[20:29:45.643]      // end if-block "DoDebugPortStop"
[20:29:45.643]    </control>
[20:29:45.644]  </sequence>
[20:29:45.644]  
[20:34:52.133]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[20:34:52.133]  
[20:34:52.133]  <debugvars>
[20:34:52.133]    // Pre-defined
[20:34:52.133]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:34:52.134]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:34:52.134]    __dp=0x00000000
[20:34:52.134]    __ap=0x00000000
[20:34:52.134]    __traceout=0x00000000      (Trace Disabled)
[20:34:52.135]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:34:52.135]    __FlashAddr=0x00000000
[20:34:52.135]    __FlashLen=0x00000000
[20:34:52.135]    __FlashArg=0x00000000
[20:34:52.135]    __FlashOp=0x00000000
[20:34:52.135]    __Result=0x00000000
[20:34:52.136]    
[20:34:52.136]    // User-defined
[20:34:52.136]    DbgMCU_CR=0x00000007
[20:34:52.136]    DbgMCU_APB1_Fz=0x00000000
[20:34:52.136]    DbgMCU_APB2_Fz=0x00000000
[20:34:52.136]    DoOptionByteLoading=0x00000000
[20:34:52.136]  </debugvars>
[20:34:52.137]  
[20:34:52.137]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[20:34:52.137]    <block atomic="false" info="">
[20:34:52.137]      Sequence("CheckID");
[20:34:52.137]        <sequence name="CheckID" Pname="" disable="false" info="">
[20:34:52.138]          <block atomic="false" info="">
[20:34:52.138]            __var pidr1 = 0;
[20:34:52.138]              // -> [pidr1 <= 0x00000000]
[20:34:52.138]            __var pidr2 = 0;
[20:34:52.138]              // -> [pidr2 <= 0x00000000]
[20:34:52.139]            __var jep106id = 0;
[20:34:52.139]              // -> [jep106id <= 0x00000000]
[20:34:52.139]            __var ROMTableBase = 0;
[20:34:52.140]              // -> [ROMTableBase <= 0x00000000]
[20:34:52.140]            __ap = 0;      // AHB-AP
[20:34:52.140]              // -> [__ap <= 0x00000000]
[20:34:52.140]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[20:34:52.141]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[20:34:52.141]              // -> [ROMTableBase <= 0xF0000000]
[20:34:52.141]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[20:34:52.142]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[20:34:52.142]              // -> [pidr1 <= 0x00000004]
[20:34:52.142]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[20:34:52.144]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[20:34:52.144]              // -> [pidr2 <= 0x0000000A]
[20:34:52.144]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[20:34:52.144]              // -> [jep106id <= 0x00000020]
[20:34:52.144]          </block>
[20:34:52.145]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[20:34:52.145]            // if-block "jep106id != 0x20"
[20:34:52.145]              // =>  FALSE
[20:34:52.145]            // skip if-block "jep106id != 0x20"
[20:34:52.145]          </control>
[20:34:52.146]        </sequence>
[20:34:52.146]    </block>
[20:34:52.146]  </sequence>
[20:34:52.146]  
[20:34:52.158]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[20:34:52.158]  
[20:34:52.171]  <debugvars>
[20:34:52.171]    // Pre-defined
[20:34:52.171]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:34:52.172]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:34:52.172]    __dp=0x00000000
[20:34:52.172]    __ap=0x00000000
[20:34:52.172]    __traceout=0x00000000      (Trace Disabled)
[20:34:52.172]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:34:52.172]    __FlashAddr=0x00000000
[20:34:52.173]    __FlashLen=0x00000000
[20:34:52.173]    __FlashArg=0x00000000
[20:34:52.173]    __FlashOp=0x00000000
[20:34:52.173]    __Result=0x00000000
[20:34:52.173]    
[20:34:52.173]    // User-defined
[20:34:52.173]    DbgMCU_CR=0x00000007
[20:34:52.174]    DbgMCU_APB1_Fz=0x00000000
[20:34:52.174]    DbgMCU_APB2_Fz=0x00000000
[20:34:52.174]    DoOptionByteLoading=0x00000000
[20:34:52.174]  </debugvars>
[20:34:52.174]  
[20:34:52.175]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[20:34:52.175]    <block atomic="false" info="">
[20:34:52.175]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[20:34:52.176]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[20:34:52.176]    </block>
[20:34:52.176]    <block atomic="false" info="DbgMCU registers">
[20:34:52.176]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[20:34:52.177]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[20:34:52.178]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[20:34:52.178]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[20:34:52.179]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[20:34:52.179]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[20:34:52.180]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[20:34:52.180]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[20:34:52.181]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[20:34:52.181]    </block>
[20:34:52.181]  </sequence>
[20:34:52.181]  
[20:35:00.026]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[20:35:00.026]  
[20:35:00.026]  <debugvars>
[20:35:00.028]    // Pre-defined
[20:35:00.028]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:35:00.028]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:35:00.029]    __dp=0x00000000
[20:35:00.029]    __ap=0x00000000
[20:35:00.029]    __traceout=0x00000000      (Trace Disabled)
[20:35:00.030]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:35:00.030]    __FlashAddr=0x00000000
[20:35:00.030]    __FlashLen=0x00000000
[20:35:00.030]    __FlashArg=0x00000000
[20:35:00.031]    __FlashOp=0x00000000
[20:35:00.031]    __Result=0x00000000
[20:35:00.031]    
[20:35:00.031]    // User-defined
[20:35:00.032]    DbgMCU_CR=0x00000007
[20:35:00.033]    DbgMCU_APB1_Fz=0x00000000
[20:35:00.033]    DbgMCU_APB2_Fz=0x00000000
[20:35:00.033]    DoOptionByteLoading=0x00000000
[20:35:00.033]  </debugvars>
[20:35:00.033]  
[20:35:00.034]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[20:35:00.034]    <block atomic="false" info="">
[20:35:00.034]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[20:35:00.034]        // -> [connectionFlash <= 0x00000001]
[20:35:00.034]      __var FLASH_BASE = 0x40022000 ;
[20:35:00.034]        // -> [FLASH_BASE <= 0x40022000]
[20:35:00.034]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[20:35:00.034]        // -> [FLASH_CR <= 0x40022004]
[20:35:00.035]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[20:35:00.035]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[20:35:00.036]      __var LOCK_BIT = ( 1 << 0 ) ;
[20:35:00.036]        // -> [LOCK_BIT <= 0x00000001]
[20:35:00.036]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[20:35:00.036]        // -> [OPTLOCK_BIT <= 0x00000004]
[20:35:00.036]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[20:35:00.037]        // -> [FLASH_KEYR <= 0x4002200C]
[20:35:00.037]      __var FLASH_KEY1 = 0x89ABCDEF ;
[20:35:00.037]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[20:35:00.037]      __var FLASH_KEY2 = 0x02030405 ;
[20:35:00.037]        // -> [FLASH_KEY2 <= 0x02030405]
[20:35:00.037]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[20:35:00.037]        // -> [FLASH_OPTKEYR <= 0x40022014]
[20:35:00.037]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[20:35:00.038]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[20:35:00.038]      __var FLASH_OPTKEY2 = 0x24252627 ;
[20:35:00.039]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[20:35:00.039]      __var FLASH_CR_Value = 0 ;
[20:35:00.039]        // -> [FLASH_CR_Value <= 0x00000000]
[20:35:00.039]      __var DoDebugPortStop = 1 ;
[20:35:00.039]        // -> [DoDebugPortStop <= 0x00000001]
[20:35:00.039]      __var DP_CTRL_STAT = 0x4 ;
[20:35:00.039]        // -> [DP_CTRL_STAT <= 0x00000004]
[20:35:00.039]      __var DP_SELECT = 0x8 ;
[20:35:00.039]        // -> [DP_SELECT <= 0x00000008]
[20:35:00.040]    </block>
[20:35:00.040]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[20:35:00.040]      // if-block "connectionFlash && DoOptionByteLoading"
[20:35:00.040]        // =>  FALSE
[20:35:00.040]      // skip if-block "connectionFlash && DoOptionByteLoading"
[20:35:00.041]    </control>
[20:35:00.041]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[20:35:00.042]      // if-block "DoDebugPortStop"
[20:35:00.042]        // =>  TRUE
[20:35:00.042]      <block atomic="false" info="">
[20:35:00.042]        WriteDP(DP_SELECT, 0x00000000);
[20:35:00.043]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[20:35:00.043]        WriteDP(DP_CTRL_STAT, 0x00000000);
[20:35:00.043]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[20:35:00.043]      </block>
[20:35:00.043]      // end if-block "DoDebugPortStop"
[20:35:00.044]    </control>
[20:35:00.044]  </sequence>
[20:35:00.045]  
[20:38:39.408]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[20:38:39.408]  
[20:38:39.409]  <debugvars>
[20:38:39.409]    // Pre-defined
[20:38:39.409]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:38:39.409]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:38:39.409]    __dp=0x00000000
[20:38:39.410]    __ap=0x00000000
[20:38:39.411]    __traceout=0x00000000      (Trace Disabled)
[20:38:39.411]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:38:39.411]    __FlashAddr=0x00000000
[20:38:39.411]    __FlashLen=0x00000000
[20:38:39.412]    __FlashArg=0x00000000
[20:38:39.412]    __FlashOp=0x00000000
[20:38:39.412]    __Result=0x00000000
[20:38:39.413]    
[20:38:39.413]    // User-defined
[20:38:39.414]    DbgMCU_CR=0x00000007
[20:38:39.414]    DbgMCU_APB1_Fz=0x00000000
[20:38:39.414]    DbgMCU_APB2_Fz=0x00000000
[20:38:39.414]    DoOptionByteLoading=0x00000000
[20:38:39.414]  </debugvars>
[20:38:39.415]  
[20:38:39.415]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[20:38:39.415]    <block atomic="false" info="">
[20:38:39.415]      Sequence("CheckID");
[20:38:39.415]        <sequence name="CheckID" Pname="" disable="false" info="">
[20:38:39.415]          <block atomic="false" info="">
[20:38:39.416]            __var pidr1 = 0;
[20:38:39.416]              // -> [pidr1 <= 0x00000000]
[20:38:39.416]            __var pidr2 = 0;
[20:38:39.416]              // -> [pidr2 <= 0x00000000]
[20:38:39.416]            __var jep106id = 0;
[20:38:39.417]              // -> [jep106id <= 0x00000000]
[20:38:39.417]            __var ROMTableBase = 0;
[20:38:39.417]              // -> [ROMTableBase <= 0x00000000]
[20:38:39.417]            __ap = 0;      // AHB-AP
[20:38:39.417]              // -> [__ap <= 0x00000000]
[20:38:39.417]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[20:38:39.417]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[20:38:39.418]              // -> [ROMTableBase <= 0xF0000000]
[20:38:39.418]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[20:38:39.420]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[20:38:39.420]              // -> [pidr1 <= 0x00000004]
[20:38:39.420]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[20:38:39.420]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[20:38:39.420]              // -> [pidr2 <= 0x0000000A]
[20:38:39.421]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[20:38:39.422]              // -> [jep106id <= 0x00000020]
[20:38:39.422]          </block>
[20:38:39.422]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[20:38:39.423]            // if-block "jep106id != 0x20"
[20:38:39.423]              // =>  FALSE
[20:38:39.423]            // skip if-block "jep106id != 0x20"
[20:38:39.423]          </control>
[20:38:39.423]        </sequence>
[20:38:39.424]    </block>
[20:38:39.424]  </sequence>
[20:38:39.424]  
[20:38:39.435]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[20:38:39.435]  
[20:38:39.448]  <debugvars>
[20:38:39.449]    // Pre-defined
[20:38:39.450]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:38:39.450]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:38:39.450]    __dp=0x00000000
[20:38:39.450]    __ap=0x00000000
[20:38:39.451]    __traceout=0x00000000      (Trace Disabled)
[20:38:39.451]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:38:39.452]    __FlashAddr=0x00000000
[20:38:39.453]    __FlashLen=0x00000000
[20:38:39.453]    __FlashArg=0x00000000
[20:38:39.454]    __FlashOp=0x00000000
[20:38:39.454]    __Result=0x00000000
[20:38:39.455]    
[20:38:39.455]    // User-defined
[20:38:39.455]    DbgMCU_CR=0x00000007
[20:38:39.456]    DbgMCU_APB1_Fz=0x00000000
[20:38:39.456]    DbgMCU_APB2_Fz=0x00000000
[20:38:39.456]    DoOptionByteLoading=0x00000000
[20:38:39.456]  </debugvars>
[20:38:39.456]  
[20:38:39.456]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[20:38:39.458]    <block atomic="false" info="">
[20:38:39.458]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[20:38:39.459]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[20:38:39.459]    </block>
[20:38:39.459]    <block atomic="false" info="DbgMCU registers">
[20:38:39.459]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[20:38:39.460]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[20:38:39.461]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[20:38:39.461]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[20:38:39.462]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[20:38:39.462]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[20:38:39.463]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[20:38:39.463]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[20:38:39.464]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[20:38:39.464]    </block>
[20:38:39.464]  </sequence>
[20:38:39.464]  
[20:38:47.294]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[20:38:47.294]  
[20:38:47.295]  <debugvars>
[20:38:47.296]    // Pre-defined
[20:38:47.296]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[20:38:47.297]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[20:38:47.297]    __dp=0x00000000
[20:38:47.297]    __ap=0x00000000
[20:38:47.298]    __traceout=0x00000000      (Trace Disabled)
[20:38:47.298]    __errorcontrol=0x00000000  (Skip Errors="False")
[20:38:47.298]    __FlashAddr=0x00000000
[20:38:47.299]    __FlashLen=0x00000000
[20:38:47.299]    __FlashArg=0x00000000
[20:38:47.299]    __FlashOp=0x00000000
[20:38:47.299]    __Result=0x00000000
[20:38:47.300]    
[20:38:47.300]    // User-defined
[20:38:47.300]    DbgMCU_CR=0x00000007
[20:38:47.301]    DbgMCU_APB1_Fz=0x00000000
[20:38:47.301]    DbgMCU_APB2_Fz=0x00000000
[20:38:47.301]    DoOptionByteLoading=0x00000000
[20:38:47.301]  </debugvars>
[20:38:47.301]  
[20:38:47.302]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[20:38:47.303]    <block atomic="false" info="">
[20:38:47.303]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[20:38:47.303]        // -> [connectionFlash <= 0x00000001]
[20:38:47.303]      __var FLASH_BASE = 0x40022000 ;
[20:38:47.303]        // -> [FLASH_BASE <= 0x40022000]
[20:38:47.304]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[20:38:47.304]        // -> [FLASH_CR <= 0x40022004]
[20:38:47.304]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[20:38:47.304]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[20:38:47.304]      __var LOCK_BIT = ( 1 << 0 ) ;
[20:38:47.304]        // -> [LOCK_BIT <= 0x00000001]
[20:38:47.305]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[20:38:47.305]        // -> [OPTLOCK_BIT <= 0x00000004]
[20:38:47.305]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[20:38:47.305]        // -> [FLASH_KEYR <= 0x4002200C]
[20:38:47.305]      __var FLASH_KEY1 = 0x89ABCDEF ;
[20:38:47.306]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[20:38:47.306]      __var FLASH_KEY2 = 0x02030405 ;
[20:38:47.306]        // -> [FLASH_KEY2 <= 0x02030405]
[20:38:47.306]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[20:38:47.306]        // -> [FLASH_OPTKEYR <= 0x40022014]
[20:38:47.306]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[20:38:47.307]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[20:38:47.307]      __var FLASH_OPTKEY2 = 0x24252627 ;
[20:38:47.307]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[20:38:47.307]      __var FLASH_CR_Value = 0 ;
[20:38:47.307]        // -> [FLASH_CR_Value <= 0x00000000]
[20:38:47.307]      __var DoDebugPortStop = 1 ;
[20:38:47.307]        // -> [DoDebugPortStop <= 0x00000001]
[20:38:47.308]      __var DP_CTRL_STAT = 0x4 ;
[20:38:47.308]        // -> [DP_CTRL_STAT <= 0x00000004]
[20:38:47.309]      __var DP_SELECT = 0x8 ;
[20:38:47.309]        // -> [DP_SELECT <= 0x00000008]
[20:38:47.309]    </block>
[20:38:47.309]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[20:38:47.309]      // if-block "connectionFlash && DoOptionByteLoading"
[20:38:47.310]        // =>  FALSE
[20:38:47.310]      // skip if-block "connectionFlash && DoOptionByteLoading"
[20:38:47.311]    </control>
[20:38:47.311]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[20:38:47.311]      // if-block "DoDebugPortStop"
[20:38:47.311]        // =>  TRUE
[20:38:47.312]      <block atomic="false" info="">
[20:38:47.312]        WriteDP(DP_SELECT, 0x00000000);
[20:38:47.312]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[20:38:47.313]        WriteDP(DP_CTRL_STAT, 0x00000000);
[20:38:47.313]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[20:38:47.313]      </block>
[20:38:47.313]      // end if-block "DoDebugPortStop"
[20:38:47.313]    </control>
[20:38:47.314]  </sequence>
[20:38:47.314]  
