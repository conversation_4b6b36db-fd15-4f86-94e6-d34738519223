/**
 * stm32l072cbt6 32MHz 128kb flash + 16kb ram + 6K eeprom
 */
#include "main.h"
#include "net.h"
#include "rtc.h"
#include "adc.h"//peripheral/include/
#include "protocol_common.h"
#include "net_protocol.h"
#include "auto_ctrl_task.h"
#include "bsp_board.h"
#include "lcd12864.h"
#include "uart.h"
#include "debug.h"
#include "stm32_flash.h"
#include "adc.h"
#include "rtc.h"
#include "user_config.h"
#include <stdio.h>
#include "daoSheng_protocol.h"

NET_INFO net_info = {0,};
float vdda_voltage = 0;
uint8_t measure_complete = 0;

#define NET_TASK_STK_SIZE 1024
#define NET_TASK_PRIO			3
rt_thread_t net_task_handle;

#define AUTO_CTRL_TASK_STK_SIZE 1024
#define AUTO_CTRL_TASK_PRIO	4

#define CAT1_POWER(x) HAL_GPIO_WritePin(GPIOB, GPIO_PIN_1, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)
#define CAT1_RST(x) HAL_GPIO_WritePin(GPIOB, GPIO_PIN_10, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)


rt_thread_t auto_ctrl_task_handle;

CALENDAR calendar;

static void net_task(void *param)
{ 
	char ip[20];
/*
	��ʼ�����缰����*/
	if(net_init(&net_info,115200) == NET_ERROR_NONE)
	{
reconnect:
		rt_memset(ip,0,sizeof(ip));
		sprintf(ip,"%d.%d.%d.%d",config.net_param.ip[0][0],config.net_param.ip[0][1],config.net_param.ip[0][2],config.net_param.ip[0][3]);
		if(net_info.tcpip->connect(ip, config.net_param.port[0]) != NET_ERROR_NONE)
			goto reconnect;
/*�����ɹ���ͬ��ʱ��*/
		CALENDAR calendar;
		rt_memset(&calendar, 0, sizeof(calendar));
		if(net_info.base_function->sync_time(&calendar.year, &calendar.month, &calendar.day, &calendar.hour, &calendar.minute, &calendar.second) == NET_ERROR_NONE)
		{
			rtc_set_time(calendar.year, calendar.month, calendar.day, calendar.hour, calendar.minute, calendar.second);
		}
	}
	//net_info.tcpip->send();
	//net_heartbeat(uint8_t central_station_address,UPLOAD_DATA data)
	while(1)
	{
		
		
		/**/
		rtc_get_calendar(&calendar.year, &calendar.month, &calendar.day, &calendar.hour, &calendar.minute, &calendar.second);
		net_protocol_parsing();
		rt_thread_mdelay(100);
	}
}

static void auto_ctrl_task(void *arg)
{
	arg = arg;
	while(1)
	{
		auto_ctrl();
		rt_thread_mdelay(500);
	}
}
static void MX_GPIO_Init(void) {
  GPIO_InitTypeDef GPIO_InitStruct = {0};

  __HAL_RCC_GPIOB_CLK_ENABLE();
	
  
  GPIO_InitStruct.Pin = GPIO_PIN_12|GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2;//PB12_LED  //PB0_CAT1_POWER
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
	
}



int main(void)
{
//	uint8_t sig = 0;
//	net_info.base_function->get_net_sig(&sig);
		/*��ֹ��ťʧЧ����Ҫ���ö�Ӧwakeup*/

	__HAL_RCC_PWR_CLK_ENABLE();
//	HAL_ADCEx_EnableVREFINT();
	//HAL_PWR_DisableWakeUpPin(PWR_WAKEUP_PIN2);
  /* ?????EEPROM */
  EEPROM_Init();
	read_config(0);
/*main �������ϴ��벻�ɵ���*/
	
	bsp_board_init();
	serial_485_init(config.water_meter_info);
	debug_init(115200);
	rtc_init();
	 /* ���ñ��������? */
  //  HAL_PWR_EnableBkUpAccess();
	lcd_init();
	lcd_clear(0); //????
	ADC_Voltage_Init();////PA4 ADC???ADC_Voltage_Init
	display_flow_rate();
	
 // MX_RTC_Init();
	//disp_row();
	
	net_init(&net_info,115200);
	
	 MX_GPIO_Init();
	 CAT1_POWER(1);
	 HAL_Delay(500);
	 CAT1_POWER(0);

	/* ���������߳�*////CAT1_POWER
	rt_enter_critical();
	net_task_handle = rt_thread_create("net_task",net_task,RT_NULL,NET_TASK_STK_SIZE,NET_TASK_PRIO,10);
	if(net_task_handle != RT_NULL)
	{
		//rt_thread_startup(net_task_handle);	
	}
	auto_ctrl_task_handle = rt_thread_create("auto_ctrl",auto_ctrl_task,RT_NULL,AUTO_CTRL_TASK_STK_SIZE,AUTO_CTRL_TASK_PRIO,10);
	if(auto_ctrl_task_handle != RT_NULL)
	{
		rt_thread_startup(auto_ctrl_task_handle);
	}
	rt_exit_critical();
	
 uint32_t CR_time = rt_tick_get();// HAL_GetTick();// 
	printf("CR_Time = %d\r\n", CR_time);
		//EEPROM_ReadBuffer(EEPROM_BANK2_ADDR,(uint8_t *)&config,sizeof(CONFIG));
	while(1)
{
	protocol_parsing();	
	HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_12);

  vdda_voltage=Get_Power_Voltage();
	
	display_rotate();
 	
	
  printf("��Դ��ѹ = %.2f\r\n", vdda_voltage);
	rt_thread_mdelay(5000);
	HAL_GPIO_WritePin(GPIOB,GPIO_PIN_0,0);
	bsp_board_enter_standy();
	
//	  SystemClock_Config();
	 //sleep_mode();           //   // ����͹���?
}
}
