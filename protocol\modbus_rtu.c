#include "modbus_rtu.h"
#include <stdio.h>
#include <string.h>

//static uint8_t modbus_send_buffer[256],send_len = 0;

uint16_t modbus_crc16(uint8_t *buf,uint8_t len)
{
  uint16_t crc = 0;
  uint8_t i = 0;
  uint8_t j = 0;

  crc = 0xffff;
  for (i = 0; i < len; i++) // len
  {
    crc ^= buf[i];
    for (j = 0; j < 8; j++)
    {
      if (crc & 0x00001)
        crc = (crc >> 1) ^ 0xA001;
      else
        crc = (crc >> 1);
    }
  }
  return crc;
}

static MODBUS_ERR modbus_rtu_send(uint8_t addr,uint8_t cmd)
{

	return MODBUS_ERR_NONE;
}

static void modbus_rtu_send_buffer_init(void)
{

}

MODBUS_ERR modbus_rtu_read(uint8_t addr,uint16_t reg_addr,uint16_t reg_count,uint8_t *out_buf,uint8_t *out_count,uint16_t timeout)
{
	modbus_rtu_send_buffer_init();
	
	modbus_rtu_send(addr,0x03);
	return MODBUS_ERR_NONE;
}
MODBUS_ERR modbus_rtu_write(uint8_t addr,uint16_t reg_addr,uint16_t dat,uint16_t timeout)
{
  return MODBUS_ERR_NONE;
}

