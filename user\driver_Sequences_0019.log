/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : D:\2025work\STM32L072PRO\source-application\user\driver_Sequences_0019.log
 *  Created     : 09:46:46 (18/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : D:\2025work\STM32L072PRO\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[09:46:46.151]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:46:46.151]  
[09:46:46.175]  <debugvars>
[09:46:46.192]    // Pre-defined
[09:46:46.209]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:46:46.225]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:46:46.226]    __dp=0x00000000
[09:46:46.226]    __ap=0x00000000
[09:46:46.226]    __traceout=0x00000000      (Trace Disabled)
[09:46:46.226]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:46:46.227]    __FlashAddr=0x00000000
[09:46:46.227]    __FlashLen=0x00000000
[09:46:46.227]    __FlashArg=0x00000000
[09:46:46.227]    __FlashOp=0x00000000
[09:46:46.227]    __Result=0x00000000
[09:46:46.228]    
[09:46:46.228]    // User-defined
[09:46:46.228]    DbgMCU_CR=0x00000007
[09:46:46.228]    DbgMCU_APB1_Fz=0x00000000
[09:46:46.228]    DbgMCU_APB2_Fz=0x00000000
[09:46:46.228]    DoOptionByteLoading=0x00000000
[09:46:46.229]  </debugvars>
[09:46:46.229]  
[09:46:46.230]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:46:46.230]    <block atomic="false" info="">
[09:46:46.230]      Sequence("CheckID");
[09:46:46.230]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:46:46.230]          <block atomic="false" info="">
[09:46:46.231]            __var pidr1 = 0;
[09:46:46.231]              // -> [pidr1 <= 0x00000000]
[09:46:46.231]            __var pidr2 = 0;
[09:46:46.231]              // -> [pidr2 <= 0x00000000]
[09:46:46.231]            __var jep106id = 0;
[09:46:46.232]              // -> [jep106id <= 0x00000000]
[09:46:46.232]            __var ROMTableBase = 0;
[09:46:46.232]              // -> [ROMTableBase <= 0x00000000]
[09:46:46.232]            __ap = 0;      // AHB-AP
[09:46:46.232]              // -> [__ap <= 0x00000000]
[09:46:46.232]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:46:46.233]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:46:46.233]              // -> [ROMTableBase <= 0xF0000000]
[09:46:46.234]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:46:46.235]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:46:46.236]              // -> [pidr1 <= 0x00000004]
[09:46:46.236]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:46:46.237]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:46:46.237]              // -> [pidr2 <= 0x0000000A]
[09:46:46.237]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:46:46.237]              // -> [jep106id <= 0x00000020]
[09:46:46.238]          </block>
[09:46:46.238]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:46:46.238]            // if-block "jep106id != 0x20"
[09:46:46.238]              // =>  FALSE
[09:46:46.238]            // skip if-block "jep106id != 0x20"
[09:46:46.239]          </control>
[09:46:46.239]        </sequence>
[09:46:46.239]    </block>
[09:46:46.239]  </sequence>
[09:46:46.239]  
[09:46:46.251]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:46:46.251]  
[09:46:46.258]  <debugvars>
[09:46:46.258]    // Pre-defined
[09:46:46.259]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:46:46.259]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:46:46.259]    __dp=0x00000000
[09:46:46.259]    __ap=0x00000000
[09:46:46.259]    __traceout=0x00000000      (Trace Disabled)
[09:46:46.259]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:46:46.260]    __FlashAddr=0x00000000
[09:46:46.260]    __FlashLen=0x00000000
[09:46:46.260]    __FlashArg=0x00000000
[09:46:46.260]    __FlashOp=0x00000000
[09:46:46.261]    __Result=0x00000000
[09:46:46.261]    
[09:46:46.261]    // User-defined
[09:46:46.261]    DbgMCU_CR=0x00000007
[09:46:46.262]    DbgMCU_APB1_Fz=0x00000000
[09:46:46.262]    DbgMCU_APB2_Fz=0x00000000
[09:46:46.263]    DoOptionByteLoading=0x00000000
[09:46:46.263]  </debugvars>
[09:46:46.263]  
[09:46:46.263]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:46:46.263]    <block atomic="false" info="">
[09:46:46.264]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:46:46.265]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:46:46.265]    </block>
[09:46:46.265]    <block atomic="false" info="DbgMCU registers">
[09:46:46.265]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:46:46.266]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[09:46:46.267]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[09:46:46.267]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:46:46.268]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:46:46.268]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:46:46.270]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:46:46.270]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:46:46.271]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:46:46.271]    </block>
[09:46:46.271]  </sequence>
[09:46:46.271]  
[09:46:54.119]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:46:54.119]  
[09:46:54.120]  <debugvars>
[09:46:54.120]    // Pre-defined
[09:46:54.120]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:46:54.120]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:46:54.120]    __dp=0x00000000
[09:46:54.120]    __ap=0x00000000
[09:46:54.120]    __traceout=0x00000000      (Trace Disabled)
[09:46:54.120]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:46:54.120]    __FlashAddr=0x00000000
[09:46:54.120]    __FlashLen=0x00000000
[09:46:54.122]    __FlashArg=0x00000000
[09:46:54.122]    __FlashOp=0x00000000
[09:46:54.122]    __Result=0x00000000
[09:46:54.122]    
[09:46:54.122]    // User-defined
[09:46:54.122]    DbgMCU_CR=0x00000007
[09:46:54.122]    DbgMCU_APB1_Fz=0x00000000
[09:46:54.122]    DbgMCU_APB2_Fz=0x00000000
[09:46:54.122]    DoOptionByteLoading=0x00000000
[09:46:54.122]  </debugvars>
[09:46:54.122]  
[09:46:54.124]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:46:54.124]    <block atomic="false" info="">
[09:46:54.124]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:46:54.124]        // -> [connectionFlash <= 0x00000001]
[09:46:54.125]      __var FLASH_BASE = 0x40022000 ;
[09:46:54.126]        // -> [FLASH_BASE <= 0x40022000]
[09:46:54.126]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:46:54.126]        // -> [FLASH_CR <= 0x40022004]
[09:46:54.126]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:46:54.126]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:46:54.126]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:46:54.126]        // -> [LOCK_BIT <= 0x00000001]
[09:46:54.126]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:46:54.126]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:46:54.126]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:46:54.126]        // -> [FLASH_KEYR <= 0x4002200C]
[09:46:54.126]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:46:54.126]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:46:54.126]      __var FLASH_KEY2 = 0x02030405 ;
[09:46:54.126]        // -> [FLASH_KEY2 <= 0x02030405]
[09:46:54.126]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:46:54.126]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:46:54.126]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:46:54.126]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:46:54.126]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:46:54.131]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:46:54.131]      __var FLASH_CR_Value = 0 ;
[09:46:54.131]        // -> [FLASH_CR_Value <= 0x00000000]
[09:46:54.131]      __var DoDebugPortStop = 1 ;
[09:46:54.131]        // -> [DoDebugPortStop <= 0x00000001]
[09:46:54.131]      __var DP_CTRL_STAT = 0x4 ;
[09:46:54.131]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:46:54.131]      __var DP_SELECT = 0x8 ;
[09:46:54.133]        // -> [DP_SELECT <= 0x00000008]
[09:46:54.133]    </block>
[09:46:54.133]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:46:54.133]      // if-block "connectionFlash && DoOptionByteLoading"
[09:46:54.134]        // =>  FALSE
[09:46:54.134]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:46:54.134]    </control>
[09:46:54.134]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:46:54.134]      // if-block "DoDebugPortStop"
[09:46:54.135]        // =>  TRUE
[09:46:54.135]      <block atomic="false" info="">
[09:46:54.135]        WriteDP(DP_SELECT, 0x00000000);
[09:46:54.150]  
[09:46:54.150]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[09:46:54.150]  
[09:46:54.151]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:46:54.166]      </block>
[09:46:54.167]      // end if-block "DoDebugPortStop"
[09:46:54.167]    </control>
[09:46:54.168]  </sequence>
[09:46:54.168]  
[09:47:19.832]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:47:19.832]  
[09:47:19.833]  <debugvars>
[09:47:19.833]    // Pre-defined
[09:47:19.833]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:47:19.833]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:47:19.834]    __dp=0x00000000
[09:47:19.834]    __ap=0x00000000
[09:47:19.835]    __traceout=0x00000000      (Trace Disabled)
[09:47:19.835]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:47:19.835]    __FlashAddr=0x00000000
[09:47:19.835]    __FlashLen=0x00000000
[09:47:19.835]    __FlashArg=0x00000000
[09:47:19.835]    __FlashOp=0x00000000
[09:47:19.836]    __Result=0x00000000
[09:47:19.836]    
[09:47:19.836]    // User-defined
[09:47:19.836]    DbgMCU_CR=0x00000007
[09:47:19.836]    DbgMCU_APB1_Fz=0x00000000
[09:47:19.836]    DbgMCU_APB2_Fz=0x00000000
[09:47:19.836]    DoOptionByteLoading=0x00000000
[09:47:19.836]  </debugvars>
[09:47:19.836]  
[09:47:19.836]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:47:19.837]    <block atomic="false" info="">
[09:47:19.837]      Sequence("CheckID");
[09:47:19.838]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:47:19.838]          <block atomic="false" info="">
[09:47:19.838]            __var pidr1 = 0;
[09:47:19.838]              // -> [pidr1 <= 0x00000000]
[09:47:19.838]            __var pidr2 = 0;
[09:47:19.839]              // -> [pidr2 <= 0x00000000]
[09:47:19.839]            __var jep106id = 0;
[09:47:19.839]              // -> [jep106id <= 0x00000000]
[09:47:19.839]            __var ROMTableBase = 0;
[09:47:19.840]              // -> [ROMTableBase <= 0x00000000]
[09:47:19.840]            __ap = 0;      // AHB-AP
[09:47:19.840]              // -> [__ap <= 0x00000000]
[09:47:19.840]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:47:19.841]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:47:19.842]              // -> [ROMTableBase <= 0xF0000000]
[09:47:19.842]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:47:19.844]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:47:19.844]              // -> [pidr1 <= 0x00000004]
[09:47:19.844]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:47:19.845]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:47:19.845]              // -> [pidr2 <= 0x0000000A]
[09:47:19.845]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:47:19.845]              // -> [jep106id <= 0x00000020]
[09:47:19.845]          </block>
[09:47:19.846]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:47:19.846]            // if-block "jep106id != 0x20"
[09:47:19.846]              // =>  FALSE
[09:47:19.846]            // skip if-block "jep106id != 0x20"
[09:47:19.846]          </control>
[09:47:19.847]        </sequence>
[09:47:19.847]    </block>
[09:47:19.847]  </sequence>
[09:47:19.847]  
[09:47:19.859]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:47:19.859]  
[09:47:19.860]  <debugvars>
[09:47:19.860]    // Pre-defined
[09:47:19.860]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:47:19.860]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:47:19.860]    __dp=0x00000000
[09:47:19.860]    __ap=0x00000000
[09:47:19.860]    __traceout=0x00000000      (Trace Disabled)
[09:47:19.861]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:47:19.861]    __FlashAddr=0x00000000
[09:47:19.861]    __FlashLen=0x00000000
[09:47:19.861]    __FlashArg=0x00000000
[09:47:19.861]    __FlashOp=0x00000000
[09:47:19.862]    __Result=0x00000000
[09:47:19.862]    
[09:47:19.862]    // User-defined
[09:47:19.862]    DbgMCU_CR=0x00000007
[09:47:19.862]    DbgMCU_APB1_Fz=0x00000000
[09:47:19.862]    DbgMCU_APB2_Fz=0x00000000
[09:47:19.862]    DoOptionByteLoading=0x00000000
[09:47:19.862]  </debugvars>
[09:47:19.862]  
[09:47:19.862]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:47:19.862]    <block atomic="false" info="">
[09:47:19.862]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:47:19.864]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:47:19.865]    </block>
[09:47:19.865]    <block atomic="false" info="DbgMCU registers">
[09:47:19.865]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:47:19.866]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[09:47:19.867]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[09:47:19.868]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:47:19.868]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:47:19.869]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:47:19.870]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:47:19.870]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:47:19.871]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:47:19.871]    </block>
[09:47:19.871]  </sequence>
[09:47:19.871]  
[09:47:31.267]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:47:31.267]  
[09:47:31.267]  <debugvars>
[09:47:31.267]    // Pre-defined
[09:47:31.268]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:47:31.268]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:47:31.268]    __dp=0x00000000
[09:47:31.268]    __ap=0x00000000
[09:47:31.269]    __traceout=0x00000000      (Trace Disabled)
[09:47:31.269]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:47:31.270]    __FlashAddr=0x00000000
[09:47:31.270]    __FlashLen=0x00000000
[09:47:31.270]    __FlashArg=0x00000000
[09:47:31.270]    __FlashOp=0x00000000
[09:47:31.271]    __Result=0x00000000
[09:47:31.271]    
[09:47:31.271]    // User-defined
[09:47:31.271]    DbgMCU_CR=0x00000007
[09:47:31.271]    DbgMCU_APB1_Fz=0x00000000
[09:47:31.272]    DbgMCU_APB2_Fz=0x00000000
[09:47:31.272]    DoOptionByteLoading=0x00000000
[09:47:31.272]  </debugvars>
[09:47:31.272]  
[09:47:31.273]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:47:31.273]    <block atomic="false" info="">
[09:47:31.273]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:47:31.274]        // -> [connectionFlash <= 0x00000001]
[09:47:31.274]      __var FLASH_BASE = 0x40022000 ;
[09:47:31.274]        // -> [FLASH_BASE <= 0x40022000]
[09:47:31.274]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:47:31.274]        // -> [FLASH_CR <= 0x40022004]
[09:47:31.275]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:47:31.275]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:47:31.275]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:47:31.275]        // -> [LOCK_BIT <= 0x00000001]
[09:47:31.276]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:47:31.276]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:47:31.276]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:47:31.276]        // -> [FLASH_KEYR <= 0x4002200C]
[09:47:31.277]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:47:31.277]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:47:31.277]      __var FLASH_KEY2 = 0x02030405 ;
[09:47:31.277]        // -> [FLASH_KEY2 <= 0x02030405]
[09:47:31.277]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:47:31.278]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:47:31.278]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:47:31.278]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:47:31.278]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:47:31.279]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:47:31.279]      __var FLASH_CR_Value = 0 ;
[09:47:31.280]        // -> [FLASH_CR_Value <= 0x00000000]
[09:47:31.280]      __var DoDebugPortStop = 1 ;
[09:47:31.280]        // -> [DoDebugPortStop <= 0x00000001]
[09:47:31.280]      __var DP_CTRL_STAT = 0x4 ;
[09:47:31.280]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:47:31.281]      __var DP_SELECT = 0x8 ;
[09:47:31.281]        // -> [DP_SELECT <= 0x00000008]
[09:47:31.281]    </block>
[09:47:31.281]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:47:31.281]      // if-block "connectionFlash && DoOptionByteLoading"
[09:47:31.282]        // =>  FALSE
[09:47:31.282]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:47:31.282]    </control>
[09:47:31.282]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:47:31.283]      // if-block "DoDebugPortStop"
[09:47:31.283]        // =>  TRUE
[09:47:31.283]      <block atomic="false" info="">
[09:47:31.283]        WriteDP(DP_SELECT, 0x00000000);
[09:47:31.284]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:47:31.284]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:47:31.285]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:47:31.285]      </block>
[09:47:31.285]      // end if-block "DoDebugPortStop"
[09:47:31.285]    </control>
[09:47:31.286]  </sequence>
[09:47:31.286]  
[09:53:49.503]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:53:49.503]  
[09:53:49.503]  <debugvars>
[09:53:49.503]    // Pre-defined
[09:53:49.503]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:53:49.503]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:53:49.503]    __dp=0x00000000
[09:53:49.504]    __ap=0x00000000
[09:53:49.504]    __traceout=0x00000000      (Trace Disabled)
[09:53:49.504]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:53:49.504]    __FlashAddr=0x00000000
[09:53:49.504]    __FlashLen=0x00000000
[09:53:49.504]    __FlashArg=0x00000000
[09:53:49.504]    __FlashOp=0x00000000
[09:53:49.504]    __Result=0x00000000
[09:53:49.506]    
[09:53:49.506]    // User-defined
[09:53:49.506]    DbgMCU_CR=0x00000007
[09:53:49.506]    DbgMCU_APB1_Fz=0x00000000
[09:53:49.506]    DbgMCU_APB2_Fz=0x00000000
[09:53:49.506]    DoOptionByteLoading=0x00000000
[09:53:49.506]  </debugvars>
[09:53:49.506]  
[09:53:49.506]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:53:49.506]    <block atomic="false" info="">
[09:53:49.506]      Sequence("CheckID");
[09:53:49.508]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:53:49.508]          <block atomic="false" info="">
[09:53:49.508]            __var pidr1 = 0;
[09:53:49.508]              // -> [pidr1 <= 0x00000000]
[09:53:49.508]            __var pidr2 = 0;
[09:53:49.508]              // -> [pidr2 <= 0x00000000]
[09:53:49.508]            __var jep106id = 0;
[09:53:49.508]              // -> [jep106id <= 0x00000000]
[09:53:49.508]            __var ROMTableBase = 0;
[09:53:49.508]              // -> [ROMTableBase <= 0x00000000]
[09:53:49.510]            __ap = 0;      // AHB-AP
[09:53:49.510]              // -> [__ap <= 0x00000000]
[09:53:49.510]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:53:49.511]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:53:49.511]              // -> [ROMTableBase <= 0xF0000000]
[09:53:49.511]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:53:49.511]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:53:49.511]              // -> [pidr1 <= 0x00000004]
[09:53:49.511]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:53:49.513]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:53:49.513]              // -> [pidr2 <= 0x0000000A]
[09:53:49.513]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:53:49.513]              // -> [jep106id <= 0x00000020]
[09:53:49.514]          </block>
[09:53:49.514]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:53:49.514]            // if-block "jep106id != 0x20"
[09:53:49.514]              // =>  FALSE
[09:53:49.516]            // skip if-block "jep106id != 0x20"
[09:53:49.516]          </control>
[09:53:49.516]        </sequence>
[09:53:49.516]    </block>
[09:53:49.516]  </sequence>
[09:53:49.516]  
[09:53:49.526]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:53:49.526]  
[09:53:49.526]  <debugvars>
[09:53:49.526]    // Pre-defined
[09:53:49.526]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:53:49.526]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:53:49.526]    __dp=0x00000000
[09:53:49.526]    __ap=0x00000000
[09:53:49.526]    __traceout=0x00000000      (Trace Disabled)
[09:53:49.526]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:53:49.526]    __FlashAddr=0x00000000
[09:53:49.526]    __FlashLen=0x00000000
[09:53:49.526]    __FlashArg=0x00000000
[09:53:49.531]    __FlashOp=0x00000000
[09:53:49.531]    __Result=0x00000000
[09:53:49.531]    
[09:53:49.531]    // User-defined
[09:53:49.531]    DbgMCU_CR=0x00000007
[09:53:49.531]    DbgMCU_APB1_Fz=0x00000000
[09:53:49.531]    DbgMCU_APB2_Fz=0x00000000
[09:53:49.531]    DoOptionByteLoading=0x00000000
[09:53:49.531]  </debugvars>
[09:53:49.531]  
[09:53:49.531]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:53:49.531]    <block atomic="false" info="">
[09:53:49.531]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:53:49.531]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:53:49.531]    </block>
[09:53:49.531]    <block atomic="false" info="DbgMCU registers">
[09:53:49.531]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:53:49.531]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[09:53:49.536]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[09:53:49.536]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:53:49.536]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:53:49.536]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:53:49.536]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:53:49.536]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:53:49.536]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:53:49.536]    </block>
[09:53:49.536]  </sequence>
[09:53:49.536]  
[09:53:57.507]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:53:57.507]  
[09:53:57.508]  <debugvars>
[09:53:57.508]    // Pre-defined
[09:53:57.509]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:53:57.509]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:53:57.510]    __dp=0x00000000
[09:53:57.510]    __ap=0x00000000
[09:53:57.510]    __traceout=0x00000000      (Trace Disabled)
[09:53:57.511]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:53:57.512]    __FlashAddr=0x00000000
[09:53:57.512]    __FlashLen=0x00000000
[09:53:57.513]    __FlashArg=0x00000000
[09:53:57.513]    __FlashOp=0x00000000
[09:53:57.514]    __Result=0x00000000
[09:53:57.514]    
[09:53:57.514]    // User-defined
[09:53:57.515]    DbgMCU_CR=0x00000007
[09:53:57.515]    DbgMCU_APB1_Fz=0x00000000
[09:53:57.516]    DbgMCU_APB2_Fz=0x00000000
[09:53:57.516]    DoOptionByteLoading=0x00000000
[09:53:57.517]  </debugvars>
[09:53:57.517]  
[09:53:57.518]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:53:57.518]    <block atomic="false" info="">
[09:53:57.519]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:53:57.519]        // -> [connectionFlash <= 0x00000001]
[09:53:57.520]      __var FLASH_BASE = 0x40022000 ;
[09:53:57.520]        // -> [FLASH_BASE <= 0x40022000]
[09:53:57.521]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:53:57.521]        // -> [FLASH_CR <= 0x40022004]
[09:53:57.521]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:53:57.522]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:53:57.522]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:53:57.522]        // -> [LOCK_BIT <= 0x00000001]
[09:53:57.523]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:53:57.523]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:53:57.523]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:53:57.524]        // -> [FLASH_KEYR <= 0x4002200C]
[09:53:57.524]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:53:57.524]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:53:57.524]      __var FLASH_KEY2 = 0x02030405 ;
[09:53:57.525]        // -> [FLASH_KEY2 <= 0x02030405]
[09:53:57.525]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:53:57.525]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:53:57.525]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:53:57.525]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:53:57.526]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:53:57.526]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:53:57.526]      __var FLASH_CR_Value = 0 ;
[09:53:57.526]        // -> [FLASH_CR_Value <= 0x00000000]
[09:53:57.526]      __var DoDebugPortStop = 1 ;
[09:53:57.527]        // -> [DoDebugPortStop <= 0x00000001]
[09:53:57.527]      __var DP_CTRL_STAT = 0x4 ;
[09:53:57.527]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:53:57.527]      __var DP_SELECT = 0x8 ;
[09:53:57.527]        // -> [DP_SELECT <= 0x00000008]
[09:53:57.527]    </block>
[09:53:57.528]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:53:57.528]      // if-block "connectionFlash && DoOptionByteLoading"
[09:53:57.528]        // =>  FALSE
[09:53:57.528]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:53:57.528]    </control>
[09:53:57.529]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:53:57.529]      // if-block "DoDebugPortStop"
[09:53:57.529]        // =>  TRUE
[09:53:57.529]      <block atomic="false" info="">
[09:53:57.529]        WriteDP(DP_SELECT, 0x00000000);
[09:53:57.530]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:53:57.530]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:53:57.531]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:53:57.531]      </block>
[09:53:57.531]      // end if-block "DoDebugPortStop"
[09:53:57.531]    </control>
[09:53:57.531]  </sequence>
[09:53:57.532]  
[09:56:17.864]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:56:17.864]  
[09:56:17.864]  <debugvars>
[09:56:17.865]    // Pre-defined
[09:56:17.865]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:56:17.865]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:56:17.866]    __dp=0x00000000
[09:56:17.866]    __ap=0x00000000
[09:56:17.866]    __traceout=0x00000000      (Trace Disabled)
[09:56:17.867]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:56:17.867]    __FlashAddr=0x00000000
[09:56:17.867]    __FlashLen=0x00000000
[09:56:17.867]    __FlashArg=0x00000000
[09:56:17.868]    __FlashOp=0x00000000
[09:56:17.868]    __Result=0x00000000
[09:56:17.868]    
[09:56:17.868]    // User-defined
[09:56:17.868]    DbgMCU_CR=0x00000007
[09:56:17.869]    DbgMCU_APB1_Fz=0x00000000
[09:56:17.869]    DbgMCU_APB2_Fz=0x00000000
[09:56:17.869]    DoOptionByteLoading=0x00000000
[09:56:17.869]  </debugvars>
[09:56:17.869]  
[09:56:17.869]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:56:17.870]    <block atomic="false" info="">
[09:56:17.870]      Sequence("CheckID");
[09:56:17.870]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:56:17.870]          <block atomic="false" info="">
[09:56:17.870]            __var pidr1 = 0;
[09:56:17.871]              // -> [pidr1 <= 0x00000000]
[09:56:17.871]            __var pidr2 = 0;
[09:56:17.871]              // -> [pidr2 <= 0x00000000]
[09:56:17.871]            __var jep106id = 0;
[09:56:17.871]              // -> [jep106id <= 0x00000000]
[09:56:17.871]            __var ROMTableBase = 0;
[09:56:17.872]              // -> [ROMTableBase <= 0x00000000]
[09:56:17.872]            __ap = 0;      // AHB-AP
[09:56:17.872]              // -> [__ap <= 0x00000000]
[09:56:17.872]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:56:17.873]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:56:17.873]              // -> [ROMTableBase <= 0xF0000000]
[09:56:17.874]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:56:17.875]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:56:17.875]              // -> [pidr1 <= 0x00000004]
[09:56:17.875]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:56:17.876]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:56:17.877]              // -> [pidr2 <= 0x0000000A]
[09:56:17.877]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:56:17.877]              // -> [jep106id <= 0x00000020]
[09:56:17.878]          </block>
[09:56:17.878]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:56:17.878]            // if-block "jep106id != 0x20"
[09:56:17.878]              // =>  FALSE
[09:56:17.878]            // skip if-block "jep106id != 0x20"
[09:56:17.878]          </control>
[09:56:17.879]        </sequence>
[09:56:17.879]    </block>
[09:56:17.879]  </sequence>
[09:56:17.879]  
[09:56:17.892]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:56:17.892]  
[09:56:17.897]  <debugvars>
[09:56:17.897]    // Pre-defined
[09:56:17.898]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:56:17.898]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:56:17.899]    __dp=0x00000000
[09:56:17.899]    __ap=0x00000000
[09:56:17.899]    __traceout=0x00000000      (Trace Disabled)
[09:56:17.900]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:56:17.901]    __FlashAddr=0x00000000
[09:56:17.902]    __FlashLen=0x00000000
[09:56:17.902]    __FlashArg=0x00000000
[09:56:17.903]    __FlashOp=0x00000000
[09:56:17.903]    __Result=0x00000000
[09:56:17.904]    
[09:56:17.904]    // User-defined
[09:56:17.904]    DbgMCU_CR=0x00000007
[09:56:17.905]    DbgMCU_APB1_Fz=0x00000000
[09:56:17.905]    DbgMCU_APB2_Fz=0x00000000
[09:56:17.906]    DoOptionByteLoading=0x00000000
[09:56:17.906]  </debugvars>
[09:56:17.907]  
[09:56:17.907]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:56:17.907]    <block atomic="false" info="">
[09:56:17.907]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:56:17.909]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:56:17.909]    </block>
[09:56:17.909]    <block atomic="false" info="DbgMCU registers">
[09:56:17.910]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:56:17.910]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[09:56:17.911]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[09:56:17.912]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:56:17.912]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:56:17.913]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:56:17.913]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:56:17.914]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:56:17.914]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:56:17.915]    </block>
[09:56:17.915]  </sequence>
[09:56:17.915]  
[09:56:25.760]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:56:25.760]  
[09:56:25.761]  <debugvars>
[09:56:25.761]    // Pre-defined
[09:56:25.761]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:56:25.761]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:56:25.762]    __dp=0x00000000
[09:56:25.762]    __ap=0x00000000
[09:56:25.762]    __traceout=0x00000000      (Trace Disabled)
[09:56:25.762]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:56:25.762]    __FlashAddr=0x00000000
[09:56:25.763]    __FlashLen=0x00000000
[09:56:25.763]    __FlashArg=0x00000000
[09:56:25.763]    __FlashOp=0x00000000
[09:56:25.763]    __Result=0x00000000
[09:56:25.763]    
[09:56:25.763]    // User-defined
[09:56:25.763]    DbgMCU_CR=0x00000007
[09:56:25.764]    DbgMCU_APB1_Fz=0x00000000
[09:56:25.764]    DbgMCU_APB2_Fz=0x00000000
[09:56:25.764]    DoOptionByteLoading=0x00000000
[09:56:25.764]  </debugvars>
[09:56:25.764]  
[09:56:25.764]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:56:25.765]    <block atomic="false" info="">
[09:56:25.765]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:56:25.765]        // -> [connectionFlash <= 0x00000001]
[09:56:25.765]      __var FLASH_BASE = 0x40022000 ;
[09:56:25.765]        // -> [FLASH_BASE <= 0x40022000]
[09:56:25.766]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:56:25.766]        // -> [FLASH_CR <= 0x40022004]
[09:56:25.766]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:56:25.766]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:56:25.767]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:56:25.767]        // -> [LOCK_BIT <= 0x00000001]
[09:56:25.767]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:56:25.767]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:56:25.768]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:56:25.768]        // -> [FLASH_KEYR <= 0x4002200C]
[09:56:25.769]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:56:25.769]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:56:25.769]      __var FLASH_KEY2 = 0x02030405 ;
[09:56:25.769]        // -> [FLASH_KEY2 <= 0x02030405]
[09:56:25.769]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:56:25.770]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:56:25.770]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:56:25.770]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:56:25.770]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:56:25.770]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:56:25.771]      __var FLASH_CR_Value = 0 ;
[09:56:25.771]        // -> [FLASH_CR_Value <= 0x00000000]
[09:56:25.771]      __var DoDebugPortStop = 1 ;
[09:56:25.771]        // -> [DoDebugPortStop <= 0x00000001]
[09:56:25.772]      __var DP_CTRL_STAT = 0x4 ;
[09:56:25.772]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:56:25.772]      __var DP_SELECT = 0x8 ;
[09:56:25.772]        // -> [DP_SELECT <= 0x00000008]
[09:56:25.773]    </block>
[09:56:25.773]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:56:25.773]      // if-block "connectionFlash && DoOptionByteLoading"
[09:56:25.774]        // =>  FALSE
[09:56:25.774]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:56:25.774]    </control>
[09:56:25.774]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:56:25.775]      // if-block "DoDebugPortStop"
[09:56:25.775]        // =>  TRUE
[09:56:25.775]      <block atomic="false" info="">
[09:56:25.775]        WriteDP(DP_SELECT, 0x00000000);
[09:56:25.776]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:56:25.776]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:56:25.776]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:56:25.777]      </block>
[09:56:25.777]      // end if-block "DoDebugPortStop"
[09:56:25.778]    </control>
[09:56:25.778]  </sequence>
[09:56:25.778]  
[10:02:20.486]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:02:20.486]  
[10:02:20.487]  <debugvars>
[10:02:20.487]    // Pre-defined
[10:02:20.487]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:02:20.487]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:02:20.487]    __dp=0x00000000
[10:02:20.487]    __ap=0x00000000
[10:02:20.487]    __traceout=0x00000000      (Trace Disabled)
[10:02:20.489]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:02:20.489]    __FlashAddr=0x00000000
[10:02:20.489]    __FlashLen=0x00000000
[10:02:20.489]    __FlashArg=0x00000000
[10:02:20.489]    __FlashOp=0x00000000
[10:02:20.489]    __Result=0x00000000
[10:02:20.489]    
[10:02:20.489]    // User-defined
[10:02:20.489]    DbgMCU_CR=0x00000007
[10:02:20.489]    DbgMCU_APB1_Fz=0x00000000
[10:02:20.491]    DbgMCU_APB2_Fz=0x00000000
[10:02:20.491]    DoOptionByteLoading=0x00000000
[10:02:20.491]  </debugvars>
[10:02:20.491]  
[10:02:20.492]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:02:20.492]    <block atomic="false" info="">
[10:02:20.492]      Sequence("CheckID");
[10:02:20.492]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:02:20.492]          <block atomic="false" info="">
[10:02:20.492]            __var pidr1 = 0;
[10:02:20.492]              // -> [pidr1 <= 0x00000000]
[10:02:20.492]            __var pidr2 = 0;
[10:02:20.494]              // -> [pidr2 <= 0x00000000]
[10:02:20.494]            __var jep106id = 0;
[10:02:20.494]              // -> [jep106id <= 0x00000000]
[10:02:20.494]            __var ROMTableBase = 0;
[10:02:20.494]              // -> [ROMTableBase <= 0x00000000]
[10:02:20.494]            __ap = 0;      // AHB-AP
[10:02:20.494]              // -> [__ap <= 0x00000000]
[10:02:20.494]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:02:20.496]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:02:20.496]              // -> [ROMTableBase <= 0xF0000000]
[10:02:20.497]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:02:20.498]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:02:20.499]              // -> [pidr1 <= 0x00000004]
[10:02:20.499]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:02:20.500]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:02:20.500]              // -> [pidr2 <= 0x0000000A]
[10:02:20.501]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:02:20.501]              // -> [jep106id <= 0x00000020]
[10:02:20.501]          </block>
[10:02:20.501]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:02:20.502]            // if-block "jep106id != 0x20"
[10:02:20.502]              // =>  FALSE
[10:02:20.503]            // skip if-block "jep106id != 0x20"
[10:02:20.503]          </control>
[10:02:20.503]        </sequence>
[10:02:20.503]    </block>
[10:02:20.504]  </sequence>
[10:02:20.504]  
[10:02:20.518]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:02:20.518]  
[10:02:20.518]  <debugvars>
[10:02:20.518]    // Pre-defined
[10:02:20.518]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:02:20.519]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:02:20.519]    __dp=0x00000000
[10:02:20.519]    __ap=0x00000000
[10:02:20.519]    __traceout=0x00000000      (Trace Disabled)
[10:02:20.519]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:02:20.519]    __FlashAddr=0x00000000
[10:02:20.519]    __FlashLen=0x00000000
[10:02:20.519]    __FlashArg=0x00000000
[10:02:20.521]    __FlashOp=0x00000000
[10:02:20.521]    __Result=0x00000000
[10:02:20.521]    
[10:02:20.521]    // User-defined
[10:02:20.521]    DbgMCU_CR=0x00000007
[10:02:20.522]    DbgMCU_APB1_Fz=0x00000000
[10:02:20.522]    DbgMCU_APB2_Fz=0x00000000
[10:02:20.522]    DoOptionByteLoading=0x00000000
[10:02:20.522]  </debugvars>
[10:02:20.522]  
[10:02:20.522]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:02:20.522]    <block atomic="false" info="">
[10:02:20.522]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:02:20.524]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:02:20.525]    </block>
[10:02:20.525]    <block atomic="false" info="DbgMCU registers">
[10:02:20.525]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:02:20.526]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[10:02:20.527]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[10:02:20.528]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:02:20.528]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:02:20.529]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:02:20.530]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:02:20.530]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:02:20.531]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:02:20.531]    </block>
[10:02:20.531]  </sequence>
[10:02:20.531]  
[10:02:28.437]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:02:28.437]  
[10:02:28.437]  <debugvars>
[10:02:28.437]    // Pre-defined
[10:02:28.437]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:02:28.437]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:02:28.437]    __dp=0x00000000
[10:02:28.438]    __ap=0x00000000
[10:02:28.438]    __traceout=0x00000000      (Trace Disabled)
[10:02:28.439]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:02:28.439]    __FlashAddr=0x00000000
[10:02:28.439]    __FlashLen=0x00000000
[10:02:28.440]    __FlashArg=0x00000000
[10:02:28.440]    __FlashOp=0x00000000
[10:02:28.440]    __Result=0x00000000
[10:02:28.440]    
[10:02:28.440]    // User-defined
[10:02:28.440]    DbgMCU_CR=0x00000007
[10:02:28.440]    DbgMCU_APB1_Fz=0x00000000
[10:02:28.440]    DbgMCU_APB2_Fz=0x00000000
[10:02:28.442]    DoOptionByteLoading=0x00000000
[10:02:28.442]  </debugvars>
[10:02:28.442]  
[10:02:28.442]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:02:28.442]    <block atomic="false" info="">
[10:02:28.443]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:02:28.443]        // -> [connectionFlash <= 0x00000001]
[10:02:28.443]      __var FLASH_BASE = 0x40022000 ;
[10:02:28.443]        // -> [FLASH_BASE <= 0x40022000]
[10:02:28.443]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:02:28.444]        // -> [FLASH_CR <= 0x40022004]
[10:02:28.444]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:02:28.444]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:02:28.444]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:02:28.445]        // -> [LOCK_BIT <= 0x00000001]
[10:02:28.445]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:02:28.445]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:02:28.445]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:02:28.446]        // -> [FLASH_KEYR <= 0x4002200C]
[10:02:28.446]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:02:28.446]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:02:28.446]      __var FLASH_KEY2 = 0x02030405 ;
[10:02:28.447]        // -> [FLASH_KEY2 <= 0x02030405]
[10:02:28.447]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:02:28.447]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:02:28.447]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:02:28.447]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:02:28.448]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:02:28.448]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:02:28.448]      __var FLASH_CR_Value = 0 ;
[10:02:28.448]        // -> [FLASH_CR_Value <= 0x00000000]
[10:02:28.448]      __var DoDebugPortStop = 1 ;
[10:02:28.449]        // -> [DoDebugPortStop <= 0x00000001]
[10:02:28.449]      __var DP_CTRL_STAT = 0x4 ;
[10:02:28.449]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:02:28.449]      __var DP_SELECT = 0x8 ;
[10:02:28.449]        // -> [DP_SELECT <= 0x00000008]
[10:02:28.450]    </block>
[10:02:28.450]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:02:28.450]      // if-block "connectionFlash && DoOptionByteLoading"
[10:02:28.450]        // =>  FALSE
[10:02:28.451]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:02:28.451]    </control>
[10:02:28.451]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:02:28.451]      // if-block "DoDebugPortStop"
[10:02:28.451]        // =>  TRUE
[10:02:28.452]      <block atomic="false" info="">
[10:02:28.452]        WriteDP(DP_SELECT, 0x00000000);
[10:02:28.452]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:02:28.452]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:02:28.453]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:02:28.453]      </block>
[10:02:28.453]      // end if-block "DoDebugPortStop"
[10:02:28.453]    </control>
[10:02:28.453]  </sequence>
[10:02:28.453]  
[11:05:43.994]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:05:43.994]  
[11:05:44.011]  <debugvars>
[11:05:44.011]    // Pre-defined
[11:05:44.012]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:05:44.012]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:05:44.012]    __dp=0x00000000
[11:05:44.012]    __ap=0x00000000
[11:05:44.012]    __traceout=0x00000000      (Trace Disabled)
[11:05:44.013]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:05:44.013]    __FlashAddr=0x00000000
[11:05:44.014]    __FlashLen=0x00000000
[11:05:44.014]    __FlashArg=0x00000000
[11:05:44.014]    __FlashOp=0x00000000
[11:05:44.014]    __Result=0x00000000
[11:05:44.014]    
[11:05:44.014]    // User-defined
[11:05:44.015]    DbgMCU_CR=0x00000007
[11:05:44.015]    DbgMCU_APB1_Fz=0x00000000
[11:05:44.015]    DbgMCU_APB2_Fz=0x00000000
[11:05:44.015]    DoOptionByteLoading=0x00000000
[11:05:44.015]  </debugvars>
[11:05:44.015]  
[11:05:44.016]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:05:44.016]    <block atomic="false" info="">
[11:05:44.016]      Sequence("CheckID");
[11:05:44.016]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:05:44.017]          <block atomic="false" info="">
[11:05:44.017]            __var pidr1 = 0;
[11:05:44.017]              // -> [pidr1 <= 0x00000000]
[11:05:44.017]            __var pidr2 = 0;
[11:05:44.017]              // -> [pidr2 <= 0x00000000]
[11:05:44.018]            __var jep106id = 0;
[11:05:44.018]              // -> [jep106id <= 0x00000000]
[11:05:44.018]            __var ROMTableBase = 0;
[11:05:44.018]              // -> [ROMTableBase <= 0x00000000]
[11:05:44.018]            __ap = 0;      // AHB-AP
[11:05:44.018]              // -> [__ap <= 0x00000000]
[11:05:44.018]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:05:44.020]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:05:44.021]              // -> [ROMTableBase <= 0xF0000000]
[11:05:44.021]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:05:44.023]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:05:44.023]              // -> [pidr1 <= 0x00000004]
[11:05:44.023]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:05:44.024]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:05:44.025]              // -> [pidr2 <= 0x0000000A]
[11:05:44.025]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:05:44.025]              // -> [jep106id <= 0x00000020]
[11:05:44.026]          </block>
[11:05:44.026]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:05:44.026]            // if-block "jep106id != 0x20"
[11:05:44.026]              // =>  FALSE
[11:05:44.026]            // skip if-block "jep106id != 0x20"
[11:05:44.026]          </control>
[11:05:44.027]        </sequence>
[11:05:44.027]    </block>
[11:05:44.028]  </sequence>
[11:05:44.028]  
[11:05:44.039]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:05:44.039]  
[11:05:44.061]  <debugvars>
[11:05:44.061]    // Pre-defined
[11:05:44.061]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:05:44.061]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:05:44.061]    __dp=0x00000000
[11:05:44.064]    __ap=0x00000000
[11:05:44.064]    __traceout=0x00000000      (Trace Disabled)
[11:05:44.064]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:05:44.064]    __FlashAddr=0x00000000
[11:05:44.064]    __FlashLen=0x00000000
[11:05:44.065]    __FlashArg=0x00000000
[11:05:44.065]    __FlashOp=0x00000000
[11:05:44.065]    __Result=0x00000000
[11:05:44.065]    
[11:05:44.065]    // User-defined
[11:05:44.065]    DbgMCU_CR=0x00000007
[11:05:44.065]    DbgMCU_APB1_Fz=0x00000000
[11:05:44.065]    DbgMCU_APB2_Fz=0x00000000
[11:05:44.065]    DoOptionByteLoading=0x00000000
[11:05:44.067]  </debugvars>
[11:05:44.067]  
[11:05:44.067]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:05:44.067]    <block atomic="false" info="">
[11:05:44.067]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:05:44.068]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:05:44.068]    </block>
[11:05:44.069]    <block atomic="false" info="DbgMCU registers">
[11:05:44.069]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:05:44.069]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:05:44.069]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:05:44.069]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:05:44.072]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:05:44.072]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:05:44.073]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:05:44.074]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:05:44.074]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:05:44.074]    </block>
[11:05:44.074]  </sequence>
[11:05:44.074]  
[11:05:51.995]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:05:51.995]  
[11:05:51.995]  <debugvars>
[11:05:51.995]    // Pre-defined
[11:05:51.995]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:05:51.995]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:05:51.995]    __dp=0x00000000
[11:05:51.995]    __ap=0x00000000
[11:05:51.996]    __traceout=0x00000000      (Trace Disabled)
[11:05:51.996]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:05:51.996]    __FlashAddr=0x00000000
[11:05:51.997]    __FlashLen=0x00000000
[11:05:51.997]    __FlashArg=0x00000000
[11:05:51.997]    __FlashOp=0x00000000
[11:05:51.997]    __Result=0x00000000
[11:05:51.997]    
[11:05:51.997]    // User-defined
[11:05:51.997]    DbgMCU_CR=0x00000007
[11:05:51.998]    DbgMCU_APB1_Fz=0x00000000
[11:05:51.998]    DbgMCU_APB2_Fz=0x00000000
[11:05:51.999]    DoOptionByteLoading=0x00000000
[11:05:51.999]  </debugvars>
[11:05:51.999]  
[11:05:51.999]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:05:51.999]    <block atomic="false" info="">
[11:05:51.999]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:05:51.999]        // -> [connectionFlash <= 0x00000001]
[11:05:52.000]      __var FLASH_BASE = 0x40022000 ;
[11:05:52.000]        // -> [FLASH_BASE <= 0x40022000]
[11:05:52.001]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:05:52.001]        // -> [FLASH_CR <= 0x40022004]
[11:05:52.001]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:05:52.001]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:05:52.001]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:05:52.002]        // -> [LOCK_BIT <= 0x00000001]
[11:05:52.002]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:05:52.002]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:05:52.002]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:05:52.002]        // -> [FLASH_KEYR <= 0x4002200C]
[11:05:52.002]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:05:52.002]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:05:52.002]      __var FLASH_KEY2 = 0x02030405 ;
[11:05:52.002]        // -> [FLASH_KEY2 <= 0x02030405]
[11:05:52.002]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:05:52.002]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:05:52.002]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:05:52.002]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:05:52.002]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:05:52.002]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:05:52.002]      __var FLASH_CR_Value = 0 ;
[11:05:52.002]        // -> [FLASH_CR_Value <= 0x00000000]
[11:05:52.002]      __var DoDebugPortStop = 1 ;
[11:05:52.002]        // -> [DoDebugPortStop <= 0x00000001]
[11:05:52.006]      __var DP_CTRL_STAT = 0x4 ;
[11:05:52.006]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:05:52.006]      __var DP_SELECT = 0x8 ;
[11:05:52.006]        // -> [DP_SELECT <= 0x00000008]
[11:05:52.006]    </block>
[11:05:52.006]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:05:52.006]      // if-block "connectionFlash && DoOptionByteLoading"
[11:05:52.006]        // =>  FALSE
[11:05:52.006]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:05:52.006]    </control>
[11:05:52.006]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:05:52.006]      // if-block "DoDebugPortStop"
[11:05:52.006]        // =>  TRUE
[11:05:52.006]      <block atomic="false" info="">
[11:05:52.006]        WriteDP(DP_SELECT, 0x00000000);
[11:05:52.006]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:05:52.006]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:05:52.006]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:05:52.006]      </block>
[11:05:52.006]      // end if-block "DoDebugPortStop"
[11:05:52.006]    </control>
[11:05:52.011]  </sequence>
[11:05:52.011]  
[11:19:51.667]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:19:51.667]  
[11:19:51.682]  <debugvars>
[11:19:51.682]    // Pre-defined
[11:19:51.683]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:19:51.683]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:19:51.684]    __dp=0x00000000
[11:19:51.684]    __ap=0x00000000
[11:19:51.685]    __traceout=0x00000000      (Trace Disabled)
[11:19:51.685]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:19:51.686]    __FlashAddr=0x00000000
[11:19:51.686]    __FlashLen=0x00000000
[11:19:51.687]    __FlashArg=0x00000000
[11:19:51.687]    __FlashOp=0x00000000
[11:19:51.687]    __Result=0x00000000
[11:19:51.688]    
[11:19:51.688]    // User-defined
[11:19:51.688]    DbgMCU_CR=0x00000007
[11:19:51.688]    DbgMCU_APB1_Fz=0x00000000
[11:19:51.689]    DbgMCU_APB2_Fz=0x00000000
[11:19:51.689]    DoOptionByteLoading=0x00000000
[11:19:51.689]  </debugvars>
[11:19:51.690]  
[11:19:51.690]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:19:51.690]    <block atomic="false" info="">
[11:19:51.692]      Sequence("CheckID");
[11:19:51.692]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:19:51.692]          <block atomic="false" info="">
[11:19:51.693]            __var pidr1 = 0;
[11:19:51.693]              // -> [pidr1 <= 0x00000000]
[11:19:51.693]            __var pidr2 = 0;
[11:19:51.693]              // -> [pidr2 <= 0x00000000]
[11:19:51.694]            __var jep106id = 0;
[11:19:51.694]              // -> [jep106id <= 0x00000000]
[11:19:51.694]            __var ROMTableBase = 0;
[11:19:51.694]              // -> [ROMTableBase <= 0x00000000]
[11:19:51.694]            __ap = 0;      // AHB-AP
[11:19:51.694]              // -> [__ap <= 0x00000000]
[11:19:51.695]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:19:51.696]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:19:51.696]              // -> [ROMTableBase <= 0xF0000000]
[11:19:51.696]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:19:51.698]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:19:51.698]              // -> [pidr1 <= 0x00000004]
[11:19:51.698]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:19:51.699]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:19:51.699]              // -> [pidr2 <= 0x0000000A]
[11:19:51.700]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:19:51.700]              // -> [jep106id <= 0x00000020]
[11:19:51.700]          </block>
[11:19:51.701]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:19:51.701]            // if-block "jep106id != 0x20"
[11:19:51.701]              // =>  FALSE
[11:19:51.701]            // skip if-block "jep106id != 0x20"
[11:19:51.702]          </control>
[11:19:51.702]        </sequence>
[11:19:51.702]    </block>
[11:19:51.702]  </sequence>
[11:19:51.702]  
[11:19:51.714]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:19:51.714]  
[11:19:51.724]  <debugvars>
[11:19:51.724]    // Pre-defined
[11:19:51.724]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:19:51.724]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:19:51.724]    __dp=0x00000000
[11:19:51.724]    __ap=0x00000000
[11:19:51.724]    __traceout=0x00000000      (Trace Disabled)
[11:19:51.724]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:19:51.724]    __FlashAddr=0x00000000
[11:19:51.724]    __FlashLen=0x00000000
[11:19:51.724]    __FlashArg=0x00000000
[11:19:51.724]    __FlashOp=0x00000000
[11:19:51.724]    __Result=0x00000000
[11:19:51.724]    
[11:19:51.724]    // User-defined
[11:19:51.724]    DbgMCU_CR=0x00000007
[11:19:51.724]    DbgMCU_APB1_Fz=0x00000000
[11:19:51.724]    DbgMCU_APB2_Fz=0x00000000
[11:19:51.729]    DoOptionByteLoading=0x00000000
[11:19:51.729]  </debugvars>
[11:19:51.729]  
[11:19:51.729]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:19:51.729]    <block atomic="false" info="">
[11:19:51.729]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:19:51.729]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:19:51.729]    </block>
[11:19:51.729]    <block atomic="false" info="DbgMCU registers">
[11:19:51.729]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:19:51.729]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:19:51.734]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:19:51.734]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:19:51.736]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:19:51.736]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:19:51.736]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:19:51.736]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:19:51.738]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:19:51.738]    </block>
[11:19:51.739]  </sequence>
[11:19:51.739]  
[11:19:59.640]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:19:59.640]  
[11:19:59.640]  <debugvars>
[11:19:59.640]    // Pre-defined
[11:19:59.640]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:19:59.642]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:19:59.642]    __dp=0x00000000
[11:19:59.642]    __ap=0x00000000
[11:19:59.642]    __traceout=0x00000000      (Trace Disabled)
[11:19:59.642]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:19:59.642]    __FlashAddr=0x00000000
[11:19:59.642]    __FlashLen=0x00000000
[11:19:59.642]    __FlashArg=0x00000000
[11:19:59.642]    __FlashOp=0x00000000
[11:19:59.642]    __Result=0x00000000
[11:19:59.642]    
[11:19:59.642]    // User-defined
[11:19:59.642]    DbgMCU_CR=0x00000007
[11:19:59.642]    DbgMCU_APB1_Fz=0x00000000
[11:19:59.645]    DbgMCU_APB2_Fz=0x00000000
[11:19:59.645]    DoOptionByteLoading=0x00000000
[11:19:59.645]  </debugvars>
[11:19:59.645]  
[11:19:59.646]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:19:59.646]    <block atomic="false" info="">
[11:19:59.646]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:19:59.646]        // -> [connectionFlash <= 0x00000001]
[11:19:59.647]      __var FLASH_BASE = 0x40022000 ;
[11:19:59.647]        // -> [FLASH_BASE <= 0x40022000]
[11:19:59.647]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:19:59.647]        // -> [FLASH_CR <= 0x40022004]
[11:19:59.647]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:19:59.647]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:19:59.647]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:19:59.647]        // -> [LOCK_BIT <= 0x00000001]
[11:19:59.647]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:19:59.647]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:19:59.647]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:19:59.649]        // -> [FLASH_KEYR <= 0x4002200C]
[11:19:59.650]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:19:59.650]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:19:59.650]      __var FLASH_KEY2 = 0x02030405 ;
[11:19:59.650]        // -> [FLASH_KEY2 <= 0x02030405]
[11:19:59.650]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:19:59.651]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:19:59.651]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:19:59.651]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:19:59.652]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:19:59.652]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:19:59.652]      __var FLASH_CR_Value = 0 ;
[11:19:59.652]        // -> [FLASH_CR_Value <= 0x00000000]
[11:19:59.653]      __var DoDebugPortStop = 1 ;
[11:19:59.653]        // -> [DoDebugPortStop <= 0x00000001]
[11:19:59.653]      __var DP_CTRL_STAT = 0x4 ;
[11:19:59.653]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:19:59.653]      __var DP_SELECT = 0x8 ;
[11:19:59.653]        // -> [DP_SELECT <= 0x00000008]
[11:19:59.653]    </block>
[11:19:59.653]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:19:59.653]      // if-block "connectionFlash && DoOptionByteLoading"
[11:19:59.653]        // =>  FALSE
[11:19:59.655]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:19:59.655]    </control>
[11:19:59.656]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:19:59.656]      // if-block "DoDebugPortStop"
[11:19:59.656]        // =>  TRUE
[11:19:59.656]      <block atomic="false" info="">
[11:19:59.656]        WriteDP(DP_SELECT, 0x00000000);
[11:19:59.656]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:19:59.656]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:19:59.656]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:19:59.656]      </block>
[11:19:59.658]      // end if-block "DoDebugPortStop"
[11:19:59.658]    </control>
[11:19:59.658]  </sequence>
[11:19:59.658]  
[11:26:52.598]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:26:52.598]  
[11:26:52.607]  <debugvars>
[11:26:52.608]    // Pre-defined
[11:26:52.608]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:26:52.608]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:26:52.608]    __dp=0x00000000
[11:26:52.608]    __ap=0x00000000
[11:26:52.608]    __traceout=0x00000000      (Trace Disabled)
[11:26:52.608]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:26:52.608]    __FlashAddr=0x00000000
[11:26:52.610]    __FlashLen=0x00000000
[11:26:52.610]    __FlashArg=0x00000000
[11:26:52.610]    __FlashOp=0x00000000
[11:26:52.610]    __Result=0x00000000
[11:26:52.610]    
[11:26:52.610]    // User-defined
[11:26:52.610]    DbgMCU_CR=0x00000007
[11:26:52.610]    DbgMCU_APB1_Fz=0x00000000
[11:26:52.610]    DbgMCU_APB2_Fz=0x00000000
[11:26:52.611]    DoOptionByteLoading=0x00000000
[11:26:52.611]  </debugvars>
[11:26:52.612]  
[11:26:52.612]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:26:52.612]    <block atomic="false" info="">
[11:26:52.612]      Sequence("CheckID");
[11:26:52.612]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:26:52.612]          <block atomic="false" info="">
[11:26:52.613]            __var pidr1 = 0;
[11:26:52.614]              // -> [pidr1 <= 0x00000000]
[11:26:52.614]            __var pidr2 = 0;
[11:26:52.614]              // -> [pidr2 <= 0x00000000]
[11:26:52.614]            __var jep106id = 0;
[11:26:52.615]              // -> [jep106id <= 0x00000000]
[11:26:52.615]            __var ROMTableBase = 0;
[11:26:52.615]              // -> [ROMTableBase <= 0x00000000]
[11:26:52.615]            __ap = 0;      // AHB-AP
[11:26:52.615]              // -> [__ap <= 0x00000000]
[11:26:52.616]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:26:52.616]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:26:52.617]              // -> [ROMTableBase <= 0xF0000000]
[11:26:52.617]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:26:52.618]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:26:52.618]              // -> [pidr1 <= 0x00000004]
[11:26:52.618]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:26:52.619]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:26:52.619]              // -> [pidr2 <= 0x0000000A]
[11:26:52.620]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:26:52.620]              // -> [jep106id <= 0x00000020]
[11:26:52.620]          </block>
[11:26:52.621]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:26:52.621]            // if-block "jep106id != 0x20"
[11:26:52.621]              // =>  FALSE
[11:26:52.621]            // skip if-block "jep106id != 0x20"
[11:26:52.621]          </control>
[11:26:52.621]        </sequence>
[11:26:52.621]    </block>
[11:26:52.622]  </sequence>
[11:26:52.622]  
[11:26:52.634]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:26:52.634]  
[11:26:52.635]  <debugvars>
[11:26:52.635]    // Pre-defined
[11:26:52.635]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:26:52.635]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:26:52.636]    __dp=0x00000000
[11:26:52.636]    __ap=0x00000000
[11:26:52.636]    __traceout=0x00000000      (Trace Disabled)
[11:26:52.637]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:26:52.637]    __FlashAddr=0x00000000
[11:26:52.637]    __FlashLen=0x00000000
[11:26:52.637]    __FlashArg=0x00000000
[11:26:52.638]    __FlashOp=0x00000000
[11:26:52.638]    __Result=0x00000000
[11:26:52.638]    
[11:26:52.638]    // User-defined
[11:26:52.639]    DbgMCU_CR=0x00000007
[11:26:52.639]    DbgMCU_APB1_Fz=0x00000000
[11:26:52.639]    DbgMCU_APB2_Fz=0x00000000
[11:26:52.640]    DoOptionByteLoading=0x00000000
[11:26:52.640]  </debugvars>
[11:26:52.640]  
[11:26:52.640]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:26:52.640]    <block atomic="false" info="">
[11:26:52.640]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:26:52.641]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:26:52.642]    </block>
[11:26:52.642]    <block atomic="false" info="DbgMCU registers">
[11:26:52.642]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:26:52.643]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:26:52.645]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:26:52.645]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:26:52.646]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:26:52.646]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:26:52.647]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:26:52.648]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:26:52.648]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:26:52.648]    </block>
[11:26:52.648]  </sequence>
[11:26:52.648]  
[11:27:00.705]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:27:00.705]  
[11:27:00.706]  <debugvars>
[11:27:00.706]    // Pre-defined
[11:27:00.706]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:27:00.706]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:27:00.707]    __dp=0x00000000
[11:27:00.707]    __ap=0x00000000
[11:27:00.707]    __traceout=0x00000000      (Trace Disabled)
[11:27:00.707]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:27:00.707]    __FlashAddr=0x00000000
[11:27:00.708]    __FlashLen=0x00000000
[11:27:00.708]    __FlashArg=0x00000000
[11:27:00.708]    __FlashOp=0x00000000
[11:27:00.708]    __Result=0x00000000
[11:27:00.708]    
[11:27:00.708]    // User-defined
[11:27:00.709]    DbgMCU_CR=0x00000007
[11:27:00.709]    DbgMCU_APB1_Fz=0x00000000
[11:27:00.709]    DbgMCU_APB2_Fz=0x00000000
[11:27:00.709]    DoOptionByteLoading=0x00000000
[11:27:00.710]  </debugvars>
[11:27:00.710]  
[11:27:00.710]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:27:00.710]    <block atomic="false" info="">
[11:27:00.710]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:27:00.711]        // -> [connectionFlash <= 0x00000001]
[11:27:00.711]      __var FLASH_BASE = 0x40022000 ;
[11:27:00.711]        // -> [FLASH_BASE <= 0x40022000]
[11:27:00.711]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:27:00.711]        // -> [FLASH_CR <= 0x40022004]
[11:27:00.712]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:27:00.712]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:27:00.713]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:27:00.713]        // -> [LOCK_BIT <= 0x00000001]
[11:27:00.713]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:27:00.713]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:27:00.713]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:27:00.713]        // -> [FLASH_KEYR <= 0x4002200C]
[11:27:00.713]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:27:00.715]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:27:00.715]      __var FLASH_KEY2 = 0x02030405 ;
[11:27:00.717]        // -> [FLASH_KEY2 <= 0x02030405]
[11:27:00.717]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:27:00.718]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:27:00.718]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:27:00.718]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:27:00.718]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:27:00.720]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:27:00.722]      __var FLASH_CR_Value = 0 ;
[11:27:00.723]        // -> [FLASH_CR_Value <= 0x00000000]
[11:27:00.724]      __var DoDebugPortStop = 1 ;
[11:27:00.724]        // -> [DoDebugPortStop <= 0x00000001]
[11:27:00.724]      __var DP_CTRL_STAT = 0x4 ;
[11:27:00.724]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:27:00.725]      __var DP_SELECT = 0x8 ;
[11:27:00.725]        // -> [DP_SELECT <= 0x00000008]
[11:27:00.725]    </block>
[11:27:00.727]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:27:00.727]      // if-block "connectionFlash && DoOptionByteLoading"
[11:27:00.729]        // =>  FALSE
[11:27:00.729]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:27:00.729]    </control>
[11:27:00.731]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:27:00.731]      // if-block "DoDebugPortStop"
[11:27:00.731]        // =>  TRUE
[11:27:00.732]      <block atomic="false" info="">
[11:27:00.732]        WriteDP(DP_SELECT, 0x00000000);
[11:27:00.732]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:27:00.732]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:27:00.732]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:27:00.732]      </block>
[11:27:00.734]      // end if-block "DoDebugPortStop"
[11:27:00.734]    </control>
[11:27:00.734]  </sequence>
[11:27:00.734]  
[11:32:13.230]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:32:13.230]  
[11:32:13.230]  <debugvars>
[11:32:13.230]    // Pre-defined
[11:32:13.230]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:32:13.230]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:32:13.230]    __dp=0x00000000
[11:32:13.230]    __ap=0x00000000
[11:32:13.230]    __traceout=0x00000000      (Trace Disabled)
[11:32:13.230]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:32:13.230]    __FlashAddr=0x00000000
[11:32:13.232]    __FlashLen=0x00000000
[11:32:13.232]    __FlashArg=0x00000000
[11:32:13.232]    __FlashOp=0x00000000
[11:32:13.232]    __Result=0x00000000
[11:32:13.232]    
[11:32:13.232]    // User-defined
[11:32:13.232]    DbgMCU_CR=0x00000007
[11:32:13.232]    DbgMCU_APB1_Fz=0x00000000
[11:32:13.232]    DbgMCU_APB2_Fz=0x00000000
[11:32:13.234]    DoOptionByteLoading=0x00000000
[11:32:13.234]  </debugvars>
[11:32:13.234]  
[11:32:13.234]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:32:13.234]    <block atomic="false" info="">
[11:32:13.235]      Sequence("CheckID");
[11:32:13.235]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:32:13.235]          <block atomic="false" info="">
[11:32:13.235]            __var pidr1 = 0;
[11:32:13.235]              // -> [pidr1 <= 0x00000000]
[11:32:13.236]            __var pidr2 = 0;
[11:32:13.236]              // -> [pidr2 <= 0x00000000]
[11:32:13.236]            __var jep106id = 0;
[11:32:13.236]              // -> [jep106id <= 0x00000000]
[11:32:13.236]            __var ROMTableBase = 0;
[11:32:13.236]              // -> [ROMTableBase <= 0x00000000]
[11:32:13.237]            __ap = 0;      // AHB-AP
[11:32:13.237]              // -> [__ap <= 0x00000000]
[11:32:13.237]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:32:13.238]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:32:13.238]              // -> [ROMTableBase <= 0xF0000000]
[11:32:13.238]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:32:13.239]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:32:13.239]              // -> [pidr1 <= 0x00000004]
[11:32:13.239]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:32:13.240]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:32:13.240]              // -> [pidr2 <= 0x0000000A]
[11:32:13.240]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:32:13.240]              // -> [jep106id <= 0x00000020]
[11:32:13.241]          </block>
[11:32:13.241]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:32:13.241]            // if-block "jep106id != 0x20"
[11:32:13.241]              // =>  FALSE
[11:32:13.241]            // skip if-block "jep106id != 0x20"
[11:32:13.242]          </control>
[11:32:13.242]        </sequence>
[11:32:13.242]    </block>
[11:32:13.242]  </sequence>
[11:32:13.243]  
[11:32:13.253]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:32:13.253]  
[11:32:13.254]  <debugvars>
[11:32:13.254]    // Pre-defined
[11:32:13.255]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:32:13.255]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:32:13.255]    __dp=0x00000000
[11:32:13.255]    __ap=0x00000000
[11:32:13.256]    __traceout=0x00000000      (Trace Disabled)
[11:32:13.256]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:32:13.256]    __FlashAddr=0x00000000
[11:32:13.257]    __FlashLen=0x00000000
[11:32:13.257]    __FlashArg=0x00000000
[11:32:13.257]    __FlashOp=0x00000000
[11:32:13.257]    __Result=0x00000000
[11:32:13.257]    
[11:32:13.257]    // User-defined
[11:32:13.258]    DbgMCU_CR=0x00000007
[11:32:13.258]    DbgMCU_APB1_Fz=0x00000000
[11:32:13.258]    DbgMCU_APB2_Fz=0x00000000
[11:32:13.258]    DoOptionByteLoading=0x00000000
[11:32:13.258]  </debugvars>
[11:32:13.258]  
[11:32:13.259]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:32:13.259]    <block atomic="false" info="">
[11:32:13.259]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:32:13.260]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:32:13.260]    </block>
[11:32:13.260]    <block atomic="false" info="DbgMCU registers">
[11:32:13.261]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:32:13.262]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:32:13.262]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:32:13.262]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:32:13.264]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:32:13.264]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:32:13.264]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:32:13.264]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:32:13.266]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:32:13.266]    </block>
[11:32:13.266]  </sequence>
[11:32:13.266]  
[11:32:25.084]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:32:25.084]  
[11:32:25.084]  <debugvars>
[11:32:25.084]    // Pre-defined
[11:32:25.084]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:32:25.086]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:32:25.086]    __dp=0x00000000
[11:32:25.086]    __ap=0x00000000
[11:32:25.087]    __traceout=0x00000000      (Trace Disabled)
[11:32:25.087]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:32:25.087]    __FlashAddr=0x00000000
[11:32:25.087]    __FlashLen=0x00000000
[11:32:25.087]    __FlashArg=0x00000000
[11:32:25.087]    __FlashOp=0x00000000
[11:32:25.087]    __Result=0x00000000
[11:32:25.087]    
[11:32:25.087]    // User-defined
[11:32:25.089]    DbgMCU_CR=0x00000007
[11:32:25.089]    DbgMCU_APB1_Fz=0x00000000
[11:32:25.089]    DbgMCU_APB2_Fz=0x00000000
[11:32:25.089]    DoOptionByteLoading=0x00000000
[11:32:25.089]  </debugvars>
[11:32:25.089]  
[11:32:25.089]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:32:25.091]    <block atomic="false" info="">
[11:32:25.092]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:32:25.092]        // -> [connectionFlash <= 0x00000001]
[11:32:25.092]      __var FLASH_BASE = 0x40022000 ;
[11:32:25.092]        // -> [FLASH_BASE <= 0x40022000]
[11:32:25.093]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:32:25.093]        // -> [FLASH_CR <= 0x40022004]
[11:32:25.093]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:32:25.093]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:32:25.093]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:32:25.094]        // -> [LOCK_BIT <= 0x00000001]
[11:32:25.094]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:32:25.094]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:32:25.094]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:32:25.094]        // -> [FLASH_KEYR <= 0x4002200C]
[11:32:25.094]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:32:25.095]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:32:25.095]      __var FLASH_KEY2 = 0x02030405 ;
[11:32:25.095]        // -> [FLASH_KEY2 <= 0x02030405]
[11:32:25.095]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:32:25.095]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:32:25.096]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:32:25.096]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:32:25.096]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:32:25.096]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:32:25.096]      __var FLASH_CR_Value = 0 ;
[11:32:25.097]        // -> [FLASH_CR_Value <= 0x00000000]
[11:32:25.097]      __var DoDebugPortStop = 1 ;
[11:32:25.097]        // -> [DoDebugPortStop <= 0x00000001]
[11:32:25.097]      __var DP_CTRL_STAT = 0x4 ;
[11:32:25.097]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:32:25.097]      __var DP_SELECT = 0x8 ;
[11:32:25.098]        // -> [DP_SELECT <= 0x00000008]
[11:32:25.098]    </block>
[11:32:25.098]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:32:25.099]      // if-block "connectionFlash && DoOptionByteLoading"
[11:32:25.099]        // =>  FALSE
[11:32:25.099]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:32:25.099]    </control>
[11:32:25.100]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:32:25.100]      // if-block "DoDebugPortStop"
[11:32:25.100]        // =>  TRUE
[11:32:25.100]      <block atomic="false" info="">
[11:32:25.100]        WriteDP(DP_SELECT, 0x00000000);
[11:32:25.101]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:32:25.101]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:32:25.102]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:32:25.102]      </block>
[11:32:25.102]      // end if-block "DoDebugPortStop"
[11:32:25.102]    </control>
[11:32:25.102]  </sequence>
[11:32:25.103]  
[11:34:45.899]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:34:45.899]  
[11:34:45.899]  <debugvars>
[11:34:45.900]    // Pre-defined
[11:34:45.900]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:34:45.900]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:34:45.900]    __dp=0x00000000
[11:34:45.900]    __ap=0x00000000
[11:34:45.901]    __traceout=0x00000000      (Trace Disabled)
[11:34:45.901]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:34:45.901]    __FlashAddr=0x00000000
[11:34:45.901]    __FlashLen=0x00000000
[11:34:45.901]    __FlashArg=0x00000000
[11:34:45.901]    __FlashOp=0x00000000
[11:34:45.902]    __Result=0x00000000
[11:34:45.902]    
[11:34:45.902]    // User-defined
[11:34:45.902]    DbgMCU_CR=0x00000007
[11:34:45.902]    DbgMCU_APB1_Fz=0x00000000
[11:34:45.903]    DbgMCU_APB2_Fz=0x00000000
[11:34:45.903]    DoOptionByteLoading=0x00000000
[11:34:45.903]  </debugvars>
[11:34:45.904]  
[11:34:45.904]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:34:45.904]    <block atomic="false" info="">
[11:34:45.904]      Sequence("CheckID");
[11:34:45.904]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:34:45.904]          <block atomic="false" info="">
[11:34:45.905]            __var pidr1 = 0;
[11:34:45.905]              // -> [pidr1 <= 0x00000000]
[11:34:45.905]            __var pidr2 = 0;
[11:34:45.905]              // -> [pidr2 <= 0x00000000]
[11:34:45.905]            __var jep106id = 0;
[11:34:45.906]              // -> [jep106id <= 0x00000000]
[11:34:45.906]            __var ROMTableBase = 0;
[11:34:45.906]              // -> [ROMTableBase <= 0x00000000]
[11:34:45.906]            __ap = 0;      // AHB-AP
[11:34:45.906]              // -> [__ap <= 0x00000000]
[11:34:45.907]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:34:45.907]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:34:45.907]              // -> [ROMTableBase <= 0xF0000000]
[11:34:45.907]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:34:45.908]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:34:45.909]              // -> [pidr1 <= 0x00000004]
[11:34:45.909]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:34:45.910]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:34:45.911]              // -> [pidr2 <= 0x0000000A]
[11:34:45.911]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:34:45.911]              // -> [jep106id <= 0x00000020]
[11:34:45.911]          </block>
[11:34:45.912]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:34:45.912]            // if-block "jep106id != 0x20"
[11:34:45.912]              // =>  FALSE
[11:34:45.913]            // skip if-block "jep106id != 0x20"
[11:34:45.913]          </control>
[11:34:45.913]        </sequence>
[11:34:45.913]    </block>
[11:34:45.913]  </sequence>
[11:34:45.914]  
[11:34:45.925]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:34:45.925]  
[11:34:45.925]  <debugvars>
[11:34:45.925]    // Pre-defined
[11:34:45.926]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:34:45.926]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:34:45.926]    __dp=0x00000000
[11:34:45.926]    __ap=0x00000000
[11:34:45.926]    __traceout=0x00000000      (Trace Disabled)
[11:34:45.927]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:34:45.927]    __FlashAddr=0x00000000
[11:34:45.927]    __FlashLen=0x00000000
[11:34:45.928]    __FlashArg=0x00000000
[11:34:45.928]    __FlashOp=0x00000000
[11:34:45.928]    __Result=0x00000000
[11:34:45.928]    
[11:34:45.928]    // User-defined
[11:34:45.928]    DbgMCU_CR=0x00000007
[11:34:45.928]    DbgMCU_APB1_Fz=0x00000000
[11:34:45.928]    DbgMCU_APB2_Fz=0x00000000
[11:34:45.929]    DoOptionByteLoading=0x00000000
[11:34:45.929]  </debugvars>
[11:34:45.929]  
[11:34:45.929]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:34:45.929]    <block atomic="false" info="">
[11:34:45.929]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:34:45.931]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:34:45.931]    </block>
[11:34:45.931]    <block atomic="false" info="DbgMCU registers">
[11:34:45.931]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:34:45.932]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:34:45.933]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:34:45.933]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:34:45.933]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:34:45.933]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:34:45.935]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:34:45.935]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:34:45.936]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:34:45.936]    </block>
[11:34:45.936]  </sequence>
[11:34:45.937]  
[11:34:53.663]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:34:53.663]  
[11:34:53.664]  <debugvars>
[11:34:53.665]    // Pre-defined
[11:34:53.665]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:34:53.665]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:34:53.666]    __dp=0x00000000
[11:34:53.666]    __ap=0x00000000
[11:34:53.667]    __traceout=0x00000000      (Trace Disabled)
[11:34:53.667]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:34:53.668]    __FlashAddr=0x00000000
[11:34:53.668]    __FlashLen=0x00000000
[11:34:53.668]    __FlashArg=0x00000000
[11:34:53.669]    __FlashOp=0x00000000
[11:34:53.670]    __Result=0x00000000
[11:34:53.670]    
[11:34:53.670]    // User-defined
[11:34:53.671]    DbgMCU_CR=0x00000007
[11:34:53.671]    DbgMCU_APB1_Fz=0x00000000
[11:34:53.671]    DbgMCU_APB2_Fz=0x00000000
[11:34:53.671]    DoOptionByteLoading=0x00000000
[11:34:53.671]  </debugvars>
[11:34:53.672]  
[11:34:53.672]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:34:53.672]    <block atomic="false" info="">
[11:34:53.672]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:34:53.672]        // -> [connectionFlash <= 0x00000001]
[11:34:53.672]      __var FLASH_BASE = 0x40022000 ;
[11:34:53.672]        // -> [FLASH_BASE <= 0x40022000]
[11:34:53.673]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:34:53.673]        // -> [FLASH_CR <= 0x40022004]
[11:34:53.674]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:34:53.674]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:34:53.674]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:34:53.674]        // -> [LOCK_BIT <= 0x00000001]
[11:34:53.674]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:34:53.674]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:34:53.675]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:34:53.675]        // -> [FLASH_KEYR <= 0x4002200C]
[11:34:53.675]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:34:53.675]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:34:53.675]      __var FLASH_KEY2 = 0x02030405 ;
[11:34:53.676]        // -> [FLASH_KEY2 <= 0x02030405]
[11:34:53.676]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:34:53.676]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:34:53.676]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:34:53.676]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:34:53.676]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:34:53.677]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:34:53.677]      __var FLASH_CR_Value = 0 ;
[11:34:53.677]        // -> [FLASH_CR_Value <= 0x00000000]
[11:34:53.677]      __var DoDebugPortStop = 1 ;
[11:34:53.678]        // -> [DoDebugPortStop <= 0x00000001]
[11:34:53.678]      __var DP_CTRL_STAT = 0x4 ;
[11:34:53.678]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:34:53.679]      __var DP_SELECT = 0x8 ;
[11:34:53.679]        // -> [DP_SELECT <= 0x00000008]
[11:34:53.679]    </block>
[11:34:53.680]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:34:53.680]      // if-block "connectionFlash && DoOptionByteLoading"
[11:34:53.680]        // =>  FALSE
[11:34:53.680]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:34:53.680]    </control>
[11:34:53.681]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:34:53.681]      // if-block "DoDebugPortStop"
[11:34:53.681]        // =>  TRUE
[11:34:53.681]      <block atomic="false" info="">
[11:34:53.681]        WriteDP(DP_SELECT, 0x00000000);
[11:34:53.682]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:34:53.682]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:34:53.683]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:34:53.683]      </block>
[11:34:53.683]      // end if-block "DoDebugPortStop"
[11:34:53.683]    </control>
[11:34:53.684]  </sequence>
[11:34:53.684]  
[11:36:35.440]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:36:35.440]  
[11:36:35.441]  <debugvars>
[11:36:35.441]    // Pre-defined
[11:36:35.441]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:36:35.441]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:36:35.442]    __dp=0x00000000
[11:36:35.442]    __ap=0x00000000
[11:36:35.443]    __traceout=0x00000000      (Trace Disabled)
[11:36:35.443]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:36:35.443]    __FlashAddr=0x00000000
[11:36:35.443]    __FlashLen=0x00000000
[11:36:35.443]    __FlashArg=0x00000000
[11:36:35.443]    __FlashOp=0x00000000
[11:36:35.444]    __Result=0x00000000
[11:36:35.444]    
[11:36:35.444]    // User-defined
[11:36:35.444]    DbgMCU_CR=0x00000007
[11:36:35.444]    DbgMCU_APB1_Fz=0x00000000
[11:36:35.444]    DbgMCU_APB2_Fz=0x00000000
[11:36:35.444]    DoOptionByteLoading=0x00000000
[11:36:35.445]  </debugvars>
[11:36:35.445]  
[11:36:35.445]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:36:35.445]    <block atomic="false" info="">
[11:36:35.446]      Sequence("CheckID");
[11:36:35.446]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:36:35.447]          <block atomic="false" info="">
[11:36:35.447]            __var pidr1 = 0;
[11:36:35.447]              // -> [pidr1 <= 0x00000000]
[11:36:35.447]            __var pidr2 = 0;
[11:36:35.447]              // -> [pidr2 <= 0x00000000]
[11:36:35.447]            __var jep106id = 0;
[11:36:35.447]              // -> [jep106id <= 0x00000000]
[11:36:35.447]            __var ROMTableBase = 0;
[11:36:35.447]              // -> [ROMTableBase <= 0x00000000]
[11:36:35.447]            __ap = 0;      // AHB-AP
[11:36:35.447]              // -> [__ap <= 0x00000000]
[11:36:35.449]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:36:35.449]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:36:35.450]              // -> [ROMTableBase <= 0xF0000000]
[11:36:35.450]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:36:35.452]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:36:35.453]              // -> [pidr1 <= 0x00000004]
[11:36:35.453]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:36:35.454]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:36:35.454]              // -> [pidr2 <= 0x0000000A]
[11:36:35.454]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:36:35.454]              // -> [jep106id <= 0x00000020]
[11:36:35.455]          </block>
[11:36:35.455]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:36:35.455]            // if-block "jep106id != 0x20"
[11:36:35.455]              // =>  FALSE
[11:36:35.455]            // skip if-block "jep106id != 0x20"
[11:36:35.455]          </control>
[11:36:35.455]        </sequence>
[11:36:35.455]    </block>
[11:36:35.455]  </sequence>
[11:36:35.455]  
[11:36:35.464]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:36:35.464]  
[11:36:35.464]  <debugvars>
[11:36:35.469]    // Pre-defined
[11:36:35.469]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:36:35.469]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:36:35.470]    __dp=0x00000000
[11:36:35.470]    __ap=0x00000000
[11:36:35.470]    __traceout=0x00000000      (Trace Disabled)
[11:36:35.470]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:36:35.470]    __FlashAddr=0x00000000
[11:36:35.470]    __FlashLen=0x00000000
[11:36:35.470]    __FlashArg=0x00000000
[11:36:35.470]    __FlashOp=0x00000000
[11:36:35.470]    __Result=0x00000000
[11:36:35.470]    
[11:36:35.470]    // User-defined
[11:36:35.470]    DbgMCU_CR=0x00000007
[11:36:35.470]    DbgMCU_APB1_Fz=0x00000000
[11:36:35.470]    DbgMCU_APB2_Fz=0x00000000
[11:36:35.470]    DoOptionByteLoading=0x00000000
[11:36:35.470]  </debugvars>
[11:36:35.470]  
[11:36:35.470]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:36:35.475]    <block atomic="false" info="">
[11:36:35.475]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:36:35.475]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:36:35.475]    </block>
[11:36:35.475]    <block atomic="false" info="DbgMCU registers">
[11:36:35.475]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:36:35.475]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:36:35.475]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:36:35.475]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:36:35.475]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:36:35.475]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:36:35.480]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:36:35.480]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:36:35.480]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:36:35.480]    </block>
[11:36:35.480]  </sequence>
[11:36:35.480]  
[11:36:43.405]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:36:43.405]  
[11:36:43.406]  <debugvars>
[11:36:43.407]    // Pre-defined
[11:36:43.407]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:36:43.408]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:36:43.408]    __dp=0x00000000
[11:36:43.409]    __ap=0x00000000
[11:36:43.409]    __traceout=0x00000000      (Trace Disabled)
[11:36:43.410]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:36:43.410]    __FlashAddr=0x00000000
[11:36:43.410]    __FlashLen=0x00000000
[11:36:43.411]    __FlashArg=0x00000000
[11:36:43.411]    __FlashOp=0x00000000
[11:36:43.412]    __Result=0x00000000
[11:36:43.412]    
[11:36:43.412]    // User-defined
[11:36:43.412]    DbgMCU_CR=0x00000007
[11:36:43.413]    DbgMCU_APB1_Fz=0x00000000
[11:36:43.413]    DbgMCU_APB2_Fz=0x00000000
[11:36:43.413]    DoOptionByteLoading=0x00000000
[11:36:43.413]  </debugvars>
[11:36:43.413]  
[11:36:43.413]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:36:43.413]    <block atomic="false" info="">
[11:36:43.415]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:36:43.415]        // -> [connectionFlash <= 0x00000001]
[11:36:43.415]      __var FLASH_BASE = 0x40022000 ;
[11:36:43.415]        // -> [FLASH_BASE <= 0x40022000]
[11:36:43.415]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:36:43.417]        // -> [FLASH_CR <= 0x40022004]
[11:36:43.417]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:36:43.417]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:36:43.418]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:36:43.418]        // -> [LOCK_BIT <= 0x00000001]
[11:36:43.419]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:36:43.419]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:36:43.419]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:36:43.420]        // -> [FLASH_KEYR <= 0x4002200C]
[11:36:43.420]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:36:43.420]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:36:43.420]      __var FLASH_KEY2 = 0x02030405 ;
[11:36:43.420]        // -> [FLASH_KEY2 <= 0x02030405]
[11:36:43.421]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:36:43.421]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:36:43.421]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:36:43.421]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:36:43.421]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:36:43.421]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:36:43.422]      __var FLASH_CR_Value = 0 ;
[11:36:43.422]        // -> [FLASH_CR_Value <= 0x00000000]
[11:36:43.422]      __var DoDebugPortStop = 1 ;
[11:36:43.422]        // -> [DoDebugPortStop <= 0x00000001]
[11:36:43.422]      __var DP_CTRL_STAT = 0x4 ;
[11:36:43.423]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:36:43.423]      __var DP_SELECT = 0x8 ;
[11:36:43.423]        // -> [DP_SELECT <= 0x00000008]
[11:36:43.423]    </block>
[11:36:43.423]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:36:43.424]      // if-block "connectionFlash && DoOptionByteLoading"
[11:36:43.424]        // =>  FALSE
[11:36:43.424]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:36:43.424]    </control>
[11:36:43.424]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:36:43.424]      // if-block "DoDebugPortStop"
[11:36:43.425]        // =>  TRUE
[11:36:43.425]      <block atomic="false" info="">
[11:36:43.425]        WriteDP(DP_SELECT, 0x00000000);
[11:36:43.425]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:36:43.426]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:36:43.426]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:36:43.427]      </block>
[11:36:43.427]      // end if-block "DoDebugPortStop"
[11:36:43.427]    </control>
[11:36:43.427]  </sequence>
[11:36:43.428]  
[11:57:26.159]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:57:26.159]  
[11:57:26.171]  <debugvars>
[11:57:26.171]    // Pre-defined
[11:57:26.172]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:57:26.172]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:57:26.172]    __dp=0x00000000
[11:57:26.172]    __ap=0x00000000
[11:57:26.173]    __traceout=0x00000000      (Trace Disabled)
[11:57:26.173]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:57:26.173]    __FlashAddr=0x00000000
[11:57:26.173]    __FlashLen=0x00000000
[11:57:26.174]    __FlashArg=0x00000000
[11:57:26.174]    __FlashOp=0x00000000
[11:57:26.174]    __Result=0x00000000
[11:57:26.174]    
[11:57:26.174]    // User-defined
[11:57:26.174]    DbgMCU_CR=0x00000007
[11:57:26.174]    DbgMCU_APB1_Fz=0x00000000
[11:57:26.175]    DbgMCU_APB2_Fz=0x00000000
[11:57:26.175]    DoOptionByteLoading=0x00000000
[11:57:26.175]  </debugvars>
[11:57:26.175]  
[11:57:26.175]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:57:26.176]    <block atomic="false" info="">
[11:57:26.176]      Sequence("CheckID");
[11:57:26.176]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:57:26.176]          <block atomic="false" info="">
[11:57:26.176]            __var pidr1 = 0;
[11:57:26.176]              // -> [pidr1 <= 0x00000000]
[11:57:26.177]            __var pidr2 = 0;
[11:57:26.177]              // -> [pidr2 <= 0x00000000]
[11:57:26.177]            __var jep106id = 0;
[11:57:26.177]              // -> [jep106id <= 0x00000000]
[11:57:26.177]            __var ROMTableBase = 0;
[11:57:26.177]              // -> [ROMTableBase <= 0x00000000]
[11:57:26.178]            __ap = 0;      // AHB-AP
[11:57:26.178]              // -> [__ap <= 0x00000000]
[11:57:26.178]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:57:26.179]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:57:26.180]              // -> [ROMTableBase <= 0xF0000000]
[11:57:26.180]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:57:26.181]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:57:26.181]              // -> [pidr1 <= 0x00000004]
[11:57:26.182]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:57:26.182]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:57:26.183]              // -> [pidr2 <= 0x0000000A]
[11:57:26.183]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:57:26.183]              // -> [jep106id <= 0x00000020]
[11:57:26.183]          </block>
[11:57:26.183]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:57:26.184]            // if-block "jep106id != 0x20"
[11:57:26.184]              // =>  FALSE
[11:57:26.184]            // skip if-block "jep106id != 0x20"
[11:57:26.184]          </control>
[11:57:26.184]        </sequence>
[11:57:26.184]    </block>
[11:57:26.185]  </sequence>
[11:57:26.185]  
[11:57:26.197]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:57:26.197]  
[11:57:26.211]  <debugvars>
[11:57:26.211]    // Pre-defined
[11:57:26.212]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:57:26.212]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:57:26.212]    __dp=0x00000000
[11:57:26.212]    __ap=0x00000000
[11:57:26.213]    __traceout=0x00000000      (Trace Disabled)
[11:57:26.213]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:57:26.213]    __FlashAddr=0x00000000
[11:57:26.213]    __FlashLen=0x00000000
[11:57:26.213]    __FlashArg=0x00000000
[11:57:26.214]    __FlashOp=0x00000000
[11:57:26.214]    __Result=0x00000000
[11:57:26.214]    
[11:57:26.214]    // User-defined
[11:57:26.214]    DbgMCU_CR=0x00000007
[11:57:26.214]    DbgMCU_APB1_Fz=0x00000000
[11:57:26.215]    DbgMCU_APB2_Fz=0x00000000
[11:57:26.215]    DoOptionByteLoading=0x00000000
[11:57:26.216]  </debugvars>
[11:57:26.216]  
[11:57:26.216]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:57:26.216]    <block atomic="false" info="">
[11:57:26.216]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:57:26.217]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:57:26.218]    </block>
[11:57:26.218]    <block atomic="false" info="DbgMCU registers">
[11:57:26.219]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:57:26.219]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:57:26.220]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:57:26.220]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:57:26.221]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:57:26.221]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:57:26.222]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:57:26.222]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:57:26.223]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:57:26.224]    </block>
[11:57:26.224]  </sequence>
[11:57:26.224]  
[11:57:34.036]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:57:34.036]  
[11:57:34.036]  <debugvars>
[11:57:34.036]    // Pre-defined
[11:57:34.037]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:57:34.037]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:57:34.038]    __dp=0x00000000
[11:57:34.038]    __ap=0x00000000
[11:57:34.038]    __traceout=0x00000000      (Trace Disabled)
[11:57:34.038]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:57:34.039]    __FlashAddr=0x00000000
[11:57:34.039]    __FlashLen=0x00000000
[11:57:34.039]    __FlashArg=0x00000000
[11:57:34.040]    __FlashOp=0x00000000
[11:57:34.041]    __Result=0x00000000
[11:57:34.041]    
[11:57:34.041]    // User-defined
[11:57:34.041]    DbgMCU_CR=0x00000007
[11:57:34.041]    DbgMCU_APB1_Fz=0x00000000
[11:57:34.042]    DbgMCU_APB2_Fz=0x00000000
[11:57:34.042]    DoOptionByteLoading=0x00000000
[11:57:34.042]  </debugvars>
[11:57:34.042]  
[11:57:34.042]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:57:34.043]    <block atomic="false" info="">
[11:57:34.043]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:57:34.043]        // -> [connectionFlash <= 0x00000001]
[11:57:34.043]      __var FLASH_BASE = 0x40022000 ;
[11:57:34.043]        // -> [FLASH_BASE <= 0x40022000]
[11:57:34.043]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:57:34.044]        // -> [FLASH_CR <= 0x40022004]
[11:57:34.044]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:57:34.044]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:57:34.045]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:57:34.045]        // -> [LOCK_BIT <= 0x00000001]
[11:57:34.045]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:57:34.045]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:57:34.045]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:57:34.045]        // -> [FLASH_KEYR <= 0x4002200C]
[11:57:34.046]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:57:34.046]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:57:34.046]      __var FLASH_KEY2 = 0x02030405 ;
[11:57:34.046]        // -> [FLASH_KEY2 <= 0x02030405]
[11:57:34.046]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:57:34.046]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:57:34.047]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:57:34.047]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:57:34.047]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:57:34.047]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:57:34.047]      __var FLASH_CR_Value = 0 ;
[11:57:34.047]        // -> [FLASH_CR_Value <= 0x00000000]
[11:57:34.048]      __var DoDebugPortStop = 1 ;
[11:57:34.048]        // -> [DoDebugPortStop <= 0x00000001]
[11:57:34.048]      __var DP_CTRL_STAT = 0x4 ;
[11:57:34.048]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:57:34.048]      __var DP_SELECT = 0x8 ;
[11:57:34.049]        // -> [DP_SELECT <= 0x00000008]
[11:57:34.049]    </block>
[11:57:34.049]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:57:34.050]      // if-block "connectionFlash && DoOptionByteLoading"
[11:57:34.050]        // =>  FALSE
[11:57:34.050]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:57:34.050]    </control>
[11:57:34.052]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:57:34.052]      // if-block "DoDebugPortStop"
[11:57:34.052]        // =>  TRUE
[11:57:34.053]      <block atomic="false" info="">
[11:57:34.053]        WriteDP(DP_SELECT, 0x00000000);
[11:57:34.053]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:57:34.054]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:57:34.054]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:57:34.054]      </block>
[11:57:34.054]      // end if-block "DoDebugPortStop"
[11:57:34.055]    </control>
[11:57:34.055]  </sequence>
[11:57:34.056]  
[13:34:30.241]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[13:34:30.241]  
[13:34:30.252]  <debugvars>
[13:34:30.252]    // Pre-defined
[13:34:30.252]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:34:30.252]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:34:30.257]    __dp=0x00000000
[13:34:30.257]    __ap=0x00000000
[13:34:30.257]    __traceout=0x00000000      (Trace Disabled)
[13:34:30.257]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:34:30.257]    __FlashAddr=0x00000000
[13:34:30.257]    __FlashLen=0x00000000
[13:34:30.257]    __FlashArg=0x00000000
[13:34:30.257]    __FlashOp=0x00000000
[13:34:30.257]    __Result=0x00000000
[13:34:30.257]    
[13:34:30.257]    // User-defined
[13:34:30.257]    DbgMCU_CR=0x00000007
[13:34:30.257]    DbgMCU_APB1_Fz=0x00000000
[13:34:30.257]    DbgMCU_APB2_Fz=0x00000000
[13:34:30.257]    DoOptionByteLoading=0x00000000
[13:34:30.257]  </debugvars>
[13:34:30.257]  
[13:34:30.257]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[13:34:30.257]    <block atomic="false" info="">
[13:34:30.257]      Sequence("CheckID");
[13:34:30.262]        <sequence name="CheckID" Pname="" disable="false" info="">
[13:34:30.262]          <block atomic="false" info="">
[13:34:30.262]            __var pidr1 = 0;
[13:34:30.262]              // -> [pidr1 <= 0x00000000]
[13:34:30.262]            __var pidr2 = 0;
[13:34:30.262]              // -> [pidr2 <= 0x00000000]
[13:34:30.262]            __var jep106id = 0;
[13:34:30.262]              // -> [jep106id <= 0x00000000]
[13:34:30.262]            __var ROMTableBase = 0;
[13:34:30.262]              // -> [ROMTableBase <= 0x00000000]
[13:34:30.262]            __ap = 0;      // AHB-AP
[13:34:30.262]              // -> [__ap <= 0x00000000]
[13:34:30.262]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[13:34:30.262]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[13:34:30.267]              // -> [ROMTableBase <= 0xF0000000]
[13:34:30.267]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[13:34:30.267]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[13:34:30.267]              // -> [pidr1 <= 0x00000004]
[13:34:30.267]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[13:34:30.267]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[13:34:30.267]              // -> [pidr2 <= 0x0000000A]
[13:34:30.267]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[13:34:30.267]              // -> [jep106id <= 0x00000020]
[13:34:30.267]          </block>
[13:34:30.267]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[13:34:30.267]            // if-block "jep106id != 0x20"
[13:34:30.267]              // =>  FALSE
[13:34:30.267]            // skip if-block "jep106id != 0x20"
[13:34:30.267]          </control>
[13:34:30.267]        </sequence>
[13:34:30.272]    </block>
[13:34:30.272]  </sequence>
[13:34:30.272]  
[13:34:30.285]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[13:34:30.285]  
[13:34:30.285]  <debugvars>
[13:34:30.285]    // Pre-defined
[13:34:30.285]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:34:30.285]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:34:30.287]    __dp=0x00000000
[13:34:30.287]    __ap=0x00000000
[13:34:30.287]    __traceout=0x00000000      (Trace Disabled)
[13:34:30.288]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:34:30.288]    __FlashAddr=0x00000000
[13:34:30.288]    __FlashLen=0x00000000
[13:34:30.288]    __FlashArg=0x00000000
[13:34:30.288]    __FlashOp=0x00000000
[13:34:30.288]    __Result=0x00000000
[13:34:30.288]    
[13:34:30.288]    // User-defined
[13:34:30.288]    DbgMCU_CR=0x00000007
[13:34:30.288]    DbgMCU_APB1_Fz=0x00000000
[13:34:30.290]    DbgMCU_APB2_Fz=0x00000000
[13:34:30.290]    DoOptionByteLoading=0x00000000
[13:34:30.290]  </debugvars>
[13:34:30.290]  
[13:34:30.290]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[13:34:30.290]    <block atomic="false" info="">
[13:34:30.290]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[13:34:30.293]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[13:34:30.293]    </block>
[13:34:30.293]    <block atomic="false" info="DbgMCU registers">
[13:34:30.293]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[13:34:30.293]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[13:34:30.295]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[13:34:30.295]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[13:34:30.295]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[13:34:30.297]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[13:34:30.298]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:34:30.298]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[13:34:30.298]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:34:30.298]    </block>
[13:34:30.298]  </sequence>
[13:34:30.298]  
[13:34:38.243]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[13:34:38.243]  
[13:34:38.244]  <debugvars>
[13:34:38.244]    // Pre-defined
[13:34:38.245]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:34:38.246]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:34:38.246]    __dp=0x00000000
[13:34:38.247]    __ap=0x00000000
[13:34:38.247]    __traceout=0x00000000      (Trace Disabled)
[13:34:38.248]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:34:38.249]    __FlashAddr=0x00000000
[13:34:38.249]    __FlashLen=0x00000000
[13:34:38.249]    __FlashArg=0x00000000
[13:34:38.250]    __FlashOp=0x00000000
[13:34:38.251]    __Result=0x00000000
[13:34:38.251]    
[13:34:38.251]    // User-defined
[13:34:38.251]    DbgMCU_CR=0x00000007
[13:34:38.252]    DbgMCU_APB1_Fz=0x00000000
[13:34:38.252]    DbgMCU_APB2_Fz=0x00000000
[13:34:38.253]    DoOptionByteLoading=0x00000000
[13:34:38.253]  </debugvars>
[13:34:38.254]  
[13:34:38.254]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[13:34:38.255]    <block atomic="false" info="">
[13:34:38.255]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[13:34:38.256]        // -> [connectionFlash <= 0x00000001]
[13:34:38.256]      __var FLASH_BASE = 0x40022000 ;
[13:34:38.257]        // -> [FLASH_BASE <= 0x40022000]
[13:34:38.257]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[13:34:38.258]        // -> [FLASH_CR <= 0x40022004]
[13:34:38.258]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[13:34:38.258]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[13:34:38.259]      __var LOCK_BIT = ( 1 << 0 ) ;
[13:34:38.259]        // -> [LOCK_BIT <= 0x00000001]
[13:34:38.259]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[13:34:38.260]        // -> [OPTLOCK_BIT <= 0x00000004]
[13:34:38.260]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[13:34:38.260]        // -> [FLASH_KEYR <= 0x4002200C]
[13:34:38.261]      __var FLASH_KEY1 = 0x89ABCDEF ;
[13:34:38.261]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[13:34:38.261]      __var FLASH_KEY2 = 0x02030405 ;
[13:34:38.261]        // -> [FLASH_KEY2 <= 0x02030405]
[13:34:38.262]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[13:34:38.262]        // -> [FLASH_OPTKEYR <= 0x40022014]
[13:34:38.262]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[13:34:38.262]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[13:34:38.263]      __var FLASH_OPTKEY2 = 0x24252627 ;
[13:34:38.263]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[13:34:38.263]      __var FLASH_CR_Value = 0 ;
[13:34:38.264]        // -> [FLASH_CR_Value <= 0x00000000]
[13:34:38.264]      __var DoDebugPortStop = 1 ;
[13:34:38.264]        // -> [DoDebugPortStop <= 0x00000001]
[13:34:38.265]      __var DP_CTRL_STAT = 0x4 ;
[13:34:38.265]        // -> [DP_CTRL_STAT <= 0x00000004]
[13:34:38.265]      __var DP_SELECT = 0x8 ;
[13:34:38.266]        // -> [DP_SELECT <= 0x00000008]
[13:34:38.266]    </block>
[13:34:38.266]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[13:34:38.266]      // if-block "connectionFlash && DoOptionByteLoading"
[13:34:38.267]        // =>  FALSE
[13:34:38.267]      // skip if-block "connectionFlash && DoOptionByteLoading"
[13:34:38.267]    </control>
[13:34:38.267]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[13:34:38.268]      // if-block "DoDebugPortStop"
[13:34:38.268]        // =>  TRUE
[13:34:38.268]      <block atomic="false" info="">
[13:34:38.268]        WriteDP(DP_SELECT, 0x00000000);
[13:34:38.269]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[13:34:38.269]        WriteDP(DP_CTRL_STAT, 0x00000000);
[13:34:38.270]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[13:34:38.270]      </block>
[13:34:38.270]      // end if-block "DoDebugPortStop"
[13:34:38.270]    </control>
[13:34:38.271]  </sequence>
[13:34:38.271]  
[13:39:49.865]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[13:39:49.865]  
[13:39:49.866]  <debugvars>
[13:39:49.866]    // Pre-defined
[13:39:49.866]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:39:49.867]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:39:49.867]    __dp=0x00000000
[13:39:49.867]    __ap=0x00000000
[13:39:49.868]    __traceout=0x00000000      (Trace Disabled)
[13:39:49.868]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:39:49.868]    __FlashAddr=0x00000000
[13:39:49.869]    __FlashLen=0x00000000
[13:39:49.869]    __FlashArg=0x00000000
[13:39:49.869]    __FlashOp=0x00000000
[13:39:49.869]    __Result=0x00000000
[13:39:49.870]    
[13:39:49.870]    // User-defined
[13:39:49.870]    DbgMCU_CR=0x00000007
[13:39:49.870]    DbgMCU_APB1_Fz=0x00000000
[13:39:49.871]    DbgMCU_APB2_Fz=0x00000000
[13:39:49.871]    DoOptionByteLoading=0x00000000
[13:39:49.871]  </debugvars>
[13:39:49.871]  
[13:39:49.872]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[13:39:49.872]    <block atomic="false" info="">
[13:39:49.872]      Sequence("CheckID");
[13:39:49.872]        <sequence name="CheckID" Pname="" disable="false" info="">
[13:39:49.873]          <block atomic="false" info="">
[13:39:49.873]            __var pidr1 = 0;
[13:39:49.873]              // -> [pidr1 <= 0x00000000]
[13:39:49.873]            __var pidr2 = 0;
[13:39:49.873]              // -> [pidr2 <= 0x00000000]
[13:39:49.873]            __var jep106id = 0;
[13:39:49.874]              // -> [jep106id <= 0x00000000]
[13:39:49.874]            __var ROMTableBase = 0;
[13:39:49.874]              // -> [ROMTableBase <= 0x00000000]
[13:39:49.874]            __ap = 0;      // AHB-AP
[13:39:49.874]              // -> [__ap <= 0x00000000]
[13:39:49.875]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[13:39:49.875]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[13:39:49.875]              // -> [ROMTableBase <= 0xF0000000]
[13:39:49.876]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[13:39:49.877]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[13:39:49.878]              // -> [pidr1 <= 0x00000004]
[13:39:49.878]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[13:39:49.879]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[13:39:49.879]              // -> [pidr2 <= 0x0000000A]
[13:39:49.879]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[13:39:49.879]              // -> [jep106id <= 0x00000020]
[13:39:49.879]          </block>
[13:39:49.879]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[13:39:49.881]            // if-block "jep106id != 0x20"
[13:39:49.881]              // =>  FALSE
[13:39:49.881]            // skip if-block "jep106id != 0x20"
[13:39:49.881]          </control>
[13:39:49.881]        </sequence>
[13:39:49.881]    </block>
[13:39:49.883]  </sequence>
[13:39:49.883]  
[13:39:49.895]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[13:39:49.895]  
[13:39:49.924]  <debugvars>
[13:39:49.924]    // Pre-defined
[13:39:49.925]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:39:49.925]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:39:49.926]    __dp=0x00000000
[13:39:49.926]    __ap=0x00000000
[13:39:49.927]    __traceout=0x00000000      (Trace Disabled)
[13:39:49.927]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:39:49.928]    __FlashAddr=0x00000000
[13:39:49.928]    __FlashLen=0x00000000
[13:39:49.929]    __FlashArg=0x00000000
[13:39:49.929]    __FlashOp=0x00000000
[13:39:49.929]    __Result=0x00000000
[13:39:49.929]    
[13:39:49.929]    // User-defined
[13:39:49.931]    DbgMCU_CR=0x00000007
[13:39:49.931]    DbgMCU_APB1_Fz=0x00000000
[13:39:49.932]    DbgMCU_APB2_Fz=0x00000000
[13:39:49.932]    DoOptionByteLoading=0x00000000
[13:39:49.933]  </debugvars>
[13:39:49.933]  
[13:39:49.934]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[13:39:49.934]    <block atomic="false" info="">
[13:39:49.935]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[13:39:49.936]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[13:39:49.937]    </block>
[13:39:49.937]    <block atomic="false" info="DbgMCU registers">
[13:39:49.938]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[13:39:49.938]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[13:39:49.940]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[13:39:49.940]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[13:39:49.941]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[13:39:49.941]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[13:39:49.942]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:39:49.942]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[13:39:49.943]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:39:49.943]    </block>
[13:39:49.944]  </sequence>
[13:39:49.944]  
[13:39:58.062]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[13:39:58.062]  
[13:39:58.062]  <debugvars>
[13:39:58.062]    // Pre-defined
[13:39:58.062]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:39:58.062]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:39:58.062]    __dp=0x00000000
[13:39:58.064]    __ap=0x00000000
[13:39:58.064]    __traceout=0x00000000      (Trace Disabled)
[13:39:58.064]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:39:58.064]    __FlashAddr=0x00000000
[13:39:58.065]    __FlashLen=0x00000000
[13:39:58.065]    __FlashArg=0x00000000
[13:39:58.065]    __FlashOp=0x00000000
[13:39:58.065]    __Result=0x00000000
[13:39:58.065]    
[13:39:58.065]    // User-defined
[13:39:58.065]    DbgMCU_CR=0x00000007
[13:39:58.065]    DbgMCU_APB1_Fz=0x00000000
[13:39:58.065]    DbgMCU_APB2_Fz=0x00000000
[13:39:58.065]    DoOptionByteLoading=0x00000000
[13:39:58.067]  </debugvars>
[13:39:58.067]  
[13:39:58.067]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[13:39:58.067]    <block atomic="false" info="">
[13:39:58.067]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[13:39:58.067]        // -> [connectionFlash <= 0x00000001]
[13:39:58.067]      __var FLASH_BASE = 0x40022000 ;
[13:39:58.067]        // -> [FLASH_BASE <= 0x40022000]
[13:39:58.069]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[13:39:58.069]        // -> [FLASH_CR <= 0x40022004]
[13:39:58.069]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[13:39:58.070]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[13:39:58.070]      __var LOCK_BIT = ( 1 << 0 ) ;
[13:39:58.070]        // -> [LOCK_BIT <= 0x00000001]
[13:39:58.070]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[13:39:58.070]        // -> [OPTLOCK_BIT <= 0x00000004]
[13:39:58.070]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[13:39:58.070]        // -> [FLASH_KEYR <= 0x4002200C]
[13:39:58.072]      __var FLASH_KEY1 = 0x89ABCDEF ;
[13:39:58.072]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[13:39:58.072]      __var FLASH_KEY2 = 0x02030405 ;
[13:39:58.072]        // -> [FLASH_KEY2 <= 0x02030405]
[13:39:58.072]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[13:39:58.072]        // -> [FLASH_OPTKEYR <= 0x40022014]
[13:39:58.072]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[13:39:58.072]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[13:39:58.074]      __var FLASH_OPTKEY2 = 0x24252627 ;
[13:39:58.074]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[13:39:58.074]      __var FLASH_CR_Value = 0 ;
[13:39:58.074]        // -> [FLASH_CR_Value <= 0x00000000]
[13:39:58.075]      __var DoDebugPortStop = 1 ;
[13:39:58.075]        // -> [DoDebugPortStop <= 0x00000001]
[13:39:58.075]      __var DP_CTRL_STAT = 0x4 ;
[13:39:58.075]        // -> [DP_CTRL_STAT <= 0x00000004]
[13:39:58.075]      __var DP_SELECT = 0x8 ;
[13:39:58.075]        // -> [DP_SELECT <= 0x00000008]
[13:39:58.075]    </block>
[13:39:58.075]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[13:39:58.075]      // if-block "connectionFlash && DoOptionByteLoading"
[13:39:58.077]        // =>  FALSE
[13:39:58.077]      // skip if-block "connectionFlash && DoOptionByteLoading"
[13:39:58.077]    </control>
[13:39:58.077]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[13:39:58.077]      // if-block "DoDebugPortStop"
[13:39:58.077]        // =>  TRUE
[13:39:58.077]      <block atomic="false" info="">
[13:39:58.077]        WriteDP(DP_SELECT, 0x00000000);
[13:39:58.079]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[13:39:58.079]        WriteDP(DP_CTRL_STAT, 0x00000000);
[13:39:58.079]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[13:39:58.080]      </block>
[13:39:58.080]      // end if-block "DoDebugPortStop"
[13:39:58.080]    </control>
[13:39:58.080]  </sequence>
[13:39:58.080]  
[15:01:32.457]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:01:32.457]  
[15:01:32.469]  <debugvars>
[15:01:32.469]    // Pre-defined
[15:01:32.469]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:01:32.469]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:01:32.469]    __dp=0x00000000
[15:01:32.469]    __ap=0x00000000
[15:01:32.469]    __traceout=0x00000000      (Trace Disabled)
[15:01:32.469]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:01:32.469]    __FlashAddr=0x00000000
[15:01:32.469]    __FlashLen=0x00000000
[15:01:32.469]    __FlashArg=0x00000000
[15:01:32.474]    __FlashOp=0x00000000
[15:01:32.474]    __Result=0x00000000
[15:01:32.474]    
[15:01:32.474]    // User-defined
[15:01:32.474]    DbgMCU_CR=0x00000007
[15:01:32.474]    DbgMCU_APB1_Fz=0x00000000
[15:01:32.474]    DbgMCU_APB2_Fz=0x00000000
[15:01:32.474]    DoOptionByteLoading=0x00000000
[15:01:32.474]  </debugvars>
[15:01:32.474]  
[15:01:32.474]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:01:32.474]    <block atomic="false" info="">
[15:01:32.474]      Sequence("CheckID");
[15:01:32.474]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:01:32.477]          <block atomic="false" info="">
[15:01:32.477]            __var pidr1 = 0;
[15:01:32.478]              // -> [pidr1 <= 0x00000000]
[15:01:32.478]            __var pidr2 = 0;
[15:01:32.478]              // -> [pidr2 <= 0x00000000]
[15:01:32.479]            __var jep106id = 0;
[15:01:32.479]              // -> [jep106id <= 0x00000000]
[15:01:32.479]            __var ROMTableBase = 0;
[15:01:32.479]              // -> [ROMTableBase <= 0x00000000]
[15:01:32.479]            __ap = 0;      // AHB-AP
[15:01:32.479]              // -> [__ap <= 0x00000000]
[15:01:32.480]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:01:32.480]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:01:32.481]              // -> [ROMTableBase <= 0xF0000000]
[15:01:32.481]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:01:32.482]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:01:32.483]              // -> [pidr1 <= 0x00000004]
[15:01:32.483]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:01:32.484]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:01:32.484]              // -> [pidr2 <= 0x0000000A]
[15:01:32.486]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:01:32.486]              // -> [jep106id <= 0x00000020]
[15:01:32.486]          </block>
[15:01:32.487]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:01:32.487]            // if-block "jep106id != 0x20"
[15:01:32.487]              // =>  FALSE
[15:01:32.487]            // skip if-block "jep106id != 0x20"
[15:01:32.487]          </control>
[15:01:32.487]        </sequence>
[15:01:32.488]    </block>
[15:01:32.488]  </sequence>
[15:01:32.488]  
[15:01:32.503]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:01:32.503]  
[15:01:32.528]  <debugvars>
[15:01:32.528]    // Pre-defined
[15:01:32.528]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:01:32.528]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:01:32.528]    __dp=0x00000000
[15:01:32.528]    __ap=0x00000000
[15:01:32.528]    __traceout=0x00000000      (Trace Disabled)
[15:01:32.528]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:01:32.531]    __FlashAddr=0x00000000
[15:01:32.531]    __FlashLen=0x00000000
[15:01:32.531]    __FlashArg=0x00000000
[15:01:32.531]    __FlashOp=0x00000000
[15:01:32.531]    __Result=0x00000000
[15:01:32.531]    
[15:01:32.531]    // User-defined
[15:01:32.531]    DbgMCU_CR=0x00000007
[15:01:32.531]    DbgMCU_APB1_Fz=0x00000000
[15:01:32.531]    DbgMCU_APB2_Fz=0x00000000
[15:01:32.531]    DoOptionByteLoading=0x00000000
[15:01:32.531]  </debugvars>
[15:01:32.531]  
[15:01:32.531]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:01:32.531]    <block atomic="false" info="">
[15:01:32.531]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:01:32.531]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:01:32.536]    </block>
[15:01:32.536]    <block atomic="false" info="DbgMCU registers">
[15:01:32.536]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:01:32.537]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[15:01:32.539]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[15:01:32.539]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:01:32.539]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:01:32.539]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:01:32.541]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:01:32.542]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:01:32.543]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:01:32.543]    </block>
[15:01:32.543]  </sequence>
[15:01:32.544]  
[15:01:40.723]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:01:40.723]  
[15:01:40.726]  <debugvars>
[15:01:40.726]    // Pre-defined
[15:01:40.726]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:01:40.726]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:01:40.726]    __dp=0x00000000
[15:01:40.726]    __ap=0x00000000
[15:01:40.728]    __traceout=0x00000000      (Trace Disabled)
[15:01:40.728]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:01:40.728]    __FlashAddr=0x00000000
[15:01:40.728]    __FlashLen=0x00000000
[15:01:40.728]    __FlashArg=0x00000000
[15:01:40.728]    __FlashOp=0x00000000
[15:01:40.728]    __Result=0x00000000
[15:01:40.728]    
[15:01:40.728]    // User-defined
[15:01:40.730]    DbgMCU_CR=0x00000007
[15:01:40.730]    DbgMCU_APB1_Fz=0x00000000
[15:01:40.730]    DbgMCU_APB2_Fz=0x00000000
[15:01:40.730]    DoOptionByteLoading=0x00000000
[15:01:40.731]  </debugvars>
[15:01:40.731]  
[15:01:40.731]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:01:40.731]    <block atomic="false" info="">
[15:01:40.731]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:01:40.731]        // -> [connectionFlash <= 0x00000001]
[15:01:40.731]      __var FLASH_BASE = 0x40022000 ;
[15:01:40.731]        // -> [FLASH_BASE <= 0x40022000]
[15:01:40.731]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:01:40.731]        // -> [FLASH_CR <= 0x40022004]
[15:01:40.731]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:01:40.731]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:01:40.731]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:01:40.731]        // -> [LOCK_BIT <= 0x00000001]
[15:01:40.731]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:01:40.731]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:01:40.731]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:01:40.731]        // -> [FLASH_KEYR <= 0x4002200C]
[15:01:40.731]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:01:40.731]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:01:40.736]      __var FLASH_KEY2 = 0x02030405 ;
[15:01:40.736]        // -> [FLASH_KEY2 <= 0x02030405]
[15:01:40.736]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:01:40.736]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:01:40.736]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:01:40.736]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:01:40.736]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:01:40.736]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:01:40.736]      __var FLASH_CR_Value = 0 ;
[15:01:40.736]        // -> [FLASH_CR_Value <= 0x00000000]
[15:01:40.736]      __var DoDebugPortStop = 1 ;
[15:01:40.736]        // -> [DoDebugPortStop <= 0x00000001]
[15:01:40.739]      __var DP_CTRL_STAT = 0x4 ;
[15:01:40.739]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:01:40.739]      __var DP_SELECT = 0x8 ;
[15:01:40.739]        // -> [DP_SELECT <= 0x00000008]
[15:01:40.739]    </block>
[15:01:40.739]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:01:40.741]      // if-block "connectionFlash && DoOptionByteLoading"
[15:01:40.741]        // =>  FALSE
[15:01:40.741]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:01:40.741]    </control>
[15:01:40.741]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:01:40.741]      // if-block "DoDebugPortStop"
[15:01:40.741]        // =>  TRUE
[15:01:40.741]      <block atomic="false" info="">
[15:01:40.741]        WriteDP(DP_SELECT, 0x00000000);
[15:01:40.741]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:01:40.744]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:01:40.744]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:01:40.744]      </block>
[15:01:40.744]      // end if-block "DoDebugPortStop"
[15:01:40.744]    </control>
[15:01:40.744]  </sequence>
[15:01:40.744]  
[19:14:45.575]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[19:14:45.575]  
[19:14:45.587]  <debugvars>
[19:14:45.587]    // Pre-defined
[19:14:45.587]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:14:45.588]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:14:45.588]    __dp=0x00000000
[19:14:45.588]    __ap=0x00000000
[19:14:45.589]    __traceout=0x00000000      (Trace Disabled)
[19:14:45.589]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:14:45.589]    __FlashAddr=0x00000000
[19:14:45.589]    __FlashLen=0x00000000
[19:14:45.590]    __FlashArg=0x00000000
[19:14:45.590]    __FlashOp=0x00000000
[19:14:45.590]    __Result=0x00000000
[19:14:45.590]    
[19:14:45.590]    // User-defined
[19:14:45.590]    DbgMCU_CR=0x00000007
[19:14:45.590]    DbgMCU_APB1_Fz=0x00000000
[19:14:45.591]    DbgMCU_APB2_Fz=0x00000000
[19:14:45.591]    DoOptionByteLoading=0x00000000
[19:14:45.591]  </debugvars>
[19:14:45.591]  
[19:14:45.591]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[19:14:45.591]    <block atomic="false" info="">
[19:14:45.592]      Sequence("CheckID");
[19:14:45.592]        <sequence name="CheckID" Pname="" disable="false" info="">
[19:14:45.592]          <block atomic="false" info="">
[19:14:45.593]            __var pidr1 = 0;
[19:14:45.593]              // -> [pidr1 <= 0x00000000]
[19:14:45.593]            __var pidr2 = 0;
[19:14:45.593]              // -> [pidr2 <= 0x00000000]
[19:14:45.593]            __var jep106id = 0;
[19:14:45.594]              // -> [jep106id <= 0x00000000]
[19:14:45.594]            __var ROMTableBase = 0;
[19:14:45.594]              // -> [ROMTableBase <= 0x00000000]
[19:14:45.594]            __ap = 0;      // AHB-AP
[19:14:45.594]              // -> [__ap <= 0x00000000]
[19:14:45.595]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[19:14:45.595]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[19:14:45.595]              // -> [ROMTableBase <= 0xF0000000]
[19:14:45.596]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[19:14:45.597]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[19:14:45.597]              // -> [pidr1 <= 0x00000004]
[19:14:45.598]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[19:14:45.599]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[19:14:45.599]              // -> [pidr2 <= 0x0000000A]
[19:14:45.599]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[19:14:45.599]              // -> [jep106id <= 0x00000020]
[19:14:45.600]          </block>
[19:14:45.600]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[19:14:45.600]            // if-block "jep106id != 0x20"
[19:14:45.600]              // =>  FALSE
[19:14:45.601]            // skip if-block "jep106id != 0x20"
[19:14:45.601]          </control>
[19:14:45.601]        </sequence>
[19:14:45.602]    </block>
[19:14:45.602]  </sequence>
[19:14:45.602]  
[19:14:45.614]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[19:14:45.614]  
[19:14:45.628]  <debugvars>
[19:14:45.628]    // Pre-defined
[19:14:45.628]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:14:45.629]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:14:45.629]    __dp=0x00000000
[19:14:45.629]    __ap=0x00000000
[19:14:45.630]    __traceout=0x00000000      (Trace Disabled)
[19:14:45.630]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:14:45.630]    __FlashAddr=0x00000000
[19:14:45.630]    __FlashLen=0x00000000
[19:14:45.631]    __FlashArg=0x00000000
[19:14:45.631]    __FlashOp=0x00000000
[19:14:45.632]    __Result=0x00000000
[19:14:45.632]    
[19:14:45.632]    // User-defined
[19:14:45.632]    DbgMCU_CR=0x00000007
[19:14:45.633]    DbgMCU_APB1_Fz=0x00000000
[19:14:45.633]    DbgMCU_APB2_Fz=0x00000000
[19:14:45.633]    DoOptionByteLoading=0x00000000
[19:14:45.633]  </debugvars>
[19:14:45.634]  
[19:14:45.634]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[19:14:45.634]    <block atomic="false" info="">
[19:14:45.634]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[19:14:45.635]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[19:14:45.635]    </block>
[19:14:45.636]    <block atomic="false" info="DbgMCU registers">
[19:14:45.636]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[19:14:45.637]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[19:14:45.638]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[19:14:45.638]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[19:14:45.639]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[19:14:45.640]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[19:14:45.640]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[19:14:45.641]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[19:14:45.642]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[19:14:45.642]    </block>
[19:14:45.642]  </sequence>
[19:14:45.643]  
[19:14:53.828]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[19:14:53.828]  
[19:14:53.829]  <debugvars>
[19:14:53.829]    // Pre-defined
[19:14:53.830]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[19:14:53.830]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[19:14:53.830]    __dp=0x00000000
[19:14:53.831]    __ap=0x00000000
[19:14:53.831]    __traceout=0x00000000      (Trace Disabled)
[19:14:53.831]    __errorcontrol=0x00000000  (Skip Errors="False")
[19:14:53.832]    __FlashAddr=0x00000000
[19:14:53.832]    __FlashLen=0x00000000
[19:14:53.832]    __FlashArg=0x00000000
[19:14:53.833]    __FlashOp=0x00000000
[19:14:53.833]    __Result=0x00000000
[19:14:53.833]    
[19:14:53.833]    // User-defined
[19:14:53.834]    DbgMCU_CR=0x00000007
[19:14:53.834]    DbgMCU_APB1_Fz=0x00000000
[19:14:53.834]    DbgMCU_APB2_Fz=0x00000000
[19:14:53.835]    DoOptionByteLoading=0x00000000
[19:14:53.835]  </debugvars>
[19:14:53.835]  
[19:14:53.835]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[19:14:53.836]    <block atomic="false" info="">
[19:14:53.836]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[19:14:53.836]        // -> [connectionFlash <= 0x00000001]
[19:14:53.836]      __var FLASH_BASE = 0x40022000 ;
[19:14:53.837]        // -> [FLASH_BASE <= 0x40022000]
[19:14:53.838]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[19:14:53.838]        // -> [FLASH_CR <= 0x40022004]
[19:14:53.838]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[19:14:53.839]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[19:14:53.840]      __var LOCK_BIT = ( 1 << 0 ) ;
[19:14:53.840]        // -> [LOCK_BIT <= 0x00000001]
[19:14:53.841]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[19:14:53.841]        // -> [OPTLOCK_BIT <= 0x00000004]
[19:14:53.841]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[19:14:53.841]        // -> [FLASH_KEYR <= 0x4002200C]
[19:14:53.841]      __var FLASH_KEY1 = 0x89ABCDEF ;
[19:14:53.842]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[19:14:53.842]      __var FLASH_KEY2 = 0x02030405 ;
[19:14:53.842]        // -> [FLASH_KEY2 <= 0x02030405]
[19:14:53.842]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[19:14:53.842]        // -> [FLASH_OPTKEYR <= 0x40022014]
[19:14:53.843]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[19:14:53.843]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[19:14:53.843]      __var FLASH_OPTKEY2 = 0x24252627 ;
[19:14:53.843]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[19:14:53.844]      __var FLASH_CR_Value = 0 ;
[19:14:53.844]        // -> [FLASH_CR_Value <= 0x00000000]
[19:14:53.844]      __var DoDebugPortStop = 1 ;
[19:14:53.844]        // -> [DoDebugPortStop <= 0x00000001]
[19:14:53.844]      __var DP_CTRL_STAT = 0x4 ;
[19:14:53.844]        // -> [DP_CTRL_STAT <= 0x00000004]
[19:14:53.844]      __var DP_SELECT = 0x8 ;
[19:14:53.844]        // -> [DP_SELECT <= 0x00000008]
[19:14:53.844]    </block>
[19:14:53.844]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[19:14:53.846]      // if-block "connectionFlash && DoOptionByteLoading"
[19:14:53.846]        // =>  FALSE
[19:14:53.846]      // skip if-block "connectionFlash && DoOptionByteLoading"
[19:14:53.846]    </control>
[19:14:53.847]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[19:14:53.847]      // if-block "DoDebugPortStop"
[19:14:53.847]        // =>  TRUE
[19:14:53.847]      <block atomic="false" info="">
[19:14:53.847]        WriteDP(DP_SELECT, 0x00000000);
[19:14:53.848]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[19:14:53.848]        WriteDP(DP_CTRL_STAT, 0x00000000);
[19:14:53.849]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[19:14:53.849]      </block>
[19:14:53.849]      // end if-block "DoDebugPortStop"
[19:14:53.849]    </control>
[19:14:53.850]  </sequence>
[19:14:53.850]  
