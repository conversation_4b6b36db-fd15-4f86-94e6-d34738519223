/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : D:\2025work\STM32L072PRO\source-application\user\driver_Sequences_0022.log
 *  Created     : 09:54:55 (20/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : D:\2025work\STM32L072PRO\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[09:54:55.057]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:54:55.057]  
[09:54:55.081]  <debugvars>
[09:54:55.111]    // Pre-defined
[09:54:55.131]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:54:55.153]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:54:55.154]    __dp=0x00000000
[09:54:55.154]    __ap=0x00000000
[09:54:55.154]    __traceout=0x00000000      (Trace Disabled)
[09:54:55.154]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:54:55.155]    __FlashAddr=0x00000000
[09:54:55.155]    __FlashLen=0x00000000
[09:54:55.155]    __FlashArg=0x00000000
[09:54:55.155]    __FlashOp=0x00000000
[09:54:55.155]    __Result=0x00000000
[09:54:55.155]    
[09:54:55.155]    // User-defined
[09:54:55.155]    DbgMCU_CR=0x00000007
[09:54:55.156]    DbgMCU_APB1_Fz=0x00000000
[09:54:55.156]    DbgMCU_APB2_Fz=0x00000000
[09:54:55.157]    DoOptionByteLoading=0x00000000
[09:54:55.157]  </debugvars>
[09:54:55.157]  
[09:54:55.157]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:54:55.157]    <block atomic="false" info="">
[09:54:55.158]      Sequence("CheckID");
[09:54:55.158]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:54:55.158]          <block atomic="false" info="">
[09:54:55.158]            __var pidr1 = 0;
[09:54:55.158]              // -> [pidr1 <= 0x00000000]
[09:54:55.159]            __var pidr2 = 0;
[09:54:55.159]              // -> [pidr2 <= 0x00000000]
[09:54:55.159]            __var jep106id = 0;
[09:54:55.159]              // -> [jep106id <= 0x00000000]
[09:54:55.159]            __var ROMTableBase = 0;
[09:54:55.160]              // -> [ROMTableBase <= 0x00000000]
[09:54:55.160]            __ap = 0;      // AHB-AP
[09:54:55.160]              // -> [__ap <= 0x00000000]
[09:54:55.160]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:54:55.161]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:54:55.161]              // -> [ROMTableBase <= 0xF0000000]
[09:54:55.161]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:54:55.162]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:54:55.163]              // -> [pidr1 <= 0x00000004]
[09:54:55.163]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:54:55.164]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:54:55.164]              // -> [pidr2 <= 0x0000000A]
[09:54:55.164]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:54:55.165]              // -> [jep106id <= 0x00000020]
[09:54:55.165]          </block>
[09:54:55.165]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:54:55.166]            // if-block "jep106id != 0x20"
[09:54:55.166]              // =>  FALSE
[09:54:55.166]            // skip if-block "jep106id != 0x20"
[09:54:55.166]          </control>
[09:54:55.166]        </sequence>
[09:54:55.166]    </block>
[09:54:55.166]  </sequence>
[09:54:55.166]  
[09:54:55.180]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:54:55.180]  
[09:54:55.206]  <debugvars>
[09:54:55.206]    // Pre-defined
[09:54:55.206]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:54:55.206]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:54:55.206]    __dp=0x00000000
[09:54:55.207]    __ap=0x00000000
[09:54:55.207]    __traceout=0x00000000      (Trace Disabled)
[09:54:55.208]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:54:55.208]    __FlashAddr=0x00000000
[09:54:55.208]    __FlashLen=0x00000000
[09:54:55.208]    __FlashArg=0x00000000
[09:54:55.208]    __FlashOp=0x00000000
[09:54:55.209]    __Result=0x00000000
[09:54:55.209]    
[09:54:55.209]    // User-defined
[09:54:55.209]    DbgMCU_CR=0x00000007
[09:54:55.209]    DbgMCU_APB1_Fz=0x00000000
[09:54:55.209]    DbgMCU_APB2_Fz=0x00000000
[09:54:55.210]    DoOptionByteLoading=0x00000000
[09:54:55.210]  </debugvars>
[09:54:55.210]  
[09:54:55.210]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:54:55.210]    <block atomic="false" info="">
[09:54:55.210]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:54:55.211]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:54:55.211]    </block>
[09:54:55.211]    <block atomic="false" info="DbgMCU registers">
[09:54:55.211]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:54:55.213]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[09:54:55.214]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[09:54:55.214]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:54:55.215]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:54:55.216]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:54:55.217]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:54:55.217]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:54:55.217]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:54:55.217]    </block>
[09:54:55.217]  </sequence>
[09:54:55.217]  
[09:55:03.196]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:55:03.196]  
[09:55:03.197]  <debugvars>
[09:55:03.197]    // Pre-defined
[09:55:03.197]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:55:03.198]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:55:03.198]    __dp=0x00000000
[09:55:03.198]    __ap=0x00000000
[09:55:03.198]    __traceout=0x00000000      (Trace Disabled)
[09:55:03.198]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:55:03.198]    __FlashAddr=0x00000000
[09:55:03.198]    __FlashLen=0x00000000
[09:55:03.199]    __FlashArg=0x00000000
[09:55:03.199]    __FlashOp=0x00000000
[09:55:03.199]    __Result=0x00000000
[09:55:03.199]    
[09:55:03.199]    // User-defined
[09:55:03.200]    DbgMCU_CR=0x00000007
[09:55:03.200]    DbgMCU_APB1_Fz=0x00000000
[09:55:03.201]    DbgMCU_APB2_Fz=0x00000000
[09:55:03.201]    DoOptionByteLoading=0x00000000
[09:55:03.201]  </debugvars>
[09:55:03.201]  
[09:55:03.202]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:55:03.202]    <block atomic="false" info="">
[09:55:03.202]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:55:03.202]        // -> [connectionFlash <= 0x00000001]
[09:55:03.203]      __var FLASH_BASE = 0x40022000 ;
[09:55:03.203]        // -> [FLASH_BASE <= 0x40022000]
[09:55:03.203]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:55:03.203]        // -> [FLASH_CR <= 0x40022004]
[09:55:03.204]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:55:03.204]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:55:03.204]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:55:03.204]        // -> [LOCK_BIT <= 0x00000001]
[09:55:03.204]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:55:03.204]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:55:03.205]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:55:03.205]        // -> [FLASH_KEYR <= 0x4002200C]
[09:55:03.205]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:55:03.205]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:55:03.205]      __var FLASH_KEY2 = 0x02030405 ;
[09:55:03.206]        // -> [FLASH_KEY2 <= 0x02030405]
[09:55:03.207]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:55:03.207]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:55:03.207]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:55:03.207]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:55:03.207]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:55:03.207]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:55:03.207]      __var FLASH_CR_Value = 0 ;
[09:55:03.208]        // -> [FLASH_CR_Value <= 0x00000000]
[09:55:03.208]      __var DoDebugPortStop = 1 ;
[09:55:03.208]        // -> [DoDebugPortStop <= 0x00000001]
[09:55:03.208]      __var DP_CTRL_STAT = 0x4 ;
[09:55:03.208]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:55:03.209]      __var DP_SELECT = 0x8 ;
[09:55:03.209]        // -> [DP_SELECT <= 0x00000008]
[09:55:03.209]    </block>
[09:55:03.209]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:55:03.209]      // if-block "connectionFlash && DoOptionByteLoading"
[09:55:03.209]        // =>  FALSE
[09:55:03.209]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:55:03.209]    </control>
[09:55:03.210]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:55:03.210]      // if-block "DoDebugPortStop"
[09:55:03.210]        // =>  TRUE
[09:55:03.210]      <block atomic="false" info="">
[09:55:03.210]        WriteDP(DP_SELECT, 0x00000000);
[09:55:03.211]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:55:03.212]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:55:03.213]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:55:03.213]      </block>
[09:55:03.213]      // end if-block "DoDebugPortStop"
[09:55:03.213]    </control>
[09:55:03.214]  </sequence>
[09:55:03.214]  
[10:23:48.003]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:23:48.003]  
[10:23:48.016]  <debugvars>
[10:23:48.016]    // Pre-defined
[10:23:48.017]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:23:48.017]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:23:48.018]    __dp=0x00000000
[10:23:48.018]    __ap=0x00000000
[10:23:48.018]    __traceout=0x00000000      (Trace Disabled)
[10:23:48.018]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:23:48.018]    __FlashAddr=0x00000000
[10:23:48.018]    __FlashLen=0x00000000
[10:23:48.019]    __FlashArg=0x00000000
[10:23:48.019]    __FlashOp=0x00000000
[10:23:48.020]    __Result=0x00000000
[10:23:48.020]    
[10:23:48.020]    // User-defined
[10:23:48.020]    DbgMCU_CR=0x00000007
[10:23:48.020]    DbgMCU_APB1_Fz=0x00000000
[10:23:48.020]    DbgMCU_APB2_Fz=0x00000000
[10:23:48.021]    DoOptionByteLoading=0x00000000
[10:23:48.021]  </debugvars>
[10:23:48.022]  
[10:23:48.022]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:23:48.022]    <block atomic="false" info="">
[10:23:48.023]      Sequence("CheckID");
[10:23:48.023]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:23:48.023]          <block atomic="false" info="">
[10:23:48.023]            __var pidr1 = 0;
[10:23:48.023]              // -> [pidr1 <= 0x00000000]
[10:23:48.024]            __var pidr2 = 0;
[10:23:48.024]              // -> [pidr2 <= 0x00000000]
[10:23:48.024]            __var jep106id = 0;
[10:23:48.024]              // -> [jep106id <= 0x00000000]
[10:23:48.024]            __var ROMTableBase = 0;
[10:23:48.024]              // -> [ROMTableBase <= 0x00000000]
[10:23:48.025]            __ap = 0;      // AHB-AP
[10:23:48.025]              // -> [__ap <= 0x00000000]
[10:23:48.025]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:23:48.026]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:23:48.026]              // -> [ROMTableBase <= 0xF0000000]
[10:23:48.026]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:23:48.027]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:23:48.027]              // -> [pidr1 <= 0x00000004]
[10:23:48.028]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:23:48.028]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:23:48.028]              // -> [pidr2 <= 0x0000000A]
[10:23:48.029]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:23:48.029]              // -> [jep106id <= 0x00000020]
[10:23:48.029]          </block>
[10:23:48.030]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:23:48.030]            // if-block "jep106id != 0x20"
[10:23:48.030]              // =>  FALSE
[10:23:48.030]            // skip if-block "jep106id != 0x20"
[10:23:48.030]          </control>
[10:23:48.031]        </sequence>
[10:23:48.031]    </block>
[10:23:48.031]  </sequence>
[10:23:48.031]  
[10:23:48.043]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:23:48.043]  
[10:23:48.066]  <debugvars>
[10:23:48.066]    // Pre-defined
[10:23:48.067]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:23:48.067]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:23:48.067]    __dp=0x00000000
[10:23:48.068]    __ap=0x00000000
[10:23:48.068]    __traceout=0x00000000      (Trace Disabled)
[10:23:48.068]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:23:48.068]    __FlashAddr=0x00000000
[10:23:48.069]    __FlashLen=0x00000000
[10:23:48.069]    __FlashArg=0x00000000
[10:23:48.069]    __FlashOp=0x00000000
[10:23:48.069]    __Result=0x00000000
[10:23:48.069]    
[10:23:48.069]    // User-defined
[10:23:48.070]    DbgMCU_CR=0x00000007
[10:23:48.070]    DbgMCU_APB1_Fz=0x00000000
[10:23:48.070]    DbgMCU_APB2_Fz=0x00000000
[10:23:48.070]    DoOptionByteLoading=0x00000000
[10:23:48.070]  </debugvars>
[10:23:48.071]  
[10:23:48.071]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:23:48.071]    <block atomic="false" info="">
[10:23:48.071]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:23:48.073]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:23:48.073]    </block>
[10:23:48.073]    <block atomic="false" info="DbgMCU registers">
[10:23:48.073]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:23:48.074]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[10:23:48.075]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[10:23:48.075]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:23:48.076]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:23:48.076]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:23:48.077]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:23:48.077]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:23:48.078]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:23:48.078]    </block>
[10:23:48.079]  </sequence>
[10:23:48.079]  
[10:23:55.827]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:23:55.827]  
[10:23:55.827]  <debugvars>
[10:23:55.827]    // Pre-defined
[10:23:55.827]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:23:55.828]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:23:55.828]    __dp=0x00000000
[10:23:55.828]    __ap=0x00000000
[10:23:55.828]    __traceout=0x00000000      (Trace Disabled)
[10:23:55.828]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:23:55.828]    __FlashAddr=0x00000000
[10:23:55.828]    __FlashLen=0x00000000
[10:23:55.828]    __FlashArg=0x00000000
[10:23:55.828]    __FlashOp=0x00000000
[10:23:55.828]    __Result=0x00000000
[10:23:55.830]    
[10:23:55.830]    // User-defined
[10:23:55.830]    DbgMCU_CR=0x00000007
[10:23:55.830]    DbgMCU_APB1_Fz=0x00000000
[10:23:55.831]    DbgMCU_APB2_Fz=0x00000000
[10:23:55.831]    DoOptionByteLoading=0x00000000
[10:23:55.831]  </debugvars>
[10:23:55.831]  
[10:23:55.831]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:23:55.831]    <block atomic="false" info="">
[10:23:55.831]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:23:55.832]        // -> [connectionFlash <= 0x00000001]
[10:23:55.832]      __var FLASH_BASE = 0x40022000 ;
[10:23:55.832]        // -> [FLASH_BASE <= 0x40022000]
[10:23:55.832]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:23:55.832]        // -> [FLASH_CR <= 0x40022004]
[10:23:55.833]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:23:55.833]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:23:55.833]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:23:55.834]        // -> [LOCK_BIT <= 0x00000001]
[10:23:55.834]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:23:55.834]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:23:55.834]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:23:55.834]        // -> [FLASH_KEYR <= 0x4002200C]
[10:23:55.835]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:23:55.835]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:23:55.835]      __var FLASH_KEY2 = 0x02030405 ;
[10:23:55.835]        // -> [FLASH_KEY2 <= 0x02030405]
[10:23:55.835]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:23:55.836]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:23:55.836]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:23:55.836]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:23:55.836]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:23:55.836]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:23:55.836]      __var FLASH_CR_Value = 0 ;
[10:23:55.836]        // -> [FLASH_CR_Value <= 0x00000000]
[10:23:55.836]      __var DoDebugPortStop = 1 ;
[10:23:55.837]        // -> [DoDebugPortStop <= 0x00000001]
[10:23:55.837]      __var DP_CTRL_STAT = 0x4 ;
[10:23:55.837]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:23:55.838]      __var DP_SELECT = 0x8 ;
[10:23:55.838]        // -> [DP_SELECT <= 0x00000008]
[10:23:55.838]    </block>
[10:23:55.838]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:23:55.838]      // if-block "connectionFlash && DoOptionByteLoading"
[10:23:55.839]        // =>  FALSE
[10:23:55.839]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:23:55.839]    </control>
[10:23:55.839]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:23:55.839]      // if-block "DoDebugPortStop"
[10:23:55.839]        // =>  TRUE
[10:23:55.839]      <block atomic="false" info="">
[10:23:55.839]        WriteDP(DP_SELECT, 0x00000000);
[10:23:55.840]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:23:55.841]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:23:55.841]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:23:55.841]      </block>
[10:23:55.841]      // end if-block "DoDebugPortStop"
[10:23:55.842]    </control>
[10:23:55.842]  </sequence>
[10:23:55.843]  
[14:41:13.057]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:41:13.057]  
[14:41:13.068]  <debugvars>
[14:41:13.068]    // Pre-defined
[14:41:13.068]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:41:13.070]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:41:13.070]    __dp=0x00000000
[14:41:13.070]    __ap=0x00000000
[14:41:13.070]    __traceout=0x00000000      (Trace Disabled)
[14:41:13.070]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:41:13.071]    __FlashAddr=0x00000000
[14:41:13.071]    __FlashLen=0x00000000
[14:41:13.071]    __FlashArg=0x00000000
[14:41:13.071]    __FlashOp=0x00000000
[14:41:13.072]    __Result=0x00000000
[14:41:13.072]    
[14:41:13.072]    // User-defined
[14:41:13.072]    DbgMCU_CR=0x00000007
[14:41:13.073]    DbgMCU_APB1_Fz=0x00000000
[14:41:13.073]    DbgMCU_APB2_Fz=0x00000000
[14:41:13.073]    DoOptionByteLoading=0x00000000
[14:41:13.073]  </debugvars>
[14:41:13.074]  
[14:41:13.074]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:41:13.074]    <block atomic="false" info="">
[14:41:13.075]      Sequence("CheckID");
[14:41:13.075]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:41:13.075]          <block atomic="false" info="">
[14:41:13.075]            __var pidr1 = 0;
[14:41:13.076]              // -> [pidr1 <= 0x00000000]
[14:41:13.076]            __var pidr2 = 0;
[14:41:13.076]              // -> [pidr2 <= 0x00000000]
[14:41:13.076]            __var jep106id = 0;
[14:41:13.076]              // -> [jep106id <= 0x00000000]
[14:41:13.077]            __var ROMTableBase = 0;
[14:41:13.077]              // -> [ROMTableBase <= 0x00000000]
[14:41:13.077]            __ap = 0;      // AHB-AP
[14:41:13.077]              // -> [__ap <= 0x00000000]
[14:41:13.079]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:41:13.079]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:41:13.079]              // -> [ROMTableBase <= 0xF0000000]
[14:41:13.080]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:41:13.081]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:41:13.081]              // -> [pidr1 <= 0x00000004]
[14:41:13.081]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:41:13.082]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:41:13.082]              // -> [pidr2 <= 0x0000000A]
[14:41:13.083]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:41:13.084]              // -> [jep106id <= 0x00000020]
[14:41:13.084]          </block>
[14:41:13.084]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:41:13.084]            // if-block "jep106id != 0x20"
[14:41:13.084]              // =>  FALSE
[14:41:13.085]            // skip if-block "jep106id != 0x20"
[14:41:13.085]          </control>
[14:41:13.085]        </sequence>
[14:41:13.085]    </block>
[14:41:13.085]  </sequence>
[14:41:13.085]  
[14:41:13.098]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:41:13.098]  
[14:41:13.099]  <debugvars>
[14:41:13.099]    // Pre-defined
[14:41:13.099]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:41:13.099]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:41:13.100]    __dp=0x00000000
[14:41:13.100]    __ap=0x00000000
[14:41:13.100]    __traceout=0x00000000      (Trace Disabled)
[14:41:13.101]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:41:13.101]    __FlashAddr=0x00000000
[14:41:13.101]    __FlashLen=0x00000000
[14:41:13.102]    __FlashArg=0x00000000
[14:41:13.102]    __FlashOp=0x00000000
[14:41:13.102]    __Result=0x00000000
[14:41:13.102]    
[14:41:13.102]    // User-defined
[14:41:13.103]    DbgMCU_CR=0x00000007
[14:41:13.103]    DbgMCU_APB1_Fz=0x00000000
[14:41:13.103]    DbgMCU_APB2_Fz=0x00000000
[14:41:13.103]    DoOptionByteLoading=0x00000000
[14:41:13.104]  </debugvars>
[14:41:13.104]  
[14:41:13.104]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:41:13.104]    <block atomic="false" info="">
[14:41:13.104]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:41:13.105]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:41:13.106]    </block>
[14:41:13.106]    <block atomic="false" info="DbgMCU registers">
[14:41:13.106]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:41:13.107]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[14:41:13.108]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[14:41:13.108]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:41:13.109]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:41:13.110]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:41:13.110]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:41:13.111]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:41:13.112]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:41:13.112]    </block>
[14:41:13.112]  </sequence>
[14:41:13.112]  
[14:41:21.201]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:41:21.201]  
[14:41:21.201]  <debugvars>
[14:41:21.203]    // Pre-defined
[14:41:21.203]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:41:21.203]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:41:21.203]    __dp=0x00000000
[14:41:21.203]    __ap=0x00000000
[14:41:21.203]    __traceout=0x00000000      (Trace Disabled)
[14:41:21.203]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:41:21.204]    __FlashAddr=0x00000000
[14:41:21.204]    __FlashLen=0x00000000
[14:41:21.204]    __FlashArg=0x00000000
[14:41:21.205]    __FlashOp=0x00000000
[14:41:21.205]    __Result=0x00000000
[14:41:21.205]    
[14:41:21.205]    // User-defined
[14:41:21.205]    DbgMCU_CR=0x00000007
[14:41:21.206]    DbgMCU_APB1_Fz=0x00000000
[14:41:21.206]    DbgMCU_APB2_Fz=0x00000000
[14:41:21.206]    DoOptionByteLoading=0x00000000
[14:41:21.206]  </debugvars>
[14:41:21.207]  
[14:41:21.207]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:41:21.207]    <block atomic="false" info="">
[14:41:21.207]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:41:21.208]        // -> [connectionFlash <= 0x00000001]
[14:41:21.208]      __var FLASH_BASE = 0x40022000 ;
[14:41:21.208]        // -> [FLASH_BASE <= 0x40022000]
[14:41:21.208]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:41:21.208]        // -> [FLASH_CR <= 0x40022004]
[14:41:21.208]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:41:21.209]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:41:21.209]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:41:21.209]        // -> [LOCK_BIT <= 0x00000001]
[14:41:21.209]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:41:21.210]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:41:21.210]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:41:21.210]        // -> [FLASH_KEYR <= 0x4002200C]
[14:41:21.210]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:41:21.211]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:41:21.212]      __var FLASH_KEY2 = 0x02030405 ;
[14:41:21.212]        // -> [FLASH_KEY2 <= 0x02030405]
[14:41:21.212]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:41:21.212]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:41:21.213]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:41:21.213]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:41:21.213]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:41:21.213]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:41:21.213]      __var FLASH_CR_Value = 0 ;
[14:41:21.213]        // -> [FLASH_CR_Value <= 0x00000000]
[14:41:21.214]      __var DoDebugPortStop = 1 ;
[14:41:21.214]        // -> [DoDebugPortStop <= 0x00000001]
[14:41:21.214]      __var DP_CTRL_STAT = 0x4 ;
[14:41:21.214]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:41:21.214]      __var DP_SELECT = 0x8 ;
[14:41:21.214]        // -> [DP_SELECT <= 0x00000008]
[14:41:21.215]    </block>
[14:41:21.215]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:41:21.215]      // if-block "connectionFlash && DoOptionByteLoading"
[14:41:21.215]        // =>  FALSE
[14:41:21.215]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:41:21.216]    </control>
[14:41:21.216]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:41:21.216]      // if-block "DoDebugPortStop"
[14:41:21.216]        // =>  TRUE
[14:41:21.216]      <block atomic="false" info="">
[14:41:21.216]        WriteDP(DP_SELECT, 0x00000000);
[14:41:21.217]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:41:21.217]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:41:21.218]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:41:21.218]      </block>
[14:41:21.218]      // end if-block "DoDebugPortStop"
[14:41:21.218]    </control>
[14:41:21.219]  </sequence>
[14:41:21.219]  
[16:14:18.654]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:14:18.654]  
[16:14:18.685]  <debugvars>
[16:14:18.685]    // Pre-defined
[16:14:18.689]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:14:18.689]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:14:18.689]    __dp=0x00000000
[16:14:18.689]    __ap=0x00000000
[16:14:18.689]    __traceout=0x00000000      (Trace Disabled)
[16:14:18.689]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:14:18.689]    __FlashAddr=0x00000000
[16:14:18.689]    __FlashLen=0x00000000
[16:14:18.690]    __FlashArg=0x00000000
[16:14:18.690]    __FlashOp=0x00000000
[16:14:18.690]    __Result=0x00000000
[16:14:18.690]    
[16:14:18.690]    // User-defined
[16:14:18.690]    DbgMCU_CR=0x00000007
[16:14:18.690]    DbgMCU_APB1_Fz=0x00000000
[16:14:18.690]    DbgMCU_APB2_Fz=0x00000000
[16:14:18.690]    DoOptionByteLoading=0x00000000
[16:14:18.690]  </debugvars>
[16:14:18.699]  
[16:14:18.700]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:14:18.700]    <block atomic="false" info="">
[16:14:18.700]      Sequence("CheckID");
[16:14:18.700]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:14:18.701]          <block atomic="false" info="">
[16:14:18.701]            __var pidr1 = 0;
[16:14:18.701]              // -> [pidr1 <= 0x00000000]
[16:14:18.701]            __var pidr2 = 0;
[16:14:18.701]              // -> [pidr2 <= 0x00000000]
[16:14:18.701]            __var jep106id = 0;
[16:14:18.701]              // -> [jep106id <= 0x00000000]
[16:14:18.701]            __var ROMTableBase = 0;
[16:14:18.701]              // -> [ROMTableBase <= 0x00000000]
[16:14:18.701]            __ap = 0;      // AHB-AP
[16:14:18.701]              // -> [__ap <= 0x00000000]
[16:14:18.701]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:14:18.701]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:14:18.701]              // -> [ROMTableBase <= 0xF0000000]
[16:14:18.701]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:14:18.701]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:14:18.701]              // -> [pidr1 <= 0x00000004]
[16:14:18.701]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:14:18.706]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:14:18.711]              // -> [pidr2 <= 0x0000000A]
[16:14:18.711]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:14:18.711]              // -> [jep106id <= 0x00000020]
[16:14:18.711]          </block>
[16:14:18.711]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:14:18.715]            // if-block "jep106id != 0x20"
[16:14:18.715]              // =>  FALSE
[16:14:18.715]            // skip if-block "jep106id != 0x20"
[16:14:18.728]          </control>
[16:14:18.728]        </sequence>
[16:14:18.729]    </block>
[16:14:18.729]  </sequence>
[16:14:18.729]  
[16:14:18.743]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:14:18.743]  
[16:14:18.743]  <debugvars>
[16:14:18.743]    // Pre-defined
[16:14:18.749]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:14:18.749]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:14:18.749]    __dp=0x00000000
[16:14:18.749]    __ap=0x00000000
[16:14:18.749]    __traceout=0x00000000      (Trace Disabled)
[16:14:18.749]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:14:18.752]    __FlashAddr=0x00000000
[16:14:18.759]    __FlashLen=0x00000000
[16:14:18.759]    __FlashArg=0x00000000
[16:14:18.759]    __FlashOp=0x00000000
[16:14:18.759]    __Result=0x00000000
[16:14:18.759]    
[16:14:18.759]    // User-defined
[16:14:18.759]    DbgMCU_CR=0x00000007
[16:14:18.759]    DbgMCU_APB1_Fz=0x00000000
[16:14:18.759]    DbgMCU_APB2_Fz=0x00000000
[16:14:18.759]    DoOptionByteLoading=0x00000000
[16:14:18.759]  </debugvars>
[16:14:18.762]  
[16:14:18.762]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:14:18.762]    <block atomic="false" info="">
[16:14:18.762]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:14:18.762]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:14:18.762]    </block>
[16:14:18.762]    <block atomic="false" info="DbgMCU registers">
[16:14:18.762]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:14:18.762]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:14:18.762]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:14:18.762]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:14:18.767]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:14:18.773]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:14:18.774]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:14:18.774]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:14:18.775]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:14:18.775]    </block>
[16:14:18.776]  </sequence>
[16:14:18.776]  
[16:14:26.711]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:14:26.711]  
[16:14:26.715]  <debugvars>
[16:14:26.715]    // Pre-defined
[16:14:26.715]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:14:26.715]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:14:26.715]    __dp=0x00000000
[16:14:26.715]    __ap=0x00000000
[16:14:26.715]    __traceout=0x00000000      (Trace Disabled)
[16:14:26.715]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:14:26.715]    __FlashAddr=0x00000000
[16:14:26.715]    __FlashLen=0x00000000
[16:14:26.715]    __FlashArg=0x00000000
[16:14:26.715]    __FlashOp=0x00000000
[16:14:26.715]    __Result=0x00000000
[16:14:26.719]    
[16:14:26.719]    // User-defined
[16:14:26.720]    DbgMCU_CR=0x00000007
[16:14:26.720]    DbgMCU_APB1_Fz=0x00000000
[16:14:26.720]    DbgMCU_APB2_Fz=0x00000000
[16:14:26.720]    DoOptionByteLoading=0x00000000
[16:14:26.720]  </debugvars>
[16:14:26.721]  
[16:14:26.721]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:14:26.721]    <block atomic="false" info="">
[16:14:26.721]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:14:26.721]        // -> [connectionFlash <= 0x00000001]
[16:14:26.726]      __var FLASH_BASE = 0x40022000 ;
[16:14:26.726]        // -> [FLASH_BASE <= 0x40022000]
[16:14:26.727]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:14:26.727]        // -> [FLASH_CR <= 0x40022004]
[16:14:26.727]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:14:26.727]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:14:26.727]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:14:26.728]        // -> [LOCK_BIT <= 0x00000001]
[16:14:26.728]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:14:26.728]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:14:26.728]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:14:26.728]        // -> [FLASH_KEYR <= 0x4002200C]
[16:14:26.729]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:14:26.729]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:14:26.729]      __var FLASH_KEY2 = 0x02030405 ;
[16:14:26.729]        // -> [FLASH_KEY2 <= 0x02030405]
[16:14:26.730]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:14:26.730]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:14:26.730]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:14:26.731]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:14:26.731]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:14:26.731]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:14:26.737]      __var FLASH_CR_Value = 0 ;
[16:14:26.737]        // -> [FLASH_CR_Value <= 0x00000000]
[16:14:26.739]      __var DoDebugPortStop = 1 ;
[16:14:26.739]        // -> [DoDebugPortStop <= 0x00000001]
[16:14:26.739]      __var DP_CTRL_STAT = 0x4 ;
[16:14:26.739]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:14:26.739]      __var DP_SELECT = 0x8 ;
[16:14:26.740]        // -> [DP_SELECT <= 0x00000008]
[16:14:26.740]    </block>
[16:14:26.740]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:14:26.740]      // if-block "connectionFlash && DoOptionByteLoading"
[16:14:26.740]        // =>  FALSE
[16:14:26.741]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:14:26.741]    </control>
[16:14:26.741]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:14:26.742]      // if-block "DoDebugPortStop"
[16:14:26.742]        // =>  TRUE
[16:14:26.742]      <block atomic="false" info="">
[16:14:26.742]        WriteDP(DP_SELECT, 0x00000000);
[16:14:26.743]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:14:26.743]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:14:26.743]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:14:26.744]      </block>
[16:14:26.744]      // end if-block "DoDebugPortStop"
[16:14:26.744]    </control>
[16:14:26.744]  </sequence>
[16:14:26.744]  
[16:17:41.980]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:17:41.980]  
[16:17:41.985]  <debugvars>
[16:17:41.988]    // Pre-defined
[16:17:41.988]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:17:41.988]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:17:41.989]    __dp=0x00000000
[16:17:41.989]    __ap=0x00000000
[16:17:41.991]    __traceout=0x00000000      (Trace Disabled)
[16:17:41.991]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:17:41.991]    __FlashAddr=0x00000000
[16:17:41.991]    __FlashLen=0x00000000
[16:17:41.991]    __FlashArg=0x00000000
[16:17:41.991]    __FlashOp=0x00000000
[16:17:41.991]    __Result=0x00000000
[16:17:41.991]    
[16:17:41.991]    // User-defined
[16:17:41.996]    DbgMCU_CR=0x00000007
[16:17:41.996]    DbgMCU_APB1_Fz=0x00000000
[16:17:41.996]    DbgMCU_APB2_Fz=0x00000000
[16:17:41.996]    DoOptionByteLoading=0x00000000
[16:17:41.996]  </debugvars>
[16:17:41.996]  
[16:17:41.996]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:17:41.996]    <block atomic="false" info="">
[16:17:41.996]      Sequence("CheckID");
[16:17:41.996]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:17:42.001]          <block atomic="false" info="">
[16:17:42.001]            __var pidr1 = 0;
[16:17:42.001]              // -> [pidr1 <= 0x00000000]
[16:17:42.001]            __var pidr2 = 0;
[16:17:42.001]              // -> [pidr2 <= 0x00000000]
[16:17:42.001]            __var jep106id = 0;
[16:17:42.001]              // -> [jep106id <= 0x00000000]
[16:17:42.001]            __var ROMTableBase = 0;
[16:17:42.001]              // -> [ROMTableBase <= 0x00000000]
[16:17:42.021]            __ap = 0;      // AHB-AP
[16:17:42.021]              // -> [__ap <= 0x00000000]
[16:17:42.021]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:17:42.023]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:17:42.031]              // -> [ROMTableBase <= 0xF0000000]
[16:17:42.031]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:17:42.031]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:17:42.031]              // -> [pidr1 <= 0x00000004]
[16:17:42.031]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:17:42.031]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:17:42.031]              // -> [pidr2 <= 0x0000000A]
[16:17:42.031]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:17:42.036]              // -> [jep106id <= 0x00000020]
[16:17:42.046]          </block>
[16:17:42.047]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:17:42.047]            // if-block "jep106id != 0x20"
[16:17:42.047]              // =>  FALSE
[16:17:42.048]            // skip if-block "jep106id != 0x20"
[16:17:42.048]          </control>
[16:17:42.048]        </sequence>
[16:17:42.048]    </block>
[16:17:42.050]  </sequence>
[16:17:42.050]  
[16:17:42.068]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:17:42.068]  
[16:17:42.073]  <debugvars>
[16:17:42.073]    // Pre-defined
[16:17:42.073]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:17:42.073]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:17:42.073]    __dp=0x00000000
[16:17:42.073]    __ap=0x00000000
[16:17:42.073]    __traceout=0x00000000      (Trace Disabled)
[16:17:42.084]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:17:42.084]    __FlashAddr=0x00000000
[16:17:42.084]    __FlashLen=0x00000000
[16:17:42.084]    __FlashArg=0x00000000
[16:17:42.096]    __FlashOp=0x00000000
[16:17:42.096]    __Result=0x00000000
[16:17:42.096]    
[16:17:42.096]    // User-defined
[16:17:42.106]    DbgMCU_CR=0x00000007
[16:17:42.106]    DbgMCU_APB1_Fz=0x00000000
[16:17:42.106]    DbgMCU_APB2_Fz=0x00000000
[16:17:42.111]    DoOptionByteLoading=0x00000000
[16:17:42.111]  </debugvars>
[16:17:42.111]  
[16:17:42.111]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:17:42.112]    <block atomic="false" info="">
[16:17:42.112]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:17:42.113]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:17:42.113]    </block>
[16:17:42.113]    <block atomic="false" info="DbgMCU registers">
[16:17:42.113]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:17:42.113]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:17:42.113]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:17:42.117]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:17:42.122]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:17:42.122]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:17:42.122]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:17:42.129]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:17:42.129]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:17:42.129]    </block>
[16:17:42.129]  </sequence>
[16:17:42.129]  
[16:17:50.027]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:17:50.027]  
[16:17:50.053]  <debugvars>
[16:17:50.063]    // Pre-defined
[16:17:50.078]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:17:50.104]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:17:50.117]    __dp=0x00000000
[16:17:50.123]    __ap=0x00000000
[16:17:50.141]    __traceout=0x00000000      (Trace Disabled)
[16:17:50.166]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:17:50.170]    __FlashAddr=0x00000000
[16:17:50.175]    __FlashLen=0x00000000
[16:17:50.179]    __FlashArg=0x00000000
[16:17:50.184]    __FlashOp=0x00000000
[16:17:50.185]    __Result=0x00000000
[16:17:50.205]    
[16:17:50.205]    // User-defined
[16:17:50.213]    DbgMCU_CR=0x00000007
[16:17:50.217]    DbgMCU_APB1_Fz=0x00000000
[16:17:50.225]    DbgMCU_APB2_Fz=0x00000000
[16:17:50.242]    DoOptionByteLoading=0x00000000
[16:17:50.253]  </debugvars>
[16:17:50.255]  
[16:17:50.259]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:17:50.259]    <block atomic="false" info="">
[16:17:50.259]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:17:50.265]        // -> [connectionFlash <= 0x00000001]
[16:17:50.270]      __var FLASH_BASE = 0x40022000 ;
[16:17:50.276]        // -> [FLASH_BASE <= 0x40022000]
[16:17:50.279]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:17:50.284]        // -> [FLASH_CR <= 0x40022004]
[16:17:50.292]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:17:50.295]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:17:50.299]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:17:50.304]        // -> [LOCK_BIT <= 0x00000001]
[16:17:50.310]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:17:50.312]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:17:50.328]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:17:50.342]        // -> [FLASH_KEYR <= 0x4002200C]
[16:17:50.348]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:17:50.360]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:17:50.371]      __var FLASH_KEY2 = 0x02030405 ;
[16:17:50.372]        // -> [FLASH_KEY2 <= 0x02030405]
[16:17:50.373]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:17:50.374]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:17:50.374]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:17:50.387]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:17:50.387]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:17:50.387]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:17:50.387]      __var FLASH_CR_Value = 0 ;
[16:17:50.724]        // -> [FLASH_CR_Value <= 0x00000000]
[16:17:50.724]      __var DoDebugPortStop = 1 ;
[16:17:50.724]        // -> [DoDebugPortStop <= 0x00000001]
[16:17:50.724]      __var DP_CTRL_STAT = 0x4 ;
[16:17:50.738]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:17:50.738]      __var DP_SELECT = 0x8 ;
[16:17:50.738]        // -> [DP_SELECT <= 0x00000008]
[16:17:50.738]    </block>
[16:17:50.738]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:17:50.739]      // if-block "connectionFlash && DoOptionByteLoading"
[16:17:50.739]        // =>  FALSE
[16:17:50.740]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:17:50.740]    </control>
[16:17:50.740]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:17:50.754]      // if-block "DoDebugPortStop"
[16:17:50.754]        // =>  TRUE
[16:17:50.754]      <block atomic="false" info="">
[16:17:50.756]        WriteDP(DP_SELECT, 0x00000000);
[16:17:50.756]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:17:50.756]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:17:50.756]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:17:50.761]      </block>
[16:17:50.761]      // end if-block "DoDebugPortStop"
[16:17:50.763]    </control>
[16:17:50.763]  </sequence>
[16:17:50.763]  
[16:27:41.540]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:27:41.540]  
[16:27:41.541]  <debugvars>
[16:27:41.543]    // Pre-defined
[16:27:41.543]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:27:41.543]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:27:41.543]    __dp=0x00000000
[16:27:41.543]    __ap=0x00000000
[16:27:41.544]    __traceout=0x00000000      (Trace Disabled)
[16:27:41.544]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:27:41.550]    __FlashAddr=0x00000000
[16:27:41.550]    __FlashLen=0x00000000
[16:27:41.550]    __FlashArg=0x00000000
[16:27:41.554]    __FlashOp=0x00000000
[16:27:41.554]    __Result=0x00000000
[16:27:41.561]    
[16:27:41.561]    // User-defined
[16:27:41.563]    DbgMCU_CR=0x00000007
[16:27:41.563]    DbgMCU_APB1_Fz=0x00000000
[16:27:41.564]    DbgMCU_APB2_Fz=0x00000000
[16:27:41.564]    DoOptionByteLoading=0x00000000
[16:27:41.564]  </debugvars>
[16:27:41.564]  
[16:27:41.564]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:27:41.564]    <block atomic="false" info="">
[16:27:41.564]      Sequence("CheckID");
[16:27:41.571]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:27:41.571]          <block atomic="false" info="">
[16:27:41.571]            __var pidr1 = 0;
[16:27:41.571]              // -> [pidr1 <= 0x00000000]
[16:27:41.574]            __var pidr2 = 0;
[16:27:41.574]              // -> [pidr2 <= 0x00000000]
[16:27:41.574]            __var jep106id = 0;
[16:27:41.576]              // -> [jep106id <= 0x00000000]
[16:27:41.583]            __var ROMTableBase = 0;
[16:27:41.583]              // -> [ROMTableBase <= 0x00000000]
[16:27:41.583]            __ap = 0;      // AHB-AP
[16:27:41.583]              // -> [__ap <= 0x00000000]
[16:27:41.585]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:27:41.585]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:27:41.586]              // -> [ROMTableBase <= 0xF0000000]
[16:27:41.586]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:27:41.587]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:27:41.596]              // -> [pidr1 <= 0x00000004]
[16:27:41.596]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:27:41.598]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:27:41.598]              // -> [pidr2 <= 0x0000000A]
[16:27:41.598]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:27:41.598]              // -> [jep106id <= 0x00000020]
[16:27:41.606]          </block>
[16:27:41.606]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:27:41.606]            // if-block "jep106id != 0x20"
[16:27:41.606]              // =>  FALSE
[16:27:41.606]            // skip if-block "jep106id != 0x20"
[16:27:41.606]          </control>
[16:27:41.606]        </sequence>
[16:27:41.606]    </block>
[16:27:41.606]  </sequence>
[16:27:41.606]  
[16:27:41.619]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:27:41.619]  
[16:27:41.619]  <debugvars>
[16:27:41.619]    // Pre-defined
[16:27:41.624]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:27:41.624]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:27:41.624]    __dp=0x00000000
[16:27:41.624]    __ap=0x00000000
[16:27:41.624]    __traceout=0x00000000      (Trace Disabled)
[16:27:41.624]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:27:41.624]    __FlashAddr=0x00000000
[16:27:41.624]    __FlashLen=0x00000000
[16:27:41.624]    __FlashArg=0x00000000
[16:27:41.624]    __FlashOp=0x00000000
[16:27:41.624]    __Result=0x00000000
[16:27:41.624]    
[16:27:41.624]    // User-defined
[16:27:41.624]    DbgMCU_CR=0x00000007
[16:27:41.624]    DbgMCU_APB1_Fz=0x00000000
[16:27:41.624]    DbgMCU_APB2_Fz=0x00000000
[16:27:41.624]    DoOptionByteLoading=0x00000000
[16:27:41.624]  </debugvars>
[16:27:41.624]  
[16:27:41.624]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:27:41.624]    <block atomic="false" info="">
[16:27:41.629]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:27:41.630]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:27:41.630]    </block>
[16:27:41.630]    <block atomic="false" info="DbgMCU registers">
[16:27:41.630]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:27:41.630]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:27:41.630]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:27:41.630]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:27:41.634]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:27:41.634]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:27:41.634]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:27:41.634]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:27:41.649]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:27:41.649]    </block>
[16:27:41.651]  </sequence>
[16:27:41.652]  
[16:27:50.020]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:27:50.020]  
[16:27:50.025]  <debugvars>
[16:27:50.025]    // Pre-defined
[16:27:50.037]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:27:50.037]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:27:50.039]    __dp=0x00000000
[16:27:50.039]    __ap=0x00000000
[16:27:50.039]    __traceout=0x00000000      (Trace Disabled)
[16:27:50.039]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:27:50.040]    __FlashAddr=0x00000000
[16:27:50.040]    __FlashLen=0x00000000
[16:27:50.040]    __FlashArg=0x00000000
[16:27:50.042]    __FlashOp=0x00000000
[16:27:50.045]    __Result=0x00000000
[16:27:50.045]    
[16:27:50.045]    // User-defined
[16:27:50.045]    DbgMCU_CR=0x00000007
[16:27:50.045]    DbgMCU_APB1_Fz=0x00000000
[16:27:50.045]    DbgMCU_APB2_Fz=0x00000000
[16:27:50.045]    DoOptionByteLoading=0x00000000
[16:27:50.045]  </debugvars>
[16:27:50.048]  
[16:27:50.050]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:27:50.050]    <block atomic="false" info="">
[16:27:50.050]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:27:50.050]        // -> [connectionFlash <= 0x00000001]
[16:27:50.050]      __var FLASH_BASE = 0x40022000 ;
[16:27:50.052]        // -> [FLASH_BASE <= 0x40022000]
[16:27:50.052]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:27:50.052]        // -> [FLASH_CR <= 0x40022004]
[16:27:50.052]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:27:50.052]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:27:50.061]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:27:50.061]        // -> [LOCK_BIT <= 0x00000001]
[16:27:50.061]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:27:50.061]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:27:50.062]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:27:50.062]        // -> [FLASH_KEYR <= 0x4002200C]
[16:27:50.063]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:27:50.063]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:27:50.068]      __var FLASH_KEY2 = 0x02030405 ;
[16:27:50.068]        // -> [FLASH_KEY2 <= 0x02030405]
[16:27:50.068]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:27:50.070]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:27:50.070]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:27:50.070]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:27:50.070]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:27:50.070]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:27:50.070]      __var FLASH_CR_Value = 0 ;
[16:27:50.070]        // -> [FLASH_CR_Value <= 0x00000000]
[16:27:50.079]      __var DoDebugPortStop = 1 ;
[16:27:50.081]        // -> [DoDebugPortStop <= 0x00000001]
[16:27:50.081]      __var DP_CTRL_STAT = 0x4 ;
[16:27:50.081]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:27:50.083]      __var DP_SELECT = 0x8 ;
[16:27:50.085]        // -> [DP_SELECT <= 0x00000008]
[16:27:50.085]    </block>
[16:27:50.085]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:27:50.085]      // if-block "connectionFlash && DoOptionByteLoading"
[16:27:50.085]        // =>  FALSE
[16:27:50.085]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:27:50.085]    </control>
[16:27:50.092]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:27:50.092]      // if-block "DoDebugPortStop"
[16:27:50.092]        // =>  TRUE
[16:27:50.092]      <block atomic="false" info="">
[16:27:50.094]        WriteDP(DP_SELECT, 0x00000000);
[16:27:50.094]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:27:50.094]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:27:50.100]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:27:50.100]      </block>
[16:27:50.102]      // end if-block "DoDebugPortStop"
[16:27:50.102]    </control>
[16:27:50.102]  </sequence>
[16:27:50.102]  
[16:33:07.033]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:33:07.033]  
[16:33:07.042]  <debugvars>
[16:33:07.061]    // Pre-defined
[16:33:07.062]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:33:07.062]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:33:07.068]    __dp=0x00000000
[16:33:07.069]    __ap=0x00000000
[16:33:07.069]    __traceout=0x00000000      (Trace Disabled)
[16:33:07.070]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:33:07.070]    __FlashAddr=0x00000000
[16:33:07.070]    __FlashLen=0x00000000
[16:33:07.070]    __FlashArg=0x00000000
[16:33:07.071]    __FlashOp=0x00000000
[16:33:07.071]    __Result=0x00000000
[16:33:07.071]    
[16:33:07.071]    // User-defined
[16:33:07.071]    DbgMCU_CR=0x00000007
[16:33:07.071]    DbgMCU_APB1_Fz=0x00000000
[16:33:07.071]    DbgMCU_APB2_Fz=0x00000000
[16:33:07.072]    DoOptionByteLoading=0x00000000
[16:33:07.072]  </debugvars>
[16:33:07.083]  
[16:33:07.083]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:33:07.083]    <block atomic="false" info="">
[16:33:07.083]      Sequence("CheckID");
[16:33:07.092]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:33:07.092]          <block atomic="false" info="">
[16:33:07.092]            __var pidr1 = 0;
[16:33:07.093]              // -> [pidr1 <= 0x00000000]
[16:33:07.093]            __var pidr2 = 0;
[16:33:07.093]              // -> [pidr2 <= 0x00000000]
[16:33:07.103]            __var jep106id = 0;
[16:33:07.103]              // -> [jep106id <= 0x00000000]
[16:33:07.103]            __var ROMTableBase = 0;
[16:33:07.104]              // -> [ROMTableBase <= 0x00000000]
[16:33:07.104]            __ap = 0;      // AHB-AP
[16:33:07.104]              // -> [__ap <= 0x00000000]
[16:33:07.105]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:33:07.105]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:33:07.116]              // -> [ROMTableBase <= 0xF0000000]
[16:33:07.116]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:33:07.117]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:33:07.124]              // -> [pidr1 <= 0x00000004]
[16:33:07.124]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:33:07.125]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:33:07.126]              // -> [pidr2 <= 0x0000000A]
[16:33:07.126]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:33:07.126]              // -> [jep106id <= 0x00000020]
[16:33:07.126]          </block>
[16:33:07.127]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:33:07.127]            // if-block "jep106id != 0x20"
[16:33:07.127]              // =>  FALSE
[16:33:07.128]            // skip if-block "jep106id != 0x20"
[16:33:07.128]          </control>
[16:33:07.129]        </sequence>
[16:33:07.129]    </block>
[16:33:07.129]  </sequence>
[16:33:07.130]  
[16:33:07.142]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:33:07.142]  
[16:33:07.149]  <debugvars>
[16:33:07.150]    // Pre-defined
[16:33:07.150]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:33:07.150]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:33:07.151]    __dp=0x00000000
[16:33:07.151]    __ap=0x00000000
[16:33:07.151]    __traceout=0x00000000      (Trace Disabled)
[16:33:07.151]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:33:07.152]    __FlashAddr=0x00000000
[16:33:07.152]    __FlashLen=0x00000000
[16:33:07.152]    __FlashArg=0x00000000
[16:33:07.152]    __FlashOp=0x00000000
[16:33:07.153]    __Result=0x00000000
[16:33:07.153]    
[16:33:07.153]    // User-defined
[16:33:07.154]    DbgMCU_CR=0x00000007
[16:33:07.164]    DbgMCU_APB1_Fz=0x00000000
[16:33:07.165]    DbgMCU_APB2_Fz=0x00000000
[16:33:07.165]    DoOptionByteLoading=0x00000000
[16:33:07.166]  </debugvars>
[16:33:07.167]  
[16:33:07.167]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:33:07.167]    <block atomic="false" info="">
[16:33:07.168]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:33:07.169]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:33:07.169]    </block>
[16:33:07.169]    <block atomic="false" info="DbgMCU registers">
[16:33:07.169]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:33:07.171]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:33:07.176]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:33:07.186]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:33:07.187]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:33:07.193]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:33:07.194]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:33:07.197]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:33:07.197]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:33:07.198]    </block>
[16:33:07.199]  </sequence>
[16:33:07.210]  
[16:33:15.143]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:33:15.143]  
[16:33:15.143]  <debugvars>
[16:33:15.144]    // Pre-defined
[16:33:15.144]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:33:15.144]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:33:15.145]    __dp=0x00000000
[16:33:15.145]    __ap=0x00000000
[16:33:15.145]    __traceout=0x00000000      (Trace Disabled)
[16:33:15.145]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:33:15.146]    __FlashAddr=0x00000000
[16:33:15.146]    __FlashLen=0x00000000
[16:33:15.146]    __FlashArg=0x00000000
[16:33:15.146]    __FlashOp=0x00000000
[16:33:15.147]    __Result=0x00000000
[16:33:15.147]    
[16:33:15.147]    // User-defined
[16:33:15.147]    DbgMCU_CR=0x00000007
[16:33:15.147]    DbgMCU_APB1_Fz=0x00000000
[16:33:15.147]    DbgMCU_APB2_Fz=0x00000000
[16:33:15.147]    DoOptionByteLoading=0x00000000
[16:33:15.147]  </debugvars>
[16:33:15.148]  
[16:33:15.148]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:33:15.149]    <block atomic="false" info="">
[16:33:15.149]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:33:15.149]        // -> [connectionFlash <= 0x00000001]
[16:33:15.150]      __var FLASH_BASE = 0x40022000 ;
[16:33:15.150]        // -> [FLASH_BASE <= 0x40022000]
[16:33:15.150]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:33:15.150]        // -> [FLASH_CR <= 0x40022004]
[16:33:15.150]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:33:15.151]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:33:15.151]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:33:15.151]        // -> [LOCK_BIT <= 0x00000001]
[16:33:15.151]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:33:15.152]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:33:15.152]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:33:15.152]        // -> [FLASH_KEYR <= 0x4002200C]
[16:33:15.153]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:33:15.153]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:33:15.153]      __var FLASH_KEY2 = 0x02030405 ;
[16:33:15.153]        // -> [FLASH_KEY2 <= 0x02030405]
[16:33:15.153]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:33:15.154]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:33:15.154]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:33:15.154]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:33:15.154]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:33:15.154]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:33:15.154]      __var FLASH_CR_Value = 0 ;
[16:33:15.155]        // -> [FLASH_CR_Value <= 0x00000000]
[16:33:15.155]      __var DoDebugPortStop = 1 ;
[16:33:15.155]        // -> [DoDebugPortStop <= 0x00000001]
[16:33:15.155]      __var DP_CTRL_STAT = 0x4 ;
[16:33:15.155]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:33:15.155]      __var DP_SELECT = 0x8 ;
[16:33:15.155]        // -> [DP_SELECT <= 0x00000008]
[16:33:15.156]    </block>
[16:33:15.156]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:33:15.156]      // if-block "connectionFlash && DoOptionByteLoading"
[16:33:15.157]        // =>  FALSE
[16:33:15.157]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:33:15.157]    </control>
[16:33:15.158]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:33:15.158]      // if-block "DoDebugPortStop"
[16:33:15.158]        // =>  TRUE
[16:33:15.158]      <block atomic="false" info="">
[16:33:15.158]        WriteDP(DP_SELECT, 0x00000000);
[16:33:15.159]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:33:15.159]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:33:15.160]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:33:15.160]      </block>
[16:33:15.160]      // end if-block "DoDebugPortStop"
[16:33:15.161]    </control>
[16:33:15.161]  </sequence>
[16:33:15.161]  
[17:32:30.762]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[17:32:30.762]  
[17:32:30.779]  <debugvars>
[17:32:30.780]    // Pre-defined
[17:32:30.780]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:32:30.780]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:32:30.780]    __dp=0x00000000
[17:32:30.780]    __ap=0x00000000
[17:32:30.781]    __traceout=0x00000000      (Trace Disabled)
[17:32:30.781]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:32:30.781]    __FlashAddr=0x00000000
[17:32:30.781]    __FlashLen=0x00000000
[17:32:30.781]    __FlashArg=0x00000000
[17:32:30.782]    __FlashOp=0x00000000
[17:32:30.782]    __Result=0x00000000
[17:32:30.782]    
[17:32:30.782]    // User-defined
[17:32:30.782]    DbgMCU_CR=0x00000007
[17:32:30.782]    DbgMCU_APB1_Fz=0x00000000
[17:32:30.783]    DbgMCU_APB2_Fz=0x00000000
[17:32:30.783]    DoOptionByteLoading=0x00000000
[17:32:30.784]  </debugvars>
[17:32:30.784]  
[17:32:30.784]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[17:32:30.784]    <block atomic="false" info="">
[17:32:30.784]      Sequence("CheckID");
[17:32:30.784]        <sequence name="CheckID" Pname="" disable="false" info="">
[17:32:30.785]          <block atomic="false" info="">
[17:32:30.785]            __var pidr1 = 0;
[17:32:30.785]              // -> [pidr1 <= 0x00000000]
[17:32:30.785]            __var pidr2 = 0;
[17:32:30.785]              // -> [pidr2 <= 0x00000000]
[17:32:30.786]            __var jep106id = 0;
[17:32:30.786]              // -> [jep106id <= 0x00000000]
[17:32:30.786]            __var ROMTableBase = 0;
[17:32:30.786]              // -> [ROMTableBase <= 0x00000000]
[17:32:30.786]            __ap = 0;      // AHB-AP
[17:32:30.787]              // -> [__ap <= 0x00000000]
[17:32:30.787]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[17:32:30.787]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[17:32:30.788]              // -> [ROMTableBase <= 0xF0000000]
[17:32:30.788]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[17:32:30.789]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[17:32:30.789]              // -> [pidr1 <= 0x00000004]
[17:32:30.789]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[17:32:30.790]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[17:32:30.790]              // -> [pidr2 <= 0x0000000A]
[17:32:30.790]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[17:32:30.790]              // -> [jep106id <= 0x00000020]
[17:32:30.791]          </block>
[17:32:30.791]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[17:32:30.791]            // if-block "jep106id != 0x20"
[17:32:30.791]              // =>  FALSE
[17:32:30.791]            // skip if-block "jep106id != 0x20"
[17:32:30.791]          </control>
[17:32:30.792]        </sequence>
[17:32:30.792]    </block>
[17:32:30.792]  </sequence>
[17:32:30.792]  
[17:32:30.804]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[17:32:30.804]  
[17:32:30.804]  <debugvars>
[17:32:30.804]    // Pre-defined
[17:32:30.804]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:32:30.805]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:32:30.805]    __dp=0x00000000
[17:32:30.805]    __ap=0x00000000
[17:32:30.805]    __traceout=0x00000000      (Trace Disabled)
[17:32:30.805]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:32:30.806]    __FlashAddr=0x00000000
[17:32:30.806]    __FlashLen=0x00000000
[17:32:30.806]    __FlashArg=0x00000000
[17:32:30.806]    __FlashOp=0x00000000
[17:32:30.806]    __Result=0x00000000
[17:32:30.806]    
[17:32:30.806]    // User-defined
[17:32:30.807]    DbgMCU_CR=0x00000007
[17:32:30.807]    DbgMCU_APB1_Fz=0x00000000
[17:32:30.807]    DbgMCU_APB2_Fz=0x00000000
[17:32:30.807]    DoOptionByteLoading=0x00000000
[17:32:30.807]  </debugvars>
[17:32:30.808]  
[17:32:30.808]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[17:32:30.808]    <block atomic="false" info="">
[17:32:30.809]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[17:32:30.809]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[17:32:30.810]    </block>
[17:32:30.810]    <block atomic="false" info="DbgMCU registers">
[17:32:30.810]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[17:32:30.811]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[17:32:30.811]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[17:32:30.812]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[17:32:30.812]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[17:32:30.812]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[17:32:30.813]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:32:30.813]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[17:32:30.814]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[17:32:30.814]    </block>
[17:32:30.815]  </sequence>
[17:32:30.815]  
[17:32:38.739]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[17:32:38.739]  
[17:32:38.740]  <debugvars>
[17:32:38.741]    // Pre-defined
[17:32:38.741]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[17:32:38.741]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[17:32:38.742]    __dp=0x00000000
[17:32:38.742]    __ap=0x00000000
[17:32:38.742]    __traceout=0x00000000      (Trace Disabled)
[17:32:38.743]    __errorcontrol=0x00000000  (Skip Errors="False")
[17:32:38.743]    __FlashAddr=0x00000000
[17:32:38.743]    __FlashLen=0x00000000
[17:32:38.743]    __FlashArg=0x00000000
[17:32:38.744]    __FlashOp=0x00000000
[17:32:38.744]    __Result=0x00000000
[17:32:38.744]    
[17:32:38.744]    // User-defined
[17:32:38.744]    DbgMCU_CR=0x00000007
[17:32:38.745]    DbgMCU_APB1_Fz=0x00000000
[17:32:38.745]    DbgMCU_APB2_Fz=0x00000000
[17:32:38.745]    DoOptionByteLoading=0x00000000
[17:32:38.745]  </debugvars>
[17:32:38.746]  
[17:32:38.746]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[17:32:38.746]    <block atomic="false" info="">
[17:32:38.746]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[17:32:38.747]        // -> [connectionFlash <= 0x00000001]
[17:32:38.747]      __var FLASH_BASE = 0x40022000 ;
[17:32:38.747]        // -> [FLASH_BASE <= 0x40022000]
[17:32:38.747]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[17:32:38.747]        // -> [FLASH_CR <= 0x40022004]
[17:32:38.747]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[17:32:38.748]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[17:32:38.748]      __var LOCK_BIT = ( 1 << 0 ) ;
[17:32:38.748]        // -> [LOCK_BIT <= 0x00000001]
[17:32:38.748]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[17:32:38.748]        // -> [OPTLOCK_BIT <= 0x00000004]
[17:32:38.748]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[17:32:38.749]        // -> [FLASH_KEYR <= 0x4002200C]
[17:32:38.749]      __var FLASH_KEY1 = 0x89ABCDEF ;
[17:32:38.749]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[17:32:38.749]      __var FLASH_KEY2 = 0x02030405 ;
[17:32:38.749]        // -> [FLASH_KEY2 <= 0x02030405]
[17:32:38.750]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[17:32:38.751]        // -> [FLASH_OPTKEYR <= 0x40022014]
[17:32:38.751]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[17:32:38.751]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[17:32:38.751]      __var FLASH_OPTKEY2 = 0x24252627 ;
[17:32:38.752]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[17:32:38.752]      __var FLASH_CR_Value = 0 ;
[17:32:38.752]        // -> [FLASH_CR_Value <= 0x00000000]
[17:32:38.752]      __var DoDebugPortStop = 1 ;
[17:32:38.753]        // -> [DoDebugPortStop <= 0x00000001]
[17:32:38.753]      __var DP_CTRL_STAT = 0x4 ;
[17:32:38.753]        // -> [DP_CTRL_STAT <= 0x00000004]
[17:32:38.753]      __var DP_SELECT = 0x8 ;
[17:32:38.753]        // -> [DP_SELECT <= 0x00000008]
[17:32:38.754]    </block>
[17:32:38.754]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[17:32:38.754]      // if-block "connectionFlash && DoOptionByteLoading"
[17:32:38.754]        // =>  FALSE
[17:32:38.755]      // skip if-block "connectionFlash && DoOptionByteLoading"
[17:32:38.755]    </control>
[17:32:38.755]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[17:32:38.755]      // if-block "DoDebugPortStop"
[17:32:38.755]        // =>  TRUE
[17:32:38.756]      <block atomic="false" info="">
[17:32:38.756]        WriteDP(DP_SELECT, 0x00000000);
[17:32:38.756]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[17:32:38.756]        WriteDP(DP_CTRL_STAT, 0x00000000);
[17:32:38.757]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[17:32:38.757]      </block>
[17:32:38.757]      // end if-block "DoDebugPortStop"
[17:32:38.758]    </control>
[17:32:38.758]  </sequence>
[17:32:38.758]  
