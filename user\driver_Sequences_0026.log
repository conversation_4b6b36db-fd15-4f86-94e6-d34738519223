/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : D:\2025work\STM32L072PRO\source-application\user\driver_Sequences_0026.log
 *  Created     : 14:12:29 (22/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : D:\2025work\STM32L072PRO\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[14:12:29.055]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:12:29.055]  
[14:12:29.076]  <debugvars>
[14:12:29.096]    // Pre-defined
[14:12:29.117]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:12:29.141]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:12:29.141]    __dp=0x00000000
[14:12:29.142]    __ap=0x00000000
[14:12:29.142]    __traceout=0x00000000      (Trace Disabled)
[14:12:29.142]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:12:29.142]    __FlashAddr=0x00000000
[14:12:29.142]    __FlashLen=0x00000000
[14:12:29.142]    __FlashArg=0x00000000
[14:12:29.142]    __FlashOp=0x00000000
[14:12:29.142]    __Result=0x00000000
[14:12:29.142]    
[14:12:29.142]    // User-defined
[14:12:29.142]    DbgMCU_CR=0x00000007
[14:12:29.142]    DbgMCU_APB1_Fz=0x00000000
[14:12:29.142]    DbgMCU_APB2_Fz=0x00000000
[14:12:29.142]    DoOptionByteLoading=0x00000000
[14:12:29.142]  </debugvars>
[14:12:29.142]  
[14:12:29.142]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:12:29.142]    <block atomic="false" info="">
[14:12:29.142]      Sequence("CheckID");
[14:12:29.142]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:12:29.146]          <block atomic="false" info="">
[14:12:29.146]            __var pidr1 = 0;
[14:12:29.146]              // -> [pidr1 <= 0x00000000]
[14:12:29.146]            __var pidr2 = 0;
[14:12:29.146]              // -> [pidr2 <= 0x00000000]
[14:12:29.147]            __var jep106id = 0;
[14:12:29.147]              // -> [jep106id <= 0x00000000]
[14:12:29.147]            __var ROMTableBase = 0;
[14:12:29.147]              // -> [ROMTableBase <= 0x00000000]
[14:12:29.147]            __ap = 0;      // AHB-AP
[14:12:29.147]              // -> [__ap <= 0x00000000]
[14:12:29.148]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:12:29.148]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:12:29.148]              // -> [ROMTableBase <= 0xF0000000]
[14:12:29.149]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:12:29.150]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:12:29.150]              // -> [pidr1 <= 0x00000004]
[14:12:29.151]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:12:29.152]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:12:29.152]              // -> [pidr2 <= 0x0000000A]
[14:12:29.152]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:12:29.152]              // -> [jep106id <= 0x00000020]
[14:12:29.152]          </block>
[14:12:29.153]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:12:29.153]            // if-block "jep106id != 0x20"
[14:12:29.154]              // =>  FALSE
[14:12:29.154]            // skip if-block "jep106id != 0x20"
[14:12:29.154]          </control>
[14:12:29.154]        </sequence>
[14:12:29.155]    </block>
[14:12:29.155]  </sequence>
[14:12:29.155]  
[14:12:29.166]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:12:29.166]  
[14:12:29.166]  <debugvars>
[14:12:29.166]    // Pre-defined
[14:12:29.166]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:12:29.166]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:12:29.166]    __dp=0x00000000
[14:12:29.166]    __ap=0x00000000
[14:12:29.166]    __traceout=0x00000000      (Trace Disabled)
[14:12:29.166]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:12:29.166]    __FlashAddr=0x00000000
[14:12:29.166]    __FlashLen=0x00000000
[14:12:29.166]    __FlashArg=0x00000000
[14:12:29.166]    __FlashOp=0x00000000
[14:12:29.166]    __Result=0x00000000
[14:12:29.166]    
[14:12:29.166]    // User-defined
[14:12:29.171]    DbgMCU_CR=0x00000007
[14:12:29.171]    DbgMCU_APB1_Fz=0x00000000
[14:12:29.171]    DbgMCU_APB2_Fz=0x00000000
[14:12:29.171]    DoOptionByteLoading=0x00000000
[14:12:29.171]  </debugvars>
[14:12:29.171]  
[14:12:29.171]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:12:29.171]    <block atomic="false" info="">
[14:12:29.171]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:12:29.171]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:12:29.171]    </block>
[14:12:29.171]    <block atomic="false" info="DbgMCU registers">
[14:12:29.171]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:12:29.171]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[14:12:29.171]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[14:12:29.176]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:12:29.176]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:12:29.176]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:12:29.176]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:12:29.176]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:12:29.176]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:12:29.176]    </block>
[14:12:29.176]  </sequence>
[14:12:29.176]  
[14:12:37.323]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:12:37.323]  
[14:12:37.324]  <debugvars>
[14:12:37.325]    // Pre-defined
[14:12:37.325]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:12:37.326]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:12:37.326]    __dp=0x00000000
[14:12:37.326]    __ap=0x00000000
[14:12:37.326]    __traceout=0x00000000      (Trace Disabled)
[14:12:37.327]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:12:37.327]    __FlashAddr=0x00000000
[14:12:37.327]    __FlashLen=0x00000000
[14:12:37.327]    __FlashArg=0x00000000
[14:12:37.327]    __FlashOp=0x00000000
[14:12:37.328]    __Result=0x00000000
[14:12:37.329]    
[14:12:37.329]    // User-defined
[14:12:37.329]    DbgMCU_CR=0x00000007
[14:12:37.329]    DbgMCU_APB1_Fz=0x00000000
[14:12:37.330]    DbgMCU_APB2_Fz=0x00000000
[14:12:37.331]    DoOptionByteLoading=0x00000000
[14:12:37.331]  </debugvars>
[14:12:37.331]  
[14:12:37.332]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:12:37.332]    <block atomic="false" info="">
[14:12:37.332]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:12:37.332]        // -> [connectionFlash <= 0x00000001]
[14:12:37.332]      __var FLASH_BASE = 0x40022000 ;
[14:12:37.333]        // -> [FLASH_BASE <= 0x40022000]
[14:12:37.333]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:12:37.333]        // -> [FLASH_CR <= 0x40022004]
[14:12:37.333]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:12:37.333]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:12:37.333]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:12:37.333]        // -> [LOCK_BIT <= 0x00000001]
[14:12:37.333]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:12:37.333]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:12:37.334]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:12:37.335]        // -> [FLASH_KEYR <= 0x4002200C]
[14:12:37.335]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:12:37.335]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:12:37.335]      __var FLASH_KEY2 = 0x02030405 ;
[14:12:37.335]        // -> [FLASH_KEY2 <= 0x02030405]
[14:12:37.336]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:12:37.337]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:12:37.337]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:12:37.337]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:12:37.338]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:12:37.338]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:12:37.338]      __var FLASH_CR_Value = 0 ;
[14:12:37.339]        // -> [FLASH_CR_Value <= 0x00000000]
[14:12:37.339]      __var DoDebugPortStop = 1 ;
[14:12:37.339]        // -> [DoDebugPortStop <= 0x00000001]
[14:12:37.339]      __var DP_CTRL_STAT = 0x4 ;
[14:12:37.339]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:12:37.339]      __var DP_SELECT = 0x8 ;
[14:12:37.339]        // -> [DP_SELECT <= 0x00000008]
[14:12:37.341]    </block>
[14:12:37.341]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:12:37.341]      // if-block "connectionFlash && DoOptionByteLoading"
[14:12:37.341]        // =>  FALSE
[14:12:37.341]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:12:37.342]    </control>
[14:12:37.343]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:12:37.343]      // if-block "DoDebugPortStop"
[14:12:37.343]        // =>  TRUE
[14:12:37.344]      <block atomic="false" info="">
[14:12:37.344]        WriteDP(DP_SELECT, 0x00000000);
[14:12:37.344]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:12:37.344]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:12:37.345]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:12:37.345]      </block>
[14:12:37.345]      // end if-block "DoDebugPortStop"
[14:12:37.345]    </control>
[14:12:37.346]  </sequence>
[14:12:37.346]  
[15:24:05.212]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:24:05.212]  
[15:24:05.231]  <debugvars>
[15:24:05.231]    // Pre-defined
[15:24:05.231]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:24:05.231]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:24:05.231]    __dp=0x00000000
[15:24:05.231]    __ap=0x00000000
[15:24:05.231]    __traceout=0x00000000      (Trace Disabled)
[15:24:05.231]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:24:05.231]    __FlashAddr=0x00000000
[15:24:05.231]    __FlashLen=0x00000000
[15:24:05.234]    __FlashArg=0x00000000
[15:24:05.235]    __FlashOp=0x00000000
[15:24:05.235]    __Result=0x00000000
[15:24:05.236]    
[15:24:05.236]    // User-defined
[15:24:05.237]    DbgMCU_CR=0x00000007
[15:24:05.237]    DbgMCU_APB1_Fz=0x00000000
[15:24:05.238]    DbgMCU_APB2_Fz=0x00000000
[15:24:05.238]    DoOptionByteLoading=0x00000000
[15:24:05.239]  </debugvars>
[15:24:05.239]  
[15:24:05.240]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:24:05.240]    <block atomic="false" info="">
[15:24:05.241]      Sequence("CheckID");
[15:24:05.241]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:24:05.241]          <block atomic="false" info="">
[15:24:05.241]            __var pidr1 = 0;
[15:24:05.242]              // -> [pidr1 <= 0x00000000]
[15:24:05.242]            __var pidr2 = 0;
[15:24:05.242]              // -> [pidr2 <= 0x00000000]
[15:24:05.242]            __var jep106id = 0;
[15:24:05.242]              // -> [jep106id <= 0x00000000]
[15:24:05.243]            __var ROMTableBase = 0;
[15:24:05.243]              // -> [ROMTableBase <= 0x00000000]
[15:24:05.243]            __ap = 0;      // AHB-AP
[15:24:05.244]              // -> [__ap <= 0x00000000]
[15:24:05.244]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:24:05.245]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:24:05.245]              // -> [ROMTableBase <= 0xF0000000]
[15:24:05.246]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:24:05.247]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:24:05.247]              // -> [pidr1 <= 0x00000004]
[15:24:05.247]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:24:05.248]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:24:05.249]              // -> [pidr2 <= 0x0000000A]
[15:24:05.249]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:24:05.249]              // -> [jep106id <= 0x00000020]
[15:24:05.249]          </block>
[15:24:05.250]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:24:05.250]            // if-block "jep106id != 0x20"
[15:24:05.250]              // =>  FALSE
[15:24:05.250]            // skip if-block "jep106id != 0x20"
[15:24:05.251]          </control>
[15:24:05.251]        </sequence>
[15:24:05.251]    </block>
[15:24:05.252]  </sequence>
[15:24:05.252]  
[15:24:05.264]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:24:05.264]  
[15:24:05.265]  <debugvars>
[15:24:05.265]    // Pre-defined
[15:24:05.265]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:24:05.265]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:24:05.265]    __dp=0x00000000
[15:24:05.267]    __ap=0x00000000
[15:24:05.267]    __traceout=0x00000000      (Trace Disabled)
[15:24:05.267]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:24:05.267]    __FlashAddr=0x00000000
[15:24:05.267]    __FlashLen=0x00000000
[15:24:05.267]    __FlashArg=0x00000000
[15:24:05.267]    __FlashOp=0x00000000
[15:24:05.267]    __Result=0x00000000
[15:24:05.267]    
[15:24:05.267]    // User-defined
[15:24:05.267]    DbgMCU_CR=0x00000007
[15:24:05.267]    DbgMCU_APB1_Fz=0x00000000
[15:24:05.267]    DbgMCU_APB2_Fz=0x00000000
[15:24:05.267]    DoOptionByteLoading=0x00000000
[15:24:05.267]  </debugvars>
[15:24:05.267]  
[15:24:05.267]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:24:05.267]    <block atomic="false" info="">
[15:24:05.267]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:24:05.272]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:24:05.272]    </block>
[15:24:05.272]    <block atomic="false" info="DbgMCU registers">
[15:24:05.272]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:24:05.272]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[15:24:05.272]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[15:24:05.272]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:24:05.272]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:24:05.272]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:24:05.277]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:24:05.277]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:24:05.278]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:24:05.278]    </block>
[15:24:05.278]  </sequence>
[15:24:05.279]  
[15:24:13.261]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:24:13.262]  
[15:24:13.262]  <debugvars>
[15:24:13.262]    // Pre-defined
[15:24:13.262]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:24:13.263]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:24:13.263]    __dp=0x00000000
[15:24:13.263]    __ap=0x00000000
[15:24:13.264]    __traceout=0x00000000      (Trace Disabled)
[15:24:13.264]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:24:13.264]    __FlashAddr=0x00000000
[15:24:13.264]    __FlashLen=0x00000000
[15:24:13.265]    __FlashArg=0x00000000
[15:24:13.265]    __FlashOp=0x00000000
[15:24:13.265]    __Result=0x00000000
[15:24:13.266]    
[15:24:13.266]    // User-defined
[15:24:13.266]    DbgMCU_CR=0x00000007
[15:24:13.266]    DbgMCU_APB1_Fz=0x00000000
[15:24:13.267]    DbgMCU_APB2_Fz=0x00000000
[15:24:13.267]    DoOptionByteLoading=0x00000000
[15:24:13.267]  </debugvars>
[15:24:13.267]  
[15:24:13.267]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:24:13.268]    <block atomic="false" info="">
[15:24:13.268]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:24:13.268]        // -> [connectionFlash <= 0x00000001]
[15:24:13.268]      __var FLASH_BASE = 0x40022000 ;
[15:24:13.269]        // -> [FLASH_BASE <= 0x40022000]
[15:24:13.269]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:24:13.270]        // -> [FLASH_CR <= 0x40022004]
[15:24:13.270]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:24:13.270]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:24:13.270]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:24:13.271]        // -> [LOCK_BIT <= 0x00000001]
[15:24:13.271]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:24:13.271]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:24:13.271]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:24:13.271]        // -> [FLASH_KEYR <= 0x4002200C]
[15:24:13.272]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:24:13.272]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:24:13.272]      __var FLASH_KEY2 = 0x02030405 ;
[15:24:13.272]        // -> [FLASH_KEY2 <= 0x02030405]
[15:24:13.272]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:24:13.272]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:24:13.273]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:24:13.273]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:24:13.273]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:24:13.273]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:24:13.273]      __var FLASH_CR_Value = 0 ;
[15:24:13.273]        // -> [FLASH_CR_Value <= 0x00000000]
[15:24:13.273]      __var DoDebugPortStop = 1 ;
[15:24:13.274]        // -> [DoDebugPortStop <= 0x00000001]
[15:24:13.274]      __var DP_CTRL_STAT = 0x4 ;
[15:24:13.275]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:24:13.275]      __var DP_SELECT = 0x8 ;
[15:24:13.275]        // -> [DP_SELECT <= 0x00000008]
[15:24:13.275]    </block>
[15:24:13.275]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:24:13.275]      // if-block "connectionFlash && DoOptionByteLoading"
[15:24:13.275]        // =>  FALSE
[15:24:13.276]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:24:13.276]    </control>
[15:24:13.276]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:24:13.276]      // if-block "DoDebugPortStop"
[15:24:13.276]        // =>  TRUE
[15:24:13.276]      <block atomic="false" info="">
[15:24:13.277]        WriteDP(DP_SELECT, 0x00000000);
[15:24:13.277]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:24:13.278]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:24:13.278]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:24:13.278]      </block>
[15:24:13.279]      // end if-block "DoDebugPortStop"
[15:24:13.279]    </control>
[15:24:13.280]  </sequence>
[15:24:13.280]  
[15:30:30.102]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:30:30.102]  
[15:30:30.102]  <debugvars>
[15:30:30.102]    // Pre-defined
[15:30:30.102]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:30:30.102]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:30:30.103]    __dp=0x00000000
[15:30:30.103]    __ap=0x00000000
[15:30:30.103]    __traceout=0x00000000      (Trace Disabled)
[15:30:30.103]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:30:30.104]    __FlashAddr=0x00000000
[15:30:30.104]    __FlashLen=0x00000000
[15:30:30.104]    __FlashArg=0x00000000
[15:30:30.105]    __FlashOp=0x00000000
[15:30:30.105]    __Result=0x00000000
[15:30:30.105]    
[15:30:30.105]    // User-defined
[15:30:30.105]    DbgMCU_CR=0x00000007
[15:30:30.106]    DbgMCU_APB1_Fz=0x00000000
[15:30:30.106]    DbgMCU_APB2_Fz=0x00000000
[15:30:30.106]    DoOptionByteLoading=0x00000000
[15:30:30.106]  </debugvars>
[15:30:30.107]  
[15:30:30.107]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:30:30.107]    <block atomic="false" info="">
[15:30:30.107]      Sequence("CheckID");
[15:30:30.107]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:30:30.107]          <block atomic="false" info="">
[15:30:30.107]            __var pidr1 = 0;
[15:30:30.108]              // -> [pidr1 <= 0x00000000]
[15:30:30.108]            __var pidr2 = 0;
[15:30:30.108]              // -> [pidr2 <= 0x00000000]
[15:30:30.108]            __var jep106id = 0;
[15:30:30.108]              // -> [jep106id <= 0x00000000]
[15:30:30.108]            __var ROMTableBase = 0;
[15:30:30.109]              // -> [ROMTableBase <= 0x00000000]
[15:30:30.109]            __ap = 0;      // AHB-AP
[15:30:30.109]              // -> [__ap <= 0x00000000]
[15:30:30.109]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:30:30.110]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:30:30.110]              // -> [ROMTableBase <= 0xF0000000]
[15:30:30.110]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:30:30.111]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:30:30.112]              // -> [pidr1 <= 0x00000004]
[15:30:30.112]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:30:30.113]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:30:30.114]              // -> [pidr2 <= 0x0000000A]
[15:30:30.114]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:30:30.114]              // -> [jep106id <= 0x00000020]
[15:30:30.114]          </block>
[15:30:30.114]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:30:30.115]            // if-block "jep106id != 0x20"
[15:30:30.115]              // =>  FALSE
[15:30:30.115]            // skip if-block "jep106id != 0x20"
[15:30:30.115]          </control>
[15:30:30.116]        </sequence>
[15:30:30.116]    </block>
[15:30:30.116]  </sequence>
[15:30:30.116]  
[15:30:30.128]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:30:30.128]  
[15:30:30.130]  <debugvars>
[15:30:30.130]    // Pre-defined
[15:30:30.131]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:30:30.131]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:30:30.131]    __dp=0x00000000
[15:30:30.131]    __ap=0x00000000
[15:30:30.132]    __traceout=0x00000000      (Trace Disabled)
[15:30:30.132]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:30:30.132]    __FlashAddr=0x00000000
[15:30:30.132]    __FlashLen=0x00000000
[15:30:30.133]    __FlashArg=0x00000000
[15:30:30.133]    __FlashOp=0x00000000
[15:30:30.133]    __Result=0x00000000
[15:30:30.133]    
[15:30:30.133]    // User-defined
[15:30:30.135]    DbgMCU_CR=0x00000007
[15:30:30.135]    DbgMCU_APB1_Fz=0x00000000
[15:30:30.135]    DbgMCU_APB2_Fz=0x00000000
[15:30:30.136]    DoOptionByteLoading=0x00000000
[15:30:30.136]  </debugvars>
[15:30:30.136]  
[15:30:30.136]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:30:30.137]    <block atomic="false" info="">
[15:30:30.137]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:30:30.138]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:30:30.138]    </block>
[15:30:30.138]    <block atomic="false" info="DbgMCU registers">
[15:30:30.139]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:30:30.140]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[15:30:30.141]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[15:30:30.141]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:30:30.142]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:30:30.142]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:30:30.144]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:30:30.144]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:30:30.145]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:30:30.145]    </block>
[15:30:30.146]  </sequence>
[15:30:30.146]  
[15:30:38.251]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:30:38.251]  
[15:30:38.251]  <debugvars>
[15:30:38.252]    // Pre-defined
[15:30:38.252]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:30:38.253]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:30:38.253]    __dp=0x00000000
[15:30:38.253]    __ap=0x00000000
[15:30:38.253]    __traceout=0x00000000      (Trace Disabled)
[15:30:38.253]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:30:38.254]    __FlashAddr=0x00000000
[15:30:38.254]    __FlashLen=0x00000000
[15:30:38.254]    __FlashArg=0x00000000
[15:30:38.254]    __FlashOp=0x00000000
[15:30:38.254]    __Result=0x00000000
[15:30:38.254]    
[15:30:38.254]    // User-defined
[15:30:38.254]    DbgMCU_CR=0x00000007
[15:30:38.255]    DbgMCU_APB1_Fz=0x00000000
[15:30:38.255]    DbgMCU_APB2_Fz=0x00000000
[15:30:38.255]    DoOptionByteLoading=0x00000000
[15:30:38.255]  </debugvars>
[15:30:38.255]  
[15:30:38.256]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:30:38.256]    <block atomic="false" info="">
[15:30:38.256]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:30:38.256]        // -> [connectionFlash <= 0x00000001]
[15:30:38.256]      __var FLASH_BASE = 0x40022000 ;
[15:30:38.257]        // -> [FLASH_BASE <= 0x40022000]
[15:30:38.257]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:30:38.257]        // -> [FLASH_CR <= 0x40022004]
[15:30:38.257]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:30:38.258]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:30:38.258]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:30:38.258]        // -> [LOCK_BIT <= 0x00000001]
[15:30:38.259]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:30:38.259]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:30:38.259]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:30:38.259]        // -> [FLASH_KEYR <= 0x4002200C]
[15:30:38.259]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:30:38.259]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:30:38.259]      __var FLASH_KEY2 = 0x02030405 ;
[15:30:38.260]        // -> [FLASH_KEY2 <= 0x02030405]
[15:30:38.260]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:30:38.260]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:30:38.261]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:30:38.261]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:30:38.261]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:30:38.261]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:30:38.262]      __var FLASH_CR_Value = 0 ;
[15:30:38.262]        // -> [FLASH_CR_Value <= 0x00000000]
[15:30:38.262]      __var DoDebugPortStop = 1 ;
[15:30:38.262]        // -> [DoDebugPortStop <= 0x00000001]
[15:30:38.262]      __var DP_CTRL_STAT = 0x4 ;
[15:30:38.262]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:30:38.263]      __var DP_SELECT = 0x8 ;
[15:30:38.263]        // -> [DP_SELECT <= 0x00000008]
[15:30:38.263]    </block>
[15:30:38.263]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:30:38.263]      // if-block "connectionFlash && DoOptionByteLoading"
[15:30:38.264]        // =>  FALSE
[15:30:38.264]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:30:38.264]    </control>
[15:30:38.264]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:30:38.264]      // if-block "DoDebugPortStop"
[15:30:38.265]        // =>  TRUE
[15:30:38.265]      <block atomic="false" info="">
[15:30:38.265]        WriteDP(DP_SELECT, 0x00000000);
[15:30:38.265]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:30:38.266]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:30:38.266]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:30:38.266]      </block>
[15:30:38.267]      // end if-block "DoDebugPortStop"
[15:30:38.267]    </control>
[15:30:38.267]  </sequence>
[15:30:38.267]  
[16:00:26.281]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:00:26.281]  
[16:00:26.281]  <debugvars>
[16:00:26.281]    // Pre-defined
[16:00:26.281]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:00:26.281]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:00:26.281]    __dp=0x00000000
[16:00:26.281]    __ap=0x00000000
[16:00:26.281]    __traceout=0x00000000      (Trace Disabled)
[16:00:26.281]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:00:26.281]    __FlashAddr=0x00000000
[16:00:26.281]    __FlashLen=0x00000000
[16:00:26.281]    __FlashArg=0x00000000
[16:00:26.281]    __FlashOp=0x00000000
[16:00:26.281]    __Result=0x00000000
[16:00:26.281]    
[16:00:26.281]    // User-defined
[16:00:26.281]    DbgMCU_CR=0x00000007
[16:00:26.281]    DbgMCU_APB1_Fz=0x00000000
[16:00:26.281]    DbgMCU_APB2_Fz=0x00000000
[16:00:26.281]    DoOptionByteLoading=0x00000000
[16:00:26.281]  </debugvars>
[16:00:26.281]  
[16:00:26.281]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:00:26.281]    <block atomic="false" info="">
[16:00:26.281]      Sequence("CheckID");
[16:00:26.281]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:00:26.281]          <block atomic="false" info="">
[16:00:26.281]            __var pidr1 = 0;
[16:00:26.281]              // -> [pidr1 <= 0x00000000]
[16:00:26.281]            __var pidr2 = 0;
[16:00:26.281]              // -> [pidr2 <= 0x00000000]
[16:00:26.281]            __var jep106id = 0;
[16:00:26.281]              // -> [jep106id <= 0x00000000]
[16:00:26.281]            __var ROMTableBase = 0;
[16:00:26.281]              // -> [ROMTableBase <= 0x00000000]
[16:00:26.296]            __ap = 0;      // AHB-AP
[16:00:26.296]              // -> [__ap <= 0x00000000]
[16:00:26.296]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:00:26.296]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:00:26.296]              // -> [ROMTableBase <= 0xF0000000]
[16:00:26.296]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:00:26.296]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:00:26.296]              // -> [pidr1 <= 0x00000004]
[16:00:26.296]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:00:26.296]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:00:26.296]              // -> [pidr2 <= 0x0000000A]
[16:00:26.296]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:00:26.296]              // -> [jep106id <= 0x00000020]
[16:00:26.296]          </block>
[16:00:26.296]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:00:26.296]            // if-block "jep106id != 0x20"
[16:00:26.296]              // =>  FALSE
[16:00:26.296]            // skip if-block "jep106id != 0x20"
[16:00:26.296]          </control>
[16:00:26.296]        </sequence>
[16:00:26.296]    </block>
[16:00:26.296]  </sequence>
[16:00:26.296]  
[16:00:26.312]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:00:26.312]  
[16:00:26.326]  <debugvars>
[16:00:26.326]    // Pre-defined
[16:00:26.326]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:00:26.326]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:00:26.326]    __dp=0x00000000
[16:00:26.326]    __ap=0x00000000
[16:00:26.326]    __traceout=0x00000000      (Trace Disabled)
[16:00:26.326]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:00:26.326]    __FlashAddr=0x00000000
[16:00:26.326]    __FlashLen=0x00000000
[16:00:26.326]    __FlashArg=0x00000000
[16:00:26.326]    __FlashOp=0x00000000
[16:00:26.326]    __Result=0x00000000
[16:00:26.326]    
[16:00:26.326]    // User-defined
[16:00:26.326]    DbgMCU_CR=0x00000007
[16:00:26.326]    DbgMCU_APB1_Fz=0x00000000
[16:00:26.326]    DbgMCU_APB2_Fz=0x00000000
[16:00:26.326]    DoOptionByteLoading=0x00000000
[16:00:26.326]  </debugvars>
[16:00:26.326]  
[16:00:26.326]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:00:26.326]    <block atomic="false" info="">
[16:00:26.326]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:00:26.326]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:00:26.326]    </block>
[16:00:26.326]    <block atomic="false" info="DbgMCU registers">
[16:00:26.326]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:00:26.326]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[16:00:26.326]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[16:00:26.326]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:00:26.342]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:00:26.342]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:00:26.342]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:00:26.342]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:00:26.342]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:00:26.342]    </block>
[16:00:26.342]  </sequence>
[16:00:26.342]  
[16:00:34.373]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:00:34.373]  
[16:00:34.373]  <debugvars>
[16:00:34.373]    // Pre-defined
[16:00:34.373]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:00:34.373]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:00:34.373]    __dp=0x00000000
[16:00:34.373]    __ap=0x00000000
[16:00:34.373]    __traceout=0x00000000      (Trace Disabled)
[16:00:34.373]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:00:34.373]    __FlashAddr=0x00000000
[16:00:34.373]    __FlashLen=0x00000000
[16:00:34.373]    __FlashArg=0x00000000
[16:00:34.373]    __FlashOp=0x00000000
[16:00:34.373]    __Result=0x00000000
[16:00:34.373]    
[16:00:34.373]    // User-defined
[16:00:34.373]    DbgMCU_CR=0x00000007
[16:00:34.373]    DbgMCU_APB1_Fz=0x00000000
[16:00:34.373]    DbgMCU_APB2_Fz=0x00000000
[16:00:34.373]    DoOptionByteLoading=0x00000000
[16:00:34.373]  </debugvars>
[16:00:34.373]  
[16:00:34.373]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:00:34.373]    <block atomic="false" info="">
[16:00:34.373]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:00:34.378]        // -> [connectionFlash <= 0x00000001]
[16:00:34.378]      __var FLASH_BASE = 0x40022000 ;
[16:00:34.378]        // -> [FLASH_BASE <= 0x40022000]
[16:00:34.378]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:00:34.378]        // -> [FLASH_CR <= 0x40022004]
[16:00:34.378]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:00:34.378]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:00:34.378]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:00:34.380]        // -> [LOCK_BIT <= 0x00000001]
[16:00:34.380]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:00:34.380]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:00:34.380]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:00:34.380]        // -> [FLASH_KEYR <= 0x4002200C]
[16:00:34.380]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:00:34.380]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:00:34.380]      __var FLASH_KEY2 = 0x02030405 ;
[16:00:34.382]        // -> [FLASH_KEY2 <= 0x02030405]
[16:00:34.382]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:00:34.382]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:00:34.382]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:00:34.382]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:00:34.382]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:00:34.383]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:00:34.383]      __var FLASH_CR_Value = 0 ;
[16:00:34.383]        // -> [FLASH_CR_Value <= 0x00000000]
[16:00:34.383]      __var DoDebugPortStop = 1 ;
[16:00:34.383]        // -> [DoDebugPortStop <= 0x00000001]
[16:00:34.383]      __var DP_CTRL_STAT = 0x4 ;
[16:00:34.383]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:00:34.383]      __var DP_SELECT = 0x8 ;
[16:00:34.385]        // -> [DP_SELECT <= 0x00000008]
[16:00:34.385]    </block>
[16:00:34.385]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:00:34.385]      // if-block "connectionFlash && DoOptionByteLoading"
[16:00:34.385]        // =>  FALSE
[16:00:34.385]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:00:34.385]    </control>
[16:00:34.385]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:00:34.385]      // if-block "DoDebugPortStop"
[16:00:34.385]        // =>  TRUE
[16:00:34.387]      <block atomic="false" info="">
[16:00:34.387]        WriteDP(DP_SELECT, 0x00000000);
[16:00:34.387]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:00:34.387]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:00:34.387]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:00:34.387]      </block>
[16:00:34.387]      // end if-block "DoDebugPortStop"
[16:00:34.387]    </control>
[16:00:34.389]  </sequence>
[16:00:34.389]  
[16:05:50.924]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:05:50.924]  
[16:05:50.924]  <debugvars>
[16:05:50.924]    // Pre-defined
[16:05:50.925]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:05:50.925]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:05:50.925]    __dp=0x00000000
[16:05:50.925]    __ap=0x00000000
[16:05:50.926]    __traceout=0x00000000      (Trace Disabled)
[16:05:50.926]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:05:50.926]    __FlashAddr=0x00000000
[16:05:50.926]    __FlashLen=0x00000000
[16:05:50.926]    __FlashArg=0x00000000
[16:05:50.926]    __FlashOp=0x00000000
[16:05:50.926]    __Result=0x00000000
[16:05:50.926]    
[16:05:50.926]    // User-defined
[16:05:50.926]    DbgMCU_CR=0x00000007
[16:05:50.926]    DbgMCU_APB1_Fz=0x00000000
[16:05:50.926]    DbgMCU_APB2_Fz=0x00000000
[16:05:50.926]    DoOptionByteLoading=0x00000000
[16:05:50.926]  </debugvars>
[16:05:50.926]  
[16:05:50.926]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:05:50.926]    <block atomic="false" info="">
[16:05:50.926]      Sequence("CheckID");
[16:05:50.926]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:05:50.926]          <block atomic="false" info="">
[16:05:50.926]            __var pidr1 = 0;
[16:05:50.926]              // -> [pidr1 <= 0x00000000]
[16:05:50.926]            __var pidr2 = 0;
[16:05:50.926]              // -> [pidr2 <= 0x00000000]
[16:05:50.926]            __var jep106id = 0;
[16:05:50.926]              // -> [jep106id <= 0x00000000]
[16:05:50.926]            __var ROMTableBase = 0;
[16:05:50.926]              // -> [ROMTableBase <= 0x00000000]
[16:05:50.926]            __ap = 0;      // AHB-AP
[16:05:50.926]              // -> [__ap <= 0x00000000]
[16:05:50.926]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:05:50.926]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:05:50.926]              // -> [ROMTableBase <= 0xF0000000]
[16:05:50.926]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:05:50.926]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:05:50.926]              // -> [pidr1 <= 0x00000004]
[16:05:50.926]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:05:50.926]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:05:50.926]              // -> [pidr2 <= 0x0000000A]
[16:05:50.926]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:05:50.926]              // -> [jep106id <= 0x00000020]
[16:05:50.926]          </block>
[16:05:50.926]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:05:50.926]            // if-block "jep106id != 0x20"
[16:05:50.926]              // =>  FALSE
[16:05:50.926]            // skip if-block "jep106id != 0x20"
[16:05:50.926]          </control>
[16:05:50.926]        </sequence>
[16:05:50.926]    </block>
[16:05:50.926]  </sequence>
[16:05:50.926]  
[16:05:50.942]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:05:50.942]  
[16:05:50.942]  <debugvars>
[16:05:50.942]    // Pre-defined
[16:05:50.942]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:05:50.942]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:05:50.942]    __dp=0x00000000
[16:05:50.942]    __ap=0x00000000
[16:05:50.942]    __traceout=0x00000000      (Trace Disabled)
[16:05:50.942]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:05:50.942]    __FlashAddr=0x00000000
[16:05:50.942]    __FlashLen=0x00000000
[16:05:50.942]    __FlashArg=0x00000000
[16:05:50.942]    __FlashOp=0x00000000
[16:05:50.957]    __Result=0x00000000
[16:05:50.957]    
[16:05:50.957]    // User-defined
[16:05:50.957]    DbgMCU_CR=0x00000007
[16:05:50.957]    DbgMCU_APB1_Fz=0x00000000
[16:05:50.957]    DbgMCU_APB2_Fz=0x00000000
[16:05:50.957]    DoOptionByteLoading=0x00000000
[16:05:50.957]  </debugvars>
[16:05:50.957]  
[16:05:50.957]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:05:50.957]    <block atomic="false" info="">
[16:05:50.957]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:05:50.957]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:05:50.957]    </block>
[16:05:50.957]    <block atomic="false" info="DbgMCU registers">
[16:05:50.957]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:05:50.957]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[16:05:50.957]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[16:05:50.957]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:05:50.965]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:05:50.965]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:05:50.965]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:05:50.965]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:05:50.967]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:05:50.967]    </block>
[16:05:50.968]  </sequence>
[16:05:50.968]  
[16:05:58.007]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:05:58.007]  
[16:05:58.007]  <debugvars>
[16:05:58.007]    // Pre-defined
[16:05:58.007]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:05:58.008]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:05:58.008]    __dp=0x00000000
[16:05:58.008]    __ap=0x00000000
[16:05:58.009]    __traceout=0x00000000      (Trace Disabled)
[16:05:58.009]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:05:58.009]    __FlashAddr=0x00000000
[16:05:58.009]    __FlashLen=0x00000000
[16:05:58.010]    __FlashArg=0x00000000
[16:05:58.010]    __FlashOp=0x00000000
[16:05:58.010]    __Result=0x00000000
[16:05:58.010]    
[16:05:58.010]    // User-defined
[16:05:58.011]    DbgMCU_CR=0x00000007
[16:05:58.011]    DbgMCU_APB1_Fz=0x00000000
[16:05:58.011]    DbgMCU_APB2_Fz=0x00000000
[16:05:58.011]    DoOptionByteLoading=0x00000000
[16:05:58.011]  </debugvars>
[16:05:58.012]  
[16:05:58.012]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:05:58.012]    <block atomic="false" info="">
[16:05:58.012]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:05:58.012]        // -> [connectionFlash <= 0x00000001]
[16:05:58.013]      __var FLASH_BASE = 0x40022000 ;
[16:05:58.013]        // -> [FLASH_BASE <= 0x40022000]
[16:05:58.013]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:05:58.013]        // -> [FLASH_CR <= 0x40022004]
[16:05:58.013]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:05:58.014]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:05:58.014]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:05:58.014]        // -> [LOCK_BIT <= 0x00000001]
[16:05:58.015]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:05:58.015]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:05:58.015]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:05:58.015]        // -> [FLASH_KEYR <= 0x4002200C]
[16:05:58.016]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:05:58.016]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:05:58.016]      __var FLASH_KEY2 = 0x02030405 ;
[16:05:58.016]        // -> [FLASH_KEY2 <= 0x02030405]
[16:05:58.016]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:05:58.017]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:05:58.017]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:05:58.017]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:05:58.017]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:05:58.017]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:05:58.017]      __var FLASH_CR_Value = 0 ;
[16:05:58.018]        // -> [FLASH_CR_Value <= 0x00000000]
[16:05:58.018]      __var DoDebugPortStop = 1 ;
[16:05:58.018]        // -> [DoDebugPortStop <= 0x00000001]
[16:05:58.019]      __var DP_CTRL_STAT = 0x4 ;
[16:05:58.019]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:05:58.019]      __var DP_SELECT = 0x8 ;
[16:05:58.019]        // -> [DP_SELECT <= 0x00000008]
[16:05:58.019]    </block>
[16:05:58.021]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:05:58.022]      // if-block "connectionFlash && DoOptionByteLoading"
[16:05:58.022]        // =>  FALSE
[16:05:58.022]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:05:58.022]    </control>
[16:05:58.023]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:05:58.023]      // if-block "DoDebugPortStop"
[16:05:58.023]        // =>  TRUE
[16:05:58.023]      <block atomic="false" info="">
[16:05:58.023]        WriteDP(DP_SELECT, 0x00000000);
[16:05:58.023]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:05:58.023]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:05:58.024]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:05:58.024]      </block>
[16:05:58.025]      // end if-block "DoDebugPortStop"
[16:05:58.026]    </control>
[16:05:58.026]  </sequence>
[16:05:58.026]  
[16:06:11.488]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:06:11.488]  
[16:06:11.489]  <debugvars>
[16:06:11.489]    // Pre-defined
[16:06:11.489]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:06:11.490]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:06:11.490]    __dp=0x00000000
[16:06:11.490]    __ap=0x00000000
[16:06:11.490]    __traceout=0x00000000      (Trace Disabled)
[16:06:11.491]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:06:11.491]    __FlashAddr=0x00000000
[16:06:11.491]    __FlashLen=0x00000000
[16:06:11.492]    __FlashArg=0x00000000
[16:06:11.492]    __FlashOp=0x00000000
[16:06:11.492]    __Result=0x00000000
[16:06:11.493]    
[16:06:11.493]    // User-defined
[16:06:11.493]    DbgMCU_CR=0x00000007
[16:06:11.493]    DbgMCU_APB1_Fz=0x00000000
[16:06:11.494]    DbgMCU_APB2_Fz=0x00000000
[16:06:11.494]    DoOptionByteLoading=0x00000000
[16:06:11.494]  </debugvars>
[16:06:11.494]  
[16:06:11.494]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:06:11.494]    <block atomic="false" info="">
[16:06:11.495]      Sequence("CheckID");
[16:06:11.495]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:06:11.495]          <block atomic="false" info="">
[16:06:11.495]            __var pidr1 = 0;
[16:06:11.495]              // -> [pidr1 <= 0x00000000]
[16:06:11.496]            __var pidr2 = 0;
[16:06:11.496]              // -> [pidr2 <= 0x00000000]
[16:06:11.496]            __var jep106id = 0;
[16:06:11.496]              // -> [jep106id <= 0x00000000]
[16:06:11.497]            __var ROMTableBase = 0;
[16:06:11.497]              // -> [ROMTableBase <= 0x00000000]
[16:06:11.497]            __ap = 0;      // AHB-AP
[16:06:11.497]              // -> [__ap <= 0x00000000]
[16:06:11.497]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:06:11.498]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:06:11.499]              // -> [ROMTableBase <= 0xF0000000]
[16:06:11.499]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:06:11.500]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:06:11.501]              // -> [pidr1 <= 0x00000004]
[16:06:11.501]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:06:11.502]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:06:11.502]              // -> [pidr2 <= 0x0000000A]
[16:06:11.502]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:06:11.503]              // -> [jep106id <= 0x00000020]
[16:06:11.503]          </block>
[16:06:11.503]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:06:11.503]            // if-block "jep106id != 0x20"
[16:06:11.503]              // =>  FALSE
[16:06:11.504]            // skip if-block "jep106id != 0x20"
[16:06:11.504]          </control>
[16:06:11.504]        </sequence>
[16:06:11.504]    </block>
[16:06:11.504]  </sequence>
[16:06:11.504]  
[16:06:11.517]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:06:11.517]  
[16:06:11.517]  <debugvars>
[16:06:11.518]    // Pre-defined
[16:06:11.518]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:06:11.518]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:06:11.519]    __dp=0x00000000
[16:06:11.519]    __ap=0x00000000
[16:06:11.519]    __traceout=0x00000000      (Trace Disabled)
[16:06:11.519]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:06:11.520]    __FlashAddr=0x00000000
[16:06:11.520]    __FlashLen=0x00000000
[16:06:11.520]    __FlashArg=0x00000000
[16:06:11.520]    __FlashOp=0x00000000
[16:06:11.520]    __Result=0x00000000
[16:06:11.520]    
[16:06:11.520]    // User-defined
[16:06:11.521]    DbgMCU_CR=0x00000007
[16:06:11.521]    DbgMCU_APB1_Fz=0x00000000
[16:06:11.521]    DbgMCU_APB2_Fz=0x00000000
[16:06:11.521]    DoOptionByteLoading=0x00000000
[16:06:11.521]  </debugvars>
[16:06:11.522]  
[16:06:11.522]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:06:11.522]    <block atomic="false" info="">
[16:06:11.522]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:06:11.523]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:06:11.523]    </block>
[16:06:11.523]    <block atomic="false" info="DbgMCU registers">
[16:06:11.524]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:06:11.524]        // -> [Read32(0x40021034) => 0x00000200]   (__dp=0x00000000, __ap=0x00000000)
[16:06:11.524]        // -> [Write32(0x40021034, 0x00400200)]   (__dp=0x00000000, __ap=0x00000000)
[16:06:11.526]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:06:11.526]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:06:11.527]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:06:11.528]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:06:11.528]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:06:11.529]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:06:11.529]    </block>
[16:06:11.529]  </sequence>
[16:06:11.529]  
[16:06:19.573]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:06:19.573]  
[16:06:19.574]  <debugvars>
[16:06:19.575]    // Pre-defined
[16:06:19.575]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:06:19.576]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:06:19.576]    __dp=0x00000000
[16:06:19.577]    __ap=0x00000000
[16:06:19.577]    __traceout=0x00000000      (Trace Disabled)
[16:06:19.578]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:06:19.578]    __FlashAddr=0x00000000
[16:06:19.579]    __FlashLen=0x00000000
[16:06:19.579]    __FlashArg=0x00000000
[16:06:19.580]    __FlashOp=0x00000000
[16:06:19.580]    __Result=0x00000000
[16:06:19.580]    
[16:06:19.580]    // User-defined
[16:06:19.580]    DbgMCU_CR=0x00000007
[16:06:19.581]    DbgMCU_APB1_Fz=0x00000000
[16:06:19.581]    DbgMCU_APB2_Fz=0x00000000
[16:06:19.582]    DoOptionByteLoading=0x00000000
[16:06:19.582]  </debugvars>
[16:06:19.583]  
[16:06:19.583]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:06:19.583]    <block atomic="false" info="">
[16:06:19.584]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:06:19.584]        // -> [connectionFlash <= 0x00000001]
[16:06:19.584]      __var FLASH_BASE = 0x40022000 ;
[16:06:19.584]        // -> [FLASH_BASE <= 0x40022000]
[16:06:19.585]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:06:19.585]        // -> [FLASH_CR <= 0x40022004]
[16:06:19.585]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:06:19.585]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:06:19.586]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:06:19.586]        // -> [LOCK_BIT <= 0x00000001]
[16:06:19.586]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:06:19.586]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:06:19.586]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:06:19.587]        // -> [FLASH_KEYR <= 0x4002200C]
[16:06:19.587]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:06:19.587]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:06:19.587]      __var FLASH_KEY2 = 0x02030405 ;
[16:06:19.587]        // -> [FLASH_KEY2 <= 0x02030405]
[16:06:19.587]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:06:19.588]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:06:19.588]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:06:19.588]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:06:19.588]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:06:19.588]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:06:19.589]      __var FLASH_CR_Value = 0 ;
[16:06:19.589]        // -> [FLASH_CR_Value <= 0x00000000]
[16:06:19.589]      __var DoDebugPortStop = 1 ;
[16:06:19.589]        // -> [DoDebugPortStop <= 0x00000001]
[16:06:19.589]      __var DP_CTRL_STAT = 0x4 ;
[16:06:19.589]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:06:19.590]      __var DP_SELECT = 0x8 ;
[16:06:19.590]        // -> [DP_SELECT <= 0x00000008]
[16:06:19.590]    </block>
[16:06:19.590]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:06:19.590]      // if-block "connectionFlash && DoOptionByteLoading"
[16:06:19.590]        // =>  FALSE
[16:06:19.590]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:06:19.590]    </control>
[16:06:19.590]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:06:19.590]      // if-block "DoDebugPortStop"
[16:06:19.590]        // =>  TRUE
[16:06:19.590]      <block atomic="false" info="">
[16:06:19.590]        WriteDP(DP_SELECT, 0x00000000);
[16:06:19.590]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:06:19.590]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:06:19.590]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:06:19.590]      </block>
[16:06:19.590]      // end if-block "DoDebugPortStop"
[16:06:19.590]    </control>
[16:06:19.590]  </sequence>
[16:06:19.590]  
