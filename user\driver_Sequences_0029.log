/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : D:\2025work\STM32L072PRO\source-application\user\driver_Sequences_0029.log
 *  Created     : 08:49:34 (25/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : D:\2025work\STM32L072PRO\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[08:49:34.057]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:49:34.057]  
[08:49:34.085]  <debugvars>
[08:49:34.110]    // Pre-defined
[08:49:34.135]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:49:34.161]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:49:34.162]    __dp=0x00000000
[08:49:34.163]    __ap=0x00000000
[08:49:34.163]    __traceout=0x00000000      (Trace Disabled)
[08:49:34.164]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:49:34.164]    __FlashAddr=0x00000000
[08:49:34.165]    __FlashLen=0x00000000
[08:49:34.166]    __FlashArg=0x00000000
[08:49:34.166]    __FlashOp=0x00000000
[08:49:34.167]    __Result=0x00000000
[08:49:34.167]    
[08:49:34.167]    // User-defined
[08:49:34.168]    DbgMCU_CR=0x00000007
[08:49:34.168]    DbgMCU_APB1_Fz=0x00000000
[08:49:34.168]    DbgMCU_APB2_Fz=0x00000000
[08:49:34.169]    DoOptionByteLoading=0x00000000
[08:49:34.169]  </debugvars>
[08:49:34.169]  
[08:49:34.170]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:49:34.170]    <block atomic="false" info="">
[08:49:34.171]      Sequence("CheckID");
[08:49:34.171]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:49:34.172]          <block atomic="false" info="">
[08:49:34.172]            __var pidr1 = 0;
[08:49:34.172]              // -> [pidr1 <= 0x00000000]
[08:49:34.172]            __var pidr2 = 0;
[08:49:34.173]              // -> [pidr2 <= 0x00000000]
[08:49:34.173]            __var jep106id = 0;
[08:49:34.173]              // -> [jep106id <= 0x00000000]
[08:49:34.174]            __var ROMTableBase = 0;
[08:49:34.174]              // -> [ROMTableBase <= 0x00000000]
[08:49:34.174]            __ap = 0;      // AHB-AP
[08:49:34.174]              // -> [__ap <= 0x00000000]
[08:49:34.174]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:49:34.175]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:49:34.175]              // -> [ROMTableBase <= 0xF0000000]
[08:49:34.175]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:49:34.176]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:49:34.177]              // -> [pidr1 <= 0x00000004]
[08:49:34.177]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:49:34.178]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:49:34.178]              // -> [pidr2 <= 0x0000000A]
[08:49:34.178]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:49:34.179]              // -> [jep106id <= 0x00000020]
[08:49:34.179]          </block>
[08:49:34.179]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:49:34.179]            // if-block "jep106id != 0x20"
[08:49:34.179]              // =>  FALSE
[08:49:34.180]            // skip if-block "jep106id != 0x20"
[08:49:34.180]          </control>
[08:49:34.180]        </sequence>
[08:49:34.180]    </block>
[08:49:34.180]  </sequence>
[08:49:34.181]  
[08:49:34.192]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:49:34.192]  
[08:49:34.193]  <debugvars>
[08:49:34.194]    // Pre-defined
[08:49:34.194]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:49:34.194]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:49:34.195]    __dp=0x00000000
[08:49:34.195]    __ap=0x00000000
[08:49:34.195]    __traceout=0x00000000      (Trace Disabled)
[08:49:34.195]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:49:34.196]    __FlashAddr=0x00000000
[08:49:34.196]    __FlashLen=0x00000000
[08:49:34.196]    __FlashArg=0x00000000
[08:49:34.197]    __FlashOp=0x00000000
[08:49:34.197]    __Result=0x00000000
[08:49:34.197]    
[08:49:34.197]    // User-defined
[08:49:34.197]    DbgMCU_CR=0x00000007
[08:49:34.197]    DbgMCU_APB1_Fz=0x00000000
[08:49:34.198]    DbgMCU_APB2_Fz=0x00000000
[08:49:34.198]    DoOptionByteLoading=0x00000000
[08:49:34.198]  </debugvars>
[08:49:34.198]  
[08:49:34.198]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:49:34.199]    <block atomic="false" info="">
[08:49:34.199]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:49:34.199]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:49:34.200]    </block>
[08:49:34.200]    <block atomic="false" info="DbgMCU registers">
[08:49:34.200]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:49:34.201]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[08:49:34.202]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[08:49:34.203]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:49:34.204]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:49:34.204]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:49:34.205]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:49:34.205]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:49:34.206]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:49:34.206]    </block>
[08:49:34.206]  </sequence>
[08:49:34.206]  
[08:49:42.079]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[08:49:42.079]  
[08:49:42.079]  <debugvars>
[08:49:42.079]    // Pre-defined
[08:49:42.079]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:49:42.079]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:49:42.079]    __dp=0x00000000
[08:49:42.087]    __ap=0x00000000
[08:49:42.087]    __traceout=0x00000000      (Trace Disabled)
[08:49:42.087]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:49:42.087]    __FlashAddr=0x00000000
[08:49:42.087]    __FlashLen=0x00000000
[08:49:42.087]    __FlashArg=0x00000000
[08:49:42.087]    __FlashOp=0x00000000
[08:49:42.087]    __Result=0x00000000
[08:49:42.089]    
[08:49:42.089]    // User-defined
[08:49:42.089]    DbgMCU_CR=0x00000007
[08:49:42.089]    DbgMCU_APB1_Fz=0x00000000
[08:49:42.089]    DbgMCU_APB2_Fz=0x00000000
[08:49:42.089]    DoOptionByteLoading=0x00000000
[08:49:42.089]  </debugvars>
[08:49:42.089]  
[08:49:42.089]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[08:49:42.089]    <block atomic="false" info="">
[08:49:42.091]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[08:49:42.091]        // -> [connectionFlash <= 0x00000001]
[08:49:42.091]      __var FLASH_BASE = 0x40022000 ;
[08:49:42.091]        // -> [FLASH_BASE <= 0x40022000]
[08:49:42.091]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[08:49:42.091]        // -> [FLASH_CR <= 0x40022004]
[08:49:42.093]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[08:49:42.093]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[08:49:42.093]      __var LOCK_BIT = ( 1 << 0 ) ;
[08:49:42.093]        // -> [LOCK_BIT <= 0x00000001]
[08:49:42.093]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[08:49:42.093]        // -> [OPTLOCK_BIT <= 0x00000004]
[08:49:42.095]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[08:49:42.095]        // -> [FLASH_KEYR <= 0x4002200C]
[08:49:42.095]      __var FLASH_KEY1 = 0x89ABCDEF ;
[08:49:42.095]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[08:49:42.095]      __var FLASH_KEY2 = 0x02030405 ;
[08:49:42.095]        // -> [FLASH_KEY2 <= 0x02030405]
[08:49:42.095]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[08:49:42.095]        // -> [FLASH_OPTKEYR <= 0x40022014]
[08:49:42.097]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[08:49:42.097]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[08:49:42.097]      __var FLASH_OPTKEY2 = 0x24252627 ;
[08:49:42.097]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[08:49:42.097]      __var FLASH_CR_Value = 0 ;
[08:49:42.097]        // -> [FLASH_CR_Value <= 0x00000000]
[08:49:42.097]      __var DoDebugPortStop = 1 ;
[08:49:42.097]        // -> [DoDebugPortStop <= 0x00000001]
[08:49:42.097]      __var DP_CTRL_STAT = 0x4 ;
[08:49:42.097]        // -> [DP_CTRL_STAT <= 0x00000004]
[08:49:42.097]      __var DP_SELECT = 0x8 ;
[08:49:42.097]        // -> [DP_SELECT <= 0x00000008]
[08:49:42.097]    </block>
[08:49:42.097]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[08:49:42.097]      // if-block "connectionFlash && DoOptionByteLoading"
[08:49:42.097]        // =>  FALSE
[08:49:42.097]      // skip if-block "connectionFlash && DoOptionByteLoading"
[08:49:42.097]    </control>
[08:49:42.097]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[08:49:42.097]      // if-block "DoDebugPortStop"
[08:49:42.097]        // =>  TRUE
[08:49:42.097]      <block atomic="false" info="">
[08:49:42.097]        WriteDP(DP_SELECT, 0x00000000);
[08:49:42.097]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[08:49:42.097]        WriteDP(DP_CTRL_STAT, 0x00000000);
[08:49:42.097]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[08:49:42.097]      </block>
[08:49:42.097]      // end if-block "DoDebugPortStop"
[08:49:42.097]    </control>
[08:49:42.097]  </sequence>
[08:49:42.097]  
[08:50:07.390]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:50:07.390]  
[08:50:07.390]  <debugvars>
[08:50:07.390]    // Pre-defined
[08:50:07.390]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:50:07.390]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:50:07.390]    __dp=0x00000000
[08:50:07.390]    __ap=0x00000000
[08:50:07.390]    __traceout=0x00000000      (Trace Disabled)
[08:50:07.390]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:50:07.390]    __FlashAddr=0x00000000
[08:50:07.390]    __FlashLen=0x00000000
[08:50:07.390]    __FlashArg=0x00000000
[08:50:07.390]    __FlashOp=0x00000000
[08:50:07.390]    __Result=0x00000000
[08:50:07.390]    
[08:50:07.390]    // User-defined
[08:50:07.390]    DbgMCU_CR=0x00000007
[08:50:07.390]    DbgMCU_APB1_Fz=0x00000000
[08:50:07.390]    DbgMCU_APB2_Fz=0x00000000
[08:50:07.390]    DoOptionByteLoading=0x00000000
[08:50:07.390]  </debugvars>
[08:50:07.390]  
[08:50:07.390]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:50:07.390]    <block atomic="false" info="">
[08:50:07.390]      Sequence("CheckID");
[08:50:07.390]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:50:07.390]          <block atomic="false" info="">
[08:50:07.390]            __var pidr1 = 0;
[08:50:07.390]              // -> [pidr1 <= 0x00000000]
[08:50:07.390]            __var pidr2 = 0;
[08:50:07.390]              // -> [pidr2 <= 0x00000000]
[08:50:07.390]            __var jep106id = 0;
[08:50:07.390]              // -> [jep106id <= 0x00000000]
[08:50:07.398]            __var ROMTableBase = 0;
[08:50:07.398]              // -> [ROMTableBase <= 0x00000000]
[08:50:07.398]            __ap = 0;      // AHB-AP
[08:50:07.398]              // -> [__ap <= 0x00000000]
[08:50:07.398]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:50:07.398]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:50:07.398]              // -> [ROMTableBase <= 0xF0000000]
[08:50:07.398]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:50:07.398]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:50:07.398]              // -> [pidr1 <= 0x00000004]
[08:50:07.398]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:50:07.398]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:50:07.398]              // -> [pidr2 <= 0x0000000A]
[08:50:07.398]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:50:07.402]              // -> [jep106id <= 0x00000020]
[08:50:07.402]          </block>
[08:50:07.402]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:50:07.402]            // if-block "jep106id != 0x20"
[08:50:07.402]              // =>  FALSE
[08:50:07.402]            // skip if-block "jep106id != 0x20"
[08:50:07.402]          </control>
[08:50:07.402]        </sequence>
[08:50:07.402]    </block>
[08:50:07.402]  </sequence>
[08:50:07.402]  
[08:50:07.413]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:50:07.413]  
[08:50:07.413]  <debugvars>
[08:50:07.413]    // Pre-defined
[08:50:07.413]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:50:07.413]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:50:07.413]    __dp=0x00000000
[08:50:07.413]    __ap=0x00000000
[08:50:07.413]    __traceout=0x00000000      (Trace Disabled)
[08:50:07.413]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:50:07.413]    __FlashAddr=0x00000000
[08:50:07.413]    __FlashLen=0x00000000
[08:50:07.413]    __FlashArg=0x00000000
[08:50:07.413]    __FlashOp=0x00000000
[08:50:07.413]    __Result=0x00000000
[08:50:07.413]    
[08:50:07.413]    // User-defined
[08:50:07.413]    DbgMCU_CR=0x00000007
[08:50:07.413]    DbgMCU_APB1_Fz=0x00000000
[08:50:07.413]    DbgMCU_APB2_Fz=0x00000000
[08:50:07.413]    DoOptionByteLoading=0x00000000
[08:50:07.413]  </debugvars>
[08:50:07.413]  
[08:50:07.413]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:50:07.413]    <block atomic="false" info="">
[08:50:07.413]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:50:07.413]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:50:07.422]    </block>
[08:50:07.422]    <block atomic="false" info="DbgMCU registers">
[08:50:07.422]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:50:07.422]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[08:50:07.424]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[08:50:07.424]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:50:07.424]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:50:07.424]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:50:07.424]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:50:07.424]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:50:07.424]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:50:07.424]    </block>
[08:50:07.424]  </sequence>
[08:50:07.428]  
[08:50:22.517]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[08:50:22.519]  
[08:50:22.519]  <debugvars>
[08:50:22.520]    // Pre-defined
[08:50:22.520]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:50:22.520]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:50:22.520]    __dp=0x00000000
[08:50:22.520]    __ap=0x00000000
[08:50:22.520]    __traceout=0x00000000      (Trace Disabled)
[08:50:22.520]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:50:22.521]    __FlashAddr=0x00000000
[08:50:22.521]    __FlashLen=0x00000000
[08:50:22.521]    __FlashArg=0x00000000
[08:50:22.521]    __FlashOp=0x00000000
[08:50:22.522]    __Result=0x00000000
[08:50:22.522]    
[08:50:22.522]    // User-defined
[08:50:22.522]    DbgMCU_CR=0x00000007
[08:50:22.522]    DbgMCU_APB1_Fz=0x00000000
[08:50:22.522]    DbgMCU_APB2_Fz=0x00000000
[08:50:22.522]    DoOptionByteLoading=0x00000000
[08:50:22.523]  </debugvars>
[08:50:22.523]  
[08:50:22.523]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[08:50:22.523]    <block atomic="false" info="">
[08:50:22.524]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[08:50:22.524]        // -> [connectionFlash <= 0x00000001]
[08:50:22.524]      __var FLASH_BASE = 0x40022000 ;
[08:50:22.524]        // -> [FLASH_BASE <= 0x40022000]
[08:50:22.525]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[08:50:22.525]        // -> [FLASH_CR <= 0x40022004]
[08:50:22.525]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[08:50:22.525]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[08:50:22.525]      __var LOCK_BIT = ( 1 << 0 ) ;
[08:50:22.525]        // -> [LOCK_BIT <= 0x00000001]
[08:50:22.526]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[08:50:22.526]        // -> [OPTLOCK_BIT <= 0x00000004]
[08:50:22.526]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[08:50:22.526]        // -> [FLASH_KEYR <= 0x4002200C]
[08:50:22.526]      __var FLASH_KEY1 = 0x89ABCDEF ;
[08:50:22.527]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[08:50:22.527]      __var FLASH_KEY2 = 0x02030405 ;
[08:50:22.527]        // -> [FLASH_KEY2 <= 0x02030405]
[08:50:22.527]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[08:50:22.528]        // -> [FLASH_OPTKEYR <= 0x40022014]
[08:50:22.528]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[08:50:22.528]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[08:50:22.528]      __var FLASH_OPTKEY2 = 0x24252627 ;
[08:50:22.529]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[08:50:22.529]      __var FLASH_CR_Value = 0 ;
[08:50:22.529]        // -> [FLASH_CR_Value <= 0x00000000]
[08:50:22.529]      __var DoDebugPortStop = 1 ;
[08:50:22.530]        // -> [DoDebugPortStop <= 0x00000001]
[08:50:22.530]      __var DP_CTRL_STAT = 0x4 ;
[08:50:22.531]        // -> [DP_CTRL_STAT <= 0x00000004]
[08:50:22.532]      __var DP_SELECT = 0x8 ;
[08:50:22.532]        // -> [DP_SELECT <= 0x00000008]
[08:50:22.533]    </block>
[08:50:22.533]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[08:50:22.533]      // if-block "connectionFlash && DoOptionByteLoading"
[08:50:22.534]        // =>  FALSE
[08:50:22.534]      // skip if-block "connectionFlash && DoOptionByteLoading"
[08:50:22.534]    </control>
[08:50:22.534]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[08:50:22.536]      // if-block "DoDebugPortStop"
[08:50:22.536]        // =>  TRUE
[08:50:22.536]      <block atomic="false" info="">
[08:50:22.536]        WriteDP(DP_SELECT, 0x00000000);
[08:50:22.536]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[08:50:22.537]        WriteDP(DP_CTRL_STAT, 0x00000000);
[08:50:22.537]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[08:50:22.537]      </block>
[08:50:22.538]      // end if-block "DoDebugPortStop"
[08:50:22.538]    </control>
[08:50:22.538]  </sequence>
[08:50:22.538]  
[08:50:25.632]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:50:25.632]  
[08:50:25.632]  <debugvars>
[08:50:25.632]    // Pre-defined
[08:50:25.633]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:50:25.633]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:50:25.633]    __dp=0x00000000
[08:50:25.633]    __ap=0x00000000
[08:50:25.634]    __traceout=0x00000000      (Trace Disabled)
[08:50:25.634]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:50:25.634]    __FlashAddr=0x00000000
[08:50:25.635]    __FlashLen=0x00000000
[08:50:25.635]    __FlashArg=0x00000000
[08:50:25.635]    __FlashOp=0x00000000
[08:50:25.636]    __Result=0x00000000
[08:50:25.636]    
[08:50:25.636]    // User-defined
[08:50:25.636]    DbgMCU_CR=0x00000007
[08:50:25.636]    DbgMCU_APB1_Fz=0x00000000
[08:50:25.637]    DbgMCU_APB2_Fz=0x00000000
[08:50:25.637]    DoOptionByteLoading=0x00000000
[08:50:25.637]  </debugvars>
[08:50:25.637]  
[08:50:25.638]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:50:25.638]    <block atomic="false" info="">
[08:50:25.639]      Sequence("CheckID");
[08:50:25.639]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:50:25.639]          <block atomic="false" info="">
[08:50:25.640]            __var pidr1 = 0;
[08:50:25.641]              // -> [pidr1 <= 0x00000000]
[08:50:25.641]            __var pidr2 = 0;
[08:50:25.641]              // -> [pidr2 <= 0x00000000]
[08:50:25.641]            __var jep106id = 0;
[08:50:25.641]              // -> [jep106id <= 0x00000000]
[08:50:25.641]            __var ROMTableBase = 0;
[08:50:25.642]              // -> [ROMTableBase <= 0x00000000]
[08:50:25.642]            __ap = 0;      // AHB-AP
[08:50:25.642]              // -> [__ap <= 0x00000000]
[08:50:25.642]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:50:25.643]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:50:25.643]              // -> [ROMTableBase <= 0xF0000000]
[08:50:25.643]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:50:25.644]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:50:25.645]              // -> [pidr1 <= 0x00000004]
[08:50:25.645]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:50:25.646]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:50:25.646]              // -> [pidr2 <= 0x0000000A]
[08:50:25.646]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:50:25.647]              // -> [jep106id <= 0x00000020]
[08:50:25.647]          </block>
[08:50:25.647]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:50:25.647]            // if-block "jep106id != 0x20"
[08:50:25.647]              // =>  FALSE
[08:50:25.648]            // skip if-block "jep106id != 0x20"
[08:50:25.648]          </control>
[08:50:25.648]        </sequence>
[08:50:25.648]    </block>
[08:50:25.648]  </sequence>
[08:50:25.649]  
[08:50:25.660]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:50:25.660]  
[08:50:25.678]  <debugvars>
[08:50:25.678]    // Pre-defined
[08:50:25.679]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:50:25.680]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:50:25.681]    __dp=0x00000000
[08:50:25.681]    __ap=0x00000000
[08:50:25.682]    __traceout=0x00000000      (Trace Disabled)
[08:50:25.683]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:50:25.683]    __FlashAddr=0x00000000
[08:50:25.684]    __FlashLen=0x00000000
[08:50:25.684]    __FlashArg=0x00000000
[08:50:25.685]    __FlashOp=0x00000000
[08:50:25.686]    __Result=0x00000000
[08:50:25.686]    
[08:50:25.686]    // User-defined
[08:50:25.687]    DbgMCU_CR=0x00000007
[08:50:25.687]    DbgMCU_APB1_Fz=0x00000000
[08:50:25.688]    DbgMCU_APB2_Fz=0x00000000
[08:50:25.688]    DoOptionByteLoading=0x00000000
[08:50:25.690]  </debugvars>
[08:50:25.691]  
[08:50:25.691]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:50:25.692]    <block atomic="false" info="">
[08:50:25.693]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:50:25.695]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:50:25.696]    </block>
[08:50:25.697]    <block atomic="false" info="DbgMCU registers">
[08:50:25.698]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:50:25.700]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[08:50:25.702]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[08:50:25.702]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:50:25.703]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:50:25.704]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:50:25.705]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:50:25.705]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:50:25.707]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:50:25.707]    </block>
[08:50:25.708]  </sequence>
[08:50:25.708]  
[08:50:32.978]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[08:50:32.978]  
[08:50:32.979]  <debugvars>
[08:50:32.980]    // Pre-defined
[08:50:32.981]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:50:32.982]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:50:32.982]    __dp=0x00000000
[08:50:32.983]    __ap=0x00000000
[08:50:32.984]    __traceout=0x00000000      (Trace Disabled)
[08:50:32.984]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:50:32.985]    __FlashAddr=0x00000000
[08:50:32.986]    __FlashLen=0x00000000
[08:50:32.986]    __FlashArg=0x00000000
[08:50:32.986]    __FlashOp=0x00000000
[08:50:32.987]    __Result=0x00000000
[08:50:32.988]    
[08:50:32.988]    // User-defined
[08:50:32.989]    DbgMCU_CR=0x00000007
[08:50:32.990]    DbgMCU_APB1_Fz=0x00000000
[08:50:32.990]    DbgMCU_APB2_Fz=0x00000000
[08:50:32.991]    DoOptionByteLoading=0x00000000
[08:50:32.992]  </debugvars>
[08:50:32.992]  
[08:50:32.993]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[08:50:32.994]    <block atomic="false" info="">
[08:50:32.994]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[08:50:32.995]        // -> [connectionFlash <= 0x00000001]
[08:50:32.995]      __var FLASH_BASE = 0x40022000 ;
[08:50:32.996]        // -> [FLASH_BASE <= 0x40022000]
[08:50:32.997]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[08:50:32.997]        // -> [FLASH_CR <= 0x40022004]
[08:50:32.998]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[08:50:32.998]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[08:50:32.999]      __var LOCK_BIT = ( 1 << 0 ) ;
[08:50:32.999]        // -> [LOCK_BIT <= 0x00000001]
[08:50:33.000]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[08:50:33.000]        // -> [OPTLOCK_BIT <= 0x00000004]
[08:50:33.000]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[08:50:33.000]        // -> [FLASH_KEYR <= 0x4002200C]
[08:50:33.001]      __var FLASH_KEY1 = 0x89ABCDEF ;
[08:50:33.002]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[08:50:33.002]      __var FLASH_KEY2 = 0x02030405 ;
[08:50:33.002]        // -> [FLASH_KEY2 <= 0x02030405]
[08:50:33.003]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[08:50:33.003]        // -> [FLASH_OPTKEYR <= 0x40022014]
[08:50:33.003]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[08:50:33.004]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[08:50:33.004]      __var FLASH_OPTKEY2 = 0x24252627 ;
[08:50:33.004]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[08:50:33.005]      __var FLASH_CR_Value = 0 ;
[08:50:33.005]        // -> [FLASH_CR_Value <= 0x00000000]
[08:50:33.005]      __var DoDebugPortStop = 1 ;
[08:50:33.006]        // -> [DoDebugPortStop <= 0x00000001]
[08:50:33.006]      __var DP_CTRL_STAT = 0x4 ;
[08:50:33.006]        // -> [DP_CTRL_STAT <= 0x00000004]
[08:50:33.007]      __var DP_SELECT = 0x8 ;
[08:50:33.007]        // -> [DP_SELECT <= 0x00000008]
[08:50:33.007]    </block>
[08:50:33.007]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[08:50:33.008]      // if-block "connectionFlash && DoOptionByteLoading"
[08:50:33.008]        // =>  FALSE
[08:50:33.008]      // skip if-block "connectionFlash && DoOptionByteLoading"
[08:50:33.008]    </control>
[08:50:33.008]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[08:50:33.008]      // if-block "DoDebugPortStop"
[08:50:33.009]        // =>  TRUE
[08:50:33.009]      <block atomic="false" info="">
[08:50:33.009]        WriteDP(DP_SELECT, 0x00000000);
[08:50:33.009]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[08:50:33.010]        WriteDP(DP_CTRL_STAT, 0x00000000);
[08:50:33.010]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[08:50:33.010]      </block>
[08:50:33.011]      // end if-block "DoDebugPortStop"
[08:50:33.011]    </control>
[08:50:33.011]  </sequence>
[08:50:33.011]  
[08:54:44.295]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:54:44.295]  
[08:54:44.296]  <debugvars>
[08:54:44.296]    // Pre-defined
[08:54:44.297]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:54:44.297]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:54:44.298]    __dp=0x00000000
[08:54:44.299]    __ap=0x00000000
[08:54:44.299]    __traceout=0x00000000      (Trace Disabled)
[08:54:44.300]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:54:44.300]    __FlashAddr=0x00000000
[08:54:44.301]    __FlashLen=0x00000000
[08:54:44.301]    __FlashArg=0x00000000
[08:54:44.302]    __FlashOp=0x00000000
[08:54:44.302]    __Result=0x00000000
[08:54:44.302]    
[08:54:44.302]    // User-defined
[08:54:44.303]    DbgMCU_CR=0x00000007
[08:54:44.303]    DbgMCU_APB1_Fz=0x00000000
[08:54:44.303]    DbgMCU_APB2_Fz=0x00000000
[08:54:44.303]    DoOptionByteLoading=0x00000000
[08:54:44.304]  </debugvars>
[08:54:44.304]  
[08:54:44.305]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:54:44.306]    <block atomic="false" info="">
[08:54:44.306]      Sequence("CheckID");
[08:54:44.307]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:54:44.307]          <block atomic="false" info="">
[08:54:44.308]            __var pidr1 = 0;
[08:54:44.308]              // -> [pidr1 <= 0x00000000]
[08:54:44.309]            __var pidr2 = 0;
[08:54:44.309]              // -> [pidr2 <= 0x00000000]
[08:54:44.310]            __var jep106id = 0;
[08:54:44.310]              // -> [jep106id <= 0x00000000]
[08:54:44.311]            __var ROMTableBase = 0;
[08:54:44.311]              // -> [ROMTableBase <= 0x00000000]
[08:54:44.312]            __ap = 0;      // AHB-AP
[08:54:44.312]              // -> [__ap <= 0x00000000]
[08:54:44.313]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:54:44.314]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:54:44.314]              // -> [ROMTableBase <= 0xF0000000]
[08:54:44.314]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:54:44.316]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:54:44.316]              // -> [pidr1 <= 0x00000004]
[08:54:44.316]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:54:44.317]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:54:44.317]              // -> [pidr2 <= 0x0000000A]
[08:54:44.317]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:54:44.318]              // -> [jep106id <= 0x00000020]
[08:54:44.318]          </block>
[08:54:44.318]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:54:44.318]            // if-block "jep106id != 0x20"
[08:54:44.319]              // =>  FALSE
[08:54:44.320]            // skip if-block "jep106id != 0x20"
[08:54:44.320]          </control>
[08:54:44.320]        </sequence>
[08:54:44.321]    </block>
[08:54:44.321]  </sequence>
[08:54:44.322]  
[08:54:44.333]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:54:44.333]  
[08:54:44.343]  <debugvars>
[08:54:44.343]    // Pre-defined
[08:54:44.344]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:54:44.345]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:54:44.345]    __dp=0x00000000
[08:54:44.346]    __ap=0x00000000
[08:54:44.346]    __traceout=0x00000000      (Trace Disabled)
[08:54:44.347]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:54:44.348]    __FlashAddr=0x00000000
[08:54:44.349]    __FlashLen=0x00000000
[08:54:44.349]    __FlashArg=0x00000000
[08:54:44.350]    __FlashOp=0x00000000
[08:54:44.350]    __Result=0x00000000
[08:54:44.351]    
[08:54:44.351]    // User-defined
[08:54:44.351]    DbgMCU_CR=0x00000007
[08:54:44.352]    DbgMCU_APB1_Fz=0x00000000
[08:54:44.352]    DbgMCU_APB2_Fz=0x00000000
[08:54:44.353]    DoOptionByteLoading=0x00000000
[08:54:44.353]  </debugvars>
[08:54:44.353]  
[08:54:44.354]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:54:44.355]    <block atomic="false" info="">
[08:54:44.355]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:54:44.356]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:54:44.356]    </block>
[08:54:44.357]    <block atomic="false" info="DbgMCU registers">
[08:54:44.357]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:54:44.358]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[08:54:44.359]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[08:54:44.359]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:54:44.360]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:54:44.360]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:54:44.361]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:54:44.361]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:54:44.362]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:54:44.362]    </block>
[08:54:44.362]  </sequence>
[08:54:44.362]  
[08:54:47.094]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[08:54:47.094]  
[08:54:47.095]  <debugvars>
[08:54:47.095]    // Pre-defined
[08:54:47.095]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:54:47.095]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:54:47.096]    __dp=0x00000000
[08:54:47.097]    __ap=0x00000000
[08:54:47.097]    __traceout=0x00000000      (Trace Disabled)
[08:54:47.097]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:54:47.097]    __FlashAddr=0x00000000
[08:54:47.097]    __FlashLen=0x00000000
[08:54:47.098]    __FlashArg=0x00000000
[08:54:47.098]    __FlashOp=0x00000000
[08:54:47.098]    __Result=0x00000000
[08:54:47.098]    
[08:54:47.098]    // User-defined
[08:54:47.098]    DbgMCU_CR=0x00000007
[08:54:47.099]    DbgMCU_APB1_Fz=0x00000000
[08:54:47.099]    DbgMCU_APB2_Fz=0x00000000
[08:54:47.099]    DoOptionByteLoading=0x00000000
[08:54:47.099]  </debugvars>
[08:54:47.099]  
[08:54:47.100]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[08:54:47.100]    <block atomic="false" info="">
[08:54:47.100]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[08:54:47.100]        // -> [connectionFlash <= 0x00000001]
[08:54:47.100]      __var FLASH_BASE = 0x40022000 ;
[08:54:47.100]        // -> [FLASH_BASE <= 0x40022000]
[08:54:47.101]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[08:54:47.101]        // -> [FLASH_CR <= 0x40022004]
[08:54:47.101]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[08:54:47.101]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[08:54:47.101]      __var LOCK_BIT = ( 1 << 0 ) ;
[08:54:47.101]        // -> [LOCK_BIT <= 0x00000001]
[08:54:47.102]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[08:54:47.102]        // -> [OPTLOCK_BIT <= 0x00000004]
[08:54:47.102]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[08:54:47.102]        // -> [FLASH_KEYR <= 0x4002200C]
[08:54:47.102]      __var FLASH_KEY1 = 0x89ABCDEF ;
[08:54:47.103]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[08:54:47.103]      __var FLASH_KEY2 = 0x02030405 ;
[08:54:47.103]        // -> [FLASH_KEY2 <= 0x02030405]
[08:54:47.103]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[08:54:47.103]        // -> [FLASH_OPTKEYR <= 0x40022014]
[08:54:47.103]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[08:54:47.104]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[08:54:47.104]      __var FLASH_OPTKEY2 = 0x24252627 ;
[08:54:47.104]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[08:54:47.105]      __var FLASH_CR_Value = 0 ;
[08:54:47.105]        // -> [FLASH_CR_Value <= 0x00000000]
[08:54:47.105]      __var DoDebugPortStop = 1 ;
[08:54:47.105]        // -> [DoDebugPortStop <= 0x00000001]
[08:54:47.106]      __var DP_CTRL_STAT = 0x4 ;
[08:54:47.106]        // -> [DP_CTRL_STAT <= 0x00000004]
[08:54:47.106]      __var DP_SELECT = 0x8 ;
[08:54:47.106]        // -> [DP_SELECT <= 0x00000008]
[08:54:47.106]    </block>
[08:54:47.107]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[08:54:47.107]      // if-block "connectionFlash && DoOptionByteLoading"
[08:54:47.107]        // =>  FALSE
[08:54:47.107]      // skip if-block "connectionFlash && DoOptionByteLoading"
[08:54:47.107]    </control>
[08:54:47.108]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[08:54:47.108]      // if-block "DoDebugPortStop"
[08:54:47.108]        // =>  TRUE
[08:54:47.108]      <block atomic="false" info="">
[08:54:47.108]        WriteDP(DP_SELECT, 0x00000000);
[08:54:47.109]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[08:54:47.109]        WriteDP(DP_CTRL_STAT, 0x00000000);
[08:54:47.109]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[08:54:47.109]      </block>
[08:54:47.110]      // end if-block "DoDebugPortStop"
[08:54:47.110]    </control>
[08:54:47.110]  </sequence>
[08:54:47.110]  
[08:54:54.142]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[08:54:54.142]  
[08:54:54.143]  <debugvars>
[08:54:54.143]    // Pre-defined
[08:54:54.143]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:54:54.144]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:54:54.144]    __dp=0x00000000
[08:54:54.144]    __ap=0x00000000
[08:54:54.145]    __traceout=0x00000000      (Trace Disabled)
[08:54:54.145]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:54:54.145]    __FlashAddr=0x00000000
[08:54:54.145]    __FlashLen=0x00000000
[08:54:54.146]    __FlashArg=0x00000000
[08:54:54.146]    __FlashOp=0x00000000
[08:54:54.146]    __Result=0x00000000
[08:54:54.146]    
[08:54:54.146]    // User-defined
[08:54:54.146]    DbgMCU_CR=0x00000007
[08:54:54.147]    DbgMCU_APB1_Fz=0x00000000
[08:54:54.147]    DbgMCU_APB2_Fz=0x00000000
[08:54:54.147]    DoOptionByteLoading=0x00000000
[08:54:54.147]  </debugvars>
[08:54:54.147]  
[08:54:54.147]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[08:54:54.148]    <block atomic="false" info="">
[08:54:54.148]      Sequence("CheckID");
[08:54:54.148]        <sequence name="CheckID" Pname="" disable="false" info="">
[08:54:54.148]          <block atomic="false" info="">
[08:54:54.149]            __var pidr1 = 0;
[08:54:54.149]              // -> [pidr1 <= 0x00000000]
[08:54:54.149]            __var pidr2 = 0;
[08:54:54.150]              // -> [pidr2 <= 0x00000000]
[08:54:54.150]            __var jep106id = 0;
[08:54:54.150]              // -> [jep106id <= 0x00000000]
[08:54:54.150]            __var ROMTableBase = 0;
[08:54:54.151]              // -> [ROMTableBase <= 0x00000000]
[08:54:54.151]            __ap = 0;      // AHB-AP
[08:54:54.151]              // -> [__ap <= 0x00000000]
[08:54:54.151]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[08:54:54.152]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[08:54:54.152]              // -> [ROMTableBase <= 0xF0000000]
[08:54:54.152]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[08:54:54.153]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[08:54:54.154]              // -> [pidr1 <= 0x00000004]
[08:54:54.154]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[08:54:54.155]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[08:54:54.155]              // -> [pidr2 <= 0x0000000A]
[08:54:54.156]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[08:54:54.156]              // -> [jep106id <= 0x00000020]
[08:54:54.156]          </block>
[08:54:54.156]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[08:54:54.157]            // if-block "jep106id != 0x20"
[08:54:54.157]              // =>  FALSE
[08:54:54.157]            // skip if-block "jep106id != 0x20"
[08:54:54.157]          </control>
[08:54:54.157]        </sequence>
[08:54:54.158]    </block>
[08:54:54.158]  </sequence>
[08:54:54.158]  
[08:54:54.169]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[08:54:54.169]  
[08:54:54.170]  <debugvars>
[08:54:54.170]    // Pre-defined
[08:54:54.170]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:54:54.171]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:54:54.171]    __dp=0x00000000
[08:54:54.171]    __ap=0x00000000
[08:54:54.172]    __traceout=0x00000000      (Trace Disabled)
[08:54:54.172]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:54:54.172]    __FlashAddr=0x00000000
[08:54:54.172]    __FlashLen=0x00000000
[08:54:54.173]    __FlashArg=0x00000000
[08:54:54.173]    __FlashOp=0x00000000
[08:54:54.174]    __Result=0x00000000
[08:54:54.174]    
[08:54:54.174]    // User-defined
[08:54:54.174]    DbgMCU_CR=0x00000007
[08:54:54.175]    DbgMCU_APB1_Fz=0x00000000
[08:54:54.175]    DbgMCU_APB2_Fz=0x00000000
[08:54:54.175]    DoOptionByteLoading=0x00000000
[08:54:54.176]  </debugvars>
[08:54:54.176]  
[08:54:54.176]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[08:54:54.177]    <block atomic="false" info="">
[08:54:54.177]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[08:54:54.177]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[08:54:54.178]    </block>
[08:54:54.178]    <block atomic="false" info="DbgMCU registers">
[08:54:54.178]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[08:54:54.179]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[08:54:54.180]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[08:54:54.181]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[08:54:54.182]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[08:54:54.182]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[08:54:54.183]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:54:54.184]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[08:54:54.185]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[08:54:54.185]    </block>
[08:54:54.185]  </sequence>
[08:54:54.186]  
[08:55:01.452]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[08:55:01.452]  
[08:55:01.453]  <debugvars>
[08:55:01.453]    // Pre-defined
[08:55:01.453]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[08:55:01.454]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[08:55:01.454]    __dp=0x00000000
[08:55:01.455]    __ap=0x00000000
[08:55:01.455]    __traceout=0x00000000      (Trace Disabled)
[08:55:01.456]    __errorcontrol=0x00000000  (Skip Errors="False")
[08:55:01.456]    __FlashAddr=0x00000000
[08:55:01.456]    __FlashLen=0x00000000
[08:55:01.457]    __FlashArg=0x00000000
[08:55:01.457]    __FlashOp=0x00000000
[08:55:01.457]    __Result=0x00000000
[08:55:01.458]    
[08:55:01.458]    // User-defined
[08:55:01.458]    DbgMCU_CR=0x00000007
[08:55:01.458]    DbgMCU_APB1_Fz=0x00000000
[08:55:01.458]    DbgMCU_APB2_Fz=0x00000000
[08:55:01.458]    DoOptionByteLoading=0x00000000
[08:55:01.459]  </debugvars>
[08:55:01.459]  
[08:55:01.459]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[08:55:01.459]    <block atomic="false" info="">
[08:55:01.459]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[08:55:01.459]        // -> [connectionFlash <= 0x00000001]
[08:55:01.460]      __var FLASH_BASE = 0x40022000 ;
[08:55:01.460]        // -> [FLASH_BASE <= 0x40022000]
[08:55:01.460]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[08:55:01.460]        // -> [FLASH_CR <= 0x40022004]
[08:55:01.460]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[08:55:01.461]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[08:55:01.461]      __var LOCK_BIT = ( 1 << 0 ) ;
[08:55:01.461]        // -> [LOCK_BIT <= 0x00000001]
[08:55:01.461]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[08:55:01.461]        // -> [OPTLOCK_BIT <= 0x00000004]
[08:55:01.461]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[08:55:01.462]        // -> [FLASH_KEYR <= 0x4002200C]
[08:55:01.462]      __var FLASH_KEY1 = 0x89ABCDEF ;
[08:55:01.462]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[08:55:01.462]      __var FLASH_KEY2 = 0x02030405 ;
[08:55:01.462]        // -> [FLASH_KEY2 <= 0x02030405]
[08:55:01.463]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[08:55:01.463]        // -> [FLASH_OPTKEYR <= 0x40022014]
[08:55:01.463]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[08:55:01.463]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[08:55:01.463]      __var FLASH_OPTKEY2 = 0x24252627 ;
[08:55:01.463]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[08:55:01.464]      __var FLASH_CR_Value = 0 ;
[08:55:01.465]        // -> [FLASH_CR_Value <= 0x00000000]
[08:55:01.465]      __var DoDebugPortStop = 1 ;
[08:55:01.465]        // -> [DoDebugPortStop <= 0x00000001]
[08:55:01.465]      __var DP_CTRL_STAT = 0x4 ;
[08:55:01.466]        // -> [DP_CTRL_STAT <= 0x00000004]
[08:55:01.466]      __var DP_SELECT = 0x8 ;
[08:55:01.466]        // -> [DP_SELECT <= 0x00000008]
[08:55:01.466]    </block>
[08:55:01.466]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[08:55:01.467]      // if-block "connectionFlash && DoOptionByteLoading"
[08:55:01.467]        // =>  FALSE
[08:55:01.467]      // skip if-block "connectionFlash && DoOptionByteLoading"
[08:55:01.467]    </control>
[08:55:01.467]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[08:55:01.467]      // if-block "DoDebugPortStop"
[08:55:01.468]        // =>  TRUE
[08:55:01.468]      <block atomic="false" info="">
[08:55:01.468]        WriteDP(DP_SELECT, 0x00000000);
[08:55:01.468]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[08:55:01.468]        WriteDP(DP_CTRL_STAT, 0x00000000);
[08:55:01.469]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[08:55:01.469]      </block>
[08:55:01.469]      // end if-block "DoDebugPortStop"
[08:55:01.470]    </control>
[08:55:01.470]  </sequence>
[08:55:01.471]  
[09:11:36.815]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[09:11:36.815]  
[09:11:36.816]  <debugvars>
[09:11:36.816]    // Pre-defined
[09:11:36.816]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:11:36.817]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:11:36.817]    __dp=0x00000000
[09:11:36.817]    __ap=0x00000000
[09:11:36.817]    __traceout=0x00000000      (Trace Disabled)
[09:11:36.818]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:11:36.818]    __FlashAddr=0x00000000
[09:11:36.818]    __FlashLen=0x00000000
[09:11:36.819]    __FlashArg=0x00000000
[09:11:36.819]    __FlashOp=0x00000000
[09:11:36.819]    __Result=0x00000000
[09:11:36.819]    
[09:11:36.819]    // User-defined
[09:11:36.820]    DbgMCU_CR=0x00000007
[09:11:36.820]    DbgMCU_APB1_Fz=0x00000000
[09:11:36.820]    DbgMCU_APB2_Fz=0x00000000
[09:11:36.820]    DoOptionByteLoading=0x00000000
[09:11:36.820]  </debugvars>
[09:11:36.821]  
[09:11:36.822]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[09:11:36.822]    <block atomic="false" info="">
[09:11:36.822]      Sequence("CheckID");
[09:11:36.823]        <sequence name="CheckID" Pname="" disable="false" info="">
[09:11:36.823]          <block atomic="false" info="">
[09:11:36.823]            __var pidr1 = 0;
[09:11:36.823]              // -> [pidr1 <= 0x00000000]
[09:11:36.824]            __var pidr2 = 0;
[09:11:36.824]              // -> [pidr2 <= 0x00000000]
[09:11:36.824]            __var jep106id = 0;
[09:11:36.824]              // -> [jep106id <= 0x00000000]
[09:11:36.824]            __var ROMTableBase = 0;
[09:11:36.825]              // -> [ROMTableBase <= 0x00000000]
[09:11:36.825]            __ap = 0;      // AHB-AP
[09:11:36.825]              // -> [__ap <= 0x00000000]
[09:11:36.825]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[09:11:36.826]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[09:11:36.826]              // -> [ROMTableBase <= 0xF0000000]
[09:11:36.826]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[09:11:36.827]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[09:11:36.828]              // -> [pidr1 <= 0x00000004]
[09:11:36.828]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[09:11:36.829]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[09:11:36.829]              // -> [pidr2 <= 0x0000000A]
[09:11:36.830]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[09:11:36.830]              // -> [jep106id <= 0x00000020]
[09:11:36.830]          </block>
[09:11:36.830]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[09:11:36.831]            // if-block "jep106id != 0x20"
[09:11:36.831]              // =>  FALSE
[09:11:36.831]            // skip if-block "jep106id != 0x20"
[09:11:36.831]          </control>
[09:11:36.831]        </sequence>
[09:11:36.831]    </block>
[09:11:36.832]  </sequence>
[09:11:36.832]  
[09:11:36.844]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[09:11:36.844]  
[09:11:36.861]  <debugvars>
[09:11:36.861]    // Pre-defined
[09:11:36.862]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:11:36.863]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:11:36.864]    __dp=0x00000000
[09:11:36.864]    __ap=0x00000000
[09:11:36.865]    __traceout=0x00000000      (Trace Disabled)
[09:11:36.865]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:11:36.866]    __FlashAddr=0x00000000
[09:11:36.867]    __FlashLen=0x00000000
[09:11:36.868]    __FlashArg=0x00000000
[09:11:36.868]    __FlashOp=0x00000000
[09:11:36.868]    __Result=0x00000000
[09:11:36.870]    
[09:11:36.870]    // User-defined
[09:11:36.871]    DbgMCU_CR=0x00000007
[09:11:36.871]    DbgMCU_APB1_Fz=0x00000000
[09:11:36.872]    DbgMCU_APB2_Fz=0x00000000
[09:11:36.872]    DoOptionByteLoading=0x00000000
[09:11:36.873]  </debugvars>
[09:11:36.873]  
[09:11:36.874]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[09:11:36.874]    <block atomic="false" info="">
[09:11:36.876]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[09:11:36.878]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[09:11:36.879]    </block>
[09:11:36.880]    <block atomic="false" info="DbgMCU registers">
[09:11:36.881]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[09:11:36.883]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[09:11:36.885]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[09:11:36.885]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[09:11:36.886]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[09:11:36.886]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[09:11:36.887]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:11:36.889]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[09:11:36.890]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[09:11:36.890]    </block>
[09:11:36.891]  </sequence>
[09:11:36.891]  
[09:11:44.199]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[09:11:44.199]  
[09:11:44.200]  <debugvars>
[09:11:44.201]    // Pre-defined
[09:11:44.201]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[09:11:44.202]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[09:11:44.203]    __dp=0x00000000
[09:11:44.203]    __ap=0x00000000
[09:11:44.204]    __traceout=0x00000000      (Trace Disabled)
[09:11:44.204]    __errorcontrol=0x00000000  (Skip Errors="False")
[09:11:44.204]    __FlashAddr=0x00000000
[09:11:44.205]    __FlashLen=0x00000000
[09:11:44.205]    __FlashArg=0x00000000
[09:11:44.205]    __FlashOp=0x00000000
[09:11:44.206]    __Result=0x00000000
[09:11:44.206]    
[09:11:44.206]    // User-defined
[09:11:44.206]    DbgMCU_CR=0x00000007
[09:11:44.207]    DbgMCU_APB1_Fz=0x00000000
[09:11:44.207]    DbgMCU_APB2_Fz=0x00000000
[09:11:44.207]    DoOptionByteLoading=0x00000000
[09:11:44.208]  </debugvars>
[09:11:44.208]  
[09:11:44.208]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[09:11:44.208]    <block atomic="false" info="">
[09:11:44.208]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[09:11:44.209]        // -> [connectionFlash <= 0x00000001]
[09:11:44.209]      __var FLASH_BASE = 0x40022000 ;
[09:11:44.209]        // -> [FLASH_BASE <= 0x40022000]
[09:11:44.209]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[09:11:44.209]        // -> [FLASH_CR <= 0x40022004]
[09:11:44.210]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[09:11:44.210]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[09:11:44.210]      __var LOCK_BIT = ( 1 << 0 ) ;
[09:11:44.210]        // -> [LOCK_BIT <= 0x00000001]
[09:11:44.210]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[09:11:44.211]        // -> [OPTLOCK_BIT <= 0x00000004]
[09:11:44.211]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[09:11:44.211]        // -> [FLASH_KEYR <= 0x4002200C]
[09:11:44.211]      __var FLASH_KEY1 = 0x89ABCDEF ;
[09:11:44.211]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[09:11:44.211]      __var FLASH_KEY2 = 0x02030405 ;
[09:11:44.212]        // -> [FLASH_KEY2 <= 0x02030405]
[09:11:44.212]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[09:11:44.212]        // -> [FLASH_OPTKEYR <= 0x40022014]
[09:11:44.212]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[09:11:44.212]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[09:11:44.213]      __var FLASH_OPTKEY2 = 0x24252627 ;
[09:11:44.213]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[09:11:44.213]      __var FLASH_CR_Value = 0 ;
[09:11:44.213]        // -> [FLASH_CR_Value <= 0x00000000]
[09:11:44.213]      __var DoDebugPortStop = 1 ;
[09:11:44.213]        // -> [DoDebugPortStop <= 0x00000001]
[09:11:44.214]      __var DP_CTRL_STAT = 0x4 ;
[09:11:44.214]        // -> [DP_CTRL_STAT <= 0x00000004]
[09:11:44.214]      __var DP_SELECT = 0x8 ;
[09:11:44.214]        // -> [DP_SELECT <= 0x00000008]
[09:11:44.214]    </block>
[09:11:44.215]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[09:11:44.215]      // if-block "connectionFlash && DoOptionByteLoading"
[09:11:44.215]        // =>  FALSE
[09:11:44.215]      // skip if-block "connectionFlash && DoOptionByteLoading"
[09:11:44.215]    </control>
[09:11:44.216]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[09:11:44.216]      // if-block "DoDebugPortStop"
[09:11:44.216]        // =>  TRUE
[09:11:44.216]      <block atomic="false" info="">
[09:11:44.216]        WriteDP(DP_SELECT, 0x00000000);
[09:11:44.217]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[09:11:44.217]        WriteDP(DP_CTRL_STAT, 0x00000000);
[09:11:44.217]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[09:11:44.218]      </block>
[09:11:44.218]      // end if-block "DoDebugPortStop"
[09:11:44.218]    </control>
[09:11:44.218]  </sequence>
[09:11:44.218]  
[10:32:53.253]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:32:53.253]  
[10:32:53.267]  <debugvars>
[10:32:53.267]    // Pre-defined
[10:32:53.267]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:32:53.267]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:32:53.267]    __dp=0x00000000
[10:32:53.267]    __ap=0x00000000
[10:32:53.267]    __traceout=0x00000000      (Trace Disabled)
[10:32:53.267]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:32:53.267]    __FlashAddr=0x00000000
[10:32:53.267]    __FlashLen=0x00000000
[10:32:53.267]    __FlashArg=0x00000000
[10:32:53.267]    __FlashOp=0x00000000
[10:32:53.267]    __Result=0x00000000
[10:32:53.267]    
[10:32:53.267]    // User-defined
[10:32:53.267]    DbgMCU_CR=0x00000007
[10:32:53.267]    DbgMCU_APB1_Fz=0x00000000
[10:32:53.267]    DbgMCU_APB2_Fz=0x00000000
[10:32:53.267]    DoOptionByteLoading=0x00000000
[10:32:53.267]  </debugvars>
[10:32:53.267]  
[10:32:53.273]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:32:53.273]    <block atomic="false" info="">
[10:32:53.273]      Sequence("CheckID");
[10:32:53.273]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:32:53.273]          <block atomic="false" info="">
[10:32:53.273]            __var pidr1 = 0;
[10:32:53.273]              // -> [pidr1 <= 0x00000000]
[10:32:53.273]            __var pidr2 = 0;
[10:32:53.273]              // -> [pidr2 <= 0x00000000]
[10:32:53.273]            __var jep106id = 0;
[10:32:53.275]              // -> [jep106id <= 0x00000000]
[10:32:53.275]            __var ROMTableBase = 0;
[10:32:53.275]              // -> [ROMTableBase <= 0x00000000]
[10:32:53.275]            __ap = 0;      // AHB-AP
[10:32:53.275]              // -> [__ap <= 0x00000000]
[10:32:53.275]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:32:53.275]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:32:53.277]              // -> [ROMTableBase <= 0xF0000000]
[10:32:53.277]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:32:53.277]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:32:53.277]              // -> [pidr1 <= 0x00000004]
[10:32:53.277]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:32:53.277]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:32:53.277]              // -> [pidr2 <= 0x0000000A]
[10:32:53.277]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:32:53.277]              // -> [jep106id <= 0x00000020]
[10:32:53.277]          </block>
[10:32:53.277]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:32:53.277]            // if-block "jep106id != 0x20"
[10:32:53.282]              // =>  FALSE
[10:32:53.282]            // skip if-block "jep106id != 0x20"
[10:32:53.282]          </control>
[10:32:53.282]        </sequence>
[10:32:53.282]    </block>
[10:32:53.282]  </sequence>
[10:32:53.282]  
[10:32:53.292]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:32:53.292]  
[10:32:53.298]  <debugvars>
[10:32:53.298]    // Pre-defined
[10:32:53.298]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:32:53.298]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:32:53.298]    __dp=0x00000000
[10:32:53.298]    __ap=0x00000000
[10:32:53.298]    __traceout=0x00000000      (Trace Disabled)
[10:32:53.298]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:32:53.298]    __FlashAddr=0x00000000
[10:32:53.300]    __FlashLen=0x00000000
[10:32:53.300]    __FlashArg=0x00000000
[10:32:53.300]    __FlashOp=0x00000000
[10:32:53.300]    __Result=0x00000000
[10:32:53.300]    
[10:32:53.300]    // User-defined
[10:32:53.300]    DbgMCU_CR=0x00000007
[10:32:53.300]    DbgMCU_APB1_Fz=0x00000000
[10:32:53.300]    DbgMCU_APB2_Fz=0x00000000
[10:32:53.300]    DoOptionByteLoading=0x00000000
[10:32:53.300]  </debugvars>
[10:32:53.300]  
[10:32:53.302]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:32:53.302]    <block atomic="false" info="">
[10:32:53.302]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:32:53.302]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:32:53.304]    </block>
[10:32:53.304]    <block atomic="false" info="DbgMCU registers">
[10:32:53.304]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:32:53.304]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[10:32:53.306]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[10:32:53.306]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:32:53.306]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:32:53.306]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:32:53.306]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:32:53.306]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:32:53.306]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:32:53.309]    </block>
[10:32:53.309]  </sequence>
[10:32:53.309]  
[10:33:00.239]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:33:00.239]  
[10:33:00.240]  <debugvars>
[10:33:00.240]    // Pre-defined
[10:33:00.240]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:33:00.241]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:33:00.241]    __dp=0x00000000
[10:33:00.241]    __ap=0x00000000
[10:33:00.242]    __traceout=0x00000000      (Trace Disabled)
[10:33:00.242]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:33:00.242]    __FlashAddr=0x00000000
[10:33:00.242]    __FlashLen=0x00000000
[10:33:00.242]    __FlashArg=0x00000000
[10:33:00.243]    __FlashOp=0x00000000
[10:33:00.243]    __Result=0x00000000
[10:33:00.243]    
[10:33:00.243]    // User-defined
[10:33:00.243]    DbgMCU_CR=0x00000007
[10:33:00.243]    DbgMCU_APB1_Fz=0x00000000
[10:33:00.243]    DbgMCU_APB2_Fz=0x00000000
[10:33:00.243]    DoOptionByteLoading=0x00000000
[10:33:00.243]  </debugvars>
[10:33:00.243]  
[10:33:00.243]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:33:00.243]    <block atomic="false" info="">
[10:33:00.243]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:33:00.243]        // -> [connectionFlash <= 0x00000001]
[10:33:00.243]      __var FLASH_BASE = 0x40022000 ;
[10:33:00.243]        // -> [FLASH_BASE <= 0x40022000]
[10:33:00.243]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:33:00.243]        // -> [FLASH_CR <= 0x40022004]
[10:33:00.243]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:33:00.243]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:33:00.243]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:33:00.243]        // -> [LOCK_BIT <= 0x00000001]
[10:33:00.243]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:33:00.243]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:33:00.243]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:33:00.243]        // -> [FLASH_KEYR <= 0x4002200C]
[10:33:00.243]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:33:00.243]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:33:00.243]      __var FLASH_KEY2 = 0x02030405 ;
[10:33:00.243]        // -> [FLASH_KEY2 <= 0x02030405]
[10:33:00.243]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:33:00.243]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:33:00.243]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:33:00.243]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:33:00.243]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:33:00.243]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:33:00.243]      __var FLASH_CR_Value = 0 ;
[10:33:00.243]        // -> [FLASH_CR_Value <= 0x00000000]
[10:33:00.243]      __var DoDebugPortStop = 1 ;
[10:33:00.243]        // -> [DoDebugPortStop <= 0x00000001]
[10:33:00.243]      __var DP_CTRL_STAT = 0x4 ;
[10:33:00.243]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:33:00.243]      __var DP_SELECT = 0x8 ;
[10:33:00.243]        // -> [DP_SELECT <= 0x00000008]
[10:33:00.251]    </block>
[10:33:00.251]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:33:00.251]      // if-block "connectionFlash && DoOptionByteLoading"
[10:33:00.252]        // =>  FALSE
[10:33:00.252]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:33:00.252]    </control>
[10:33:00.252]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:33:00.252]      // if-block "DoDebugPortStop"
[10:33:00.252]        // =>  TRUE
[10:33:00.252]      <block atomic="false" info="">
[10:33:00.252]        WriteDP(DP_SELECT, 0x00000000);
[10:33:00.252]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:33:00.252]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:33:00.252]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:33:00.257]      </block>
[10:33:00.257]      // end if-block "DoDebugPortStop"
[10:33:00.257]    </control>
[10:33:00.257]  </sequence>
[10:33:00.258]  
[10:53:09.915]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:53:09.915]  
[10:53:09.915]  <debugvars>
[10:53:09.920]    // Pre-defined
[10:53:09.920]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:53:09.920]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:53:09.920]    __dp=0x00000000
[10:53:09.922]    __ap=0x00000000
[10:53:09.922]    __traceout=0x00000000      (Trace Disabled)
[10:53:09.922]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:53:09.922]    __FlashAddr=0x00000000
[10:53:09.924]    __FlashLen=0x00000000
[10:53:09.924]    __FlashArg=0x00000000
[10:53:09.924]    __FlashOp=0x00000000
[10:53:09.925]    __Result=0x00000000
[10:53:09.925]    
[10:53:09.925]    // User-defined
[10:53:09.925]    DbgMCU_CR=0x00000007
[10:53:09.925]    DbgMCU_APB1_Fz=0x00000000
[10:53:09.925]    DbgMCU_APB2_Fz=0x00000000
[10:53:09.927]    DoOptionByteLoading=0x00000000
[10:53:09.927]  </debugvars>
[10:53:09.927]  
[10:53:09.927]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:53:09.927]    <block atomic="false" info="">
[10:53:09.927]      Sequence("CheckID");
[10:53:09.927]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:53:09.930]          <block atomic="false" info="">
[10:53:09.930]            __var pidr1 = 0;
[10:53:09.930]              // -> [pidr1 <= 0x00000000]
[10:53:09.931]            __var pidr2 = 0;
[10:53:09.932]              // -> [pidr2 <= 0x00000000]
[10:53:09.932]            __var jep106id = 0;
[10:53:09.932]              // -> [jep106id <= 0x00000000]
[10:53:09.933]            __var ROMTableBase = 0;
[10:53:09.933]              // -> [ROMTableBase <= 0x00000000]
[10:53:09.933]            __ap = 0;      // AHB-AP
[10:53:09.934]              // -> [__ap <= 0x00000000]
[10:53:09.934]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:53:09.934]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:53:09.935]              // -> [ROMTableBase <= 0xF0000000]
[10:53:09.935]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:53:09.936]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:53:09.936]              // -> [pidr1 <= 0x00000004]
[10:53:09.937]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:53:09.937]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:53:09.937]              // -> [pidr2 <= 0x0000000A]
[10:53:09.938]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:53:09.938]              // -> [jep106id <= 0x00000020]
[10:53:09.938]          </block>
[10:53:09.939]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:53:09.939]            // if-block "jep106id != 0x20"
[10:53:09.939]              // =>  FALSE
[10:53:09.939]            // skip if-block "jep106id != 0x20"
[10:53:09.940]          </control>
[10:53:09.940]        </sequence>
[10:53:09.940]    </block>
[10:53:09.940]  </sequence>
[10:53:09.940]  
[10:53:09.953]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:53:09.953]  
[10:53:09.964]  <debugvars>
[10:53:09.969]    // Pre-defined
[10:53:09.969]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:53:09.970]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:53:09.970]    __dp=0x00000000
[10:53:09.971]    __ap=0x00000000
[10:53:09.972]    __traceout=0x00000000      (Trace Disabled)
[10:53:09.973]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:53:09.973]    __FlashAddr=0x00000000
[10:53:09.974]    __FlashLen=0x00000000
[10:53:09.975]    __FlashArg=0x00000000
[10:53:09.975]    __FlashOp=0x00000000
[10:53:09.976]    __Result=0x00000000
[10:53:09.977]    
[10:53:09.977]    // User-defined
[10:53:09.977]    DbgMCU_CR=0x00000007
[10:53:09.977]    DbgMCU_APB1_Fz=0x00000000
[10:53:09.977]    DbgMCU_APB2_Fz=0x00000000
[10:53:09.979]    DoOptionByteLoading=0x00000000
[10:53:09.979]  </debugvars>
[10:53:09.980]  
[10:53:09.980]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:53:09.980]    <block atomic="false" info="">
[10:53:09.981]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:53:09.982]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:53:09.982]    </block>
[10:53:09.982]    <block atomic="false" info="DbgMCU registers">
[10:53:09.984]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:53:09.985]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[10:53:09.987]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[10:53:09.987]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:53:09.989]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:53:09.989]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:53:09.990]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:53:09.991]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:53:09.992]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:53:09.992]    </block>
[10:53:09.992]  </sequence>
[10:53:09.993]  
[10:53:14.359]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:53:14.359]  
[10:53:14.360]  <debugvars>
[10:53:14.360]    // Pre-defined
[10:53:14.361]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:53:14.361]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:53:14.361]    __dp=0x00000000
[10:53:14.361]    __ap=0x00000000
[10:53:14.361]    __traceout=0x00000000      (Trace Disabled)
[10:53:14.361]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:53:14.361]    __FlashAddr=0x00000000
[10:53:14.361]    __FlashLen=0x00000000
[10:53:14.361]    __FlashArg=0x00000000
[10:53:14.361]    __FlashOp=0x00000000
[10:53:14.361]    __Result=0x00000000
[10:53:14.361]    
[10:53:14.361]    // User-defined
[10:53:14.361]    DbgMCU_CR=0x00000007
[10:53:14.361]    DbgMCU_APB1_Fz=0x00000000
[10:53:14.361]    DbgMCU_APB2_Fz=0x00000000
[10:53:14.361]    DoOptionByteLoading=0x00000000
[10:53:14.361]  </debugvars>
[10:53:14.361]  
[10:53:14.365]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:53:14.365]    <block atomic="false" info="">
[10:53:14.365]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:53:14.365]        // -> [connectionFlash <= 0x00000001]
[10:53:14.365]      __var FLASH_BASE = 0x40022000 ;
[10:53:14.365]        // -> [FLASH_BASE <= 0x40022000]
[10:53:14.365]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:53:14.365]        // -> [FLASH_CR <= 0x40022004]
[10:53:14.365]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:53:14.365]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:53:14.365]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:53:14.365]        // -> [LOCK_BIT <= 0x00000001]
[10:53:14.365]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:53:14.368]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:53:14.368]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:53:14.368]        // -> [FLASH_KEYR <= 0x4002200C]
[10:53:14.368]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:53:14.368]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:53:14.370]      __var FLASH_KEY2 = 0x02030405 ;
[10:53:14.370]        // -> [FLASH_KEY2 <= 0x02030405]
[10:53:14.370]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:53:14.370]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:53:14.370]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:53:14.370]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:53:14.370]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:53:14.370]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:53:14.370]      __var FLASH_CR_Value = 0 ;
[10:53:14.373]        // -> [FLASH_CR_Value <= 0x00000000]
[10:53:14.373]      __var DoDebugPortStop = 1 ;
[10:53:14.373]        // -> [DoDebugPortStop <= 0x00000001]
[10:53:14.373]      __var DP_CTRL_STAT = 0x4 ;
[10:53:14.373]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:53:14.374]      __var DP_SELECT = 0x8 ;
[10:53:14.374]        // -> [DP_SELECT <= 0x00000008]
[10:53:14.375]    </block>
[10:53:14.375]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:53:14.375]      // if-block "connectionFlash && DoOptionByteLoading"
[10:53:14.376]        // =>  FALSE
[10:53:14.376]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:53:14.376]    </control>
[10:53:14.376]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:53:14.376]      // if-block "DoDebugPortStop"
[10:53:14.377]        // =>  TRUE
[10:53:14.377]      <block atomic="false" info="">
[10:53:14.377]        WriteDP(DP_SELECT, 0x00000000);
[10:53:14.388]  
[10:53:14.388]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[10:53:14.388]  
[10:53:14.397]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:53:14.399]      </block>
[10:53:14.399]      // end if-block "DoDebugPortStop"
[10:53:14.399]    </control>
[10:53:14.399]  </sequence>
[10:53:14.399]  
[10:53:20.178]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:53:20.178]  
[10:53:20.178]  <debugvars>
[10:53:20.178]    // Pre-defined
[10:53:20.178]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:53:20.178]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:53:20.178]    __dp=0x00000000
[10:53:20.178]    __ap=0x00000000
[10:53:20.178]    __traceout=0x00000000      (Trace Disabled)
[10:53:20.178]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:53:20.178]    __FlashAddr=0x00000000
[10:53:20.178]    __FlashLen=0x00000000
[10:53:20.178]    __FlashArg=0x00000000
[10:53:20.178]    __FlashOp=0x00000000
[10:53:20.178]    __Result=0x00000000
[10:53:20.178]    
[10:53:20.178]    // User-defined
[10:53:20.178]    DbgMCU_CR=0x00000007
[10:53:20.182]    DbgMCU_APB1_Fz=0x00000000
[10:53:20.182]    DbgMCU_APB2_Fz=0x00000000
[10:53:20.182]    DoOptionByteLoading=0x00000000
[10:53:20.182]  </debugvars>
[10:53:20.182]  
[10:53:20.182]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:53:20.182]    <block atomic="false" info="">
[10:53:20.182]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:53:20.182]        // -> [connectionFlash <= 0x00000001]
[10:53:20.182]      __var FLASH_BASE = 0x40022000 ;
[10:53:20.182]        // -> [FLASH_BASE <= 0x40022000]
[10:53:20.182]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:53:20.182]        // -> [FLASH_CR <= 0x40022004]
[10:53:20.182]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:53:20.182]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:53:20.182]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:53:20.182]        // -> [LOCK_BIT <= 0x00000001]
[10:53:20.182]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:53:20.182]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:53:20.182]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:53:20.182]        // -> [FLASH_KEYR <= 0x4002200C]
[10:53:20.182]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:53:20.182]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:53:20.182]      __var FLASH_KEY2 = 0x02030405 ;
[10:53:20.187]        // -> [FLASH_KEY2 <= 0x02030405]
[10:53:20.187]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:53:20.187]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:53:20.187]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:53:20.188]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:53:20.188]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:53:20.188]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:53:20.188]      __var FLASH_CR_Value = 0 ;
[10:53:20.188]        // -> [FLASH_CR_Value <= 0x00000000]
[10:53:20.188]      __var DoDebugPortStop = 1 ;
[10:53:20.190]        // -> [DoDebugPortStop <= 0x00000001]
[10:53:20.190]      __var DP_CTRL_STAT = 0x4 ;
[10:53:20.190]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:53:20.190]      __var DP_SELECT = 0x8 ;
[10:53:20.191]        // -> [DP_SELECT <= 0x00000008]
[10:53:20.191]    </block>
[10:53:20.191]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:53:20.191]      // if-block "connectionFlash && DoOptionByteLoading"
[10:53:20.191]        // =>  FALSE
[10:53:20.192]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:53:20.192]    </control>
[10:53:20.192]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:53:20.192]      // if-block "DoDebugPortStop"
[10:53:20.192]        // =>  TRUE
[10:53:20.192]      <block atomic="false" info="">
[10:53:20.193]        WriteDP(DP_SELECT, 0x00000000);
[10:53:20.193]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:53:20.193]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:53:20.194]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:53:20.194]      </block>
[10:53:20.194]      // end if-block "DoDebugPortStop"
[10:53:20.194]    </control>
[10:53:20.194]  </sequence>
[10:53:20.194]  
[10:53:29.623]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:53:29.623]  
[10:53:29.623]  <debugvars>
[10:53:29.623]    // Pre-defined
[10:53:29.623]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:53:29.623]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:53:29.623]    __dp=0x00000000
[10:53:29.627]    __ap=0x00000000
[10:53:29.627]    __traceout=0x00000000      (Trace Disabled)
[10:53:29.627]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:53:29.627]    __FlashAddr=0x00000000
[10:53:29.627]    __FlashLen=0x00000000
[10:53:29.627]    __FlashArg=0x00000000
[10:53:29.628]    __FlashOp=0x00000000
[10:53:29.628]    __Result=0x00000000
[10:53:29.628]    
[10:53:29.628]    // User-defined
[10:53:29.628]    DbgMCU_CR=0x00000007
[10:53:29.629]    DbgMCU_APB1_Fz=0x00000000
[10:53:29.629]    DbgMCU_APB2_Fz=0x00000000
[10:53:29.629]    DoOptionByteLoading=0x00000000
[10:53:29.630]  </debugvars>
[10:53:29.630]  
[10:53:29.630]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:53:29.630]    <block atomic="false" info="">
[10:53:29.631]      Sequence("CheckID");
[10:53:29.631]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:53:29.631]          <block atomic="false" info="">
[10:53:29.631]            __var pidr1 = 0;
[10:53:29.631]              // -> [pidr1 <= 0x00000000]
[10:53:29.632]            __var pidr2 = 0;
[10:53:29.632]              // -> [pidr2 <= 0x00000000]
[10:53:29.632]            __var jep106id = 0;
[10:53:29.632]              // -> [jep106id <= 0x00000000]
[10:53:29.632]            __var ROMTableBase = 0;
[10:53:29.632]              // -> [ROMTableBase <= 0x00000000]
[10:53:29.633]            __ap = 0;      // AHB-AP
[10:53:29.633]              // -> [__ap <= 0x00000000]
[10:53:29.633]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:53:29.634]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:53:29.634]              // -> [ROMTableBase <= 0xF0000000]
[10:53:29.634]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:53:29.635]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:53:29.636]              // -> [pidr1 <= 0x00000004]
[10:53:29.636]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:53:29.637]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:53:29.638]              // -> [pidr2 <= 0x0000000A]
[10:53:29.638]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:53:29.639]              // -> [jep106id <= 0x00000020]
[10:53:29.639]          </block>
[10:53:29.639]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:53:29.640]            // if-block "jep106id != 0x20"
[10:53:29.640]              // =>  FALSE
[10:53:29.640]            // skip if-block "jep106id != 0x20"
[10:53:29.641]          </control>
[10:53:29.641]        </sequence>
[10:53:29.641]    </block>
[10:53:29.642]  </sequence>
[10:53:29.642]  
[10:53:29.653]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:53:29.653]  
[10:53:29.654]  <debugvars>
[10:53:29.654]    // Pre-defined
[10:53:29.654]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:53:29.655]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:53:29.655]    __dp=0x00000000
[10:53:29.655]    __ap=0x00000000
[10:53:29.656]    __traceout=0x00000000      (Trace Disabled)
[10:53:29.656]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:53:29.656]    __FlashAddr=0x00000000
[10:53:29.656]    __FlashLen=0x00000000
[10:53:29.657]    __FlashArg=0x00000000
[10:53:29.657]    __FlashOp=0x00000000
[10:53:29.657]    __Result=0x00000000
[10:53:29.657]    
[10:53:29.657]    // User-defined
[10:53:29.657]    DbgMCU_CR=0x00000007
[10:53:29.658]    DbgMCU_APB1_Fz=0x00000000
[10:53:29.658]    DbgMCU_APB2_Fz=0x00000000
[10:53:29.658]    DoOptionByteLoading=0x00000000
[10:53:29.658]  </debugvars>
[10:53:29.658]  
[10:53:29.659]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:53:29.659]    <block atomic="false" info="">
[10:53:29.659]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:53:29.660]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:53:29.660]    </block>
[10:53:29.660]    <block atomic="false" info="DbgMCU registers">
[10:53:29.661]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:53:29.661]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[10:53:29.662]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[10:53:29.663]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:53:29.663]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:53:29.663]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:53:29.664]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:53:29.664]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:53:29.665]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:53:29.665]    </block>
[10:53:29.665]  </sequence>
[10:53:29.665]  
[10:53:46.029]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:53:46.029]  
[10:53:46.029]  <debugvars>
[10:53:46.029]    // Pre-defined
[10:53:46.029]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:53:46.029]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:53:46.029]    __dp=0x00000000
[10:53:46.029]    __ap=0x00000000
[10:53:46.029]    __traceout=0x00000000      (Trace Disabled)
[10:53:46.029]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:53:46.029]    __FlashAddr=0x00000000
[10:53:46.029]    __FlashLen=0x00000000
[10:53:46.029]    __FlashArg=0x00000000
[10:53:46.029]    __FlashOp=0x00000000
[10:53:46.029]    __Result=0x00000000
[10:53:46.029]    
[10:53:46.029]    // User-defined
[10:53:46.029]    DbgMCU_CR=0x00000007
[10:53:46.029]    DbgMCU_APB1_Fz=0x00000000
[10:53:46.029]    DbgMCU_APB2_Fz=0x00000000
[10:53:46.029]    DoOptionByteLoading=0x00000000
[10:53:46.029]  </debugvars>
[10:53:46.034]  
[10:53:46.034]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:53:46.034]    <block atomic="false" info="">
[10:53:46.034]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:53:46.035]        // -> [connectionFlash <= 0x00000001]
[10:53:46.035]      __var FLASH_BASE = 0x40022000 ;
[10:53:46.035]        // -> [FLASH_BASE <= 0x40022000]
[10:53:46.035]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:53:46.035]        // -> [FLASH_CR <= 0x40022004]
[10:53:46.035]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:53:46.035]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:53:46.035]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:53:46.035]        // -> [LOCK_BIT <= 0x00000001]
[10:53:46.035]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:53:46.035]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:53:46.035]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:53:46.035]        // -> [FLASH_KEYR <= 0x4002200C]
[10:53:46.035]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:53:46.035]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:53:46.035]      __var FLASH_KEY2 = 0x02030405 ;
[10:53:46.035]        // -> [FLASH_KEY2 <= 0x02030405]
[10:53:46.039]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:53:46.039]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:53:46.039]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:53:46.039]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:53:46.040]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:53:46.040]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:53:46.040]      __var FLASH_CR_Value = 0 ;
[10:53:46.040]        // -> [FLASH_CR_Value <= 0x00000000]
[10:53:46.041]      __var DoDebugPortStop = 1 ;
[10:53:46.041]        // -> [DoDebugPortStop <= 0x00000001]
[10:53:46.041]      __var DP_CTRL_STAT = 0x4 ;
[10:53:46.041]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:53:46.042]      __var DP_SELECT = 0x8 ;
[10:53:46.042]        // -> [DP_SELECT <= 0x00000008]
[10:53:46.042]    </block>
[10:53:46.042]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:53:46.042]      // if-block "connectionFlash && DoOptionByteLoading"
[10:53:46.043]        // =>  FALSE
[10:53:46.043]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:53:46.043]    </control>
[10:53:46.044]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:53:46.044]      // if-block "DoDebugPortStop"
[10:53:46.044]        // =>  TRUE
[10:53:46.044]      <block atomic="false" info="">
[10:53:46.045]        WriteDP(DP_SELECT, 0x00000000);
[10:53:46.045]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:53:46.045]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:53:46.045]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:53:46.046]      </block>
[10:53:46.046]      // end if-block "DoDebugPortStop"
[10:53:46.046]    </control>
[10:53:46.046]  </sequence>
[10:53:46.047]  
[10:53:51.644]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:53:51.644]  
[10:53:51.644]  <debugvars>
[10:53:51.645]    // Pre-defined
[10:53:51.645]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:53:51.645]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:53:51.646]    __dp=0x00000000
[10:53:51.646]    __ap=0x00000000
[10:53:51.646]    __traceout=0x00000000      (Trace Disabled)
[10:53:51.646]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:53:51.647]    __FlashAddr=0x00000000
[10:53:51.647]    __FlashLen=0x00000000
[10:53:51.647]    __FlashArg=0x00000000
[10:53:51.648]    __FlashOp=0x00000000
[10:53:51.648]    __Result=0x00000000
[10:53:51.648]    
[10:53:51.648]    // User-defined
[10:53:51.648]    DbgMCU_CR=0x00000007
[10:53:51.649]    DbgMCU_APB1_Fz=0x00000000
[10:53:51.649]    DbgMCU_APB2_Fz=0x00000000
[10:53:51.649]    DoOptionByteLoading=0x00000000
[10:53:51.649]  </debugvars>
[10:53:51.650]  
[10:53:51.650]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:53:51.650]    <block atomic="false" info="">
[10:53:51.651]      Sequence("CheckID");
[10:53:51.651]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:53:51.651]          <block atomic="false" info="">
[10:53:51.651]            __var pidr1 = 0;
[10:53:51.651]              // -> [pidr1 <= 0x00000000]
[10:53:51.652]            __var pidr2 = 0;
[10:53:51.652]              // -> [pidr2 <= 0x00000000]
[10:53:51.652]            __var jep106id = 0;
[10:53:51.652]              // -> [jep106id <= 0x00000000]
[10:53:51.652]            __var ROMTableBase = 0;
[10:53:51.653]              // -> [ROMTableBase <= 0x00000000]
[10:53:51.653]            __ap = 0;      // AHB-AP
[10:53:51.653]              // -> [__ap <= 0x00000000]
[10:53:51.653]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:53:51.654]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:53:51.654]              // -> [ROMTableBase <= 0xF0000000]
[10:53:51.654]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:53:51.656]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:53:51.656]              // -> [pidr1 <= 0x00000004]
[10:53:51.656]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:53:51.657]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:53:51.657]              // -> [pidr2 <= 0x0000000A]
[10:53:51.658]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:53:51.658]              // -> [jep106id <= 0x00000020]
[10:53:51.659]          </block>
[10:53:51.659]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:53:51.659]            // if-block "jep106id != 0x20"
[10:53:51.659]              // =>  FALSE
[10:53:51.659]            // skip if-block "jep106id != 0x20"
[10:53:51.659]          </control>
[10:53:51.660]        </sequence>
[10:53:51.660]    </block>
[10:53:51.660]  </sequence>
[10:53:51.660]  
[10:53:51.672]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:53:51.672]  
[10:53:51.682]  <debugvars>
[10:53:51.682]    // Pre-defined
[10:53:51.684]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:53:51.684]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:53:51.685]    __dp=0x00000000
[10:53:51.685]    __ap=0x00000000
[10:53:51.686]    __traceout=0x00000000      (Trace Disabled)
[10:53:51.686]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:53:51.687]    __FlashAddr=0x00000000
[10:53:51.687]    __FlashLen=0x00000000
[10:53:51.688]    __FlashArg=0x00000000
[10:53:51.689]    __FlashOp=0x00000000
[10:53:51.689]    __Result=0x00000000
[10:53:51.689]    
[10:53:51.689]    // User-defined
[10:53:51.690]    DbgMCU_CR=0x00000007
[10:53:51.690]    DbgMCU_APB1_Fz=0x00000000
[10:53:51.691]    DbgMCU_APB2_Fz=0x00000000
[10:53:51.691]    DoOptionByteLoading=0x00000000
[10:53:51.691]  </debugvars>
[10:53:51.692]  
[10:53:51.692]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:53:51.693]    <block atomic="false" info="">
[10:53:51.693]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:53:51.694]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:53:51.695]    </block>
[10:53:51.695]    <block atomic="false" info="DbgMCU registers">
[10:53:51.696]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:53:51.697]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[10:53:51.697]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[10:53:51.698]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:53:51.699]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:53:51.699]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:53:51.700]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:53:51.700]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:53:51.701]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:53:51.701]    </block>
[10:53:51.701]  </sequence>
[10:53:51.701]  
[10:53:58.519]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:53:58.519]  
[10:53:58.519]  <debugvars>
[10:53:58.519]    // Pre-defined
[10:53:58.519]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:53:58.519]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:53:58.519]    __dp=0x00000000
[10:53:58.519]    __ap=0x00000000
[10:53:58.519]    __traceout=0x00000000      (Trace Disabled)
[10:53:58.519]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:53:58.519]    __FlashAddr=0x00000000
[10:53:58.519]    __FlashLen=0x00000000
[10:53:58.519]    __FlashArg=0x00000000
[10:53:58.519]    __FlashOp=0x00000000
[10:53:58.519]    __Result=0x00000000
[10:53:58.519]    
[10:53:58.519]    // User-defined
[10:53:58.525]    DbgMCU_CR=0x00000007
[10:53:58.525]    DbgMCU_APB1_Fz=0x00000000
[10:53:58.525]    DbgMCU_APB2_Fz=0x00000000
[10:53:58.525]    DoOptionByteLoading=0x00000000
[10:53:58.525]  </debugvars>
[10:53:58.525]  
[10:53:58.526]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:53:58.526]    <block atomic="false" info="">
[10:53:58.526]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:53:58.526]        // -> [connectionFlash <= 0x00000001]
[10:53:58.526]      __var FLASH_BASE = 0x40022000 ;
[10:53:58.527]        // -> [FLASH_BASE <= 0x40022000]
[10:53:58.527]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:53:58.527]        // -> [FLASH_CR <= 0x40022004]
[10:53:58.527]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:53:58.527]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:53:58.527]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:53:58.528]        // -> [LOCK_BIT <= 0x00000001]
[10:53:58.528]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:53:58.528]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:53:58.528]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:53:58.528]        // -> [FLASH_KEYR <= 0x4002200C]
[10:53:58.528]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:53:58.528]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:53:58.530]      __var FLASH_KEY2 = 0x02030405 ;
[10:53:58.530]        // -> [FLASH_KEY2 <= 0x02030405]
[10:53:58.530]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:53:58.530]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:53:58.530]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:53:58.530]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:53:58.530]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:53:58.530]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:53:58.530]      __var FLASH_CR_Value = 0 ;
[10:53:58.530]        // -> [FLASH_CR_Value <= 0x00000000]
[10:53:58.530]      __var DoDebugPortStop = 1 ;
[10:53:58.530]        // -> [DoDebugPortStop <= 0x00000001]
[10:53:58.530]      __var DP_CTRL_STAT = 0x4 ;
[10:53:58.530]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:53:58.530]      __var DP_SELECT = 0x8 ;
[10:53:58.530]        // -> [DP_SELECT <= 0x00000008]
[10:53:58.530]    </block>
[10:53:58.530]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:53:58.530]      // if-block "connectionFlash && DoOptionByteLoading"
[10:53:58.530]        // =>  FALSE
[10:53:58.530]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:53:58.530]    </control>
[10:53:58.535]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:53:58.535]      // if-block "DoDebugPortStop"
[10:53:58.535]        // =>  TRUE
[10:53:58.535]      <block atomic="false" info="">
[10:53:58.535]        WriteDP(DP_SELECT, 0x00000000);
[10:53:58.544]  
[10:53:58.544]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[10:53:58.544]  
[10:53:58.567]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:53:58.568]      </block>
[10:53:58.569]      // end if-block "DoDebugPortStop"
[10:53:58.569]    </control>
[10:53:58.569]  </sequence>
[10:53:58.570]  
[10:54:12.697]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:54:12.697]  
[10:54:12.702]  <debugvars>
[10:54:12.702]    // Pre-defined
[10:54:12.702]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:54:12.702]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:54:12.702]    __dp=0x00000000
[10:54:12.702]    __ap=0x00000000
[10:54:12.702]    __traceout=0x00000000      (Trace Disabled)
[10:54:12.702]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:54:12.702]    __FlashAddr=0x00000000
[10:54:12.702]    __FlashLen=0x00000000
[10:54:12.702]    __FlashArg=0x00000000
[10:54:12.702]    __FlashOp=0x00000000
[10:54:12.702]    __Result=0x00000000
[10:54:12.702]    
[10:54:12.702]    // User-defined
[10:54:12.702]    DbgMCU_CR=0x00000007
[10:54:12.702]    DbgMCU_APB1_Fz=0x00000000
[10:54:12.702]    DbgMCU_APB2_Fz=0x00000000
[10:54:12.702]    DoOptionByteLoading=0x00000000
[10:54:12.702]  </debugvars>
[10:54:12.702]  
[10:54:12.702]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:54:12.702]    <block atomic="false" info="">
[10:54:12.707]      Sequence("CheckID");
[10:54:12.707]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:54:12.707]          <block atomic="false" info="">
[10:54:12.707]            __var pidr1 = 0;
[10:54:12.707]              // -> [pidr1 <= 0x00000000]
[10:54:12.707]            __var pidr2 = 0;
[10:54:12.707]              // -> [pidr2 <= 0x00000000]
[10:54:12.707]            __var jep106id = 0;
[10:54:12.707]              // -> [jep106id <= 0x00000000]
[10:54:12.707]            __var ROMTableBase = 0;
[10:54:12.707]              // -> [ROMTableBase <= 0x00000000]
[10:54:12.707]            __ap = 0;      // AHB-AP
[10:54:12.707]              // -> [__ap <= 0x00000000]
[10:54:12.707]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:54:12.707]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:54:12.707]              // -> [ROMTableBase <= 0xF0000000]
[10:54:12.707]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:54:12.712]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:54:12.712]              // -> [pidr1 <= 0x00000004]
[10:54:12.712]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:54:12.714]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:54:12.714]              // -> [pidr2 <= 0x0000000A]
[10:54:12.714]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:54:12.714]              // -> [jep106id <= 0x00000020]
[10:54:12.714]          </block>
[10:54:12.714]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:54:12.714]            // if-block "jep106id != 0x20"
[10:54:12.714]              // =>  FALSE
[10:54:12.714]            // skip if-block "jep106id != 0x20"
[10:54:12.718]          </control>
[10:54:12.718]        </sequence>
[10:54:12.718]    </block>
[10:54:12.718]  </sequence>
[10:54:12.718]  
[10:54:12.730]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:54:12.730]  
[10:54:12.730]  <debugvars>
[10:54:12.730]    // Pre-defined
[10:54:12.730]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:54:12.730]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:54:12.730]    __dp=0x00000000
[10:54:12.730]    __ap=0x00000000
[10:54:12.730]    __traceout=0x00000000      (Trace Disabled)
[10:54:12.730]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:54:12.733]    __FlashAddr=0x00000000
[10:54:12.733]    __FlashLen=0x00000000
[10:54:12.733]    __FlashArg=0x00000000
[10:54:12.733]    __FlashOp=0x00000000
[10:54:12.733]    __Result=0x00000000
[10:54:12.733]    
[10:54:12.733]    // User-defined
[10:54:12.733]    DbgMCU_CR=0x00000007
[10:54:12.733]    DbgMCU_APB1_Fz=0x00000000
[10:54:12.733]    DbgMCU_APB2_Fz=0x00000000
[10:54:12.733]    DoOptionByteLoading=0x00000000
[10:54:12.733]  </debugvars>
[10:54:12.733]  
[10:54:12.733]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:54:12.733]    <block atomic="false" info="">
[10:54:12.733]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:54:12.733]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:54:12.733]    </block>
[10:54:12.733]    <block atomic="false" info="DbgMCU registers">
[10:54:12.733]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:54:12.738]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[10:54:12.738]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[10:54:12.738]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:54:12.738]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:54:12.738]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:54:12.738]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:54:12.738]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:54:12.738]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:54:12.738]    </block>
[10:54:12.738]  </sequence>
[10:54:12.738]  
[10:54:14.963]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:54:14.963]  
[10:54:14.963]  <debugvars>
[10:54:14.963]    // Pre-defined
[10:54:14.963]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:54:14.963]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:54:14.963]    __dp=0x00000000
[10:54:14.965]    __ap=0x00000000
[10:54:14.965]    __traceout=0x00000000      (Trace Disabled)
[10:54:14.965]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:54:14.965]    __FlashAddr=0x00000000
[10:54:14.966]    __FlashLen=0x00000000
[10:54:14.966]    __FlashArg=0x00000000
[10:54:14.966]    __FlashOp=0x00000000
[10:54:14.966]    __Result=0x00000000
[10:54:14.966]    
[10:54:14.966]    // User-defined
[10:54:14.966]    DbgMCU_CR=0x00000007
[10:54:14.966]    DbgMCU_APB1_Fz=0x00000000
[10:54:14.966]    DbgMCU_APB2_Fz=0x00000000
[10:54:14.966]    DoOptionByteLoading=0x00000000
[10:54:14.966]  </debugvars>
[10:54:14.966]  
[10:54:14.966]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:54:14.966]    <block atomic="false" info="">
[10:54:14.966]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:54:14.966]        // -> [connectionFlash <= 0x00000001]
[10:54:14.966]      __var FLASH_BASE = 0x40022000 ;
[10:54:14.966]        // -> [FLASH_BASE <= 0x40022000]
[10:54:14.966]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:54:14.971]        // -> [FLASH_CR <= 0x40022004]
[10:54:14.971]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:54:14.971]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:54:14.971]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:54:14.971]        // -> [LOCK_BIT <= 0x00000001]
[10:54:14.971]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:54:14.973]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:54:14.973]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:54:14.974]        // -> [FLASH_KEYR <= 0x4002200C]
[10:54:14.974]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:54:14.974]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:54:14.975]      __var FLASH_KEY2 = 0x02030405 ;
[10:54:14.975]        // -> [FLASH_KEY2 <= 0x02030405]
[10:54:14.975]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:54:14.975]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:54:14.976]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:54:14.976]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:54:14.976]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:54:14.976]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:54:14.976]      __var FLASH_CR_Value = 0 ;
[10:54:14.976]        // -> [FLASH_CR_Value <= 0x00000000]
[10:54:14.977]      __var DoDebugPortStop = 1 ;
[10:54:14.977]        // -> [DoDebugPortStop <= 0x00000001]
[10:54:14.977]      __var DP_CTRL_STAT = 0x4 ;
[10:54:14.977]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:54:14.977]      __var DP_SELECT = 0x8 ;
[10:54:14.977]        // -> [DP_SELECT <= 0x00000008]
[10:54:14.978]    </block>
[10:54:14.978]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:54:14.978]      // if-block "connectionFlash && DoOptionByteLoading"
[10:54:14.978]        // =>  FALSE
[10:54:14.978]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:54:14.979]    </control>
[10:54:14.980]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:54:14.980]      // if-block "DoDebugPortStop"
[10:54:14.980]        // =>  TRUE
[10:54:14.980]      <block atomic="false" info="">
[10:54:14.981]        WriteDP(DP_SELECT, 0x00000000);
[10:54:14.988]  
[10:54:14.988]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[10:54:14.988]  
[10:54:15.011]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:54:15.012]      </block>
[10:54:15.012]      // end if-block "DoDebugPortStop"
[10:54:15.012]    </control>
[10:54:15.012]  </sequence>
[10:54:15.012]  
[10:54:18.346]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[10:54:18.346]  
[10:54:18.346]  <debugvars>
[10:54:18.346]    // Pre-defined
[10:54:18.346]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:54:18.346]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:54:18.346]    __dp=0x00000000
[10:54:18.346]    __ap=0x00000000
[10:54:18.346]    __traceout=0x00000000      (Trace Disabled)
[10:54:18.351]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:54:18.351]    __FlashAddr=0x00000000
[10:54:18.351]    __FlashLen=0x00000000
[10:54:18.351]    __FlashArg=0x00000000
[10:54:18.351]    __FlashOp=0x00000000
[10:54:18.351]    __Result=0x00000000
[10:54:18.351]    
[10:54:18.351]    // User-defined
[10:54:18.351]    DbgMCU_CR=0x00000007
[10:54:18.351]    DbgMCU_APB1_Fz=0x00000000
[10:54:18.351]    DbgMCU_APB2_Fz=0x00000000
[10:54:18.351]    DoOptionByteLoading=0x00000000
[10:54:18.351]  </debugvars>
[10:54:18.351]  
[10:54:18.351]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[10:54:18.351]    <block atomic="false" info="">
[10:54:18.351]      Sequence("CheckID");
[10:54:18.351]        <sequence name="CheckID" Pname="" disable="false" info="">
[10:54:18.351]          <block atomic="false" info="">
[10:54:18.351]            __var pidr1 = 0;
[10:54:18.351]              // -> [pidr1 <= 0x00000000]
[10:54:18.351]            __var pidr2 = 0;
[10:54:18.351]              // -> [pidr2 <= 0x00000000]
[10:54:18.351]            __var jep106id = 0;
[10:54:18.351]              // -> [jep106id <= 0x00000000]
[10:54:18.357]            __var ROMTableBase = 0;
[10:54:18.357]              // -> [ROMTableBase <= 0x00000000]
[10:54:18.357]            __ap = 0;      // AHB-AP
[10:54:18.357]              // -> [__ap <= 0x00000000]
[10:54:18.357]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[10:54:18.357]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[10:54:18.357]              // -> [ROMTableBase <= 0xF0000000]
[10:54:18.357]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[10:54:18.359]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[10:54:18.359]              // -> [pidr1 <= 0x00000004]
[10:54:18.359]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[10:54:18.361]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[10:54:18.361]              // -> [pidr2 <= 0x0000000A]
[10:54:18.361]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[10:54:18.361]              // -> [jep106id <= 0x00000020]
[10:54:18.361]          </block>
[10:54:18.362]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[10:54:18.362]            // if-block "jep106id != 0x20"
[10:54:18.362]              // =>  FALSE
[10:54:18.362]            // skip if-block "jep106id != 0x20"
[10:54:18.362]          </control>
[10:54:18.362]        </sequence>
[10:54:18.362]    </block>
[10:54:18.362]  </sequence>
[10:54:18.362]  
[10:54:18.372]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[10:54:18.372]  
[10:54:18.372]  <debugvars>
[10:54:18.372]    // Pre-defined
[10:54:18.372]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:54:18.372]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:54:18.372]    __dp=0x00000000
[10:54:18.372]    __ap=0x00000000
[10:54:18.372]    __traceout=0x00000000      (Trace Disabled)
[10:54:18.372]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:54:18.372]    __FlashAddr=0x00000000
[10:54:18.372]    __FlashLen=0x00000000
[10:54:18.372]    __FlashArg=0x00000000
[10:54:18.372]    __FlashOp=0x00000000
[10:54:18.377]    __Result=0x00000000
[10:54:18.377]    
[10:54:18.377]    // User-defined
[10:54:18.377]    DbgMCU_CR=0x00000007
[10:54:18.377]    DbgMCU_APB1_Fz=0x00000000
[10:54:18.377]    DbgMCU_APB2_Fz=0x00000000
[10:54:18.377]    DoOptionByteLoading=0x00000000
[10:54:18.377]  </debugvars>
[10:54:18.377]  
[10:54:18.377]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[10:54:18.377]    <block atomic="false" info="">
[10:54:18.377]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[10:54:18.377]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[10:54:18.377]    </block>
[10:54:18.377]    <block atomic="false" info="DbgMCU registers">
[10:54:18.377]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[10:54:18.377]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[10:54:18.382]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[10:54:18.382]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[10:54:18.382]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[10:54:18.382]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[10:54:18.382]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:54:18.382]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[10:54:18.382]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[10:54:18.382]    </block>
[10:54:18.382]  </sequence>
[10:54:18.382]  
[10:54:25.710]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[10:54:25.710]  
[10:54:25.710]  <debugvars>
[10:54:25.710]    // Pre-defined
[10:54:25.710]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[10:54:25.710]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[10:54:25.710]    __dp=0x00000000
[10:54:25.714]    __ap=0x00000000
[10:54:25.715]    __traceout=0x00000000      (Trace Disabled)
[10:54:25.715]    __errorcontrol=0x00000000  (Skip Errors="False")
[10:54:25.716]    __FlashAddr=0x00000000
[10:54:25.716]    __FlashLen=0x00000000
[10:54:25.716]    __FlashArg=0x00000000
[10:54:25.716]    __FlashOp=0x00000000
[10:54:25.718]    __Result=0x00000000
[10:54:25.718]    
[10:54:25.718]    // User-defined
[10:54:25.718]    DbgMCU_CR=0x00000007
[10:54:25.719]    DbgMCU_APB1_Fz=0x00000000
[10:54:25.719]    DbgMCU_APB2_Fz=0x00000000
[10:54:25.720]    DoOptionByteLoading=0x00000000
[10:54:25.720]  </debugvars>
[10:54:25.721]  
[10:54:25.721]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[10:54:25.721]    <block atomic="false" info="">
[10:54:25.722]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[10:54:25.723]        // -> [connectionFlash <= 0x00000001]
[10:54:25.723]      __var FLASH_BASE = 0x40022000 ;
[10:54:25.724]        // -> [FLASH_BASE <= 0x40022000]
[10:54:25.724]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[10:54:25.724]        // -> [FLASH_CR <= 0x40022004]
[10:54:25.725]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[10:54:25.725]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[10:54:25.725]      __var LOCK_BIT = ( 1 << 0 ) ;
[10:54:25.726]        // -> [LOCK_BIT <= 0x00000001]
[10:54:25.726]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[10:54:25.726]        // -> [OPTLOCK_BIT <= 0x00000004]
[10:54:25.726]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[10:54:25.726]        // -> [FLASH_KEYR <= 0x4002200C]
[10:54:25.727]      __var FLASH_KEY1 = 0x89ABCDEF ;
[10:54:25.727]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[10:54:25.727]      __var FLASH_KEY2 = 0x02030405 ;
[10:54:25.727]        // -> [FLASH_KEY2 <= 0x02030405]
[10:54:25.727]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[10:54:25.727]        // -> [FLASH_OPTKEYR <= 0x40022014]
[10:54:25.728]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[10:54:25.728]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[10:54:25.728]      __var FLASH_OPTKEY2 = 0x24252627 ;
[10:54:25.728]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[10:54:25.728]      __var FLASH_CR_Value = 0 ;
[10:54:25.729]        // -> [FLASH_CR_Value <= 0x00000000]
[10:54:25.729]      __var DoDebugPortStop = 1 ;
[10:54:25.729]        // -> [DoDebugPortStop <= 0x00000001]
[10:54:25.730]      __var DP_CTRL_STAT = 0x4 ;
[10:54:25.730]        // -> [DP_CTRL_STAT <= 0x00000004]
[10:54:25.730]      __var DP_SELECT = 0x8 ;
[10:54:25.730]        // -> [DP_SELECT <= 0x00000008]
[10:54:25.730]    </block>
[10:54:25.731]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[10:54:25.731]      // if-block "connectionFlash && DoOptionByteLoading"
[10:54:25.731]        // =>  FALSE
[10:54:25.731]      // skip if-block "connectionFlash && DoOptionByteLoading"
[10:54:25.731]    </control>
[10:54:25.732]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[10:54:25.732]      // if-block "DoDebugPortStop"
[10:54:25.732]        // =>  TRUE
[10:54:25.732]      <block atomic="false" info="">
[10:54:25.732]        WriteDP(DP_SELECT, 0x00000000);
[10:54:25.733]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[10:54:25.733]        WriteDP(DP_CTRL_STAT, 0x00000000);
[10:54:25.734]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[10:54:25.734]      </block>
[10:54:25.734]      // end if-block "DoDebugPortStop"
[10:54:25.734]    </control>
[10:54:25.734]  </sequence>
[10:54:25.735]  
[11:04:55.094]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:04:55.094]  
[11:04:55.094]  <debugvars>
[11:04:55.094]    // Pre-defined
[11:04:55.094]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:04:55.094]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:04:55.094]    __dp=0x00000000
[11:04:55.094]    __ap=0x00000000
[11:04:55.094]    __traceout=0x00000000      (Trace Disabled)
[11:04:55.094]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:04:55.094]    __FlashAddr=0x00000000
[11:04:55.094]    __FlashLen=0x00000000
[11:04:55.094]    __FlashArg=0x00000000
[11:04:55.094]    __FlashOp=0x00000000
[11:04:55.094]    __Result=0x00000000
[11:04:55.097]    
[11:04:55.097]    // User-defined
[11:04:55.097]    DbgMCU_CR=0x00000007
[11:04:55.097]    DbgMCU_APB1_Fz=0x00000000
[11:04:55.097]    DbgMCU_APB2_Fz=0x00000000
[11:04:55.097]    DoOptionByteLoading=0x00000000
[11:04:55.097]  </debugvars>
[11:04:55.097]  
[11:04:55.097]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:04:55.097]    <block atomic="false" info="">
[11:04:55.099]      Sequence("CheckID");
[11:04:55.099]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:04:55.099]          <block atomic="false" info="">
[11:04:55.099]            __var pidr1 = 0;
[11:04:55.099]              // -> [pidr1 <= 0x00000000]
[11:04:55.099]            __var pidr2 = 0;
[11:04:55.099]              // -> [pidr2 <= 0x00000000]
[11:04:55.099]            __var jep106id = 0;
[11:04:55.099]              // -> [jep106id <= 0x00000000]
[11:04:55.099]            __var ROMTableBase = 0;
[11:04:55.099]              // -> [ROMTableBase <= 0x00000000]
[11:04:55.099]            __ap = 0;      // AHB-AP
[11:04:55.099]              // -> [__ap <= 0x00000000]
[11:04:55.099]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:04:55.099]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:04:55.099]              // -> [ROMTableBase <= 0xF0000000]
[11:04:55.099]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:04:55.104]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:04:55.104]              // -> [pidr1 <= 0x00000004]
[11:04:55.104]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:04:55.104]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:04:55.104]              // -> [pidr2 <= 0x0000000A]
[11:04:55.104]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:04:55.106]              // -> [jep106id <= 0x00000020]
[11:04:55.106]          </block>
[11:04:55.106]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:04:55.106]            // if-block "jep106id != 0x20"
[11:04:55.106]              // =>  FALSE
[11:04:55.106]            // skip if-block "jep106id != 0x20"
[11:04:55.106]          </control>
[11:04:55.106]        </sequence>
[11:04:55.106]    </block>
[11:04:55.106]  </sequence>
[11:04:55.106]  
[11:04:55.119]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:04:55.119]  
[11:04:55.136]  <debugvars>
[11:04:55.136]    // Pre-defined
[11:04:55.137]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:04:55.137]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:04:55.137]    __dp=0x00000000
[11:04:55.137]    __ap=0x00000000
[11:04:55.137]    __traceout=0x00000000      (Trace Disabled)
[11:04:55.137]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:04:55.137]    __FlashAddr=0x00000000
[11:04:55.137]    __FlashLen=0x00000000
[11:04:55.137]    __FlashArg=0x00000000
[11:04:55.139]    __FlashOp=0x00000000
[11:04:55.139]    __Result=0x00000000
[11:04:55.139]    
[11:04:55.139]    // User-defined
[11:04:55.139]    DbgMCU_CR=0x00000007
[11:04:55.139]    DbgMCU_APB1_Fz=0x00000000
[11:04:55.139]    DbgMCU_APB2_Fz=0x00000000
[11:04:55.139]    DoOptionByteLoading=0x00000000
[11:04:55.139]  </debugvars>
[11:04:55.139]  
[11:04:55.139]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:04:55.139]    <block atomic="false" info="">
[11:04:55.141]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:04:55.141]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:04:55.141]    </block>
[11:04:55.141]    <block atomic="false" info="DbgMCU registers">
[11:04:55.141]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:04:55.143]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:04:55.144]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:04:55.144]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:04:55.144]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:04:55.144]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:04:55.146]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:04:55.146]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:04:55.146]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:04:55.146]    </block>
[11:04:55.148]  </sequence>
[11:04:55.148]  
[11:05:02.142]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:05:02.142]  
[11:05:02.143]  <debugvars>
[11:05:02.143]    // Pre-defined
[11:05:02.143]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:05:02.144]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:05:02.144]    __dp=0x00000000
[11:05:02.144]    __ap=0x00000000
[11:05:02.145]    __traceout=0x00000000      (Trace Disabled)
[11:05:02.145]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:05:02.145]    __FlashAddr=0x00000000
[11:05:02.145]    __FlashLen=0x00000000
[11:05:02.145]    __FlashArg=0x00000000
[11:05:02.145]    __FlashOp=0x00000000
[11:05:02.145]    __Result=0x00000000
[11:05:02.145]    
[11:05:02.145]    // User-defined
[11:05:02.145]    DbgMCU_CR=0x00000007
[11:05:02.145]    DbgMCU_APB1_Fz=0x00000000
[11:05:02.145]    DbgMCU_APB2_Fz=0x00000000
[11:05:02.148]    DoOptionByteLoading=0x00000000
[11:05:02.148]  </debugvars>
[11:05:02.148]  
[11:05:02.148]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:05:02.148]    <block atomic="false" info="">
[11:05:02.150]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:05:02.150]        // -> [connectionFlash <= 0x00000001]
[11:05:02.150]      __var FLASH_BASE = 0x40022000 ;
[11:05:02.150]        // -> [FLASH_BASE <= 0x40022000]
[11:05:02.150]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:05:02.150]        // -> [FLASH_CR <= 0x40022004]
[11:05:02.150]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:05:02.152]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:05:02.152]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:05:02.152]        // -> [LOCK_BIT <= 0x00000001]
[11:05:02.152]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:05:02.152]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:05:02.153]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:05:02.153]        // -> [FLASH_KEYR <= 0x4002200C]
[11:05:02.153]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:05:02.153]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:05:02.153]      __var FLASH_KEY2 = 0x02030405 ;
[11:05:02.153]        // -> [FLASH_KEY2 <= 0x02030405]
[11:05:02.153]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:05:02.153]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:05:02.153]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:05:02.155]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:05:02.155]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:05:02.155]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:05:02.155]      __var FLASH_CR_Value = 0 ;
[11:05:02.155]        // -> [FLASH_CR_Value <= 0x00000000]
[11:05:02.155]      __var DoDebugPortStop = 1 ;
[11:05:02.155]        // -> [DoDebugPortStop <= 0x00000001]
[11:05:02.155]      __var DP_CTRL_STAT = 0x4 ;
[11:05:02.155]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:05:02.157]      __var DP_SELECT = 0x8 ;
[11:05:02.157]        // -> [DP_SELECT <= 0x00000008]
[11:05:02.157]    </block>
[11:05:02.157]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:05:02.157]      // if-block "connectionFlash && DoOptionByteLoading"
[11:05:02.158]        // =>  FALSE
[11:05:02.158]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:05:02.158]    </control>
[11:05:02.158]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:05:02.159]      // if-block "DoDebugPortStop"
[11:05:02.159]        // =>  TRUE
[11:05:02.159]      <block atomic="false" info="">
[11:05:02.159]        WriteDP(DP_SELECT, 0x00000000);
[11:05:02.160]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:05:02.161]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:05:02.161]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:05:02.161]      </block>
[11:05:02.162]      // end if-block "DoDebugPortStop"
[11:05:02.162]    </control>
[11:05:02.162]  </sequence>
[11:05:02.163]  
[11:05:07.324]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:05:07.324]  
[11:05:07.324]  <debugvars>
[11:05:07.324]    // Pre-defined
[11:05:07.325]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:05:07.325]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:05:07.325]    __dp=0x00000000
[11:05:07.326]    __ap=0x00000000
[11:05:07.326]    __traceout=0x00000000      (Trace Disabled)
[11:05:07.326]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:05:07.326]    __FlashAddr=0x00000000
[11:05:07.326]    __FlashLen=0x00000000
[11:05:07.326]    __FlashArg=0x00000000
[11:05:07.326]    __FlashOp=0x00000000
[11:05:07.326]    __Result=0x00000000
[11:05:07.326]    
[11:05:07.326]    // User-defined
[11:05:07.326]    DbgMCU_CR=0x00000007
[11:05:07.326]    DbgMCU_APB1_Fz=0x00000000
[11:05:07.326]    DbgMCU_APB2_Fz=0x00000000
[11:05:07.326]    DoOptionByteLoading=0x00000000
[11:05:07.326]  </debugvars>
[11:05:07.326]  
[11:05:07.329]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:05:07.329]    <block atomic="false" info="">
[11:05:07.329]      Sequence("CheckID");
[11:05:07.329]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:05:07.329]          <block atomic="false" info="">
[11:05:07.329]            __var pidr1 = 0;
[11:05:07.329]              // -> [pidr1 <= 0x00000000]
[11:05:07.329]            __var pidr2 = 0;
[11:05:07.329]              // -> [pidr2 <= 0x00000000]
[11:05:07.329]            __var jep106id = 0;
[11:05:07.329]              // -> [jep106id <= 0x00000000]
[11:05:07.329]            __var ROMTableBase = 0;
[11:05:07.329]              // -> [ROMTableBase <= 0x00000000]
[11:05:07.329]            __ap = 0;      // AHB-AP
[11:05:07.329]              // -> [__ap <= 0x00000000]
[11:05:07.329]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:05:07.329]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:05:07.329]              // -> [ROMTableBase <= 0xF0000000]
[11:05:07.329]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:05:07.334]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:05:07.335]              // -> [pidr1 <= 0x00000004]
[11:05:07.335]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:05:07.336]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:05:07.337]              // -> [pidr2 <= 0x0000000A]
[11:05:07.337]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:05:07.337]              // -> [jep106id <= 0x00000020]
[11:05:07.338]          </block>
[11:05:07.338]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:05:07.338]            // if-block "jep106id != 0x20"
[11:05:07.338]              // =>  FALSE
[11:05:07.339]            // skip if-block "jep106id != 0x20"
[11:05:07.339]          </control>
[11:05:07.339]        </sequence>
[11:05:07.340]    </block>
[11:05:07.340]  </sequence>
[11:05:07.340]  
[11:05:07.351]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:05:07.351]  
[11:05:07.352]  <debugvars>
[11:05:07.352]    // Pre-defined
[11:05:07.352]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:05:07.353]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:05:07.353]    __dp=0x00000000
[11:05:07.353]    __ap=0x00000000
[11:05:07.353]    __traceout=0x00000000      (Trace Disabled)
[11:05:07.354]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:05:07.354]    __FlashAddr=0x00000000
[11:05:07.354]    __FlashLen=0x00000000
[11:05:07.354]    __FlashArg=0x00000000
[11:05:07.354]    __FlashOp=0x00000000
[11:05:07.355]    __Result=0x00000000
[11:05:07.355]    
[11:05:07.355]    // User-defined
[11:05:07.355]    DbgMCU_CR=0x00000007
[11:05:07.355]    DbgMCU_APB1_Fz=0x00000000
[11:05:07.355]    DbgMCU_APB2_Fz=0x00000000
[11:05:07.355]    DoOptionByteLoading=0x00000000
[11:05:07.356]  </debugvars>
[11:05:07.356]  
[11:05:07.356]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:05:07.356]    <block atomic="false" info="">
[11:05:07.356]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:05:07.357]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:05:07.357]    </block>
[11:05:07.358]    <block atomic="false" info="DbgMCU registers">
[11:05:07.358]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:05:07.359]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[11:05:07.359]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[11:05:07.359]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:05:07.361]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:05:07.361]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:05:07.362]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:05:07.362]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:05:07.364]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:05:07.364]    </block>
[11:05:07.364]  </sequence>
[11:05:07.364]  
[11:05:14.766]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:05:14.766]  
[11:05:14.767]  <debugvars>
[11:05:14.768]    // Pre-defined
[11:05:14.769]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:05:14.770]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:05:14.770]    __dp=0x00000000
[11:05:14.771]    __ap=0x00000000
[11:05:14.772]    __traceout=0x00000000      (Trace Disabled)
[11:05:14.772]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:05:14.774]    __FlashAddr=0x00000000
[11:05:14.775]    __FlashLen=0x00000000
[11:05:14.776]    __FlashArg=0x00000000
[11:05:14.776]    __FlashOp=0x00000000
[11:05:14.777]    __Result=0x00000000
[11:05:14.778]    
[11:05:14.778]    // User-defined
[11:05:14.778]    DbgMCU_CR=0x00000007
[11:05:14.779]    DbgMCU_APB1_Fz=0x00000000
[11:05:14.780]    DbgMCU_APB2_Fz=0x00000000
[11:05:14.780]    DoOptionByteLoading=0x00000000
[11:05:14.781]  </debugvars>
[11:05:14.781]  
[11:05:14.782]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:05:14.782]    <block atomic="false" info="">
[11:05:14.783]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:05:14.783]        // -> [connectionFlash <= 0x00000001]
[11:05:14.784]      __var FLASH_BASE = 0x40022000 ;
[11:05:14.784]        // -> [FLASH_BASE <= 0x40022000]
[11:05:14.784]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:05:14.784]        // -> [FLASH_CR <= 0x40022004]
[11:05:14.784]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:05:14.784]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:05:14.784]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:05:14.784]        // -> [LOCK_BIT <= 0x00000001]
[11:05:14.784]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:05:14.784]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:05:14.784]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:05:14.784]        // -> [FLASH_KEYR <= 0x4002200C]
[11:05:14.784]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:05:14.789]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:05:14.789]      __var FLASH_KEY2 = 0x02030405 ;
[11:05:14.789]        // -> [FLASH_KEY2 <= 0x02030405]
[11:05:14.791]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:05:14.791]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:05:14.792]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:05:14.792]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:05:14.793]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:05:14.794]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:05:14.794]      __var FLASH_CR_Value = 0 ;
[11:05:14.795]        // -> [FLASH_CR_Value <= 0x00000000]
[11:05:14.795]      __var DoDebugPortStop = 1 ;
[11:05:14.795]        // -> [DoDebugPortStop <= 0x00000001]
[11:05:14.796]      __var DP_CTRL_STAT = 0x4 ;
[11:05:14.797]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:05:14.797]      __var DP_SELECT = 0x8 ;
[11:05:14.798]        // -> [DP_SELECT <= 0x00000008]
[11:05:14.798]    </block>
[11:05:14.799]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:05:14.799]      // if-block "connectionFlash && DoOptionByteLoading"
[11:05:14.800]        // =>  FALSE
[11:05:14.800]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:05:14.800]    </control>
[11:05:14.801]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:05:14.801]      // if-block "DoDebugPortStop"
[11:05:14.802]        // =>  TRUE
[11:05:14.803]      <block atomic="false" info="">
[11:05:14.803]        WriteDP(DP_SELECT, 0x00000000);
[11:05:14.804]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:05:14.804]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:05:14.805]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:05:14.805]      </block>
[11:05:14.806]      // end if-block "DoDebugPortStop"
[11:05:14.806]    </control>
[11:05:14.806]  </sequence>
[11:05:14.807]  
[11:56:51.170]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:56:51.170]  
[11:56:51.187]  <debugvars>
[11:56:51.187]    // Pre-defined
[11:56:51.187]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:56:51.187]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:56:51.189]    __dp=0x00000000
[11:56:51.189]    __ap=0x00000000
[11:56:51.189]    __traceout=0x00000000      (Trace Disabled)
[11:56:51.190]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:56:51.190]    __FlashAddr=0x00000000
[11:56:51.190]    __FlashLen=0x00000000
[11:56:51.191]    __FlashArg=0x00000000
[11:56:51.191]    __FlashOp=0x00000000
[11:56:51.191]    __Result=0x00000000
[11:56:51.192]    
[11:56:51.192]    // User-defined
[11:56:51.192]    DbgMCU_CR=0x00000007
[11:56:51.192]    DbgMCU_APB1_Fz=0x00000000
[11:56:51.193]    DbgMCU_APB2_Fz=0x00000000
[11:56:51.193]    DoOptionByteLoading=0x00000000
[11:56:51.193]  </debugvars>
[11:56:51.194]  
[11:56:51.194]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:56:51.194]    <block atomic="false" info="">
[11:56:51.195]      Sequence("CheckID");
[11:56:51.195]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:56:51.195]          <block atomic="false" info="">
[11:56:51.196]            __var pidr1 = 0;
[11:56:51.197]              // -> [pidr1 <= 0x00000000]
[11:56:51.197]            __var pidr2 = 0;
[11:56:51.197]              // -> [pidr2 <= 0x00000000]
[11:56:51.198]            __var jep106id = 0;
[11:56:51.198]              // -> [jep106id <= 0x00000000]
[11:56:51.198]            __var ROMTableBase = 0;
[11:56:51.199]              // -> [ROMTableBase <= 0x00000000]
[11:56:51.199]            __ap = 0;      // AHB-AP
[11:56:51.200]              // -> [__ap <= 0x00000000]
[11:56:51.200]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:56:51.201]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:56:51.202]              // -> [ROMTableBase <= 0xF0000000]
[11:56:51.202]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:56:51.203]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:56:51.204]              // -> [pidr1 <= 0x00000004]
[11:56:51.204]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:56:51.205]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:56:51.206]              // -> [pidr2 <= 0x0000000A]
[11:56:51.206]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:56:51.206]              // -> [jep106id <= 0x00000020]
[11:56:51.207]          </block>
[11:56:51.207]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:56:51.207]            // if-block "jep106id != 0x20"
[11:56:51.208]              // =>  FALSE
[11:56:51.208]            // skip if-block "jep106id != 0x20"
[11:56:51.208]          </control>
[11:56:51.209]        </sequence>
[11:56:51.209]    </block>
[11:56:51.209]  </sequence>
[11:56:51.210]  
[11:56:51.223]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:56:51.223]  
[11:56:51.224]  <debugvars>
[11:56:51.224]    // Pre-defined
[11:56:51.224]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:56:51.226]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:56:51.226]    __dp=0x00000000
[11:56:51.226]    __ap=0x00000000
[11:56:51.226]    __traceout=0x00000000      (Trace Disabled)
[11:56:51.227]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:56:51.228]    __FlashAddr=0x00000000
[11:56:51.228]    __FlashLen=0x00000000
[11:56:51.228]    __FlashArg=0x00000000
[11:56:51.229]    __FlashOp=0x00000000
[11:56:51.229]    __Result=0x00000000
[11:56:51.229]    
[11:56:51.229]    // User-defined
[11:56:51.229]    DbgMCU_CR=0x00000007
[11:56:51.229]    DbgMCU_APB1_Fz=0x00000000
[11:56:51.230]    DbgMCU_APB2_Fz=0x00000000
[11:56:51.230]    DoOptionByteLoading=0x00000000
[11:56:51.230]  </debugvars>
[11:56:51.230]  
[11:56:51.230]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:56:51.230]    <block atomic="false" info="">
[11:56:51.231]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:56:51.231]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:56:51.232]    </block>
[11:56:51.232]    <block atomic="false" info="DbgMCU registers">
[11:56:51.232]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:56:51.233]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:56:51.233]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:56:51.234]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:56:51.234]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:56:51.235]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:56:51.235]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:56:51.235]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:56:51.236]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:56:51.236]    </block>
[11:56:51.236]  </sequence>
[11:56:51.236]  
[11:56:58.499]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:56:58.499]  
[11:56:58.502]  <debugvars>
[11:56:58.502]    // Pre-defined
[11:56:58.502]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:56:58.502]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:56:58.502]    __dp=0x00000000
[11:56:58.502]    __ap=0x00000000
[11:56:58.502]    __traceout=0x00000000      (Trace Disabled)
[11:56:58.504]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:56:58.504]    __FlashAddr=0x00000000
[11:56:58.504]    __FlashLen=0x00000000
[11:56:58.504]    __FlashArg=0x00000000
[11:56:58.504]    __FlashOp=0x00000000
[11:56:58.504]    __Result=0x00000000
[11:56:58.504]    
[11:56:58.504]    // User-defined
[11:56:58.504]    DbgMCU_CR=0x00000007
[11:56:58.504]    DbgMCU_APB1_Fz=0x00000000
[11:56:58.504]    DbgMCU_APB2_Fz=0x00000000
[11:56:58.504]    DoOptionByteLoading=0x00000000
[11:56:58.504]  </debugvars>
[11:56:58.504]  
[11:56:58.504]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:56:58.504]    <block atomic="false" info="">
[11:56:58.504]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:56:58.504]        // -> [connectionFlash <= 0x00000001]
[11:56:58.504]      __var FLASH_BASE = 0x40022000 ;
[11:56:58.504]        // -> [FLASH_BASE <= 0x40022000]
[11:56:58.504]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:56:58.504]        // -> [FLASH_CR <= 0x40022004]
[11:56:58.504]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:56:58.509]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:56:58.509]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:56:58.509]        // -> [LOCK_BIT <= 0x00000001]
[11:56:58.509]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:56:58.509]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:56:58.509]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:56:58.509]        // -> [FLASH_KEYR <= 0x4002200C]
[11:56:58.509]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:56:58.509]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:56:58.509]      __var FLASH_KEY2 = 0x02030405 ;
[11:56:58.509]        // -> [FLASH_KEY2 <= 0x02030405]
[11:56:58.509]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:56:58.512]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:56:58.512]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:56:58.512]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:56:58.512]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:56:58.512]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:56:58.512]      __var FLASH_CR_Value = 0 ;
[11:56:58.512]        // -> [FLASH_CR_Value <= 0x00000000]
[11:56:58.512]      __var DoDebugPortStop = 1 ;
[11:56:58.512]        // -> [DoDebugPortStop <= 0x00000001]
[11:56:58.512]      __var DP_CTRL_STAT = 0x4 ;
[11:56:58.512]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:56:58.514]      __var DP_SELECT = 0x8 ;
[11:56:58.514]        // -> [DP_SELECT <= 0x00000008]
[11:56:58.514]    </block>
[11:56:58.514]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:56:58.514]      // if-block "connectionFlash && DoOptionByteLoading"
[11:56:58.514]        // =>  FALSE
[11:56:58.514]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:56:58.514]    </control>
[11:56:58.514]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:56:58.514]      // if-block "DoDebugPortStop"
[11:56:58.514]        // =>  TRUE
[11:56:58.514]      <block atomic="false" info="">
[11:56:58.514]        WriteDP(DP_SELECT, 0x00000000);
[11:56:58.514]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:56:58.514]        WriteDP(DP_CTRL_STAT, 0x00000000);
[11:56:58.514]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[11:56:58.519]      </block>
[11:56:58.519]      // end if-block "DoDebugPortStop"
[11:56:58.519]    </control>
[11:56:58.519]  </sequence>
[11:56:58.519]  
[13:45:57.384]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[13:45:57.384]  
[13:45:57.407]  <debugvars>
[13:45:57.407]    // Pre-defined
[13:45:57.407]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:45:57.408]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:45:57.408]    __dp=0x00000000
[13:45:57.408]    __ap=0x00000000
[13:45:57.408]    __traceout=0x00000000      (Trace Disabled)
[13:45:57.410]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:45:57.410]    __FlashAddr=0x00000000
[13:45:57.410]    __FlashLen=0x00000000
[13:45:57.411]    __FlashArg=0x00000000
[13:45:57.411]    __FlashOp=0x00000000
[13:45:57.411]    __Result=0x00000000
[13:45:57.411]    
[13:45:57.411]    // User-defined
[13:45:57.412]    DbgMCU_CR=0x00000007
[13:45:57.412]    DbgMCU_APB1_Fz=0x00000000
[13:45:57.412]    DbgMCU_APB2_Fz=0x00000000
[13:45:57.413]    DoOptionByteLoading=0x00000000
[13:45:57.413]  </debugvars>
[13:45:57.413]  
[13:45:57.413]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[13:45:57.413]    <block atomic="false" info="">
[13:45:57.414]      Sequence("CheckID");
[13:45:57.414]        <sequence name="CheckID" Pname="" disable="false" info="">
[13:45:57.414]          <block atomic="false" info="">
[13:45:57.414]            __var pidr1 = 0;
[13:45:57.414]              // -> [pidr1 <= 0x00000000]
[13:45:57.415]            __var pidr2 = 0;
[13:45:57.415]              // -> [pidr2 <= 0x00000000]
[13:45:57.415]            __var jep106id = 0;
[13:45:57.415]              // -> [jep106id <= 0x00000000]
[13:45:57.415]            __var ROMTableBase = 0;
[13:45:57.415]              // -> [ROMTableBase <= 0x00000000]
[13:45:57.416]            __ap = 0;      // AHB-AP
[13:45:57.416]              // -> [__ap <= 0x00000000]
[13:45:57.417]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[13:45:57.417]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[13:45:57.417]              // -> [ROMTableBase <= 0xF0000000]
[13:45:57.417]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[13:45:57.419]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[13:45:57.420]              // -> [pidr1 <= 0x00000004]
[13:45:57.420]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[13:45:57.421]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[13:45:57.421]              // -> [pidr2 <= 0x0000000A]
[13:45:57.421]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[13:45:57.421]              // -> [jep106id <= 0x00000020]
[13:45:57.421]          </block>
[13:45:57.422]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[13:45:57.422]            // if-block "jep106id != 0x20"
[13:45:57.423]              // =>  FALSE
[13:45:57.423]            // skip if-block "jep106id != 0x20"
[13:45:57.423]          </control>
[13:45:57.423]        </sequence>
[13:45:57.423]    </block>
[13:45:57.423]  </sequence>
[13:45:57.424]  
[13:45:57.436]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[13:45:57.436]  
[13:45:57.439]  <debugvars>
[13:45:57.439]    // Pre-defined
[13:45:57.439]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:45:57.439]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:45:57.439]    __dp=0x00000000
[13:45:57.440]    __ap=0x00000000
[13:45:57.441]    __traceout=0x00000000      (Trace Disabled)
[13:45:57.441]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:45:57.441]    __FlashAddr=0x00000000
[13:45:57.442]    __FlashLen=0x00000000
[13:45:57.442]    __FlashArg=0x00000000
[13:45:57.442]    __FlashOp=0x00000000
[13:45:57.442]    __Result=0x00000000
[13:45:57.443]    
[13:45:57.443]    // User-defined
[13:45:57.443]    DbgMCU_CR=0x00000007
[13:45:57.443]    DbgMCU_APB1_Fz=0x00000000
[13:45:57.443]    DbgMCU_APB2_Fz=0x00000000
[13:45:57.443]    DoOptionByteLoading=0x00000000
[13:45:57.444]  </debugvars>
[13:45:57.444]  
[13:45:57.444]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[13:45:57.444]    <block atomic="false" info="">
[13:45:57.444]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[13:45:57.445]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[13:45:57.445]    </block>
[13:45:57.445]    <block atomic="false" info="DbgMCU registers">
[13:45:57.446]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[13:45:57.447]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[13:45:57.447]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[13:45:57.447]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[13:45:57.449]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[13:45:57.449]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[13:45:57.450]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:45:57.450]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[13:45:57.451]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[13:45:57.451]    </block>
[13:45:57.452]  </sequence>
[13:45:57.452]  
[13:46:04.731]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[13:46:04.731]  
[13:46:04.732]  <debugvars>
[13:46:04.732]    // Pre-defined
[13:46:04.733]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[13:46:04.733]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[13:46:04.735]    __dp=0x00000000
[13:46:04.735]    __ap=0x00000000
[13:46:04.736]    __traceout=0x00000000      (Trace Disabled)
[13:46:04.736]    __errorcontrol=0x00000000  (Skip Errors="False")
[13:46:04.737]    __FlashAddr=0x00000000
[13:46:04.738]    __FlashLen=0x00000000
[13:46:04.739]    __FlashArg=0x00000000
[13:46:04.739]    __FlashOp=0x00000000
[13:46:04.740]    __Result=0x00000000
[13:46:04.741]    
[13:46:04.741]    // User-defined
[13:46:04.741]    DbgMCU_CR=0x00000007
[13:46:04.742]    DbgMCU_APB1_Fz=0x00000000
[13:46:04.743]    DbgMCU_APB2_Fz=0x00000000
[13:46:04.743]    DoOptionByteLoading=0x00000000
[13:46:04.744]  </debugvars>
[13:46:04.744]  
[13:46:04.745]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[13:46:04.745]    <block atomic="false" info="">
[13:46:04.745]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[13:46:04.746]        // -> [connectionFlash <= 0x00000001]
[13:46:04.746]      __var FLASH_BASE = 0x40022000 ;
[13:46:04.747]        // -> [FLASH_BASE <= 0x40022000]
[13:46:04.747]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[13:46:04.747]        // -> [FLASH_CR <= 0x40022004]
[13:46:04.747]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[13:46:04.749]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[13:46:04.749]      __var LOCK_BIT = ( 1 << 0 ) ;
[13:46:04.749]        // -> [LOCK_BIT <= 0x00000001]
[13:46:04.750]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[13:46:04.750]        // -> [OPTLOCK_BIT <= 0x00000004]
[13:46:04.751]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[13:46:04.751]        // -> [FLASH_KEYR <= 0x4002200C]
[13:46:04.751]      __var FLASH_KEY1 = 0x89ABCDEF ;
[13:46:04.751]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[13:46:04.752]      __var FLASH_KEY2 = 0x02030405 ;
[13:46:04.752]        // -> [FLASH_KEY2 <= 0x02030405]
[13:46:04.752]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[13:46:04.753]        // -> [FLASH_OPTKEYR <= 0x40022014]
[13:46:04.753]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[13:46:04.753]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[13:46:04.753]      __var FLASH_OPTKEY2 = 0x24252627 ;
[13:46:04.754]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[13:46:04.754]      __var FLASH_CR_Value = 0 ;
[13:46:04.754]        // -> [FLASH_CR_Value <= 0x00000000]
[13:46:04.754]      __var DoDebugPortStop = 1 ;
[13:46:04.755]        // -> [DoDebugPortStop <= 0x00000001]
[13:46:04.755]      __var DP_CTRL_STAT = 0x4 ;
[13:46:04.755]        // -> [DP_CTRL_STAT <= 0x00000004]
[13:46:04.755]      __var DP_SELECT = 0x8 ;
[13:46:04.755]        // -> [DP_SELECT <= 0x00000008]
[13:46:04.755]    </block>
[13:46:04.755]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[13:46:04.756]      // if-block "connectionFlash && DoOptionByteLoading"
[13:46:04.756]        // =>  FALSE
[13:46:04.756]      // skip if-block "connectionFlash && DoOptionByteLoading"
[13:46:04.757]    </control>
[13:46:04.757]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[13:46:04.757]      // if-block "DoDebugPortStop"
[13:46:04.757]        // =>  TRUE
[13:46:04.758]      <block atomic="false" info="">
[13:46:04.759]        WriteDP(DP_SELECT, 0x00000000);
[13:46:04.759]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[13:46:04.760]        WriteDP(DP_CTRL_STAT, 0x00000000);
[13:46:04.760]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[13:46:04.760]      </block>
[13:46:04.761]      // end if-block "DoDebugPortStop"
[13:46:04.761]    </control>
[13:46:04.761]  </sequence>
[13:46:04.761]  
