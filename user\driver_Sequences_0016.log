/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : D:\2025work\STM32L072PRO\source-application\user\driver_Sequences_0016.log
 *  Created     : 14:43:52 (15/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : C:/Users/<USER>/AppData/Local/Arm/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : D:\2025work\STM32L072PRO\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[14:43:52.061]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:43:52.061]  
[14:43:52.089]  <debugvars>
[14:43:52.126]    // Pre-defined
[14:43:52.156]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:43:52.177]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:43:52.178]    __dp=0x00000000
[14:43:52.178]    __ap=0x00000000
[14:43:52.178]    __traceout=0x00000000      (Trace Disabled)
[14:43:52.178]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:43:52.178]    __FlashAddr=0x00000000
[14:43:52.178]    __FlashLen=0x00000000
[14:43:52.178]    __FlashArg=0x00000000
[14:43:52.178]    __FlashOp=0x00000000
[14:43:52.178]    __Result=0x00000000
[14:43:52.178]    
[14:43:52.178]    // User-defined
[14:43:52.178]    DbgMCU_CR=0x00000007
[14:43:52.178]    DbgMCU_APB1_Fz=0x00000000
[14:43:52.178]    DbgMCU_APB2_Fz=0x00000000
[14:43:52.178]    DoOptionByteLoading=0x00000000
[14:43:52.178]  </debugvars>
[14:43:52.178]  
[14:43:52.178]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:43:52.178]    <block atomic="false" info="">
[14:43:52.183]      Sequence("CheckID");
[14:43:52.183]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:43:52.183]          <block atomic="false" info="">
[14:43:52.183]            __var pidr1 = 0;
[14:43:52.183]              // -> [pidr1 <= 0x00000000]
[14:43:52.183]            __var pidr2 = 0;
[14:43:52.183]              // -> [pidr2 <= 0x00000000]
[14:43:52.183]            __var jep106id = 0;
[14:43:52.183]              // -> [jep106id <= 0x00000000]
[14:43:52.183]            __var ROMTableBase = 0;
[14:43:52.183]              // -> [ROMTableBase <= 0x00000000]
[14:43:52.183]            __ap = 0;      // AHB-AP
[14:43:52.183]              // -> [__ap <= 0x00000000]
[14:43:52.183]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:43:52.183]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:43:52.183]              // -> [ROMTableBase <= 0xF0000000]
[14:43:52.183]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:43:52.188]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:43:52.188]              // -> [pidr1 <= 0x00000004]
[14:43:52.188]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:43:52.190]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:43:52.190]              // -> [pidr2 <= 0x0000000A]
[14:43:52.190]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:43:52.190]              // -> [jep106id <= 0x00000020]
[14:43:52.190]          </block>
[14:43:52.190]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:43:52.190]            // if-block "jep106id != 0x20"
[14:43:52.190]              // =>  FALSE
[14:43:52.190]            // skip if-block "jep106id != 0x20"
[14:43:52.190]          </control>
[14:43:52.190]        </sequence>
[14:43:52.190]    </block>
[14:43:52.190]  </sequence>
[14:43:52.190]  
[14:43:52.206]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:43:52.206]  
[14:43:52.212]  <debugvars>
[14:43:52.214]    // Pre-defined
[14:43:52.214]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:43:52.214]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:43:52.214]    __dp=0x00000000
[14:43:52.214]    __ap=0x00000000
[14:43:52.214]    __traceout=0x00000000      (Trace Disabled)
[14:43:52.214]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:43:52.214]    __FlashAddr=0x00000000
[14:43:52.214]    __FlashLen=0x00000000
[14:43:52.214]    __FlashArg=0x00000000
[14:43:52.217]    __FlashOp=0x00000000
[14:43:52.217]    __Result=0x00000000
[14:43:52.217]    
[14:43:52.217]    // User-defined
[14:43:52.217]    DbgMCU_CR=0x00000007
[14:43:52.217]    DbgMCU_APB1_Fz=0x00000000
[14:43:52.217]    DbgMCU_APB2_Fz=0x00000000
[14:43:52.217]    DoOptionByteLoading=0x00000000
[14:43:52.217]  </debugvars>
[14:43:52.217]  
[14:43:52.217]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:43:52.217]    <block atomic="false" info="">
[14:43:52.217]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:43:52.217]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:43:52.217]    </block>
[14:43:52.217]    <block atomic="false" info="DbgMCU registers">
[14:43:52.217]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:43:52.217]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[14:43:52.222]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[14:43:52.222]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:43:52.224]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:43:52.224]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:43:52.224]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:43:52.224]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:43:52.226]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:43:52.226]    </block>
[14:43:52.226]  </sequence>
[14:43:52.226]  
[14:44:00.287]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:44:00.287]  
[14:44:00.287]  <debugvars>
[14:44:00.287]    // Pre-defined
[14:44:00.287]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:44:00.287]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:44:00.287]    __dp=0x00000000
[14:44:00.287]    __ap=0x00000000
[14:44:00.293]    __traceout=0x00000000      (Trace Disabled)
[14:44:00.293]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:44:00.293]    __FlashAddr=0x00000000
[14:44:00.293]    __FlashLen=0x00000000
[14:44:00.294]    __FlashArg=0x00000000
[14:44:00.294]    __FlashOp=0x00000000
[14:44:00.294]    __Result=0x00000000
[14:44:00.294]    
[14:44:00.294]    // User-defined
[14:44:00.295]    DbgMCU_CR=0x00000007
[14:44:00.295]    DbgMCU_APB1_Fz=0x00000000
[14:44:00.295]    DbgMCU_APB2_Fz=0x00000000
[14:44:00.295]    DoOptionByteLoading=0x00000000
[14:44:00.296]  </debugvars>
[14:44:00.296]  
[14:44:00.296]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:44:00.296]    <block atomic="false" info="">
[14:44:00.296]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:44:00.296]        // -> [connectionFlash <= 0x00000001]
[14:44:00.297]      __var FLASH_BASE = 0x40022000 ;
[14:44:00.297]        // -> [FLASH_BASE <= 0x40022000]
[14:44:00.297]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:44:00.297]        // -> [FLASH_CR <= 0x40022004]
[14:44:00.297]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:44:00.297]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:44:00.298]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:44:00.298]        // -> [LOCK_BIT <= 0x00000001]
[14:44:00.298]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:44:00.298]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:44:00.298]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:44:00.298]        // -> [FLASH_KEYR <= 0x4002200C]
[14:44:00.299]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:44:00.299]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:44:00.299]      __var FLASH_KEY2 = 0x02030405 ;
[14:44:00.299]        // -> [FLASH_KEY2 <= 0x02030405]
[14:44:00.300]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:44:00.300]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:44:00.300]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:44:00.300]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:44:00.300]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:44:00.302]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:44:00.302]      __var FLASH_CR_Value = 0 ;
[14:44:00.302]        // -> [FLASH_CR_Value <= 0x00000000]
[14:44:00.302]      __var DoDebugPortStop = 1 ;
[14:44:00.302]        // -> [DoDebugPortStop <= 0x00000001]
[14:44:00.302]      __var DP_CTRL_STAT = 0x4 ;
[14:44:00.302]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:44:00.302]      __var DP_SELECT = 0x8 ;
[14:44:00.303]        // -> [DP_SELECT <= 0x00000008]
[14:44:00.303]    </block>
[14:44:00.303]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:44:00.303]      // if-block "connectionFlash && DoOptionByteLoading"
[14:44:00.303]        // =>  FALSE
[14:44:00.303]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:44:00.303]    </control>
[14:44:00.303]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:44:00.303]      // if-block "DoDebugPortStop"
[14:44:00.303]        // =>  TRUE
[14:44:00.303]      <block atomic="false" info="">
[14:44:00.303]        WriteDP(DP_SELECT, 0x00000000);
[14:44:00.303]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:44:00.303]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:44:00.303]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:44:00.303]      </block>
[14:44:00.303]      // end if-block "DoDebugPortStop"
[14:44:00.303]    </control>
[14:44:00.303]  </sequence>
[14:44:00.308]  
[14:44:00.640]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:44:00.640]  
[14:44:00.640]  <debugvars>
[14:44:00.640]    // Pre-defined
[14:44:00.640]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:44:00.641]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[14:44:00.641]    __dp=0x00000000
[14:44:00.641]    __ap=0x00000000
[14:44:00.641]    __traceout=0x00000000      (Trace Disabled)
[14:44:00.641]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:44:00.641]    __FlashAddr=0x00000000
[14:44:00.641]    __FlashLen=0x00000000
[14:44:00.641]    __FlashArg=0x00000000
[14:44:00.641]    __FlashOp=0x00000000
[14:44:00.641]    __Result=0x00000000
[14:44:00.641]    
[14:44:00.641]    // User-defined
[14:44:00.641]    DbgMCU_CR=0x00000007
[14:44:00.641]    DbgMCU_APB1_Fz=0x00000000
[14:44:00.641]    DbgMCU_APB2_Fz=0x00000000
[14:44:00.641]    DoOptionByteLoading=0x00000000
[14:44:00.643]  </debugvars>
[14:44:00.644]  
[14:44:00.644]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:44:00.644]    <block atomic="false" info="">
[14:44:00.644]      Sequence("CheckID");
[14:44:00.645]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:44:00.645]          <block atomic="false" info="">
[14:44:00.645]            __var pidr1 = 0;
[14:44:00.645]              // -> [pidr1 <= 0x00000000]
[14:44:00.645]            __var pidr2 = 0;
[14:44:00.645]              // -> [pidr2 <= 0x00000000]
[14:44:00.646]            __var jep106id = 0;
[14:44:00.646]              // -> [jep106id <= 0x00000000]
[14:44:00.646]            __var ROMTableBase = 0;
[14:44:00.646]              // -> [ROMTableBase <= 0x00000000]
[14:44:00.646]            __ap = 0;      // AHB-AP
[14:44:00.647]              // -> [__ap <= 0x00000000]
[14:44:00.647]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:44:00.647]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:44:00.648]              // -> [ROMTableBase <= 0xF0000000]
[14:44:00.648]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:44:00.649]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:44:00.649]              // -> [pidr1 <= 0x00000004]
[14:44:00.649]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:44:00.650]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:44:00.650]              // -> [pidr2 <= 0x0000000A]
[14:44:00.650]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:44:00.651]              // -> [jep106id <= 0x00000020]
[14:44:00.651]          </block>
[14:44:00.651]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:44:00.651]            // if-block "jep106id != 0x20"
[14:44:00.651]              // =>  FALSE
[14:44:00.651]            // skip if-block "jep106id != 0x20"
[14:44:00.652]          </control>
[14:44:00.652]        </sequence>
[14:44:00.652]    </block>
[14:44:00.652]  </sequence>
[14:44:00.652]  
[14:44:00.663]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:44:00.663]  
[14:44:00.664]  <debugvars>
[14:44:00.664]    // Pre-defined
[14:44:00.665]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:44:00.665]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[14:44:00.665]    __dp=0x00000000
[14:44:00.665]    __ap=0x00000000
[14:44:00.666]    __traceout=0x00000000      (Trace Disabled)
[14:44:00.666]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:44:00.666]    __FlashAddr=0x00000000
[14:44:00.667]    __FlashLen=0x00000000
[14:44:00.667]    __FlashArg=0x00000000
[14:44:00.667]    __FlashOp=0x00000000
[14:44:00.667]    __Result=0x00000000
[14:44:00.668]    
[14:44:00.668]    // User-defined
[14:44:00.668]    DbgMCU_CR=0x00000007
[14:44:00.668]    DbgMCU_APB1_Fz=0x00000000
[14:44:00.669]    DbgMCU_APB2_Fz=0x00000000
[14:44:00.669]    DoOptionByteLoading=0x00000000
[14:44:00.669]  </debugvars>
[14:44:00.670]  
[14:44:00.670]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:44:00.670]    <block atomic="false" info="">
[14:44:00.670]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:44:00.671]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:44:00.672]    </block>
[14:44:00.672]    <block atomic="false" info="DbgMCU registers">
[14:44:00.672]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:44:00.673]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[14:44:00.674]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[14:44:00.674]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:44:00.675]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:44:00.676]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:44:00.677]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:44:00.678]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:44:00.679]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:44:00.679]    </block>
[14:44:00.679]  </sequence>
[14:44:00.679]  
[14:53:16.981]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:53:16.981]  
[14:53:16.982]  <debugvars>
[14:53:16.983]    // Pre-defined
[14:53:16.983]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:53:16.984]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[14:53:16.984]    __dp=0x00000000
[14:53:16.985]    __ap=0x00000000
[14:53:16.985]    __traceout=0x00000000      (Trace Disabled)
[14:53:16.985]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:53:16.986]    __FlashAddr=0x00000000
[14:53:16.986]    __FlashLen=0x00000000
[14:53:16.987]    __FlashArg=0x00000000
[14:53:16.987]    __FlashOp=0x00000000
[14:53:16.988]    __Result=0x00000000
[14:53:16.989]    
[14:53:16.989]    // User-defined
[14:53:16.989]    DbgMCU_CR=0x00000007
[14:53:16.989]    DbgMCU_APB1_Fz=0x00000000
[14:53:16.990]    DbgMCU_APB2_Fz=0x00000000
[14:53:16.990]    DoOptionByteLoading=0x00000000
[14:53:16.991]  </debugvars>
[14:53:16.991]  
[14:53:16.992]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:53:16.993]    <block atomic="false" info="">
[14:53:16.993]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:53:16.993]        // -> [connectionFlash <= 0x00000000]
[14:53:16.993]      __var FLASH_BASE = 0x40022000 ;
[14:53:16.993]        // -> [FLASH_BASE <= 0x40022000]
[14:53:16.993]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:53:16.993]        // -> [FLASH_CR <= 0x40022004]
[14:53:16.993]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:53:16.995]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:53:16.995]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:53:16.995]        // -> [LOCK_BIT <= 0x00000001]
[14:53:16.995]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:53:16.996]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:53:16.996]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:53:16.996]        // -> [FLASH_KEYR <= 0x4002200C]
[14:53:16.996]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:53:16.996]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:53:16.997]      __var FLASH_KEY2 = 0x02030405 ;
[14:53:16.997]        // -> [FLASH_KEY2 <= 0x02030405]
[14:53:16.997]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:53:16.997]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:53:16.997]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:53:16.997]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:53:16.998]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:53:16.998]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:53:16.998]      __var FLASH_CR_Value = 0 ;
[14:53:16.998]        // -> [FLASH_CR_Value <= 0x00000000]
[14:53:16.998]      __var DoDebugPortStop = 1 ;
[14:53:16.998]        // -> [DoDebugPortStop <= 0x00000001]
[14:53:16.999]      __var DP_CTRL_STAT = 0x4 ;
[14:53:16.999]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:53:16.999]      __var DP_SELECT = 0x8 ;
[14:53:16.999]        // -> [DP_SELECT <= 0x00000008]
[14:53:16.999]    </block>
[14:53:17.000]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:53:17.000]      // if-block "connectionFlash && DoOptionByteLoading"
[14:53:17.000]        // =>  FALSE
[14:53:17.000]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:53:17.001]    </control>
[14:53:17.001]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:53:17.001]      // if-block "DoDebugPortStop"
[14:53:17.002]        // =>  TRUE
[14:53:17.002]      <block atomic="false" info="">
[14:53:17.002]        WriteDP(DP_SELECT, 0x00000000);
[14:53:17.003]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:53:17.003]        WriteDP(DP_CTRL_STAT, 0x00000000);
[14:53:17.003]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[14:53:17.003]      </block>
[14:53:17.004]      // end if-block "DoDebugPortStop"
[14:53:17.004]    </control>
[14:53:17.004]  </sequence>
[14:53:17.004]  
[15:04:34.960]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:04:34.960]  
[15:04:34.961]  <debugvars>
[15:04:34.961]    // Pre-defined
[15:04:34.961]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:04:34.961]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:04:34.961]    __dp=0x00000000
[15:04:34.961]    __ap=0x00000000
[15:04:34.961]    __traceout=0x00000000      (Trace Disabled)
[15:04:34.961]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:04:34.963]    __FlashAddr=0x00000000
[15:04:34.963]    __FlashLen=0x00000000
[15:04:34.963]    __FlashArg=0x00000000
[15:04:34.963]    __FlashOp=0x00000000
[15:04:34.963]    __Result=0x00000000
[15:04:34.964]    
[15:04:34.964]    // User-defined
[15:04:34.964]    DbgMCU_CR=0x00000007
[15:04:34.964]    DbgMCU_APB1_Fz=0x00000000
[15:04:34.964]    DbgMCU_APB2_Fz=0x00000000
[15:04:34.964]    DoOptionByteLoading=0x00000000
[15:04:34.965]  </debugvars>
[15:04:34.965]  
[15:04:34.965]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:04:34.965]    <block atomic="false" info="">
[15:04:34.966]      Sequence("CheckID");
[15:04:34.966]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:04:34.966]          <block atomic="false" info="">
[15:04:34.966]            __var pidr1 = 0;
[15:04:34.966]              // -> [pidr1 <= 0x00000000]
[15:04:34.966]            __var pidr2 = 0;
[15:04:34.966]              // -> [pidr2 <= 0x00000000]
[15:04:34.966]            __var jep106id = 0;
[15:04:34.966]              // -> [jep106id <= 0x00000000]
[15:04:34.968]            __var ROMTableBase = 0;
[15:04:34.968]              // -> [ROMTableBase <= 0x00000000]
[15:04:34.968]            __ap = 0;      // AHB-AP
[15:04:34.968]              // -> [__ap <= 0x00000000]
[15:04:34.969]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:04:34.969]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:04:34.970]              // -> [ROMTableBase <= 0xF0000000]
[15:04:34.970]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:04:34.971]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:04:34.971]              // -> [pidr1 <= 0x00000004]
[15:04:34.972]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:04:34.973]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:04:34.973]              // -> [pidr2 <= 0x0000000A]
[15:04:34.973]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:04:34.974]              // -> [jep106id <= 0x00000020]
[15:04:34.974]          </block>
[15:04:34.974]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:04:34.974]            // if-block "jep106id != 0x20"
[15:04:34.974]              // =>  FALSE
[15:04:34.974]            // skip if-block "jep106id != 0x20"
[15:04:34.975]          </control>
[15:04:34.975]        </sequence>
[15:04:34.975]    </block>
[15:04:34.975]  </sequence>
[15:04:34.975]  
[15:04:34.987]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:04:34.987]  
[15:04:34.987]  <debugvars>
[15:04:34.987]    // Pre-defined
[15:04:34.990]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:04:34.990]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:04:34.990]    __dp=0x00000000
[15:04:34.990]    __ap=0x00000000
[15:04:34.990]    __traceout=0x00000000      (Trace Disabled)
[15:04:34.990]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:04:34.990]    __FlashAddr=0x00000000
[15:04:34.990]    __FlashLen=0x00000000
[15:04:34.990]    __FlashArg=0x00000000
[15:04:34.990]    __FlashOp=0x00000000
[15:04:34.992]    __Result=0x00000000
[15:04:34.992]    
[15:04:34.992]    // User-defined
[15:04:34.992]    DbgMCU_CR=0x00000007
[15:04:34.993]    DbgMCU_APB1_Fz=0x00000000
[15:04:34.993]    DbgMCU_APB2_Fz=0x00000000
[15:04:34.993]    DoOptionByteLoading=0x00000000
[15:04:34.993]  </debugvars>
[15:04:34.993]  
[15:04:34.993]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:04:34.993]    <block atomic="false" info="">
[15:04:34.993]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:04:34.993]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:04:34.995]    </block>
[15:04:34.995]    <block atomic="false" info="DbgMCU registers">
[15:04:34.995]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:04:34.996]        // -> [Read32(0x40021034) => 0x00004001]   (__dp=0x00000000, __ap=0x00000000)
[15:04:34.997]        // -> [Write32(0x40021034, 0x00404001)]   (__dp=0x00000000, __ap=0x00000000)
[15:04:34.997]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:04:34.997]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:04:34.997]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:04:34.999]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:04:34.999]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:04:34.999]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:04:35.000]    </block>
[15:04:35.000]  </sequence>
[15:04:35.000]  
[15:04:43.173]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:04:43.173]  
[15:04:43.173]  <debugvars>
[15:04:43.173]    // Pre-defined
[15:04:43.173]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:04:43.173]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:04:43.173]    __dp=0x00000000
[15:04:43.173]    __ap=0x00000000
[15:04:43.173]    __traceout=0x00000000      (Trace Disabled)
[15:04:43.178]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:04:43.178]    __FlashAddr=0x00000000
[15:04:43.178]    __FlashLen=0x00000000
[15:04:43.179]    __FlashArg=0x00000000
[15:04:43.179]    __FlashOp=0x00000000
[15:04:43.180]    __Result=0x00000000
[15:04:43.180]    
[15:04:43.180]    // User-defined
[15:04:43.180]    DbgMCU_CR=0x00000007
[15:04:43.180]    DbgMCU_APB1_Fz=0x00000000
[15:04:43.180]    DbgMCU_APB2_Fz=0x00000000
[15:04:43.181]    DoOptionByteLoading=0x00000000
[15:04:43.181]  </debugvars>
[15:04:43.181]  
[15:04:43.181]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:04:43.181]    <block atomic="false" info="">
[15:04:43.182]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:04:43.182]        // -> [connectionFlash <= 0x00000001]
[15:04:43.182]      __var FLASH_BASE = 0x40022000 ;
[15:04:43.182]        // -> [FLASH_BASE <= 0x40022000]
[15:04:43.182]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:04:43.182]        // -> [FLASH_CR <= 0x40022004]
[15:04:43.183]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:04:43.183]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:04:43.183]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:04:43.183]        // -> [LOCK_BIT <= 0x00000001]
[15:04:43.184]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:04:43.184]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:04:43.184]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:04:43.185]        // -> [FLASH_KEYR <= 0x4002200C]
[15:04:43.185]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:04:43.185]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:04:43.185]      __var FLASH_KEY2 = 0x02030405 ;
[15:04:43.185]        // -> [FLASH_KEY2 <= 0x02030405]
[15:04:43.186]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:04:43.186]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:04:43.186]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:04:43.186]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:04:43.186]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:04:43.186]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:04:43.187]      __var FLASH_CR_Value = 0 ;
[15:04:43.187]        // -> [FLASH_CR_Value <= 0x00000000]
[15:04:43.187]      __var DoDebugPortStop = 1 ;
[15:04:43.187]        // -> [DoDebugPortStop <= 0x00000001]
[15:04:43.187]      __var DP_CTRL_STAT = 0x4 ;
[15:04:43.187]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:04:43.188]      __var DP_SELECT = 0x8 ;
[15:04:43.188]        // -> [DP_SELECT <= 0x00000008]
[15:04:43.188]    </block>
[15:04:43.189]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:04:43.189]      // if-block "connectionFlash && DoOptionByteLoading"
[15:04:43.189]        // =>  FALSE
[15:04:43.189]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:04:43.189]    </control>
[15:04:43.189]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:04:43.190]      // if-block "DoDebugPortStop"
[15:04:43.190]        // =>  TRUE
[15:04:43.190]      <block atomic="false" info="">
[15:04:43.190]        WriteDP(DP_SELECT, 0x00000000);
[15:04:43.190]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:04:43.191]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:04:43.191]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:04:43.191]      </block>
[15:04:43.192]      // end if-block "DoDebugPortStop"
[15:04:43.192]    </control>
[15:04:43.192]  </sequence>
[15:04:43.192]  
[15:05:07.263]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:05:07.263]  
[15:05:07.264]  <debugvars>
[15:05:07.264]    // Pre-defined
[15:05:07.264]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:05:07.264]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[15:05:07.265]    __dp=0x00000000
[15:05:07.265]    __ap=0x00000000
[15:05:07.265]    __traceout=0x00000000      (Trace Disabled)
[15:05:07.265]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:05:07.265]    __FlashAddr=0x00000000
[15:05:07.265]    __FlashLen=0x00000000
[15:05:07.265]    __FlashArg=0x00000000
[15:05:07.265]    __FlashOp=0x00000000
[15:05:07.265]    __Result=0x00000000
[15:05:07.265]    
[15:05:07.265]    // User-defined
[15:05:07.265]    DbgMCU_CR=0x00000007
[15:05:07.265]    DbgMCU_APB1_Fz=0x00000000
[15:05:07.265]    DbgMCU_APB2_Fz=0x00000000
[15:05:07.269]    DoOptionByteLoading=0x00000000
[15:05:07.269]  </debugvars>
[15:05:07.269]  
[15:05:07.269]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:05:07.269]    <block atomic="false" info="">
[15:05:07.269]      Sequence("CheckID");
[15:05:07.269]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:05:07.269]          <block atomic="false" info="">
[15:05:07.269]            __var pidr1 = 0;
[15:05:07.269]              // -> [pidr1 <= 0x00000000]
[15:05:07.269]            __var pidr2 = 0;
[15:05:07.269]              // -> [pidr2 <= 0x00000000]
[15:05:07.269]            __var jep106id = 0;
[15:05:07.269]              // -> [jep106id <= 0x00000000]
[15:05:07.269]            __var ROMTableBase = 0;
[15:05:07.269]              // -> [ROMTableBase <= 0x00000000]
[15:05:07.269]            __ap = 0;      // AHB-AP
[15:05:07.269]              // -> [__ap <= 0x00000000]
[15:05:07.269]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:05:07.269]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:05:07.269]              // -> [ROMTableBase <= 0xF0000000]
[15:05:07.269]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:05:07.274]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:05:07.275]              // -> [pidr1 <= 0x00000004]
[15:05:07.275]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:05:07.276]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:05:07.276]              // -> [pidr2 <= 0x0000000A]
[15:05:07.277]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:05:07.277]              // -> [jep106id <= 0x00000020]
[15:05:07.277]          </block>
[15:05:07.277]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:05:07.278]            // if-block "jep106id != 0x20"
[15:05:07.278]              // =>  FALSE
[15:05:07.278]            // skip if-block "jep106id != 0x20"
[15:05:07.278]          </control>
[15:05:07.278]        </sequence>
[15:05:07.279]    </block>
[15:05:07.279]  </sequence>
[15:05:07.280]  
[15:05:07.291]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:05:07.291]  
[15:05:07.314]  <debugvars>
[15:05:07.315]    // Pre-defined
[15:05:07.315]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:05:07.315]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[15:05:07.316]    __dp=0x00000000
[15:05:07.316]    __ap=0x00000000
[15:05:07.316]    __traceout=0x00000000      (Trace Disabled)
[15:05:07.317]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:05:07.317]    __FlashAddr=0x00000000
[15:05:07.317]    __FlashLen=0x00000000
[15:05:07.317]    __FlashArg=0x00000000
[15:05:07.318]    __FlashOp=0x00000000
[15:05:07.318]    __Result=0x00000000
[15:05:07.318]    
[15:05:07.318]    // User-defined
[15:05:07.319]    DbgMCU_CR=0x00000007
[15:05:07.319]    DbgMCU_APB1_Fz=0x00000000
[15:05:07.319]    DbgMCU_APB2_Fz=0x00000000
[15:05:07.319]    DoOptionByteLoading=0x00000000
[15:05:07.320]  </debugvars>
[15:05:07.320]  
[15:05:07.320]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:05:07.320]    <block atomic="false" info="">
[15:05:07.320]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:05:07.321]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:05:07.321]    </block>
[15:05:07.321]    <block atomic="false" info="DbgMCU registers">
[15:05:07.322]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:05:07.322]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[15:05:07.324]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[15:05:07.324]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:05:07.325]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:05:07.325]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:05:07.326]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:05:07.326]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:05:07.327]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:05:07.327]    </block>
[15:05:07.327]  </sequence>
[15:05:07.327]  
[15:09:08.983]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:09:08.983]  
[15:09:08.984]  <debugvars>
[15:09:08.984]    // Pre-defined
[15:09:08.984]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:09:08.984]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[15:09:08.985]    __dp=0x00000000
[15:09:08.985]    __ap=0x00000000
[15:09:08.985]    __traceout=0x00000000      (Trace Disabled)
[15:09:08.986]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:09:08.986]    __FlashAddr=0x00000000
[15:09:08.986]    __FlashLen=0x00000000
[15:09:08.986]    __FlashArg=0x00000000
[15:09:08.986]    __FlashOp=0x00000000
[15:09:08.987]    __Result=0x00000000
[15:09:08.987]    
[15:09:08.987]    // User-defined
[15:09:08.988]    DbgMCU_CR=0x00000007
[15:09:08.988]    DbgMCU_APB1_Fz=0x00000000
[15:09:08.988]    DbgMCU_APB2_Fz=0x00000000
[15:09:08.988]    DoOptionByteLoading=0x00000000
[15:09:08.988]  </debugvars>
[15:09:08.988]  
[15:09:08.989]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:09:08.990]    <block atomic="false" info="">
[15:09:08.990]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:09:08.990]        // -> [connectionFlash <= 0x00000000]
[15:09:08.991]      __var FLASH_BASE = 0x40022000 ;
[15:09:08.991]        // -> [FLASH_BASE <= 0x40022000]
[15:09:08.991]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:09:08.991]        // -> [FLASH_CR <= 0x40022004]
[15:09:08.992]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:09:08.992]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:09:08.992]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:09:08.992]        // -> [LOCK_BIT <= 0x00000001]
[15:09:08.992]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:09:08.992]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:09:08.993]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:09:08.993]        // -> [FLASH_KEYR <= 0x4002200C]
[15:09:08.993]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:09:08.993]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:09:08.993]      __var FLASH_KEY2 = 0x02030405 ;
[15:09:08.994]        // -> [FLASH_KEY2 <= 0x02030405]
[15:09:08.994]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:09:08.994]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:09:08.994]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:09:08.995]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:09:08.995]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:09:08.995]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:09:08.995]      __var FLASH_CR_Value = 0 ;
[15:09:08.995]        // -> [FLASH_CR_Value <= 0x00000000]
[15:09:08.996]      __var DoDebugPortStop = 1 ;
[15:09:08.996]        // -> [DoDebugPortStop <= 0x00000001]
[15:09:08.997]      __var DP_CTRL_STAT = 0x4 ;
[15:09:08.997]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:09:08.997]      __var DP_SELECT = 0x8 ;
[15:09:08.997]        // -> [DP_SELECT <= 0x00000008]
[15:09:08.998]    </block>
[15:09:08.998]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:09:08.998]      // if-block "connectionFlash && DoOptionByteLoading"
[15:09:08.998]        // =>  FALSE
[15:09:08.999]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:09:08.999]    </control>
[15:09:08.999]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:09:08.999]      // if-block "DoDebugPortStop"
[15:09:09.000]        // =>  TRUE
[15:09:09.000]      <block atomic="false" info="">
[15:09:09.000]        WriteDP(DP_SELECT, 0x00000000);
[15:09:09.001]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:09:09.001]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:09:09.002]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:09:09.002]      </block>
[15:09:09.002]      // end if-block "DoDebugPortStop"
[15:09:09.003]    </control>
[15:09:09.003]  </sequence>
[15:09:09.003]  
[15:18:42.689]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:18:42.689]  
[15:18:42.689]  <debugvars>
[15:18:42.689]    // Pre-defined
[15:18:42.689]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:18:42.689]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:18:42.689]    __dp=0x00000000
[15:18:42.689]    __ap=0x00000000
[15:18:42.691]    __traceout=0x00000000      (Trace Disabled)
[15:18:42.691]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:18:42.691]    __FlashAddr=0x00000000
[15:18:42.691]    __FlashLen=0x00000000
[15:18:42.691]    __FlashArg=0x00000000
[15:18:42.692]    __FlashOp=0x00000000
[15:18:42.692]    __Result=0x00000000
[15:18:42.692]    
[15:18:42.692]    // User-defined
[15:18:42.692]    DbgMCU_CR=0x00000007
[15:18:42.692]    DbgMCU_APB1_Fz=0x00000000
[15:18:42.692]    DbgMCU_APB2_Fz=0x00000000
[15:18:42.692]    DoOptionByteLoading=0x00000000
[15:18:42.692]  </debugvars>
[15:18:42.692]  
[15:18:42.692]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:18:42.694]    <block atomic="false" info="">
[15:18:42.694]      Sequence("CheckID");
[15:18:42.694]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:18:42.694]          <block atomic="false" info="">
[15:18:42.694]            __var pidr1 = 0;
[15:18:42.694]              // -> [pidr1 <= 0x00000000]
[15:18:42.694]            __var pidr2 = 0;
[15:18:42.694]              // -> [pidr2 <= 0x00000000]
[15:18:42.694]            __var jep106id = 0;
[15:18:42.694]              // -> [jep106id <= 0x00000000]
[15:18:42.696]            __var ROMTableBase = 0;
[15:18:42.696]              // -> [ROMTableBase <= 0x00000000]
[15:18:42.696]            __ap = 0;      // AHB-AP
[15:18:42.696]              // -> [__ap <= 0x00000000]
[15:18:42.696]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:18:42.697]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:18:42.697]              // -> [ROMTableBase <= 0xF0000000]
[15:18:42.697]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:18:42.699]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:18:42.699]              // -> [pidr1 <= 0x00000004]
[15:18:42.699]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:18:42.699]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:18:42.699]              // -> [pidr2 <= 0x0000000A]
[15:18:42.699]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:18:42.701]              // -> [jep106id <= 0x00000020]
[15:18:42.701]          </block>
[15:18:42.701]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:18:42.701]            // if-block "jep106id != 0x20"
[15:18:42.701]              // =>  FALSE
[15:18:42.702]            // skip if-block "jep106id != 0x20"
[15:18:42.702]          </control>
[15:18:42.702]        </sequence>
[15:18:42.702]    </block>
[15:18:42.702]  </sequence>
[15:18:42.704]  
[15:18:42.714]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:18:42.714]  
[15:18:42.714]  <debugvars>
[15:18:42.716]    // Pre-defined
[15:18:42.716]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:18:42.716]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:18:42.716]    __dp=0x00000000
[15:18:42.716]    __ap=0x00000000
[15:18:42.717]    __traceout=0x00000000      (Trace Disabled)
[15:18:42.717]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:18:42.717]    __FlashAddr=0x00000000
[15:18:42.717]    __FlashLen=0x00000000
[15:18:42.717]    __FlashArg=0x00000000
[15:18:42.717]    __FlashOp=0x00000000
[15:18:42.717]    __Result=0x00000000
[15:18:42.717]    
[15:18:42.717]    // User-defined
[15:18:42.717]    DbgMCU_CR=0x00000007
[15:18:42.717]    DbgMCU_APB1_Fz=0x00000000
[15:18:42.719]    DbgMCU_APB2_Fz=0x00000000
[15:18:42.719]    DoOptionByteLoading=0x00000000
[15:18:42.719]  </debugvars>
[15:18:42.719]  
[15:18:42.719]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:18:42.719]    <block atomic="false" info="">
[15:18:42.719]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:18:42.721]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:18:42.721]    </block>
[15:18:42.721]    <block atomic="false" info="DbgMCU registers">
[15:18:42.721]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:18:42.722]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[15:18:42.722]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[15:18:42.722]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:18:42.724]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:18:42.724]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:18:42.724]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:18:42.724]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:18:42.726]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:18:42.726]    </block>
[15:18:42.727]  </sequence>
[15:18:42.727]  
[15:18:50.966]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:18:50.966]  
[15:18:50.967]  <debugvars>
[15:18:50.968]    // Pre-defined
[15:18:50.968]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:18:50.969]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:18:50.969]    __dp=0x00000000
[15:18:50.970]    __ap=0x00000000
[15:18:50.970]    __traceout=0x00000000      (Trace Disabled)
[15:18:50.971]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:18:50.972]    __FlashAddr=0x00000000
[15:18:50.972]    __FlashLen=0x00000000
[15:18:50.973]    __FlashArg=0x00000000
[15:18:50.973]    __FlashOp=0x00000000
[15:18:50.974]    __Result=0x00000000
[15:18:50.974]    
[15:18:50.974]    // User-defined
[15:18:50.975]    DbgMCU_CR=0x00000007
[15:18:50.975]    DbgMCU_APB1_Fz=0x00000000
[15:18:50.975]    DbgMCU_APB2_Fz=0x00000000
[15:18:50.976]    DoOptionByteLoading=0x00000000
[15:18:50.976]  </debugvars>
[15:18:50.977]  
[15:18:50.977]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:18:50.978]    <block atomic="false" info="">
[15:18:50.978]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:18:50.979]        // -> [connectionFlash <= 0x00000001]
[15:18:50.979]      __var FLASH_BASE = 0x40022000 ;
[15:18:50.980]        // -> [FLASH_BASE <= 0x40022000]
[15:18:50.980]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:18:50.981]        // -> [FLASH_CR <= 0x40022004]
[15:18:50.981]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:18:50.981]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:18:50.982]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:18:50.982]        // -> [LOCK_BIT <= 0x00000001]
[15:18:50.982]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:18:50.982]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:18:50.982]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:18:50.982]        // -> [FLASH_KEYR <= 0x4002200C]
[15:18:50.984]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:18:50.984]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:18:50.984]      __var FLASH_KEY2 = 0x02030405 ;
[15:18:50.985]        // -> [FLASH_KEY2 <= 0x02030405]
[15:18:50.985]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:18:50.985]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:18:50.985]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:18:50.986]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:18:50.986]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:18:50.986]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:18:50.986]      __var FLASH_CR_Value = 0 ;
[15:18:50.986]        // -> [FLASH_CR_Value <= 0x00000000]
[15:18:50.986]      __var DoDebugPortStop = 1 ;
[15:18:50.987]        // -> [DoDebugPortStop <= 0x00000001]
[15:18:50.987]      __var DP_CTRL_STAT = 0x4 ;
[15:18:50.987]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:18:50.987]      __var DP_SELECT = 0x8 ;
[15:18:50.987]        // -> [DP_SELECT <= 0x00000008]
[15:18:50.988]    </block>
[15:18:50.988]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:18:50.988]      // if-block "connectionFlash && DoOptionByteLoading"
[15:18:50.988]        // =>  FALSE
[15:18:50.988]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:18:50.988]    </control>
[15:18:50.989]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:18:50.989]      // if-block "DoDebugPortStop"
[15:18:50.989]        // =>  TRUE
[15:18:50.989]      <block atomic="false" info="">
[15:18:50.989]        WriteDP(DP_SELECT, 0x00000000);
[15:18:50.990]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:18:50.990]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:18:50.991]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:18:50.991]      </block>
[15:18:50.991]      // end if-block "DoDebugPortStop"
[15:18:50.991]    </control>
[15:18:50.992]  </sequence>
[15:18:50.992]  
[15:42:09.507]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:42:09.507]  
[15:42:09.508]  <debugvars>
[15:42:09.508]    // Pre-defined
[15:42:09.508]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:42:09.509]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:42:09.509]    __dp=0x00000000
[15:42:09.509]    __ap=0x00000000
[15:42:09.509]    __traceout=0x00000000      (Trace Disabled)
[15:42:09.509]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:42:09.509]    __FlashAddr=0x00000000
[15:42:09.509]    __FlashLen=0x00000000
[15:42:09.509]    __FlashArg=0x00000000
[15:42:09.509]    __FlashOp=0x00000000
[15:42:09.511]    __Result=0x00000000
[15:42:09.511]    
[15:42:09.511]    // User-defined
[15:42:09.511]    DbgMCU_CR=0x00000007
[15:42:09.511]    DbgMCU_APB1_Fz=0x00000000
[15:42:09.511]    DbgMCU_APB2_Fz=0x00000000
[15:42:09.511]    DoOptionByteLoading=0x00000000
[15:42:09.512]  </debugvars>
[15:42:09.512]  
[15:42:09.512]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:42:09.512]    <block atomic="false" info="">
[15:42:09.512]      Sequence("CheckID");
[15:42:09.513]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:42:09.513]          <block atomic="false" info="">
[15:42:09.513]            __var pidr1 = 0;
[15:42:09.513]              // -> [pidr1 <= 0x00000000]
[15:42:09.513]            __var pidr2 = 0;
[15:42:09.513]              // -> [pidr2 <= 0x00000000]
[15:42:09.514]            __var jep106id = 0;
[15:42:09.514]              // -> [jep106id <= 0x00000000]
[15:42:09.514]            __var ROMTableBase = 0;
[15:42:09.514]              // -> [ROMTableBase <= 0x00000000]
[15:42:09.514]            __ap = 0;      // AHB-AP
[15:42:09.515]              // -> [__ap <= 0x00000000]
[15:42:09.515]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:42:09.516]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:42:09.516]              // -> [ROMTableBase <= 0xF0000000]
[15:42:09.516]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:42:09.517]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:42:09.517]              // -> [pidr1 <= 0x00000004]
[15:42:09.517]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:42:09.519]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:42:09.519]              // -> [pidr2 <= 0x0000000A]
[15:42:09.519]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:42:09.519]              // -> [jep106id <= 0x00000020]
[15:42:09.519]          </block>
[15:42:09.520]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:42:09.520]            // if-block "jep106id != 0x20"
[15:42:09.520]              // =>  FALSE
[15:42:09.520]            // skip if-block "jep106id != 0x20"
[15:42:09.520]          </control>
[15:42:09.520]        </sequence>
[15:42:09.521]    </block>
[15:42:09.521]  </sequence>
[15:42:09.521]  
[15:42:09.533]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:42:09.533]  
[15:42:09.533]  <debugvars>
[15:42:09.533]    // Pre-defined
[15:42:09.533]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:42:09.533]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:42:09.534]    __dp=0x00000000
[15:42:09.534]    __ap=0x00000000
[15:42:09.534]    __traceout=0x00000000      (Trace Disabled)
[15:42:09.534]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:42:09.534]    __FlashAddr=0x00000000
[15:42:09.535]    __FlashLen=0x00000000
[15:42:09.535]    __FlashArg=0x00000000
[15:42:09.535]    __FlashOp=0x00000000
[15:42:09.535]    __Result=0x00000000
[15:42:09.535]    
[15:42:09.535]    // User-defined
[15:42:09.536]    DbgMCU_CR=0x00000007
[15:42:09.536]    DbgMCU_APB1_Fz=0x00000000
[15:42:09.536]    DbgMCU_APB2_Fz=0x00000000
[15:42:09.536]    DoOptionByteLoading=0x00000000
[15:42:09.537]  </debugvars>
[15:42:09.537]  
[15:42:09.537]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:42:09.537]    <block atomic="false" info="">
[15:42:09.538]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:42:09.538]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:42:09.539]    </block>
[15:42:09.539]    <block atomic="false" info="DbgMCU registers">
[15:42:09.539]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:42:09.540]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[15:42:09.541]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[15:42:09.541]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:42:09.542]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:42:09.542]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:42:09.543]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:42:09.543]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:42:09.544]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:42:09.544]    </block>
[15:42:09.544]  </sequence>
[15:42:09.544]  
[15:42:17.505]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:42:17.505]  
[15:42:17.506]  <debugvars>
[15:42:17.506]    // Pre-defined
[15:42:17.506]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:42:17.507]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:42:17.507]    __dp=0x00000000
[15:42:17.507]    __ap=0x00000000
[15:42:17.507]    __traceout=0x00000000      (Trace Disabled)
[15:42:17.507]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:42:17.508]    __FlashAddr=0x00000000
[15:42:17.508]    __FlashLen=0x00000000
[15:42:17.508]    __FlashArg=0x00000000
[15:42:17.508]    __FlashOp=0x00000000
[15:42:17.508]    __Result=0x00000000
[15:42:17.508]    
[15:42:17.508]    // User-defined
[15:42:17.509]    DbgMCU_CR=0x00000007
[15:42:17.509]    DbgMCU_APB1_Fz=0x00000000
[15:42:17.509]    DbgMCU_APB2_Fz=0x00000000
[15:42:17.510]    DoOptionByteLoading=0x00000000
[15:42:17.510]  </debugvars>
[15:42:17.510]  
[15:42:17.510]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:42:17.510]    <block atomic="false" info="">
[15:42:17.511]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:42:17.511]        // -> [connectionFlash <= 0x00000001]
[15:42:17.511]      __var FLASH_BASE = 0x40022000 ;
[15:42:17.511]        // -> [FLASH_BASE <= 0x40022000]
[15:42:17.511]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:42:17.511]        // -> [FLASH_CR <= 0x40022004]
[15:42:17.511]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:42:17.512]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:42:17.512]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:42:17.513]        // -> [LOCK_BIT <= 0x00000001]
[15:42:17.513]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:42:17.513]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:42:17.513]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:42:17.513]        // -> [FLASH_KEYR <= 0x4002200C]
[15:42:17.514]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:42:17.514]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:42:17.514]      __var FLASH_KEY2 = 0x02030405 ;
[15:42:17.514]        // -> [FLASH_KEY2 <= 0x02030405]
[15:42:17.514]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:42:17.515]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:42:17.515]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:42:17.515]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:42:17.515]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:42:17.515]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:42:17.515]      __var FLASH_CR_Value = 0 ;
[15:42:17.515]        // -> [FLASH_CR_Value <= 0x00000000]
[15:42:17.515]      __var DoDebugPortStop = 1 ;
[15:42:17.516]        // -> [DoDebugPortStop <= 0x00000001]
[15:42:17.516]      __var DP_CTRL_STAT = 0x4 ;
[15:42:17.517]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:42:17.517]      __var DP_SELECT = 0x8 ;
[15:42:17.517]        // -> [DP_SELECT <= 0x00000008]
[15:42:17.517]    </block>
[15:42:17.517]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:42:17.518]      // if-block "connectionFlash && DoOptionByteLoading"
[15:42:17.518]        // =>  FALSE
[15:42:17.518]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:42:17.518]    </control>
[15:42:17.518]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:42:17.518]      // if-block "DoDebugPortStop"
[15:42:17.518]        // =>  TRUE
[15:42:17.518]      <block atomic="false" info="">
[15:42:17.519]        WriteDP(DP_SELECT, 0x00000000);
[15:42:17.520]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:42:17.520]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:42:17.520]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:42:17.520]      </block>
[15:42:17.521]      // end if-block "DoDebugPortStop"
[15:42:17.521]    </control>
[15:42:17.521]  </sequence>
[15:42:17.522]  
[15:44:26.244]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:44:26.244]  
[15:44:26.244]  <debugvars>
[15:44:26.245]    // Pre-defined
[15:44:26.245]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:44:26.245]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[15:44:26.245]    __dp=0x00000000
[15:44:26.246]    __ap=0x00000000
[15:44:26.246]    __traceout=0x00000000      (Trace Disabled)
[15:44:26.246]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:44:26.246]    __FlashAddr=0x00000000
[15:44:26.246]    __FlashLen=0x00000000
[15:44:26.246]    __FlashArg=0x00000000
[15:44:26.247]    __FlashOp=0x00000000
[15:44:26.248]    __Result=0x00000000
[15:44:26.248]    
[15:44:26.248]    // User-defined
[15:44:26.248]    DbgMCU_CR=0x00000007
[15:44:26.248]    DbgMCU_APB1_Fz=0x00000000
[15:44:26.248]    DbgMCU_APB2_Fz=0x00000000
[15:44:26.249]    DoOptionByteLoading=0x00000000
[15:44:26.249]  </debugvars>
[15:44:26.249]  
[15:44:26.249]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:44:26.250]    <block atomic="false" info="">
[15:44:26.250]      Sequence("CheckID");
[15:44:26.250]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:44:26.250]          <block atomic="false" info="">
[15:44:26.251]            __var pidr1 = 0;
[15:44:26.251]              // -> [pidr1 <= 0x00000000]
[15:44:26.251]            __var pidr2 = 0;
[15:44:26.251]              // -> [pidr2 <= 0x00000000]
[15:44:26.252]            __var jep106id = 0;
[15:44:26.252]              // -> [jep106id <= 0x00000000]
[15:44:26.252]            __var ROMTableBase = 0;
[15:44:26.252]              // -> [ROMTableBase <= 0x00000000]
[15:44:26.252]            __ap = 0;      // AHB-AP
[15:44:26.252]              // -> [__ap <= 0x00000000]
[15:44:26.253]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:44:26.253]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:44:26.254]              // -> [ROMTableBase <= 0xF0000000]
[15:44:26.254]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:44:26.256]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:44:26.256]              // -> [pidr1 <= 0x00000004]
[15:44:26.256]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:44:26.257]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:44:26.257]              // -> [pidr2 <= 0x0000000A]
[15:44:26.257]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:44:26.257]              // -> [jep106id <= 0x00000020]
[15:44:26.257]          </block>
[15:44:26.257]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:44:26.257]            // if-block "jep106id != 0x20"
[15:44:26.257]              // =>  FALSE
[15:44:26.257]            // skip if-block "jep106id != 0x20"
[15:44:26.257]          </control>
[15:44:26.257]        </sequence>
[15:44:26.261]    </block>
[15:44:26.261]  </sequence>
[15:44:26.261]  
[15:44:26.273]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:44:26.273]  
[15:44:26.275]  <debugvars>
[15:44:26.275]    // Pre-defined
[15:44:26.275]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:44:26.275]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[15:44:26.275]    __dp=0x00000000
[15:44:26.275]    __ap=0x00000000
[15:44:26.275]    __traceout=0x00000000      (Trace Disabled)
[15:44:26.277]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:44:26.277]    __FlashAddr=0x00000000
[15:44:26.277]    __FlashLen=0x00000000
[15:44:26.277]    __FlashArg=0x00000000
[15:44:26.277]    __FlashOp=0x00000000
[15:44:26.277]    __Result=0x00000000
[15:44:26.277]    
[15:44:26.277]    // User-defined
[15:44:26.277]    DbgMCU_CR=0x00000007
[15:44:26.277]    DbgMCU_APB1_Fz=0x00000000
[15:44:26.277]    DbgMCU_APB2_Fz=0x00000000
[15:44:26.277]    DoOptionByteLoading=0x00000000
[15:44:26.277]  </debugvars>
[15:44:26.279]  
[15:44:26.279]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:44:26.279]    <block atomic="false" info="">
[15:44:26.279]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:44:26.281]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:44:26.281]    </block>
[15:44:26.281]    <block atomic="false" info="DbgMCU registers">
[15:44:26.282]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:44:26.283]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[15:44:26.283]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[15:44:26.284]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:44:26.285]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:44:26.285]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:44:26.285]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:44:26.285]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:44:26.285]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:44:26.285]    </block>
[15:44:26.285]  </sequence>
[15:44:26.285]  
[15:51:46.362]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:51:46.362]  
[15:51:46.362]  <debugvars>
[15:51:46.363]    // Pre-defined
[15:51:46.363]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:51:46.363]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[15:51:46.363]    __dp=0x00000000
[15:51:46.365]    __ap=0x00000000
[15:51:46.365]    __traceout=0x00000000      (Trace Disabled)
[15:51:46.366]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:51:46.366]    __FlashAddr=0x00000000
[15:51:46.366]    __FlashLen=0x00000000
[15:51:46.366]    __FlashArg=0x00000000
[15:51:46.367]    __FlashOp=0x00000000
[15:51:46.367]    __Result=0x00000000
[15:51:46.367]    
[15:51:46.367]    // User-defined
[15:51:46.367]    DbgMCU_CR=0x00000007
[15:51:46.367]    DbgMCU_APB1_Fz=0x00000000
[15:51:46.367]    DbgMCU_APB2_Fz=0x00000000
[15:51:46.367]    DoOptionByteLoading=0x00000000
[15:51:46.367]  </debugvars>
[15:51:46.367]  
[15:51:46.367]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:51:46.367]    <block atomic="false" info="">
[15:51:46.367]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:51:46.370]        // -> [connectionFlash <= 0x00000000]
[15:51:46.370]      __var FLASH_BASE = 0x40022000 ;
[15:51:46.370]        // -> [FLASH_BASE <= 0x40022000]
[15:51:46.371]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:51:46.371]        // -> [FLASH_CR <= 0x40022004]
[15:51:46.371]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:51:46.371]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:51:46.372]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:51:46.372]        // -> [LOCK_BIT <= 0x00000001]
[15:51:46.372]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:51:46.373]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:51:46.373]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:51:46.374]        // -> [FLASH_KEYR <= 0x4002200C]
[15:51:46.374]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:51:46.374]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:51:46.374]      __var FLASH_KEY2 = 0x02030405 ;
[15:51:46.375]        // -> [FLASH_KEY2 <= 0x02030405]
[15:51:46.375]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:51:46.375]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:51:46.376]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:51:46.376]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:51:46.376]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:51:46.377]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:51:46.377]      __var FLASH_CR_Value = 0 ;
[15:51:46.377]        // -> [FLASH_CR_Value <= 0x00000000]
[15:51:46.377]      __var DoDebugPortStop = 1 ;
[15:51:46.378]        // -> [DoDebugPortStop <= 0x00000001]
[15:51:46.378]      __var DP_CTRL_STAT = 0x4 ;
[15:51:46.378]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:51:46.378]      __var DP_SELECT = 0x8 ;
[15:51:46.379]        // -> [DP_SELECT <= 0x00000008]
[15:51:46.379]    </block>
[15:51:46.379]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:51:46.379]      // if-block "connectionFlash && DoOptionByteLoading"
[15:51:46.380]        // =>  FALSE
[15:51:46.380]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:51:46.380]    </control>
[15:51:46.380]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:51:46.381]      // if-block "DoDebugPortStop"
[15:51:46.381]        // =>  TRUE
[15:51:46.381]      <block atomic="false" info="">
[15:51:46.382]        WriteDP(DP_SELECT, 0x00000000);
[15:51:46.382]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:51:46.383]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:51:46.383]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:51:46.383]      </block>
[15:51:46.384]      // end if-block "DoDebugPortStop"
[15:51:46.384]    </control>
[15:51:46.384]  </sequence>
[15:51:46.384]  
[15:54:43.856]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[15:54:43.856]  
[15:54:43.857]  <debugvars>
[15:54:43.857]    // Pre-defined
[15:54:43.857]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:54:43.858]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:54:43.858]    __dp=0x00000000
[15:54:43.858]    __ap=0x00000000
[15:54:43.858]    __traceout=0x00000000      (Trace Disabled)
[15:54:43.858]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:54:43.859]    __FlashAddr=0x00000000
[15:54:43.859]    __FlashLen=0x00000000
[15:54:43.859]    __FlashArg=0x00000000
[15:54:43.859]    __FlashOp=0x00000000
[15:54:43.860]    __Result=0x00000000
[15:54:43.860]    
[15:54:43.860]    // User-defined
[15:54:43.860]    DbgMCU_CR=0x00000007
[15:54:43.860]    DbgMCU_APB1_Fz=0x00000000
[15:54:43.860]    DbgMCU_APB2_Fz=0x00000000
[15:54:43.861]    DoOptionByteLoading=0x00000000
[15:54:43.861]  </debugvars>
[15:54:43.861]  
[15:54:43.861]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[15:54:43.861]    <block atomic="false" info="">
[15:54:43.861]      Sequence("CheckID");
[15:54:43.861]        <sequence name="CheckID" Pname="" disable="false" info="">
[15:54:43.861]          <block atomic="false" info="">
[15:54:43.861]            __var pidr1 = 0;
[15:54:43.861]              // -> [pidr1 <= 0x00000000]
[15:54:43.863]            __var pidr2 = 0;
[15:54:43.863]              // -> [pidr2 <= 0x00000000]
[15:54:43.863]            __var jep106id = 0;
[15:54:43.863]              // -> [jep106id <= 0x00000000]
[15:54:43.863]            __var ROMTableBase = 0;
[15:54:43.864]              // -> [ROMTableBase <= 0x00000000]
[15:54:43.864]            __ap = 0;      // AHB-AP
[15:54:43.864]              // -> [__ap <= 0x00000000]
[15:54:43.865]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[15:54:43.865]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[15:54:43.866]              // -> [ROMTableBase <= 0xF0000000]
[15:54:43.866]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[15:54:43.867]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[15:54:43.867]              // -> [pidr1 <= 0x00000004]
[15:54:43.867]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[15:54:43.868]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[15:54:43.868]              // -> [pidr2 <= 0x0000000A]
[15:54:43.869]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[15:54:43.869]              // -> [jep106id <= 0x00000020]
[15:54:43.869]          </block>
[15:54:43.869]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[15:54:43.869]            // if-block "jep106id != 0x20"
[15:54:43.869]              // =>  FALSE
[15:54:43.869]            // skip if-block "jep106id != 0x20"
[15:54:43.869]          </control>
[15:54:43.869]        </sequence>
[15:54:43.869]    </block>
[15:54:43.869]  </sequence>
[15:54:43.869]  
[15:54:43.885]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[15:54:43.885]  
[15:54:43.885]  <debugvars>
[15:54:43.885]    // Pre-defined
[15:54:43.885]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:54:43.886]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:54:43.886]    __dp=0x00000000
[15:54:43.886]    __ap=0x00000000
[15:54:43.886]    __traceout=0x00000000      (Trace Disabled)
[15:54:43.886]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:54:43.886]    __FlashAddr=0x00000000
[15:54:43.886]    __FlashLen=0x00000000
[15:54:43.886]    __FlashArg=0x00000000
[15:54:43.886]    __FlashOp=0x00000000
[15:54:43.886]    __Result=0x00000000
[15:54:43.888]    
[15:54:43.888]    // User-defined
[15:54:43.888]    DbgMCU_CR=0x00000007
[15:54:43.888]    DbgMCU_APB1_Fz=0x00000000
[15:54:43.888]    DbgMCU_APB2_Fz=0x00000000
[15:54:43.888]    DoOptionByteLoading=0x00000000
[15:54:43.888]  </debugvars>
[15:54:43.889]  
[15:54:43.889]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[15:54:43.889]    <block atomic="false" info="">
[15:54:43.889]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[15:54:43.890]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[15:54:43.891]    </block>
[15:54:43.891]    <block atomic="false" info="DbgMCU registers">
[15:54:43.891]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[15:54:43.893]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[15:54:43.893]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[15:54:43.893]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[15:54:43.894]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[15:54:43.895]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[15:54:43.895]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:54:43.896]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[15:54:43.896]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[15:54:43.897]    </block>
[15:54:43.897]  </sequence>
[15:54:43.897]  
[15:54:52.072]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[15:54:52.072]  
[15:54:52.072]  <debugvars>
[15:54:52.072]    // Pre-defined
[15:54:52.072]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[15:54:52.073]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[15:54:52.074]    __dp=0x00000000
[15:54:52.074]    __ap=0x00000000
[15:54:52.074]    __traceout=0x00000000      (Trace Disabled)
[15:54:52.075]    __errorcontrol=0x00000000  (Skip Errors="False")
[15:54:52.075]    __FlashAddr=0x00000000
[15:54:52.075]    __FlashLen=0x00000000
[15:54:52.075]    __FlashArg=0x00000000
[15:54:52.075]    __FlashOp=0x00000000
[15:54:52.076]    __Result=0x00000000
[15:54:52.077]    
[15:54:52.077]    // User-defined
[15:54:52.077]    DbgMCU_CR=0x00000007
[15:54:52.077]    DbgMCU_APB1_Fz=0x00000000
[15:54:52.077]    DbgMCU_APB2_Fz=0x00000000
[15:54:52.077]    DoOptionByteLoading=0x00000000
[15:54:52.078]  </debugvars>
[15:54:52.078]  
[15:54:52.078]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[15:54:52.078]    <block atomic="false" info="">
[15:54:52.078]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[15:54:52.079]        // -> [connectionFlash <= 0x00000001]
[15:54:52.079]      __var FLASH_BASE = 0x40022000 ;
[15:54:52.079]        // -> [FLASH_BASE <= 0x40022000]
[15:54:52.079]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[15:54:52.079]        // -> [FLASH_CR <= 0x40022004]
[15:54:52.080]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[15:54:52.080]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[15:54:52.080]      __var LOCK_BIT = ( 1 << 0 ) ;
[15:54:52.080]        // -> [LOCK_BIT <= 0x00000001]
[15:54:52.080]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[15:54:52.080]        // -> [OPTLOCK_BIT <= 0x00000004]
[15:54:52.080]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[15:54:52.081]        // -> [FLASH_KEYR <= 0x4002200C]
[15:54:52.081]      __var FLASH_KEY1 = 0x89ABCDEF ;
[15:54:52.082]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[15:54:52.082]      __var FLASH_KEY2 = 0x02030405 ;
[15:54:52.082]        // -> [FLASH_KEY2 <= 0x02030405]
[15:54:52.083]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[15:54:52.083]        // -> [FLASH_OPTKEYR <= 0x40022014]
[15:54:52.083]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[15:54:52.083]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[15:54:52.083]      __var FLASH_OPTKEY2 = 0x24252627 ;
[15:54:52.083]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[15:54:52.084]      __var FLASH_CR_Value = 0 ;
[15:54:52.084]        // -> [FLASH_CR_Value <= 0x00000000]
[15:54:52.084]      __var DoDebugPortStop = 1 ;
[15:54:52.084]        // -> [DoDebugPortStop <= 0x00000001]
[15:54:52.084]      __var DP_CTRL_STAT = 0x4 ;
[15:54:52.085]        // -> [DP_CTRL_STAT <= 0x00000004]
[15:54:52.085]      __var DP_SELECT = 0x8 ;
[15:54:52.085]        // -> [DP_SELECT <= 0x00000008]
[15:54:52.085]    </block>
[15:54:52.085]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[15:54:52.086]      // if-block "connectionFlash && DoOptionByteLoading"
[15:54:52.086]        // =>  FALSE
[15:54:52.086]      // skip if-block "connectionFlash && DoOptionByteLoading"
[15:54:52.086]    </control>
[15:54:52.086]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[15:54:52.087]      // if-block "DoDebugPortStop"
[15:54:52.087]        // =>  TRUE
[15:54:52.087]      <block atomic="false" info="">
[15:54:52.087]        WriteDP(DP_SELECT, 0x00000000);
[15:54:52.088]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[15:54:52.088]        WriteDP(DP_CTRL_STAT, 0x00000000);
[15:54:52.089]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[15:54:52.089]      </block>
[15:54:52.090]      // end if-block "DoDebugPortStop"
[15:54:52.091]    </control>
[15:54:52.091]  </sequence>
[15:54:52.091]  
[16:12:15.108]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:12:15.108]  
[16:12:15.108]  <debugvars>
[16:12:15.109]    // Pre-defined
[16:12:15.109]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:12:15.109]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:12:15.109]    __dp=0x00000000
[16:12:15.109]    __ap=0x00000000
[16:12:15.110]    __traceout=0x00000000      (Trace Disabled)
[16:12:15.110]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:12:15.110]    __FlashAddr=0x00000000
[16:12:15.110]    __FlashLen=0x00000000
[16:12:15.110]    __FlashArg=0x00000000
[16:12:15.111]    __FlashOp=0x00000000
[16:12:15.111]    __Result=0x00000000
[16:12:15.111]    
[16:12:15.111]    // User-defined
[16:12:15.111]    DbgMCU_CR=0x00000007
[16:12:15.112]    DbgMCU_APB1_Fz=0x00000000
[16:12:15.112]    DbgMCU_APB2_Fz=0x00000000
[16:12:15.112]    DoOptionByteLoading=0x00000000
[16:12:15.112]  </debugvars>
[16:12:15.112]  
[16:12:15.113]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:12:15.113]    <block atomic="false" info="">
[16:12:15.113]      Sequence("CheckID");
[16:12:15.113]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:12:15.113]          <block atomic="false" info="">
[16:12:15.114]            __var pidr1 = 0;
[16:12:15.114]              // -> [pidr1 <= 0x00000000]
[16:12:15.114]            __var pidr2 = 0;
[16:12:15.114]              // -> [pidr2 <= 0x00000000]
[16:12:15.114]            __var jep106id = 0;
[16:12:15.115]              // -> [jep106id <= 0x00000000]
[16:12:15.115]            __var ROMTableBase = 0;
[16:12:15.115]              // -> [ROMTableBase <= 0x00000000]
[16:12:15.115]            __ap = 0;      // AHB-AP
[16:12:15.115]              // -> [__ap <= 0x00000000]
[16:12:15.115]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:12:15.117]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:12:15.117]              // -> [ROMTableBase <= 0xF0000000]
[16:12:15.117]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:12:15.118]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:12:15.119]              // -> [pidr1 <= 0x00000004]
[16:12:15.119]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:12:15.120]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:12:15.121]              // -> [pidr2 <= 0x0000000A]
[16:12:15.121]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:12:15.121]              // -> [jep106id <= 0x00000020]
[16:12:15.121]          </block>
[16:12:15.122]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:12:15.122]            // if-block "jep106id != 0x20"
[16:12:15.123]              // =>  FALSE
[16:12:15.123]            // skip if-block "jep106id != 0x20"
[16:12:15.123]          </control>
[16:12:15.123]        </sequence>
[16:12:15.124]    </block>
[16:12:15.124]  </sequence>
[16:12:15.124]  
[16:12:15.136]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:12:15.136]  
[16:12:15.137]  <debugvars>
[16:12:15.138]    // Pre-defined
[16:12:15.138]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:12:15.138]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:12:15.138]    __dp=0x00000000
[16:12:15.139]    __ap=0x00000000
[16:12:15.139]    __traceout=0x00000000      (Trace Disabled)
[16:12:15.139]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:12:15.139]    __FlashAddr=0x00000000
[16:12:15.139]    __FlashLen=0x00000000
[16:12:15.139]    __FlashArg=0x00000000
[16:12:15.139]    __FlashOp=0x00000000
[16:12:15.139]    __Result=0x00000000
[16:12:15.139]    
[16:12:15.139]    // User-defined
[16:12:15.139]    DbgMCU_CR=0x00000007
[16:12:15.139]    DbgMCU_APB1_Fz=0x00000000
[16:12:15.139]    DbgMCU_APB2_Fz=0x00000000
[16:12:15.141]    DoOptionByteLoading=0x00000000
[16:12:15.141]  </debugvars>
[16:12:15.141]  
[16:12:15.141]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:12:15.141]    <block atomic="false" info="">
[16:12:15.141]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:12:15.143]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:12:15.143]    </block>
[16:12:15.143]    <block atomic="false" info="DbgMCU registers">
[16:12:15.144]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:12:15.145]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[16:12:15.145]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[16:12:15.145]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:12:15.146]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:12:15.171]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:12:15.171]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:12:15.171]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:12:15.172]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:12:15.173]    </block>
[16:12:15.173]  </sequence>
[16:12:15.174]  
[16:12:23.113]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:12:23.113]  
[16:12:23.114]  <debugvars>
[16:12:23.114]    // Pre-defined
[16:12:23.114]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:12:23.114]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:12:23.114]    __dp=0x00000000
[16:12:23.115]    __ap=0x00000000
[16:12:23.115]    __traceout=0x00000000      (Trace Disabled)
[16:12:23.115]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:12:23.115]    __FlashAddr=0x00000000
[16:12:23.115]    __FlashLen=0x00000000
[16:12:23.116]    __FlashArg=0x00000000
[16:12:23.116]    __FlashOp=0x00000000
[16:12:23.116]    __Result=0x00000000
[16:12:23.117]    
[16:12:23.117]    // User-defined
[16:12:23.117]    DbgMCU_CR=0x00000007
[16:12:23.117]    DbgMCU_APB1_Fz=0x00000000
[16:12:23.117]    DbgMCU_APB2_Fz=0x00000000
[16:12:23.117]    DoOptionByteLoading=0x00000000
[16:12:23.117]  </debugvars>
[16:12:23.118]  
[16:12:23.118]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:12:23.118]    <block atomic="false" info="">
[16:12:23.118]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:12:23.119]        // -> [connectionFlash <= 0x00000001]
[16:12:23.120]      __var FLASH_BASE = 0x40022000 ;
[16:12:23.120]        // -> [FLASH_BASE <= 0x40022000]
[16:12:23.120]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:12:23.120]        // -> [FLASH_CR <= 0x40022004]
[16:12:23.120]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:12:23.121]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:12:23.122]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:12:23.122]        // -> [LOCK_BIT <= 0x00000001]
[16:12:23.122]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:12:23.122]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:12:23.122]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:12:23.123]        // -> [FLASH_KEYR <= 0x4002200C]
[16:12:23.123]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:12:23.123]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:12:23.123]      __var FLASH_KEY2 = 0x02030405 ;
[16:12:23.123]        // -> [FLASH_KEY2 <= 0x02030405]
[16:12:23.124]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:12:23.124]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:12:23.124]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:12:23.124]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:12:23.124]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:12:23.125]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:12:23.125]      __var FLASH_CR_Value = 0 ;
[16:12:23.125]        // -> [FLASH_CR_Value <= 0x00000000]
[16:12:23.125]      __var DoDebugPortStop = 1 ;
[16:12:23.125]        // -> [DoDebugPortStop <= 0x00000001]
[16:12:23.126]      __var DP_CTRL_STAT = 0x4 ;
[16:12:23.126]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:12:23.126]      __var DP_SELECT = 0x8 ;
[16:12:23.126]        // -> [DP_SELECT <= 0x00000008]
[16:12:23.126]    </block>
[16:12:23.127]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:12:23.127]      // if-block "connectionFlash && DoOptionByteLoading"
[16:12:23.127]        // =>  FALSE
[16:12:23.127]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:12:23.127]    </control>
[16:12:23.128]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:12:23.128]      // if-block "DoDebugPortStop"
[16:12:23.128]        // =>  TRUE
[16:12:23.128]      <block atomic="false" info="">
[16:12:23.128]        WriteDP(DP_SELECT, 0x00000000);
[16:12:23.129]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:12:23.129]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:12:23.129]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:12:23.130]      </block>
[16:12:23.130]      // end if-block "DoDebugPortStop"
[16:12:23.130]    </control>
[16:12:23.130]  </sequence>
[16:12:23.130]  
[16:29:34.735]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:29:34.735]  
[16:29:34.735]  <debugvars>
[16:29:34.735]    // Pre-defined
[16:29:34.736]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:29:34.736]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:29:34.736]    __dp=0x00000000
[16:29:34.736]    __ap=0x00000000
[16:29:34.737]    __traceout=0x00000000      (Trace Disabled)
[16:29:34.737]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:29:34.737]    __FlashAddr=0x00000000
[16:29:34.738]    __FlashLen=0x00000000
[16:29:34.738]    __FlashArg=0x00000000
[16:29:34.738]    __FlashOp=0x00000000
[16:29:34.738]    __Result=0x00000000
[16:29:34.739]    
[16:29:34.739]    // User-defined
[16:29:34.739]    DbgMCU_CR=0x00000007
[16:29:34.739]    DbgMCU_APB1_Fz=0x00000000
[16:29:34.739]    DbgMCU_APB2_Fz=0x00000000
[16:29:34.739]    DoOptionByteLoading=0x00000000
[16:29:34.740]  </debugvars>
[16:29:34.740]  
[16:29:34.740]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:29:34.740]    <block atomic="false" info="">
[16:29:34.740]      Sequence("CheckID");
[16:29:34.740]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:29:34.741]          <block atomic="false" info="">
[16:29:34.741]            __var pidr1 = 0;
[16:29:34.741]              // -> [pidr1 <= 0x00000000]
[16:29:34.741]            __var pidr2 = 0;
[16:29:34.741]              // -> [pidr2 <= 0x00000000]
[16:29:34.742]            __var jep106id = 0;
[16:29:34.742]              // -> [jep106id <= 0x00000000]
[16:29:34.742]            __var ROMTableBase = 0;
[16:29:34.742]              // -> [ROMTableBase <= 0x00000000]
[16:29:34.742]            __ap = 0;      // AHB-AP
[16:29:34.742]              // -> [__ap <= 0x00000000]
[16:29:34.742]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:29:34.742]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:29:34.742]              // -> [ROMTableBase <= 0xF0000000]
[16:29:34.742]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:29:34.744]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:29:34.744]              // -> [pidr1 <= 0x00000004]
[16:29:34.746]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:29:34.746]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:29:34.747]              // -> [pidr2 <= 0x0000000A]
[16:29:34.747]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:29:34.747]              // -> [jep106id <= 0x00000020]
[16:29:34.747]          </block>
[16:29:34.748]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:29:34.748]            // if-block "jep106id != 0x20"
[16:29:34.748]              // =>  FALSE
[16:29:34.748]            // skip if-block "jep106id != 0x20"
[16:29:34.748]          </control>
[16:29:34.748]        </sequence>
[16:29:34.749]    </block>
[16:29:34.749]  </sequence>
[16:29:34.749]  
[16:29:34.761]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:29:34.761]  
[16:29:34.786]  <debugvars>
[16:29:34.787]    // Pre-defined
[16:29:34.787]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:29:34.788]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:29:34.789]    __dp=0x00000000
[16:29:34.790]    __ap=0x00000000
[16:29:34.790]    __traceout=0x00000000      (Trace Disabled)
[16:29:34.790]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:29:34.790]    __FlashAddr=0x00000000
[16:29:34.790]    __FlashLen=0x00000000
[16:29:34.790]    __FlashArg=0x00000000
[16:29:34.793]    __FlashOp=0x00000000
[16:29:34.793]    __Result=0x00000000
[16:29:34.793]    
[16:29:34.793]    // User-defined
[16:29:34.793]    DbgMCU_CR=0x00000007
[16:29:34.793]    DbgMCU_APB1_Fz=0x00000000
[16:29:34.793]    DbgMCU_APB2_Fz=0x00000000
[16:29:34.793]    DoOptionByteLoading=0x00000000
[16:29:34.793]  </debugvars>
[16:29:34.793]  
[16:29:34.798]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:29:34.798]    <block atomic="false" info="">
[16:29:34.798]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:29:34.802]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:29:34.802]    </block>
[16:29:34.803]    <block atomic="false" info="DbgMCU registers">
[16:29:34.804]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:29:34.806]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[16:29:34.806]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[16:29:34.806]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:29:34.809]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:29:34.810]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:29:34.811]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:29:34.812]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:29:34.813]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:29:34.813]    </block>
[16:29:34.814]  </sequence>
[16:29:34.814]  
[16:29:42.786]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:29:42.786]  
[16:29:42.787]  <debugvars>
[16:29:42.787]    // Pre-defined
[16:29:42.787]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:29:42.788]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:29:42.788]    __dp=0x00000000
[16:29:42.788]    __ap=0x00000000
[16:29:42.788]    __traceout=0x00000000      (Trace Disabled)
[16:29:42.789]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:29:42.789]    __FlashAddr=0x00000000
[16:29:42.789]    __FlashLen=0x00000000
[16:29:42.789]    __FlashArg=0x00000000
[16:29:42.789]    __FlashOp=0x00000000
[16:29:42.790]    __Result=0x00000000
[16:29:42.790]    
[16:29:42.790]    // User-defined
[16:29:42.790]    DbgMCU_CR=0x00000007
[16:29:42.790]    DbgMCU_APB1_Fz=0x00000000
[16:29:42.790]    DbgMCU_APB2_Fz=0x00000000
[16:29:42.790]    DoOptionByteLoading=0x00000000
[16:29:42.791]  </debugvars>
[16:29:42.791]  
[16:29:42.791]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:29:42.791]    <block atomic="false" info="">
[16:29:42.791]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:29:42.791]        // -> [connectionFlash <= 0x00000001]
[16:29:42.792]      __var FLASH_BASE = 0x40022000 ;
[16:29:42.792]        // -> [FLASH_BASE <= 0x40022000]
[16:29:42.792]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:29:42.792]        // -> [FLASH_CR <= 0x40022004]
[16:29:42.793]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:29:42.793]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:29:42.794]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:29:42.795]        // -> [LOCK_BIT <= 0x00000001]
[16:29:42.795]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:29:42.795]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:29:42.795]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:29:42.796]        // -> [FLASH_KEYR <= 0x4002200C]
[16:29:42.796]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:29:42.796]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:29:42.796]      __var FLASH_KEY2 = 0x02030405 ;
[16:29:42.796]        // -> [FLASH_KEY2 <= 0x02030405]
[16:29:42.797]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:29:42.797]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:29:42.797]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:29:42.797]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:29:42.797]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:29:42.798]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:29:42.798]      __var FLASH_CR_Value = 0 ;
[16:29:42.798]        // -> [FLASH_CR_Value <= 0x00000000]
[16:29:42.798]      __var DoDebugPortStop = 1 ;
[16:29:42.798]        // -> [DoDebugPortStop <= 0x00000001]
[16:29:42.798]      __var DP_CTRL_STAT = 0x4 ;
[16:29:42.799]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:29:42.799]      __var DP_SELECT = 0x8 ;
[16:29:42.799]        // -> [DP_SELECT <= 0x00000008]
[16:29:42.799]    </block>
[16:29:42.799]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:29:42.800]      // if-block "connectionFlash && DoOptionByteLoading"
[16:29:42.800]        // =>  FALSE
[16:29:42.800]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:29:42.800]    </control>
[16:29:42.800]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:29:42.801]      // if-block "DoDebugPortStop"
[16:29:42.801]        // =>  TRUE
[16:29:42.801]      <block atomic="false" info="">
[16:29:42.801]        WriteDP(DP_SELECT, 0x00000000);
[16:29:42.802]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:29:42.802]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:29:42.803]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:29:42.803]      </block>
[16:29:42.803]      // end if-block "DoDebugPortStop"
[16:29:42.804]    </control>
[16:29:42.804]  </sequence>
[16:29:42.804]  
[16:31:20.837]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:31:20.837]  
[16:31:20.837]  <debugvars>
[16:31:20.838]    // Pre-defined
[16:31:20.838]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:31:20.839]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:31:20.839]    __dp=0x00000000
[16:31:20.839]    __ap=0x00000000
[16:31:20.839]    __traceout=0x00000000      (Trace Disabled)
[16:31:20.839]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:31:20.840]    __FlashAddr=0x00000000
[16:31:20.840]    __FlashLen=0x00000000
[16:31:20.840]    __FlashArg=0x00000000
[16:31:20.840]    __FlashOp=0x00000000
[16:31:20.840]    __Result=0x00000000
[16:31:20.840]    
[16:31:20.840]    // User-defined
[16:31:20.840]    DbgMCU_CR=0x00000007
[16:31:20.841]    DbgMCU_APB1_Fz=0x00000000
[16:31:20.841]    DbgMCU_APB2_Fz=0x00000000
[16:31:20.842]    DoOptionByteLoading=0x00000000
[16:31:20.842]  </debugvars>
[16:31:20.842]  
[16:31:20.842]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:31:20.842]    <block atomic="false" info="">
[16:31:20.842]      Sequence("CheckID");
[16:31:20.843]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:31:20.843]          <block atomic="false" info="">
[16:31:20.843]            __var pidr1 = 0;
[16:31:20.843]              // -> [pidr1 <= 0x00000000]
[16:31:20.843]            __var pidr2 = 0;
[16:31:20.844]              // -> [pidr2 <= 0x00000000]
[16:31:20.844]            __var jep106id = 0;
[16:31:20.844]              // -> [jep106id <= 0x00000000]
[16:31:20.844]            __var ROMTableBase = 0;
[16:31:20.844]              // -> [ROMTableBase <= 0x00000000]
[16:31:20.844]            __ap = 0;      // AHB-AP
[16:31:20.845]              // -> [__ap <= 0x00000000]
[16:31:20.845]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:31:20.846]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:31:20.846]              // -> [ROMTableBase <= 0xF0000000]
[16:31:20.846]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:31:20.847]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:31:20.847]              // -> [pidr1 <= 0x00000004]
[16:31:20.847]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:31:20.848]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:31:20.848]              // -> [pidr2 <= 0x0000000A]
[16:31:20.849]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:31:20.849]              // -> [jep106id <= 0x00000020]
[16:31:20.850]          </block>
[16:31:20.850]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:31:20.850]            // if-block "jep106id != 0x20"
[16:31:20.850]              // =>  FALSE
[16:31:20.850]            // skip if-block "jep106id != 0x20"
[16:31:20.850]          </control>
[16:31:20.850]        </sequence>
[16:31:20.851]    </block>
[16:31:20.852]  </sequence>
[16:31:20.852]  
[16:31:20.863]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:31:20.863]  
[16:31:20.864]  <debugvars>
[16:31:20.864]    // Pre-defined
[16:31:20.864]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:31:20.864]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:31:20.866]    __dp=0x00000000
[16:31:20.866]    __ap=0x00000000
[16:31:20.866]    __traceout=0x00000000      (Trace Disabled)
[16:31:20.866]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:31:20.866]    __FlashAddr=0x00000000
[16:31:20.866]    __FlashLen=0x00000000
[16:31:20.867]    __FlashArg=0x00000000
[16:31:20.867]    __FlashOp=0x00000000
[16:31:20.868]    __Result=0x00000000
[16:31:20.868]    
[16:31:20.868]    // User-defined
[16:31:20.868]    DbgMCU_CR=0x00000007
[16:31:20.869]    DbgMCU_APB1_Fz=0x00000000
[16:31:20.869]    DbgMCU_APB2_Fz=0x00000000
[16:31:20.869]    DoOptionByteLoading=0x00000000
[16:31:20.869]  </debugvars>
[16:31:20.869]  
[16:31:20.870]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:31:20.870]    <block atomic="false" info="">
[16:31:20.870]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:31:20.871]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:31:20.871]    </block>
[16:31:20.871]    <block atomic="false" info="DbgMCU registers">
[16:31:20.871]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:31:20.872]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[16:31:20.873]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[16:31:20.873]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:31:20.874]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:31:20.874]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:31:20.875]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:31:20.875]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:31:20.876]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:31:20.876]    </block>
[16:31:20.877]  </sequence>
[16:31:20.877]  
[16:31:28.597]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:31:28.597]  
[16:31:28.597]  <debugvars>
[16:31:28.598]    // Pre-defined
[16:31:28.598]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:31:28.598]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:31:28.598]    __dp=0x00000000
[16:31:28.598]    __ap=0x00000000
[16:31:28.599]    __traceout=0x00000000      (Trace Disabled)
[16:31:28.599]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:31:28.600]    __FlashAddr=0x00000000
[16:31:28.600]    __FlashLen=0x00000000
[16:31:28.600]    __FlashArg=0x00000000
[16:31:28.600]    __FlashOp=0x00000000
[16:31:28.600]    __Result=0x00000000
[16:31:28.601]    
[16:31:28.601]    // User-defined
[16:31:28.601]    DbgMCU_CR=0x00000007
[16:31:28.601]    DbgMCU_APB1_Fz=0x00000000
[16:31:28.601]    DbgMCU_APB2_Fz=0x00000000
[16:31:28.602]    DoOptionByteLoading=0x00000000
[16:31:28.602]  </debugvars>
[16:31:28.603]  
[16:31:28.603]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:31:28.603]    <block atomic="false" info="">
[16:31:28.603]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:31:28.603]        // -> [connectionFlash <= 0x00000001]
[16:31:28.603]      __var FLASH_BASE = 0x40022000 ;
[16:31:28.604]        // -> [FLASH_BASE <= 0x40022000]
[16:31:28.604]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:31:28.604]        // -> [FLASH_CR <= 0x40022004]
[16:31:28.604]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:31:28.604]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:31:28.605]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:31:28.605]        // -> [LOCK_BIT <= 0x00000001]
[16:31:28.605]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:31:28.605]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:31:28.605]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:31:28.606]        // -> [FLASH_KEYR <= 0x4002200C]
[16:31:28.606]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:31:28.606]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:31:28.606]      __var FLASH_KEY2 = 0x02030405 ;
[16:31:28.607]        // -> [FLASH_KEY2 <= 0x02030405]
[16:31:28.607]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:31:28.607]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:31:28.607]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:31:28.607]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:31:28.608]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:31:28.608]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:31:28.608]      __var FLASH_CR_Value = 0 ;
[16:31:28.608]        // -> [FLASH_CR_Value <= 0x00000000]
[16:31:28.608]      __var DoDebugPortStop = 1 ;
[16:31:28.609]        // -> [DoDebugPortStop <= 0x00000001]
[16:31:28.609]      __var DP_CTRL_STAT = 0x4 ;
[16:31:28.609]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:31:28.609]      __var DP_SELECT = 0x8 ;
[16:31:28.609]        // -> [DP_SELECT <= 0x00000008]
[16:31:28.610]    </block>
[16:31:28.610]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:31:28.610]      // if-block "connectionFlash && DoOptionByteLoading"
[16:31:28.611]        // =>  FALSE
[16:31:28.611]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:31:28.611]    </control>
[16:31:28.611]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:31:28.611]      // if-block "DoDebugPortStop"
[16:31:28.612]        // =>  TRUE
[16:31:28.612]      <block atomic="false" info="">
[16:31:28.612]        WriteDP(DP_SELECT, 0x00000000);
[16:31:28.612]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:31:28.612]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:31:28.613]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:31:28.613]      </block>
[16:31:28.613]      // end if-block "DoDebugPortStop"
[16:31:28.613]    </control>
[16:31:28.614]  </sequence>
[16:31:28.614]  
[16:32:32.948]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:32:32.948]  
[16:32:32.949]  <debugvars>
[16:32:32.949]    // Pre-defined
[16:32:32.949]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:32:32.950]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:32:32.950]    __dp=0x00000000
[16:32:32.950]    __ap=0x00000000
[16:32:32.950]    __traceout=0x00000000      (Trace Disabled)
[16:32:32.950]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:32:32.951]    __FlashAddr=0x00000000
[16:32:32.951]    __FlashLen=0x00000000
[16:32:32.951]    __FlashArg=0x00000000
[16:32:32.952]    __FlashOp=0x00000000
[16:32:32.952]    __Result=0x00000000
[16:32:32.953]    
[16:32:32.953]    // User-defined
[16:32:32.953]    DbgMCU_CR=0x00000007
[16:32:32.953]    DbgMCU_APB1_Fz=0x00000000
[16:32:32.953]    DbgMCU_APB2_Fz=0x00000000
[16:32:32.953]    DoOptionByteLoading=0x00000000
[16:32:32.954]  </debugvars>
[16:32:32.954]  
[16:32:32.954]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:32:32.954]    <block atomic="false" info="">
[16:32:32.954]      Sequence("CheckID");
[16:32:32.955]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:32:32.955]          <block atomic="false" info="">
[16:32:32.955]            __var pidr1 = 0;
[16:32:32.955]              // -> [pidr1 <= 0x00000000]
[16:32:32.955]            __var pidr2 = 0;
[16:32:32.956]              // -> [pidr2 <= 0x00000000]
[16:32:32.956]            __var jep106id = 0;
[16:32:32.956]              // -> [jep106id <= 0x00000000]
[16:32:32.956]            __var ROMTableBase = 0;
[16:32:32.956]              // -> [ROMTableBase <= 0x00000000]
[16:32:32.956]            __ap = 0;      // AHB-AP
[16:32:32.956]              // -> [__ap <= 0x00000000]
[16:32:32.957]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:32:32.958]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:32:32.958]              // -> [ROMTableBase <= 0xF0000000]
[16:32:32.958]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:32:32.960]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:32:32.960]              // -> [pidr1 <= 0x00000004]
[16:32:32.960]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:32:32.961]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:32:32.961]              // -> [pidr2 <= 0x0000000A]
[16:32:32.962]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:32:32.962]              // -> [jep106id <= 0x00000020]
[16:32:32.962]          </block>
[16:32:32.963]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:32:32.963]            // if-block "jep106id != 0x20"
[16:32:32.963]              // =>  FALSE
[16:32:32.963]            // skip if-block "jep106id != 0x20"
[16:32:32.964]          </control>
[16:32:32.964]        </sequence>
[16:32:32.964]    </block>
[16:32:32.964]  </sequence>
[16:32:32.964]  
[16:32:32.976]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:32:32.976]  
[16:32:32.977]  <debugvars>
[16:32:32.977]    // Pre-defined
[16:32:32.977]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:32:32.977]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:32:32.978]    __dp=0x00000000
[16:32:32.978]    __ap=0x00000000
[16:32:32.978]    __traceout=0x00000000      (Trace Disabled)
[16:32:32.979]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:32:32.979]    __FlashAddr=0x00000000
[16:32:32.979]    __FlashLen=0x00000000
[16:32:32.979]    __FlashArg=0x00000000
[16:32:32.980]    __FlashOp=0x00000000
[16:32:32.980]    __Result=0x00000000
[16:32:32.980]    
[16:32:32.980]    // User-defined
[16:32:32.980]    DbgMCU_CR=0x00000007
[16:32:32.980]    DbgMCU_APB1_Fz=0x00000000
[16:32:32.980]    DbgMCU_APB2_Fz=0x00000000
[16:32:32.981]    DoOptionByteLoading=0x00000000
[16:32:32.981]  </debugvars>
[16:32:32.981]  
[16:32:32.981]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:32:32.981]    <block atomic="false" info="">
[16:32:32.982]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:32:32.982]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:32:32.983]    </block>
[16:32:32.983]    <block atomic="false" info="DbgMCU registers">
[16:32:32.983]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:32:32.984]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[16:32:32.985]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[16:32:32.986]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:32:32.986]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:32:32.987]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:32:32.987]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:32:32.988]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:32:32.989]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:32:32.989]    </block>
[16:32:32.990]  </sequence>
[16:32:32.990]  
[16:32:40.750]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:32:40.750]  
[16:32:40.751]  <debugvars>
[16:32:40.751]    // Pre-defined
[16:32:40.751]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:32:40.751]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:32:40.752]    __dp=0x00000000
[16:32:40.752]    __ap=0x00000000
[16:32:40.753]    __traceout=0x00000000      (Trace Disabled)
[16:32:40.753]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:32:40.753]    __FlashAddr=0x00000000
[16:32:40.754]    __FlashLen=0x00000000
[16:32:40.754]    __FlashArg=0x00000000
[16:32:40.754]    __FlashOp=0x00000000
[16:32:40.755]    __Result=0x00000000
[16:32:40.755]    
[16:32:40.755]    // User-defined
[16:32:40.755]    DbgMCU_CR=0x00000007
[16:32:40.755]    DbgMCU_APB1_Fz=0x00000000
[16:32:40.756]    DbgMCU_APB2_Fz=0x00000000
[16:32:40.756]    DoOptionByteLoading=0x00000000
[16:32:40.756]  </debugvars>
[16:32:40.756]  
[16:32:40.757]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:32:40.757]    <block atomic="false" info="">
[16:32:40.757]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:32:40.757]        // -> [connectionFlash <= 0x00000001]
[16:32:40.758]      __var FLASH_BASE = 0x40022000 ;
[16:32:40.758]        // -> [FLASH_BASE <= 0x40022000]
[16:32:40.758]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:32:40.758]        // -> [FLASH_CR <= 0x40022004]
[16:32:40.758]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:32:40.760]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:32:40.760]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:32:40.760]        // -> [LOCK_BIT <= 0x00000001]
[16:32:40.760]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:32:40.761]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:32:40.761]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:32:40.761]        // -> [FLASH_KEYR <= 0x4002200C]
[16:32:40.761]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:32:40.761]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:32:40.762]      __var FLASH_KEY2 = 0x02030405 ;
[16:32:40.762]        // -> [FLASH_KEY2 <= 0x02030405]
[16:32:40.762]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:32:40.762]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:32:40.762]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:32:40.763]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:32:40.763]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:32:40.763]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:32:40.763]      __var FLASH_CR_Value = 0 ;
[16:32:40.763]        // -> [FLASH_CR_Value <= 0x00000000]
[16:32:40.763]      __var DoDebugPortStop = 1 ;
[16:32:40.764]        // -> [DoDebugPortStop <= 0x00000001]
[16:32:40.764]      __var DP_CTRL_STAT = 0x4 ;
[16:32:40.764]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:32:40.764]      __var DP_SELECT = 0x8 ;
[16:32:40.764]        // -> [DP_SELECT <= 0x00000008]
[16:32:40.765]    </block>
[16:32:40.765]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:32:40.765]      // if-block "connectionFlash && DoOptionByteLoading"
[16:32:40.765]        // =>  FALSE
[16:32:40.765]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:32:40.765]    </control>
[16:32:40.766]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:32:40.766]      // if-block "DoDebugPortStop"
[16:32:40.767]        // =>  TRUE
[16:32:40.768]      <block atomic="false" info="">
[16:32:40.768]        WriteDP(DP_SELECT, 0x00000000);
[16:32:40.768]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:32:40.768]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:32:40.769]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:32:40.769]      </block>
[16:32:40.769]      // end if-block "DoDebugPortStop"
[16:32:40.770]    </control>
[16:32:40.770]  </sequence>
[16:32:40.770]  
[16:41:20.859]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:41:20.859]  
[16:41:20.859]  <debugvars>
[16:41:20.860]    // Pre-defined
[16:41:20.860]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:41:20.860]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:41:20.860]    __dp=0x00000000
[16:41:20.860]    __ap=0x00000000
[16:41:20.861]    __traceout=0x00000000      (Trace Disabled)
[16:41:20.861]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:41:20.861]    __FlashAddr=0x00000000
[16:41:20.861]    __FlashLen=0x00000000
[16:41:20.861]    __FlashArg=0x00000000
[16:41:20.862]    __FlashOp=0x00000000
[16:41:20.862]    __Result=0x00000000
[16:41:20.862]    
[16:41:20.862]    // User-defined
[16:41:20.862]    DbgMCU_CR=0x00000007
[16:41:20.862]    DbgMCU_APB1_Fz=0x00000000
[16:41:20.863]    DbgMCU_APB2_Fz=0x00000000
[16:41:20.863]    DoOptionByteLoading=0x00000000
[16:41:20.863]  </debugvars>
[16:41:20.864]  
[16:41:20.864]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:41:20.864]    <block atomic="false" info="">
[16:41:20.864]      Sequence("CheckID");
[16:41:20.864]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:41:20.864]          <block atomic="false" info="">
[16:41:20.865]            __var pidr1 = 0;
[16:41:20.865]              // -> [pidr1 <= 0x00000000]
[16:41:20.865]            __var pidr2 = 0;
[16:41:20.865]              // -> [pidr2 <= 0x00000000]
[16:41:20.866]            __var jep106id = 0;
[16:41:20.866]              // -> [jep106id <= 0x00000000]
[16:41:20.866]            __var ROMTableBase = 0;
[16:41:20.866]              // -> [ROMTableBase <= 0x00000000]
[16:41:20.866]            __ap = 0;      // AHB-AP
[16:41:20.866]              // -> [__ap <= 0x00000000]
[16:41:20.867]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:41:20.867]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:41:20.868]              // -> [ROMTableBase <= 0xF0000000]
[16:41:20.868]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:41:20.869]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:41:20.869]              // -> [pidr1 <= 0x00000004]
[16:41:20.869]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:41:20.870]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:41:20.870]              // -> [pidr2 <= 0x0000000A]
[16:41:20.871]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:41:20.871]              // -> [jep106id <= 0x00000020]
[16:41:20.872]          </block>
[16:41:20.872]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:41:20.872]            // if-block "jep106id != 0x20"
[16:41:20.872]              // =>  FALSE
[16:41:20.873]            // skip if-block "jep106id != 0x20"
[16:41:20.873]          </control>
[16:41:20.873]        </sequence>
[16:41:20.873]    </block>
[16:41:20.873]  </sequence>
[16:41:20.874]  
[16:41:20.887]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:41:20.887]  
[16:41:20.899]  <debugvars>
[16:41:20.899]    // Pre-defined
[16:41:20.899]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:41:20.899]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:41:20.899]    __dp=0x00000000
[16:41:20.900]    __ap=0x00000000
[16:41:20.900]    __traceout=0x00000000      (Trace Disabled)
[16:41:20.901]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:41:20.901]    __FlashAddr=0x00000000
[16:41:20.901]    __FlashLen=0x00000000
[16:41:20.901]    __FlashArg=0x00000000
[16:41:20.901]    __FlashOp=0x00000000
[16:41:20.902]    __Result=0x00000000
[16:41:20.902]    
[16:41:20.902]    // User-defined
[16:41:20.902]    DbgMCU_CR=0x00000007
[16:41:20.902]    DbgMCU_APB1_Fz=0x00000000
[16:41:20.903]    DbgMCU_APB2_Fz=0x00000000
[16:41:20.903]    DoOptionByteLoading=0x00000000
[16:41:20.903]  </debugvars>
[16:41:20.903]  
[16:41:20.903]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:41:20.903]    <block atomic="false" info="">
[16:41:20.904]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:41:20.905]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:41:20.905]    </block>
[16:41:20.906]    <block atomic="false" info="DbgMCU registers">
[16:41:20.906]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:41:20.907]        // -> [Read32(0x40021034) => 0x00004000]   (__dp=0x00000000, __ap=0x00000000)
[16:41:20.907]        // -> [Write32(0x40021034, 0x00404000)]   (__dp=0x00000000, __ap=0x00000000)
[16:41:20.907]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:41:20.909]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:41:20.909]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:41:20.910]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:41:20.910]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:41:20.911]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:41:20.911]    </block>
[16:41:20.911]  </sequence>
[16:41:20.912]  
[16:41:29.152]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[16:41:29.152]  
[16:41:29.154]  <debugvars>
[16:41:29.154]    // Pre-defined
[16:41:29.154]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:41:29.154]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[16:41:29.154]    __dp=0x00000000
[16:41:29.154]    __ap=0x00000000
[16:41:29.154]    __traceout=0x00000000      (Trace Disabled)
[16:41:29.155]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:41:29.155]    __FlashAddr=0x00000000
[16:41:29.155]    __FlashLen=0x00000000
[16:41:29.155]    __FlashArg=0x00000000
[16:41:29.155]    __FlashOp=0x00000000
[16:41:29.155]    __Result=0x00000000
[16:41:29.155]    
[16:41:29.155]    // User-defined
[16:41:29.156]    DbgMCU_CR=0x00000007
[16:41:29.156]    DbgMCU_APB1_Fz=0x00000000
[16:41:29.156]    DbgMCU_APB2_Fz=0x00000000
[16:41:29.156]    DoOptionByteLoading=0x00000000
[16:41:29.156]  </debugvars>
[16:41:29.156]  
[16:41:29.156]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[16:41:29.156]    <block atomic="false" info="">
[16:41:29.158]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[16:41:29.158]        // -> [connectionFlash <= 0x00000001]
[16:41:29.158]      __var FLASH_BASE = 0x40022000 ;
[16:41:29.158]        // -> [FLASH_BASE <= 0x40022000]
[16:41:29.158]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[16:41:29.158]        // -> [FLASH_CR <= 0x40022004]
[16:41:29.158]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[16:41:29.159]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[16:41:29.159]      __var LOCK_BIT = ( 1 << 0 ) ;
[16:41:29.159]        // -> [LOCK_BIT <= 0x00000001]
[16:41:29.159]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[16:41:29.159]        // -> [OPTLOCK_BIT <= 0x00000004]
[16:41:29.160]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[16:41:29.160]        // -> [FLASH_KEYR <= 0x4002200C]
[16:41:29.160]      __var FLASH_KEY1 = 0x89ABCDEF ;
[16:41:29.160]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[16:41:29.160]      __var FLASH_KEY2 = 0x02030405 ;
[16:41:29.160]        // -> [FLASH_KEY2 <= 0x02030405]
[16:41:29.161]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[16:41:29.161]        // -> [FLASH_OPTKEYR <= 0x40022014]
[16:41:29.161]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[16:41:29.161]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[16:41:29.161]      __var FLASH_OPTKEY2 = 0x24252627 ;
[16:41:29.162]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[16:41:29.162]      __var FLASH_CR_Value = 0 ;
[16:41:29.162]        // -> [FLASH_CR_Value <= 0x00000000]
[16:41:29.162]      __var DoDebugPortStop = 1 ;
[16:41:29.162]        // -> [DoDebugPortStop <= 0x00000001]
[16:41:29.163]      __var DP_CTRL_STAT = 0x4 ;
[16:41:29.163]        // -> [DP_CTRL_STAT <= 0x00000004]
[16:41:29.163]      __var DP_SELECT = 0x8 ;
[16:41:29.163]        // -> [DP_SELECT <= 0x00000008]
[16:41:29.163]    </block>
[16:41:29.164]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[16:41:29.164]      // if-block "connectionFlash && DoOptionByteLoading"
[16:41:29.164]        // =>  FALSE
[16:41:29.164]      // skip if-block "connectionFlash && DoOptionByteLoading"
[16:41:29.164]    </control>
[16:41:29.165]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[16:41:29.165]      // if-block "DoDebugPortStop"
[16:41:29.165]        // =>  TRUE
[16:41:29.165]      <block atomic="false" info="">
[16:41:29.165]        WriteDP(DP_SELECT, 0x00000000);
[16:41:29.166]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[16:41:29.166]        WriteDP(DP_CTRL_STAT, 0x00000000);
[16:41:29.166]          // -> [WriteDP(0x00000004, 0x00000000)]   (__dp=0x00000000)
[16:41:29.167]      </block>
[16:41:29.167]      // end if-block "DoDebugPortStop"
[16:41:29.167]    </control>
[16:41:29.167]  </sequence>
[16:41:29.168]  
[16:41:29.516]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[16:41:29.516]  
[16:41:29.516]  <debugvars>
[16:41:29.516]    // Pre-defined
[16:41:29.516]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:41:29.516]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[16:41:29.516]    __dp=0x00000000
[16:41:29.516]    __ap=0x00000000
[16:41:29.517]    __traceout=0x00000000      (Trace Disabled)
[16:41:29.517]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:41:29.517]    __FlashAddr=0x00000000
[16:41:29.518]    __FlashLen=0x00000000
[16:41:29.518]    __FlashArg=0x00000000
[16:41:29.519]    __FlashOp=0x00000000
[16:41:29.519]    __Result=0x00000000
[16:41:29.519]    
[16:41:29.519]    // User-defined
[16:41:29.519]    DbgMCU_CR=0x00000007
[16:41:29.519]    DbgMCU_APB1_Fz=0x00000000
[16:41:29.519]    DbgMCU_APB2_Fz=0x00000000
[16:41:29.520]    DoOptionByteLoading=0x00000000
[16:41:29.520]  </debugvars>
[16:41:29.520]  
[16:41:29.520]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[16:41:29.521]    <block atomic="false" info="">
[16:41:29.521]      Sequence("CheckID");
[16:41:29.521]        <sequence name="CheckID" Pname="" disable="false" info="">
[16:41:29.521]          <block atomic="false" info="">
[16:41:29.521]            __var pidr1 = 0;
[16:41:29.521]              // -> [pidr1 <= 0x00000000]
[16:41:29.522]            __var pidr2 = 0;
[16:41:29.522]              // -> [pidr2 <= 0x00000000]
[16:41:29.522]            __var jep106id = 0;
[16:41:29.522]              // -> [jep106id <= 0x00000000]
[16:41:29.522]            __var ROMTableBase = 0;
[16:41:29.523]              // -> [ROMTableBase <= 0x00000000]
[16:41:29.523]            __ap = 0;      // AHB-AP
[16:41:29.523]              // -> [__ap <= 0x00000000]
[16:41:29.523]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[16:41:29.524]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[16:41:29.524]              // -> [ROMTableBase <= 0xF0000000]
[16:41:29.524]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[16:41:29.525]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[16:41:29.526]              // -> [pidr1 <= 0x00000004]
[16:41:29.526]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[16:41:29.527]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[16:41:29.527]              // -> [pidr2 <= 0x0000000A]
[16:41:29.527]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[16:41:29.527]              // -> [jep106id <= 0x00000020]
[16:41:29.528]          </block>
[16:41:29.528]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[16:41:29.529]            // if-block "jep106id != 0x20"
[16:41:29.533]              // =>  FALSE
[16:41:29.533]            // skip if-block "jep106id != 0x20"
[16:41:29.534]          </control>
[16:41:29.534]        </sequence>
[16:41:29.534]    </block>
[16:41:29.534]  </sequence>
[16:41:29.534]  
[16:41:29.546]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[16:41:29.546]  
[16:41:29.547]  <debugvars>
[16:41:29.547]    // Pre-defined
[16:41:29.547]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[16:41:29.548]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[16:41:29.548]    __dp=0x00000000
[16:41:29.548]    __ap=0x00000000
[16:41:29.549]    __traceout=0x00000000      (Trace Disabled)
[16:41:29.549]    __errorcontrol=0x00000000  (Skip Errors="False")
[16:41:29.549]    __FlashAddr=0x00000000
[16:41:29.550]    __FlashLen=0x00000000
[16:41:29.550]    __FlashArg=0x00000000
[16:41:29.550]    __FlashOp=0x00000000
[16:41:29.551]    __Result=0x00000000
[16:41:29.551]    
[16:41:29.551]    // User-defined
[16:41:29.551]    DbgMCU_CR=0x00000007
[16:41:29.551]    DbgMCU_APB1_Fz=0x00000000
[16:41:29.551]    DbgMCU_APB2_Fz=0x00000000
[16:41:29.552]    DoOptionByteLoading=0x00000000
[16:41:29.552]  </debugvars>
[16:41:29.552]  
[16:41:29.552]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[16:41:29.552]    <block atomic="false" info="">
[16:41:29.553]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[16:41:29.553]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[16:41:29.553]    </block>
[16:41:29.554]    <block atomic="false" info="DbgMCU registers">
[16:41:29.554]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[16:41:29.555]        // -> [Read32(0x40021034) => 0x00000000]   (__dp=0x00000000, __ap=0x00000000)
[16:41:29.555]        // -> [Write32(0x40021034, 0x00400000)]   (__dp=0x00000000, __ap=0x00000000)
[16:41:29.556]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[16:41:29.556]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[16:41:29.557]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[16:41:29.558]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:41:29.558]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[16:41:29.559]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[16:41:29.559]    </block>
[16:41:29.559]  </sequence>
[16:41:29.560]  
